import React from 'react';
import { Image } from 'react-native';
import FastImage from 'react-native-fast-image';
import { helper } from "@common";

const ImageURI = ({
    uri,
    style,
    resizeMode = "contain"
}) => {
    const isValidate = checkSourceURI(uri);
    return (
        isValidate
            ? <FastImage
                style={style}
                source={{ uri: uri, }}
                resizeMode={resizeMode}
                key={`FastImage${uri}`}
            />
            : <Image
                style={style}
                source={{ uri: uri }}
                resizeMode={resizeMode}
            />
    );
}

export default ImageURI;

const checkSourceURI = (uri) => {
    if (helper.IsNonEmptyString(uri)) {
        return uri.includes(`http`)
    }
    return false;
}