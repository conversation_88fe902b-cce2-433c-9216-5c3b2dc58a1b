import React, { useRef, useState } from 'react';
import {
    View,
    PanResponder,
    StyleSheet,
    Animated,
    Dimensions,
    TouchableOpacity,
    Platform
} from 'react-native';
import { Icon } from '@components';
import Lottie from 'lottie-react-native';
import { COLORS } from '../styles';
import { useSelector } from 'react-redux';
import { helper } from '@common';

const { height: screenHeight } = Dimensions.get('window');
const fabHeight = 46;
const bottomMargin = 20;
const topMargin = 100;

const offsetY = Platform.select({
    android: -100,
    ios: -400
});
const DraggableFAB = ({ onPress, isRecording }) => {
    const [offset] = useState(new Animated.ValueXY({ x: 0, y: offsetY }));
    const animationRef = useRef(null);
    const { userName } = useSelector((state) => state.userReducer);
    const pan = useRef(new Animated.ValueXY()).current;

    const panResponder = useRef(
        PanResponder.create({
            onStartShouldSetPanResponder: () => true,
            onMoveShouldSetPanResponder: (_, gestureState) => {
                // Chỉ kéo khi di chuyển đủ nhiều
                return (
                    Math.abs(gestureState.dx) > 5 ||
                    Math.abs(gestureState.dy) > 5
                );
            },
            onPanResponderMove: Animated.event(
                [null, { dx: pan.x, dy: pan.y }],
                { useNativeDriver: false }
            ),
            onPanResponderRelease: (_, gestureState) => {
                // Cập nhật vị trí hiện tại
                offset.x.setValue(offset.x._value + pan.x._value);
                let newY = offset.y._value + pan.y._value;

                // Giới hạn không vượt màn hình
                newY = Math.max(
                    topMargin - (screenHeight - fabHeight - bottomMargin),
                    Math.min(newY, 0)
                );
                offset.y.setValue(newY);

                const snapToRight =
                    gestureState.moveX > Dimensions.get('window').width / 2;
                const snapX = snapToRight
                    ? 0
                    : -Dimensions.get('window').width + fabHeight + 20 * 2;

                Animated.spring(offset.x, {
                    toValue: snapX,
                    useNativeDriver: false
                }).start();

                Animated.timing(pan, {
                    toValue: { x: 0, y: 0 },
                    duration: 0,
                    useNativeDriver: false
                }).start();
            }
        })
    ).current;


    if (!helper.configCouponUser(userName)) return null

    return (
        <Animated.View
            style={[
                styles.fabContainer,
                {
                    transform: [
                        { translateX: Animated.add(pan.x, offset.x) },
                        { translateY: Animated.add(pan.y, offset.y) }
                    ]
                }
            ]}
            {...panResponder.panHandlers}>
            <View style={{ flex: 1 }}>
                <TouchableOpacity
                    style={styles.fab}
                    activeOpacity={0.7}
                    onPress={onPress}
                    onPressIn={(e) => e.stopPropagation()}
                    delayPressIn={0}>
                    {isRecording ? (
                        <Lottie
                            ref={animationRef}
                            source={require('../../assets/Sound.json')}
                            loop
                            autoPlay
                            style={{ width: '100%', height: '100%' }}
                        />
                    ) : (
                        <Icon
                            iconSet={'MaterialIcon'}
                            name={'mic'}
                            size={25}
                            color={COLORS.bg1E88E5}
                        />
                    )}
                </TouchableOpacity>
            </View>
        </Animated.View>
    );
};

const styles = StyleSheet.create({
    fabContainer: {
        position: 'absolute',
        bottom: 20,
        right: 20
    },
    fab: {
        width: 46,
        height: 46,
        borderRadius: 28,
        backgroundColor: COLORS.bgE0E0E0,
        justifyContent: 'center',
        alignItems: 'center',
        elevation: 5,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 4
    }
});

export default DraggableFAB;
