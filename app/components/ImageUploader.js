import React, { useState } from 'react';
import { View, StyleSheet, Alert, Text } from 'react-native';
import { launchImageLibrary } from 'react-native-image-picker';

import {
  MyText,
  Icon,
  CaptureCamera,
  showBlockUI,
  hideBlockUI
} from '@components';
import { helper } from '@common';
import { API_CONST } from '@constants';
import { getImageCDN } from '../container/ShoppingCart/action';
import ImageProcess from '../container/AnKhangNew/components/ImageProcess';
import { TouchableOpacity } from 'react-native';

const ImageUploader = ({
  maxImages = 5,
  onImagesChange = () => { },
  buttonText = 'Upload or take photo',
  buttonStyle,
  iconProps = {
    iconSet: 'MaterialCommunityIcons',
    name: 'camera-plus-outline',
    color: 'gray',
    size: 32
  },
  filePath = 'CART_SCREEN'
}) => {
  const [isVisibleCamera, setIsVisibleCamera] = useState(false);
  const [images, setImages] = useState([]);

  const handleUpload = async (source) => {
    try {
      showBlockUI();

      const { uri, name } = await helper.resizeImage(source);
      const body = helper.createFormData({
        uri,
        type: 'image/jpg',
        name,
        path: filePath
      });

      const res = await getImageCDN(body);
      const remoteURI = API_CONST.API_GET_IMAGE_CDN_NEW + res[0];
      const updatedImages = [...images, { url: remoteURI }];

      setImages(updatedImages);
      onImagesChange(updatedImages);
    } catch (error) {
      console.log('Image upload error:', error);
    } finally {
      hideBlockUI();
    }
  };

  const takePicture = (photo) => {
    setIsVisibleCamera(false);
    if (helper.hasProperty(photo, 'uri')) {
      handleUpload(photo);
    }
  };

  const selectPicture = () => {
    if (images.length >= maxImages) {
      Alert.alert('', `You can only upload up to ${maxImages} images`);
      return;
    }

    launchImageLibrary({ mediaType: 'photo', noData: true }, (response) => {
      setIsVisibleCamera(false);
      if (helper.hasProperty(response, 'uri')) {
        handleUpload(response);
      }
    });
  };

  const removeImage = (index) => {
    const updated = images.filter((_, i) => i !== index);
    setImages(updated);
    onImagesChange(updated);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>Chụp ảnh bổ sung</Text>
      <View style={styles.uploaderBox}>
        <View style={styles.imagesContainer}>
          {images.map((image, index) => (
            <View
              key={index.toString()}
              style={styles.imageWrapper}>
              <ImageProcess
                urlImageLocal={image.url}
                urlImageRemote={image.url}
                deleteImage={() => removeImage(index)}
                onPress={() => { }}
              />
            </View>
          ))}
        </View>

        {images.length < maxImages && (
          <View style={styles.uploadContainer}>
            <CaptureCamera
              isVisibleCamera={isVisibleCamera}
              takePicture={takePicture}
              closeCamera={() => setIsVisibleCamera(false)}
              selectPicture={selectPicture}
            />

            <TouchableOpacity
              onPress={() => setIsVisibleCamera(true)}
              style={[styles.cameraContainer, buttonStyle]}>
              <Icon {...iconProps} />
              <MyText
                text={buttonText}
                style={styles.buttonTakePhoto}
              />
            </TouchableOpacity>
          </View>
        )}
      </View>
    </View>
  );
};

const IMAGE_MARGIN = 8;

const styles = StyleSheet.create({
  container: {
    padding: 10
  },
  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
    marginBottom: 10
  },
  imageWrapper: {
    width: '33.33%',
    padding: 5
  },
  uploadContainer: {
    marginTop: 12
  },
  cameraContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 10,
    backgroundColor: '#f8f8f8'
  },
  buttonTakePhoto: {
    marginTop: 8,
    paddingHorizontal: 16,
    paddingVertical: 8
  },
  description: {
    marginTop: 6,
    fontSize: 12,
    color: '#777',
    textAlign: 'center'
  },

  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333'
  },
  uploaderBox: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#fff'
  }
});

export default ImageUploader;
