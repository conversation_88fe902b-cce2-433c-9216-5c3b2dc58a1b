import React from 'react';
import { View } from 'react-native';
import { WebView } from 'react-native-webview';
import KModal from 'react-native-modal';
import { ModalHeader } from '@header';
import { constants } from '@constants';
import { COLORS } from '@styles';

const ViewHTML = ({ isVisible, hideModal, source, title }) => {
    return (
        <KModal
            isVisible={isVisible}
            deviceWidth={constants.width}
            deviceHeight={constants.height}
            animationIn="slideInUp"
            animationOut="slideOutDown"
            useNativeDriver
            hideModalContentWhileAnimating
            style={{ margin: 0 }}>
            <View
                style={{
                    flex: 1
                }}>
                <ModalHeader onClose={hideModal} title={title} />

                <View
                    style={{
                        flex: 1,
                        width: constants.width,
                        backgroundColor: COLORS.bgFFFFFF,
                        alignItems: 'center',
                        paddingHorizontal: 10
                    }}>
                    <WebView
                        style={{
                            height: constants.height,
                            width: constants.width
                        }}
                        source={{ html: source }}
                        automaticallyAdjustContentInsets={false}
                    />
                </View>
            </View>
        </KModal>
    );
};

export default ViewHTML;
