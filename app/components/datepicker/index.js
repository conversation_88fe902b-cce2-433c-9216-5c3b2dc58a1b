import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import DatePicker from 'react-native-date-picker';
import { MyText, Icon } from '@components';
import { COLORS } from '@styles';
import { translate } from '@translate';
import { dateHelper } from '../../common';
import moment from 'moment';
import SingleDatePicker from "./SingleDatePicker";


const datePicker = ({
    date,
    onDateChange,
    style,
    styleText,
    maxDate,
    minDate,
    placeholder = "DD/MM/YYYY",
    format = "YYYY-MM-DD",
    disabled
}) => {
    const [show, setShow] = useState(false);
    let dateObj = new Date();
    let text = placeholder;
    if (date) {
        const momentObj = moment(date, format);
        if (momentObj.isValid()) {
            dateObj = momentObj.toDate();
            text = momentObj.format('DD/MM/YYYY');
        } else {
            text = `${date}`;
        }
    }
    return (
        <View
            style={{
                flex: 1
            }}>
            <TouchableOpacity
                style={[style, {
                    flex: 1,
                    marginTop: 6,
                    height: 40,
                    alignItems: 'center',
                    justifyContent: 'center',
                    flexDirection: 'row',
                    paddingHorizontal: 10,
                    borderWidth: 1,
                    borderRadius: 6,
                    borderColor: COLORS.bdCCCCCC
                }]}
                onPress={() => setShow(true)}
                disabled={disabled}
            >
                <View
                    style={{
                        flex: 0.8
                    }}>
                    <MyText
                        style={[styleText, {
                            color:
                                date
                                    ? COLORS.bg000000
                                    : COLORS.bgE0E0E0,
                            fontSize: 16

                        }]}
                        text={text}
                    />
                </View>
                <View
                    style={{
                        flex: 0.2,
                        justifyContent: 'flex-end',
                        alignItems: 'flex-end'
                    }}>
                    <Icon
                        iconSet={'Ionicons'}
                        name={'calendar-outline'}
                        style={{
                            justifyContent: 'center',
                            fontSize: 22,
                            color: COLORS.ic147EFB
                        }}
                    />
                </View>
            </TouchableOpacity>
            {show && <SingleDatePicker
                isVisible={true}
                hideModal={() => {
                    setShow(false);
                }}
                selectedDate={dateObj}
                onSubmit={(value) => {
                    setShow(false);
                    const dateStr = moment(value).format(format);
                    onDateChange(dateStr, value);
                }}
                minDate={minDate}
                maxDate={maxDate}
            />}
        </View>
    );
};

export default datePicker;

const styles = StyleSheet.create({});

/// ===========  Styles ========= ///
{/* <DatePicker
date={birthday}
onDateChange={(date) => {
    setBirthday(date);
    setState({
        ...state,
        EPOSTransactionBO: {
            ...state.EPOSTransactionBO,
            // Birthday: new Date(newdate),
            Birthday: ${date}T00:00:00
        }
    });
}}
/> */}