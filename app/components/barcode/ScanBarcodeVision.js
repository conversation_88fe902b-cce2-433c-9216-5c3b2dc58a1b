import React, { useState } from 'react';
import { View } from 'react-native';
import KModal from "react-native-modal";
import Safe<PERSON>reaView from 'react-native-safe-area-view';
import { constants } from "@constants";
import { helper } from '@common';
import ContentView from './ContentView';
import MaskView from './NewMaskView';
import { Camera, useCameraDevice, useCameraFormat, useCodeScanner } from 'react-native-vision-camera';

const { width } = constants;
const FRAME_WIDTH = width;
const MASK_WIDTH = FRAME_WIDTH - 40;
const MASK_HEIGHT = MASK_WIDTH * 0.55;
const FRAME_HEIGHT = MASK_HEIGHT + 30;

const ScanBarcodeVision = ({ isVisible, closeCamera, resultScanBarcode }) => {

    const device = useCameraDevice('back', {
        physicalDevices: [
            'ultra-wide-angle-camera',
            'wide-angle-camera',
            'telephoto-camera'
        ],
        hardwareLevel: 'full',
        supportsFocus: true
    })
    const format = useCameraFormat(device, [
        { videoResolution: { width: 1920, height: 1080 } }
    ])

    const [isActive, setIsActive] = useState(true);
    const [listBarcode, setListBarcode] = useState([]);

    const codeScanner = useCodeScanner({
        codeTypes: [
            'qr',
            'ean-13',
            'code-128',
            'code-39',
            'code-93',
            'ean-8',
            'itf',
            'upc-e',
            'pdf-417',
            'aztec',
            'data-matrix'
        ],
        onCodeScanned: (codes) => {
            if (helper.IsNonEmptyArray(codes)) {
                setIsActive(false);
                if (codes.length == 1) {
                    resolveBarcode(codes[0].value);
                } else {
                    setListBarcode(codes.map(code => code.value));
                }
            }
        }
    });

    const resolveBarcode = (barcode) => {
        const { BARCODEPATTERNS } = global.config;
        if (helper.IsNonEmptyString(BARCODEPATTERNS)) {
            const jsonData = JSON.parse(BARCODEPATTERNS);
            const RegExCasio = new RegExp(jsonData[0].CheckPattern, 'g');
            if (RegExCasio.test(barcode)) {
                barcode = barcode.slice(-12);
            }
        }
        resultScanBarcode(barcode);
    };

    return (
        <KModal
            onRequestClose={null}
            transparent={true}
            isVisible={isVisible}
            onBackdropPress={closeCamera}
            onSwipeComplete={closeCamera}
            swipeDirection={['down']}
            style={{ margin: 0 }}
            deviceWidth={constants.width}
            deviceHeight={constants.height}
        >
            <SafeAreaView style={{ flex: 1 }} >
                <View style={{
                    width: FRAME_WIDTH,
                    height: FRAME_HEIGHT,
                    backgroundColor: 'transparent'
                }}>
                    {device != null && (
                        <Camera
                            key={device.id}
                            style={{
                                width: FRAME_WIDTH,
                                height: FRAME_HEIGHT
                            }}
                            device={device}
                            format={format}
                            fps={format?.maxFps}
                            isActive={isActive}
                            codeScanner={codeScanner}
                            exposure={0}
                            photo={false}
                            video={false}
                            audio={false}
                            zoom={device.neutralZoom}
                        />
                    )}
                    <View style={{
                        position: 'absolute',
                        width: FRAME_WIDTH,
                        height: FRAME_HEIGHT
                    }}>
                        <MaskView bounds={{
                            width: 0,
                            height: 0,
                            top: 0,
                            left: 0
                        }} />
                    </View>
                </View>
                <ContentView
                    data={listBarcode}
                    onClose={closeCamera}
                    onSelected={resultScanBarcode}
                />
            </SafeAreaView>
        </KModal>
    );
};

export default ScanBarcodeVision;
