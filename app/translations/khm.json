{"fetchAPI": {"error_time_out": "Sorry, no response from the system. Please check the network, wifi or contact system department.", "error_connect": "Unable to connect. Please check the network or wifi.", "error_server": "Failed to connect to server. Please try again later!", "no_connected": "Unable to connect. Please check the network or wifi.", "error_get_data": "Unable to fetch data from the system!", "data_not_found": "Data not found!", "unable_connect_system": "Unable to connect to the system. please try again!", "fetch_data_server_failed": "Failed to fetch data from server. Please try again!", "error_occurred": "An error occurred. please try again!", "unexpected_error": "There was an unexpected error!", "login_at_another_device": "Your account is already logged in on another device. Please login again!", "token_expired": "Expired token. Please login again!", "please_use_wifi": "Please use internal wifi to perform this function!", "please_use_wifi_store": "Invalid job store.\nPlease select the correct job store to use the application!", "urs_pwd_incorrect": "Username or password is incorrect. Please check again!", "cannot_token": "Unable to get token"}, "login": {"btn_login": "<PERSON><PERSON>", "text_input_username": "Username", "text_input_password": "Password", "scan_qr_code": "Scan QR code:", "empty_username": "Please enter username.", "empty_password": "Please enter password.", "err_login": "Login error: ", "authen_error_get_token_1": "Unable to get access_token.", "authen_error_get_token_2": "An error occurred while saving your data. Please log in again!", "info_store_default": "User default working store information", "change_login_again": "changed. Please login again!", "dont_get_info_default": "The system didn't get your default store information. Please try again.", "buy_store_default": "You don't have the right to sell at the store. Please contact IT for permission.", "declare_default_store": "You haven't declared a default work store. Please contact IT for permission.", "urs_pwd_incorrect": "Username or password is incorrect. Please check again!", "info_user_problem": "There is a problem with the user data information, contact the IT department to check", "version_old": "The current version of the app is out of date. Please update to the latest version", "use_app": "to use the MWG POS app.", "version": "Version", "version_old_cus": "of the current app is out of date. Please update to the latest version", "info_user_not_found": "User information is not available.", "mwg_verify_info": "MWG is verifying your information", "cannot_authen": "No authentication information, contact IT to check.", "cannot_information_work": "There is a problem with the user's working information, contact the IT department to check.", "cannot_information_warehouse": "There is a problem with the user's workspace information, contact the IT department to check.", "please_update_new_version": "the current app is old or buggy. Please update to the latest version to use the MWG POS app.", "permission_err": "Permissions error. Please contact IT"}, "menu": {"home_screen": "Home", "store": "Change Store", "management_order": "Management Order", "sale": "Sale", "inventory_stock": "Product Inventory", "management_sim": "Management SIM", "complaint_refund": "Complaint Refund", "log_out": "Log out", "installment": "Management Installment", "cod_pay": "Codpay", "additional_promotion": "Additional Promotion", "search_imei_history": "Search IMEI history", "change_watch_battery": "Change watch battery", "f88_loan_contract": "F88 loan contract", "screen_sticker": "<PERSON> Sticker", "product_returns": "<PERSON><PERSON><PERSON> tr<PERSON> hàng", "view_promotion_info": "Promotion Information", "search_inout_voucher": "Search In/Out Voucher", "scan_qrcode_id": "Scan QR Code ID", "new_product_request": "Request new product", "use_guide": "Use Guide", "output_receipt_management": "Output Receipt Management", "menu_search_placeholder": "Search feature. Ex: Sale", "all_features": "All features: ", "button_your_features": "Your features", "your_features": "Your features: ", "button_view_all": "View all", "new_features": "New features: ", "search_result": "Search results: ", "product_infor": "Product request", "sim_price_report": "Sim price report", "restock": "Restock request", "restock_request_management": "Restock request management"}, "sale": {"place_holder_search_product": "Enter Code, Name, IMEI, SIM", "placeholder_search_pharmacy": "Enter Code, Name, IMEI", "footer": "Copyright © 2019 MWG. Mobile World Investment Corporation", "version": "Sales version", "delete": "Are you sure you want to delete?", "favorites_list_question": "From favorites list?", "color": "Color: ", "no_product_in_cart": "There are no products in the cart currently", "go_to_cart": "Go to cart", "favorites_product": "Favorite product: ", "you": "You have", "history": "Search history :"}, "saleOrder": {"title": "CART", "description_crate_order_success": "Shopping cart created successfully.", "code_shopping_cart": "Shopping cart code ", "description_separate_sale_order": "The shopping cart above has 1 export request", "description_separate_sale_order_1": "The shopping cart above is separated into ", "description_separate_sale_order_2": " export request", "description_separate_sale_order_3": "with the following code:", "code_require_output": "Export request code: ", "type_require_output": "Export request type: ", "output_store": "Output store: ", "delivery_type": "Receiving form: ", "address": "Delivery address: ", "output_type": "Export form: ", "taxId": "TaxID: ", "error_create_order": "System error. Can't create orders at the moment!", "non_sale_order_detail": "Can't get order details!", "sum_price_sale_order": "Total payment: ", "pay_type": "Form of payment:", "advance": "Advance:", "note": "Note:", "sim_requirements": "SIM handling requirements: ", "create_profile": "Create a profile:", "export_cash": "Collection & Delivery", "opt_exists": "Previously generated OTP is still valid. Please wait 1 minute to get a new code.", "otp_verified": "OTP has been verified before. Please get a new one for verification.", "otp_expried": "OTP has expired. Please get a new one for verification.", "opt_found": "The OTP is invalid."}, "common": {"enter_numberphone_check": "Please enter phone number to check", "quanlity": "Quantity: ", "price": "Price: ", "stock": "Stock: ", "discount": "Discount: ", "notification_uppercase": "Notification", "notification": "Notify", "btn_notify_try_again": "Retry", "btn_back": "Back", "btn_confirm": "Submit", "inventory": "Inventory", "store": "Store", "orderd": "Ordered", "state": "Status: ", "warranty_time": "Genuine warranty until date: ", "non_warranty_info": "No genuine warranty information", "warranty_at_store": "Warranty at TGDD: ", "no_have": "Do not have ", "non_description": "No description available", "search_other_product": "Do you want to be sure to look for other products?", "search_other_product_confirm_search": "Search", "btn_save_watch_list": "Save to watch list", "month": "month", "day": "day", "before": "Before", "delivery_time": "Delivery time: ", "cost_price": "Amount:", "inventory_status": "Status:", "view_detail": "View detail", "code_product": "Product code: ", "error_load_data": "The data load has failed. Please press the retry button to reload!", "btn_close": "Close", "btn_skip": "<PERSON><PERSON>", "btn_accept": "Accept", "btn_cancel": "Cancel", "data_not_found": "Data not found", "text_input_search_keyword": "Enter key to search...", "btn_continue": "Continue", "customer": "Customer: ", "phone_number": "Phone number: ", "slugon_delete": "Deleting a product from the cart will remove the discount code and promotion associated with the product. Would you like to remove: ", "remove_list": "from the bundled list?", "btn_view_more": "View more", "btn_view_less": "View less", "delete": "Delete", "btn_add_to_cart": "Add to cart", "placeholder_search_product": "Enter Code/Name", "customer_accept": "<PERSON><PERSON><PERSON><PERSON> hàng đồng <PERSON>", "customer_decline": "<PERSON><PERSON><PERSON><PERSON> hàng từ chối"}, "installmentsteps": {"stepone_client_idcardtypecmnd": "ID card", "stepone_client_idcardtypecccd": "ID card"}, "arrearsResults": {"headerListStaff": "Arrears staff information"}, "inventory_share": {"func": "Function", "manager_area": "Management area", "inventory": "Inventory", "view_inventory_difference": "View inventory difference", "view_inventory_result": "View inventory result", "view_deduction_result": "View arrears results", "result_inventory_difference": "Inventory difference results", "create_collection": "Create a arrears request", "manager_inventory_requitment": "Arrears request management", "completed": "Completed", "access_camere": "Allow camera access", "app_mwg_acccess_camera": "MWG app need permission to access your camera", "area_list": "Area list", "area_list_auto_create_by_system": "The system automatically creates 2 inspection zones for showroom and warehouse", "applying": "Applying", "apply": "Apply", "add_area_suceess": "Successfully added area", "add_new_area": "Add new area", "enter_area_name": "Input area name", "new_area_name": "Area name", "add_area": "Add area", "choose_type_check": "Choose inventory type", "check_suggestion": "Suggestion", "check_inspection": "Periodic", "check_proactive": "Proactive", "not_found_inven_history": "Inventory history not found", "not_found_period_inven": "Inventory term not found", "no_premission": "You don't have permission", "select_start_time": "Choose a start time", "not_completed": "Not Completed", "get_start": "Start", "finished": "Finished", "view_inven_history": "View inventory history", "search": "Search", "select_area": "Choose area", "product_imei": "Product with IMEI", "product_no_imei": "Product no IMEI", "list_product_no_imei": "Product list no IMEI", "list_product_imei": "Product list has IMEI", "input_lots_code_name": "Input lots code/ product code/ product name", "not_found_product_info": "Product information not found", "not_found_info_area": "Inventory area not found", "not_found_products": "Product not found", "products_not_declare_in_term": "This product has not been declared in this inventory term", "product_has_checked": "This product is being checked by %{usercheck}. Please select other product", "period_inven_blocked": "Period inventory blocked, please contact to management!", "see_more": "See more...", "not_found_info_about_product": "Couldn't find any IMEI information about the product", "not_found_info_product_status": "Information status products not found", "checking": "Checking", "checked": "Checked", "change_stock": "Change stock", "quantity": "Quantity", "choose_products": "Choose product", "input_imei_lots_name": "Input IMEI/ lots code/ product code/ product name", "status": "Status", "p_imei": "Product IMEI", "please_check_again": "Please check again!", "list_imei_export": "IMEI list exported while inventory", "cancel_operation": "Are you sure you want to cancel this operation?", "filter": "Filter", "product_transit_not_enter": "Product is on the way, no manual input is allowed!", "product_status_not_yet_inven": "Product status not yet inventory", "product_has_inven_change": "The product you are checking has an inventory change, You must recheck this product in other areas!", "product_status_available": "Status product available", "number_check": "Number of checks", "inven_detail": "Inventory Detail", "status_stock_cus": "Stock status", "status_check_cus": "Check status", "status_stock": "Stock status", "status_check": "Check status", "select_inven_status": "Select inventory status", "pl_select_inven_status": "Please select check status", "input_imei": "Input IMEI", "export_while_checking": "Export while checking", "import_while_checking": "Import while checking", "out_stock": "Out of stock", "processing_product": "Processing product", "finish_check": "Finished checking", "confirm_inventory_amount": "Confirm inventory amount", "update": "Update", "product_deviation": "Product deviation", "product_check_completed": "Products must be checked to complete", "not_fount_info_product": "Product information not found", "have_imei": "Have IMEI", "view_disparity_result": "View differences result", "no_imei": "No IMEI", "area": "Area", "remaining_stock": "Qty stock", "quantity_check": "Num checks", "total_check": "Total checks", "product_excess": "Excess product", "product_lack": "Missing product", "deduction_list": "Arrears list", "deduction_expect": "Expected arrears", "total_dedution": "Total arrears", "products": "Products", "deduction_amount": "Arrears amount", "choose_inven": "Choose inventory term", "company_indemnify": "Company indemnify", "staff_indemnify": "Staff indemnify", "external_processing": "External processing", "total_amount": "Total amount", "staff_deduction": "Staff's deduction", "input_staff_dedution": "Input staff deduction", "not_found_info_dedution_expect": "Not found information deduction expect", "not_found_info_dedution_list": "Not found information deduction list", "not_found_staff_info": "Not found Staff information", "not_found_dedution_info_detail": "Not found deduction information detail", "export_lost": "Export lost", "change_product": "Change product", "amount": "Amount", "import_more": "Import more", "amount_staff_indemnify": "Amount staff indemnify", "input_amount": "Input amount", "no_process": "No process", "processed": "Processed", "cash_receipt": "Cash Receipt", "not_save_total_deduction_diference": "You cannot save because the total deduction amount of other staffs is difference the total deduction amount at store", "not_save_total_duduction_less": "You cannot add because the total deduction amount of staffs is less then the total deduction amount at the store", "deduction_processing": "Deduction processing", "access_result_processed": "Processed arrears results", "id_staff": "Staff ID", "name_staff": "Staff name", "save_info": "Save information", "qty": "Qty", "id": "ID", "history": "history", "choose_end_time": "Choose the end time", "list_suggest": "List of Suggested Products", "p_list": "Product list", "choose": "<PERSON><PERSON>", "choose_status": "Choose status", "unchecked": "Unchecked", "status_diff": "Status difference", "pl_input_area_name": "Please input area name", "check_area": "Check area", "choose_area": "Choose area", "choose_time": "Choose time", "not_add_emp_is": "You can't add because of employees total arrears is", "less_than_total_amount_is": "Less than the total amount arrears at the store is", "not_save_emp_is": "You can't save because the total arrears of the staff is", "diff_from_total_amount_is": "different from the total amount of arrears at the store is", "amount_paid": "Amount to be paid", "info_staff_arrears": "Arrears staff information", "imei_of_product": "IMEI of the product", "cannot_data": "Data not found!", "imei_not_in_format": "The entered IMEI is not in the correct format. Please check again", "": "The IMEI you entered does not exist in the system, do you still want to enter it for inventory?", "begin_term_time": "The begin time of this term ", "prdchangesttquantity": "change status"}, "detail": {"no_infomation_pre": "No pre-order lock information", "color": "Color: ", "date": "DATE", "quantity": "QUANLITY", "check_phone_number": "CHECK PHONE NUMBER", "phone_number_apply_promotion": "Phone number to apply promotion:", "btn_done": "Done", "btn_add_to_cart": "ADD TO CART", "consult_product_service": "Consult product's services", "store_not_found": "Store not found", "receive_within_5_7_days": "Receive goods in 5-7 days", "quantity_short": "Quantity", "size_choice": "SIZE", "promotion_still": "Promotion is still", "applied": " applied pressure", "btn_at_store": "At the store", "btn_at_home": "At home", "btn_create_store": "Create store", "btn_other_store": "Other store", "select_time": "Choose time:", "old_customer": "Old customers", "transfer_store": "Transfer store:", "transfer_po": "Transfer PO:", "output_store": "Output store: ", "sale_installment": "INSTALLMENT PURCHASE", "new": "New", "secondStock": "Used", "display": "Display", "on_sale": "On sale", "non_warranty_info": "No genuine warranty information", "btn_transfer_record": "Transfer Record", "fifo": "FIFO", "feature_product": "Salient\nfeatures", "config_product": "Parameters\ndetails", "installment_service": "Consultant\ninstallation", "instalment_service": "Consultant\ninstallment", "emei": "IMEI: ", "total_reward": "Point standard bonus", "standard_point": "Point Hot bonus", "close_member_point": "Loyalty loyalty points provisional calculation", "cannot_apply_promotion": " - This promotion isn't applicable in conjunction with the program: ", "promotion_has": "Promotion is still ", "time_remained": " applicable times", "text_input_search_name": "Enter product code, Product name", "text_input_phone_number": "Enter phone number", "select_package": "Select package:", "config_product_uppercase": "DETAILED SPECIFICATIONS", "feature_product_uppercase": "HIGHLIGHTS", "fifo_uppercase": "FIFO STOCK INFORMATION", "instalment_program_uppercase": "INSTALLMENT PROGRAM", "create_date": "Create date: ", "color_or_quantity": "Color (Qty): ", "customer_phone_number": "SĐT: ", "sale_agent": "Salesman: ", "create_store": "Store create:", "support_phone_number": "Support phone number:", "create_agent": "Employee creates:", "lock_information_uppercase": "THE LOCK INFORMATION", "text_input_address_to_find_store": "Enter keywords to find store", "select_store_uppercase": "CHOOSE STORE", "text_input_phone_number_contact": "Enter phone number contact", "text_input_name_contact": "Enter first & last name contact", "text_input_address_contact": "Enter house number, street name", "text_input_note": "Note", "distance": "Distance: ", "vehicle": "Vehicle: ", "text_input_street_to_find_store": "Enter street name to quickly find store", "check_box_receive_at_store": "You can come and pick up the goods at the store", "check_box_delivery": "Store delivers goods to", "check_box_delivery_po": "PO delivers goods to", "contact_address": "Address: ", "contact_name": "Contact person's name: ", "non_imei": "The product has no IMEI", "warranty_at_store": "Warranty at TGDD: ", "spare": "Accessory: ", "not_available": "Not available", "promotion_by_delivery_method": "Promotion in the form of delivery", "sale_with_delivery_method": "Sold with delivery method", "attached_product_promotion": "Promotion of bundled products", "sim_register_procedure": "SIM REGISTRATION PROCEDURE", "id_card": "Original ID card (issued under 15 years) or Citizen's Identity Card (with validity) or Passport (with validity) of the subscriber.", "potrait": "Portrait photo of subscriber owner at the time of transaction.", "package_information_uppercase": "PACKAGE INFORMATION", "product_information_not_available": "No product information", "promotion": "Promotion:", "attached": "Sold with:", "no_store_found": "Can't find a supermarket to buy goods", "no_store_delivery_found": "Can't find a supermarket to deliver the goods to", "expired_time": "Delivery time exceeds the maximum allowable hold time of the Export Request Type. Or the system fails to retrieve the delivery load", "no_delivery_information_found": "No shipping information", "no_verified_information": "No authentication information. Please contact IT to check!", "no_product_information": "No product information available.", "no_instalment_partner_information": "No installment partner information", "no_instalment_information": "No Installment Program Information", "no_fifo_information": "No FIFO information", "no_detail_information": "No detailed configuration information", "no_feature_information": "No feature information available", "no_lock_information": "No product Lock information", "no_package_information": "No package information", "no_sim_price_information_found": "Can't get SIM price information", "products": "Products", "promotions": "Promotions", "attachments": "Sold with", "delivery_method": "Form of delivery", "delivery_method:": "Form of delivery: ", "no_promotion": "No promotion", "no_attachments": "No Sales with", "please_select_sim_package": "Please select SIM package", "please_select_promotion": "Please select promotion required:", "dismiss_promotion": "There is a promotion that hasn't been selected. Are you sure you want to skip the promotion?", "no_product_in_cart": "Current cart hasn't products", "view_lock_product": "View order lock", "go_to_cart": "Go to cart", "export_request": "Request export: ", "change_store_request": "Request transfer store:", "exchange_request": "Request to convert goods:", "report": "Commodity record:", "please_enter_phone_number": "Please enter the phone number", "please_follow_phone_number_format": "Please enter the correct phone number format", "apply": "Apply", "select_hour": "Choose hour", "select_date": "Choose date", "please_enter_contact_phone_number": "Please enter contact phone number", "please_enter_10_digits_phone_number": "Please correct 10-digit phone number format", "please_enter_correct_phone_number_header": "Please enter the correct phone number", "please_enter_contact_name": "Please enter the contact's first and last name", "please_enter_delivery_address": "Please enter shipping address", "please_select_delivery_time": "Please choose a delivery time", "please_select_store": "Please choose a supermarket to pick up the goods", "select_province": "Select Province/City", "select_district": "Select Town/District", "please_select_delivery_store": "Please choose a supermarket to deliver the goods to", "not_enough_inventory": "Product is not in stock", "delivery_at_home": "Home delivery:", "delivery_at_store": "Delivered at the store:", "remove_instalment_information": "Do you want to delete your current installment information?", "export_code": "Export request code: ", "change_store_code": "Code to request warehouse transfer: ", "exchange_code": "Code to request a change of goods: ", "cancel_code": "Deactivation request code: ", "no_installment_support_information": "No installation advice", "no_instalment_support_information": "There is no information on installment advice", "no_description_information": "No description available", "no_check_information_found": "No timekeeping data found at the supermarket", "all_store": "View all supermarkets", "condition": "Condition:", "notice": "Notice", "unknown": "Unknown", "deposit_date": "Deposit date: ", "lock_order_type_date": "Allocation date: ", "keep_lock_all_day": "Keep the goods locked until the end of the day: ", "output_type": "Type EXR: ", "full_product": "Sufficient stock", "no_full_product": "Not enough stock", "lock_information_pre_order_uppercase": "PRE-ORDER LOCK INFORMATION", "male": "Male", "female": "Female", "size": "Choose size", "quantity_promotion": "Promotion applicable number:", "transport": "Transport: ", "motorbike": "Motorbike", "truck": "Truck", "warning_get_promotion": "Promotion information is not valid. Please check again", "deactivated_installment_partner_notice": "The {{partnerInstallmentName}} partner is experiencing issues and is temporarily unable to process deferred payment applications. Please advise customers to switch to another partner or contact the Consumer Finance Department for assistance if needed."}, "cameraDoc": {"pls_document_into": "Please frame your document", "uncorect_document": "The document's face image is incorrect. Please retake it.", "blur_picture": "The photo is blurry, missing or flared. Please retake it.", "blur_picture1": "The photo is blurry, missing or flared. Please retake it.", "face_into": "Put your face in the circle", "move_cam_close": "Move your camera closer", "move_cam_far": "Move your camera further away", "more_face": "There are many faces in the circle", "blur_pls_again": "The photo you took is blurry or not a real portrait. Please try again"}, "shoppingCart": {"validation_gender": "Please select customer gender.", "product_intend": "(Product does not participate in the total promotion)", "customer_info": "Customer information", "customer_info_uppercase": "CUSTOMER INFORMATION", "customer_print_company_bill": "Customers print company invoices", "old_customer": "Old customers", "ID_card_number": "ID number:", "take_ID_and_student_card_pictures": "Shoot ID & STUDENT CARD", "btn_delete_cart": "CANCEL CART", "btn_create_sale_order": "CREATE ORDER", "cart_total_price": "Total Order Amount", "not_rounded": "(Unrounded)", "total_customer_deposit": "Total amount to be paid in advance", "additional_fee": "Surcharge", "provisional_total_membership_score": "Total cumulative points of provisional plan", "btn_adjust_price": "Price Adjustment", "btn_create_competitive_price": "Create competitive price", "text_input_customer_tax": "Tax id", "placeholder_cccd": "Enter CardID customer", "text_input_customer_company_name": "Company name: ", "text_input_customer_company_address": "company address: ", "text_input_customer_company_phone": "company phone: ", "text_input_contact_phone": "Contact phone: ", "text_input_contact_name": "Contact person's name:", "text_input_contact_address": "Contact address: ", "text_input_phone": "Phone number:", "text_input_name": "Name:", "btn_check": "Check", "text_input_address": "Address:", "text_input_email": "Email:", "text_input_cost_contract": "Application fee:", "text_input_deposit": "Prepaid amount:", "picker_loan_term": "Loan term:", "text_input_payment_per_month": "Monthly payment amount:", "popup_phone_coupon_requires_phone": "Coupon code requires entering phone number", "placeholder_customer_tax": "Enter tax code", "placeholder_customer_company_name": "Enter company name", "placeholder_address": "Enter address", "placeholder_email": "Enter email", "placeholder_phone": "Enter phone number", "placeholder_name": "Enter first & last name", "placeholder_ID_card_number": "Enter ID/CCCD number", "total_promotion": "Promotion on total order", "discount_product_amount_promotion": "Promotion buy more, get more", "use_Coupon": "Apply coupon", "btn_apply": "Apply", "delivery_type": "Delivery form: ", "installment_payment": " (Installment)", "product_not_valid_for_order_promotion": "(Product does not participate in the total promotion)", "adjust_price": "Adjust price: ", "competitive_price": "War Price:", "product_price": "Into money: ", "btn_confirm_uppercase": "CONFIRM", "btn_update_uppercase": "UPDATE", "btn_update": "UPDATE", "btn_skip_verify": "Bypass Authentication", "btn_agree_use": "Agree to Use", "btn_disagree_use": "Disagree to use", "confirm_relationship_with_customer": "Confirming the relationship between <PERSON> and the Customer:", "type_IMEI_SIM": "Enter IMEI SIM", "scan_employee_code_adjust_price": "Scan the employee code allowed to adjust the selling price:", "current_price": "Current selling price:", "adjust": "Adjust:", "price_after_adjust": "Price after adjustment:", "adjust_price_reason": "Reason for adjustment:", "attach_files": "Add attachment: ", "add_files": "Add files", "max_attached_files_quantity": "(Max: 3 files)", "delete": "Delete", "remaining_limit": "Remaining limit", "provisional": "(temporary calculation)", "discounted_value": "Reduced value:", "price_after_competition": "Price after the war price:", "guide": "Guide:", "employee_type_OTP": "(Staff enter OTP and press \"Confirm\")", "skip_verify_guide": "When selecting \"Skip authentication\" the staff instructs customers to use the App registered with their purchase number, scan the QR Code on the receipt to authenticate.", "employee_type_ID_code": "(Employees enter or scan \"Identity Code\" taken from the Loyalty App and press \"Confirm\")", "OTP_then_apply": "Enter the OTP and select \"Apply\"", "OTP_then_confirm": "(Staff enter OTP and press \"Confirm\")", "caution": "Note: ", "for_this_order": " (For this order)", "total_price_after_discount": "Total amount after discount: ", "deposit_delivery_cost": "Deposit & DELIVERY FEES", "delivery_cost": "DELIVERY FEES AND SHIPPING FEES", "membership_score": "ACCUMULATE POINTS", "discount_product_amount": "BUY MULTIPLE DISCOUNT", "product": "Product", "quantity": "Qty", "more_discount": "Extra discount", "total_discount": "Total value reduction", "total_more_discount": "Total additional value reduction: ", "more_discount_caution": "Note: The extra discount is calculated on the listed selling price (no promotion)", "finish": "Finish", "choose_a_plan": "Select package:", "apply_times": " applicable times", "fee": "Fee:", "IMEI": "IMEI:", "quantity_full": "Quantity: ", "output_store": "Output store:", "btn_receive_OTP_call": "Receive OTP Calls", "btn_receive_OTP_message": "Receive OTP Messages", "change_phone_number": "Change phone number\npurchase", "condition": "Condition:", "promotion_until": "Promotions remain", "text_input_contract_code": "Contract number: ", "lf_contract_employee": "Documentation staff: ", "lf_dealer_product_code": "Agent/Partner ID: ", "at_store": "At the supermarket", "at_home": "At home", "promotion": "Promotion", "bundle_sale": "Sold with", "promotion_delivery_type": "Promotion in the form of delivery", "bundle_sale_delivery_type": "Sold with delivery method", "competitive_price_opponent": "Price war opponent:", "opponent_price": "opponent price", "MWG_price": "MWG selling price", "opponent_inStock": "Opponent's inventory", "take_front_ID_picture": "Take a photo of your Card ID on the front:", "take_front_student_card_picture": "Take a picture of your student ID card on the front:", "take_front_grab_driver_picture": "Take a picture of your app driver:", "take_front_exam_score": "Take a picture of exam score:", "placeholder_coupon": "Enter Coupon", "placeholder_OTP": "Enter OTP", "placeholder_ID_code": "Enter the identifier", "placeholder_IMEI": "Enter IMEI SIM", "btn_skip_uppercase": "Enter identifier", "btn_continue_uppercase": "CONTINUE", "picker_opponent": "Choose an opponent", "note": "Note: ", "contact_address": "Address: ", "employee": "Staff: ", "department": "Department: ", "position": "Position: ", "ft_name": "First and last name:", "delivery_fee": "Shipping fee: ", "deposit": "Deposit: ", "deposit_time": "Guests must deposit in advance: ", "deposit_amount": "Guest must pay in advance: ", "deposit_rate": "<PERSON><PERSON>: ", "shipping_fee": "Delivery Fee:", "accumulated_score": "Accumulated points:", "male": "Male", "female": "Female", "cannot_information_user": "Cannot find user information to adjust price", "upload_image_error": "Error uploading image", "cannot_data_price": "Cannot get price war data", "no_verified_information": "No credentials", "code_verified_incorrect": "Invalid authentication code", "cannot_information_IMEI_SIM": "No SIM IMEI information.", "no_package_information": "No package information", "no_information_payment_month": "No monthly payment information.", "btn_retry_uppercase": "RETRY", "delete_information_cart": "Are you sure you want to delete the current shopping cart information?", "please_enter_coupon": "Please enter Coupon code", "notification_update_cart": "The cart is undergoing price or price adjustment, so the discount code cannot be applied. If you want to apply this discount code, please cancel the price adjustment or price war.", "notification_cart_coupon": "The cart has been applied with a discount coupon code, so the price cannot be adjusted. Please cancel the discount coupon to adjust the price!", "please_enter_tax_code": "Please enter tax code", "validation_tax": "The tax code must be exactly 10 numeric characters or exactly 14 characters, where the 11th character is a dash, the rest of the characters are numbers.", "validation_tax_cam": "Tax code must be true 14 characters, where the first character is a letter, the 5th character is a dash, the rest are numbers.", "validation_company_name": "Please enter Company name", "validation_company_address": "Please enter company address", "validation_customer_name": "Please enter the customer's first and last name", "validation_phone_number": "Please enter the correct 10 digit phone number", "validation_phone_number_1": "Please enter the correct phone number", "validation_phone_number_rim": "Please enter the phone number", "validation_CMND_rim": "Please enter ID/CCCD number", "validation_CMND_number": "Please enter correct 9 or 12 digits ID/CCCD number", "validation_image": "Please fully update additional images", "validation_prepayment": "Please enter prepayment amount", "validation_ID_number": "Please enter CardID", "validation_CARD_ID_number": "Please check number CardID before creating an order.", "validation_email": "Your email is invalid.", "please_choose_term_loan": "Please select loan term", "please_choose_relation_ship_type": "Please select the relationship with the customer. If you have any questions please contact your superior or user 17269 - <PERSON><PERSON><PERSON>", "please_enter_information_IMEI_SIM": "Please enter the IMEI information of the SIM product", "warning_no_enter_phone_number_customer": "You are creating an order without ENTERING the customer's PHONE NUMBER. Phone number may affect warranty lookup information, electronic invoices, membership points accumulation...\nDo you want to continue create order?", "warning_no_choose_promotion": "There is a promotion that has not been selected. Are you sure you want to skip the promotion?", "sure_create_order": "Are you sure you want to create an order?", "validation_promotion": "Please select promotion required:", "warning_delete_order": "Deleting a product from the cart will remove the discount code and promotion associated with the product. Do you want to delete:", "out_card": "Out of cart?", "out_list": "From slod list with?", "no_adjust_price": "No pẻmission to adjust price", "please_enter_adjusted_price": "Please enter an adjusted price lower than the selling price", "please_enter_reason_adjust_price": "Please enter price adjustment text", "reduce": "Reduce", "augment": "Increase", "cannot_adjust_price": "No permission create competitive price", "please_upload_mandatory_file": "Please update 2 required attachments", "description_scan_identity_1": "(Please ask the staff who is allowed to adjust the selling price to open the MWG app, click on the Mobile World logo on the left corner of the screen, select Identity code)", "description_scan_identity_2": "(Please ask authorized staff to review the price to open the MWG app, click on the Mobile World logo on the left corner of the screen, select Identity code)", "attachment_must_less_than_3MB": "Attached file must be less than 3Mb.", "file_format": "File format", "not_supported": "Unsupported.", "no_information_adjust_price": "No information about price war opponent", "validate_empty_otp": "Please enter OTP", "validate_otp": "Please enter the correct 4-digit OTP", "validate_otp_4": "Please enter the correct 4-digit OTP", "installment_otp_guide": "This OTP code is used to verify that the customer has agreed to authorize TGDD to collect and transfer information to the financial company for making installment records.", "pin_otp_guide": "This OTP is used to verify that the customer has agreed to change the Watch battery.", "sticker_otp_guide": "This OTP is used to verify that the customer has agreed to re-stick the screen.", "add_sale_otp_guide": "This OTP is used to verify that the customer buying previous order has agreed to create this order.", "otp_sent": "OTP has been sent to number", "please_enter_id_code": "Please enter identifier", "please_enter_4_digits_id_code": "Please enter the correct 4-digit identifier", "skip_authentication": "You want to bypass purchase validation?", "guide_1": "1. Customers who buy goods at MWG will earn loyalty points.", "guide_2": "2. Purchase information needs to go through the verification step to earn points.", "guide_3": "3. Employees need to assist customers in performing authentication when purchasing goods to ensure customers' interests.", "note_description": "Adding/removing/editing products, changing to installments, or creating 2 orders at the same time may lead to customers not being able to apply points!!!", "guide_4": "\t1. You thank customers for sticking with and supporting the company.", "guide_5": "\t2. You inform about the offer only for loyal customers (below). Customers will not be able to apply this discount when changing products, order value.", "cannot_apply_promotion": " - This promotion is not applicable in conjunction with the program: ", "male_1": "Male", "female_1": "Female", "yourself": "My own order", "your_friend": "Orders that employees are familiar with (<PERSON><PERSON><PERSON>, <PERSON>, Colleagues, Former Colleagues, PG, Partners, Neighbors...", "outer_customer": "Orders of EXTERNAL CUSTOMERS", "please_take_full_info_picture": "Please take enough pictures", "please_enter_imei_product": "Please enter product IMEI", "please_enter_serial_product": "Please enter product SERIAL", "imei_already_exists": "IMEI already exists in order", "placeholder_warranty": "Enter Warranty IMEI", "pls_enter_total_prepaid": "Please enter prepaid amount.", "please_enter_id": "Please enter ID", "existed_app_loyalty": "The customer has installed the VIP GIFT app.", "no_install_app_loyalty": "Customers have not installed the VIP GIFT app.", "staff_info_uppercase": "STAFF INFORMATION", "text_input_buyer_phone": "Buyer phone:", "text_input_buyer_name": "Buyer name:"}, "provincemain": {"mess_error": "No Province/City information", "mess_error1": "No District Information", "mess_error2": "No information on Ward/Commune", "mess_error3": "No nationality information"}, "editSaleOrder": {"program": "Program ", "exsit_store": "does not exist in the export store ", "check_phone_number_uppercase": "CHECK PHONE NUMBER", "phone_number_apply_promotion": "Phone number to apply promotion:", "btn_done": "Done", "cannot_apply_promotion": "- This promotion is not applicable in conjunction with the program: ", "promotion_has": "Promotion is still ", "time_remained": " times of application", "text_input_search_name": "Enter Product code, Product Name", "text_input_phone_number": "Enter phone number", "please_select_promotion": "Please select promotion REQUIRED select:", "dismiss_promotion": "There is a promotion that hasn't been selected. Are you sure you want to skip the promotion?", "please_enter_phone_number": "Please enter the phone number", "please_follow_phone_number_format": "Please enter the correct phone number format", "btn_apply": "Apply", "delivery_at_home": "Delivery at home: ", "delivery_at_store": "Delivery at store: ", "contact_address": "Contact address: ", "contact_name": "Contact name: ", "promotion_by_delivery_method": "Promotion in the form of delivery", "sale_with_delivery_method": "Sold with delivery method", "attached_product_promotion": "Promotion of bundled products", "quantity_short": "Qty", "condition": "Condition: ", "notice": "Notice", "employer_adjust_price": "Scan the employee code allowed to adjust the selling price:", "description_scan_identity_2": "(Please ask authorized staff to browse the pricing and open the MWG app, click on the Mobile World logo on the left corner of the screen, select Identity code)", "limit_remain": "Remaining limit", "temporary": "(provisional)", "current_price": "Current selling price:", "discount_value": "Reduced value:", "adjusted_price": "Price after the war:", "add_file": "Add attachment: ", "max_files": "(Max: 3 files)", "btn_update": "Update", "btn_delete": "Delete", "btn_add_file": "Add file", "btn_continue_uppercase": "CONTINUE", "please_enter_adjusted_price": "Please enter an adjusted price lower than the selling price", "please_upload_mandatory_file": "Please update 2 required attachments", "agent": "Staff: ", "department": "Part: ", "position": "Position: ", "attachment_must_less_than_3MB": "Attachments must be less than 3Mb.", "picker_opponent": "Chosse competitor", "opponent": "Price war opponent", "opponent_price": "Competitor price", "MWG_price": "Mwg price", "opponent_inventory": "Competitor Inventory", "cannot_adjust_price": "No permission create competitive price", "file_format": "File format", "not_supported": "Unsupported.", "detail_specification_uppercase": "DETAILED SPECIFICATIONS", "detail_specification": "Parameters\ndetails", "outstanding_features_uppercase": "HIGHLIGHTS", "outstanding_features": "Salient\nfeatures", "color": "Color", "date_uppercase": "Date", "quantity_uppercase": "Quantity", "fifo_stock_info_uppercase": "FIFO STOCK INFORMATION", "phone_check_uppercase": "CHECK PHONE NUMBER", "promotion_phone_number": "Phone number to apply promotion:", "finish": "Finish", "btn_add_product": "ADD PRODUCTS", "new": "New", "used": "Used", "display": "Display", "no_official_warranty": "No genuine warranty information", "FIFO": "FIFO", "IMEI": "IMEI", "setup_consult": "Consultant\ninstallation", "installment_consult": "Consultant\ninstallment", "exact_bonus": "Standard Bonus", "btn_update_uppercase": "UPDATE", "transfer_store": "Shipping warehouse:", "delivery_method": "Delivery form", "delivery_method:": "Delivery form: ", "select_date": "Select Date", "select_time": "Select time: ", "select_hour": "Choose houser", "expired_time": "Delivery time exceeds the maximum allowable Hold time of the Export Request Type. Or the system fails to retrieve the delivery load", "old_customer": "old custommer", "text_input_name": "Enter name", "text_input_address": "<PERSON><PERSON>", "please_enter_contact_phone_number": "Please enter contact phone number", "please_enter_10_digits_phone_number": "Please correct 10-digit phone number format", "please_enter_correct_phone_number_header": "Please enter the correct phone number", "please_enter_contact_name": "Please enter the contact's first and last name", "please_enter_delivery_address": "Please enter shipping address", "please_select_delivery_time": "Please choose a delivery time", "btn_at_home": "At home", "output_store": "Output store: ", "distance": "Distance: ", "vehicle": "Vehicle: ", "shipping_fee": "Delivery Fee:", "btn_at_store": "At store", "debt_must_be_0": "The amount owed must be zero", "change_request_with_expense": "EDIT EXPORT REQUEST HAS SPENDING MONEY", "expense": "ACTUALLY SPENT", "voucher": "Vouchers", "vip_point": "VIP gift points", "qr_cash": "Money QR", "credit": "Card money", "receive_cash_at_store": "There are customers directly at the supermarket to receive money", "create_expense_note": "Check here, the system will automatically generate payment voucher for you", "cash": "Cash", "debt": "Still owed", "old_output_request": "Old export request:", "receipt": "Cash Collected", "available_cash": "Money can carry over", "without_voucher": "\n(Not including gift voucher)", "new_output_request": "New export request:", "must_receive": "The amount to be collected", "expense_for_customer": "Expenses for guests:", "charge": "Residual money after collection", "voucher_restore": "recovery gift voucher", "must_expend": "Money to be spent", "invalid_voucher": "Gift voucher used", "payment": "Payment Amount:", "output_request_applied": "EXR Apply:", "please_select_pos": "Please select a POS", "please_enter_expense_code": "Please enter verification code", "expense_code_must_have_6_letters": "The standard code must be exactly 6 characters", "please_enter_payment": "Please enter payment amount", "expense_more_than_must_expend": "Total actual expenditure is greater than the amount to be spent", "existed_expense_code": "The standard code already exists in the order", "pos": "POS machine: ", "select_pos": "Select POS", "text_input_appv_code": "Enter code APPV", "get_appv_code": "How to get APPV Code", "transaction_confirm": "Payment confirmation", "add_card": "Add card", "output_request_short": "EXR", "please_select_cancel_reason": "Please choose a reason for cancellation", "please_enter_cancel_content": "Please enter cancellation text", "confirm_instalment_uppercase": "CONFIRMATION OF PAYMENT DOCUMENTS", "cancel_reason": "Reason for cancellation:", "select_reason": "Choose a reason", "text_input_cancel_content": "Enter cancellation text", "scan_agent_code": "Scan the employee code authorized to create a request to cancel the installment file:", "invalid_agent_code": "(Please ask the authorized staff to confirm creating a request to cancel the installment file, open the MWG app, click on the Mobile World logo in the left corner of the screen, select Identity code)", "agent_information": "Staff information:", "agent_id": "Employee code: ", "sale_order": "Order ", "record": "have a profile", "state": "in status", "confirm_information": ".You need to confirm the information to cancel the installment file", "reason_7": "Customers who don't buy anymore automatically cancel", "reason_4": "Customers change prepayment or loan term", "reason_5": "The customer's family does not agree", "reason_6": "Contract fraud", "reason_1": "Incorrect information entered", "reason_2": "Customers change products", "reason_3": "Customers don't buy anymore", "receipt_expense_information": "INFORMATION FOR CREATE COLLECTION COLLECTION", "available_expense": "Money can be spent", "alternative_output_request": "Request export instead:", "deposit_delivery_cost_uppercase": "Deposit & DELIVERY FEES", "product_price": "Into money: ", "delivery_fee": "Shipping fee: ", "deposit": "Deposit: ", "deposit_time": "Guests must deposit in advance: ", "deposit_rate": "<PERSON><PERSON>: ", "deposit_amount": "Guest must pay in advance: ", "delivery_cost_uppercase": "DELIVERY FEES AND SHIPPING FEES", "additional_fee": "Surcharge: ", "additional_fee_2": "Surcharge:", "membership_score_uppercase": "POINTS ACCUMULATE", "discount_product_amount_uppercase": "BUY MULTIPLE DISCOUNT", "product": "Product", "additional_discount": "Additional reduction", "total_discount": "Total value reduction", "additional_discount_caution": "Note: The extra discount is calculated on the listed selling price (no promotion)", "text_input_IMEI_SIM": "Enter IMEI SIM", "select_package": "Choose a package:", "please_enter_otp": "Please enter OTP", "please_enter_6_digits_otp": "Please enter the correct 6-digit OTP", "please_enter_4_digits_otp": "Please enter the correct 4-digit OTP", "please_enter_id_code": "Please enter identifier", "please_enter_4_digits_id_code": "Please enter the correct 4-digit identifier", "create_order_successful": "Successful order creation", "btn_skip_verify": "Bypass Authentication", "customer_info_uppercase": "CUSTOMER INFORMATION", "full_name": "First and last name:", "customer_phone_number": "Phone number:", "accumulated_score": "Accumulated points:", "guide": "Tutorial:", "guide_1": "1. Customers who shop at MWG will earn loyalty points.", "guide_2": "2. Purchase information needs to go through the verification step to earn points.", "guide_3": "3. Employees need to support customers to authenticate when purchasing to ensure the interests of customers.", "text_input_id_code": "Enter identifier", "confirm_information_uppercase": "CONFIRM INFORMATION", "text_input_otp": "Enter OTP", "otp_guide_1": "Enter the OTP and select \"Apply\"", "otp_guide_2": "(Staff enter or scan \"Identity Code\" taken from Loyalty App and press \"Confirm\")", "otp_sent": "OTP has been sent to number", "so_code": "\nSO Code:", "btn_change_phone_number": "Change phone number\npurchase", "skip_authentication": "Want to bypass purchase validation?", "btn_call_otp": "Receive OTP calls", "btn_send_otp": "Receive OTP messages", "btn_agree_use": "Agree", "btn_disagree_use": "Disagree", "caution": "Cause: ", "note_description": "Adding/removing/editing products, changing to installments, or creating 2 orders at the same time can all lead to customers not being able to apply points!!!", "for_this_order": " (For this order)", "total_price_after_discount": "Total amount after discount: ", "guide_success_1": "\t1. You thank customers for sticking with and supporting the company.", "guide_success_2": "\t2.You are informed about the offer only for loyal customers (below). Customers will not be able to apply this discount when changing products or order value.", "enter_otp_then_confirm": "(Staff enter OTP and press \"Confirm\")", "mr": "Mr", "ms": "Ms", "full_payment": "Pay straight", "instalment": "Instalment", "yourself": "OWN ORDER", "your_friend": "Orders that staff are familiar with CUSTOMERS (Relatives, Friends, Colleagues, Former Colleagues, PG, Partners, Neighbors...)", "outer_customer": "Orders from OUTSIDE CUSTOMERS", "confirm_relationship_with_customer": "Confirm the relationship between You and the Customer:", "hot_bonus": "Hot bonus points", "provisional_membership_point": "Loyalty loyalty points are temporarily calculated", "choose_a_plan": "Choose a package:", "note": "Note", "warranty_at_store": "Warranty at TGDD: ", "warranty_time": "Genuine warranty until date: ", "accessory": "Accessory: ", "time_out": "Holding Expiration Time: ", "non": "None", "btn_confirm_uppercase": "CONFIRM", "adjust": "Adjust:", "price_after_adjust": "Price after adjustment:", "adjust_price_reason": "Adjustment reason:", "total_more_discount": "Total additional value reduction: ", "please_enter_reason_adjust_price": "Please enter price adjustment text", "please_select_sim_package": "Please choose a SIM plan", "not_enough_inventory": "Product is not in stock", "MWG_mark": "Copyright © 2019 MWG. Thế Giới Di Động Stock Company", "search_history": "Search History: ", "sale_version": "Sales version", "you_have": "You have", "fav_products": "Favorite product: ", "placeholder_search_product": "Enter Code, Name, IMEI, SIM", "are_you_sure_delete": "Are you sure you want to delete?", "from_fav_list": "from favorites list?", "ft_name": "First and last name:", "ft_phone": "Phone number:", "installment_otp_guide": "This OTP code is used to verify that the customer has agreed to authorize TGDD to collect and transfer information to the financial company for making installment records.", "pin_otp_guide": "This OTP is used to verify that the customer has agreed to change the Watch battery.", "sticker_otp_guide": "This OTP is used to verify that the customer has agreed to re-stick the screen.", "btn_receive_OTP_call": "Receive OTP Calls", "btn_receive_OTP_message": "Receive OTP Messages", "OTP_then_confirm": "(Staff enter OTP and press \"Confirm\")", "placeholder_OTP": "Enter OTP", "OTP_alert": "Please enter OTP", "OTP_full_alert": "Please enter the correct 4-digit OTP code", "create_order_successfully": "Successful order creation", "SO_code": "SO_code: ", "EP_code": "EP_code: ", "take_front_ID_picture": "Take a photo of your ID on the front:", "take_front_student_card_picture": "Take a photo of your student ID card on the front:", "take_front_grab_driver_picture": "Take a picture of your app driver:", "please_take_full_info_picture": "Please take enough picture information", "SIM_registration_procedure_uppercase": "SIM REGISTRATION PROCEDURE", "ID_requirement": "Original ID card (issued under 15 years) or Citizen's Identity Card (with validity) or Passport (with validity) of the subscriber.", "customer_portrait": "Portrait photo of subscriber owner at the time of transaction.", "SIM_plan_info_uppercase": "PACKAGE INFORMATION", "total_promotion": "Single promotion", "discount_product_amount_promotion": "Buy more, get more discount", "use_Coupon": "Use coupons", "installment": " (Installment)", "product_not_valid_for_order_promotion": "(Products do not participate in the total promotion)", "not_rounded": "(Not rounded)", "type_IMEI_SIM": "Enter IMEI SIM", "fee": "Fee:", "quantity_full": "Quantity:", "text_input_contract_code": "Number contracts: ", "lf_contract_employee": "Documentation staff: ", "lf_dealer_product_code": "Agent ID/Partner ID: ", "lf_cost_contract_colon": "Application fee: ", "lf_deposit": "Prepaid amount:", "lf_loan_term": "Lending term: ", "lf_payment_per_month": "Monthly Payment:", "promotion": "Promotion", "bundle_sale": "Sold with", "promotion_delivery_type": "Promotion in the delivery method", "bundle_sale_delivery_type": "Sold with delivery method", "placeholder_coupon": "Enter Coupon", "placeholder_note": "Enter note", "placeholder_phone": "Enter phone number", "caution_delete_coupon": "Deleting a product from the cart will remove the discount code and promotion associated with the product. Do you want to delete:", "from_the_cart": "Out of cart?", "from_the_bundle_list": "from the bundled list?", "no_promotion": "No promotions", "no_attachments": "Not included", "delivery_type": "Receipt form: ", "btn_create_order": "Create order", "btn_adjust_price": "Adjust price", "btn_create_competitive_price": "Create competitive price", "output_store_2": "Output store:", "amount": "Amount of money: ", "txt_done": "Done", "products_list": "List of products ", "products_list_2": "List of products ", "total_price": "Total Order Amount", "total_customer_deposit": "Total amount to be paid in advance", "provisional_total_membership_score": "Total cumulative points of provisional plan", "customer_information": "Customer information", "payment_method": "Form of payment", "customer_print_company_bill": "Customers print company invoices", "text_input_contact_phone": "Contact phone number:", "id_card_number": "ID number ", "take_ID_and_student_card_pictures": "Shoot ID CARD & STUDENT CARD", "membership_point": "Loyalty Points", "instruction": "When selecting \"Skip authentication\" the staff instructs customers to use the App registered with their purchase number, scan the QR Code on the receipt to authenticate.", "text_input_tax_id": "Tax code:", "text_input_customer_company_name": "Company name:", "text_input_customer_company_address": "Company address:", "text_input_customer_company_phone": "Company phone number:", "text_input_contact_name": "Name of contact person:", "text_input_contact_address": "Contact person address:", "text_input_phone": "Phone number:", "text_input_name_2": "Name:", "text_input_address_2": "Address:", "text_input_id_card_number": "ID Card:", "text_input_cost_contract": "Application fee:", "text_input_deposit": "Prepaid amount:", "picker_loan_term": "Loan term:", "text_input_payment_per_month": "Monthly payment amount:", "popup_phone_coupon_requires_phone": "Coupon code requires entering phone number", "color_full": "Color: ", "order_information_not_exist": "No order information", "cannot_data_price": "Cannot get war data", "no_store_found": "Can't find a supermarket to buy goods", "no_delivery_information_found": "No shipping information", "no_delivery_information": "No delivery load information", "placeholder_customer_tax": "Enter tax ID", "placeholder_phone_number": "Enter phone number", "placeholder_CMND": "Enter ID card", "btn_skip_uppercase": "<PERSON><PERSON>", "btn_retry_uppercase": "Retry", "please_enter_coupon": "Please enter coupon code", "notification_update_cart": "The cart is being adjusted for price or  competitive price, so the discount code cannot be applied. If you want to apply this discount code, please cancel the price adjustment or competitive price.", "notification_cart_coupon": "The cart has been applied with a discount coupon code, so the price cann't be adjusted. Please cancel the discount coupon to adjust the price!", "notification_cart_coupon_1": "The cart has been applied with a discount coupon code, so it isn't possible to create a competitive price. Please cancel the discount coupon to create a competitive price!", "please_enter_tax_code": "Please enter tax code", "validation_tax": "The tax code must be exactly 10 numeric characters, or exactly 14 characters, where the 11th character is a dash, the rest of the characters are numbers.", "validation_company_name": "Please enter Company name", "validation_company_address": "Please enter company address", "validation_customer_name": "Please enter the customer's first and last name", "validation_phone_number": "Please enter the correct 10 digit phone number", "validation_phone_number_1": "Please enter the correct phone number", "validation_phone_number_2": "Please enter the correct phone number format", "validation_phone_number_rim": "Please enter the phone number", "validation_CMND_rim": "Please enter ID card", "validation_CMND_number": "Please enter the correct 9 or 12 digits ID/CCCD number", "validation_image": "Please fully update additional images", "validation_prepayment": "Please enter prepayment amount", "please_choose_term_loan": "Please select loan term", "please_choose_relation_ship_type": "Please select the relationship with the customer. If you have any questions please contact your superior or user 17269 - <PERSON><PERSON><PERSON>", "please_enter_information_IMEI_SIM": "Please enter the IMEI information of the SIM product", "warning_no_enter_phone_number_customer": "You are creating an order without entering the customer's phone number. The phone number may affect warranty lookup information, e-invoices, membership points accumulation...\nDo you want to continue create order?", "warning_no_choose_promotion": "There is an unselected promotion. Are you sure you want to skip the promotion?", "sure_create_order": "Are you sure you want to create an order?", "validation_promotion": "Please select the required promotion select:", "warning_update_order_no_phone_number_customer": "You are updating an order without ENTERING the customer's PHONE NUMBER. Phone number may affect warranty lookup information, electronic invoices, accumulating membership points...\nDo you want to continue keep updating orders?", "sure_update_order": "Are you sure you want to update your current order information?", "update_order_successful": "Order update successful", "warning_update_payment": "You are not allowed to edit from cash to installment.\nIf you want to choose installment again, please search again for the order", "warning_update_pay_ment_1": "You are not allowed to edit from installment to cash.\nIf you want to choose cash again, please search again for the order", "profile_sent_partner": "The profile has been sent to the partner. You are not allowed to change one installment plan to another.", "adjust_price": "Adjust price", "competitive_price": "War Price:", "balance_price": "Điều chỉnh cân bằng giá khi đổi hàng:", "export_code": "Export request code: ", "change_store_code": "Code to request store transfer: ", "exchange_code": "Code to request a change of goods: ", "cancel_code": "Deactivation request code: ", "commodity_record": "Commodity record: ", "no_description_information": "No description available", "no_adjust_price": "no right to adjust price", "discount_1": "Reduce", "augment": "increase", "no_information_adjust_price": "No information about price war opponent", "text_input_buyer_phone": "Buyer phone:", "text_input_buyer_name": "Buyer name:", "text_input_buyer_address": "Buyer address:"}, "saleOrderPayment": {"QR_code_valid_until": "The QR code is valid to: ", "customer_guide": "Customer guide:", "step_one": "Step 1: ", "step_two": "Step 2: ", "open_app": "Open the app", "choose_scan_QR": "Select QR scan", "money_amount_colon": "Amount of money: ", "press": "(Press ", "btn_go_back_uppercase": "COME BACK", "continue_with_different_payment_method": "to continue to pay in another form)", "if_customer_deny_paying": "(If the customer refuses to pay, choose ", "btn_cancel_QR_code_uppercase": "CANCEL QR CODE", "installment_info": "Installment Information", "installment_money_uppercase": "installment amount", "appv_code": "APPV Code: ", "point_to_pay": "Payment Points", "max_applied_point": "Maximum applicable points: ", "OTP_will_be_sent_to": "(OTP code will be sent to phone number", "point_paid_successfully": "Points have been paid successfully", "point": "Score: ", "sec_code_paid_successfully": "Sec code was paid successfully", "sec_code": "Sec Code:", "valid_point": "points available", "IMEI": "IMEI: ", "QR_code_type_colon": "Choose wallet:", "QR_code_type": "QR Code Type", "QR_transaction_ongoing": "The QR transaction is paying:", "status": "Status", "money_amount": "Amount of money", "btn_verify": "Accuracy", "btn_enter": "Import", "btn_finish": "COMPLETED", "POS_device": "POS machine:", "appv_code_guide": "How to get APPV Code", "btn_payment_confirm": "Payment confirmation", "btn_add_card": "Add Card", "btn_create_QR_code": "Generate payment code", "btn_receive_OTP_call": "Receive OTP Calls", "btn_receive_OTP_message": "Receive OTP Messages", "quantity": "Qty", "output": "Export", "products_list": "List of products ", "attached_sale_list": "Sales list with", "promotion_list": "Promotion List", "charge": "Collect money", "customer_voucher": "MWG's Gift Voucher", "membership_point": "Loyalty Points", "customer_pay_card": "Customers swipe card", "customer_pay_QR": "Customers pay via E-WALLET", "choose_printer": "Select Printer", "customer_at_store": "There are customers directly at the supermarket to receive money", "check_here": "Check here, the system will automatically generate a check for you", "text_input_contract_code": "Contract number: ", "lf_contract_employee": "Documentation staff: ", "lf_dealer_product_code": "Agent/Partner ID: ", "lf_cost_contract_colon": "Application fee:", "lf_deposit": "Prepaid amount:", "lf_loan_term": "Lending term:", "lf_payment_per_month": "Monthly Payment amount:", "pay_by_point": "Use payment points", "pay_by_point_card": "Use scorecard to pay", "bill_printer": "Retail invoice printer", "VAT_bill_printer": "VAT invoice printer", "composition_bill_printer": "Composer Printer", "pay_for_output_demand": "Pay for export request", "output_demand": "EXR:", "must_pay": "Have to pay", "charged": "Collected", "money_to_charge_customer": "Receivables from guests", "output_store": "Output store:", "customer_phone": "Customer phone: ", "sale_employee": "Salesman: ", "cash_customer_pay": "Cash given by customer", "in_debt": "in debt", "customer_change": "Return money to the guest", "change_point": "Extra points", "total_products_price": "Total product cost", "additional_fee": "Surcharge", "round": "Rounding", "deposit": "Prepayment", "cost_contract": "Application fee", "advance_payment": "Advance", "placeholder_enter_form_code": "Enter or scan coupon code", "placeholder_enter_appv_code": "Enter APPV", "placeholder_enter_ID_code": "Enter or scan the identifier", "placeholder_enter_point_code": "Enter scorecard code", "placeholder_enter_OTP": "Enter OTP", "placeholder_enter_IMEI": "Enter IMEI", "placeholder_enter_serial_one": "Enter Serial 1", "placeholder_enter_serial_two": "Enter Serial 2", "btn_skip_uppercase": "SKIP", "btn_retry_uppercase": "RETRY", "please_use_XMPOS": "Please install App XMPOS to use this function.", "please_choose_printer": "Please select a printer", "you_want_finish_order": "Do you want to complete the order?", "internal_wifi_needed": "Please use internal wifi to perform this function.", "cannot_connect_printer": "Unable to connect to printer.", "print_successfully": "Print success", "you_want_delete_QR_code": "Are you sure you want to cancel this QR code?\nYou need to make sure the customer hasn't scanned this QR code", "please_select_POS": "Please select POS machine", "please_enter_authorization_code": "Please enter authorization code", "please_enter_OTP_code": "Please enter OTP", "please_enter_ID_code": "Please enter identifier", "authorization_code_six_letters": "The standard code must be exactly 6 characters", "OTP_code_4_digits": "Please enter the correct 4-digit OTP", "ID_code_4_digits": "Please enter the correct 4-digit identifier", "please_enter_pay_amount": "Please enter payment amount", "pay_amount_larger": "Total payment is greater than receivable", "authorization_code_exist": "The standard code already exists in the order", "please_enter_point": "Please enter the number of usage points", "excessive_point": "The usage points are exceeding the maximum number of points", "excessive_total_point": "The total points used for payment is exceeding the maximum number of points", "excessive_valid_point": "Use points are exceeding available points", "please_choose_QR_type": "Please select payment QR type", "btn_continue": "Print", "btn_continue_print": "Continue print", "btn_create_again": "Create agine", "please_scan_qr_code_to_pay": "Please scan the QR code to pay", "transact_successfully": "Successful transaction", "transact_fail": "Transaction failed", "transact_pending": "The exchange is under process...", "system_auto_create": "The system automatically created and updated the receipt", "please_create_QR_code_again": "Please recreate the QR code to make the transaction", "cash": "Cash", "order_information_not_exist": "No order information", "no_information_printer": "No printer information", "no_information_IEMI": "No IMEI information", "no_information_serial": "No SERIAL information", "no_information_voucher": "No Voucher Code Information", "voucher_already_use": "Scorecard already used", "voucher_out_date": "scorecard has expired", "voucher_cancel": "Canceled scorecard", "voucher_lock_up": "scorecard is locked", "voucher_no_information": "No scorecard information", "no_information_receipts_bill": "There is no information of Receipt and bill", "error_print": "Printing error!", "cannot_connect_to_printer": "Can't connect to printer!", "cannot_information_transaction": "Cannot get transaction information", "cannot_information_QR": "Can't to get QR code information", "cannot_information_order": "Order information not found.", "cannot_content_printer": "Prints not found", "create_receipts": "Create receipt successfully ", "create_bill": "Create bill successfully", "create_payment": "Successfully created deposit slip:", "extra_points": "\nExtra points", "pay_vip_account": "will be paid to customer's VIP GIFT account", "customers_contact_manager_report_card": "\nCustomers contact the Supermarket Manager to receive the Scorecard of the excess points", "description": "The guest has scanned the QR Code\nPlease wait for the customer to confirm the payment...", "month": "month", "please_enter_report_card": "Please enter your scorecard", "report_card_exists": "The scorecard already exists in the order", "maximum_score": "The total points used for payment is exceeding the maximum number of points", "verify_OTP": "OTP Authentication", "verify_ID": "Identifier Validation", "unconfirmed_transaction": "Transaction not confirmed", "please_confirm_transcation": "Please click confirm to complete the transaction", "please_again": "Please try again later", "IMEI_exists_order": "IMEI already exists in order", "please_enter_code": "Please enter coupon code", "please_enter_IMEI": "Please enter product IMEI", "please_enter_SERIAL": "Please enter product SERIAL", "error_get_partner_vouchers": "Partner voucher is not declared at your store."}, "saleOrderManager": {"order_not_found": "Order not found", "cannot_get_order_information": "Can't get order information", "order_information_not_found": "Order information not found", "order_information_not_exist": "No order information", "promotion": "Promotion:", "attached": "Sold with:", "please_enter_cancel_reason": "Please enter a reason for cancellation", "debt_must_be_0": "The amount owed must be zero", "please_select_printer": "Please select a printer", "btn_skip_uppercase": "<PERSON><PERSON>", "btn_continue_uppercase": "CONTINUE", "btn_retry_uppercase": "RETRY", "cancel_order_success": "The order has been successfully canceled from the system", "print_expense_report": "Print Payment Voucher", "cannot_connect_printer": "Unable to connect to printer.", "cannot_information_printer": "No print form information", "cannot_content_printer": "No print content", "print_success": "Successful Print", "expense_uppercase": "ACTUALLY SPENT", "voucher": "Gift voucher", "vip_point": "VIP Gift Points", "qr_cash": "QR Money", "credit": "Card money", "select_printer": "Select printer", "confirm_cancel": "Are you sure you want to cancel this order?", "additional_receipt_order": "Orders generate additional receipts:", "reason_1": "1 - There are visitors watching", "reason_2": "2 - Defective machine isn't eligible for sale", "reason_3": "3 - Recreate order due to wrong information (Price, customer name, phone number, delivery method,...)", "reason_4": "4 - Change order information (Delivery time, delivery address,...)", "reason_5": "5 - Customers don't buy (cancel ERP)", "delete_order_success": "Order has been successfully removed from the system", "deposit": " deposit ", "detain_from_CO": "retention due to transferred CO", "deposit_term": " Deposit term: ", "enter_cancel_reason": "Please enter a reason for cancellation!", "btn_cancel": "Cancel", "select_cancel_reason": "Select a cancellation reason", "select_reason": "Choose a reason", "update_export_request": "Update cancel EXR and generate payment slip", "btn_cancel_order": "Cancel order", "send_coupon": "Send a coupon value 100,000", "product_not_for_sale": "Inform customers of unsold products", "created_request": "Request you create", "canceled_request": "Request canceled", "btn_cancel_export_request": "Cancel EXR and generate payment voucher", "received": "Received", "voucher_fee": "Gift voucher usage fee", "detain": "Retained Money", "voucher_fee_not_return": "Gift voucher doesn't pay customers", "must_expend": "Money to be paid", "text_input_export_request_code": "Enter SO/Phone/Name/ContractID/SH", "cart_id": "cart", "export_request_type": "export request type", "must_pay": "Must pay:", "remain": "In debt", "in_debt": "In debt", "cash": "Cash", "confirm_status": "Browsing Status", "payment_status": "Collection Status", "export_status": "Export Status", "shipment_status": "Delivery Status", "export_cash": "Collect money & Export", "warning_yellow": "Orders with warranty stickers or free watch battery replacement are not allowed to ship. Please use the sticker receiving function of the screen protector to ship the goods.", "reprint": "Reprint", "not_exported_request": "Unexported request", "exported_request": "Exported request", "invalid_voucher_uppercase": "Gift voucher applied:", "payment": "Payments:", "export_request_applied": "EXR Applied:", "please_select_pos": "Please select POS machine", "please_enter_expense_code": "Please enter the standard code", "expense_code_must_have_6_letters": "The standard code must be exactly 6 characters", "please_enter_payment": "Please enter payment amount", "expense_more_than_must_expend": "The total actual expenditure is greater than the amount to be spent", "existed_expense_code": "The standard code already exists in the order", "select_pos": "Select POS machine", "text_input_appv_code": "Enter the APPV code", "amount": "Amount: ", "btn_transaction_confirm": "Payment confirmation", "btn_add_card": "Add Card", "retail_printer": "Receipt printers", "VAT_printer": "VAT invoice printer", "common_printer": "Composer Printer", "no_promotion": "No promotion", "please_select_promotion": "Please select the required promotion:", "dismiss_promotion": "There is a promotion that has not been selected. Are you sure you want to skip the promotion?", "delivery_at_home": "Home delivery:", "delivery_at_store": "Delivery at store:", "contact_address": "Address: ", "contact_name": "Name of contact person: ", "please_select_more_reason": "Please add a reason to cancel the order!", "receipt_code": "Receipt code", "must_pay_for_export_request": "Payable for EXR", "additional_fee": "Additional fee", "shipping_fee": "(Shipping fee, product deposit)", "cancel_reason": "Reason for cancellation", "mandatory": "Required", "title_cancel_reason": "Cancellation reason:", "not_mandatory": "No input required", "product_cannot_sale": "Report unsold products:", "text_input_reason": "Enter reason", "total_price": "Order Total", "not_rounded": "(Unrounded)", "total_customer_deposit": "Total amount to be paid in advance", "shipping_fee_deposit": "\n(Shipping fee, product deposit)", "apply_voucher": "\n(PMH applies to the issuance application itself)", "source": "Generation Source", "product_promotion": "promotion by product", "delivery_method": "Form of delivery", "amount_new_line": "\nAmount", "will_be_returned": "will be refunded to your SmartPay account", "deposit_new_line": "\n<PERSON><PERSON><PERSON><PERSON>", "detain_from_CO_new_line": "\nMoney withheld due to CO transfer:", "deposit_term_new_line": "\nDeposit term", "confirm_cancel_new_line": "\nAre you sure you want to cancel this order?", "view_more": "View_more", "cancel_export_request": "Cancel EXR", "export_request": "EXR", "view_transfer_information": "View transfer information", "transfer_validation": "Transfer validation", "transfer_information_uppercase": "TRANSFER INFORMATION", "please_choose_printer": "Please select the print pattern number", "number_paper": "num: ", "continue_printer": "Continue printer", "How to get APPV Code": "How to get APPV Code", "non_report_history": "Not found history report", "non_edit_history": "Not found history saleOrder", "sale_order_id": "SaleOrderID: ", "content_update": "Content update: ", "employee": "Staff: ", "computer_user": "Computer User: ", "computer_name": "Computer Name: ", "note": "Note: ", "type_report": "Report type: ", "printer_name": "Printer name: ", "result": "Result: ", "history_report": "History Report", "history_sale_order": "history SaleOrder"}, "instalmentManager": {"no_hometown_information": "no country of origin information available", "no_information": "No information", "not_select_cancel_reason": "You have not selected a reason for the cancellation of your profile. Please check again", "btn_cancel": "Cancel", "btn_delete": "Delete", "cannot_read_id_code": "Cannot read identifier information, please rescan!", "reason": "Reason", "cancel_record_reason": "Reasons for canceling records", "record": "record", "delete": "delete", "cancel": "cancel", "agent_information": "Staff information", "agent_id": "Staff ID", "position": "Position", "no_payment_per_month_information": "No monthly payment information", "invalid_deposit": "Invalid prepayment amount please check again.", "loan_information": "LOAN INFORMATION:", "total_order_price": "Total amount of order: ", "loan_term": "Loan term", "picker_loan_term": "Loan term", "payment_per_month": "Monthly Payment:", "record_manager": "Records management", "send_success": "Send the voucher to the partner successfully. Please check the status of the application.", "processing_information": "Partner is processing information. Please wait more", "check_status": "min then check the status again", "btn_check_record_status": "Check Profile Status", "check_record_status_success": "Profile status check successful. Please check profile status has been updated", "btn_confirm_otp": "OTP Confirmation", "btn_send_record": "Send records", "btn_prepare_document": "Document preparation", "btn_add_record": "Additional documents", "btn_capture_contract": "Take a picture of the contract", "btn_send_record_to_partner": "Send voucher to partner", "btn_print_contract": "Print installment contract", "please_enter_information": "Please enter information [", "at_step": "] in step", "information": " Information [", "invalid_1": "Not valid. Please check again(#1)", "invalid_2": "Not valid. Please check again(#2)", "invalid_3": "Not valid. Please check again(#3)", "invalid_4": "Not valid. Please check again(#4)", "invalid_5": "Not valid. Please check again(#5)", "invalid": "illegal. Please check again. You can only enter [", "later_or_equal": "] is greater than or equal to date", "sooner_or_equal": "] is less than or equal to date", "wrong_format": "Invalid format. Please check again", "hometown_not_found": "Origin not found!", "text_input_hometown": "Enter original name", "btn_search_uppercase": "Search", "created_record": "Profile you create", "deleted_record": "Profile you deleted", "select_province": "Select Province/City", "select_district": "Select District/District", "select_ward": "Select Ward/Commune", "header_marriage_status": "-- Marital status --", "enter": "Enter ", "enter_code": "Enter code", "front": "Front", "back": "back:", "enter_content": "Enter content", "header_academic_standard": "-- Academic level --", "header_job": "-- Job --", "header_company_type": "-- Select company type--", "company_address": "Company address", "house_number": "apartment number", "street": "Road/Ham", "enter_street": "Enter street/hamlet name", "header_position": "-- Select a position--", "date_start": "Date start work", "please_enter_phone_number": "Please enter the phone number", "current_address": "Current address ", "quarter": "Nest/neighborhood", "enter_quarter_name": "Enter the name of the group/neighbourhood", "enter_information": "Enter according to the document information, if the document doesn't have this information, enter .", "delivery_address": "Contacts (shipping)", "enter_house_number": "Enter house number", "header_resident_type": "-- Select residency type--", "upload_image_error": "Error uploading image to server.", "capture": "Take a shot", "back_2": "back", "id_card_old": "ID", "id_card_new": "ID", "portrait": "Take a portrait", "please_enter_otp": "Please enter OTP", "enter_6_or_8_digits_otp": "Please enter correct OTP code 6-8 digits", "otp_success": "OTP confirmation is successful. The application has been received by the partner", "otp_sent": "OTP has been sent to customer's phone number. Please enter the box below to complete.", "enter_otp": "Enter OTP", "customer_information": "CUSTOMER INFORMATION", "full_name": "First and last name:", "update_record_success": "Updated installment profile information successfully", "householder": "As a householder", "relationship": "Relationship", "header_relationship": "-- Select relationship--", "appellation": "Name", "header_appellation": "-- Name --", "householder_full_name": "First and last name", "enter_householder_full_name": "Enter first & last name", "contact_phone_number": "Contact phone number", "enter_contact_phone_number": "Enter contact phone", "id": "ID number", "enter_id": "Enter ID number", "conjugal_information": "coupe Information", "btn_update": "Update", "no_id_card": "Customer is the owner of the household / The head of the household doesn't have an ID card", "please_enter_record_number": "Please enter the document number of [", "record_number": "Document number of [", "at_least_5_letters": "] must be at least 5 characters. Please check again", "please_enter_record_number_2": "Please enter the document number on the back of [", "record_number_2": "Document number on the back of [", "please_capture": "Please shoot [", "record_type": "Document type of record", "header_record_type": "-- Select a document type--", "customer_information_2": "CUSTOMER INFORMATION: ", "text_input_surname": "Enter the customer's last name", "text_input_middle_name": "Enter customer middle name", "text_input_last_name": "Enter customer name", "header_place": "-- Select the place to issue the--", "no_term": "Indefinite", "header_hometown": "-- Select origin--", "text_input_phone_number": "Enter mobile number", "text_input_email": "Enter customer email", "hometown": "Origin", "driving_licence_short": "License", "update_success": "Update and supplement documents for successful partners.", "customer_information_3": "CUSTOMER INFORMATION:", "surname": "Surname: ", "middle_name": "Middle name: ", "last_name": "Name: ", "date_of_birth": "Date of birth: ", "gender": "Sex: ", "male": "Male", "female": "Female", "id_code": "ID number: ", "place": "Issued by: ", "date": "Date Range: ", "title_hometown": "Domicile: ", "licence_type": "TYPE OF PAPER:", "dl_code_front": "Front driver's license number: ", "dl_code_back": "Driver's license number on the back: ", "family_record": "Household number: ", "householder_id_code": "ID number of the head of household: ", "address": "PERMANENT ADDRESS:", "house_number_2": "Apartment number:", "street_2": "Road/Ham: ", "quarter_2": "Group/Neighbourhood: ", "ward": "Wards: ", "district": "District: ", "province": "Province: ", "print_success": "Print Success", "please_select_print_format": "Please select a print pattern", "print_format_not_found": "Print form information not found", "select_printer": "Select Printer", "printer_information_not_found": "Printer information not found", "print_record": "Print Profile", "number_of_print": "Number of prints:", "btn_done": "Done", "send_record_success": "Successful submission of profile to partner. Please check profile status again.", "check_customer_information_success": "Customer information verification successful.", "otp_sent_to": "OTP has been sent to number", "cancel_record_uppercase": "Cancel record", "please_enter_canel_reason": "Please enter the reason for the application", "text_input_cancel_content": "Enter cancel content", "scan_valid_agent_id": "Scan the identity code of the employee who has the right to confirm the creation of a request to cancel the installment record", "request_agent": "(Please ask the authorized staff to confirm creating a request to cancel the installment file, open the MWG app, click the Mobile World logo in the left corner of the screen, select \"Identity code\")", "input_deposit": "Prepayment amount:", "min_deposit": "% minimum prepayment ", "max_deposit": "% maximum prepayment", "record_2": "Record", "fee": "Fee", "price": "Price: ", "signature_register": "Link to register electronic signature", "not_signed_up": "Customer has not registered to use", "electronic_service": "electronic services", "please_instruct_customer": "Please direct customers to access", "link_below": "link below to sign", "sign_up_electronic_service": "Register to use electronic services", "resend_code": "resend verification code", "phone": "phone number", "reference_information": "phone number", "number": "Number", "contract_number": "Contract number; ", "id_card": "ID card", "id_card_2": "ID card", "select_additional_document": "Select the type of documents to be added for the application: ", "print_contract": "Print contract", "instalment_record_not_found": "Installment profile information not found", "no_place": "no information on where to issue ID card/HC", "no_instalment_information": "Installment profile information not found.", "no_record_type": "document type not found for the record", "error_update_instalment_information": "Error updating installment profile information.", "no_cancel_reason": "There is no information on the list of reasons for canceling the installment profile", "no_record_code_sent_to_partner": "Cannot find the profile code for sending information via partner. Please try again or contact IT for support.", "error_get_signature": "Error retrieving electronic signature registration information from HC partner. Please try again.", "error_resend_otp": "Error resending OTP Code information. Please try again.", "cannot_verify_otp": "Unable to verify OTP Code. Please try again!", "no_agent_information_found": "Employee information not found. Please try again.", "error_get_agent_information": "Error getting employee information. Please contact IT.", "error_add_record_partner": "Error adding documents to partner.", "error_send_record": "Error sending voucher to partner. Please try again or contact IT for support", "error_print_contract": "Error printing installment contract information.", "no_printer_information": "No printer information", "no_print_format_found": "Print form information not found", "error_print": "Printing error!", "cannot_connect_to_printer": "could not connect to printer!", "search_input": "Enter EP/SO/Phone", "export_request": "Export request:", "record_status": "Profile Status:", "note": "Note", "partner_record_code": "Partner Profile ID:", "start_date_cannot_later_than_end_date": "The start date cannot be greater than the end date!", "at_most_5_days": "Start date and end date can only be separated by a maximum of 5 days!", "time": "Time", "from_to": "(From day to day)", "guide": "Guide: ", "review_information_uppercase": "Check the information again", "review_information": "Please check full information", "hintNoteStep6": "If this information is not available, enter “.”. You can only enter up to 128 characters.", "infoOther": "Other information"}, "searchStore": {"pick_store": "Choose store", "input_store": "Enter Store", "no_authority": "(Deny permission)", "no_store_found": "Store not found! ", "confirm_change_store": "Do you want to change store?", "no_store_authority": "You don't have permission to operate this supermarket. Please contact IT for permission!", "understood": "understood"}, "searchImei": {"input_type": "Form", "input_store": "Store", "product": "Product:", "status_type": "Status:", "haswarranty": "Haswarranty:", "input_id": "Form code", "input_date": "Date", "user_name": "Staff", "placeholder": "Enter IMEI", "no_find_imei": "IMEI information not found"}, "pinOclock": {"customer_name": "Customer:", "input_store": "At store:", "product": "Product:", "choice_pin": "Select the type of battery to replace:", "btn_saleoder": "CREATE ORDER", "total_money": "Total amount: ", "input_date": "Purchase date:", "defalt_value": "-- Select the type of battery to replace --", "price": "Price: ", "placeholder": "-- Enter phone number --", "not_find": "Watch information not found", "no_get_price": "Cannot get the price of the watch battery", "confirm_add_to_cart": "Are you sure you want to add to cart ?"}, "cMMoneyComplaint": {"no_expense_voucher_information": "No payment voucher code information", "error": "Process completion failed", "no_authority": "You do not have permission to perform this function. Please contact IT for permission", "invalid_expense_voucher_information": "Invalid payment voucher information. Please check again", "complete_expend": "Are you sure you want to finish paying?", "expend_success": "Complete spending successfully", "btn_complete": "Complete payment", "customer_information": "Customer information", "customer_id": "Customer id:", "customer_name": "Customer name:", "gender": "Gender:", "phone_number": "Phone number:", "tax_id": "Tax id:", "address": "Address: ", "district_province": "District/Province:", "text_input_expense_voucher_id": "Enter payment voucher", "male": "Male", "female": "Female", "expense": "Actually spent", "cash": "Cash:", "debt_remain": "Remaining debt:", "expense_voucher_information": "Payment voucher information", "store": "Store:", "voucher_type": "Vote type:", "voucher_id": "Invoice number:", "invoice_symbol": "Sign of contract", "voucher_date": "Bill symbol:", "content": "Content:", "total_money": "Totalmoney:"}, "codPay": {"error_inspect_request": "Browsing error requires adjustment!", "no_order_information_found": "Order ID/Phone number not found", "notification_2": "Notification", "adjustment_greater_than_zero": "Adjustment amount must be large or zero.", "adjustment_must_not_equal_cod": "The adjustment amount cannot be equal to the COD collection amount.", "adjustment_must_smaller_than_remain": "The adjustment amount must be less than or equal to the remaining amount.", "please_enter_adjust_reason": "Please enter a reason for the adjustment.", "create_request_success": "COD adjustment request created successfully!", "not_exported": "Not Exported", "exported": "Exported", "order_type": "Order type: ", "remain": "The remaining amount: ", "cod": "COD collection amount: ", "text_input_adjusted_money": "Enter the amount after adjustment:", "adjust_reason": "Adjustment reason: ", "btn_create_request": "CREATE REQUEST", "adjust_request_cod": "Request for adjustment of COD collection amount", "requests": "request list", "search_input_order_code": "Enter university code/phone", "btn_find": "Find", "please_enter_order_code": "Please enter SO/phone", "create_request": "Create export request", "before_arrive_day": "Please select a date from less than the arrival date!", "within_30_days": "Please choose within 30 days!", "no_information": "No information", "inspect_request_success": "Adjustment request approved!", "delete_request_success": "Delete adjustment request successful!", "adjust_request_code": "Adjustment request code: ", "agent_created": "Employee creates: ", "store_created": "store create: ", "remain_request": "Adjustment request also: ", "btn_delete": "Btn_delete", "status": "Status: ", "deleted": "Deleted", "inspect_status": "Browsing Status:", "accept": "Accept"}, "activeSimManager": {"order_status": "Order status: ", "exported": "Exported", "not_exported": "Not exported", "imei": "IMEI number: ", "package": "Pack of data: ", "source": "Source of creation", "not_connected": "Not Connected", "connected": "Connected", "text_input_keyword": "SO, IMEI, phone, name", "error_1": "No SIM processing request found", "error_2": "no SIM processing information", "error_3": "No country information", "error_4": "No province information", "error_5": "no district information", "error_6": "no ward information", "error_7": "no information on where to issue ID card/HC", "error_8": "Customer information not found!", "error_9": "SIM serial does not exist!", "error_10": "Esim information not found!", "signature_uppercase": "Signature", "delete": "Delete", "done": "DONE", "sim_process_request": "Requires SIM Handling ", "id_card_1_short": "ID Card", "id_card_2_short": "ID Card", "passport": "Passport", "recapture": "Recapture", "portrait": "Take a portrait", "sign": "Sign", "resign": "Resign", "request_store": "Request store: ", "process_store": "Processing store: ", "sale_agent": "Salesman: ", "search_input_sale_agent": "Enter salesman", "product": "Product: ", "search_input_sim_serial": "Enter SIM Serial", "get_sim_serial": "Press try again to get SIM Serial", "package_2": "Pack of data: ", "select_package": "Select package", "customer_information": "Customer information", "select_country": "Select nationality", "user": "User: ", "user_type_1": "Self", "user_type_2": "Born", "user_type_3": "Adopted children under 14 years old", "user_type_4": "Guardian", "user_type_5": "<PERSON><PERSON>", "select_user": "Select Object", "record_type": "Document Type:", "id_card_1": "ID Card", "id_card_2": "Citizen ID", "select_record_type": "Select document type", "id_number": "ID card/ passport:", "date": "Issuance date: ", "select_date": "Select Date", "place": "Place of issue: ", "select_place": "Choose a place", "expired_day": "Expiration date: ", "visa_number": "Visa Number:", "id_number_in_id_card": "ID card in passport: ", "province": "Province: ", "select_province": "Select Province/City", "district": "District: ", "select_district": "Select District", "ward": "Wards/Commune:", "select_ward": "Select Ward/Commune", "please_enter_address_on_id_card": "Please enter the house number, street/hamlet, group/neighbourhood according to the place of permanent residence on the ID card (if any).", "house_number": "house number: ", "street": "Street/hamlet name: ", "quarter": "group/neighborhood name: ", "address": "Place of residence: ", "full_name": "First and last name: ", "date_of_birth": "Date of birth: ", "btn_update": "Update", "btn_connect": "Connection", "enter": "Enter", "select_pg": "Select PG", "photo_and_signature": "Please take enough pictures and signatures!", "error_update_image": "Error updating image", "connect_success": "Connection successful!", "update_success": "Update successful!", "message_1": "Please enter SIM serial", "message_2": "Please enter correct SIM serial", "message_3": "Please enter your first and last name", "message_4": "Please choose a package!", "message_5": "Please choose your nationality", "message_6": "Please choose the user", "message_7": "Please choose Province/City", "message_8": "Please select a County/District", "message_9": "Please choose Ward/Commune", "message_10": "Please select a document type", "message_11": "Please enter ID card/HC", "message_12": "Please select the date of issue of ID card/HC", "message_13": "Please select the place to issue ID card/HC", "message_14": "Please select ID card/Passport expiration date", "message_15": "Please enter Visa number", "message_16": "Please enter the ID card in your passport", "message_17": "Please select date of birth", "message_18": "Please enter the phone number", "message_19": "Please choose P<PERSON>!", "name": "Name: ", "date_of_birth_2": "Date of birth: ", "address_2": "Address: ", "select_customer_information": "Select customer information", "capture": "Take a shot ", "front": "front", "back": "backside", "nationality": "Nationality:", "physical_sim": "Physical white SIM", "esim": "Electronic white SIM (eSIM)", "male": "male", "female": "Female"}, "splash": {"token_expired": "<PERSON><PERSON> has expired. Please login again.", "cannot_information_user": "There is a problem with the user's data information, contact the IT department to check.", "cannot_information_work": "There is a problem with the user's working information, contact the IT department to check.", "cannot_information_warehouse": "There is a problem with the user's work store information, contact the IT department to check.", "impormation_supermarket_defaut": "Your default working supermarket information", "please_update_new_version": "The current app is old or faulty. Please update to the latest version to use the MWG POS app.", "btn_notify_log_out": "Log out", "info_change": "changed. Please log in again", "language": "Language:", "btn_login": "Login by SSO", "sso_authenticating": "Authenticating by SSO", "updating": "Updating application (%{progress}%)", "checking_update": "Checking application update"}, "components": {"title_camera_access": "Access to the camera", "MWG_camera_access": "The MWG app needs access to your camera!", "version": "Version: ", "loading": "Loading: ", "completed": "Completed", "data_not_found": "No data found!", "on_loading_data": "Data in progress", "update_success": "MWG POS updated successfully", "please_close_app": "Please close the app and reopen it to use the updated version.", "POS_update": "MWG POS updated", "app_new_update": "The application has a new updated version.\nPlease click Agree to update to the new version.", "POS_updating": "MWG POS is updating", "app_sync_data": "The app is syncing data. Please wait until it's done.", "select_province": "Select Province", "select_district": "Select District", "select_ward": "Select Ward"}, "header": {"home_screen_uppercase": "HOME", "cod_pay_uppercase": "ADJUSTMENT COD COD", "search_installment_records_uppercase": "SEARCH PAYING RECORDS", "inventory_uppercase": "PRODUCT INVENTORY", "order_manager_uppercase": "MANAGEMENT ORDER", "adjust_price_uppercase": "PRICE ADJUSTMENT", "create_price_uppercase": "CREATE COMPETITIVE PRICE", "order_member_point": "Loyal customer", "order_installment_OTP": "CONFIRM OTP INFORMATION", "add_image_uppercase": "ADDITIONAL IMAGES", "search_product_uppercase": "SEARCH PRODUCT", "information_product_uppercase": "PRODUCT INFORMATION", "information_cart_uppercase": "CART INFORMATION", "manager_SIM_uppercase": "SEARCH SIM PROCESS", "choose_supermarket_uppercase": "CHOOSE STORE", "CM_money_complaint": "PAYING COMPLAIN REFUND ", "additional_promotion_uppercase": "ADDITIONAL PROMOTION", "search_imei_history": "SEARCH IMEI HISTORY", "f88_loan_contract": "LOAN CONTRACT F88", "create_contract_f88": "CREATE LOAN CONTRACT F88", "print_contract_f88": "PRINT CONTRACT F88", "handover_f88": "HANDOVER LETER", "handover_list": "HANDOVER LIST", "employee_of_partner": "EMPLOYEE OF PARTNER", "history_handover": "HISTORY HANDOVER", "print_handover_letter": "PRINT HANDOVER LETTER", "detail_handover": "DETAIL HANDOVER", "promotion_detail": "PROMOTION DETAIL", "view_promotion_info": "PROMOTION INFORMATION", "search_inout_voucher": "SEARCH IN/OUT VOUCHER", "scan_qrcode_id": "Scan QR Code ID", "search_output_receipt": "OUTPUT RECEIPT MANAGEMENT", "search_sale_service": "SALE SERVICE MANAGEMENT", "area_management_uppercase": "AREA MANAGEMENT", "inventory_product": "INVENTORY PRODUCT", "select_inventory_status": "SELECT INVENTORY PRODUCT", "view_inventory_result": "VIEW INVENTORY'S RESULT", "view_arrears_result": "VIEW ARREARS'S RESULT", "processing_arrears": "PROCESSING ARREARS", "arrears_staff_info": "ARREARS STAFF'S INFORMATION", "inventory_no_imei_product": "INVENTORY NO IMEI PRODUCT", "print_report": "PRINT REPORT", "inventory_has_imei_product": "INVENTORY HAS IMEI PRODUCT", "add_new_area": "ADD NEW AREA", "search_imei_history_uppercase": "SEARCH IMEI HISTORY", "select_product_state": "SELECT PRODUCT STATE", "product_detail": "PRODUCT DETAIL"}, "additionalPromotion": {"order_not_found": "There are no orders that match the search criteria", "cannot_get_order_information": "Can't get order information", "order_information_not_found": "Order information not found", "order_information_not_exist": "No order information", "text_input_export_request_code": "Enter Code SO/Phone/IMEI", "customer": "Client: ", "phone_number": "Phone number: ", "export_request_type": "Export request type: ", "view_detail": "View details", "additional_sale": "Additional sold with product", "source": "Generation Source", "output_date": "Export date: ", "output_store": "Output store: ", "quantity": "Quantity: ", "total_price": "Price: ", "debt_promotion_product_list": "List of promotional debt products", "debt_sale_product": "Bundled Supplements", "debt_product_list": "List of debt products", "committed_delivery_date": "Delivery commitment time", "action_pay_debt": "Paying debt", "action_export_additional_promotion": "Additional export", "debt_promotion_quantity": "Debt promotion amount", "in_stock_quantity": "Inventory number", "at_least_one_product_selected": "You have not selected an additional promotional product, please select a product", "no_debt_product": "Product is not included in gift declarations of Promotion. Please select another one or contact the category for support!", "sale_price": "Selling price", "expired_additional_promotion_program": "The program has expired, allowing additional promotions.", "total_must_expend": "Total amount to spend", "action_create_debt_payment_voucher": "Create a debit note", "inStock_message": "%{productName} in stock: %{inStock}", "ask_to_choose_others": "Please choose another product", "header_acronym": "BKSM %{SaleOrderID}", "quantity_acronym": "Qty: %{quantity}", "create_payment": "Create payment slip successfully:", "promotion_tab": "Promotion", "receive_method_tab": "Form of receipt"}, "screenSticker": {"ordered_products_to_be_pasted": "Products received stickers today", "waiting_to_be_pasted": "Wait for stickers", "waiting_list_to_be_pasted": "Stickers waiting list", "ordered_to_be_pasted": "Get stickers", "no_product_to_be_pasted": "No products waiting to be Stickers", "employee_id": "Staff code", "imei": "IMEI", "customer": "Customer: ", "product_name": "Product: ", "output_paste_date": "Past Date: ", "created_date": "Date created: ", "at_store": "At the supermarket: ", "choose_sticker_to_be_warranted": "Choose stickers for warranty", "btn_create_sale_order": "Create Order", "sale_order_id": "Code orders: ", "created_by_user": "Employee creates: ", "enter_input_placeholder": "Enter IMEI/Phone Number", "paste_screen_sticker": "Screen Paste", "warrant_screen_sticker": "Pattern Warranty", "total_amount": "Total amount: ", "warrant_sticker_not_found": "Warranty information not found", "no_ordered_product_to_be_pasted": "No products received stickers", "failed_to_get_price": "Retrieved sticker price failed", "failed_to_create_so": "Get pasted failed", "btn_get_price": "Get price", "product_has_no_warranty": "not participating in the warranty policy.", "ensure_add_to_cart": "Are you sure you want to add to cart?", "at_least_one_product_selected": "You have not selected a warranty sticker, please choose a sticker.", "error_get_more_list": "There was an error during the process of retrieving more lists. Please exit and select the feature again", "load_more_fail": "Unable to retrieve any more stickers. Please try again", "confirm_customer": "Are you sure you want to get the stickers?"}, "f88": {"id_number": "Identification number: ", "customer": "Customer: ", "bike_number": "License plate: ", "contract_status": "Contract status: ", "contract_handover_status": "Contract handover status: ", "partner_contract_id": "Partner contract number: ", "select_contract": "Select handover contract", "select_all": "Select all", "released_contract": "Total drawdown contracts: ", "selected_contract": "Total contracts selected to transfer: ", "btn_continue": "Continue", "just_not_handover_contract": "Just select not transfered contracts", "select_contract_status": "Select contract status", "created_user": "Created by user: ", "input_user_id": "Enter user id", "please_select_printer": "Please select printer", "handover_contract_quantity": "Total contracts to transfer: ", "create_handover_request": "Create handover request", "please_capture_introduction_letter": "Please take photo of introduction letter of partner's agent", "please_capture_agent_card": "Please take photo of agent card of partner's agent", "please_capture_customer_portrait": "Please take photo of customer's portrait", "please_capture_agent_front_id_card": "Please take photo of front ID card of partner's agent", "please_capture_agent_back_id_card": "Please take photo of back ID card of partner's agent", "please_capture_contract_with_signature": "Please take photo of invoice with signature of partner's agent", "please_take_partner_signature": "Please take the signature of partner's agent", "update_handover_request_success": "Update handover request successfully", "capture_introduction_letter": "Take photo of confrimation letter of partner's agent", "introduction_letter": "Introduction letter", "agent_card": "Agent car", "capture_agent_id_card": "Take photo of ID card of partner's agent", "agent_front_id_card": "Front of ID card", "agent_back_id_card": "Back of ID card", "capture_contract_with_signature": "Take photo of invoice with signature of partner's agent", "contract_with_signature": "Invoice with signature", "take_partner_signature": "Take the signature of partner's agent", "partner_signature": "Signature of partner's agent", "btn_done": "Done", "please_use_pdf_path": "Please use PDF file path", "pdf_url": "PDF file path:", "input_url": "Enter URL", "btn_print_contract": "PRINT CONTRACT", "create_contract": "Create loan contract", "create_handover_loan_request": "Create handover request", "history_transfer": "History handover", "enter_loan_customer": "Enter loan application code/plate number/customer name", "IdentificationNo": "Identification number:", "License_Plate": "License plate:", "Contract_Status_Name": "Status contracts:", "Partner_ContractID": "Partner profile number:", "list_profile": "List of handover documents", "staff_info": "Information about employees who get vouchers", "working_papers_staff": "Employee working papers for vouchers", "identification_staff": "Identity document of the employee taking the ballot", "signed_statement_staff": "Statement signed by the employee taking the document", "sign_staff": "Document signing staff", "Count_RQContract": "Total number of documents handed over:", "HanoverDate": "Delivery time:", "HandoverStatusName": "Status of handover record", "continue_print_uppercase": "CONTINUE PRINT", "choose_day": "Choose date:", "handover_code": "Code of handover minutes", "no_find_contract": "<PERSON><PERSON><PERSON><PERSON> tìm thấy hồ sơ vay", "no_find_exam": "No print form information", "no_find_list": "No handover list found", "no_find": "No handover documents found", "update_fail_retry": "Update handover failed, please try again", "fail_tryagain": "Create handover failed, please try again", "fail_conect": "The connection to F88 has failed. Please contact IT for support", "no_find_file": "No loan application found", "customer_portrait": "Customer portrait"}, "searchPicker": {"placeholder": "Enter keywords to search for...", "fail_loading": "No data found!"}, "modalFilter": {"search": "Search", "search_enter": "Find in import history", "search_export": "Find in export history", "select_promotion_date": "Promotion date:", "select_sale_program_id": "Sale program:"}, "oldProduct": {"approve": "<PERSON><PERSON><PERSON><PERSON>", "reject": "Bỏ duyệt", "enter_reject_reason": "<PERSON><PERSON> lòng nhập lý do bỏ duyệt", "reject_reason": "<PERSON><PERSON> do bỏ duyệt", "reason_1": "<PERSON><PERSON><PERSON> tê<PERSON>, IMEI, m<PERSON><PERSON> s<PERSON>c", "reason_2": "<PERSON><PERSON><PERSON> thi<PERSON> phụ kiện", "reason_3": "<PERSON><PERSON><PERSON> bể màn hình", "reason_4": "<PERSON><PERSON><PERSON> cho m<PERSON>", "reason_5": "<PERSON><PERSON><PERSON>", "reason_6": "Máy lỗi", "other_reason": "Lý do khác", "lack_of_images": "Bạn đang thiếu hình <PERSON>nh cho: ", "placeholder_search": "<PERSON><PERSON><PERSON><PERSON>, Tên SP", "choose_category": "<PERSON><PERSON><PERSON> ng<PERSON>nh hàng", "chosse_status": "<PERSON><PERSON><PERSON> trạng thái", "no_find_oldproduct": "<PERSON><PERSON><PERSON><PERSON> tìm thấy danh sách máy cũ", "no_find_image": "<PERSON><PERSON><PERSON><PERSON> tìm thấy danh sách hình máy cũ", "no_find_accessories": "<PERSON><PERSON><PERSON><PERSON> tìm thấy danh sách máy cũ", "no_find_accessories_oldproduct": "<PERSON><PERSON><PERSON><PERSON> có phụ kiện sản phẩm cũ", "up_image_suscess": "<PERSON><PERSON><PERSON>nh không thành công", "confirm_update_status": "Bạn có muốn cập nhật trạng thái sản phẩm?", "update_suscess": "<PERSON><PERSON><PERSON> nh<PERSON>t thành công", "accessories_included": "<PERSON><PERSON> kiện kèm theo: ", "btn_close": "Đ<PERSON><PERSON>", "btn_update": "<PERSON><PERSON><PERSON>", "imei": "IMEI: ", "price": "Giá: ", "warranty": "<PERSON><PERSON><PERSON>: ", "inventory_status_name": "<PERSON>r<PERSON><PERSON> thái sản phẩm: ", "status": "Tình trạng: ", "view_web": "Xem ở web", "name_product": "Tên SP: ", "import_store": "<PERSON><PERSON> nhập kho đ<PERSON>", "time": "giờ", "view_detail": "<PERSON>em chi tiết", "placeholder_note": "<PERSON><PERSON><PERSON><PERSON>hi <PERSON>ú", "update_info": "<PERSON><PERSON><PERSON> nhật thông tin", "confirm_delete_image": "Bạn có chắc muốn xóa ảnh?", "point_gift": "<PERSON><PERSON><PERSON>m thưởng Upload hình máy cũ", "staff": "Nhân viên: ", "sum_image": "<PERSON><PERSON><PERSON> hình: ", "reward": "Tổng thưởng: ", "header_point_gift": "ĐIỂM THƯỞNG UPLOAD HÌNH MÁY CŨ", "header_upload_oldproduct": "UPLOAD HÌNH MÁY CŨ", "header_old_product_info": "THÔNG TIN MÁY CŨ", "condition": "<PERSON>ui lòng cập nhật thông tin tình trạng và danh sách phụ kiện trước khi chụp hình!", "direction": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> anh chị kiểm tra model, màu sắc sản phẩm, đủ ảnh và đúng như ảnh mẫu mới đư<PERSON><PERSON> du<PERSON>t", "value_0": "Tr<PERSON>ng thái - All", "value_1": "Chờ duyệt Web", "value_2": "Đã duyệt Web", "value_4": "<PERSON><PERSON> ch<PERSON> kho", "value_7": "<PERSON><PERSON><PERSON> c<PERSON> khách đặt", "value_8": "Chưa up Web", "cagegory_-1": "<PERSON><PERSON><PERSON> – All", "cagegory_42": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i", "cagegory_44": "Laptop", "cagegory_522": "<PERSON><PERSON><PERSON> b<PERSON>", "cagegory_1942": "Tivi", "cagegory_1882": "Wearable", "cagegory_7077": "<PERSON><PERSON><PERSON> hồ thông minh", "cagegory_166": "<PERSON><PERSON> đ<PERSON>ng", "cagegory_1943": "Tủ lạnh", "cagegory_1944": "Máy giặt", "cagegory_1962": "<PERSON><PERSON><PERSON> n<PERSON>g", "cagegory_2002": "<PERSON><PERSON><PERSON>", "cagegory_2202": "<PERSON><PERSON><PERSON> s<PERSON><PERSON> qu<PERSON>n <PERSON>o", "cagegory_2065": "Micro", "cagegory_622": "Micro", "cagegory_2162": "<PERSON><PERSON>, Dàn <PERSON>h", "cagegory_165": "<PERSON><PERSON><PERSON>", "cagegory_2022": "DVD, Karaoke", "cagegory_3305": "<PERSON><PERSON><PERSON> hồ<PERSON> ng<PERSON>", "cagegory_284": "<PERSON><PERSON><PERSON>", "cagegory_1982": "<PERSON><PERSON><PERSON> từ", "cagegory_7604": "<PERSON><PERSON><PERSON> b<PERSON>", "cagegory_3385": "<PERSON><PERSON><PERSON>", "cagegory_5693": "<PERSON><PERSON><PERSON> in, Fax", "cagegory_5697": "<PERSON><PERSON><PERSON> h<PERSON>nh má<PERSON> t<PERSON>h", "cagegory_5698": "<PERSON><PERSON><PERSON> t<PERSON>h để bàn", "cagegory_5475": "<PERSON><PERSON><PERSON><PERSON>", "cagegory_1": "K<PERSON><PERSON><PERSON>", "cagegory_54": "<PERSON> nghe", "cagegory_2162_1": "Loa", "cagegory_4727": "<PERSON><PERSON><PERSON><PERSON> bị mạng", "cagegory_9499": "<PERSON><PERSON><PERSON> s<PERSON>, chuy<PERSON>n đổi", "cagegory_9118": "TV Box", "cagegory_10618": "Airtag", "cagegory_86": "<PERSON><PERSON><PERSON>", "cagegory_862": "<PERSON><PERSON><PERSON>", "cagegory_57": "Sạc dự phòng", "cagegory_1882_1": "<PERSON><PERSON> kiện tablet", "cagegory_7264": "<PERSON><PERSON><PERSON> hồ thời trang", "cagegory_2222": "<PERSON><PERSON><PERSON> nư<PERSON> nóng lạnh", "cagegory_1989": "<PERSON><PERSON><PERSON> đun siêu tốc", "cagegory_1984": "<PERSON><PERSON><PERSON>", "cagegory_1988": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "cagegory_1992": "Quạt", "cagegory_10139": "<PERSON> hút b<PERSON>i", "cagegory_1985": "<PERSON><PERSON><PERSON> xay sinh tố", "cagegory_1991": "<PERSON><PERSON><PERSON> t<PERSON>c", "cagegory_1922": "<PERSON><PERSON><PERSON> c<PERSON>m đi<PERSON>n", "cagegory_1983": "Bếp gas", "cagegory_1987": "Lò vi sóng", "cagegory_1990": "<PERSON><PERSON><PERSON>", "cagegory_9418": "<PERSON><PERSON><PERSON> chiên không dầu", "cagegory_1986": "<PERSON><PERSON><PERSON>", "cagegory_2062": "<PERSON><PERSON>y Ép Trái <PERSON>", "cagegory_2064": "<PERSON><PERSON><PERSON>", "cagegory_2262": "<PERSON><PERSON><PERSON>", "cagegory_2322": "<PERSON><PERSON><PERSON>", "cagegory_2428": "Quạt Sưởi", "cagegory_5473": "<PERSON><PERSON><PERSON> Không Khí", "cagegory_7498": "<PERSON><PERSON><PERSON><PERSON>", "title_1": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON>", "title_2": "<PERSON><PERSON><PERSON><PERSON>", "title_3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "turn": "Xoay", "error_turn": "<PERSON><PERSON> lỗi xảy ra trong quá trình xoay hình. Xin bạn vui lòng chụp lại hình ảnh", "error_turn_1": "Lỗi xoay hình", "error_edit": "Lỗi chỉnh sửa hình ảnh. Xin vui lòng thử lại", "error_getinfo": "Lỗi lấy thông tin hình ảnh. <PERSON><PERSON> lòng thử lại", "error_select": "Lỗi chọn hình ảnh. Xin vui lòng thử lại", "error_take": "Lỗi chụp hình ảnh. Xin vui lòng thử lại", "required_accessory_approve": "<PERSON><PERSON> lòng cập nhật danh sách phụ kiện tr<PERSON><PERSON><PERSON> khi duyệt", "upload_oldproduct": "Upload hình máy cũ", "choices_status": "<PERSON><PERSON><PERSON> trạng thái", "placeholder_description": "<PERSON><PERSON><PERSON><PERSON> thông tin mô tả hiện tại (Nhập mô tả cũ và mới):", "cancel_modal": "<PERSON><PERSON><PERSON>", "approve_modal": "<PERSON><PERSON><PERSON> t<PERSON>t", "modal_title": "<PERSON><PERSON> tả tình trạng", "title_desc_form": "<PERSON><PERSON> tả chi tiết tình trạng máy cũ:", "empty_desc_warning": "<PERSON><PERSON><PERSON> nhập mô tả chi tiết tình trạng máy cũ. <PERSON><PERSON><PERSON> cầu anh chị nhập đầy đủ thông tin mô tả tình trạng máy trư<PERSON><PERSON> khi duyệt", "old_image_title": "<PERSON><PERSON><PERSON> c<PERSON>", "new_image_title": "<PERSON><PERSON><PERSON> m<PERSON>i", "wrong_desc_old_product": "SAI MÔ TẢ", "right_desc_old_product": "ĐÚNG MÔ TẢ", "warning_confirm_status_desc": "Bạn vui lòng xác nhận thông tin mô tả máy cũ trước khi up hình!", "update_desc_err": "Lỗi cập nhật mô tả máy cũ", "condition_copy": "<PERSON>ui lòng cập nhật thông tin tình trạng và danh sách phụ kiện trước khi sao chép hình ảnh!"}, "offlineCart": {"delivery_form": "Delivery form", "btn_continue": "Continue", "pick_up_at_store": "Pick up at the supermarket", "store_deliver": "Delivery Supermarket", "dedicated_deliver": "Dedicated to deliver", "choose_delivery_method": "Choose a delivery method:"}, "offlineCreateOrder": {"btn_create_order": "CREATE ORDER", "info_create_export_request": "SHIPPING INFORMATION TO CREATE EXPORT REQUEST", "create_export_request": "CREATE EXPORT REQUEST"}, "offlineOrderManager": {"receipt_delivery": "Receipt cum delivery note", "no_find_printed": "No print pattern information", "err_html": "Error converting HTML", "no_vote_info": "No vote information", "confirm_delete": "Are you sure you want to delete coupon information", "delete_success": "Successfully deleted sales slip", "delete_err": "Delete voucher failed", "form_code": "Voucher Code", "form_ordered": "Form created form", "form_order_yet": "Uncreated coupon", "total_money": "Total product amount : ", "collected": "collected:", "collected_export_request:": "Collected on export request:", "placeholder": "Enter coupon code/Phone number/Customer name"}, "offlinePayment": {"vote_generation_failed": "Vote generation failed", "imei": "IMEI", "not_in_stock": "not in stock", "not_exist": "does not exist", "pls_enter_imei": "Please enter product IMEI", "please_choose_collect_payment_delivery": "Please select collection for delivery", "pls_collect_money": "Please collect the full amount payable", "pls_collect_owed": "Please collect the full amount owed", "create_form": "CREATE VOTE", "imei_exist": "IMEI already exists in order", "pls_enter_serial": "Please enter product SERIAL", "placeholder_imei": "Enter warranty IMEI", "imei_not_in_stock": "IMEI is not in stock", "placeholder_search": "Enter SP code, IMEI"}, "offlineSale": {"imei_not_in_stock": "IMEI is not in stock", "placeholder_search": "Enter Product code, IMEI"}, "offlineNavigate": {"manager": "MANAGE SALE VOICES", "search": "SEARCH PRODUCTS", "promotion": "PROMOTION INFORMATION", "cart": "OFFLINE CART", "create_form": "CREATE VOTE"}, "goodsReturn": {"fee_details": "CHI TIẾT PHÍ THU", "fee_tax_details": "CHI TIẾT MỨC PHIẾU THU", "period": "<PERSON><PERSON><PERSON><PERSON> thời gian", "fee_return_product": "<PERSON><PERSON> tr<PERSON> m<PERSON> (%)", "fee_return_product_on_day": "<PERSON><PERSON> tr<PERSON> máy trên <PERSON> (%)", "corresponding_value": "<PERSON><PERSON><PERSON> trị tương <PERSON>ng", "btn_accept": "OK", "btn_done": "<PERSON><PERSON>", "time_hold_product": "<PERSON>h<PERSON>i gian giữ sản phẩm", "export_price_vat": "Đơn giá xuất có VAT", "fee_return": "<PERSON><PERSON> hoàn tiền", "fee_return_total": "<PERSON>í thu + hoàn tiền", "fee_warranty": "<PERSON><PERSON><PERSON>m phí vi phạm cam kết b<PERSON><PERSON> hành", "fee_total": "Tổng cộng phí", "customer": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>: ", "phone_number": "<PERSON><PERSON> điện thoại: ", "address": "Địa chỉ: ", "export_date": "<PERSON><PERSON><PERSON>: ", "export_store": "<PERSON><PERSON>: ", "lookup_fee": "<PERSON><PERSON><PERSON> h<PERSON>", "product_status": "<PERSON><PERSON><PERSON> trạng sản phẩm", "return_quantity": "Số lư<PERSON>ng trả", "select_product_status": "<PERSON><PERSON><PERSON> tình trạng sản phẩm", "refund_method": "<PERSON><PERSON><PERSON><PERSON> thức hoàn tiền", "select_refund_method": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> thức hoàn tiền", "return_accessories": "<PERSON><PERSON> s<PERSON>ch phụ kiện tr<PERSON> hàng", "detail": "<PERSON> ti<PERSON>", "residual_value_of_product": "<PERSON><PERSON><PERSON> trị còn lại của sản phẩm", "total_fee": "T<PERSON>ng phí thu", "status": "Tình trạng máy: ", "return_product_fee": "<PERSON><PERSON> thu sản phẩm trả", "note": "<PERSON><PERSON><PERSON>", "adjust_reduce_fees": "Điều chỉnh giảm thu phí", "placeholder_search": "Nhập IMEI/Mã phiếu xuất/Mã YCX/SĐT", "product_id": "<PERSON>ã sản phẩm: ", "imei": "IMEI: ", "quantity": "Số lượng: ", "price": "Giá: ", "return_value": "<PERSON><PERSON><PERSON> trị trả hàng: ", "fees_per_one": "<PERSON><PERSON> thu", "fees": "<PERSON><PERSON> thu", "fees_percent": "Tỉ lệ thu phí (%): ", "no_found_goods_return": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin yêu cầu xuất", "please_select_an_product": "<PERSON><PERSON> lòng chọn sản phẩm để tiếp tục.", "seri": "Seri: ", "holding_time": "Thời gian giữ sản phẩm: ", "output_type": "<PERSON><PERSON><PERSON> thức xuất: ", "returned_quantity": "Số lượng đã đổi trả: ", "view_detail": "<PERSON>em chi tiết", "fee_due_to_deposit": "Tiền thu do giữ lại phí cọc", "fee_due_to_surcharge": "Tiền thu do giữ lại phụ phí", "lookup_product_returns": "<PERSON>ra c<PERSON>u đổi tr<PERSON> hàng", "total": "<PERSON><PERSON><PERSON> hóa đơn: ", "payment_method": "<PERSON><PERSON><PERSON> thức thanh to<PERSON>: ", "adjust_fees": "Điều chỉnh phí thu", "no_params_declare_note": "<PERSON><PERSON><PERSON> phẩm chưa khai báo tham số đổi trả hàng. <PERSON><PERSON> lòng liên hệ 18291 - <PERSON><PERSON><PERSON>ễn Đ<PERSON><PERSON> Hào để hỗ trợ khai báo.", "no_params_declare_notification": "Có %{quantity} sản phẩm chưa khai báo tham số trả hàng. Vui lòng xem lý do tại phần thông tin thêm.", "no_cashback_method": "<PERSON><PERSON><PERSON><PERSON> tìm thấy ph<PERSON><PERSON><PERSON> thức hoàn trả", "no_fee_info": "<PERSON><PERSON><PERSON>ng có thông tin giá", "no_invoice_info": "không có thông tin hoá đơn", "not_liquidated_yet": "CHƯA THANH LÝ", "liquidated": "THANH LÝ", "liquidation_cancel": "ĐÃ HUỶ", "liquidation_unknown": "KHÔNG RÕ", "contract_status": "<PERSON><PERSON><PERSON><PERSON> thái hợp đồng", "full_payment": "Trả thẳng", "installment": "Trả góp", "contract_id": "<PERSON><PERSON> hợp đồng: ", "total_amount": "Tổng tiền: ", "product_list": "DANH SÁCH SẢN PHẨM", "paid_total_amount": "Số tiền đã thanh toán", "total_amount_of_missing_promotion": "Tiền thu do không trả sản phẩm khuyến mãi", "total_amount_of_debt_payment_promotion": "Tiền thu do đã chi nợ sản phẩm khuyến mãi", "total_amount_of_missing_accessories": "Ti<PERSON>n thu do trả thiếu phụ kiện", "other_total_amount": "<PERSON><PERSON><PERSON> phí thu khác", "collection_detail_of_sale_order": "CHI TIẾT THU CỦA YÊU CẦU XUẤT", "cash": "Tiền mặt", "money_from_card": "<PERSON><PERSON><PERSON><PERSON> qua thẻ", "money_from_voucher": "<PERSON><PERSON><PERSON><PERSON> phiếu thu mua hàng", "cash_in_bank": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>i ngân hàng", "vip_point": "<PERSON><PERSON><PERSON><PERSON> quà tặng VIP", "money_collected_by_accountant": "Tiền do kế toán thu", "clearing_debt_money": "Tiền tự động cấn trừ công nợ", "no_info_clearing_debt_money": "<PERSON><PERSON><PERSON><PERSON> có chi tiết tiền tự động cấn trừ công nợ.", "paid": "<PERSON><PERSON> thanh toán", "total_final": "<PERSON><PERSON><PERSON> cộng", "notice_content": "<PERSON>ã thanh toán được tính là tổng tiền không bao gồm tiền do kế toán thu.", "notice": "<PERSON><PERSON><PERSON>", "money_amount": "<PERSON><PERSON> tiền", "surcharge_detail": "<PERSON> tiết phụ phí", "delivery_cost": "Phí giao: ", "forward_cost": "<PERSON><PERSON> chuyển: ", "opportunity_cost": "<PERSON><PERSON> c<PERSON> hội: ", "please_select_product_status": "<PERSON><PERSON> lòng chọn Tình trạng sản phẩm", "processed_date": "<PERSON><PERSON><PERSON> chi: ", "processed_store_name": "<PERSON>ho chi: ", "process_voucher_concern": "Mã phiếu chi: ", "debt_promotion_paid": "Số tiền chi: ", "debt_payment_promotion_list_header": "DANH SÁCH SẢN PHẨM CHI NỢ KHUYẾN MÃI", "select_invoice_back_header": "CHỌN HOÁ ĐƠN THU HỒI", "returns_products_list_header": "DANH SÁCH SẢN PHẨM TRẢ HÀNG", "whether_return_record_is_valid": "<PERSON><PERSON><PERSON><PERSON> bản tr<PERSON> hàng hợp lệ", "whether_customer_asks_invoice": "<PERSON><PERSON><PERSON><PERSON> hàng có xuất hoá đơn", "fee_due_to_lost_invoice": "<PERSON><PERSON> do mất hoá đơn", "full_box_fee": "<PERSON><PERSON> đổi Fullbox", "product_required_jobcard": "<PERSON><PERSON><PERSON> phẩm yêu cầu nhập mã <PERSON><PERSON> khi đổi trả", "product_auto_generated_jobcard_push_to_wms": "<PERSON><PERSON><PERSON> phẩm tự động tạo Jobcard đ<PERSON>y qua WMS", "product_required_expertise_from_crm": "<PERSON><PERSON><PERSON> phẩm có yêu cầu phiếu thẩm định từ CRM", "alert_negative_fee": "<PERSON><PERSON><PERSON> trị còn lại của sản phẩm < 0. <PERSON><PERSON><PERSON> không thể nhập trả sản phẩm này. <PERSON>ui lòng liên hệ 3588 - Vương Văn <PERSON> để hỗ trợ!", "alert_input_negative_quantity": "<PERSON><PERSON><PERSON><PERSON> thể nhập tr<PERSON> số lượng nhỏ hơn 0", "alert_input_over_quantity": "<PERSON><PERSON><PERSON><PERSON> thể nhập trả số lượng lớn hơn số lượng cần trả", "surcharge": "Phụ phí: ", "must_collect": "<PERSON><PERSON><PERSON> thu", "must_expense": "<PERSON><PERSON><PERSON> chi", "installment_contract_info_header": "THÔNG TIN HỢP ĐỒNG TRẢ GÓP", "select_exchanged_promotion_header": "CHỌN KHUYẾN MÃI ĐÃ ĐỔI HÀNG CẦN TRẢ LẠI", "no_params_exchange": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tham số đổi trả"}, "switchIMEI": {"placeholder_Search": "<PERSON><PERSON><PERSON><PERSON> mã Sp, <PERSON><PERSON><PERSON>, mã <PERSON>", "not_found_product": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin sản phẩm", "choice_status": "<PERSON><PERSON><PERSON> trạng thái sản phẩm", "product_input": "<PERSON><PERSON><PERSON> ph<PERSON>m đã nhập", "btn_export": "<PERSON><PERSON><PERSON> đổi hàng", "switch_Product": "<PERSON><PERSON><PERSON> phẩm chuyển đổi", "placeholder_imei": "Nhập IMEI sản phẩm", "btn_input": "<PERSON><PERSON><PERSON><PERSON>", "pls_choice_status": "VUi lòng chọn trạng thái sản phẩm", "enter_and_check_imei": "<PERSON><PERSON> lòng nhập IMEI và kiểm tra IMEI hợp lệ ", "confirm_export": "<PERSON><PERSON>n chắc chắn muốn xuất đổi hàng", "notify_success": "<PERSON><PERSON> xuất chuyển đổi thành công", "product_name": "<PERSON><PERSON><PERSON> sản phẩm: ", "product_id": "<PERSON>ã sản phẩm: ", "enter_other_IMEI": "IMEI đã tồn tại trong lư<PERSON>i, <PERSON><PERSON> lò<PERSON> nhập IMEI khác.", "count_imei": "Tổng IMEI đã nhập:", "quantity_imei_instock": "<PERSON><PERSON> lượng tồn kho không bị lock hàng:", "scanned_successfully": "<PERSON><PERSON> quét thành công"}, "searchInOutVoucher": {"text_input_placeholder": "Enter UserID/CM/ContractID/Phone number", "search_type_-1": "Search by - All", "search_type_1": "User ID", "search_type_2": "CM", "search_type_3": "Contract ID", "search_type_4": "Phone number", "source": "Generation Source", "customer_name": "Customer name", "total_liquidate": "Total liquidate", "debt": "Debt", "voucher_type_name": "Voucher type name", "inout_voucher_not_found": "In out voucher information not found"}, "pharmacy": {"no_selected_product": "<PERSON><PERSON> lòng chọn sản phẩm để tiếp tục.", "no_quantity_product": "<PERSON><PERSON> lòng nhập số lượng sản phẩm.", "lot_date": "Lô/Date", "batch_id": "Số lô", "expiration": "Hạn sử dụng", "stock": "SL tồn", "quantity": "SL bán", "lot_date_info": "Thông tin Lô Date", "wrong_total_quantity_msg": "<PERSON><PERSON> lư<PERSON><PERSON> bán đ<PERSON> nhập chưa đúng. <PERSON><PERSON> lòng kiểm tra lại.", "over_total_instock_msg": "<PERSON><PERSON> lượng bán đ<PERSON> nhập quá số lượng tồn còn lại.", "stock_available": "<PERSON><PERSON><PERSON> b<PERSON>", "before_meal": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>n", "after_meal": "<PERSON>u khi <PERSON>n", "instruction_placeholder": "<PERSON><PERSON><PERSON><PERSON> hướng dẫn sử dụng", "morning": "<PERSON><PERSON><PERSON>", "noon": "Trưa", "afternoon": "<PERSON><PERSON><PERSON>", "evening": "<PERSON><PERSON><PERSON>", "quantity_acronym": "SL", "information": "Thông tin", "quantity_header": "Số lượng", "lot_date_header": "Lô Date", "unknown_batch": "<PERSON><PERSON> ch<PERSON>a x<PERSON><PERSON>"}, "insert_new_product": {"name_product": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "active": "<PERSON><PERSON><PERSON> ch<PERSON>", "quantity": "<PERSON><PERSON> l<PERSON> cần", "industry": "<PERSON><PERSON><PERSON>", "supplier": "<PERSON><PERSON><PERSON> cung cấp", "insert": "<PERSON><PERSON><PERSON><PERSON> mới", "complete": "<PERSON><PERSON><PERSON> t<PERSON>t", "delete": "<PERSON><PERSON><PERSON>", "pls_enter_product_name": "<PERSON><PERSON> lòng nhập tên thuốc", "pls_enter_active": "<PERSON><PERSON> lòng nh<PERSON><PERSON> ho<PERSON> chất", "pls_choice_industry": "<PERSON><PERSON> lòng chọn nhành hàng", "validate_quantity": "<PERSON><PERSON> lượng bạn nhập chưa hợp lệ. <PERSON>ui lòng nhập lại!", "validate_image": "<PERSON><PERSON> lòng ch<PERSON><PERSON> sản phẩm"}, "tips": {"use_guide": "Mẹo sử dụng", "article": "Hướng dẫn", "list_of_article": "<PERSON><PERSON> s<PERSON>ch b<PERSON>i vi<PERSON>t", "detail_of_article": "<PERSON> tiết bài viết", "no_use_guide_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông tin mẹo sử dụng"}, "saleExpress": {"warning_required_promotions_with_params": "<PERSON><PERSON> lòng chọn <PERSON> mãi bắt buộc:\n%{productNames}", "confirm_clear_all_products": "Bạn muốn xóa toàn bộ sản phẩm?", "product_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy sản phẩm phù hợp", "product_lack_of_in_stock_with_params": "SP %{productName} không đủ số lượng tồn", "warning_required_promotions": "<PERSON><PERSON>n chưa chọn <PERSON>ến mãi b<PERSON><PERSON> buộ<PERSON>.", "not_enough_in_stock": "không đủ số lượng tồn", "not_made_price": "ch<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> làm giá", "warning_validation_product_with_params": "<PERSON><PERSON>n phẩm %{ProductName} %{msgText}.", "error_can_not_create_cart": "<PERSON><PERSON><PERSON><PERSON> thể tạo giỏ hàng, vui lòng kiểm tra lại.", "warning_apply_coupon": "<PERSON><PERSON> lòng chọn khu<PERSON>ến mãi trư<PERSON><PERSON> khi áp dụng Coupon.", "warning_empty_coupon": "<PERSON><PERSON> lòng nh<PERSON>p Coupon.", "warning_not_found_promotions": "<PERSON><PERSON><PERSON><PERSON> tìm thấy khu<PERSON>ến mãi.", "title_usage_guide": "Hướng dẫn sử dụng", "title_promotions": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", "title_dosage": "<PERSON><PERSON><PERSON> d<PERSON>", "title_sale_promotions": "<PERSON><PERSON>", "placeholder_coupon": "Mã coupon", "warning_no_dosage": "Trong giỏ hàng đang không có sản phẩm nào thuộc ngành hàng có hướng dẫn sử dụng, li<PERSON><PERSON> dùng", "scan_to_find_product": "Quét Barcode để thêm sản phẩm", "payment_info": "Thông tin thanh toán", "provisional_price": "<PERSON><PERSON><PERSON>", "placeholder_drug_suggestion": "<PERSON><PERSON><PERSON><PERSON> tê<PERSON> b<PERSON>nh, tri<PERSON><PERSON> chứ<PERSON>... (Vd: <PERSON><PERSON><PERSON><PERSON>)", "confirm_replace_products": "Bạn có muốn chọn sản phẩm thay thế không?", "title_auxiliary": "SP Bổ trợ", "title_replaced_products": "<PERSON><PERSON><PERSON> ph<PERSON>m thay thế", "quantity": "Số lượng", "in_stock_quantity_with_params": "SL tồn: %{quantity}", "unit_name": "Đơn vị tính: ", "retry": "<PERSON><PERSON><PERSON> lại", "category": "Phân loại gói", "disease_name": "<PERSON><PERSON><PERSON> b<PERSON>: ", "btn_show_less": "<PERSON><PERSON>", "prescribe": "<PERSON><PERSON> đ<PERSON>n thuốc", "label_provisional_price": "<PERSON><PERSON><PERSON> t<PERSON>: ", "label_ingredient": "Thành phần: ", "confirm_clear_cart": "<PERSON>hao tác này sẽ xóa toàn bộ sản phẩm trong giỏ hàng, bạn có muốn tiếp tục?", "quantity_acronym": "SL: %{quantity}", "total_amount": "<PERSON><PERSON><PERSON> tiền", "extra_discount_amount": "Tổng tiền đã giảm thêm", "discount_by_coupon": "Giảm giá coupon", "discount_by_cart_promotion": "<PERSON><PERSON><PERSON><PERSON> giá tổng đơn", "total_paid": "<PERSON><PERSON><PERSON> to<PERSON>", "tax_id": "<PERSON><PERSON> số thuế", "company_name": "<PERSON><PERSON>n công ty", "company_address": "Địa chỉ công ty", "company_phone": "<PERSON><PERSON> điện tho<PERSON>i công ty", "placeholder_tax_id": "<PERSON><PERSON><PERSON><PERSON> mã số thuế", "placeholder_company_address": "<PERSON><PERSON><PERSON><PERSON> địa chỉ công ty", "placeholder_company_phone": "<PERSON><PERSON><PERSON><PERSON> số điện thoại công ty", "placeholder_phone": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "contact_phone": "<PERSON><PERSON> điện thoại liên hệ", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "mr": "<PERSON><PERSON>", "ms": "Chị", "placeholder_customer_name": "<PERSON><PERSON><PERSON><PERSON> họ tên khách hàng", "full_name": "<PERSON><PERSON> tên", "contact_name": "<PERSON><PERSON> tên ng<PERSON><PERSON>i liên hệ", "contact_address": "<PERSON><PERSON><PERSON> chỉ liên hệ", "address": "Địa chỉ", "placeholder_address": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "info_upload_take_photo_of_prescription": "Bạn vui lòng tải hình ảnh hoặc chụp lại ảnh đơn thuốc", "upload_or_take_photo": "Chọn/<PERSON><PERSON><PERSON>nh", "info_max_size_photo": "Tối đa 10MB/3 ảnh", "hot_bonus": "<PERSON><PERSON><PERSON><PERSON> thưởng nóng", "lot_date": "Lô/Date", "title_cart_promotion": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi tổng đơn", "choose_promotion": "<PERSON><PERSON><PERSON> mãi", "info_promotion": "Th<PERSON>ng tin khu<PERSON>ến mãi", "total_quantity_of_cart": "Tổng số lượng sản phẩm trong giỏ hàng", "placeholder_search_drugs": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ất", "placeholder_search_ava": "Nhập <PERSON>, Tên SP, IMEI", "search_result": "Search results", "title_cart": "Giỏ hàng", "title_customer_info": "<PERSON><PERSON><PERSON><PERSON>", "title_prescription": "<PERSON><PERSON>", "title_sale_express": "<PERSON><PERSON> hàng không tư vấn", "title_drug_suggestion": "<PERSON><PERSON><PERSON><PERSON> c<PERSON><PERSON> li<PERSON>", "title_provisional_cart_list": "Giỏ hàng tạm", "title_combo": "Combo", "title_vicinity": "<PERSON><PERSON>", "title_alternative": "<PERSON><PERSON><PERSON> ý thay thế", "title_receive_at_store": "<PERSON><PERSON><PERSON><PERSON> tại siêu thị", "title_home_delivery": "<PERSON><PERSON><PERSON> t<PERSON>n n<PERSON>", "title_promotion_profit": "<PERSON><PERSON> nhiều gi<PERSON><PERSON> nhiều", "placeholder_search_provisional_cart": "<PERSON><PERSON><PERSON><PERSON> tên giỏ hàng, ng<PERSON><PERSON> tạo (Vd: Giỏ hàng 1)", "notify_provisional_cart_1": "<PERSON><PERSON> sách giỏ hàng sẽ được xoá sau 24 giờ mỗi ngày.", "notify_provisional_cart_2": "Giỏ hàng sẽ không lưu lại thông tin khuyến mãi, thông tin Lô/date", "confirm_delete_all_provisional_carts": "Bạn có muốn xoá tất cả các %{target} không?", "shipping_info": "<PERSON><PERSON>ng giao đến địa chỉ: %{address}", "store_change_info": "<PERSON><PERSON>ng xin từ kho: %{id} - %{name}", "warning_enough_stock": "<PERSON><PERSON><PERSON> kho tại siêu thị đang đủ bán. <PERSON>ạn không thể sử dụng tính năng này.", "required_quantity": "<PERSON><PERSON> l<PERSON> cần tìm", "view_cross_selling_products": "<PERSON><PERSON> ph<PERSON>m b<PERSON> k<PERSON>m", "inventory_status_new": "<PERSON><PERSON><PERSON>", "inventory_status_old_date": "Cận date", "inventory_status_new_with_defect": "M<PERSON>i (Giảm giá)", "no_buy_product_close_date": "<PERSON>ản phẩm với trạng thái cận date không đư<PERSON>c sử dụng chức năng này", "view_promotion": "<PERSON><PERSON> mãi", "info_out_stock_promotion": "<PERSON><PERSON><PERSON><PERSON> trình khu<PERSON>ến mãi đã hết quà tặng."}, "inventory": {"add_new_area": "Add new area: ", "area_list": "Inventory areas: ", "delete": "Delete", "save": "Save", "inventory_type": "Inventory type: ", "list_inventory_period": "Inventory terms: ", "complete_input": "Confirm input", "inventory_status": "Inventory status: ", "select_control_area": "Select area: ", "select_product_status": "Select product status ", "have_emei": "has IMEI", "not_emei": "no IMEI", "collapse": "Collapse", "see_all": "View all", "input_while_checking": "Inputted while checking", "amount": "Quantity", "product_id": "Product ID: ", "product_status": "Status: ", "save_and_go_back": "Save and go back", "save_and_continue_scanning": "Save and scanning", "total_checked_quantity": "Total checked quantity", "total_checking_quantity": "Total checking quantity", "unit_short": "Unit", "list_inventory_term": "Inventory terms", "inventory_history": "Inventory history", "checked_quantity": "Checked qtt", "checking_quantity": "Checking qtt", "check_quantity": "Check qtt:", "imei_not_exist_in_system": "IMEI does not exist in system", "time_start": "Start: ", "time_end": "Finish: ", "not_started": "Not started", "choose_product": "Select product", "product_no_imei": "Product no IMEI", "product_has_imei": "Product has IMEI", "inventory_product_no_imei_uppercase": "INVENTORY PRODUCT NO IMEI", "select_inventory_status_product_no_imei": "Select product no IMEI status", "select_inventory_status_product_with_imei": "Select product has IMEI status", "exported": "Exported", "quantity": "Quantity", "inventory_status_2": "Inventory status", "area_management": "Area management", "inventory": "Inventory product", "view_inventory_result": "Inventory result", "view_arrears_result": "Arrears result", "no_inventory_area_found": "No inventory area found", "no_inventory_type_found": "No inventory type found", "no_inventory_history_found": "No inventory history found", "no_inventory_term_found": "No inventory term found", "noti_no_inventory_area_found": "Please go to \"AREA MANAGEMENT\" and \"Add new\" at least 1 area", "no_inventory_status_found": "No inventory status found!", "no_valid_data_found": "No valid data found", "product_info_not_found": "No product information found!", "lock_product_unsuccessful": "Lock product failed", "no_arrears_product_found": "No arrears product found", "no_processing_arrears_product_found": "No processing arrears product found", "no_arrears_staff_info_found": "no arrears staff's information found", "no_imei_found": "No product's list IMEI found", "no_product_found": "No product found", "error_delete_inventory_info": "Delete inventory information failed!", "delete_inventory_info_success": "Delete inventory's information successfully!", "area_list_auto_create_by_system": "System created 2 inventory ereas: Showroom and Store", "enter_area_name": "Enter inventory area's name", "new_area_name": "New area's name", "input_arrears_staff": "Enter arrears staff's ID", "staff_id": "Staff ID", "paid_amount": "Paid amount", "input_amount": "Input amount", "id": "ID", "no_process": "Not processed", "processed": "Processed", "total_amount": "Total amount", "company_paid": "Company paid: ", "extra_paid": "Extra paid: ", "receipt": "Receipt: ", "staff_paid": "Staff paid: ", "status": "Status", "cash_amount": "Cash", "arrears_list": "Arrears list", "arrears_expect": "Expected arrears", "total_arrears_amount": "Total arrears amount:", "print_report": "Print report", "save_info": "Save information", "update": "Update", "staff_id_short": "Staff's ID", "staff_name": "Staff's name", "cash": "Cash", "upload_report_image": "Upload report image ", "max_3_files": "(Max: 3 files)", "attach_image": "Attach image ", "max_5_files": "(Max: 5 files)", "note_print_report": "(Note: Please print report before taking image)", "please_print_report": "Please print report first!", "select_time": "Select time:", "select_inventory_term": "Select inventory term:", "checked_product": "Checked product", "product_needs_to_check": "Product need to check", "deviant_after_minus_transferring": "Deviant after minus transferring:", "no_imei_uppercase": "No IMEI", "has_imei_uppercase": "Has IMEI", "changed_stock_quantity": "Stock quantity changed!\nPlease delete data and recheck.", "delete_inventory_data": "Delete data", "stock_quantity_short": "Stock qtt: ", "not_including_in_transit": "\n(Not including in transit)", "check_quantity_short": "Check qtt: ", "view_detail": "Detail", "stock_status_short": "Stock stt", "inventory_status_short": "Check stt", "inventory_bill_id": "Inventory ID: ", "user_id": "User's ID: ", "check_qtt": "Check quantity: ", "checked_qtt": "Checked quantity: ", "adjusted_quantity": "Adjusted quantity", "total_adjusted_quantity": "Total adjusted quantity", "inventory_area": "Inventory area: ", "inventory_result_no_imei_product_uppercase": "NO IMEI PRODUCT'S INVENTORY RESULT", "selected_inventory_status": "Selected inventory status: ", "deviated_status": "Deviated status: ", "all": "All", "deviated_product": "Deviated product", "changed_stock_qtt_product": "Changed stock quantity", "not_devated_product": "Not deviated product", "from_to": "From date - To date: ", "search": "Search", "area": "Area", "has_been_deleted": "has been deleted", "enter_imei": "Enter IMEI", "imei_is_belong_to": "IMEI is belong to product", "please_check_again": "Please check again!", "modified_products_exist": "Modified products exist", "max_5_image": "You can only add up to 5 pictures!", "scan_try_again": "Rescan", "select_all": "Select all", "report_product_ticket": "Report product state ticket", "select_report_product_ticket": "Select report product state ticket", "request_arrears_process": "Request for arrears has been processed", "not_have_access": "You are not authorized to use this feature", "report_product_state": "Report actual product state", "pls_enter_product_before_enter_imei": "Please enter the product ID before entering the IMEI!", "not_enough_inventory": "Insufficient inventory, please check again!", "not_out": "You must select \"Create request\" before leaving this screen!", "created_success": "Create request successfully!", "product_list": "LIST OF PRODUCTS", "status_1": "Status: ", "create_request": "Create request", "differ_process_code": "does not have the same inventory process code as the product", "import_export": "You confirm to create the request, and when processing this request, it will be Excessively import - Deficiently export.", "not_product_with_code_inventory": "No products found with a valid inventory process code!", "product_selected_with_imei": "The product you selected is the product with IMEI. Please check again!", "product_selected_no_imei": "The product you selected is the product with no IMEI. Please check again!", "product_same_product": "The correct product is the same as the wrong product, please check again!", "product_selected_with_no_imei": "The product you selected is the product without IMEI. Please check again!", "enter_product_same_product_shot": "entering has the same IMEI as the product shot in. Please check again!", "import_inventory": "entering is in stock. Please check again!", "no_import_export_history": "has no import/export history and wrong format. Please contact the branch accountant for processing!", "no_history_import": "has no import/export history. Are you sure to create a request?", "no_history_systems": "has no import/export history on the system. Are you sure to create a request?", "in_stock": "is in stock at store", "input_not_allow": "is not allow to Excessively import. Please check again!", "exists_system": "already exists in the system, please check again!", "total_convert": "Total exchange quantity: ", "enter_quantity": "Enter quantity: ", "true_product_id": "True product ID: ", "true_imei": "True IMEI: ", "out_system_imei": " (external IMEI)", "note": "Note: ", "enter_note": "Enter note", "unit": "Unit: ", "true_product_info": "TRUE PRODUCT INFORMATION", "report_quantity": "Quantity: ", "label_enter_true_productid": "Enter true PID", "true_productid_placeholder": "True product ID", "label_enter_true_imei": "Enter true IMEI: ", "true_imei_placeholder": "True IMEI", "label_enter_spare_imei": "Enter IMEI: ", "spare_imei_placeholder": "Enter excessive IMEI", "product": "Product", "not_declared_business_status": "Product group of scanned/imported product has not been declared business status.", "status_2": "status", "not_in_stock": "is out of stock. Please check again!", "save_report_request_error": "Error saving report product state request", "no_report_requests_found": "No report product state requests found!", "cannot_get_report_request_detail": "Cannot get report product state request's detailed information!", "update_failed": "Update failed", "select_finish": "You must select finish typing before exiting the screen", "list_imei_export": "List of IMEI exported and not in stock: ", "system_cancel_imei": "The system will cancel this IMEI check request. Please select OK and Save", "imei_different_status": "The IMEI being checked has a different status than the checked state", "contact_supermarket": "contact the supermarket", "processing_check": "for processing before check-in.", "imei_not_exist": "IMEI does not exist", "select_max": "You can only choose up to", "day": "day!", "different_product": "DIFFERENT PRODUCT WITHOUT IMEI", "attched_file": "Please attach the file", "list_employees_product": "List of employees jointly responsible for property/goods loss", "before_save": "before Save", "not_get_content": "Can't get the content of the minutes", "not_get_list_img": "Couldn't get the list of images", "total_exchange_quantity": "Total exchange quantity: ", "select_status": "Select poroduct status", "product_not_found": "No product's information found", "no_report_state_found": "No report product state found!", "select_new_status": "Select new status:", "link_camera": "Link camera: ", "link_camera_placeholder": "Link camera", "off_30": "tidak masuk kerja lebih dari", "off_30_2": "hari. <PERSON>a tidak boleh menambahkan staf kompensasi tambahan. <PERSON><PERSON> perbarui staf kompensasi lainnya.", "invalid_expiration_date": "Invalid date, please try again!", "not_declare_imei_format": "Product requires IMEI but IMEI format hasn't been declared yet!"}, "otpConfirm": {"verify_OTP": "OTP code verification", "will_sent_OTP": "OTP code will be sent to TEL "}, "collection": {"multicat_industry_service": "MULTICAT INDUSTRY SERVICE", "multicat_industry_service_nfp": "Multicat industry service", "deposit_money_into_your_account": "DEPOSIT TO ACCOUNT NUMBER", "managerment_multicat_industry_transactions": "MANAGEMENT OF MULTICAT SECTORS", "managerment_multicat_industry_transactions_nfp": "Management of the Multicat industry", "take_front_id_card": "Please take a photo of the front of your ID/Identity Card", "take_back_id_card": "Please take a photo of the back of your ID/Identity Card", "enter_card_number": "Please enter ID/Identity Card number", "enter_load_name": "Please enter Loader Name", "enter_load_phone_number": "Please enter recharger phone number", "choose_transfer_bank": "Please select the Bank to transfer", "enter_account_number": "Please enter account number", "enter_amount_to_transfer": "Please enter Amount to transfer", "amount_received_must_multiple_of_1000": "The amount received must be a multiple of 1000", "sender_information": "Sender information", "take_a_shot": "Take", "front": "front", "backside": "backside", "id_card": "ID card", "id_card_2": "Citizen ID", "sym_id_card": "ID", "sym_id_card_2": "Identity Card", "card_number": "ID number/Identity Card", "placeholder_ID_card_number": "Enter ID/Identity Card number", "sender_name": "Sender name", "enter_sender_name": "Enter sender name", "sender_phone": "Sender phone number", "enter_sender_phone": "Enter sender phone number", "receiver_information": "Recipient information", "bank": "Bank", "choose_bank": "Select bank", "account_number": "Account number", "account_number_required": "Enter account number", "recipient_name": "Recipient name", "transaction_amount": "Transaction amount", "enter_amount_want_to_deposit": "Enter the amount you want to deposit", "transaction_fee": "Transaction fee", "retry": "Try again", "btn_accept": "OK", "agree_to_terms": "Customers agree to all terms and conditions set out on the website", "customer_signature": "Customer signature", "continue": "CONTINUE", "recharge_request": "Transaction's recharge request", "send_to_App_XWork": " has been notified to the supermarket manager's X-Work app with timekeeping in shifts including: ", "wait_for_management_to_approve": "Please wait for the supermarket manager to confirm on the X-Work App!", "ticket_is_valid_for_10_minutes": "Ticket is valid for 10 minutes", "ticket_status": "ticket status:", "sent_to_supermarket_manager": "Sent to Supermarket Manager", "create_order": "CREATE ORDER", "reply_ticket": "SEND TICKET", "check_the_result": "CHECK RESULTS", "information_check_management": "Staff carefully confirm the account number/Customer information and the amount you want to deposit, receive enough money before pressing the Continue button. This transaction will not support cancellation", "transactions_processed": "Transaction in progress: ", "completed": "completed", "close": "CLOSE", "export_request": "Export request: ", "transaction_type": "Transaction type: ", "status": "Status: ", "note": "Note: ", "customer_name": "Customer name: ", "amount_of_money": "Amount: ", "click_check_result": "Click check results", "collect_money": "collect money", "print_receipt": "Print receipt", "the_transaction_you_create": "Transaction you create", "the_transaction_you_cancel": "The transaction you cancel", "refund_status": "Refund status: ", "refunds": "Refunded"}}