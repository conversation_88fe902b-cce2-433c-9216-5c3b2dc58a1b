import { useRef, useEffect, useCallback } from 'react';

const useCountdown = (setExpireTime, initialTime = 300, setOnlySms = () => { }) => {
    const intervalId = useRef(null);

    const countDown = useCallback(() => {
        setExpireTime((prevExpireTime) => {
            const newExpireTime = prevExpireTime - 1;
            if (newExpireTime > 0) return newExpireTime;
            resetCountdown();
            return 0;
        });
    }, [setExpireTime]);

    const resetCountdown = useCallback(() => {
        if (intervalId.current) {
            clearInterval(intervalId.current);
        }
        setExpireTime(0);
        setOnlySms(true)
    }, [setExpireTime]);

    const startCountdown = useCallback(() => {
        setExpireTime(initialTime);
        if (intervalId.current) {
            clearInterval(intervalId.current); // Clear any previous interval
        }
        intervalId.current = setInterval(countDown, 1000);
    }, [initialTime, countDown, setExpireTime]);

    useEffect(() => {
        // startCountdown();
        return () => {
            if (intervalId.current) {
                clearInterval(intervalId.current);
            }
        };
    }, [startCountdown]);

    return { startCountdown, resetCountdown };
};

export default useCountdown;
