import moment from 'moment';
import dayjs from 'dayjs';
import { IsNonEmptyString } from '../helper';

const customParseFormat = require('dayjs/plugin/customParseFormat');
const localizedFormat = require('dayjs/plugin/localizedFormat');
const isSameOrBefore = require('dayjs/plugin/isSameOrBefore');
const relativeTime = require('dayjs/plugin/relativeTime');

dayjs.extend(localizedFormat);
dayjs.extend(customParseFormat);
dayjs.extend(isSameOrBefore);
dayjs.extend(relativeTime);

const FORTMAT_DATE = "yyyy-mm-ddThh:ii:ss";
const regExpYYYYMMDD = /^((?:19|20)\d\d)[- /](0[1-9]|1[012])[- /](0[1-9]|[12][0-9]|3[01])/;
const regExpDDMMYYYY =
    /^(?:(?:31(\/|-|\.)(?:0?[13578]|1[02]))\1|(?:(?:29|30)(\/|-|\.)(?:0?[13-9]|1[0-2])\2))(?:(?:1[6-9]|[2-9]\d)?\d{2})$|^(?:29(\/|-|\.)0?2\3(?:(?:(?:1[6-9]|[2-9]\d)?(?:0[48]|[2468][048]|[13579][26])|(?:(?:16|[2468][048]|[3579][26])00))))$|^(?:0?[1-9]|1\d|2[0-8])(\/|-|\.)(?:(?:0?[1-9])|(?:1[0-2]))\4(?:(?:1[6-9]|[2-9]\d)?\d{2})$/;


export const isDate = (obj) => {
    return obj !== undefined && obj !== null && obj.constructor === Date;
}

export const pad = (number) => {
    if (number < 10) {
        return `0${number}`;
    }
    return number;
};

export const isValidDate = function (date) {
    return isDate(date) && !Number.isNaN(date.getDate());
};

export const isValidStrDateDDMMYYYY = function (strDate) {
    return regExpDDMMYYYY.test(strDate);
};

export const isValidTime = function (time) {
    return (time > 0) && isValidDate(new Date(time));
};

export const isValidStrDate = function (strDate) {
    return regExpYYYYMMDD.test(strDate);
};

export const getCurrentDate = function () {
    return new Date();
};

export const getTimestamp = function (date = new Date()) {
    return date.getTime();
};

export const convertJStoCwithString = (date = new Date()) => {
    if (isValidDate(date)) {
        let offset = date.getTimezoneOffset();
        const sign = (offset < 0) ? '+' : '-';
        const z_hh = pad(parseInt(Math.abs(offset / 60)), 2);
        const z_mm = pad(Math.abs(offset % 60), 2);
        const tzd = `${sign}${z_hh}:${z_mm}`;
        const dd = pad(date.getDate());
        const mm = pad(date.getMonth() + 1);
        const yyyy = date.getFullYear();
        const hh = pad(date.getHours());
        const ii = pad(date.getMinutes());
        const ss = pad(date.getSeconds());
        const s = (date.getMilliseconds() / 1000).toFixed(2).slice(2, 4)
        return `${yyyy}-${mm}-${dd}T${hh}:${ii}:${ss}.${s}${tzd}`;
    }
    return "";
};

export const formatDateYYYYMMDD = function (date = new Date()) {
    if (isValidDate(date)) {
        const dd = pad(date.getDate());
        const mm = pad(date.getMonth() + 1);
        const yyyy = date.getFullYear();
        return `${yyyy}-${mm}-${dd}`;
    }
    return "";
};

export const formatDateFULL = function (date = new Date()) {
    if (isValidDate(date)) {
        const dd = pad(date.getDate());
        const mm = pad(date.getMonth() + 1);
        const yyyy = date.getFullYear();
        const hh = pad(date.getHours());
        const ii = pad(date.getMinutes());
        const ss = pad(date.getSeconds());
        return `${dd}/${mm}/${yyyy} ${hh}:${ii}:${ss}`;
    }
    return "";
};

export const formatDateDDMMYYYY = function (date = new Date()) {
    if (isValidDate(date)) {
        const dd = pad(date.getDate());
        const mm = pad(date.getMonth() + 1);
        const yyyy = date.getFullYear();
        return `${dd}/${mm}/${yyyy}`;
    }
    return "";
};

export const formatDateMMYYYY = function (date = new Date()) {
    if (isValidDate(date)) {
        const mm = pad(date.getMonth() + 1);
        const yyyy = date.getFullYear();
        return `${mm}/${yyyy}`;
    }
    return "";
};

export const formatDateDDMM = function (date = new Date()) {
    if (isValidDate(date)) {
        const dd = pad(date.getDate());
        const mm = pad(date.getMonth() + 1);
        return `${dd}/${mm}`;
    }
    return "";
};

export const formatDateHHMM = function (date = new Date()) {
    if (isValidDate(date)) {
        const hh = pad(date.getHours());
        const ii = pad(date.getMinutes());
        return `${hh}:${ii}`;
    }
    return "";
};

export const formatTimeFULL = function (time) {
    if (isValidTime(time)) {
        date = new Date(time);
        const dd = pad(date.getDate());
        const mm = pad(date.getMonth() + 1);
        const yyyy = date.getFullYear();
        const hh = pad(date.getHours());
        const ii = pad(date.getMinutes());
        const ss = pad(date.getSeconds());
        return `${dd}/${mm}/${yyyy} ${hh}:${ii}:${ss}`;
    }
    return "";
};

export const formatTimeDDMMYYYY = function (time) {
    if (isValidTime(time)) {
        date = new Date(time);
        const dd = pad(date.getDate());
        const mm = pad(date.getMonth() + 1);
        const yyyy = date.getFullYear();
        return `${dd}/${mm}/${yyyy}`;
    }
    return "";
};

export const formatTimeHHMM = function (time) {
    if (isValidTime(time)) {
        date = new Date(time);
        const hh = pad(date.getHours());
        const ii = pad(date.getMinutes());
        return `${hh}:${ii}`;
    }
    return "";
};

export const convert_string_to_date = function (strDate, formatDate = FORTMAT_DATE) {
    if (isValidStrDate(strDate)) {
        const reg = new RegExp('T');
        const reg2 = new RegExp('Z');
        strDate = strDate.replace(reg, ' ').replace(reg2, '');
        formatDate = formatDate.replace(reg, ' ');
        return String(strDate).toDate(formatDate);
    }
    return "";
};

export const formatStrDateFULL = function (strDate, formatDate = FORTMAT_DATE) {
    const date = convert_string_to_date(strDate, formatDate);
    if (isValidDate(date)) {
        const dd = pad(date.getDate());
        const mm = pad(date.getMonth() + 1);
        const yyyy = date.getFullYear();
        const hh = pad(date.getHours());
        const ii = pad(date.getMinutes());
        const ss = pad(date.getSeconds());
        return `${dd}/${mm}/${yyyy} ${hh}:${ii}:${ss}`;
    }
    return strDate;
};

export const formatStrDateDDMMYYYY = function (strDate, formatDate = FORTMAT_DATE) {
    const date = convert_string_to_date(strDate, formatDate);
    if (isValidDate(date)) {
        const dd = pad(date.getDate());
        const mm = pad(date.getMonth() + 1);
        const yyyy = date.getFullYear();
        return `${dd}/${mm}/${yyyy}`;
    }
    return strDate;
};

export const formatStrDateDDMM = function (strDate, formatDate = FORTMAT_DATE) {
    const date = convert_string_to_date(strDate, formatDate);
    if (isValidDate(date)) {
        const dd = pad(date.getDate());
        const mm = pad(date.getMonth() + 1);
        return `${dd}/${mm}`;
    }
    return strDate;
};

export const formatStrDateHHMM = function (strDate, formatDate = FORTMAT_DATE) {
    const date = convert_string_to_date(strDate, formatDate);
    if (isValidDate(date)) {
        const hh = pad(date.getHours());
        const ii = pad(date.getMinutes());
        return `${hh}:${ii}`;
    }
    return strDate;
};

String.prototype.toDate = function (formatDate = FORTMAT_DATE) {
    if (IsNonEmptyString(this)) {
        const today = new Date();
        const normalized = this.replace(/[^a-zA-Z0-9]/g, '-');
        const normalizedFormat = formatDate.toLowerCase().replace(/[^a-zA-Z0-9]/g, '-');
        const formatItems = normalizedFormat.split('-');
        const dateItems = normalized.split('-');
        const monthIndex = formatItems.indexOf('mm');
        const dayIndex = formatItems.indexOf('dd');
        const yearIndex = formatItems.indexOf('yyyy');
        const hourIndex = formatItems.indexOf('hh');
        const minutesIndex = formatItems.indexOf('ii');
        const secondsIndex = formatItems.indexOf('ss');
        const year = (yearIndex > -1) ? dateItems[yearIndex] : today.getFullYear();
        const month = (monthIndex > -1) ? (dateItems[monthIndex] - 1) : (today.getMonth() - 1);
        const day = (dayIndex > -1) ? dateItems[dayIndex] : today.getDate();
        const hour = (hourIndex > -1) ? dateItems[hourIndex] : today.getHours();
        const minute = (minutesIndex > -1) ? dateItems[minutesIndex] : today.getMinutes();
        const second = (secondsIndex > -1) ? dateItems[secondsIndex] : today.getSeconds();
        return new Date(year, month, day, hour, minute, second);
    }
    return "";
};

const DAY_OF_WEEK = {
    sunday: 0,
    monday: 1,
    tuesday: 2,
    wednesday: 3,
    thursday: 4,
    friday: 5,
    saturday: 6
};

export const getDayOfWeek = function (date = new Date()) {
    const day = isValidDate(date) ? date.getDay() : "";
    switch (day) {
        case DAY_OF_WEEK.sunday:
            return "Chủ nhật";
        case DAY_OF_WEEK.monday:
            return "Thứ hai";
        case DAY_OF_WEEK.tuesday:
            return "Thứ ba";
        case DAY_OF_WEEK.wednesday:
            return "Thứ tư";
        case DAY_OF_WEEK.thursday:
            return "Thứ năm";
        case DAY_OF_WEEK.friday:
            return "Thứ sáu";
        case DAY_OF_WEEK.saturday:
            return "Thứ bảy";
        default:
            return day;
    }
}

/*  */
const getDetailTimes = (hour, date) => {
    const data = [];
    for (let i = hour; i < 23; i = i + 2) {
        const start = pad(i - 2);
        const end = pad(i);
        data.push({
            "hour": i,
            "isWarning": false,
            "deliveryText": `${start}h00 - ${end}h00`,
            "deliveryValue": `${date}T${end}:00:00`
        })
    }
    return data;
}

export const initSuggestTimes = () => {
    let dataDate = {};
    let minDate = "";
    let maxDate = "";
    let date = new Date();
    date.setMinutes(0);
    date.setSeconds(0);
    let hour = date.getHours() + 2;
    if (hour < 10) {
        hour = 10;
        date.setHours(hour);
    }
    else if (hour > 22) {
        hour = 10;
        date.setHours(hour);
        date.setDate(date.getDate() + 1);
    }
    else {
        hour = (hour % 2 == 0) ? hour : (hour + 1);
        date.setHours(hour);
    }
    minDate = formatDateYYYYMMDD(date);
    dataDate[`${minDate}`] = getDetailTimes(hour, minDate);
    for (let i = 1; i <= 6; i++) {
        const now = new Date(date);
        now.setDate(now.getDate() + i);
        maxDate = formatDateYYYYMMDD(now);
        dataDate[`${maxDate}`] = getDetailTimes(10, maxDate);
    }
    return { minDate, maxDate, dataDate };
}

export const initResponseSuggestTime = () => {
    let dataDate = [];
    let date = new Date();
    date.setMinutes(0);
    date.setSeconds(0);
    let hour = date.getHours() + 2;
    if (hour < 10) {
        hour = 10;
        date.setHours(hour);
    }
    else if (hour > 22) {
        hour = 10;
        date.setHours(hour);
        date.setDate(date.getDate() + 1);
    }
    else {
        hour = (hour % 2 == 0) ? hour : (hour + 1);
        date.setHours(hour);
    }
    const minDate = formatDateYYYYMMDD(date);
    dataDate = dataDate.concat(getDetailTimes(hour, minDate));
    for (let i = 1; i <= 6; i++) {
        const now = new Date(date);
        now.setDate(now.getDate() + i);
        const maxDate = formatDateYYYYMMDD(now);
        dataDate = dataDate.concat(getDetailTimes(10, maxDate));
    }
    return dataDate;
}

export const getNextDay = (date = new Date()) => {
    const next = new Date(date.getTime());
    next.setDate(date.getDate() + 1);
    return next;
}

export const getLastThreeMonths = () => {
    const date = new Date();
    let result = [];
    for (i = 0; i < 3; i++) {
        const firstDate = new Date(date.getFullYear(), date.getMonth() - i, 1);
        const lastDate = new Date(date.getFullYear(), date.getMonth() - i + 1, 0);
        const item = {
            monthIndex: i,
            title: `${pad(firstDate.getMonth() + 1)}/${firstDate.getFullYear()}`,
            startDate: formatDateYYYYMMDD(firstDate),
            endDate: formatDateYYYYMMDD(lastDate)
        }
        result.push(item)
    }
    return result
}

export const getTimeElapsed = (date) => {
    const now = new Date();
    const secondsElapsed = Math.floor((now - date) / 1000);

    // Calculate the number of days, hours, minutes, and seconds elapsed
    const days = Math.floor(secondsElapsed / (24 * 60 * 60));
    const hours = Math.floor(secondsElapsed / (60 * 60)) % 24;
    const minutes = Math.floor(secondsElapsed / 60) % 60;
    const seconds = secondsElapsed % 60;

    return {
        days,
        hours,
        minutes,
        seconds
    };
};

// date: timestamp
export const getTimeElapseString = (date) => {
    const { days, hours, minutes, seconds } = getTimeElapsed(date);
    let timeElapsedString = formatDateYYYYMMDD(new Date(date));

    if (days <= 30) {
        if (days > 0) {
            timeElapsedString = `${days} ngày trước`;
        } else if (hours > 0) {
            timeElapsedString = `${hours} giờ trước`;
        } else if (minutes > 0) {
            timeElapsedString = `${minutes} phút trước`;
        } else {
            timeElapsedString = `${seconds} giây trước`;
        }
    }

    return timeElapsedString;
};

export const getDiffDays = function (date1, date2) {
    let diffTime = Math.abs(date2.getTime() - date1.getTime());
    let diffDays = Math.ceil(diffTime / (24 * 60 * 60 * 1000));
    return diffDays;
};
