import { combineReducers } from 'redux';
import { appSwitchReducer } from '../container/AppSwitch/reducer';
import { loginReducer } from '../container/Login/reducer';
import { authenReducer } from '../container/Splash/reducer';
import { userReducer } from '../container/UserInfo/reducer';
import { networkReducer } from '../container/Network/reducer';
import { pouchDBReducer } from '../container/PouchDB/reducer';
import { cmVoucherReducer } from '../container/CMMoneyComplaint/reducer';
import { CODPayReducer } from '../container/CodPay/reducer';
import { ActiveSimManagementReducer } from '../container/ActiveSimManager/reducer';
import { locationReducer } from '../container/Location/reducer';
import { pouchFavorite, pouchCartApply, pouchProvisionalCarts } from '../container/PouchRedux/reducer';
import { saleReducer } from '../container/Sale/reducer';
import { detailReducer } from '../container/Detail/reducer';
import { shoppingCartReducer } from '../container/ShoppingCart/reducer';
import { saleOrderCartReducer } from '../container/SaleOrderCart/reducer';
import { managerSOReducer } from '../container/SaleOrderManager/reducer';
import { saleOrderPaymentReducer } from '../container/SaleOrderPayment/reducer';
import { InstallmentReducer, DataCacheInstallmentReducer } from '../container/InstallmentManager/reducer';
import { editSaleOrderReducer } from '../container/EditSaleOrder/reducer';
import { additionalPromotionSOReducer } from "../container/AdditionalPromotion/reducer";
import { appSettingReducer } from '../container/AppSetting/reducer';
import { addSalePromotionReducer } from '../container/AddSalePromotion/reducer';
import { searchImeiReducer } from "../container/SearchImei/reducer";
import { f88Reducer } from "../container/PortalF88/reducer";
import { stickerProtectorReducer } from "../container/StickerProtector/reducer";
import { oclockReducer } from '../container/Oclock/reducer';
import { notifyReducer } from '../container/Notification/reducer';
import { UploadPictureReducer } from '../container/UploadPictureOldProduct/reducer';
import { offlineCartReducer } from '../container/OfflineCart/reducer';
import { offlineCreateOrderReducer } from '../container/OfflineCreateOrder/reducer';
import { offlineManagerSRReducer } from '../container/OfflineOrderManager/reducer';
import { offlinePaymentReducer } from '../container/OfflinePayment/reducer';
import { offlineSaleReducer } from '../container/OfflineSale/reducer';
import { productDisplayManagerReducer } from '../container/ProductDisplayManager/reducer';
import { goodsReturnReducer } from '../container/ProductReturns/reducer';
import { VoucherReducer } from "../container/InOutVoucher/reducer";
import { specialSaleProgramReducer } from '../container/SpecialSaleProgram/reducer';
import { pharmacyReducer } from '../container/AnKhangPharmacy/reducer';
import { cardReducer } from '../container/Card/reducer';
import { printPriceListReducer } from '../container/PrintPriceList/reducer';
import { newRequestProductReducer } from '../container/NewProductRequest/reducer';
import { _pharmacyReducer } from '../container/AnKhangNew/reducer';
import { serviceReceiptReducer } from '../container/ServiceReceiptManager/reducer';
import { serviceReceiptPaymentReducer } from "../container/ServiceReceiptPayment/reducer";
import { staffPromotionReducer } from "../container/StaffPromotion/reducer";
import { collectionReducer } from '../container/CollectionTransfer/reducer';
import { collectionManagerReducer } from '../container/CollectionTransferManager/reducer';
import { consultReducer } from '../container/ClientConsultant/reducer';
import { insuranceReducer } from '../container/Insurance/reducer';
import { insuranceManagerReducer } from '../container/InsuranceManager/reducer';
import { insuranceBrightsideReducer } from '../container/InsuranceBrightside/reducer';
import { collectAndCloseRepairReducer } from '../container/TanTamCollection/PaymentCollectionAndClosingServices/reducer';
import { electricityReducer } from '../container/ElectricityBill/reducer'
import { collectInstallmentReducer } from '../container/CollectInstallmentPayments/reducer';
import { insurancePVIReducer } from '../container/InsurancePVI/reducer';
import { insuranceAirtimeServiceReducer } from '../container/InsuranceAirtimeService/reducer'
/*  */
import { suggestTypeReducer } from "../container/InventoryPeriod/reducer";
import { inventoryProductReducer } from "../container/InventoryProduct/reducer";
import { productWithoutImeiReducer } from "../container/ProductWithoutImei/reducer";
import { getReportProductReducer } from "../container/ProductDifferentNumber/reducer";
import { productDeflectionReducer } from "../container/ProductDeflection/reducer";
import { inventoryOptionsReducer } from "../container/InventoryOptions/reducer";
import { viewArrearsResultsReducer } from "../container/ViewArrearsResults/reducer";
import { locationInventoryReducer } from "../container/LocationProduct/reducer";
import recordDataReducer from "../container/ProductWithoutImei/reducer";
import recordDataReducerImeis from "../container/InventoryProduct/reducer";
import recordDataReducerStaffArrears from "../container/ViewArrearsResults/reducer";
import { inventoryReducer } from '../container/Inventory/reducer';
import { productEvaluationReducer } from '../container/ProductEvaluation/reducer';
import { loyaltyReducer } from '../container/Loyalty/reducer';
import { simPrintPriceReducer } from '../container/PrintSimPrice/reducer';
import { restockReducer } from '../container/Restock/reducer';
import { waterBillReducer } from '../container/WaterBill/reducer';
import { healthInsuranceReducer } from '../container/HealthInsurance/reducer';
import { insuranceExtendedReducer } from '../container/InsuranceExtendedWarranty/reducer';
import { paymentTransactionsReducer } from '../container/PaymentTransactions/reducer';
import { bankAirtimeServiceReducer } from '../container/BankAirtimeService/reducer';
import { consumerLoanAirtimeServiceReducer } from '../container/ConsumerLoanAirtimeService/reducer';
import { cardOpeningServiceReducer } from '../container/CardOpeningService/reducer';
import { sendBankReducer } from '../container/SendBankManager/reducer'

const rootReducer = (state, action) => {
    // when a logout action is dispatched it will reset redux state
    switch (action.type) {
        case 'RESET_DATA_LOGOUT':
            state.userReducer = undefined;
            state.pouchDBReducer = undefined;
            state.pouchCartApply = undefined;
            state.pouchFavorite = undefined;
            state.pouchProvisionalCarts = undefined;
            state.appSettingReducer = undefined;
            /*  */
            state.saleReducer = undefined;
            state.detailReducer = undefined;
            state.shoppingCartReducer = undefined;
            state.saleOrderCartReducer = undefined;
            state.managerSOReducer = undefined;
            state.saleOrderPaymentReducer = undefined;
            state.editSaleOrderReducer = undefined;
            state.cmVoucherReducer = undefined;
            state.CODPayReducer = undefined;
            state.ActiveSimManagementReducer = undefined;
            state.InstallmentReducer = undefined;
            state.DataCacheInstallmentReducer = undefined;
            state.inventoryOptionsReducer = undefined;
            state.locationInventoryReducer = undefined;
            state.viewArrearsResultsReducer = undefined;
            state.recordDataReducerStaffArrears = undefined;
            state.additionalPromotionSOReducer = undefined;
            state.addSalePromotionReducer = undefined;
            state.searchImeiReducer = undefined;
            state.f88Reducer = undefined;
            state.stickerProtectorReducer = undefined;
            state.oclockReducer = undefined;
            state.notifyReducer = undefined;
            state.UploadPictureReducer = undefined;
            state.offlineCartReducer = undefined;
            state.offlineCreateOrderReducer = undefined;
            state.offlinePaymentReducer = undefined;
            state.offlineSaleReducer = undefined;
            state.offlineManagerSRReducer = undefined;
            state.productDisplayManagerReducer = undefined;
            state.goodsReturnReducer = undefined;
            state.VoucherReducer = undefined;
            state.specialSaleProgramReducer = undefined;
            state.pharmacyReducer = undefined;
            state.cardReducer = undefined;
            state.printPriceListReducer = undefined;
            state.newRequestProductReducer = undefined;
            state._pharmacyReducer = undefined;
            state.serviceReceiptReducer = undefined;
            state.serviceReceiptPaymentReducer = undefined;
            state.inventoryReducer = undefined;
            state.staffPromotionReducer = undefined;
            state.productEvaluationReducer = undefined;
            state.loyaltyReducer = undefined;
            state.collectionReducer = undefined;
            state.collectionManagerReducer = undefined;
            state.consultReducer = undefined;
            state.restockReducer = undefined;
            state.insuranceReducer = undefined;
            state.insuranceManagerReducer = undefined;
            state.insuranceBrightsideReducer = undefined;
            state.collectAndCloseRepairReducer = undefined;
            state.electricityReducer = undefined;
            state.waterBillReducer = undefined;
            state.collectInstallmentReducer = undefined;
            state.healthInsuranceReducer = undefined;
            state.insuranceExtendedReducer = undefined;
            state.insurancePVIReducer = undefined;
            state.paymentTransactionsReducer = undefined
            state.insuranceAirtimeServiceReducer = undefined;
            state.bankAirtimeServiceReducer = undefined;
            state.consumerLoanAirtimeServiceReducer = undefined;
            state.cardOpeningServiceReducer = undefined;
            state.sendBankReducer = undefined
            break;
        case 'RESET_CHANGE_STORE':
            state.saleReducer = undefined;
            state.detailReducer = undefined;
            state.shoppingCartReducer = undefined;
            state.saleOrderCartReducer = undefined;
            state.managerSOReducer = undefined;
            state.saleOrderPaymentReducer = undefined;
            state.editSaleOrderReducer = undefined;
            state.cmVoucherReducer = undefined;
            state.CODPayReducer = undefined;
            state.ActiveSimManagementReducer = undefined;
            state.InstallmentReducer = undefined;
            state.DataCacheInstallmentReducer = undefined;
            state.inventoryOptionsReducer = undefined;
            state.locationInventoryReducer = undefined;
            state.viewArrearsResultsReducer = undefined;
            state.recordDataReducerStaffArrears = undefined;
            state.additionalPromotionSOReducer = undefined;
            state.addSalePromotionReducer = undefined;
            state.searchImeiReducer = undefined;
            state.f88Reducer = undefined;
            state.stickerProtectorReducer = undefined;
            state.oclockReducer = undefined;
            state.UploadPictureReducer = undefined;
            state.offlineCartReducer = undefined;
            state.offlineCreateOrderReducer = undefined;
            state.offlinePaymentReducer = undefined;
            state.offlineSaleReducer = undefined;
            state.offlineManagerSRReducer = undefined;
            state.productDisplayManagerReducer = undefined;
            state.goodsReturnReducer = undefined;
            state.VoucherReducer = undefined;
            state.specialSaleProgramReducer = undefined;
            state.pharmacyReducer = undefined;
            state.cardReducer = undefined;
            state.printPriceListReducer = undefined;
            state.newRequestProductReducer = undefined;
            state._pharmacyReducer = undefined;
            state.serviceReceiptReducer = undefined;
            state.serviceReceiptPaymentReducer = undefined;
            state.inventoryReducer = undefined;
            state.staffPromotionReducer = undefined;
            state.productEvaluationReducer = undefined;
            state.loyaltyReducer = undefined;
            state.collectionReducer = undefined;
            state.collectionManagerReducer = undefined;
            state.consultReducer = undefined;
            state.restockReducer = undefined;
            state.insuranceReducer = undefined;
            state.insuranceManagerReducer = undefined;
            state.insuranceBrightsideReducer = undefined;
            state.collectAndCloseRepairReducer = undefined;
            state.electricityReducer = undefined;
            state.waterBillReducer = undefined;
            state.collectInstallmentReducer = undefined;
            state.healthInsuranceReducer = undefined;
            state.insuranceExtendedReducer = undefined;
            state.insurancePVIReducer = undefined;
            state.paymentTransactionsReducer = undefined
            state.insuranceAirtimeServiceReducer = undefined;
            state.bankAirtimeServiceReducer = undefined;
            state.consumerLoanAirtimeServiceReducer = undefined;
            state.cardOpeningServiceReducer = undefined;
            state.sendBankReducer = undefined
            break;
        default:
            break;
    }
    return appReducer(state, action);
};

const appReducer = combineReducers({
    appSwitchReducer,
    loginReducer,
    authenReducer,
    userReducer,
    networkReducer,
    pouchDBReducer,
    cmVoucherReducer,
    CODPayReducer,
    ActiveSimManagementReducer,
    locationReducer,
    pouchFavorite,
    pouchCartApply,
    pouchProvisionalCarts,
    saleReducer,
    detailReducer,
    shoppingCartReducer,
    saleOrderCartReducer,
    managerSOReducer,
    saleOrderPaymentReducer,
    InstallmentReducer,
    DataCacheInstallmentReducer,
    editSaleOrderReducer,
    additionalPromotionSOReducer,
    appSettingReducer,
    addSalePromotionReducer,
    searchImeiReducer,
    f88Reducer,
    stickerProtectorReducer,
    oclockReducer,
    notifyReducer,
    UploadPictureReducer,
    offlineCartReducer,
    offlineCreateOrderReducer,
    offlineManagerSRReducer,
    offlinePaymentReducer,
    offlineSaleReducer,
    productDisplayManagerReducer,
    goodsReturnReducer,
    VoucherReducer,
    specialSaleProgramReducer,
    pharmacyReducer,
    cardReducer,
    printPriceListReducer,
    newRequestProductReducer,
    _pharmacyReducer,
    collectionReducer,
    collectionManagerReducer,
    consultReducer,
    insuranceReducer,
    insuranceManagerReducer,
    electricityReducer,
    /*  */
    inventoryProductReducer,
    suggestTypeReducer,
    productWithoutImeiReducer,
    recordDataReducer,
    recordDataReducerImeis,
    getReportProductReducer,
    productDeflectionReducer,
    inventoryOptionsReducer,
    locationInventoryReducer,
    viewArrearsResultsReducer,
    recordDataReducerStaffArrears,
    serviceReceiptReducer,
    serviceReceiptPaymentReducer,
    inventoryReducer,
    staffPromotionReducer,
    productEvaluationReducer,
    loyaltyReducer,
    simPrintPriceReducer,
    restockReducer,
    insuranceBrightsideReducer,
    collectAndCloseRepairReducer,
    waterBillReducer,
    collectInstallmentReducer,
    healthInsuranceReducer,
    insuranceExtendedReducer,
    insurancePVIReducer,
    paymentTransactionsReducer,
    insuranceAirtimeServiceReducer,
    bankAirtimeServiceReducer,
    consumerLoanAirtimeServiceReducer,
    cardOpeningServiceReducer,
    sendBankReducer
});

export { rootReducer };
