import React from 'react';
import {
    View,
} from 'react-native';
import { MyText, Icon } from '@components';
import { COLORS } from '@styles';

const HeaderRight = ({ info }) => {
    const { speed, level } = info;
    const { name, color } = SIGNAL_MAP[level];
    return (
        <View style={{
            height: 54,
            width: 50,
            justifyContent: "center",
            alignItems: "center",
        }}>
            <Icon
                iconSet={"MaterialCommunityIcons"}
                name={name}
                color={COLORS.icFFFFFF}
                size={24}
            />
            <MyText
                style={{
                    color: color,
                    fontSize: 10
                }}
                text={speed}
                children={<MyText
                    style={{
                        color: COLORS.txtFFFFFF,
                        fontSize: 10
                    }}
                    text={" KB/s"}
                />}
            />
        </View>
    );
}

export default HeaderRight;

const SIGNAL_MAP = [
    {
        name: "signal-cellular-outline",
        color: COLORS.txtFF0000
    },
    {
        name: "signal-cellular-1",
        color: COLORS.txtFFFF00
    },
    {
        name: "signal-cellular-2",
        color: COLORS.txtFFFFFF
    },
    {
        name: "signal-cellular-3",
        color: COLORS.txtFFFFFF
    }
]