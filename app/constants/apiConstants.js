import Config from "react-native-config";
const {
    HOST,
    HOST_SEARCH,
    HOST_SALE,
    HOST_PARTNER,
    HOST_INVENTORY,
    HOST_AIRTIME,
    MPOS_HOST,
    OFFLINE_HOST,
    FIREWALL_HOST,
    LED_HOST,
    F88_HOST,
    AUTH_SERVICE,
    PRODUCT_SERVICE,
    MEDIA_SERVICE,
    MPOS_SERVICE,
    PRODUCT_ERP_SERVICE,
    LOYALTY_SERVICE,
    LOYALTY_ERP_SERVICE,
    PRODUCT_SERVICE_SEARCH,
    PRODUCT_DISPLAY_SERVICE,
    PLANOGRAM_SERVICE,
    SYNC_USER_SERVICE,
    PRODUCT_SERVICE_OFFLINE,
    PROMOTION_SERVICE,
    AIRTIME_SERVICE,
    PROFILE_SERVICE,
    EXCHANGE_RETURN_SERVICE,
    // MAP_SERVICE,
    // HOST_MAP,
} = Config;

const HOST_MAP = "https://mapsservice.tgdd.vn/"
const MAP_SERVICE = "mwg-app-service-gis-web-service/"


const APIERP = "api/erp/";
const API_BCNB = "api/bcnb/";
export const API_REQUEST_OAUTH_TOKEN = HOST + AUTH_SERVICE + 'oauth/token';
export const API_REQUEST_TOKEN = HOST + AUTH_SERVICE + APIERP + 'auth';
export const API_REFRESH_TOKEN = HOST + AUTH_SERVICE + APIERP + 'refreshToken';

const PRODUCT_INFO = "product/info/";
const PRODUCT_PROMOTION = "product/promotion/";
const PRODUCT_PROMOTION_SALE = "product/promotionforsale/";
const STORE_INFO = "store/info/";
const LOCATION = "location/";
const PRODUCT_STOCK = "product/stock/";
const CART = "cart/";
const PARTNER = "partner/";
const SALEORDER = "saleorder/";
const SEARCH_PRODUCT = "searchproduct/";
const SALE_STOCK = "salestock/";
const POINT = "point/";
const MEMBER = "member/";
const API = "api/";
const SIM = "sim/";
const MEDIA = "media/pos/";
const SALE = "sale/";
const XPOS_TOKEN = "xpos/token/";
const PAYMENT = "payment/";
const REPORT = "report/";
const IDENTIFY = "identify/";
const PROMOTION = "promotion/";
const API_INVENTORY = "api/inventory-area/"
const API_TERM_INVENTORY = "inventoryterm/"
const SCREEN_STICKER = "screensticker/";
const PRODUCT_CONTROLLER_SERVICE = "serviceproductscontroller/";
const WATCH_BATTERY = "watchbattery/";
const NOTIFICATION = "notification/";
const OLDPRODUCT = "oldproduct/";
const AUTH_OFFLINE = "oauth/posoffline/";
const GOODS_RETURN = "exchangesaleorder/";
const VOUCHER = "voucher/";
const PROMOTION_ERP = "promotion/erp/";
const API_USER_GUIDE = 'api/user/userguide/';
const ANKHANG = 'ankhang/';
const COMBO = 'combo/';
const LOYALTY = 'loyalty/';
const WORKFLOW = 'exchangereturn/workflow/'
const SENDBANK = 'accounting/sendbank/'
const API_TICKET = 'api/ticket/bot/';

// mwg-app-erp-service
export const API_DETAILS_PRODUCT = HOST_SALE + PRODUCT_SERVICE + APIERP + PRODUCT_INFO;
export const API_PROMOTION = HOST_SALE + PRODUCT_SERVICE + APIERP + PRODUCT_PROMOTION;
export const API_PROMOTION_NEWSTOCK = HOST_SALE + PRODUCT_SERVICE + APIERP + PRODUCT_PROMOTION + 'newstock';
export const API_PROMOTION_SECONDSTOCK = HOST_SALE + PRODUCT_SERVICE + APIERP + PRODUCT_PROMOTION + 'secondstock';
export const API_PROMOTION_EXHIBISTOCK = HOST_SALE + PRODUCT_SERVICE + APIERP + PRODUCT_PROMOTION + 'exhibitstock';
export const API_GET_STORE_INFO = HOST_SALE + PRODUCT_SERVICE + APIERP + STORE_INFO + 'search';
export const API_GET_OUTPUT_STORE = HOST_SALE + PRODUCT_SERVICE + APIERP + STORE_INFO + 'searchv2';
export const API_CHECK_CUSTOMER_PHONE_PROMOTION = HOST_SALE + PRODUCT_SERVICE + APIERP + PRODUCT_PROMOTION + 'checkcustomerphone';
export const API_CHECK_BARCODE_PROMOTION = HOST_SALE + PRODUCT_SERVICE + APIERP + PRODUCT_PROMOTION + 'barcode';
export const API_GET_PROMOTION_SALE_ES = HOST_SALE + PRODUCT_SERVICE + APIERP + PRODUCT_PROMOTION_SALE + 'getpromotionlist'; // EXPRESS-SALE
export const API_GET_PROMOTION_CSSALE = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + 'getpromotionlistofpromotionlistgroupID'; // EXPRESS-SALE
export const API_GET_PROMOTION_CSSALE_OLD_TOOL = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + 'getpromotionlistofpromotionlistgroupIDlowercase'; // EXPRESS-SALE
export const API_CHECK_IMEI_PROMOTION = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + 'checkimei';
export const API_CHECK_STUDENT_DISCOUNT = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + 'checkcandidatenoback2school';
export const API_GET_PROVINCE = HOST_SALE + PRODUCT_SERVICE + APIERP + LOCATION + "province";
export const API_GET_DISTRICT = HOST_SALE + PRODUCT_SERVICE + APIERP + LOCATION + "district";
export const API_GET_WARD = HOST_SALE + PRODUCT_SERVICE + APIERP + LOCATION + "ward";
export const API_GET_STORE_SHIPPING = HOST_SALE + PRODUCT_SERVICE + APIERP + PRODUCT_STOCK + "shipping";
export const API_GET_STORE_NEAREST = HOST_SALE + PRODUCT_SERVICE + APIERP + PRODUCT_STOCK + "nearest";
export const API_GET_STORE_BY_LOCATION = HOST_SALE + PRODUCT_SERVICE + APIERP + PRODUCT_STOCK + "location";
export const API_GET_TIMES_BY_DATE = HOST_SALE + PRODUCT_SERVICE + APIERP + PRODUCT_STOCK + "shippingbydate";
export const API_GET_SUGGEST_TIMES = HOST_SALE + PRODUCT_SERVICE + APIERP + "stock/getsuggesttimes4shippingroute";
export const API_GET_STORE_SHIPPING_SO = HOST_SALE + PRODUCT_SERVICE + APIERP + PRODUCT_STOCK + "shippingv2";
export const API_GET_SUGGEST_TIMES_SO = HOST_SALE + PRODUCT_SERVICE + APIERP + "stock/getsuggesttimes4shippingroutev2";
export const API_GET_STORE_TIMES_SO = HOST_SALE + PRODUCT_SERVICE + APIERP + "stock/getnewsuggesttimesatstore";
export const API_GET_TIMES_BY_DATE_SO = HOST_SALE + PRODUCT_SERVICE + APIERP + "stock/shippingbydatev2";
export const API_GET_EMPLOYEE_AT_STORE = HOST_SALE + PRODUCT_SERVICE + API_BCNB + "employee/store";
export const API_PROMOTION_DELIVERYTYPE = HOST_SALE + PRODUCT_SERVICE + APIERP + PRODUCT_PROMOTION + 'deliverytype';
export const API_PROMOTION_LOSTSALE = HOST_SALE + PRODUCT_SERVICE + APIERP + PRODUCT_PROMOTION + 'getlostsale';
export const API_ADD_TO_SHOPPING_CART = HOST_SALE + PRODUCT_SERVICE + APIERP + CART + "promotion";
export const API_ADD_TO_SALE_ORDER_CART = HOST_SALE + PRODUCT_SERVICE + APIERP + CART + "saleorder";
export const SALE_ORDER_CART_PROMOTION = HOST_SALE + PRODUCT_SERVICE + APIERP + CART + SALEORDER + "promotion";
export const API_GET_INSTALLMENT = HOST_SALE + PRODUCT_SERVICE + APIERP + PARTNER + "installment";
export const API_GET_PROGRAM_INSTALLMENT = HOST_SALE + PRODUCT_SERVICE + APIERP + PARTNER + "saleprogram";
export const API_CONTRACT_INSTALLMENT = HOST_SALE + PRODUCT_SERVICE + APIERP + PARTNER + "contract";
export const API_GET_SALE_ORDER_SO_DETAIL = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + "detail";
export const API_LOGO_PARTNER_INSTALLMENT = "https://cdn.tgdd.vn/erp/InstallementPartnerLogos/";
export const API_GET_CUSTOMER_INFO = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + "customerbyphonenumber";
export const API_GET_CUSTOMER_INFO_NEW = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + "customerbyphonenumbernew";
export const API_GET_COMPANY_INFO = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + "companybytaxno";
export const API_GET_MEMBER_POINT = HOST_SALE + PRODUCT_SERVICE + APIERP + MEMBER + "getpoints";
export const API_REQUIRE_OTP = HOST_SALE + PRODUCT_SERVICE + APIERP + POINT + "getotp";
export const API_GET_FIFO_INFO = HOST_SALE + PRODUCT_SERVICE + APIERP + SALE_STOCK + "getfifoinfo";
export const API_GET_CONFIG_PRODUCT = HOST_SALE + PRODUCT_SERVICE + APIERP + SEARCH_PRODUCT + "getproductconfig";
export const API_GET_FEATURE_PRODUCT = HOST_SALE + PRODUCT_SERVICE + APIERP + SEARCH_PRODUCT + "getproductfeature";
export const API_GET_LOCK_PRODUCT_INFO = HOST_SALE + PRODUCT_SERVICE + APIERP + SALE_STOCK + "getlockproductinfo";
export const API_GET_PERMISSION_ADJUST_PRICE = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + "checkper";
export const API_GET_SIM_PRICE = HOST_SALE + PRODUCT_SERVICE + APIERP + SIM + "getsimprice";
export const API_GET_SIM_PACKAGE = HOST_SALE + PRODUCT_SERVICE + APIERP + SIM + "getpackagetype";
export const API_GET_SIM_PACKAGES_NEW = HOST_SALE + PRODUCT_SERVICE + APIERP + SIM + "getpackagestypenew";
export const API_CHECK_PACKAGE_SIM_PRICE = HOST_SALE + PRODUCT_SERVICE + APIERP + SIM + "checkpackagesimprice";
export const API_GET_SIM_PROCESS_INFO = HOST_SALE + PRODUCT_SERVICE + APIERP + SIM + "loadsimprocessinfo";
export const API_GET_SIM_PROCESS_TYPE = HOST_SALE + PRODUCT_SERVICE + APIERP + SIM + "getsimprocesstype";
export const API_GET_SIM_PROVINCE = HOST_SALE + PRODUCT_SERVICE + APIERP + SIM + "getprovincemap";
export const API_GET_SIM_DISTRICT = HOST_SALE + PRODUCT_SERVICE + APIERP + SIM + "getdistrictmap";
export const API_GET_SIM_WARD = HOST_SALE + PRODUCT_SERVICE + APIERP + SIM + "getwardmap";
export const API_GET_SIM_NATIONALITY = HOST_SALE + PRODUCT_SERVICE + APIERP + SIM + "getnationality";
export const API_GET_SIM_ISSUE_PLACE = HOST_SALE + PRODUCT_SERVICE + APIERP + SIM + "getidcardissueplace";
export const API_UPDATE_SIM_PROCESS_INFO = HOST_SALE + PRODUCT_SERVICE + APIERP + SIM + "updatesimprocess";
export const API_CHECK_SIM_SERIAL = HOST_SALE + PRODUCT_SERVICE + APIERP + SIM + "checkserialsim";
export const API_GET_SIM_SERIAL = HOST_SALE + PRODUCT_SERVICE + APIERP + SIM + "getesimserial";
export const API_GET_INFO_VIETTEL = HOST_SALE + PRODUCT_SERVICE + APIERP + SIM + "getCustomerInfoByCardIDFromViettel";
export const API_CHECK_STATUS_CONNECT_SIM_MOBIFONE = HOST_SALE + PRODUCT_SERVICE + APIERP + SIM + "checkStatusSimMobi";
export const API_GET_TOKEN_MOBI = HOST_SALE + PRODUCT_SERVICE + APIERP + SIM + "gettokenjwtmobi";
export const API_GET_QR_CODE_SIM = HOST_SALE + PRODUCT_SERVICE + APIERP + SIM + "getqrcodeesim";
export const API_CANCEL_ORDER_SIM = HOST_SALE + PRODUCT_SERVICE + APIERP + SIM + "cancelordersim";
export const API_QUERY_STATUS_SIM = HOST_SALE + PRODUCT_SERVICE + APIERP + SIM + "querystatussim";
export const API_CREATE_SIM_PROCESS_INFO = HOST_SALE + PRODUCT_SERVICE + APIERP + SIM + "createnewsimprocessrequest";
export const API_READ_INFO_BY_IMAGE = HOST_SALE + PRODUCT_SERVICE + APIERP + "identifyidcard";
export const API_GET_DATA_CREATE_PRICE = HOST_SALE + PRODUCT_SERVICE + APIERP + SALE + "checkcreateprice";
export const API_GET_INFO_CREATE_PRICE = HOST_SALE + PRODUCT_SERVICE + APIERP + SALE + "recalcreateprice";
export const API_GET_DATA_CREATE_PRICE_SO = HOST_SALE + PRODUCT_SERVICE + APIERP + SALE + "checkcreateprice4editso";
export const API_GET_INFO_CREATE_PRICE_SO = HOST_SALE + PRODUCT_SERVICE + APIERP + SALE + "recalcreateprice4editso";
export const API_GET_PAYCASH_INFO = HOST_SALE + PRODUCT_SERVICE + APIERP + SALE + "loadsoandpaycash";
export const API_CHECK_VOUCHER = HOST_SALE + PRODUCT_SERVICE + APIERP + PAYMENT + "giftvoucherissue";
export const API_CHECK_IMEI_SERIAL_EXP = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + "checkimeiserialexp";
export const API_MODIFY_SALE_ORDER = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + "modifysaleorder";
export const API_GET_PRINTER = HOST_SALE + PRODUCT_SERVICE + API + REPORT + 'getreportprinter';
export const API_CHECK_INSTALLMENT = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + 'modifysaleprogram';
export const API_CREATE_OUTPUT_VOUCHER = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + 'createandoutputvoucher';
export const API_CREATE_OTP = HOST_SALE + PRODUCT_SERVICE + APIERP + 'createotp';
export const API_VERIFY_OTP = HOST_SALE + PRODUCT_SERVICE + APIERP + 'verifyotp';
export const API_VERIFY_IDENTIFY = HOST_SALE + PRODUCT_SERVICE + APIERP + IDENTIFY + 'verify';
export const API_CHECK_CREDENTIAL = HOST_SALE + PRODUCT_SERVICE + APIERP + 'credential/exists';
export const API_SEARCH_SO = HOST_SALE + PRODUCT_SERVICE + APIERP + 'saleorder/search';
export const API_VALIDATE_SO = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + 'validate4createcmandov'
export const API_REMOVE_SO = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + 'deletesaleorder'
export const API_GET_EDIT_SALEORDER = HOST_SALE + PRODUCT_SERVICE + APIERP + 'so/loadinfosoandpromodiscount';
export const API_MODIFY_EDIT_SALEORDER = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + 'modifysaleorderv2';
export const API_DETAIL_TO_SALEORDER = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + 'adddetail';
export const API_UPDATE_SALEORDER = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + 'updatesaleorder';
export const API_REPRINT_SALEORDER = HOST_SALE + PRODUCT_SERVICE + API + REPORT + "printsaleorder";
export const API_GET_LIST_REPORT_BY_SO = HOST_SALE + PRODUCT_SERVICE + API + REPORT + "getlistreportbyso";
export const API_CHECK_PHONE_GIFT_VOUCHER = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + "checkcreategiftvoucher";
export const API_MODIFY_DETAIL_PROMOTION = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + 'modifysaleorderv3';
export const API_GET_LIST_SCREEN_STICKER_BY_USER = HOST_SALE + PRODUCT_SERVICE + API + SALEORDER + 'getsoinputimeilistwithuser'
export const API_GET_LIST_SCREEN_STICKER = HOST_SALE + PRODUCT_SERVICE + API + SALEORDER + 'getsoinputimeilist'
export const API_SEARCH_IMEI = HOST_SALE + PRODUCT_SERVICE + API + "stock/getimeihistory";
export const API_USER_RECEIVE_SCREEN_STICKER = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + 'updateusernamebyso'
export const API_GET_PAYMENTMONTHLY = HOST_SALE + PRODUCT_SERVICE + APIERP + "installment/getpaymentamountmonthly";
export const API_GET_PAYMENT_AMOUNT_MONTHLY_BROADCAST = HOST_SALE + PRODUCT_SERVICE + APIERP + 'installment/getpaymentamountmonthlybyepbroadcast';
export const API_ADD_PRODUCT_SALE_ORDER = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + "getpromotionlistproductbyso";
export const API_ADD_PROMOTION_SALE_ORDER = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + "applypromotionso";
export const API_UPDATE_SALE_PROGRAM_ORDER = HOST_SALE + PRODUCT_SERVICE + APIERP + "installment/loadsaleprogramso";
export const API_SEARCH_INSTALLMENT = HOST_SALE + PRODUCT_SERVICE + API + "installment/getepostransactionlistnew";
export const API_SEARCH_SIM = HOST_SALE + PRODUCT_SERVICE + APIERP + "sim/searchsim";
export const API_SEARCH_USER = HOST_SALE + PRODUCT_SERVICE + APIERP + "search/searchuser";
export const API_CHECK_IMEI_SALE = HOST_SALE + PRODUCT_SERVICE + APIERP + "checkimeioftab";
export const API_GET_STORE_CONFIG = HOST_SALE + PRODUCT_SERVICE + APIERP + "getconfigstorenew";
export const API_GET_EPOSTRANSECTION = HOST_SALE + PRODUCT_SERVICE + APIERP + "installment/getepostransactionbyepid";
export const API_GET_GETPAPERTYPEINFOBYEP = HOST_SALE + PRODUCT_SERVICE + APIERP + "installment/getpapertypeinforbyep";
export const API_READ_INFO_BY_IMAGE_DRIVERGLX = HOST_SALE + PRODUCT_SERVICE + APIERP + "identifyidcardglx";
export const API_CONNECT_SIM = HOST_SALE + PRODUCT_SERVICE + APIERP + "connectsim";
export const API_GET_UPDATEEPOSTRANSECTION = HOST_SALE + PRODUCT_SERVICE + APIERP + "installment/updateepostransaction";
export const API_UPDATE_EPOS_BROADCAST = HOST_SALE + PRODUCT_SERVICE + APIERP + "installment/updateeposboadcast";
export const API_GET_IDCARDHOMETOWN = HOST_SALE + PRODUCT_SERVICE + APIERP + "installment/getidcardhometown";
export const API_INSATLLMENT_REASONCANCEL = HOST_SALE + PRODUCT_SERVICE + APIERP + "installment/geteposcancelreason";
export const API_INSATLLMENT_GETATTACHMENTTYPE = HOST_SALE + PRODUCT_SERVICE + APIERP + "installment/getattachmenttypebyid";
export const API_GET_QR_TYPE = HOST_SALE + PRODUCT_SERVICE + APIERP + "getlistpaymenttransactiontype";
export const API_GET_DATA_TRANSACTION = HOST_SALE + PRODUCT_SERVICE + APIERP + "getlistpaymenttransaction";
export const API_GET_DATA_TRANSACTION_SC = HOST_SALE + PRODUCT_SERVICE + APIERP + "smartposgetallpaymenttransactions";
export const API_GET_INSTALLMENT_PRINTER = HOST_SALE + PRODUCT_SERVICE + API + "installment/getreportprinter";
export const API_GET_INSTALLMENT_LIST_REPORT = HOST_SALE + PRODUCT_SERVICE + API + "installment/getlistreport";
export const API_LOAD_INFO_SALEORDER = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + 'loadinfosaleorder';
export const API_LOAD_INFO_SALEORDER_CANCEL = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + 'loadinfosaleordercancel';
export const API_CANCEL_SO = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + 'cancelsaleorder';
export const API_GET_REASON_CANCEL_EP = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + "checkissendpartnerinstallment";
export const API_GET_LIST_APPLICATION_OFFER = HOST_SALE + PRODUCT_SERVICE + APIERP + "installment/getlistapplicationoffer";
export const API_ACCEPT_ALTERNATIVE_OFFERS = HOST_SALE + PRODUCT_SERVICE + APIERP + "installment/acceptalternativeoffers";
export const API_GET_CM_VOUCHER = HOST_SALE + PRODUCT_SERVICE + APIERP + PAYMENT + "loadvoucherinfo";
export const API_REFUND_MONEY_CM_VOUCHER = HOST_SALE + PRODUCT_SERVICE + APIERP + PAYMENT + "createoutvoucher";
export const API_UPDATE_DELIVERYINFO_ODER = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + "changeeditdeliveryinfoso";
export const API_GET_INFO_INOUTVOUCHER = HOST_SALE + PRODUCT_SERVICE + APIERP + "so/loadinfoinoutvouchereditso";
export const API_GET_EXPAND_PROMOTION = HOST_SALE + PRODUCT_SERVICE + APIERP + "product/getpromotionlistbysalepromotionproduct";
export const API_CHECK_SO_PREORDER = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + "checkpreorderso";
export const API_GET_PREORDER_CART = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + "getpromotionpreorderbyso";
export const API_ADD_PROMOTION_PREORDER = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + "applyselectedpromotionpreorder";
export const API_REVIEWPRINT_SALEORDER = HOST_SALE + PRODUCT_SERVICE + API + REPORT + "viewPrintSaleOrder";
export const API_GET_STORE_BY_ID = HOST_SALE + PRODUCT_SERVICE + APIERP + "findstorebyip";
export const API_GET_PROMOTION_PROFIT = HOST_SALE + PRODUCT_SERVICE + APIERP + CART + "promotionprofit";
export const API_GET_PROMOTION_PROFIT_EXPRESS = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + "promotionprofitexpress";
export const API_GET_PROMOTION_PROFIT_SO = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + "promotionprofit4SO";
export const API_INSTALLATION_REPAIR = HOST_SALE + PRODUCT_SERVICE + APIERP + "product/getsaleconsultantproductinfo";
export const API_GET_PRE_ORDER_LOCK_PRODUCT_INFO = HOST_SALE + PRODUCT_SERVICE + APIERP + SALE_STOCK + "getpreorderlockproductinfo";
export const API_PRODUCT_LOYALTY_POINT = HOST_SALE + PRODUCT_ERP_SERVICE + "loyaltyerpapi/loyaltyerp/v1/GetPointByProductList";
export const API_GET_SUGGEST_PRODUCT = HOST_SALE + PRODUCT_SERVICE + APIERP + "salesstock/" + "getpromotionproductsuggestion";
export const API_SEARCH_SO_ADDITIONAL_PROMOTION = HOST_SALE + PRODUCT_SERVICE + APIERP + 'saleorder/searchsoaddpromotion';
export const API_SO_DETAIL_ADDITIONAL_PROMOTION = HOST_SALE + PRODUCT_SERVICE + APIERP + 'saleorder/getsodetailaddpromotion';
export const API_CHECK_PHONE_NUMBER_APPLY_PROMOTION = HOST_SALE + PRODUCT_SERVICE + APIERP + 'saleorder/checkphonenumberapplypromotion';
export const API_EXPORTED_ADDITIONAL_PROMOTION = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + 'getexportedadditionalpromotionso';
export const API_SO_DEBT_ADDITIONAL_PROMOTION = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + 'getdebtadditionalpromotionso';
export const API_GET_APP_SETTING = HOST_SALE + PRODUCT_SERVICE + API + 'incache/get/applicationconfig';
export const API_GET_SIM_PREORDER = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + 'createsimprocessrequestpreorder';
export const API_UPDATE_SIM_PREORDER = HOST_SALE + PRODUCT_SERVICE + APIERP + SIM + 'updatesimprocessrequestfromso';
export const API_ADD_TO_SHOPPING_CART_ADDITIONAL_PROMOTION = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + 'adddebtpromotionproducttoshoppingcart';
export const API_GET_DEBT_MONEY_ADDITIONAL_PROMOTION = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + 'getdebtpromotionmoney';
export const API_PAY_DEBT_MONEY_ADDITIONAL_PROMOTION = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + 'paydebtpromotionmoney';
export const API_PRINT_PAYMENT_VOUCHER_ADDITIONAL_PROMOTION = HOST_SALE + PRODUCT_SERVICE + APIERP + REPORT + 'printdebtpaymentvoucher';
export const API_GET_PROMOTION_ADD_SALE = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + 'getsaleincludeadditionalpromotionso';
export const API_ADD_SALE_TO_CART = HOST_SALE + PRODUCT_SERVICE + APIERP + CART + 'additionPromotion';
export const API_SEARCH_IMEI_HISTOTY = HOST_SALE + PRODUCT_SERVICE + API + "stock/getimeihistory";
export const API_ADD_SALE_BARCODE = HOST_SALE + PRODUCT_SERVICE + APIERP + PRODUCT_PROMOTION + 'barcodeincludepromotion';
export const API_GET_PHONE_VOUCHER = HOST_SALE + PRODUCT_SERVICE + APIERP + PAYMENT + 'getgiftvoucherissuebycustomerphone';
export const API_GET_SO_INPUT_IMEI_LIST = HOST_SALE + PRODUCT_SERVICE + APIERP + SCREEN_STICKER + 'getsoinputimeilist';
export const API_GET_SO_INPUT_IMEI_LIST_WITH_USER = HOST_SALE + PRODUCT_SERVICE + APIERP + SCREEN_STICKER + 'getsoinputimeilistwithuser';
export const API_SEARCH_SCREEN_STICKER = HOST_SALE + PRODUCT_SERVICE + APIERP + SCREEN_STICKER + 'search';
export const API_CREATE_SALE_ORDER_SCREEN_STICKER = HOST_SALE + PRODUCT_SERVICE + APIERP + SCREEN_STICKER + 'createsaleorder';
export const API_GET_PROMOTION_VALUES_DISCOUNT = HOST_SALE + PRODUCT_SERVICE + APIERP + SCREEN_STICKER + 'getpromotionpricevaluesdiscount';
export const API_CREATE_WARRANTY_SALE_ORDER_SCREEN_STICKER = HOST_SALE + PRODUCT_SERVICE + APIERP + SCREEN_STICKER + 'createwarrantysaleorder';
export const API_SEARCH_LOAN_CONTRACT = HOST_SALE + PRODUCT_SERVICE + APIERP + PRODUCT_CONTROLLER_SERVICE + 'searchloancontract';
export const API_GET_HISTORY_TRANSFER_F88 = HOST_SALE + PRODUCT_SERVICE + APIERP + PRODUCT_CONTROLLER_SERVICE + 'searchloanhandoverrequest';
export const API_GET_DETAILS_TRANSFER_F88 = HOST_SALE + PRODUCT_SERVICE + APIERP + PRODUCT_CONTROLLER_SERVICE + 'loadhandoverrequest';
export const API_GET_LOAN_CONTRACT_STATUS_F88 = HOST_SALE + PRODUCT_SERVICE + APIERP + PRODUCT_CONTROLLER_SERVICE + 'getloancontractstatus';
export const API_CREATE_LOAN_HANDOVER_REQUEST_F88 = HOST_SALE + PRODUCT_SERVICE + APIERP + PRODUCT_CONTROLLER_SERVICE + 'createloanhandoverrequest';
export const API_UPDATE_LOAN_HANDOVER_REQUEST_F88 = HOST_SALE + PRODUCT_SERVICE + APIERP + PRODUCT_CONTROLLER_SERVICE + 'updateloanhandoverrequest';
export const API_GET_REPORT_LOG = HOST_SALE + PRODUCT_SERVICE + API + REPORT + "getreportlog";
export const API_GET_EDIT_LOG = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + 'loadeditlog';
export const API_ADD_STICKER_TO_SHOPPING_CART = HOST_SALE + PRODUCT_SERVICE + APIERP + CART + "screenStickerWarranty";
export const API_SEARCH_BATTERY_INFO = HOST_SALE + PRODUCT_SERVICE + APIERP + WATCH_BATTERY + 'search';
export const API_GET_PRICE_BATTERY = HOST_SALE + PRODUCT_SERVICE + APIERP + WATCH_BATTERY + 'getprice';
export const API_CREATE_BATTERY_SO = HOST_SALE + PRODUCT_SERVICE + APIERP + WATCH_BATTERY + 'createbatterysaleorder';
export const API_CREATE_CART_BATTERY = HOST_SALE + PRODUCT_SERVICE + APIERP + CART + 'screenStickerWarranty';
export const API_REPRINT_HANDOVER_F88 = HOST_SALE + PRODUCT_SERVICE + APIERP + PRODUCT_CONTROLLER_SERVICE + 'viewContentPrintHandOverContract';
export const API_SUBSCRIBE_NOTIFY = HOST_SALE + PRODUCT_SERVICE + APIERP + NOTIFICATION + "subscribe";
export const API_UNSUBSCRIBE_NOTIFY = HOST_SALE + PRODUCT_SERVICE + APIERP + NOTIFICATION + "unsubscribe";
export const API_GET_PRINTER_SOCKET = HOST_SALE + PRODUCT_SERVICE + API + 'incache/getstring/dataprint';
export const API_GET_LIST_NOTIFICATION = HOST_SALE + PRODUCT_SERVICE + APIERP + NOTIFICATION + "getlistnotification";
export const API_SEEN_NOTIFICATION = HOST_SALE + PRODUCT_SERVICE + APIERP + NOTIFICATION + "seen/update";
export const API_REMOVE_NOTIFICATION = HOST_SALE + PRODUCT_SERVICE + APIERP + NOTIFICATION + "remove";
export const API_REMOVE_ALL_NOTIFICATION = HOST_SALE + PRODUCT_SERVICE + APIERP + NOTIFICATION + "removeall";
export const API_GET_LIST_OLD_PRODUCT = HOST_SALE + PRODUCT_SERVICE + APIERP + OLDPRODUCT + 'getlistoldproducts';
export const API_GET_OLD_PRODUCT_INFO = HOST_SALE + PRODUCT_SERVICE + APIERP + OLDPRODUCT + 'getoldproductinfo';
export const API_APPROVED_OLD_PRODUCT = HOST_SALE + PRODUCT_SERVICE + APIERP + OLDPRODUCT + 'approvedoldproduct';
export const API_VALID_OR_GET_OLD_PRODUCTS_IMAGE = HOST_SALE + PRODUCT_SERVICE + APIERP + OLDPRODUCT + 'validorgetoldproductsimage';
export const API_GET_PRODUCT_ACCESSORIES = HOST_SALE + PRODUCT_SERVICE + APIERP + OLDPRODUCT + 'getproductaccessories';
export const API_UPDATE_OLDPRODUCT_STATUSDESC = HOST_SALE + PRODUCT_SERVICE + APIERP + OLDPRODUCT + 'updateoldproductstatusdesc';
export const API_GET_OLD_PRODUCT_ACCESSORIES = HOST_SALE + PRODUCT_SERVICE + APIERP + OLDPRODUCT + 'getoldproductaccessories';
export const API_DELETE_OLD_PRODUCT_IMAGE = HOST_SALE + PRODUCT_SERVICE + APIERP + OLDPRODUCT + 'deleteoldproductimage';
export const API_UPDATE_OLD_PRODUCT_INFO = HOST_SALE + PRODUCT_SERVICE + APIERP + OLDPRODUCT + 'updateoldproductinfo';
export const API_GET_OLD_PRODUCT_CATEGORY_LIST = HOST_SALE + PRODUCT_SERVICE + APIERP + OLDPRODUCT + 'getlistoldproductcategoryinfo';
export const API_INSERT_OLD_PRODUCT_IMAGE = HOST_SALE + PRODUCT_SERVICE + APIERP + OLDPRODUCT + 'insertoldproductimage';
export const API_GET_REPORT_DATA = HOST_SALE + PRODUCT_SERVICE + APIERP + OLDPRODUCT + 'getreportdata';
export const API_BAR_CODE_EXPORTED_ADDITIONAL_PROMOTION = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + 'addbarcodeexportedadditionalpromotion';
export const API_INSERT_LOG_PMH = HOST_SALE + PRODUCT_SERVICE + API + 'report/insertreportlog';
export const API_PROMOTION_FOR_VIEW = HOST_SALE + PRODUCT_SERVICE + API + "promotion/getpromotionlistbyproductforview";
export const API_MAP_REPORT_TYPE = HOST_SALE + PRODUCT_SERVICE + API + "report/getreportprinternew";
export const API_CREATE_SALEORDER_OFFLINE = HOST_SALE + PRODUCT_SERVICE + APIERP + 'salereceipt/createSO';
export const API_MAP_ORDER_RECEIPT_OFFLINE = HOST_SALE + PRODUCT_SERVICE + APIERP + 'salereceipt/search';
export const API_SEARCH_EXSO = HOST_SALE + PRODUCT_SERVICE + APIERP + GOODS_RETURN + 'searchexso';
export const API_GET_PRODUCT_EXCHANGE_RETURN_BY_SOD = HOST_SALE + PRODUCT_SERVICE + APIERP + GOODS_RETURN + 'getproductexchreturnbysod';
export const API_GET_PRODUCT_EXCHANGE_ACCESSORIES = HOST_SALE + PRODUCT_SERVICE + APIERP + GOODS_RETURN + 'getprdaccessories';
export const API_GET_PARAM_EXCHANGE_RETURN = HOST_SALE + PRODUCT_SERVICE + APIERP + GOODS_RETURN + 'getparamexchreturn';
export const API_GET_EXCHANGE_FEE_WITH_METHOD = HOST_SALE + PRODUCT_SERVICE + APIERP + GOODS_RETURN + 'getexchangefeewithmethod';
export const API_GET_TOTAL_PAID_AND_VC_DETAIL = HOST_SALE + PRODUCT_SERVICE + APIERP + GOODS_RETURN + 'gettotalpaidandvcdetail';
export const API_GET_INVOICE_BACK = HOST_SALE + PRODUCT_SERVICE + APIERP + GOODS_RETURN + 'getinvoiceback';
export const API_GET_PARAM_EXCHANGE_RETURN_AND_CALC_FEE = HOST_SALE + PRODUCT_SERVICE + APIERP + GOODS_RETURN + 'getparamexchreturnandcalfeedetail';
export const API_CALC_OUT_MONEY_N_KEEP_MONEY_FEE_POLICY = HOST_SALE + PRODUCT_SERVICE + APIERP + GOODS_RETURN + 'caloutmoneyandkeepmoneyfeepolicy';
export const API_CALCULATE_FEE_DETAIL = HOST_SALE + PRODUCT_SERVICE + APIERP + GOODS_RETURN + 'calculatefeedetail';
export const API_PROCESS_EXPORT_IMEI = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + "ProcessProChangeIMEI";
export const API_GET_IMEI_INFO = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + "GetInfoProChangeIMEI";
export const API_GET_WARRANTY_PRODUCT = HOST_SALE + PRODUCT_SERVICE + APIERP + SEARCH_PRODUCT + "getproductpolicy";
export const API_SEARCH_INOUT_VOUCHER = HOST_SALE + PRODUCT_SERVICE + API + VOUCHER + "searchinoutvoucher";
export const API_GET_INFO_INOUT_VOUCHER = HOST_SALE + PRODUCT_SERVICE + API + VOUCHER + "loadinoutvoucherinfo";
export const API_GET_INSTALMENT_INFO_F88 = HOST_SALE + PRODUCT_SERVICE + API + 'installment/senddatatof88';
export const API_GET_BACK_INFORMATION = HOST_SALE + PRODUCT_SERVICE + APIERP + "installment/getinforofepold";
export const API_GET_BROADCAST_PAPER_TYPE = HOST_SALE + PRODUCT_SERVICE + APIERP + 'installment/getbroadcastpapertype';
export const API_GET_BROADCAST_INSTALMENT_PARTNER = HOST_SALE + PRODUCT_SERVICE + APIERP + 'installment/getpartnerinstallmentbroadcast';
export const API_GET_BROADCAST_SALE_PROGRAM = HOST_SALE + PRODUCT_SERVICE + APIERP + 'installment/getsaleprogrambroadcast';
export const API_GET_BROADCAST_LOAN_PACKAGE = HOST_SALE + PRODUCT_SERVICE + APIERP + 'installment/getloanpackagebroadcast';
export const API_GET_BROADCAST_INFORMATION = HOST_SALE + PRODUCT_SERVICE + APIERP + 'installment/getinformationbroadcast';
export const API_ADD_PROMOTION_TO_CART = HOST_SALE + PRODUCT_SERVICE + APIERP + "product/promotiontocart";
export const API_SEARCH_AND_GET_STOCK = HOST_SALE + PRODUCT_SERVICE + APIERP + "product/searchproductandgetinstock";
export const API_GET_CUSTOMER_INFO_BARCODE = HOST_SALE + PRODUCT_SERVICE + API + "cdp/getcustomerbyphonenumber";
export const API_GET_CUSTOMER_INFO_BARCODE_NEW = HOST_SALE + PRODUCT_SERVICE + API + "cdp/getcustomerbyphonenumberNew";
export const API_LOAD_INFO_BATCH_NO_BY_SO = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + "loadinforbatchnobyso";
export const API_LOAD_INFO_BATCH_NO_BY_CART = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + "loadinforbatchnobycart";
export const API_GET_CARD_LIST = HOST_SALE + PRODUCT_SERVICE + APIERP + "serviceproducts/receiptcardsgetbrandlist";
export const API_ACTUAL_SELLING_PRICE = HOST_SALE + PRODUCT_SERVICE + APIERP + "serviceproducts/receiptcardsgetsaleprice";
export const API_GET_PRODUCT_CARD_LIST = HOST_SALE + PRODUCT_SERVICE + APIERP + "serviceproducts/receiptcardsgetproductcardlist";
export const API_ACTUAL_SELLING_PRICE_NEW = HOST_SALE + PRODUCT_SERVICE + APIERP + "serviceproducts/receiptcardsgetsalepricecard";
export const API_RECEIPT_ADD_CART_DETAIL = HOST_SALE + PRODUCT_SERVICE + APIERP + "serviceproducts/receiptcardsadddetail";
export const API_RECEIPT_CARDS_CREATE_OUTPUT_RECEIPT = HOST_SALE + PRODUCT_SERVICE + APIERP + "serviceproducts/receiptcardscreateoutputreceipt"
export const API_GET_MONNEY_CART_LIST = HOST_SALE + PRODUCT_SERVICE + APIERP + "payment/getmoneycardlist";
export const API_CHECK_VOUCHER_CARD = HOST_SALE + PRODUCT_SERVICE + APIERP + PAYMENT + "giftvoucherissuev2";
export const API_MODIFY_RECEIPT_CARD = HOST_SALE + PRODUCT_SERVICE + APIERP + "serviceproducts/receiptcardsmodify";
export const API_GET_USAGE_GUIDE = HOST_SALE + PRODUCT_SERVICE + APIERP + SEARCH_PRODUCT + "getinforproductusageguide";
export const API_GET_DISCOUNT_VALUE = HOST_SALE + PRODUCT_SERVICE + APIERP + "product/getdiscountvalue";
export const API_APPLY_PROMOTION = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + "getrequestapplypromotion";
export const API_CHECK_PERMISSION_STORE_SALE = HOST_SALE + PRODUCT_SERVICE + APIERP + "store/getuserpermission";
export const GET_PRODUCTID_BY_IDREF = HOST_SALE + PRODUCT_SERVICE + APIERP + "product/productidbyproductdref";
export const API_APPLY_COUPON = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + "getrequestapplypromotion";
export const API_SEARCH_PRODUCT_REQUEST = HOST_SALE + PRODUCT_SERVICE + APIERP + "requestnewproduct/search";
export const API_LOAD_INFO_REQUEST = HOST_SALE + PRODUCT_SERVICE + APIERP + "requestnewproduct/loadinfo";
export const API_GET_NEW_REQUEST_PRODUCT = HOST_SALE + PRODUCT_SERVICE + APIERP + "requestnewproduct/add";
export const API_GET_ARTICLES = HOST_SALE + PRODUCT_SERVICE + API_USER_GUIDE + 'getlistarticle';
export const API_GET_ARTICLE_BY_ID = HOST_SALE + PRODUCT_SERVICE + API_USER_GUIDE + 'getarticlebyid';
export const API_GET_IMAGE_SO = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + "getattachments";
export const API_GET_CATEGORIES = HOST_SALE + PRODUCT_SERVICE + API_USER_GUIDE + 'getlistcategory';
export const API_CHECK_PROMOTION_19ANDCOUPON = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + "checkattachmenttypeso";
export const API_UPDATE_IMAGE_SALEORDER = HOST_SALE + PRODUCT_SERVICE + APIERP + SALEORDER + "updateattachment";
export const API_GET_LIST_VOUCHER = HOST_SALE + PRODUCT_SERVICE + APIERP + "getlistpaymenttransactiontype4partner";
export const API_SEARCH_BARCODE_CART_PROMOTION = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + "searchproductgift4promotionorder";
export const API_USED_CODE_PARTNER = HOST_SALE + PRODUCT_SERVICE + APIERP + "usedcodepartner";
export const API_GET_PRODUCT_INFO = HOST_SALE + PRODUCT_SERVICE + APIERP + ANKHANG + 'product/info';
export const API_GET_PRODUCT_PRICE = HOST_SALE + PRODUCT_SERVICE + APIERP + ANKHANG + 'product/getprice';
export const API_GET_PRODUCT_STOCK = HOST_SALE + PRODUCT_SERVICE + APIERP + ANKHANG + 'product/getinstock';
export const API_CHECK_PROMOTION = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + "checkapplypromotionprogram";
export const API_GET_PROMOTION = HOST_SALE + PRODUCT_SERVICE + APIERP + ANKHANG + 'product/getpromotion';
export const API_APPLY_PROMOTION_AK = HOST_SALE + PRODUCT_SERVICE + APIERP + ANKHANG + 'product/promotionapplydiscount';
export const API_ADD_CART = HOST_SALE + PRODUCT_SERVICE + APIERP + ANKHANG + 'product/addcart';
export const API_GET_CART_PROMOTION = HOST_SALE + PRODUCT_SERVICE + APIERP + ANKHANG + 'cart/saleorder/promotion';
export const API_CREATE_SALEORDER_AK = HOST_SALE + PRODUCT_SERVICE + APIERP + ANKHANG + 'product/createsaleorder';
export const API_APPLY_CART_PROMOTION = HOST_SALE + PRODUCT_SERVICE + APIERP + ANKHANG + 'promotion/applybyorder';
export const API_APPLY_COUPON_EXPRESS_SALE = HOST_SALE + PRODUCT_SERVICE + APIERP + ANKHANG + 'promotion/applycoupon';
export const API_GET_BATCH_NO_INFO = HOST_SALE + PRODUCT_SERVICE + APIERP + ANKHANG + 'product/loadinforbatchno';
export const API_GET_STANDARD_POINT = HOST_SALE + PRODUCT_SERVICE + API + 'product/getstandardpoint';
export const API_GET_ALTER_PRODUCTS = HOST_SALE + PRODUCT_SERVICE + APIERP + "ankhang/product/getlistreplaceproductinfo";
export const API_GET_SERVICE_GROUP_TYPE = HOST_SALE + PRODUCT_SERVICE + APIERP + "servicevoucher/getservicegrouptype";
export const API_GET_SERVICE_PROGRAM = HOST_SALE + PRODUCT_SERVICE + APIERP + "servicevoucher/getinfoinsuranceprograms";
export const API_GET_SERVICE_PROGRAM_FEE = HOST_SALE + PRODUCT_SERVICE + APIERP + "servicevoucher/getfeeinsurance";
export const API_CREATE_SERVICE_VOUCHER = HOST_SALE + PRODUCT_SERVICE + APIERP + "servicevoucher/createservicevoucher";
export const API_SEARCH_SERVICEVOUCHER = HOST_SALE + PRODUCT_SERVICE + APIERP + "servicevoucher/searchservicevoucher";
export const API_DELETE_SERVICEVOUCHER = HOST_SALE + PRODUCT_SERVICE + APIERP + "servicevoucher/deleteservicevoucher";
export const API_COLLECTMONEY_SERVICEVOUCHER = HOST_SALE + PRODUCT_SERVICE + APIERP + "servicevoucher/servicevouchercollectmoney";
export const API_MODIFY_SERVICEVOUCHER = HOST_SALE + PRODUCT_SERVICE + APIERP + "servicevoucher/modifyservicevoucher";
export const API_SEARCH_OUTPUT_RECEIPT = HOST_SALE + PRODUCT_SERVICE + API + "report/searchoutputreceipt";
export const API_GET_SALE_ORDER_OUT_TRANSACTION = HOST_SALE + PRODUCT_SERVICE + APIERP + "serviceproducts/getsaleorderouttransaction";
export const API_CHECK_MONTHLY_PAYMENT_BROADCAST = HOST_SALE + PRODUCT_SERVICE + APIERP + 'installment/checkmonthlypaymentbroadcast';
export const API_GET_MAIN_AND_SUBEP_TO_UPDATE = HOST_SALE + PRODUCT_SERVICE + APIERP + "installment/getmainandsubeptoupdate";
export const API_GET_NAME_BRAND = HOST_SALE + PRODUCT_SERVICE + APIERP + "serviceproducts/getbrandairtime";
export const API_PRODUCT_BY_PHONE = HOST_SALE + PRODUCT_SERVICE + APIERP + "serviceproducts/getproductairtimebyphonenumber";
export const API_GET_SALE_ORDER_PAYMENT = HOST_SALE + PRODUCT_SERVICE + APIERP + "serviceproducts/GetSaleOrderAirTime";
export const API_CONFIRM_OTP = HOST_SALE + PRODUCT_SERVICE + APIERP + "serviceproducts/confirmotpairtime";
export const API_CREATE_TRANSACTION = HOST_SALE + PRODUCT_SERVICE + APIERP + "serviceproducts/createnewtransaction";
export const API_GET_MEDICINE_PACKAGE = HOST_SALE + PRODUCT_SERVICE + APIERP + ANKHANG + 'medicine/getqualitylist';
export const API_SEARCH_DISEASE = HOST_SALE + PRODUCT_SERVICE + APIERP + ANKHANG + 'medicine/searchdisease';
export const API_GET_PRODUCTS_BY_DISEASE_GROUP = HOST_SALE + PRODUCT_SERVICE + APIERP + ANKHANG + 'medicine/getproductbydiseasequalitygroup';
export const API_GET_REPLACED_MEDICINE = HOST_SALE + PRODUCT_SERVICE + APIERP + ANKHANG + 'medicine/getproductdiseasebyreplace';
export const API_GET_SALE_ID_INFO = HOST_SALE + PRODUCT_SERVICE + APIERP + "saleorder/getsaleidinfo";
export const API_GET_PRODUCT_BY_PRODUCTID = HOST_SALE + PRODUCT_SERVICE + APIERP + "getproductbyproductid";
export const API_INSERT_PACKAGING_BAG = HOST_SALE + PRODUCT_SERVICE + APIERP + "saleorder/insertpackagingsinfo";
export const API_CHECK_SHOW_PACKAGING_SCREEN = HOST_SALE + PRODUCT_SERVICE + APIERP + "saleorder/checkshowscreenpackagings";
export const API_SEARCH_BARCODE_PROMOTION = HOST_SALE + PRODUCT_SERVICE + APIERP + 'promotion/getlistproductofscanbarcodepromotion';
export const API_SEARCH_BARCODE_CART_PROMOTION_SALE_EXPRESS = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + "searchproductgift4promotionordersaleexpress";
export const API_GET_PROMOTION_BY_SALE_PROMOTION_EXPRESS = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + "getpromotionlistbysalepromotionproductexpress";
export const API_CHECK_CUSTOMER_PHONE_BY_PROMOTIONID = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + "checkcustomerphonebypromotionid";
export const API_GET_INFO_SO_INSNURANCE = HOST_SALE + PRODUCT_SERVICE + APIERP + "warrantyinsurance/getinfoso";
export const API_GET_BRAND_CUSTOMER = HOST_SALE + PRODUCT_SERVICE + APIERP + "warrantyinsurance/getbrandcustomer";
export const API_GET_LIST_WARRANTY_INSURANCE = HOST_SALE + PRODUCT_SERVICE + APIERP + "warrantyinsurance/getlistwarrantyinsurance";
export const GET_INSURANCE_INFO = HOST_SALE + PRODUCT_SERVICE + APIERP + "warrantyinsurance/getinsuranceinfo";
export const API_CHECK_BUY_INSURANCE = HOST_SALE + PRODUCT_SERVICE + APIERP + "warrantyinsurance/checkbuyinsurance";
export const API_GET_PROMOTION_INSURANCE = HOST_SALE + PRODUCT_SERVICE + APIERP + "warrantyinsurance/getpromotionins";
export const API_GET_APPLY_SELECTED_PROMOTION = HOST_SALE + PRODUCT_SERVICE + APIERP + "warrantyinsurance/applyselectedpromotion";
export const API_CHECK_PHONE_GIFT_VOUCHER_EXPRESS = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + "checkcreategiftvoucherexpress";
export const API_PARSE_PRODUCT_INFO_OBJECT = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + "promotionsaleparseproductinfo";
export const API_GET_LOYALTY_POINT = HOST_SALE + PRODUCT_SERVICE + APIERP + LOYALTY + "getparnerloyalty";
export const API_GET_INVITATIONEVENT = HOST_SALE + PRODUCT_SERVICE + APIERP + LOYALTY + "getinvitationevent";
export const API_GET_WOW_POINT = HOST_SALE + PRODUCT_SERVICE + APIERP + LOYALTY + "getpointwow";
export const API_GET_POINT_BY_PAYMENT = HOST_SALE + PRODUCT_SERVICE + APIERP + LOYALTY + "getpointbypayment";
export const API_GET_LOYALTY_TRANSACTION_INFO = HOST_SALE + PRODUCT_SERVICE + APIERP + LOYALTY + "getloyaltytransactioninfo";
export const API_GET_DETAIL_WARRANTY_INSURANCE = HOST_SALE + PRODUCT_SERVICE + APIERP + "warrantyinsurance/getdetailwarrantyinsurance";
export const API_CONFIRM_PREORDER_EVENT = HOST_SALE + PRODUCT_SERVICE + APIERP + LOYALTY + "confirminvitation";
export const API_GET_LIST_SO = HOST_SALE + PRODUCT_SERVICE + APIERP + "warrantyinsurance/getlistso";
export const API_CHECK_PRODUCT_RETURN = HOST_SALE + PRODUCT_SERVICE + APIERP + "warrantyinsurance/checkproductreturn";
export const API_QUERY_STATUS_WARRANTY_INSURANCE = HOST_SALE + PRODUCT_SERVICE + APIERP + "warrantyinsurance/querystatuswarrantyinsurance";
export const API_SEARCH_EX_PROGRAM = HOST_SALE + PRODUCT_SERVICE + APIERP + "productevaluation/searchexprogram";
export const API_GET_RETURN_PRODUCT_INFO_BY_IMEI = HOST_SALE + PRODUCT_SERVICE + APIERP + "productevaluation/getinfosoproductchange";
export const API_GET_RETURN_PRODUCT_STATES = HOST_SALE + PRODUCT_SERVICE + APIERP + "productevaluation/getexchangeproductstatelist";
export const API_SEARCH_EX_RECEIPT = HOST_SALE + PRODUCT_SERVICE + APIERP + "productevaluation/searchexchangereceipt";
export const API_DELETE_RECEIPT = HOST_SALE + PRODUCT_SERVICE + APIERP + "productevaluation/deleteexchangereceipt";
export const API_GET_EX_RECEIPT_DETAIL = HOST_SALE + PRODUCT_SERVICE + APIERP + "productevaluation/getexchangereceiptbyid";
export const API_PRINT_EX_RECEIPT = HOST_SALE + PRODUCT_SERVICE + APIERP + "productevaluation/printexchangereceipt";
export const API_GET_EXCHANGE_PRODUCT_INFO = HOST_SALE + PRODUCT_SERVICE + APIERP + "productevaluation/getinfoexchangeproduct";
export const API_CHECK_RETURN_PRODUCT_STATES = HOST_SALE + PRODUCT_SERVICE + APIERP + "productevaluation/checkstateofreturnproductinexchangeprogram";
export const API_INSERT_EXCHANGE_RECEIPT = HOST_SALE + PRODUCT_SERVICE + APIERP + "productevaluation/insertexchangereceipt";
export const API_CHECK_PRODUCT_PRE = HOST_SALE + PRODUCT_SERVICE + APIERP + PROMOTION + "checkgetpromotiontypeofpreorder";
export const API_UPDATE_COMBO = HOST_SALE + PRODUCT_SERVICE + APIERP + COMBO + "put";
export const API_SEARCH_COMBO = HOST_SALE + PRODUCT_SERVICE + APIERP + COMBO + "search";
export const API_GET_DETAIL_COMBO = HOST_SALE + PRODUCT_SERVICE + APIERP + COMBO + "getdetail";
export const API_DELETE_COMBO = HOST_SALE + PRODUCT_SERVICE + APIERP + COMBO + "delete";
export const API_SEEN_CUSTOMER_INFO = HOST_SALE + PRODUCT_SERVICE + APIERP + "crm/insertrequestlog";
export const API_GET_STORE_BY_CRM = HOST + PRODUCT_SERVICE + APIERP + "crmlogistic/recommendedinventorystorebycrm";
export const API_UPDATE_DELIVERYINFO_TO_CART = HOST + PRODUCT_SERVICE + APIERP + "saleorder/updatedeliveryinfotocart";
export const API_GET_SHIPPING_ROUTE = HOST + PRODUCT_SERVICE + APIERP + "stock/multishippingroute";
export const API_GET_PACKAGE_SERVICE = HOST + PRODUCT_SERVICE + APIERP + "salesstock/getpricepolicyprogram";
export const API_GET_INSTALLMENT_PACKAGE_SMART_POS = HOST + PRODUCT_SERVICE + APIERP + "getinstallmentpackage";
export const API_GET_IMEI_INFO_EXPRESSSALE = HOST_SALE + PRODUCT_SERVICE + APIERP + "search/getIMEIinfo";
export const API_LOCK_INSTOCK_CART = HOST_SALE + PRODUCT_SERVICE + APIERP + "inventory/instocklocking";
export const API_GET_PRESCRIPTION_INFO = HOST_SALE + PRODUCT_SERVICE + APIERP + "inventory/getnationalprescriptioninfo";
export const API_GET_DRUG_DETAIL = HOST_SALE + PRODUCT_SERVICE + APIERP + "inventory/getdrugreferencedetailproduct";
export const API_GET_REWARD_INSTALLMENT = HOST_SALE + "mwg-app-erp-reward-service/" + "api/rewardep/GetRewardPointsUser";
export const API_GET_PARTNER_INSTALLMENT_USER_CODE = HOST_SALE + PRODUCT_SERVICE + APIERP + "installment/getpartnerinstallmentusercode";
export const API_CHECK_LOYALTY = HOST_SALE + PRODUCT_SERVICE + APIERP + "event/pos-checkin";
export const API_GET_LIST_EVENT = HOST_SALE + PRODUCT_SERVICE + APIERP + "event/getlisteventbystoreid";
export const API_GET_PROFILE_CUSTOMER_INFO = HOST_SALE + PRODUCT_SERVICE + APIERP + "loyalty/checkcustomermenbership";
export const API_GET_MEDICINE = HOST_SALE + PRODUCT_SERVICE + APIERP + "inventory/getsubsribedproinfo";
export const API_PUSH_MEDICINE = HOST_SALE + PRODUCT_SERVICE + APIERP + "inventory/pushsubscribedorder";
export const API_GET_PROMOTION_RANDOM = HOST_SALE + PRODUCT_SERVICE + APIERP + 'express/promotion/cart/random';
export const API_GET_QUESTIONS = HOST_SALE + PRODUCT_SERVICE + APIERP + 'saleorder/getquestion';
export const API_GET_SEARCH_PRODUCT = HOST_SALE + PRODUCT_SERVICE + APIERP + 'priceofstore/searchpriceofstore';
export const API_UPDATE_PRICE_PRODUCT = HOST_SALE + PRODUCT_SERVICE + APIERP + 'priceofstore/insertpriceofstore';
export const API_APPLY_STOP = HOST_SALE + PRODUCT_SERVICE + APIERP + 'priceofstore/updatestoppriceofstore';
export const API_GET_DETAIL_PRODUCT = HOST_SALE + PRODUCT_SERVICE + APIERP + 'priceofstore/getpriceforadjust';
export const API_SEARCH_IMEI_PACKAGE = HOST_SALE + PRODUCT_SERVICE + APIERP + 'saleorder/searchsodetail';
export const API_CHECK_IMEI_PACKAGE = HOST_SALE + 'mwg-app-erp-service-package-service/api/servicepackage/workflow/servicepackage/getaddininfo';
export const API_SEARCH_WATER_FILTER_PACKAGE = HOST_SALE + PRODUCT_SERVICE + APIERP + 'product/getmainproductfromproductsuggestion';
export const API_GET_INFO_PRE_ORDER = HOST_SALE + PRODUCT_SERVICE + APIERP + 'salesstock/getinforpreorderform';
export const API_INSERT_INFO_PRE_CRM = HOST + PRODUCT_SERVICE + APIERP + "crmlogistic/insertinforcollectionformpreorder";
export const API_GET_PROGRAM_PRE_CRM = HOST + PRODUCT_SERVICE + APIERP + "crmlogistic/getcustomerprogram";
export const API_CHECK_CUSTOMER_LIMIT = HOST + PRODUCT_SERVICE + APIERP + "promotion/checkcustomerlimit";
export const API_PROMOTION_PRE = HOST_SALE + PRODUCT_SERVICE + APIERP + "product/getpreordpromotionofinforcollection";
export const API_GET_CONTRACT = HOST_SALE + PRODUCT_SERVICE + APIERP + "installment/getcontractinfoforcancel";
export const API_PROCESS_CANCEL = HOST_SALE + PRODUCT_SERVICE + APIERP + "installment/processCancel";
export const API_CONFIRM_CANCEL = HOST_SALE + PRODUCT_SERVICE + APIERP + "installment/processConfirmCancel";
export const API_GET_TICKET = HOST_SALE + PRODUCT_SERVICE + APIERP + "installment/actionTicketServiceRule";
export const API_CHECK_TICKET = HOST_SALE + PRODUCT_SERVICE + APIERP + "installment/actionTicketServiceRule";
export const API_SEARCH_TICKET_CUSTOMER_CARE = HOST_SALE + PRODUCT_SERVICE + APIERP + "ankhang/customercare/search";
export const API_CREATE_TICKET_CUSTOMER_CARE = HOST_SALE + PRODUCT_SERVICE + APIERP + "ankhang/customercare/createticket";
export const API_GET_SO_INFO_CUSTOMER_CARE = HOST_SALE + PRODUCT_SERVICE + APIERP + "saleorder/loadinfo";
export const API_UPDATE_RECEIVER_CUSTOMER_CARE = HOST_SALE + PRODUCT_SERVICE + APIERP + "ankhang/customercare/updatereceiver";
export const API_GET_DETAIL_CUSTOMER_CARE = HOST_SALE + PRODUCT_SERVICE + APIERP + "ankhang/customercare/loadinfocustomercare";
export const API_GET_DETAIL_SO_CUSTOMER_CARE = HOST_SALE + PRODUCT_SERVICE + APIERP + "ankhang/customercare/loadinfosocustomercare";
export const API_GET_UPDATE_TICKET_CUSTOMER_CARE = HOST_SALE + PRODUCT_SERVICE + APIERP + "ankhang/customercare/updateticket";
export const API_PARSE_PRODUCT_INFO_BO = HOST_SALE + PRODUCT_SERVICE + APIERP + "ankhang/product/castproductinfo";
export const API_GET_BANK_INFO = HOST_SALE + PRODUCT_SERVICE + APIERP + "report/getbankaccountinfo";
export const API_GET_TRANSACTION_TRANFER = HOST_SALE + PRODUCT_SERVICE + APIERP + 'banktransfer/getransactionbanktranfer';
export const API_GET_K_FACTOR = HOST_SALE + PRODUCT_SERVICE + APIERP + "ankhang/product/getrevenueconversionrate";
export const API_CHECK_NUMBER_PHONE_APPLY_VOUCHER = HOST_SALE + PRODUCT_SERVICE + APIERP + "crm/customerpurchased";
export const API_CHECK_SEND_OTP_GEN_QR_CODE = HOST_SALE + PRODUCT_SERVICE + APIERP + "checkSenOTPGenQRCode";
export const API_CHECK_NUMBER_PHONE_APPLY_MEDICINE = HOST_SALE + PRODUCT_SERVICE + APIERP + "customer/numberofslot";
export const API_CHECK_STATUS_APPLY_GIFT_AMOUNT = HOST_SALE + PRODUCT_SERVICE + APIERP + "saleorder/checkstatusapplygiftAmount";
export const API_SEARCH_CERTIFICATE = HOST_SALE + "mwg-app-erp-service-package-service/api/servicepackage/searchcertificateofcustomer";
export const API_ACTIVE_CERTIFICATE = HOST_SALE + "mwg-app-erp-service-package-service/api/servicepackage/triggertoactivatecert";
export const API_VALIDATE_IMEI_WITH_PARTNER = HOST_SALE + PRODUCT_SERVICE + APIERP + "checkValidImei";
export const API_CHECK_COUPON_EXPIRED_BY_PHONE = HOST_SALE + PRODUCT_SERVICE + APIERP + "payment/checkhascouponexpiried";
export const API_GET_PRICE_POLICY_PROGRAM = HOST_SALE + PRODUCT_SERVICE + APIERP + "salesstock/getpricepolicyprogramofsrvproducts";
export const API_GET_VOUCHER_CUSTOMER = HOST_SALE + PRODUCT_SERVICE + APIERP + "gift-wallet/getcustomergiftbybrand"
export const API_UPLOAD_FILE_AUDIO = HOST_SALE + PRODUCT_SERVICE + APIERP + "uploadFile"
export const API_GET_PROGRAM_ECO = HOST_SALE + "mwg-app-erp-web-output-service/api/exhchange-program/search"
export const API_GET_PRICE_NEAR_STORE = HOST_SALE + PRODUCT_SERVICE + APIERP + "saleorder/checknearstoreprice"



//-------Partner--------//
export const API_INSTALLMENT_VERIFYOTP = HOST_PARTNER + PRODUCT_SERVICE + APIERP + "installment/verifyotp";
export const API_INSTALLMENT_GETSTATUS = HOST_PARTNER + PRODUCT_SERVICE + APIERP + "installment/getstatusepostransaction";
export const API_INSTALLMENT_SUBMITAPPLICATION = HOST_PARTNER + PRODUCT_SERVICE + APIERP + "installment/submitapplication";
export const API_INSATLLMENT_UPDATEATTACHMENTTYPE = HOST_PARTNER + PRODUCT_SERVICE + APIERP + "installment/updateaddattachmentinfor";
export const API_INSATLLMENT_SENDACTTACHFILE = HOST_PARTNER + PRODUCT_SERVICE + APIERP + "installment/sendattachfiletotartner";
export const API_INSTALLMENT_GETPREPARECONTRACT = HOST_PARTNER + PRODUCT_SERVICE + APIERP + "installment/getpreparecontractnnfo";
export const API_INSTALLMENT_RESENDOTP = HOST_PARTNER + PRODUCT_SERVICE + APIERP + "installment/resendotp";
export const API_Delete_EPOSTransaction = HOST_PARTNER + PRODUCT_SERVICE + APIERP + "installment/deleteepostransaction";
export const API_INSATLLMENT_PRINTCONTRACT = HOST_PARTNER + PRODUCT_SERVICE + APIERP + "installment/checkurlcontract";
export const API_SEND_DATA_F88 = HOST_PARTNER + PRODUCT_SERVICE + API + "installment/senddatatof88";
export const API_QUERY_STATUS_TRANSACTION = HOST_PARTNER + PRODUCT_SERVICE + APIERP + "querystatuspaymenttransaction";
export const API_QUERY_STATUS_TRANSACTION_SC = HOST_PARTNER + PRODUCT_SERVICE + APIERP + "smartposquerytransaction";
export const API_GET_QR_PAYMENT = HOST_PARTNER + PRODUCT_SERVICE + APIERP + "getpaysmartqrcode";
export const API_GET_SC_PAYMENT = HOST_PARTNER + PRODUCT_SERVICE + APIERP + "smartposcreatetransaction";
export const API_DELETE_QR_TRANSACTION = HOST_PARTNER + PRODUCT_SERVICE + APIERP + "deletepaymenttransaction";
export const API_CHECK_DOCUMENT_INFORMATION = HOST_PARTNER + PRODUCT_SERVICE + APIERP + SIM + "checkdocumentinformation";
export const API_DELETE_QR_TRANSACTION_PARTNER = HOST_PARTNER + PRODUCT_SERVICE + APIERP + "unreservedvoucherpartner";
export const API_GET_QR_PAYMENT_PARTNER = HOST_PARTNER + PRODUCT_SERVICE + APIERP + "reservedvoucherpartner";
export const API_CHECK_INVENTORY_STATUS = HOST_SALE + PRODUCT_SERVICE + APIERP + 'product/checkinventorystatus';
export const API_UPDATE_STATUS_RANDOM_DISCOUNT_PROMOTION = HOST_SALE + PRODUCT_SERVICE + APIERP + 'randomdiscpromo/updatestatus';
export const API_VERIFIED_GIFT_VOUCHER_ISSUE = HOST_SALE + PRODUCT_SERVICE + APIERP + PAYMENT + "verifiedgiftvoucherissue";
export const API_SEARCH_PRODUCT_NEW = HOST_SALE + PRODUCT_SERVICE + APIERP + 'productevaluation/searchreturningproduct';
export const API_INSERT_LOG_PAYMENT = HOST_SALE + PRODUCT_SERVICE + APIERP + 'payment/insertmoneycardtranslog';
export const API_CHECK_SEND_OTP_PARTNER = HOST_SALE + PRODUCT_SERVICE + API + 'installment/checksendotp_samsungfinance';
export const API_SAVE_INFO_CUSTOMER = HOST_SALE + PRODUCT_SERVICE + API + 'installment/saveotp_samsungfinance';

// mwg-app-erp-search-service
export const API_SEARCH_PRODUCT = HOST_SEARCH + PRODUCT_SERVICE_SEARCH + APIERP + 'product/search';
export const API_SEARCH_PRODUCT_AN_KHANG = HOST_SEARCH + PRODUCT_SERVICE_SEARCH + API + 'web/product/search';
export const API_GET_CATEGORIES_FILTER = HOST_SEARCH + PRODUCT_SERVICE_SEARCH + API + 'web/product/getallcategories'
export const API_GET_PROP_BY_CATEGORY = HOST_SEARCH + PRODUCT_SERVICE_SEARCH + API + 'web/product/getproductpropbycategory'
export const API_GET_MENU = HOST_SEARCH + PRODUCT_SERVICE_SEARCH + API + 'web/product/getmenuchild'
export const API_GET_MANU_BY_CATEGORY = HOST_SEARCH + PRODUCT_SERVICE_SEARCH + API + 'web/product/getmanubycategoryid'
export const API_GET_PRODUCTS_BY_FILTER = HOST_SEARCH + PRODUCT_SERVICE_SEARCH + API + 'web/product/getproductfilter'


// mwg-app-erp-product-service
export const API_SEARCHDATA_COD_RQCHANGE = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "cod/searchdatabillcod";
export const API_INSERT_RQCHANGE = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "cod/insertrqchange";
export const API_SEARCHDATA_COD_REVEIW = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "cod/searchreviewcode";
export const API_REVIEW_AND_DELETE_RQCHANGE = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "cod/deleteorreview";
export const API_DELETE_INVENTORY_BY_ID = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "inventory/delete-diff-stock";
// export const API_SEARCH_INSTALLMENT = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "installment/getepostransactionlist";
export const API_SEARCHUSER = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "user/get";
export const API_MANA_PRODUCT = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "inventory-area/search"
export const API_GET_INFORMATION_PRODUCT = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "inventory/search";
export const API_GET_GROUP_PRODUCT_IV = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "product/getsuggest";
export const API_GET_PRODUCT_BY_KEYWORD_IV = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "product/get";
export const API_GET_ALL_INFO_BY_KEYWORD = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "product/get-all-for-inv";
export const API_INVENTORY_DEFAULT = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API_INVENTORY
export const API_GET_ALL_LOCATION = API_INVENTORY_DEFAULT + "search"
export const API_ADD_LOCATION = API_INVENTORY_DEFAULT + "add"
export const API_REMOVE_LOCATION = API_INVENTORY_DEFAULT + 'delete'
export const API_CHECK_LOCATION = API_INVENTORY_DEFAULT + 'count-in-term'
export const API_APPLY_LOCATION = API_INVENTORY_DEFAULT + 'apply'
export const API_LOAD_INVENTORY = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + API_TERM_INVENTORY + "load"
export const API_LOCK_PRODUCT_TO_USER = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "inventory-lock/add";
export const API_UPD_LOCK_PRODUCT_TO_USER = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "inventory-lock/upd";
export const API_CHECK_PRODUCTS_BY_PRODUCTIDS = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "product/checkexits";
export const API_GET_IMEIS_BY_PRODUCTID = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "inventory/search-imei";
export const API_GET_STATUS_INVENTORY = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "inventory-status/search";
export const API_SAVE_IMEIS = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "inventory/save-imei";
export const API_GET_TERM_AREA = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "inventory-term-area/search";
export const API_SAVE_PRODUCT_WITHOUT_IMEI = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + 'inventory/save'
export const API_SAVE_PRODUCT_WITHOUT_IMEI_V2 = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + 'inventory/save-ver-2'
export const API_CHECK_PRODUCT_WITHOUT_IMEI = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + 'inventory/search-stock'
export const API_LOAD_PROTEMP = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + 'inventory/search'
export const API_LOAD_PRODUCT_ITEM = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + 'product/getbyitem'
export const API_LOAD_PRODUCT_DETAIL_ITEM = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + 'inventory-detail-item/get-by-inventoryids'
export const API_GET_SUBGROUP = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "inventoryterm/getsubgroup";
export const API_CHECKEXISTS_AREA = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "inventory-term-store/search";
export const API_GET_REPORT_PRODUCT_WITHOUT_IMEI = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + 'inventory/search-diff-stock'
export const API_GET_REPORT_PRODUCT_WITH_IMEI = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + 'inventory/search-diff-stock-imei'
export const API_GET_LIST_PRODUCT_WITH_IMEI_FINISH = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + 'inventory/search-product-process-imei'
export const API_DELETE_INVENTORY = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + 'inventory/delete'
export const API_PRODUCT_PROCESS = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + 'inventory/search-product-process'
export const API_PRODUCT_PROCESS_IMEI = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + 'inventory/search-product-process-imei'
export const API_CHECK_PERMISSION = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + 'user/permission'
export const API_GET_PERMISSION = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + 'user/allpermission'
export const API_CHECK_IMEI = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "inventory/check-imei";
export const API_GET_LIST_PRODUCT_ARREARS = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "inventory-debt-process/search-product";
export const API_GET_INVENTORY_TERM_LOAD = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "inventoryterm/load";
export const API_GET_LIST_ARREARS = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "inventory-debt-process/search";
export const API_SAVE_STAFF_ARREARS = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "inventory-debt-process/user-save";
export const API_GET_LIST_STAFF_ARREARS = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "inventory-debt-process/user-search";
export const API_USER_CHECK = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "inventory-debt-process/user-check";
export const API_GET_LIST_DETAILS_ARREARS = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "inventory-debt-process/details";
export const API_GET_LIST_ORDER_TYPE = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "ordertype/get";
export const API_CHECK_IN_USER = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "game/checkinuserOPPO";
export const API_CHECK_REWARD = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "game/checkrewardOPPO";
export const API_UPDATE_REWARD = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "game/updaterewardOPPO";
export const API_CLOSE_REWARD = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "game/closerewardOPPO";
export const API_REPRINT_OUTPUT_RECEIPT = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "serviceproducts/reprintoutreceipt";
export const API_GET_BYID = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + 'inventory/getbyid';
export const API_REPRINT_SALEORDER_NEW = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "serviceproducts/reprintoutreceipt"
export const API_DELETE_INVENTORIES = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "inventory/delete-inventories";
export const API_GET_BASE64_INVENTORY = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "inventory-debt-process/report";
export const API_GET_IMAGE_INVENTORY_DEBT = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "inventory-debt-process/attachment";
export const API_GET_LIST_REPORT_PRODUCT = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "product/get-for-real-status";
export const API_GET_LIST_REPORT_PRODUCT_STATUS = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "real-product-status/get-real-product-inventory-status";
export const API_GET_LIST_REPORT_STATE = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "real-product-status/get-real-product-status-type";
export const API_SAVE_REPORT_PRODUCT_STATE = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "real-product-status/save";
export const API_GET_LIST_REPORT_PRODUCT_STATE_REQUEST = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "real-product-status/view";
export const API_SEARCH_PRODUCT_BY_INVENTORY_PROCESS_CODE = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "product/get-by-inventoryprocesscode";
export const API_DELETE_REPORT_PRODUCT_STATE_REQUEST = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "real-product-status/delete-real-product-status-detail";
export const API_GET_LIST_ERROR_REASON = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "product/get-cause-error";
export const API_GET_BATCH_BY_EXPIRATION_DATE = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "product/get-batch-by-exp";
export const API_CHECK_PRODUCT_STOCK = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "product/get-stock";
export const API_CHECK_PRODUCT_STOCK_IMEI = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "product/get-stock-imei";
export const API_SAVE_REPORT_PRODUCT_STATE_NO_IMEI = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "real-product-status/save";
export const API_SAVE_REPORT_PRODUCT_STATE_HAS_IMEI = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "real-product-status/save-imei";
export const API_GET_REPORT_REQUEST_DETAIL = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "real-product-status/view-detail";
export const API_GET_PRODUCT_IMAGE = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "product/get-image";
export const API_FINISH_REPORT_PRODUCT_STATE_REQUEST = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "real-product-status/update-finish-status";
export const API_GET_LIST_PROCESSS_RP = HOST_INVENTORY + PRODUCT_ERP_SERVICE + API + "inventory-debt-process/get-inventory-process-rp";
// mwg-app-media-service
export const API_UPLOAD_IMAGE_CDN = HOST + MEDIA_SERVICE + API + MEDIA + "upload";
export const API_GET_IMAGE_CDN = HOST + MEDIA_SERVICE + API + MEDIA + "file/";
export const API_UPLOAD_OLD_IMAGE_CDN = HOST + MEDIA_SERVICE + API + MEDIA + "used/upload";
export const API_GET_IMAGE_INVENTORY = "https://weberp.thegioididong.com:8080/download/";
export const API_UPLOAD_IMAGE_CDN_NEW = HOST + MEDIA_SERVICE + API + MEDIA + "upload";
export const API_GET_IMAGE_CDN_NEW = HOST + MEDIA_SERVICE + API + MEDIA + "file/";
export const API_UPLOAD_OLD_IMAGE_CDN_NEW = HOST + "mwg-app-minio-media-pos-service/api/publicposimageused/multiupload";
export const API_GET_UPLOAD_OLD_IMAGE_CDN_NEW = "https://cdnv2.tgdd.vn/posimageused/";
// F88
export const API_GET_TOKEN_PORTAL_F88 = HOST + AUTH_SERVICE + API + 'auth/f88/login';
export const PORTAL_AGENT_F88 = F88_HOST + "kiem-tra-tai-khoan";
// mwg-app-product-offline-service
export const API_PRINT_BIT_INVOICE = FIREWALL_HOST + "epsonapi/v1/?action=Print_Invoice";
export const API_PRINT_BASE64_A4 = FIREWALL_HOST + "epsonapi/v1/?action=Print_A4";
export const API_GET_PRINTER_FW = FIREWALL_HOST + "epsonapi/v1/?action=Get_Printer";
export const API_CHECK_ACTIVE_OFFLINE = OFFLINE_HOST + SYNC_USER_SERVICE + API + AUTH_OFFLINE + 'getstatus';
export const API_GET_SALERECEIPT_OFFLINE = OFFLINE_HOST + PRODUCT_SERVICE_OFFLINE + API + 'salereceipt/search';
export const API_UPDATE_SALERECEIPT_OFFLINE = OFFLINE_HOST + PRODUCT_SERVICE_OFFLINE + API + 'salereceipt/update';
export const API_CREATE_SALERECEIPT_OFFLINE = OFFLINE_HOST + PRODUCT_SERVICE_OFFLINE + API + 'salereceipt/create';
export const API_GET_PROMOTION_OFFLINE = OFFLINE_HOST + PRODUCT_SERVICE_OFFLINE + API + 'promotionoffline/get';
export const API_CHECK_IMEI_INSTOCK_OFFLINE = OFFLINE_HOST + PRODUCT_SERVICE_OFFLINE + API + 'instockdetail/get';
export const API_GET_PRICE_PRODUCT_OFFLINE = OFFLINE_HOST + PRODUCT_SERVICE_OFFLINE + API + 'priceofproduct/get';
export const API_GET_PRODUCT_INFO_OFFLINE = OFFLINE_HOST + PRODUCT_SERVICE_OFFLINE + API + 'pmproduct/get';
export const API_GEN_SALERECEIPT_HTML = FIREWALL_HOST + "PosOfflineAPI/v1/?action=DL_HTML";
export const API_AUTHEN_FW_OFFLINE = FIREWALL_HOST + "PosOfflineAPI/v1/?action=Auth_Off";
// PrinterService
export const API_REQUEST_PRINT_PDF_FROMFILEURL = LED_HOST + "PrinterService/PrinterSvc.asmx/PrintPDFFromFileUrl";
export const API_REQUEST_PRINT_PDF = LED_HOST + "PrinterService/PrinterSvc.asmx/PrintPDF";
export const API_REQUEST_PRINT_PDF_NEW = LED_HOST + "PrinterService/PrinterSvc.asmx/PrintNewPDF";
export const API_REQUEST_PRINT_PDF_BCH = LED_HOST + "PrinterService/PrinterSvc.asmx/PrintBCH";
// mwg-app-loyalty-product-service
export const API_GET_SEC_INFO = HOST + LOYALTY_SERVICE + API + "loyalty/getInfoCheque";
export const API_GET_MPOS_TOKEN = MPOS_HOST + MPOS_SERVICE + API + XPOS_TOKEN + "create";
export const API_CHECK_IDENTIFIED = HOST + LOYALTY_ERP_SERVICE + API + "invitation/RegisterInternalUserInvitation";
export const API_GET_DATA_HISTORY = HOST + LOYALTY_ERP_SERVICE + API + "reward/RewardReportPointEmployee";
// mwg-app-product-promotion-service
export const API_PROMOTION_NEW = HOST + PROMOTION_SERVICE + API + PROMOTION_ERP + "getlistbyproduct";
export const API_PROMOTION_DELIVERYTYPE_NEW = HOST + PROMOTION_SERVICE + API + PROMOTION_ERP + "getlistbydeliverytype";
export const SALE_ORDER_CART_PROMOTION_NEW = HOST + PROMOTION_SERVICE + API + PROMOTION_ERP + "getlistbycart";
export const API_CHECK_CUSTOMER_PHONE_PROMOTION_NEW = HOST + PROMOTION_SERVICE + API + PROMOTION_ERP + 'checkcustomerphone';
export const API_GET_PROMOTION_NEW = HOST + PROMOTION_SERVICE + API + PROMOTION_ERP + 'getsaleexpresspromotion';
export const API_CHECK_BARCODE_PROMOTION_NEW = HOST + PROMOTION_SERVICE + API + PROMOTION_ERP + 'barcode';
export const API_GET_PROMOTION_BY_SALE_PROMOTION_EXPRESS_NEW = HOST + PROMOTION_SERVICE + API + PROMOTION_ERP + "getpromotionlistbysalepromotionproductexpress";
export const API_GET_PROMOTION_SALE_ES_NEW = HOST + PROMOTION_SERVICE + API + PROMOTION_ERP + 'getpromotionlistexpress';
export const API_SEARCH_BARCODE_PROMOTION_NEW = HOST + PROMOTION_SERVICE + API + PROMOTION_ERP + 'getlistproductofscanbarcodepromotion';
export const API_PROMOTION_LOSTSALE_NEW = HOST + PROMOTION_SERVICE + API + PROMOTION_ERP + 'getlistbylostsale';
export const API_GET_SUGGUEST_PROMOTION = HOST + PROMOTION_SERVICE + API + PROMOTION_ERP + 'getpromotionlist';
export const API_GET_SUGGUEST_EXPRESS_PROMOTION = HOST + PROMOTION_SERVICE + API + PROMOTION_ERP + 'getpromotionlistofsaleexpresspromotion';
export const API_GET_PROMOTION_CSSALE_NEW = HOST_SALE + PROMOTION_SERVICE + API + PROMOTION_ERP + 'getpromotionlistofpromotionlistgroupid';
export const API_GET_PROMOTION_CSSALE_FOR_OLD_TOOL_NEW = HOST_SALE + PROMOTION_SERVICE + API + PROMOTION_ERP + 'getpromotionlistbylistgroupid';
export const API_SEARCH_BARCODE_CART_PROMOTION_NEW = HOST_SALE + PROMOTION_SERVICE + API + PROMOTION_ERP + "searchproductgift4promotionorder";
export const API_SEARCH_BARCODE_CART_PROMOTION_SALE_EXPRESS_NEW = HOST_SALE + PROMOTION_SERVICE + API + PROMOTION_ERP + "searchproductgift4promotionordersaleexpress";
export const API_GET_CART_PROMOTION_NEW = HOST_SALE + PROMOTION_SERVICE + API + PROMOTION_ERP + 'getlistsaleexpressbycart';
// mwg-app-display-service
export const API_GET_LOCATION_BY_PRODUCT = HOST_INVENTORY + PLANOGRAM_SERVICE + API + "product/search-position-product";
export const API_GET_LIST_SHELF_GROUP_NOT_COMPLETE = HOST_INVENTORY + PLANOGRAM_SERVICE + API + "shelf-group/get-not-completed";
export const API_GET_LIST_PRODUCT_TRAY_NOT_COMPLETE = HOST_INVENTORY + PLANOGRAM_SERVICE + API + "product/get-not-completed";
export const API_GET_CONFIRM_PRODUCT_COMPLETE = HOST_INVENTORY + PLANOGRAM_SERVICE + API + "product/confirm-on-tray";
export const API_GET_LIST_SHELF_GROUP = HOST_INVENTORY + PLANOGRAM_SERVICE + API + "shelf-group/get-by-store";
export const API_GET_LIST_TRAY_BY_SHELFGROUPID = HOST_INVENTORY + PLANOGRAM_SERVICE + API + "tray/get";
export const API_GET_LIST_PRODUCT_BY_LOCATION = HOST_INVENTORY + PLANOGRAM_SERVICE + API + "product/get-by-location";
export const API_GET_NOTIFY_PRODUCTS = HOST_INVENTORY + PRODUCT_DISPLAY_SERVICE + API + "show-quota-notify-product/get-notify-products";
export const API_UPDATE_REVIEWED_NOTIFY_PRODUCT = HOST_INVENTORY + PRODUCT_DISPLAY_SERVICE + API + "show-quota-notify-product/update-reviewed-notify-products";
export const API_GET_PUTON_SHELF = HOST_INVENTORY + PLANOGRAM_SERVICE + API + "shelf-group/get-by-auto-put";
export const API_GET_PUTON_PRODUCT = HOST_INVENTORY + PLANOGRAM_SERVICE + API + "product/get-by-auto-put";
export const CALCULATION_CHANGE_DISPLAY_PRODUCT = HOST_INVENTORY + PLANOGRAM_SERVICE + API + "calculation-product/calculation-planogram-product";
export const API_GET_INFO_CROD_SHELF_GROUP = HOST_INVENTORY + PLANOGRAM_SERVICE + API + "shelf-group/get-inter-shelf";
export const API_PRINT_BY_TRAY = HOST_INVENTORY + PRODUCT_DISPLAY_SERVICE + API + "/print/by-tray";
export const API_PRINT_BY_SHELF_GROUP = HOST_INVENTORY + PRODUCT_DISPLAY_SERVICE + API + "/print/by-shelfgroup";
export const API_PRINT_BY_SHELF_GROUP_NEW = HOST_INVENTORY + PLANOGRAM_SERVICE + API + "/print/shelfgroup";
export const API_PUTON_PRODUCT_BY_HAND = HOST_INVENTORY + PLANOGRAM_SERVICE + API + "calculation-product/put-product-by-hand";
export const CAL_PUTON_PRODUCT = HOST_INVENTORY + PLANOGRAM_SERVICE + API + "show-puton-shelf/cal-puton-product";
export const API_DISPLAY_GET_LIST_MAINGROUP = HOST_INVENTORY + PRODUCT_DISPLAY_SERVICE + API + "pull-product/get-main-group";
export const API_DISPLAY_GET_LIST_SUBGROUP = HOST_INVENTORY + PRODUCT_DISPLAY_SERVICE + API + "pull-product/get-sub-group";
export const API_GET_LIST_PRODUCT_DRAG = HOST_INVENTORY + PRODUCT_DISPLAY_SERVICE + API + "pull-product/search";
export const API_CREATE_PULL_PRODUCT_REQUEST = HOST_INVENTORY + PRODUCT_DISPLAY_SERVICE + API + "pull-product/create-request";
export const LOAD_INFO_PRODUCT_IN_STOCK = HOST_INVENTORY + PRODUCT_DISPLAY_SERVICE + API + "show-product/load-info-product-in-stock";
export const GETLISTINVENTORYSTATUS = HOST_INVENTORY + PRODUCT_DISPLAY_SERVICE + API + "eprice-report/get-list-inventory-status";
export const GETPRINTTEMPLATE = HOST_INVENTORY + PRODUCT_DISPLAY_SERVICE + API + "eprice-report/get-print-template";
export const GETPRODUCT = HOST_INVENTORY + PRODUCT_DISPLAY_SERVICE + API + "eprice-report/get-product";
export const PRICELIST = HOST_INVENTORY + PRODUCT_DISPLAY_SERVICE + API + "print/price-list";
export const GETLISTMAINGROUP = HOST_INVENTORY + PRODUCT_DISPLAY_SERVICE + API + "eprice-report/get-list-main-group";
export const GETLISTSUBGROUPBYMAINGROUP = HOST_INVENTORY + PRODUCT_DISPLAY_SERVICE + API + "eprice-report/get-list-sub-group-by-main-group";
export const GETDISPLAYPOSITION = HOST_INVENTORY + PRODUCT_DISPLAY_SERVICE + API + "eprice-report/get-display-position";
export const GETPRODUCTCHANGE = HOST_INVENTORY + PRODUCT_DISPLAY_SERVICE + API + "eprice-report/get-product-change";
export const GETPRODUCTBYSHELFGROUP = HOST_INVENTORY + PRODUCT_DISPLAY_SERVICE + API + "eprice-report/get-eprice-product-by-shelfgroup";
export const SEARCH_NEW_SALE_STOCK = HOST_INVENTORY + PRODUCT_SERVICE + APIERP + "salesstock/searchnew";
export const API_CREATE_RESTOCK_REQUEST = HOST_INVENTORY + PRODUCT_SERVICE + APIERP + "requestnewproduct/add";
export const API_UPDATE_RESTOCK_REQUEST = HOST_INVENTORY + PRODUCT_SERVICE + APIERP + "requestnewproduct/update";
export const GET_INFOR_BUYER_BY_PRODUCT_ID = HOST_INVENTORY + PRODUCT_SERVICE + APIERP + "requestnewproduct/getinfobuyerbyproductid"
export const API_GET_SIM_BRAND_LIST = HOST_INVENTORY + PRODUCT_DISPLAY_SERVICE + API + "esim-report/get-sim-brand";
export const API_GET_SIM_PACKAGE_TYPE = HOST_INVENTORY + PRODUCT_DISPLAY_SERVICE + API + "esim-report/get-sim-package-type";
export const API_GET_SIM_GROUP = HOST_INVENTORY + PRODUCT_DISPLAY_SERVICE + API + "esim-report/get-sim-group";
export const API_CHECK_PACKAGES_TYPE = HOST_INVENTORY + PRODUCT_DISPLAY_SERVICE + API + "esim-report/check-packages-type";
export const API_SEARCH_SIM1 = HOST_INVENTORY + PRODUCT_DISPLAY_SERVICE + API + "esim-report/search";
export const API_SEARCH_SIM_A4 = HOST_INVENTORY + PRODUCT_DISPLAY_SERVICE + API + "esim-report/search-sim-a4";
export const API_GET_PRINT_SIM_DATA = HOST_INVENTORY + PRODUCT_DISPLAY_SERVICE + API + "esim-report/price-sim-list";
//mwg-app-erp-airtime-service
//-------Collection------//
export const API_ADD_TO_SHOPPING_CART_05 = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + CART + "promotion";
export const API_ADD_TO_SALE_ORDER_CART_05 = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + CART + "saleorder";
export const API_GET_BANK_ACCOUNT_LIST = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "paybill/getbankaccountlist";
export const API_QUERY_CUSTOMER_BANK_ACCOUNT = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "paybill/querycustomerbankacc";
export const API_GET_FEE_CASHIN = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "paybill/getfeecashin";
export const API_INSERT_AND_CREATE_TICKET = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "paybill/insertandcreateticket";
export const API_CREATE_TICKET = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "paybill/createticket";
export const API_CHECK_STATUS_TICKET = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "paybill/checkstatusticket";
export const API_GET_DATA_COLLECTION_MANAGER = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/getdataservicerequest";
export const API_GET_DATA_PAY_BILL = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "paybill/getdatapaybill";
export const API_CREATE_TRANSACTION_PARTNER = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "paybill/createtransactionpartner";
export const API_GET_DATA_ATTACH = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "paybill/getdataattach";
export const API_QUERYTRANSACTION_PARTNER = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "paybill/querytransactionpartner";
export const API_GET_DATA_CUSTOMER_QTV = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "paybill/getdatacustomerqtv";
export const API_QUERY_SERVICE_REQUEST = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/querystatusservicerequest";
//-------Insurance------//
export const API_GET_PROPERTY_SERVICE_REQUEST = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/getpropertyservicerequest";
export const API_GET_WEBVIEW_AND_CREATE_SALEORDER_INSURANCE = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/createservicerequest";
export const API_GET_DATA_INFO = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/getdataaddinfo";
export const API_SEARCH_DATA_INSURANCE = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/searchdataprofile";
export const API_GET_SEARCH_SERVICE_REQUEST = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/getdataservicerequest";
export const API_PARTNER_QUERY_STATUS = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/querystatusservicerequest";
export const API_GET_PRICE_SERVICE = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/getpriceservice";
export const API_GET_CREATE_SERVICE_REQUEST = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/createservicerequest";
export const API_GET_CASH_RETURN_DETAIL = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/getcashreturndetail";
//-------Catalog--------//
export const API_GET_CATALOG_LIST = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/getcataloglist";
export const API_GET_SERVICE_GROUP_LIST_CATALOG = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/getservicegrouplist";
//------Recharge Data----//
export const API_VALIDATE_DATA_SERVICE_REQUEST = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/validatedataservicerequest";
//-------Brightside-----//
export const API_GET_SERVICE_LIST = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/getservicelist";
export const API_GET_PROMOTION_SERVICE = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/getpromotionservice";
export const API_GET_INFO_REFUND = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/getinforefund";
export const API_CREATE_AIRTIME_REFUND = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/createairtimerqrefund";
export const API_CHECK_STATUS_TICKET_SERVICE = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/checkstatusticketservice";
export const API_GET_SERVICE_LIST_HISTORY_REFUND = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/searchrequestrefund";
export const API_GET_PROCESSOUT_VOUCHER = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/processoutvoucher";
export const API_GET_CANCEL_AND_CREATE_AIRTIME = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/querystatusrefundtransaction";
export const API_GET_QUERY_STATUS = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/partnerquerystatus";
export const API_CREATE_TICKET_SERVICE = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/createticketservice";
export const API_GET_DATA_COLLECTION_MANAGER_NEW = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/getdataservicerequestnew";
export const API_VALIDATE_IMEI = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/validateeditimei";
export const API_CREATE_AIRTIME_EDIT_REQUEST = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/createairtimeeditrequest";
export const API_QUERY_STATUS_EDIT_TRANSACTION = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/querystatusedittransaction";
export const API_SEARCH_AIRTIME_EDIT_REQUEST = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/searchairtimeeditrequest";
//------Tan Tam Collection-----//
export const API_GET_DATA_SO_CONSCIEN_TIOUS = HOST_SALE + 'mwg-app-tms-pos-service/' + API + "sale-order/wf/create";
export const API_JOB_CARD_AIRTIME_SERVICE = HOST_SALE + "mwg-app-tms-pos-service/" + API + "job-card/search";
export const API_CREATE_OUTPUT_VOUCHER_CONSCIEN_TIOUS = HOST_SALE + "mwg-app-tms-pos-service/" + API + "voucher/wf/create";
// mwg-app-erp-customer-profile-service
export const API_GET_CUSTOMER_PROFILE = HOST_SALE + PROFILE_SERVICE + API + "profile/get";
export const API_UPDATE_CUSTOMER_RECEIVE = HOST_SALE + PROFILE_SERVICE + API + "profile/update";
export const API_INSERT_CUSTOMER_RECEIVE = HOST_SALE + PROFILE_SERVICE + API + "profile/insert";
export const API_MODIFY_CUSTOMER_PROFILE = HOST_SALE + PROFILE_SERVICE + API + "profile/modify";
export const API_GET_PROFILE_SO = HOST_SALE + PROFILE_SERVICE + API + "profile/getById";
//-------Electricity--------//
export const API_GET_ELECTRICITY_PRODUCT_LIST = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/getproductlist";
export const API_GET_ELECTRICITY_PRICE_AND_FEE = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/getpriceandfeeservice";
export const API_CREATE_SERVICE_REQUEST = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/createservicerequest";
export const API_CHECK_PROMOTION_ELECTRICITY = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/checktransactionlockedbycqrs";
//-------Topup Data----//
export const API_GET_PRODUCT_LIST = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/getservicelist";
export const API_GET_PRODUCT_LIST_PACK_OF_DATA = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/getproductlist";
export const API_GET_PROCESS_SERVICE_REQUEST = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/processservicerequest";
//-------InsuranceExtendedWarranty-----//
export const API_GET_SEARCH_SO = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/validatedataservicerequest";
export const API_GET_DETAIL_SO = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/validatedataservicerequest";
export const API_BUY_INSURANCE = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/validatedataservicerequest";
export const API_SEARCH_REQUEST_RETURN = HOST_SALE + EXCHANGE_RETURN_SERVICE + "erpapp/" + "searcherr";
export const API_LOAD_INFO_REQUEST_RETURN = HOST_SALE + EXCHANGE_RETURN_SERVICE + "erpapp/" + "getexchreturnrequestinfo";
export const API_UPDATE_REQUEST_RETURN = HOST_SALE + EXCHANGE_RETURN_SERVICE + "erpapp/" + "updateadjustfee";
//-------Health Insurance--------//
export const API_GET_INPUT_FORM_TEMPLATE = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/getlistinformationtobecollected";
export const API_GET_HEALTH_INSURANCE_FEE = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/getpriceservice";
export const API_GET_TRANSACTION_DETAIL = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/gettransactiondetail";
export const API_GET_PRINT_SERVICE_REPORT = HOST_AIRTIME + PRODUCT_SERVICE + APIERP + REPORT + "printservicereport";
//------Installment flow new ------//
export const API_GET_EPOSTRANSECTION_NEW = HOST_SALE + PRODUCT_SERVICE + APIERP + "installment/getepostransinfo";
export const API_GET_UPDATEEPOSTRANSECTION_NEW = HOST_SALE + PRODUCT_SERVICE + APIERP + "installment/updateepostrans";
export const API_GET_BROADCAST_INFORMATION_NEW = HOST_SALE + PRODUCT_SERVICE + APIERP + "installment/getbroadcastepostransinfo";
export const API_UPDATE_EPOS_BROADCAST_NEW = HOST_SALE + PRODUCT_SERVICE + APIERP + "installment/updatebroadcastepostrans";
//------Paymenttransaction--------//
export const API_SEARCH_PAYMENT_AND_TRANSACTION = HOST_SALE + PRODUCT_SERVICE + APIERP + "accounting/get-paymentbanktransfer";
export const API_CREATE_PAYMENT_TRANSACTION = HOST_SALE + PRODUCT_SERVICE + APIERP + "accounting/create-invoucher-paymentbanktransfer";
export const API_VALIDATE_EDIT_DATA = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/validateeditdata";
export const API_DELETE_REFUND = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/deleterefund";
//------BankAirtimeService------//
export const API_PROCESS_SERVICE_REQUEST = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/processservicerequest";
export const API_GET_PRICE_AND_FEE_SERVICE = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/getpriceandfeeservice";
export const API_PREPARSE_CREATE_REQUEST = HOST_AIRTIME + AIRTIME_SERVICE + APIERP + "servicerequest/preparecreateservierequest";
//------Note log deeplink-----//
export const API_INSERT_LOG_DEEPLINK = HOST_AIRTIME + PRODUCT_SERVICE + APIERP + "sim/insertlogdeeplink";
//------SendBankInternal--------//
export const API_SEARCH_LIST_SENDBANK = HOST_SALE + PRODUCT_SERVICE + APIERP + SENDBANK + 'search';
export const API_CHANGE_APPROVE_USER_SENDBANK = HOST_SALE + PRODUCT_SERVICE + APIERP + SENDBANK + 'changeapproverequser';
export const API_CHECK_PERMISSION_SENDBANK = HOST_SALE + PRODUCT_SERVICE + APIERP + SENDBANK + 'checkpermission';
export const API_GET_CANCEL_REASON = HOST_SALE + PRODUCT_SERVICE + APIERP + SENDBANK + 'getcancelreason';
export const API_GET_APPROVAL_STATUS = HOST_SALE + PRODUCT_SERVICE + APIERP + SENDBANK + 'getapprovestatus';
export const API_GET_PAYMENT_DETAILS = HOST_SALE + PRODUCT_SERVICE + APIERP + SENDBANK + "getpaymentdetail";
export const API_GET_USER = HOST_SALE + PRODUCT_SERVICE + APIERP + SENDBANK + "getuser";
export const API_GET_FILE_ATTACH = HOST_SALE + PRODUCT_SERVICE + APIERP + SENDBANK + "getfileattach";
export const API_APPROVE_REQ_MPOS = HOST_SALE + PRODUCT_SERVICE + APIERP + SENDBANK + "approvereqmpos";
export const API_CANCEL_APPROVE_REQ = HOST_SALE + PRODUCT_SERVICE + APIERP + SENDBANK + "cancelapprovereq";
export const API_GET_LIST_BANK = HOST_SALE + PRODUCT_SERVICE + APIERP + SENDBANK + "getbank";
//Deposit from
export const API_GET_BANK_CREARTE_FORM = HOST_SALE + PRODUCT_SERVICE + APIERP + SENDBANK + 'getbankcreateform'
export const API_GET_BANK_SEARCH_FORM = HOST_SALE + PRODUCT_SERVICE + APIERP + SENDBANK + 'getbanksearchform'
export const API_SEARCH_SENDBANK = HOST_SALE + PRODUCT_SERVICE + APIERP + SENDBANK + 'searchsendbank'
export const API_GET_DAILYFUND = HOST_SALE + PRODUCT_SERVICE + APIERP + SENDBANK + 'getdailyfund'
export const API_CREATE_SENDBANK = HOST_SALE + PRODUCT_SERVICE + APIERP + SENDBANK + 'createsendbank'
export const API_UPDATE_SENDBANK = HOST_SALE + PRODUCT_SERVICE + APIERP + SENDBANK + 'updatesendbank'
export const API_DELETE_SENDBANK = HOST_SALE + PRODUCT_SERVICE + APIERP + SENDBANK + 'deletesendbank'
export const API_CREATE_SENDBANK_APPROVE_REQ = HOST_SALE + PRODUCT_SERVICE + APIERP + SENDBANK + 'createsendbankapprove'
export const API_GET_DETAIL_SENDBANK = HOST_SALE + PRODUCT_SERVICE + APIERP + SENDBANK + 'getdetailsendbank'
export const API_VALIDATE_POPUP_SENDBANK_REQ = HOST_SALE + PRODUCT_SERVICE + APIERP + SENDBANK + 'validatepopupsendbankreq'
export const API_PRINT_SENDBANK = HOST_SALE + PRODUCT_SERVICE + APIERP + SENDBANK + "printsendbank";
export const API_CHECK_PRINT_SEND_BANK = HOST_SALE + PRODUCT_SERVICE + APIERP + SENDBANK + "checkprintsendbank";
// MAP
export const API_SEARCH_ADDRESS = HOST_MAP + MAP_SERVICE + API + "v1/address/search"
export const API_GET_INFO_ADDRESS = HOST_MAP + MAP_SERVICE + API;

// Feedback
export const API_SEND_FEEDBACK = `${HOST + PRODUCT_SERVICE + API_TICKET}create`

