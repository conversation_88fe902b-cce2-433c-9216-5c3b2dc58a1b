import { Dimensions, Platform, NativeModules } from "react-native";
import StaticSafeAreaInsets from 'react-native-static-safe-area-insets';
import DeviceInfo from 'react-native-device-info';
import { RNHyperSnapParams } from 'hypersnapsdk_reactnative';
import * as DEVICE from './device';

export const isHasNotch = DEVICE.isIOS && DEVICE.isNotch;
export const isHasDynamicIsland = DEVICE.isIOS && DEVICE.isDynamicIsland;
const heightTopSafeIOS = isHasDynamicIsland ? (StaticSafeAreaInsets.safeAreaInsetsTop - 12) : StaticSafeAreaInsets.safeAreaInsetsTop;
export const heightTopSafe = DEVICE.isIOS
    ? ((isHasNotch || isHasDynamicIsland) ? heightTopSafeIOS : 20)
    : 0;
export const heightBottomSafe = (isHasNotch || isHasDynamicIsland) ? StaticSafeAreaInsets.safeAreaInsetsBottom : 0;
export const heightIndicator = (isHasNotch || isHasDynamicIsland) ? 16 : 0;
export const NAVIGATION_HEADER_HEIGHT = 60;

const ExtraDimensions = DEVICE.isIOS ? null : require("react-native-extra-dimensions-android");

export const width = Dimensions.get("screen").width;

export const height = DEVICE.isIOS ? Dimensions.get("window").height : ExtraDimensions.get("REAL_WINDOW_HEIGHT") - ExtraDimensions.get("SOFT_MENU_BAR_HEIGHT");

export const isIphoneX = function () {
    return (
        DEVICE.isIOS && (height === 812 || width === 812)
    );
};

export const statusBar = function () {
    if (isIphoneX()) {
        return 44;
    }
    return 20;
}

const standardWidth = 375;
const standardHeight = 667;

const scale = size => width / standardWidth * size;

export const getSize = (initSize) => {
    return Math.round(scale(initSize));
}


export const defaultPadding = 4;
export const defaultFontSize = 14;
export const defaultFontColor = "#333333";
export const defaultFont = DEVICE.isIOS ? "Helvetica Neue" : "Roboto";
export const normalWeight = "400";
export const boldWeight = "600";
export const boldWeightExtra = "800";
export const boldWeightMega = "900";
export const defaultPaddingHorizontal = 10;
export const defaultPaddingVertical = 10;
export const defaultMarginHorizontal = 10;
export const defaultMarginVertical = 10;

/*  */
export const MIN_QUANTITY_APPLY = 1;
export const MAX_QUANTITY_APPLY = 99;
export const HYPER_VERGE = {
    DocumentCARD: RNHyperSnapParams.DocumentTypeCard,
    DocumentOTHER: RNHyperSnapParams.DocumentTypePassport,
    DocumentFront: RNHyperSnapParams.DocumentFront,
    DocumentBack: RNHyperSnapParams.DocumentBack,
    DocumentNoneSide: "none.none.none",
    ContentCARD_FRONT: JSON.stringify({
        docCaptureTitle: "Chụp CMND/CCCD mặt trước",
        docCaptureDescription: "Đảm bảo tài liệu của bạn không bị lóa và hoàn toàn đầy đủ",
        docCaptureSubText: "Mặt trước",
        docRetakeButtonText: "Chụp lại",
    }),
    ContentCARD_BACK: JSON.stringify({
        docCaptureTitle: "Chụp CMND/CCCD mặt sau",
        docCaptureDescription: "Đảm bảo tài liệu của bạn không bị lóa và hoàn toàn đầy đủ",
        docCaptureSubText: "Mặt sau",
        docRetakeButtonText: "Chụp lại",
    }),
    ContentOther: JSON.stringify({
        docCaptureTitle: "Chụp giấy tờ",
        docCaptureDescription: "Đảm bảo tài liệu của bạn không bị lóa và hoàn toàn đầy đủ",
        docCaptureSubText: "Tài liệu",
        docReviewRetakeButton: "Chụp lại"
    })
}
export const H_BILL = 1800;
export const H_VOUCHER = 1500;
export const H_BANK = 1500;
export const H_KEY = 1500;
export const URL_DOWNLOAD_APP = 'xmanager://xmanager/';
export const URL_NET_INFO = 'https://clients3.google.com/generate_204';
export const URL_FIND_IP = 'https://api.ipify.org';
export const URL_OCR_DOC = 'https://vnm-docs.hyperverge.co/v2/nationalID';
export const URL_APPV_CODE = 'https://erpapp.tgdd.vn/mwg-app-media-service/api/media/file/L2VycGNtbmQvY21uZC1kYXRhLzIwMjEvMTEvMTAvcXJvaWtpOGItaW1hZ2U=.jpg';
export const URL_SALE_COMBO = 'https://erpapp.tgdd.vn//mwg-app-pos-media-service/api/media/pos/file/L21lZGlhL3Bvcy8yMDI0LzEwLzA2L3JrYzlkdWRvLWltYWdl.jpg';
export const URL_OLD_PRODUCT = 'https://cdn.tgdd.vn/may-cu/';
export const URL_THUMBNAIL = `${URL_OLD_PRODUCT}Thumbnail/`;
export const THUMBNAIL_DESKTOP = "https://cdn.tgdd.vn/may-cu/Desktop/";
export const PROMOTION_CONFIG = new Set(['16356', '162882', '54413', '182603']);
export const PRINT_CONFIG = new Set([
    '8714', '487', '580', '1135', '2043', '4257', '1365', '1126', '1240',
    '2077', '10636', '1366', '1180', '86', '1244', '1574', '521', '4427', '3474',
    '9849', '1179', '1081', '1392', '1230', '1042', '460', '932', '933', '1034',
    '1649', '1142', '4827', '5738', '1092', '10899', '1018', '1093', '1415',
    '1003', '1130', '1041', '78', '1030'
]);
export const regexEmail = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/;

const brands = {
    TGDD: 1,
    DMX: 2,
    BHX: 3,
    AN_KHANG: 8
};

export const MENUID = {
    // [brands.TGDD]: 4784,
    [brands.TGDD]: 4196,
    [brands.DMX]: 4196
}

export const DISTANCE = {
    [brands.AN_KHANG]: 15,
    [brands.TGDD]: 20,
    [brands.DMX]: 20
};

export const ADJUST_PRICE_TYPE = {
    ADJUST_PRICE: 1,
    PRICE_WAR: 2,
    BALANCE_PRICE: 3
};

const { ADJUST_PRICE, BALANCE_PRICE, PRICE_WAR } = ADJUST_PRICE_TYPE;

export const ADJUST_PRICE_LABEL = {
    [ADJUST_PRICE]: 'editSaleOrder.adjust_price', // Điều chỉnh giá
    [PRICE_WAR]: 'editSaleOrder.competitive_price', // Chiến giá
    [BALANCE_PRICE]: 'editSaleOrder.balance_price' // Điều chỉnh cân bằng giá khi đổi hàng
};
// 2 -> Chuyển khoản; 1 -> Tiền mặt
export const PAYMENT_TYPE = {
    CASH: 1,
    BANKING: 2
};

export const PAYMENT_LABEL = {
    [PAYMENT_TYPE.CASH]: 'tiền mặt',
    [PAYMENT_TYPE.BANKING]: 'chuyển khoản'
};

export const TRANSACTION_STATUS = {
    UNKNOWN: 0,
    SUCCESS: 1,
    PROCESSING: 2,
    FAIL: 3,
    REFUND: 4
};

export const DATE_STATUS = {
    UNKOWN_DEPOSIT_DATE: 0,
    UNKOWN_ORDER_DATE: 1,
    UNKOWN_KEEP_DATE: 2,
};

export const SO_TYPE = {
    LOCK_PRE: ********,
};
export const BOUNCY_CHECK = {
    ALL: 2,
    ONE: 0,
    MULTIPLE: 1,
    SELECT_ALL: "SELECT_ALL"
};

export const VOUCHER_PARTNER_TYPE = {
    GOTIT: 9,
    URBOX: 12,
};


export const PROVINCE_OTP = new Set("3", "82", "105", "107", "81", "7", "115", "122", "126", "132", "144", "151", "152", "154");

export const REPORT_NAME = {
    EBillContent: "Biên nhận thanh toán kiêm phiếu giao hàng",
    EBillContentIncome: "Biên nhận thu tiền",
    GiftVCIssueContentPrint: "Phiếu mua hàng",
    KeySoftwareContent: "Key phần mềm"
}

export const BRAND_ID_OF_SIM = {
    VIETTEL: 126,
    VINA: 125,
    ITEL: 6306
};
export const CONNECTED_TYPE_SIM = {
    NOT_CONNECTED: 5,
    DIRECT_CONNECT: 1
};

export const STORE_ALLOW_VIETTEL_SIM = new Set(["777", "889"]);

export const TYPE_PROFILE = {
    CUSTOMER: 1,
    COMPANY: 5,
    ADDRESS_RECEIVE: 2,
    CUSTOMER_RECEIVE: 6
};


export const STATUS_PROMOTION = {
    INIT: -1,
    ACTIVE: 1,
    NON_ACTIVE: 0
};

export const PARTNER_ID = {
    HOME_CREDIT: '2',
    FE_CREDIT: '3',
    M_CREDIT: '10',
    SMART_POS: '23',
    MOMO: '26',
    MIRAE_ASSET: '15',
    HOME_PAY_LATER: '27',
    ACS: '1',
    SHINHAN: '22',
    KREDIVO: '29',
    CAKE: '30',
    QTV: '32',
    SAMSUNG: '33',
    TPBanhEVO: '34',
    QTV: '35',
};

export const PARTNER_REWARD_VALUE = {
    [PARTNER_ID.FE_CREDIT]: { FE_7: 10000, FE_20: 10000 },
    [PARTNER_ID.HOME_CREDIT]: { REWARD_1: 10000, REWARD_2: 0 },
    [PARTNER_ID.M_CREDIT]: 0,
    [PARTNER_ID.KREDIVO]: 50000
};
// phía Nam (Siêu thị thuộc 4 vùng: Hồ Chí Minh; Duyên Hải; Tây Nam Bộ; Đông Cao Nguyên) southern
export const EREA_APPLY_REWARD_7 = [3922, 3911, 3923, 3927, 3915, 3926, 3916, 3924, 3918, 3921, 3928, 3910, 3914, 3908, 3913, 3920, 3919, 3909, 3925, 3917, 3912, 3870, 3858, 3857, 3861, 3859, 3862, 3860, 3836, 3874, 3837, 3871, 3875, 3873, 3883, 3878, 3841, 3839, 3843, 3842, 3879, 3872, 3835, 3880, 3881, 3840, 3877, 3882, 3838, 3876]
// phía Bắc (Siêu thị thuộc 4 vùng: Hà Nội +; Đông Tây Bắc; Đồng Bằng Sông Hồng; Trung Bộ). North
export const EREA_APPLY_REWARD_20 = [3948, 3949, 3947, 3867, 3934, 3865, 3832, 3833, 3950, 3886, 3864, 3890, 3888, 3868, 3831, 3848, 3932, 3834, 3887, 3885, 3884, 3847, 3829, 3856, 3853, 3855, 3844, 3953, 3852, 3957, 3830, 3854, 3849, 3846, 3863, 3929, 3931, 3845, 3952, 3866, 3944, 3933, 3828, 3940, 3869, 3851, 3889, 3930, 3942, 3946, 3943, 3937, 3850, 3935, 3956, 3936, 3939, 3954, 3941, 3955, 3945, 3938, 3951]

export const RETURN_CODE_VPBANK = {
    CARD_DECLINE: "-2",//: Thẻ bị từ chối
    MAX_AMOUNT_EX: "-1",//: Số tiền tối đa vượt quá
    SUCCESS: "00",//: Được chấp thuận hoặc hoàn thành thành công
    OVER_LIM: "05",///: Không chấp nhận (Vượt hạn mức)  
    ERROR: "06",//: Lỗi  
    PROCESSING: "09",//: Yêu cầu đang được xử lý  
    APPROVED: "11",//: Được chấp thuận (VIP)  
    INVALID_TRAN: "12",//: Giao dịch không hợp lệ  
    INVALID_AMOUNT: "13",//: Số tiền không hợp lệ  
    INVALID_NUM: "14",//: Số thẻ không hợp lệ (không có số này)  
    INVALID_FORMAT: "30",//: Lỗi định dạng  
    NOT_SUFFICIENT: "51",//: Không đủ tiền trong tài khoản  
    EXCEEDS_AMOUNT: "61",//: Vượt quá hạn mức số tiền rút  
    EXCEEDS_FREQUENCY: "65"//: Vượt quá hạn mức tần suất rút
}

export const ID_CARD_TYPE = {
    CMND: 1,
    CCCD: 2,
    CCCD_NEW: 4,
    UNKNOWN: 0
};
export const ID_CARD_SIDE = {
    CMND: {
        FRONT: 1,
        BACK: 3,
        UNKNOWN: 0
    },
    CCCD: {
        FRONT: 2,
        BACK: 4,
        UNKNOWN: 0
    },
    CCCD_NEW: {
        FRONT: 11,
        BACK: 12,
        UNKNOWN: 0
    },
};

export const RETURN_CODE_VPBANK_VALUE = {
    [RETURN_CODE_VPBANK.CARD_DECLINE]: "Thẻ bị từ chối",
    [RETURN_CODE_VPBANK.MAX_AMOUNT_EX]: "Số tiền tối đa vượt quá",
    [RETURN_CODE_VPBANK.SUCCESS]: "Được chấp thuận hoặc hoàn thành thành công",
    [RETURN_CODE_VPBANK.OVER_LIM]: "Không chấp nhận (Vượt hạn mức)",
    [RETURN_CODE_VPBANK.ERROR]: "Lỗi",
    [RETURN_CODE_VPBANK.PROCESSING]: "Yêu cầu đang được xử lý  ",
    [RETURN_CODE_VPBANK.APPROVED]: " Được chấp thuận (VIP)  ",
    [RETURN_CODE_VPBANK.INVALID_TRAN]: "Giao dịch không hợp lệ ",
    [RETURN_CODE_VPBANK.INVALID_AMOUNT]: " Số tiền không hợp lệ",
    [RETURN_CODE_VPBANK.INVALID_NUM]: "Số thẻ không hợp lệ (không có số này)  ",
    [RETURN_CODE_VPBANK.INVALID_FORMAT]: "Lỗi định dạng  ",
    [RETURN_CODE_VPBANK.NOT_SUFFICIENT]: "Không đủ tiền trong tài khoản",
    [RETURN_CODE_VPBANK.EXCEEDS_AMOUNT]: "Vượt quá hạn mức số tiền rút  ",
    [RETURN_CODE_VPBANK.EXCEEDS_FREQUENCY]: " Vượt quá hạn mức tần suất rút",
};
export const MONEY_CARD_ID = {
    VP_BANK: "481"
};

export const PAYMENT_PARTNER_ID = {
    HOME_PAY_LATER: 13,
    KREDIVO: 15,
    CAKE: 16,
    QTV: 17,
    TPBanhEVO: 18
};

export const MAX_QUANTITY = {
    "*************": 60,
    "*************": 2,
    "*************": 60,
    "*************": 1,
    "*************": 10,
    "*************": 140,
    "*************": 5,
    "*************": 585,
    "*************": 117,
    "*************": 27,
    "*************": 7,
    "*************": 5,
    "*************": 27,
    "*************": 12,
    "*************": 14,
    "*************": 12,
    "*************": 12,
    "*************": 16,
    "*************": 12,
    "*************": 20,
    "*************": 12,
    "*************": 20,
    "*************": 12,
    "*************": 3332,
    "*************": 238,
    "*************": 28,
    "*************": 52,
    "*************": 20,
    "*************": 7,
    "*************": 32,
    "*************": 9,
    "*************": 27,
    "*************": 1680,
    "1193682000156": 28,
    "1193682000102": 840,
    "1193682000165": 28,
    "1193697000072": 24,
    "1193668000231": 1200,
    "1193668000291": 20,
    "1193682000066": 3332,
    "1193682000130": 238,
    "1193699000107": 54,
    "1193699000333": 54,
    "1193699000295": 3240,
    "1193680000306": 31,
    "1193680000156": 49,
    "1193695000185": 16,
    "1193680000146": 70,
    "1193699000080": 27,
    "1193699000084": 7,
    "1193692000006": 1,
    "1193678000124": 2,
    "1193678000182": 2,
    "1193685000027": 24,
    "1193685000042": 2,
    "1193697000054": 0,
    "1193697000163": 0,
    "1193697000157": 0,
    "1193697000055": 0,
    "1193695000041": 20,
    "1193695000067": 1,
    "1193700000107": 30,
    "1193700000108": 3,
    "1193663000039": 80,
    "1193663000065": 0,
    "1193678000111": 1,
    "1193667000020": 45,
    "1193667000038": 1,
    "1193660000022": 20,
    "1193660000037": 1,
    "1193670000054": 100,
    "1193670000055": 1,
    "1193670000250": 20,
    "1193670000253": 1,
    "1193695000032": 100,
    "1193695000058": 1,
    "1193684000063": 20,
    "1193684000096": 1,
    "1193687000416": 30,
    "1193687000431": 1,
    "1193687000338": 16,
    "1193687000351": 1,
    "1193670000345": 20,
    "1193670000346": 1,
    "1193687000246": 30,
    "1193687000248": 1,
    "1193684000034": 40,
    "1193684000067": 1,
    "1193687000232": 20,
    "1193687000236": 1,
    "1193687000045": 20,
    "1193687000046": 1,
    "1193687000596": 30,
    "1193687000601": 1,
    "1193687000410": 60,
    "1193687000425": 1,
    "1193670000262": 100,
    "1193670000264": 1,
    "1193671000017": 0,
    "1196046000460": 10,
    "1193687000692": 2,
    "1193670000334": 100,
    "1193670000335": 1,
    "1193670000307": 20,
    "1193670000308": 0,
    "1193670000322": 20,
    "1193670000323": 0,
    "1193684000040": 80,
    "1193684000073": 1,
    "1193687000447": 30,
    "1193687000458": 1,
    "1193670000261": 100,
    "1193670000263": 1,
    "1193660000017": 30,
    "1193660000032": 1,
    "1193687000298": 100,
    "1193687000301": 1,
    "1193660000049": 100,
    "1193660000055": 1,
    "1193664000096": 3,
    "1193663000028": 30,
    "1193663000054": 1,
    "1193671000010": 2,
    "1193702000274": 2,
    "1193702000273": 100,
    "1193664000091": 2,
    "1193689000344": 2,
    "1195274000034": 90,
    "1195274000037": 3,
    "9253124000188": 2,
    "1193688000617": 140,
    "1193688000633": 10,
    "1193688000619": 140,
    "1193688000635": 10,
    "1193681000191": 10,
    "1193681000097": 300,
    "1193681000220": 1,
    "1193681000126": 98,
    "1193663000734": 3,
    "1193663000733": 3,
    "1193663000731": 15,
    "1193663000732": 15,
    "1193681000305": 150,
    "1193681000350": 5,
    "1193671000006": 5,
    "1193698000058": 200,
    "1193698000097": 10
}