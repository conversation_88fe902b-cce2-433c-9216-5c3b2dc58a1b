import React from 'react';
import { Keyboard } from 'react-native';
import { createStackNavigator } from '@react-navigation/stack';
import { BackHeader, NoneHeader, MenuHeader } from "@header";
import { translate } from "@translate";
import PaymentTransactions from '../container/PaymentTransactions';
import MustPayment from '../container/PaymentTransactions/screens/MustPayment';

const PaymentTransaction = createStackNavigator();

const PaymentTransactionNavigator = () => {
    return (
        <PaymentTransaction.Navigator
            initialRouteName={"PaymentTransactions"}
            headerMode={"screen"}
        >
            <PaymentTransaction.Screen
                name={"PaymentTransactions"}
                component={PaymentTransactions}
                options={{
                    header: ({ navigation }) => (<MenuHeader
                        openDrawer={onOpenDrawer(navigation)}
                        title={'QUẢN LÝ GIAO DỊCH CHUYỂN KHOẢN'}
                        key={"HomeHeader"}
                        navigation={navigation}
                        isHomeScreen={true}
                    />),
                    gestureEnabled: false
                }}
            />
            <PaymentTransaction.Screen
                name={"MustPayment"}
                component={MustPayment}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={onGoBack(navigation)}
                        title={'ĐƠN HÀNG CẦN XỬ LÝ'}
                        // key={"HomeHeader"}
                        navigation={navigation}
                        isHomeScreen={true}
                    />),
                    gestureEnabled: false
                }}
            />
        </PaymentTransaction.Navigator>
    );
};

export default PaymentTransactionNavigator;

const onOpenDrawer = (navigation) => () => {
    Keyboard.dismiss();
    navigation.openDrawer();
}
const onGoBack = (navigation) => () => {
    navigation.goBack()
}
