import React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import { Keyboard, StyleSheet } from "react-native";
import { BackHeader, MenuHeader, NoneHeader } from "@header";
import { connect } from "react-redux";
const CollectionAirtimeServiceStack = createStackNavigator();
import BankAirtimeService from "../container/BankAirtimeService";
import StepOne from "../container/BankAirtimeService/Screen/BankStep/StepOne";
import StepTwo from "../container/BankAirtimeService/Screen/BankStep/StepTwo";
import StepThree from "../container/BankAirtimeService/Screen/BankStep/StepThree";
import QrCodePay from "../container/BankAirtimeService/Screen/QrCodePay";
import CustomerExpense from "../container/BankAirtimeService/Screen/CustomerExpense";
import HistorySell from "../container/CollectionTransferManager/Screen/HistorySell";
import { translate } from '@translate';
import * as actionBankAirtimeServiceCreator from "../container/BankAirtimeService/action";
import { bindActionCreators } from "redux";
import ReprintSaleOrder from "../container/SaleOrderManager/ReprintSaleOrder";
import OrderBackHeader from "../container/SaleOrderManager/Header/BackHeader";
import StepTwoDeposit from "../container/BankAirtimeService/Screen/BankStep/StepTwoDeposit";
import StepThreeDeposit from "../container/BankAirtimeService/Screen/BankStep/StepThreeDeposit";
import CreateSaleOrder from "../container/BankAirtimeService/Screen/CreateSaleOrder";
import SaleOrderPayment from '../container/SaleOrderPayment';
import OrderPaymentHeader from "../container/SaleOrderPayment/Header/BackHeader";
import QRPayment from '../container/SaleOrderPayment/QRPayment';
import OTPConsumerAirtimeService from "../container/InsuranceAirtimeService/Screen/OTPAirtimeService/index";
import DeleteSO from "../container/SaleOrderManager/DeleteSO";
import CancelSO from "../container/SaleOrderManager/CancelSO";
import PrintCoupon from "../container/CollectInstallmentPayments/PrintCoupon";

const BankAirtimeServiceNavigator = ({
  itemCatalog,
  updateHeaderAirtime,
  actionBankAirtimeService
}) => {
  const getTitleInsurance = () => {
    const { AirtimeServiceGroupName } = itemCatalog ?? "";
    const conertServiceGroupName = AirtimeServiceGroupName?.toUpperCase();
    return conertServiceGroupName;
  };

  const getTitleAirtimeList = () => {
    const { AirTimeTransactionTypeName } = updateHeaderAirtime ?? "";
    const conertTransactionTypeName = AirTimeTransactionTypeName?.toUpperCase();
    return conertTransactionTypeName;
  };

  return (
    <CollectionAirtimeServiceStack.Navigator
      initialRouteName={"InsuranceAirtimeService"}
      headerMode={"screen"}
    >
      <CollectionAirtimeServiceStack.Screen
        name={"BankAirtimeService"}
        component={BankAirtimeService}
        options={{
          header: ({ navigation }) => (
            <BackHeader
              onGoBack={navigation.goBack}
              title={getTitleInsurance()}
              key={"CollectionAirtimeService"}
              navigation={navigation}
            />
          ),
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"StepOne"}
        component={StepOne}
        options={{
          header: ({ navigation }) => (
            <BackHeader
              onGoBack={() => {
                actionBankAirtimeService.clear_data_customer();
                navigation.goBack()
              }}
              title={getTitleAirtimeList()}
              key={"StepOne"}
              navigation={navigation}
            />
          ),
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"StepTwo"}
        component={StepTwo}
        options={{
          header: ({ navigation }) => (
            <BackHeader
              onGoBack={navigation.goBack}
              title={getTitleAirtimeList()}
              key={"StepTwo"}
              navigation={navigation}
            />
          ),
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"StepThree"}
        component={StepThree}
        options={{
          headerShown: false,
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"QrCodePay"}
        component={QrCodePay}
        options={{
          header: NoneHeader,
          gestureEnabled: false
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"CustomerExpense"}
        component={CustomerExpense}
        options={{
          header: NoneHeader,
          gestureEnabled: false
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"HistorySell"}
        component={HistorySell}
        options={{
          header: ({ navigation }) => (<MenuHeader
            openDrawer={onOpenDrawer(navigation)}
            title={translate("collection.managerment_multicat_industry_transactions")}
            key={"HistorySell"}
            navigation={navigation}
          />),
          gestureEnabled: false
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"ReprintSaleOrder"}
        component={ReprintSaleOrder}
        options={{
          header: ({ navigation }) => (<OrderBackHeader
            navigation={navigation}
            key={"ReprintSOHeader"}
          />),
          gestureEnabled: false
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"StepTwoDeposit"}
        component={StepTwoDeposit}
        options={{
          header: ({ navigation }) => (
            <BackHeader
              onGoBack={navigation.goBack}
              title={getTitleAirtimeList()}
              key={"StepTwo"}
              navigation={StepTwoDeposit}
            />
          ),
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"StepThreeDeposit"}
        component={StepThreeDeposit}
        options={{
          headerShown: false,
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"CreateSaleOrder"}
        component={CreateSaleOrder}
        options={{
          headerShown: false,
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name="SaleOrderPayment"
        component={SaleOrderPayment}
        options={{
          header: ({ navigation }) => (
            <OrderPaymentHeader
              navigation={navigation}
              key="OrderPaymentHeader"
            />
          ),
          gestureEnabled: false
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"QRPayment"}
        component={QRPayment}
        options={{
          header: NoneHeader,
          gestureEnabled: false
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"OTPAirtimeService"}
        component={OTPConsumerAirtimeService}
        options={{
          headerShown: false, 
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"DeleteSO"}
        component={DeleteSO}
        options={{
          header: ({ navigation }) => (<OrderBackHeader
            navigation={navigation}
            key={"DeleteSOHeader"}
            isDelete={true}
          />),
          gestureEnabled: false
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"CancelSO"}
        component={CancelSO}
        options={{
          header: ({ navigation }) => (<OrderBackHeader
            navigation={navigation}
            key={"CancelSOHeader"}
            isDelete={true}
          />),
          gestureEnabled: false
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"PrintCoupon"}
        component={PrintCoupon}
        options={{
          header: ({ navigation }) => (<OrderBackHeader
            navigation={navigation}
            key={"PrintCoupon"}
          />),
          gestureEnabled: false
        }}
      />
    </CollectionAirtimeServiceStack.Navigator>
  );
};

const mapStateToProps = function (state) {
  return {
    updateHeaderAirtime: state.bankAirtimeServiceReducer.updateHeaderAirtime,
    itemCatalog: state.collectionReducer.itemCatalog,
  };
};

const mapDispatchToProps = function (dispatch) {
  return {
    actionBankAirtimeService: bindActionCreators(actionBankAirtimeServiceCreator, dispatch),
  };
};

const onGoBack = (navigation) => () => {
  navigation.reset({
    index: 0,
    routes: [{ name: "InsuranceAirtimeService" }],
  });
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(BankAirtimeServiceNavigator);

const styles = StyleSheet.create({});

const onOpenDrawer = (navigation) => () => {
  Keyboard.dismiss();
  navigation.openDrawer();
}