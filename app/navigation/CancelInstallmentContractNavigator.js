import { View, Text } from 'react-native'
import React from 'react'
import { createStackNavigator } from '@react-navigation/stack'
import { CancelInstallmentContract } from '../container/CancelInstallmentContract'
import { MenuHeader, BackHeader } from "@header";
import { Keyboard } from 'react-native';

const CancelStack = createStackNavigator()

const CancelInstallmentContractNavigator = () => {
  return (
    <CancelStack.Navigator
      initialRouteName='CancelInstallmentContract'
      headerMode={"screen"}

    >
      <CancelStack.Screen
        name='CancelInstallmentContract'
        component={CancelInstallmentContract}
        options={{
          header: ({ navigation }) => (<MenuHeader
            openDrawer={onOpenDrawer(navigation)}
            title={"Huỷ hợp đồng trả góp".toLocaleUpperCase()}
            key={"CancelInstallmentContract"}
            navigation={navigation}
          />),
          gestureEnabled: false
        }}
      />
    </CancelStack.Navigator>
  )
}

export default CancelInstallmentContractNavigator

const onOpenDrawer = (navigation) => () => {
  Keyboard.dismiss();
  navigation.openDrawer();
}