import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { Keyboard } from 'react-native';
import { BackHeader, MenuHeader, NoneHeader } from "@header";

const RequestReturnStack = createStackNavigator();
import RequestReturn from '../container/RequestReturn';

const RequestReturnNavigator = () => {
    return (
        <RequestReturnStack.Navigator
            initialRouteName={"RequestReturn"}
            headerMode={"screen"}
        >
            <RequestReturnStack.Screen
                name={"RequestReturn"}
                component={RequestReturn}
                options={{
                    header: ({ navigation }) => (
                        <MenuHeader
                            openDrawer={onOpenDrawer(navigation)}
                            title={"HỖ TRỢ PHÍ ĐỔI TRẢ"}
                            key={"RequestReturn"}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false
                }}
            />

        </RequestReturnStack.Navigator>
    );
}

export default RequestReturnNavigator

const onOpenDrawer = (navigation) => () => {
    Keyboard.dismiss();
    navigation.openDrawer();
}

