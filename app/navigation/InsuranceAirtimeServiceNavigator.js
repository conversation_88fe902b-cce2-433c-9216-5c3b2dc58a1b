import React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import { Keyboard, StyleSheet } from "react-native";
import { BackHeader, MenuHeader, NoneHeader } from "@header";
import { connect } from "react-redux";
import { translate } from '@translate';
import InsuranceAirtimeService from "../container/InsuranceAirtimeService";
const CollectionAirtimeServiceStack = createStackNavigator();
import SellInsurance from "../container/InsuranceAirtimeService/Screen/SellOrder/SellInsurance";
import SaleOrderPayment from "../container/SaleOrderPayment";
import OrderPaymentHeader from "../container/SaleOrderPayment/Header/BackHeader";
import ReprintSaleOrder from "../container/SaleOrderManager/ReprintSaleOrder";
import DeleteSO from "../container/SaleOrderManager/DeleteSO";
import CancelSO from "../container/SaleOrderManager/CancelSO";
import OrderBackHeader from "../container/SaleOrderManager/Header/BackHeader";
import PrintCertificate from "../container/InsuranceAirtimeService/Screen/PrintDocuments/PrintCertificate";
import HistorySellInsurance from "../container/InsuranceAirtimeService/Screen/SellOrder/HistorySellInsurance";
import RefundInsurance from "../container/InsuranceAirtimeService/Screen/Refund/RefundInsurance";
import HistoryRefundInsurance from "../container/InsuranceAirtimeService/Screen/Refund/HistoryRefundInsurance";
import ChangeImeiInsurance from "../container/InsuranceAirtimeService/Screen/ChangeImei/ChangeImeiInsurance";
import HistoryChangeImeiInsurance from "../container/InsuranceAirtimeService/Screen/ChangeImei/HistoryChangeImeiInsurance";
import WarrantyOrders from "../container/InsuranceAirtimeService/Screen/SellOrder/ExtendedWarranty/WarrantyOrders";
import SaleOrderDetail from "../container/InsuranceAirtimeService/Screen/SellOrder/ExtendedWarranty/SaleOrderDetail";
import InsuranceExtendedWarranty from "../container/InsuranceAirtimeService/Screen/SellOrder/ExtendedWarranty/InsuranceExtendedWarranty";
import CreateOrder from "../container/InsuranceAirtimeService/Screen/SellOrder/ExtendedWarranty/CreateOrder";
import HealthInsuranceSteps from "../container/InsuranceAirtimeService/Screen/SellOrder/HealthInsuranceSteps/HealthInsuranceSteps";
import CarInsuranceSteps from "../container/InsuranceAirtimeService/Screen/SellOrder/CarInsuranceSteps/CarInsuranceSteps";
import CarPhysicalDamageInsuranceSteps from "../container/InsuranceAirtimeService/Screen/SellOrder/CarPhysicalDamageInsuranceSteps/CarPhysicalDamageInsuranceSteps";
import RefundInsuranceMic from "../container/InsuranceAirtimeService/Screen/Refund/RefundInsuranceMic";
import PrintGuaranteeMIC from "../container/InsuranceAirtimeService/Screen/PrintDocuments/PrintGuaranteeMIC";
import PrintCertificateMIC from "../container/InsuranceAirtimeService/Screen/PrintDocuments/PrintCertificateMIC";
import PrintCertificateNew from "../container/InsuranceAirtimeService/Screen/PrintDocuments/PrintCertificateNew";
import QRPayment from '../container/SaleOrderPayment/QRPayment';
import SaleOrderInsuraneLoanProtection from "../container/InsuranceAirtimeService/Screen/SellOrder/LoanInsurance/SaleOrderInsuraneLoanProtection";
import InsuranceLoan from "../container/InsuranceAirtimeService/Screen/SellOrder/LoanInsurance/InsuranceLoan";
import CreateOrderLoan from "../container/InsuranceAirtimeService/Screen/SellOrder/LoanInsurance/CreateOrderLoan";
import OTPConsumerAirtimeService from "../container/InsuranceAirtimeService/Screen/OTPAirtimeService/index";
import HistorySell from "../container/CollectionTransferManager/Screen/HistorySell";
import PrintCoupon from "../container/CollectInstallmentPayments/PrintCoupon";
import CollectionNavigator from './CollectionNavigator';

const InsuranceAirtimeServiceNavigator = ({
  itemCatalog,
  updateHeaderAirtime,
}) => {
  const getTitleInsurance = () => {
    const { AirtimeServiceGroupName } = itemCatalog ?? "";
    const conertServiceGroupName = AirtimeServiceGroupName?.toUpperCase();
    return conertServiceGroupName;
  };

  const getTitleAirtimeList = () => {
    const { AirTimeTransactionTypeName } = updateHeaderAirtime ?? "";
    const conertTransactionTypeName = AirTimeTransactionTypeName?.toUpperCase();
    return conertTransactionTypeName;
  };

  return (
    <CollectionAirtimeServiceStack.Navigator
      initialRouteName={"InsuranceAirtimeService"}
      headerMode={"screen"}
    >
      <CollectionAirtimeServiceStack.Screen
        name={"InsuranceAirtimeService"}
        component={InsuranceAirtimeService}
        options={{
          header: ({ navigation }) => (
            <BackHeader
              onGoBack={navigation.goBack}
              title={getTitleInsurance()}
              key={"CollectionAirtimeService"}
              navigation={navigation}
            />
          ),
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"SellInsurance"}
        component={SellInsurance}
        options={{
          header: ({ navigation }) => (
            <BackHeader
              onGoBack={navigation.goBack}
              title={getTitleAirtimeList()}
              key={"SellInsurance"}
              navigation={navigation}
            />
          ),
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"WarrantyOrders"}
        component={WarrantyOrders}
        options={{
          header: ({ navigation }) => (
            <BackHeader
              onGoBack={navigation.goBack}
              title={getTitleAirtimeList()}
              key={"WarrantyOrders"}
              navigation={navigation}
            />
          ),
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"HistorySellInsurance"}
        component={HistorySellInsurance}
        options={{
          header: ({ navigation }) => (
            <BackHeader
              onGoBack={onGoBack(navigation)}
              title={"XEM LỊCH SỬ BÁN"}
              key={"HistorySellInsurance"}
              navigation={navigation}
            />
          ),
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"RefundInsurance"}
        component={RefundInsurance}
        options={{
          header: ({ navigation }) => (
            <BackHeader
              onGoBack={navigation.goBack}
              title={getTitleInsurance()}
              key={"RefundInsurance"}
              navigation={navigation}
            />
          ),
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"HistoryRefundInsurance"}
        component={HistoryRefundInsurance}
        options={{
          header: ({ navigation }) => (
            <BackHeader
              onGoBack={onGoBack(navigation)}
              title={"XEM LỊCH SỬ HOÀN TIỀN"}
              key={"HistoryRefundInsurance"}
              navigation={navigation}
            />
          ),
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"SaleOrderPayment"}
        component={SaleOrderPayment}
        options={{
          header: ({ navigation }) => (
            <OrderPaymentHeader
              navigation={navigation}
              key={"OrderPaymentHeader"}
            />
          ),
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"ReprintSaleOrder"}
        component={ReprintSaleOrder}
        options={{
          header: ({ navigation }) => (
            <OrderBackHeader navigation={navigation} key={"ReprintSOHeader"} />
          ),
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"DeleteSO"}
        component={DeleteSO}
        options={{
          header: ({ navigation }) => (
            <OrderBackHeader
              navigation={navigation}
              key={"DeleteSOHeader"}
              isDelete={true}
            />
          ),
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"CancelSO"}
        component={CancelSO}
        options={{
          header: ({ navigation }) => (
            <OrderBackHeader
              navigation={navigation}
              key={"CancelSOHeader"}
              isDelete={true}
            />
          ),
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"PrintCertificate"}
        component={PrintCertificate}
        options={{
          header: ({ navigation }) => (
            <BackHeader
              onGoBack={onGoBack(navigation)}
              title={"IN GIẤY CHỨNG NHẬN BẢO HIỂM"}
              key={"PrintCertificate"}
              navigation={navigation}
            />
          ),
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"PrintCertificateMIC"}
        component={PrintCertificateMIC}
        options={{
          header: ({ navigation }) => (<BackHeader
            onGoBack={onGoBack(navigation)}
            title={"IN GIẤY CHỨNG NHẬN BẢO HIỂM"}
            key={"PrintCertificateMIC"}
            navigation={navigation}
          />),
          gestureEnabled: false
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"PrintCertificateNew"}
        component={PrintCertificateNew}
        options={{
          header: ({ navigation }) => (<BackHeader
            onGoBack={onGoBack(navigation)}
            title={"IN GIẤY CHỨNG NHẬN BẢO HIỂM"}
            key={"PrintCertificateNew"}
            navigation={navigation}
          />),
          gestureEnabled: false
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"PrintGuaranteeMIC"}
        component={PrintGuaranteeMIC}
        options={{
          header: ({ navigation }) => (<BackHeader
            onGoBack={onGoBack(navigation)}
            title={"IN THẺ BẢO LÃNH"}
            key={"PrintGuaranteeMIC"}
            navigation={navigation}
          />),
          gestureEnabled: false
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"ChangeImeiInsurance"}
        component={ChangeImeiInsurance}
        options={{
          header: ({ navigation }) => (
            <BackHeader
              onGoBack={navigation.goBack}
              title={"CHỈNH SỬA IMEI"}
              key={"ChangeImeiInsurance"}
              navigation={navigation}
            />
          ),
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"HistoryChangeImeiInsurance"}
        component={HistoryChangeImeiInsurance}
        options={{
          header: ({ navigation }) => (
            <BackHeader
              onGoBack={onGoBack(navigation)}
              title={"GIAO DỊCH CHỈNH SỬA IMEI"}
              key={"HistoryChangeImeiInsurance"}
              navigation={navigation}
            />
          ),
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"SaleOrderDetail"}
        component={SaleOrderDetail}
        options={{
          headerShown: false,
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"InsuranceExtendedWarranty"}
        component={InsuranceExtendedWarranty}
        options={{
          headerShown: false,
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"CreateOrder"}
        component={CreateOrder}
        options={{
          header: ({ navigation }) => (
            <BackHeader
              onGoBack={navigation.goBack}
              title={getTitleInsurance()}
              key={"CreateOrder"}
              navigation={navigation}
            />
          ),
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"HealthInsuranceSteps"}
        component={HealthInsuranceSteps}
        options={{
          header: ({ navigation }) => (
            <BackHeader
              onGoBack={navigation.goBack}
              title={getTitleAirtimeList()}
              key={"HealthInsuranceSteps"}
              navigation={navigation}
            />
          ),
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"CarInsuranceSteps"}
        component={CarInsuranceSteps}
        options={{
          header: ({ navigation }) => (
            <BackHeader
              onGoBack={navigation.goBack}
              title={getTitleAirtimeList()}
              key={"CarInsuranceSteps"}
              navigation={navigation}
            />
          ),
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"CarPhysicalDamageInsuranceSteps"}
        component={CarPhysicalDamageInsuranceSteps}
        options={{
          header: ({ navigation }) => (
            <BackHeader
              onGoBack={navigation.goBack}
              title={getTitleAirtimeList()}
              key={"CarPhysicalDamageInsuranceSteps"}
              navigation={navigation}
            />
          ),
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"RefundInsuranceMic"}
        component={RefundInsuranceMic}
        options={{
          header: ({ navigation }) => (
            <BackHeader
              onGoBack={navigation.goBack}
              title={getTitleInsurance()}
              key={"RefundInsuranceMic"}
              navigation={navigation}
            />
          ),
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"QRPayment"}
        component={QRPayment}
        options={{
          header: NoneHeader,
          gestureEnabled: false
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"SaleOrderInsuraneLoanProtection"}
        component={SaleOrderInsuraneLoanProtection}
        options={{
          header: ({ navigation }) => (
            <BackHeader
              onGoBack={navigation.goBack}
              title={getTitleAirtimeList()}
              key={"SaleOrderInsuraneLoanProtection"}
              navigation={navigation}
            />
          ),
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"InsuranceLoan"}
        component={InsuranceLoan}
        options={{
          header: ({ navigation }) => (
            <BackHeader
              onGoBack={navigation.goBack}
              title={getTitleAirtimeList()}
              key={"InsuranceLoan"}
              navigation={navigation}
            />
          ),
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"CreateOrderLoan"}
        component={CreateOrderLoan}
        options={{
          header: ({ navigation }) => (
            <BackHeader
              onGoBack={navigation.goBack}
              title={getTitleAirtimeList()}
              key={"CreateOrderLoan"}
              navigation={navigation}
            />
          ),
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"OTPAirtimeService"}
        component={OTPConsumerAirtimeService}
        options={{
          headerShown: false,
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"HistorySell"}
        component={HistorySell}
        options={{
          header: ({ navigation }) => (<MenuHeader
            openDrawer={onOpenDrawer(navigation)}
            title={translate("collection.managerment_multicat_industry_transactions")}
            key={"HistorySell"}
            navigation={navigation}
          />),
          gestureEnabled: false
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"PrintCoupon"}
        component={PrintCoupon}
        options={{
          header: ({ navigation }) => (
            <OrderBackHeader navigation={navigation} key={"PrintCoupon"} />
          ),
          gestureEnabled: false,
        }}
      />
      <CollectionAirtimeServiceStack.Screen
        name={"CollectionNavigator"}
        component={CollectionNavigator}
        options={{
          headerShown: false,
          gestureEnabled: false
        }}
      />
    </CollectionAirtimeServiceStack.Navigator>
  );
};

const mapStateToProps = function (state) {
  return {
    updateHeaderAirtime: state.insuranceAirtimeServiceReducer.updateHeaderAirtime,
    itemCatalog: state.collectionReducer.itemCatalog,
  };
};

const mapDispatchToProps = function (dispatch) {
  return {};
};

const onGoBack = (navigation) => () => {
  navigation.reset({
    index: 0,
    routes: [{ name: "InsuranceAirtimeService" }],
  });
};

const onOpenDrawer = (navigation) => () => {
  Keyboard.dismiss();
  navigation.openDrawer();
};


export default connect(
  mapStateToProps,
  mapDispatchToProps
)(InsuranceAirtimeServiceNavigator);

const styles = StyleSheet.create({});
