import React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import ScreenshotFeedback from '../container/ScreenshotFeedback/index';
import { BackHeader } from '@header';
import { translate } from '@translate';

const ScreenshotFeedbackScreen = createStackNavigator();

const ScreenshotFeedbackNavigator = () => {
    return (
        <ScreenshotFeedbackScreen.Navigator initialRouteName="ScreenshotFeedbackScreen">
            <ScreenshotFeedbackScreen.Screen
                name={'ScreenshotFeedback'}
                component={ScreenshotFeedback}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={onGoBack(navigation)}
                            title={translate('feedback.feedback')}
                            key={'ScreenshotFeedback'}
                            navigation={navigation} />
                    )
                }}
            />
        </ScreenshotFeedbackScreen.Navigator>
    );
};

export default ScreenshotFeedbackNavigator;

const onGoBack = (navigation) => () => {
    navigation.goBack();
};
