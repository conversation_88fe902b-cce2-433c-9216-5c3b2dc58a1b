import React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import { Keyboard, StyleSheet } from "react-native";
import { BackHeader, MenuHeader, NoneHeader } from "@header";
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import { translate } from '@translate';
import CardOpeningService from "../container/CardOpeningService";
import QrCodeOpenCardService from "../container/CardOpeningService/Screen/QrCodeOpenCard/index";
import MenuOpenCard from "../container/CardOpeningService/Screen/MenuOpenCard";
import NoteInformationOpenCard from "../container/CardOpeningService/Screen/NoteInformationOpenCard";
import ConfirmOTPOpenCard from "../container/CardOpeningService/Screen/ConfirmOTPOpenCard";
import PullConsumerOpenCardPackage from "../container/CardOpeningService/Screen/PullConsumerOpenCardPackage";
import QrCodeOpenCardOld from "../container/CardOpeningService/Screen/QrCodeOpenCardOld/index";
import CustomerInfor from "../container/CardOpeningService/Screen/OCR/CustomerInfor";
const CardOpeningServiceServiceStack = createStackNavigator();

const CardOpeningServiceNavigator = ({
    itemCatalog,
    updateHeaderAirtime,
}) => {


    const getTitleLoanConsumer = () => {
        const { AirtimeServiceGroupName } = itemCatalog ?? "";
        const conertServiceGroupName = AirtimeServiceGroupName?.toUpperCase();
        return conertServiceGroupName;
    };

    const getTitleAirtimeList = () => {
        const { AirTimeTransactionTypeName } = updateHeaderAirtime ?? "";
        const conertTransactionTypeName = AirTimeTransactionTypeName?.toUpperCase();
        return conertTransactionTypeName;
    };
    return (
        <CardOpeningServiceServiceStack.Navigator
            initialRouteName={"ConsumerLoanAirtimeServiceNavigator"}
            headerMode={"screen"}
        >
            <CardOpeningServiceServiceStack.Screen
                name={"CardOpeningService"}
                component={CardOpeningService}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={navigation.goBack}
                            title={getTitleLoanConsumer()}
                            key={"CardOpeningService"}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false,
                }}
            />
            <CardOpeningServiceServiceStack.Screen
                name={"QrCodeOpenCardService"}
                component={QrCodeOpenCardService}
                options={{
                    header: NoneHeader,
                    gestureEnabled: false
                }}
            />
            <CardOpeningServiceServiceStack.Screen
                name={"MenuOpenCard"}
                component={MenuOpenCard}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={navigation.goBack}
                            title={getTitleAirtimeList()}
                            key={"MenuOpenCard"}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false,
                }}
            />
            <CardOpeningServiceServiceStack.Screen
                name={"NoteInformationOpenCard"}
                component={NoteInformationOpenCard}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={navigation.goBack}
                            title={getTitleAirtimeList()}
                            key={"NoteInformationOpenCard"}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false,
                }}
            />
            <CardOpeningServiceServiceStack.Screen
                name={"ConfirmOTPOpenCard"}
                component={ConfirmOTPOpenCard}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={navigation.goBack}
                            title={getTitleAirtimeList()}
                            key={"ConfirmOTPOpenCard"}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false,
                }}
            />
            <CardOpeningServiceServiceStack.Screen
                name={"PullConsumerOpenCardPackage"}
                component={PullConsumerOpenCardPackage}
                options={{
                    headerShown: false,
                    gestureEnabled: false,
                }}
            />
             <CardOpeningServiceServiceStack.Screen
                name={"QrCodeOpenCardOld"}
                component={QrCodeOpenCardOld}
                options={{
                    header: NoneHeader,
                    gestureEnabled: false
                }}
            />
            <CardOpeningServiceServiceStack.Screen
                name={"CustomerInfor"}
                component={CustomerInfor}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={navigation.goBack}
                            title={getTitleAirtimeList()}
                            key={"CustomerInfor"}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false,
                }}
            />
        </CardOpeningServiceServiceStack.Navigator>
    )
}
const mapStateToProps = function (state) {
    return {
        updateHeaderAirtime: state.consumerLoanAirtimeServiceReducer.updateHeaderAirtime,
        itemCatalog: state.collectionReducer.itemCatalog,
    };
};

const mapDispatchToProps = function (dispatch) {
    return {

    };
};

export default connect(
    mapStateToProps,
    mapDispatchToProps
)(CardOpeningServiceNavigator);


const styles = StyleSheet.create({})