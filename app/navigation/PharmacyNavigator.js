import React from 'react';
import { Keyboard } from 'react-native';
import { createStackNavigator } from '@react-navigation/stack';
import { Header, NoneHeader } from '@header';
import { translate } from '@translate';
import { BackHeader, MenuHeader } from '../header/SaleExpress';
import Pharmacy from '../container/AnKhangNew';
import {
    CartScreen,
    DrugsSuggestion,
    PaymentScreen,
    DrugsPackage,
    SuggestionDetail,
    ProvisionalCartList,
    SaleOrderCart,
    DrugsPrescription,
    CategoryListDrugs
} from '../container/AnKhangNew/screens';
import { SCREENS } from '../container/AnKhangNew/constants';
import QRPayment from '../container/SaleOrderPayment/QRPayment';
import BackHeaderPayment from '../container/AnKhangNew/Header/BackHeader';
import Loyalty from '../container/AnKhangNew/components/Loyalty';
import PackagingBag from '../container/PackagingBag';
import ThemeProvider from '../container/AnKhangNew/ThemeProvider';
import { useDispatch, useSelector } from 'react-redux';
import { reset_map_prescriptions } from '../container/AnKhangNew/action';
import { helper } from '@common';
import BuyMedicineRegularly from '../container/AnKhangNew/screens/BuyMedicineRegularly'
import HealthGuide from '../container/HealthGuide';
import CreateCard from '../container/HealthGuide/Screens/CreateCard';
import CardDetail from '../container/HealthGuide/Screens/CardDetail';
import FormPromotion from '../container/ShoppingCart/component/FormPromotion';

const { Navigator, Screen } = createStackNavigator();

const PharmacyStackNavigator = ({ route }) => {
    const { defaultScreen = SCREENS.Pharmacy } = route?.params ?? {};
    const dispatch = useDispatch();
    const { electricalPrescriptionBO } = useSelector((state) => state._pharmacyReducer);
    return (
        <ThemeProvider>
            <Navigator initialRouteName={defaultScreen} headerMode="screen">
                <Screen
                    name={SCREENS.Pharmacy}
                    component={Pharmacy}
                    options={{
                        header: ({ navigation }) => (
                            <MenuHeader
                                openDrawer={onOpenDrawer(navigation)}
                                title={translate(
                                    'header.search_product_uppercase'
                                )}
                                key="Pharmacy"
                            />
                        )
                    }}
                />
                <Screen
                    name={SCREENS.CartScreen}
                    component={CartScreen}
                    options={({ route }) => ({
                        header: ({ navigation }) => (
                            <BackHeader
                                onGoBack={() => {
                                    route.params.onGoBack();
                                    navigation.goBack();
                                }}
                                title="THÔNG TIN GIỎ HÀNG"
                                key="CartScreen"
                            />
                        )
                    })}
                />
                <Screen
                    name={SCREENS.DrugsSuggestion}
                    component={DrugsSuggestion}
                    options={{
                        header: ({ navigation }) => (
                            <BackHeader
                                onGoBack={navigation.goBack}
                                title="TƯ VẤN BỆNH"
                                key="DrugsSuggestion"
                            />
                        )
                    }}
                />
                <Screen
                    name={SCREENS.DrugsPackage}
                    component={DrugsPackage}
                    options={{
                        header: ({ navigation }) => (
                            <BackHeader
                                onGoBack={navigation.goBack}
                                title="TƯ VẤN GÓI THUỐC"
                                key="DrugsPackage"
                            />
                        )
                    }}
                />
                <Screen
                    name={SCREENS.SuggestionDetail}
                    component={SuggestionDetail}
                    options={{
                        header: ({ navigation }) => (
                            <BackHeader
                                onGoBack={navigation.goBack}
                                title="CHI TIẾT CẮT LIỀU"
                                key="SuggestionDetail"
                            />
                        )
                    }}
                />
                <Screen
                    name={SCREENS.ProvisionalCartList}
                    component={ProvisionalCartList}
                    options={{
                        header: ({ navigation }) => (
                            <BackHeader
                                onGoBack={navigation.goBack}
                                title="DANH SÁCH GIỎ HÀNG ĐÃ LƯU"
                                key="ProvisionalCartList"
                            />
                        )
                    }}
                />
                <Screen
                    name={SCREENS.PaymentScreen}
                    component={PaymentScreen}
                    options={{
                        header: ({ navigation }) => (
                            <BackHeaderPayment
                                navigation={navigation}
                                key="PaymentScreen"
                            />
                        ),
                        gestureEnabled: false
                    }}
                />
                <Screen
                    name={SCREENS.QRPayment}
                    component={QRPayment}
                    options={{
                        header: NoneHeader,
                        gestureEnabled: false
                    }}
                />
                <Screen
                    name={SCREENS.Loyalty}
                    component={Loyalty}
                    options={{
                        header: ({ navigation }) => (
                            <BackHeader
                                onGoBack={navigation.goBack}
                                title={translate('header.order_member_point')}
                                key="LoyaltyHeader"
                            />
                        ),
                        gestureEnabled: false
                    }}
                />
                <Screen
                    name={SCREENS.SaleOrderCart}
                    component={SaleOrderCart}
                    options={{
                        header: NoneHeader,
                        gestureEnabled: false
                    }}
                />
                <Screen
                    name={SCREENS.PackagingBag}
                    component={PackagingBag}
                    options={({ route, navigation }) => ({
                        header: () => (
                            <Header
                                navigation={navigation}
                                key="PackagingBag"
                                title={`${translate(
                                    'saleOrderManager.export_request'
                                )} ${route.params.saleOrderID}`}
                            />
                        ),
                        gestureEnabled: false
                    })}
                />
                <Screen
                    name={SCREENS.DrugsPrescription}
                    component={DrugsPrescription}
                    options={{
                        header: ({ navigation }) => (
                            <BackHeader
                                onGoBack={navigation.goBack}
                                title="BÁN THUỐC THEO TOA"
                                key="DrugsPrescription"
                            />
                        )
                    }}
                />
                <Screen
                    name={SCREENS.CategoryListDrugs}
                    component={CategoryListDrugs}
                    options={{
                        header: ({ navigation }) => (
                            <BackHeader
                                onGoBack={navigation.goBack}
                                title="BÁN THUỐC THEO TOA"
                                key="CategoryListDrugs"
                            />
                        )
                    }}
                />
                <Screen
                    name={"PrescriptionDetail"}
                    component={SuggestionDetail}
                    options={{
                        header: ({ navigation }) => (
                            <BackHeader
                                onGoBack={() => {
                                    if (!helper.IsEmptyObject(electricalPrescriptionBO)) {
                                        dispatch(reset_map_prescriptions());
                                    }
                                    navigation.goBack();
                                }}
                                title="BÁN THUỐC THEO TOA"
                                key="PrescriptionDetail"
                            />
                        )
                    }}
                />
                <Screen
                    name={SCREENS.BuyMedicineRegularlyScreenName}
                    component={BuyMedicineRegularly}
                    options={({ route, navigation }) => ({
                        header: () => (
                            <BackHeader
                                onGoBack={() => {
                                    navigation.navigate('Pharmacy');
                                }}
                                title={"ĐẶT LỊCH ĐỊNH KỲ"}
                                key={SCREENS.BuyMedicineRegularlyScreenName}
                                hiddenMenu
                            />

                        ),
                        gestureEnabled: false
                    })}
                />
                <Screen
                    name="HealthGuide"
                    component={HealthGuide}
                    options={{
                        header: ({ navigation }) => (
                            <MenuHeader
                                openDrawer={onOpenDrawer(navigation)}
                                title="CẨM NANG CHĂM SÓC KHÁCH HÀNG"
                                key="HealthGuidelHeader"
                                navigation={navigation}
                            />
                        ),
                        gestureEnabled: false
                    }}
                />
                <Screen
                    name="CreateCard"
                    component={CreateCard}
                    options={{
                        header: ({ navigation }) => (
                            <BackHeader
                                onGoBack={() => navigation.goBack()}
                                title="CẨM NANG CHĂM SÓC KHÁCH HÀNG"
                                key={'CreateCard'}
                            />
                        ),
                        gestureEnabled: false
                    }}
                />
                <Screen
                    name="CardDetail"
                    component={CardDetail}
                    options={{
                        header: ({ navigation }) => (
                            <BackHeader
                                onGoBack={() => navigation.goBack()}
                                title="CHI TIẾT CẨM NANG CSKH"
                                key={'CardDetail'}
                            />
                        ),
                        gestureEnabled: false
                    }}
                />
                <Screen
                    name={"FormPromotion"}
                    component={FormPromotion}
                    options={{
                        header: ({ navigation }) => (<BackHeader
                            onGoBack={navigation.goBack}
                            title={translate('header.add_image_uppercase')}
                            key={"FormPromotion"}
                        />),
                        gestureEnabled: false
                    }}
                />

            </Navigator>
        </ThemeProvider>
    );
};

export default PharmacyStackNavigator;

const onOpenDrawer = (navigation) => () => {
    Keyboard.dismiss();
    navigation.openDrawer();
};
