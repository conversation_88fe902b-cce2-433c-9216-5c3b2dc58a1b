import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { Keyboard } from 'react-native';
import { BackHeader, MenuHeader, NoneHeader } from "@header";

const WaterBillStack = createStackNavigator();
import OrderPaymentHeader from "../container/SaleOrderPayment/Header/BackHeader";
import SaleOrderPayment from '../container/SaleOrderPayment';
import WaterBill from '../container/WaterBill';

const WaterBillNavigator = () => {
    return (
        <WaterBillStack.Navigator
            initialRouteName={"WaterBill"}
            headerMode={"screen"}
        >
            <WaterBillStack.Screen
                name={"WaterBill"}
                component={WaterBill}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"THU HỘ TIỀN NƯỚC"}
                        key={"WaterBill"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <WaterBillStack.Screen
                name={"SaleOrderPayment"}
                component={SaleOrderPayment}
                options={{
                    header: ({ navigation }) => (<OrderPaymentHeader
                        navigation={navigation}
                        key={"OrderPaymentHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
        </WaterBillStack.Navigator>
    );
}

export default WaterBillNavigator

const onOpenDrawer = (navigation) => () => {
    Keyboard.dismiss();
    navigation.openDrawer();
}

