import React from 'react';
import { Keyboard } from 'react-native';
import { createStackNavigator } from '@react-navigation/stack';
import ActiveSimManager from '../container/ActiveSimManager';
import { MenuHeader } from '@header';
import { translate } from '@translate';
import SimProcess from '../container/SimProcess';
import BackHeaderSim from '../container/SimProcess/components/BackHeaderSim';

const SIMStack = createStackNavigator();

const SIMStackNavigator = () => {
    return (
        <SIMStack.Navigator initialRouteName={'ActiveSimManager'}>
            <SIMStack.Screen
                name={'ActiveSimManager'}
                component={ActiveSimManager}
                options={{
                    header: ({ navigation }) => (
                        <MenuHeader
                            openDrawer={onOpenDrawer(navigation)}
                            title={translate('header.manager_SIM_uppercase')}
                            key={'SIMHeader'}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false
                }}
            />

            <SIMStack.Screen
                name={'SimProcess'}
                component={SimProcess}
                options={{
                    header: ({ navigation }) => (
                        <BackHeaderSim
                            onGoBack={navigation.goBack}
                            key={'SIMHeader'}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false
                }}
            />
        </SIMStack.Navigator>
    );
};

export default SIMStackNavigator;

const onOpenDrawer = (navigation) => () => {
    Keyboard.dismiss();
    navigation.openDrawer();
};
