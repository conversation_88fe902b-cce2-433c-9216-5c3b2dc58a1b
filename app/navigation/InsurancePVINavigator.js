import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { Keyboard, StyleSheet } from 'react-native';
import { BackHeader, MenuHeader, NoneHeader } from "@header";
import InsurancePVI from '../container/InsurancePVI';
const InsurancePVIStack = createStackNavigator();
import OrderPaymentHeader from "../container/SaleOrderPayment/Header/BackHeader";
import ReplacementService from '../container/InsuranceBrightside/screen/ReplacementService';
import CancelService from '../container/InsuranceBrightside/screen/CancelService';
import ReprintSaleOrder from '../container/SaleOrderManager/ReprintSaleOrder';
import OrderBackHeader from "../container/SaleOrderManager/Header/BackHeader";
import DeleteSO from '../container/SaleOrderManager/DeleteSO';
import CancelSO from '../container/SaleOrderManager/CancelSO';
import RefundHistory from '../container/InsuranceBrightside/screen/RefundHistory';
import PrintCertificate from '../container/InsuranceBrightside/screen/PrintCertificate';
import SaleOrderPayment from '../container/SaleOrderPayment';
import CollectInsurancePVI from '../container/InsurancePVI/screen/CollectInsurancePVI';
import HistorySellPVI from '../container/InsurancePVI/screen/HistorySellPVI';
import EditEmei from '../container/InsurancePVI/screen/EditEmei';
import HistoryEditEmei from '../container/InsurancePVI/screen/HistoryEditEmei';
import { connect } from 'react-redux';

const InsurancePVINavigator = ({ updateHeaderAirtime }) => {

    const getTitleInsurance = () => {
        if (updateHeaderAirtime.AirTimeTransactionTypeID == 1112) {
            return "BẢO HIỂM RƠI VỠ PVI"
        } else if (updateHeaderAirtime.AirTimeTransactionTypeID == 1212) {
            return "BẢO HIỂM RƠI VỠ SC PLUS"
        } else if (updateHeaderAirtime.AirTimeTransactionTypeID == 1252) {
            return "BẢO HIỂM RƠI VỠ BLI"
        } else if (updateHeaderAirtime.AirTimeTransactionTypeID == 1452) {
            return "BẢO HIỂM RƠI VỠ PTI"
        } else if (updateHeaderAirtime.AirTimeTransactionTypeID == 1832) {
            return "BẢO HIỂM RƠI VỠ GIC-BOLTTECH"
        } else {
            return "BẢO HIỂM RƠI VỠ"
        }
    }

    return (
        <InsurancePVIStack.Navigator
            initialRouteName={"Insurance"}
            headerMode={"screen"}
        >
            <InsurancePVIStack.Screen
                name={"InsurancePVI"}
                component={InsurancePVI}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"BẢO HIỂM RƠI VỠ"}
                        key={"Insurance"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsurancePVIStack.Screen
                name={"CollectInsurancePVI"}
                component={CollectInsurancePVI}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={getTitleInsurance()}
                        key={"Insurance"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsurancePVIStack.Screen
                name={"ReplacementService"}
                component={ReplacementService}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={getTitleInsurance()}
                        key={"Insurance"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsurancePVIStack.Screen
                name={"SaleOrderPayment"}
                component={SaleOrderPayment}
                options={{
                    header: ({ navigation }) => (<OrderPaymentHeader
                        navigation={navigation}
                        key={"OrderPaymentHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsurancePVIStack.Screen
                name={"CancelService"}
                component={CancelService}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={getTitleInsurance()}
                        key={"Insurance"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsurancePVIStack.Screen
                name={"ReprintSaleOrder"}
                component={ReprintSaleOrder}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"ReprintSOHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsurancePVIStack.Screen
                name={"DeleteSO"}
                component={DeleteSO}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"DeleteSOHeader"}
                        isDelete={true}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsurancePVIStack.Screen
                name={"CancelSO"}
                component={CancelSO}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"CancelSOHeader"}
                        isDelete={true}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsurancePVIStack.Screen
                name={"HistorySellPVI"}
                component={HistorySellPVI}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={onGoBack(navigation)}
                        title={"XEM LỊCH SỬ BÁN"}
                        key={"Insurance"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsurancePVIStack.Screen
                name={"RefundHistory"}
                component={RefundHistory}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={onGoBack(navigation)}
                        title={"XEM LỊCH SỬ HOÀN TIỀN"}
                        key={"Insurance"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsurancePVIStack.Screen
                name={"PrintCertificate"}
                component={PrintCertificate}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={onGoBack(navigation)}
                        title={"IN GIẤY CHỨNG NHẬN BẢO HIỂM"}
                        key={"Insurance"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsurancePVIStack.Screen
                name={"EditEmei"}
                component={EditEmei}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"CHỈNH SỬA IMEI"}
                        key={"EditEmei"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsurancePVIStack.Screen
                name={"HistoryEditEmei"}
                component={HistoryEditEmei}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={onGoBack(navigation)}
                        title={"GIAO DỊCH CHỈNH SỬA IMEI"}
                        key={"HistoryEditEmei"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
        </InsurancePVIStack.Navigator>
    )
}

const onGoBack = (navigation) => () => {
    navigation.reset({
        index: 0,
        routes: [{ name: 'InsurancePVI' }]
    });
};

const mapStateToProps = function (state) {
    return {
        updateHeaderAirtime: state.insurancePVIReducer.updateHeaderAirtime,
    }
}

const mapDispatchToProps = function (dispatch) {
    return {

    }
}

export default connect(mapStateToProps, mapDispatchToProps)(InsurancePVINavigator);

const styles = StyleSheet.create({})