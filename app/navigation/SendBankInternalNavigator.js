import React from 'react';
import { BackHand<PERSON>, Keyboard } from 'react-native';
import { createStackNavigator } from '@react-navigation/stack';
import SendBankNavigator from '../container/SendBankManager';
import SendBank from '../container/SendBankManager/SendBank/index';
import EditSendBank from '../container/SendBankManager/SendBank/EditSendBank';
import DetailSendBank from '../container/SendBankManager/SendBank/DetailSendBank';
import DepositReceipt from '../container/SendBankManager/DepositReceiptManager/index';
import DepositReceiptDetail from '../container/SendBankManager/DepositReceiptManager/DepositReceiptDetail/index';
import CreateDepositReceipt from '../container/SendBankManager/DepositReceiptManager/DepositReceiptDetail/CreateDepositReceipt';
import CreateDepositRequest from '../container/SendBankManager/DepositReceiptManager/CreateDepositRequest/index';
import PrintDeposit from '../container/SendBankManager/DepositReceiptManager/PrintDeposit/index';
import { MenuHeader, BackHeader } from '@header';
import { translate } from '@translate';

const SendBankStask = createStackNavigator();

const SendBankStaskNavigator = () => {
    return (
        <SendBankStask.Navigator initialRouteName={'SendBankManager'}>
            <SendBankStask.Screen
                name={'SendBankManager'}
                component={SendBankNavigator}
                options={{
                    header: ({ navigation }) => (
                        <MenuHeader
                            openDrawer={onOpenDrawer(navigation)}
                            title={translate('header.send_bank_internal')}
                            key={'SendBank'}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false
                }}
            />
            <SendBankStask.Screen
                name={'SendBankStask'}
                component={SendBank}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={onGoBack(navigation)}
                            title={'QLYC DUYỆT CHI TIỀN'}
                            key={'SendBank'}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false
                }}
            />
            <SendBankStask.Screen
                name={'EditSendBank'}
                component={EditSendBank}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={onGoBack(navigation)}
                            title={'QLYC DUYỆT CHI TIỀN'}
                            key={'SendBank'}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false
                }}
            />
            <SendBankStask.Screen
                name={'DetailSendBank'}
                component={DetailSendBank}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={onGoBack(navigation)}
                            title={'QLYC DUYỆT CHI TIỀN'}
                            key={'SendBank'}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false
                }}
            />
            <SendBankStask.Screen
                name="DepositReceipt"
                component={DepositReceipt}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={onGoBack(navigation)}
                            title={'QUẢN LÝ PHIẾU NỘP TIỀN'}
                            key={'SendBank'}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false
                }}
            />
            <SendBankStask.Screen
                name="CreateDepositRequest"
                component={CreateDepositRequest}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={navigation.goBack}
                            title={'TẠO PHIẾU DUYỆT CHI'}
                            key={'CreateDepositRequest'}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false
                }}
            />
            <SendBankStask.Screen
                name="DepositReceiptDetail"
                component={DepositReceiptDetail}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={navigation.goBack}
                            title={'XEM / CHỈNH SỬA PHIẾU NỘP TIỀN'}
                            key={'DepositReceiptDetail'}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false
                }}
            />
            <SendBankStask.Screen
                name="CreateDepositReceipt"
                component={CreateDepositReceipt}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={navigation.goBack}
                            title={'TẠO PHIẾU NỘP TIỀN'}
                            key={'CreateDepositReceipt'}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false
                }}
            />
             <SendBankStask.Screen
                name="PrintDeposit"
                component={PrintDeposit}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={navigation.goBack}
                            title={'IN PHIẾU NỘP TIỀN'}
                            key={'PrintDeposit'}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false
                }}
            />
        </SendBankStask.Navigator>
    );
};

export default SendBankStaskNavigator;

const onOpenDrawer = (navigation) => () => {
    Keyboard.dismiss();
    navigation.openDrawer();
};

const onGoBack = (navigation) => () => {
    navigation.goBack();
};
