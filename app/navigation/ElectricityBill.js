import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { Keyboard } from 'react-native';
import { BackHeader, MenuHeader, NoneHeader } from "@header";
import ElectricityBill from '../container/ElectricityBill';
const ElectricityBillStack = createStackNavigator();
import OrderPaymentHeader from "../container/SaleOrderPayment/Header/BackHeader";
import SaleOrderPayment from '../container/SaleOrderPayment';

const ElectricityBillNavigator = () => {
    return (
        <ElectricityBillStack.Navigator
            initialRouteName={"ElectricityBill"}
            headerMode={"screen"}
        >
            <ElectricityBillStack.Screen
                name={"ElectricityBill"}
                component={ElectricityBill}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"THU HỘ TIỀN ĐIỆN"}
                        key={"ElectricityBill"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <ElectricityBillStack.Screen
                name={"SaleOrderPayment"}
                component={SaleOrderPayment}
                options={{
                    header: ({ navigation }) => (<OrderPaymentHeader
                        navigation={navigation}
                        key={"OrderPaymentHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
        </ElectricityBillStack.Navigator>
    );
}

export default ElectricityBillNavigator

const onOpenDrawer = (navigation) => () => {
    Keyboard.dismiss();
    navigation.openDrawer();
}

