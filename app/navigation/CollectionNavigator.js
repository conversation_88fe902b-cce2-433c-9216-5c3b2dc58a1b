import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { Keyboard } from 'react-native';
import { BackHeader, MenuHeader, NoneHeader } from "@header";
const CollectionStack = createStackNavigator();
import { translate } from '@translate';
import MenuCollection from '../container/CollectionTransfer/index'
import CollectionTwo from '../container/CollectionTransfer/Screen/CollectionTwo';
import CollectionOne from '../container/CollectionTransfer/Screen/CollectionOne';
import QueryStatus from '../container/CollectionTransfer/Screen/QueryStatus';
import SaleOrderPayment from '../container/SaleOrderPayment';
import SaleOrderCart from '../container/SaleOrderCart';
import OrderPaymentHeader from "../container/SaleOrderPayment/Header/BackHeader";
import CatalogCollection from '../container/CollectionTransfer/CatalogCollection';
import HealthInsuranceNavigator from './HealthInsuranceNavigator';
import InsurancePVINavigator from './InsurancePVINavigator';
import InsuranceExtendedWarrantyNavigator from './InsuranceExtendedWarrantyNavigator';
import InsuranceBrightsideNavigator from './InsuranceBrightsideNavigator';
import CollectInstallmentNavigator from './CollectInstallmentNavigator';
import InsuranceNavigator from './InsuranceNavigator';
import InsuranceAirtimeServiceNavigator from './InsuranceAirtimeServiceNavigator';
import BankAirtimeServiceNavigator from './BankAirtimeServiceNavigator'
import ConsumerLoanAirtimeServiceNavigator from './ConsumerLoanAirtimeServiceNavigator';
import OTPConsumerAirtimeService from '../container/ConsumerLoanAirtimeService/Screen/OTPConsumerAirtimeService';
import CardOpeningServiceNavigator from './CardOpeningServiceNavigator'

const CollectionNavigator = () => {
    return (
        <CollectionStack.Navigator
            initialRouteName={"MenuCollection"}
            headerMode={"screen"}
        >
            <CollectionStack.Screen
                name={"MenuCollection"}
                component={MenuCollection}
                options={{
                    header: ({ navigation }) => (<MenuHeader
                        openDrawer={onOpenDrawer(navigation)}
                        title={translate("collection.multicat_industry_service")}
                        key={"Collection"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <CollectionStack.Screen
                name={"CollectionOne"}
                component={CollectionOne}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={translate("collection.deposit_money_into_your_account")}
                        key={"CollectionOne"}
                    />),
                    gestureEnabled: false
                }}
            />
            <CollectionStack.Screen
                name={"CollectionTwo"}
                component={CollectionTwo}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={translate("collection.deposit_money_into_your_account")}
                        key={"Collection"}
                    />),
                    gestureEnabled: false
                }}
            />
            <CollectionStack.Screen
                name={"QueryStatus"}
                component={QueryStatus}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={translate("collection.deposit_money_into_your_account")}
                        key={"Collection"}
                    />),
                    gestureEnabled: false
                }}
            />
            <CollectionStack.Screen
                name={"SaleOrderCart"}
                component={SaleOrderCart}
                options={{
                    header: NoneHeader,
                    gestureEnabled: false
                }}
            />
            <CollectionStack.Screen
                name={"SaleOrderPayment"}
                component={SaleOrderPayment}
                options={{
                    header: ({ navigation }) => (<OrderPaymentHeader
                        navigation={navigation}
                        key={"OrderPaymentHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <CollectionStack.Screen
                name={"CatalogCollection"}
                component={CatalogCollection}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"DANH MỤC NHÓM DỊCH VỤ"}
                        key={"Collection"}
                    />),
                    gestureEnabled: false
                }}
            />
            <CollectionStack.Screen
                name={"HealthInsuranceNavigator"}
                component={HealthInsuranceNavigator}
                options={{
                    headerShown: false,
                    gestureEnabled: false
                }}
            />
            <CollectionStack.Screen
                name={"InsuranceNavigator"}
                component={InsuranceNavigator}
                options={{
                    headerShown: false,
                    gestureEnabled: false
                }}
            />
            <CollectionStack.Screen
                name={"InsurancePVINavigator"}
                component={InsurancePVINavigator}
                options={{
                    headerShown: false,
                    gestureEnabled: false
                }}
            />
            <CollectionStack.Screen
                name={"InsuranceExtendedWarrantyNavigator"}
                component={InsuranceExtendedWarrantyNavigator}
                options={{
                    headerShown: false,
                    gestureEnabled: false
                }}
            />
            <CollectionStack.Screen
                name={"InsuranceBrightsideNavigator"}
                component={InsuranceBrightsideNavigator}
                options={{
                    headerShown: false,
                    gestureEnabled: false
                }}
            />
            <CollectionStack.Screen
                name={"CollectInstallmentNavigator"}
                component={CollectInstallmentNavigator}
                options={{
                    headerShown: false,
                    gestureEnabled: false
                }}
            />
            <CollectionStack.Screen
                name={"InsuranceAirtimeServiceNavigator"}
                component={InsuranceAirtimeServiceNavigator}
                options={{
                    headerShown: false,
                    gestureEnabled: false
                }}
            />
            <CollectionStack.Screen
                name={"BankAirtimeServiceNavigator"}
                component={BankAirtimeServiceNavigator}
                options={{
                    headerShown: false,
                    gestureEnabled: false
                }}
            />
            <CollectionStack.Screen
                name={"ConsumerLoanAirtimeServiceNavigator"}
                component={ConsumerLoanAirtimeServiceNavigator}
                options={{
                    headerShown: false,
                    gestureEnabled: false
                }}
            />
            <CollectionStack.Screen
                name={"OTPAirtimeService"}
                component={OTPConsumerAirtimeService}
                options={{
                    header: NoneHeader,
                    gestureEnabled: false
                  }}
            />
             <CollectionStack.Screen
                name={"CardOpeningServiceNavigator"}
                component={CardOpeningServiceNavigator}
                options={{
                    headerShown: false,
                    gestureEnabled: false
                }}
            />
        </CollectionStack.Navigator>
    );
}

export default CollectionNavigator

const onOpenDrawer = (navigation) => () => {
    Keyboard.dismiss();
    navigation.openDrawer();
}

