import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Alert, Keyboard, KeyboardAvoidingView, TouchableOpacity, View } from 'react-native';
import { constants } from "@constants";
import { createDrawerNavigator } from '@react-navigation/drawer';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import messaging from '@react-native-firebase/messaging';
import { Icon, NotifService } from "@components";
import { subscribeNotify, storageHelper, helper, getTokenNotification } from '@common';
import { DEVICE, STORAGE_CONST, ENUM } from '@constants';
import { getQueryString } from "@config";
import MenuScreen from "../container/Menu";
import SaleNavigator from './SaleNavigator';
import StoreNavigator from "./StoreNavigator";
import VoucherNavigator from "./VoucherNavigator";
import CODNavigator from "./CODNavigator";
import SIMNavigator from "./SIMNavigator";
import OrderManagerNavigator from "./OrderManagerNavigator";
import InstallmentManagerNavigator from "./InstallmentManagerNavigator";
import InstallmentManagerNavigatorBC from "./InstallmentManagerNavigatorBC";
import InventoryNavigator from "./InventoryNavigator";
import InventoryStackNavigatorNew from './InventoryNavigatorNew'
import AdditionalPromotionNavigator from "./AdditionalPromotionNavigator";
import AddSaleStackNavigator from "./AddSalePromotionNavigator";
import SearchImeiStackNavigator from "./SearchImeiNavigator";
import F88StackNavigator from "./PortalF88Navigator";
import StickerProtectorNavigator from "./StickerProtectorNavigator";
import OclockStackNavigator from './OclockNavigator';
import ReceiptManagerNavigator from "./OrderOfflineManagerNavigator";
import NotifyStackNavigator from './NotifyNavigator';
import UploadPictureStackNavigator from './UploadPictureOldProductNavigator';
import ProductDisplayStackNavigator from './ProductDisplayNavigator';
import * as pouchDBActionCreator from '../container/PouchDB/action';
import ViewPromotionNavigator from './ViewPromotionNavigator';
import SwitchIMEINavigator from './SwitchIMEINavigator';
import ProductReturnsStackNavigator from './ProductReturnsNavigator';
import InOutVoucherNavigator from './InOutVoucherNavigator';
import AnKhangPharmacyStackNavigator from './AnKhangPharmacyNavigator';
import SpecialSaleProgramNavigator from './SpecialSaleProgramNavigator';
import CardNavigator from './CardNavigator';
import CardManagerNavigator from './CardManagerNavigator';
import PrintPriceListStackNavigator from './PrintPriceListNavigator';
import ScanQRCodeIDNavigator from './ScanQRCodeIDNavigator';
import NewProductRequestStackNavigator from './NewProductRequestNavigator';
import ServiceReceiptManagerNavigator from './ServiceReceiptManagerNavigator';
import TipsNavigator from './TipsNavigator';
import PharmacyStackNavigator from './PharmacyNavigator';
import SearchOutputReceiptNavigator from './SearchOutputReceiptNavigator';
import HomeNavigator from './HomeNavigator';
import StaffPromotionNavigator from './StaffPromotionNavigator';
import CustomerConsultantNavigator from './CustomerConsultantNavigator';
import ProductEvaluationNavigator from './ProductEvaluationNavigator';
import MenuCollectionNavigator from './CollectionNavigator';
import CollectionManagerNavigator from './CollectionManagerNavigator';
import SimPriceReportNavigator from './SimPriceReportNavigator';
import ClientConsultantNavigator from './ClientConsultantNavigator';
import RestockNavigator from './RestockNavigator';
import CheckInLoyaltyNavigator from './CheckInLoyaltyNavigator';
import CollectAndCloseRepairNavigator from './CollectAndCloseRepairNavigator';
import ElectricityBillNavigator from './ElectricityBillNavigator';
import WaterBillNavigator from './WaterBillNavigator';
import PaymentTransactionNavigator from './PaymentTransactionNavigator';
import PriceMakingNavigator from './PriceMakingNavigator';
import PreInformationNavigator from './PreInformationNavigator';
import BankAirtimeServiceNavigator from './BankAirtimeServiceNavigator';
import RequestReturnNavigator from './RequestReturnNavigator';
import ConsumerLoanAirtimeServiceNavigator from './ConsumerLoanAirtimeServiceNavigator'
import CancelInstallmentContractNavigator from './CancelInstallmentContractNavigator';
import CardOpeningServiceNavigator from './CardOpeningServiceNavigator';
import SendBankNavigator from './SendBankInternalNavigator';
import CertificateNavigator from './CertificateNavigator';
import { addScreenshotListener } from 'react-native-detector';
import { MyText } from '../components';


const widthMenu = constants.width * 0.88;
const Drawer = createDrawerNavigator();
const DrawerNavigator = ({ pouchDBAction, userInfo, navigation }) => {

    const hideFeedbackButtonTimeoutRef = useRef(null);
    const [isVisibleFeedbackButton, setIsVisibleFeedbackButton] = useState(false);

    /* Handler Notification */
    const onRegister = (token) => {
        console.log("onRegister", token);
    }
    const onNotification = (notification) => {
        console.log("onNotification", notification);
        const { data } = notification;
        if (helper.IsNonEmptyString(data?.custom_notification)) {
            const { title, body } = JSON.parse(data?.custom_notification);
            Alert.alert(title, body, [{
                text: "OK"
            }]);
        }
    }
    const _notify = new NotifService(onRegister, onNotification);

    const getTokenSubscribe = async () => {
        try {
            const tokenFCM = await getTokenNotification();
            if (tokenFCM) {
                const result = await subscribeNotify({
                    "userToken": userInfo.userName,
                    "session": DEVICE.uniqueId,
                    "token": tokenFCM,
                    "device": DEVICE.deviceNotify
                });
                console.log("getTokenSubscribe success", result);
            }
            console.log("getTokenSubscribe success", result);
        } catch (error) {
            console.log("getTokenSubscribe error", error);
        }
    }

    const onScreenshotDetected = () => {
        Keyboard.dismiss();
        setIsVisibleFeedbackButton(true);
    };

    const onHideFeedbackButton = () => {
        setIsVisibleFeedbackButton(false);
    }

    /* didMount */
    const didMount = () => {
        pouchDBAction.initPouchDB();
        const unsubscribeNotify = messaging().onMessage(onReceiveForeground);
        const unsubscribeScreenshotDetector = addScreenshotListener(onScreenshotDetected);
        getTokenSubscribe();
        return () => {
            if (hideFeedbackButtonTimeoutRef.current) {
                clearTimeout(hideFeedbackButtonTimeoutRef.current);
            }
            unsubscribeNotify();
            unsubscribeScreenshotDetector();
        };
    }
    useEffect(didMount, [])

    /* Handler Notification */
    const onReceiveForeground = async (remoteMessage) => {
        const { notification, data } = remoteMessage;
        _notify.localNotify({
            "title": notification.title,
            "message": notification.body,
            "data": data
        });
    }

    const onPressFeedbackButton = () => {
        navigation.navigate('Feedback');
        setIsVisibleFeedbackButton(false)
    }

    const initRouteName = useMemo(() => {
        const isAva = ENUM.BRAND_ID.AVA.some((brand) => userInfo.brandID == brand)
        switch (true) {
            case parseInt(userInfo.brandID) == ENUM.BRAND_ID.AN_KHANG || (isAva && ENUM.ALLOW_STORE_ID.AVA.some((storeId) => storeId == -1 || userInfo.storeID == storeId)):
                return ENUM.SCREENS.PHARMACY;
            default:
                return ENUM.SCREENS.SALE;
        }
    }, [userInfo.brandID, userInfo.storeID]);

    const isSaleCardNew = useMemo(() => {
        return SALE_CARD_SO.has(`${userInfo.storeID}`);
    }, [userInfo.storeID]);

    return (
        <KeyboardAvoidingView style={{
            flex: 1
        }}>
            <Drawer.Navigator
                initialRouteName={initRouteName}
                drawerContent={(props) => (<MenuScreen
                    {...props}
                    width={widthMenu}
                />)}
                screenOptions={{
                    gestureEnabled: false,
                    drawerStyle: {
                        width: constants.width,
                        backgroundColor: 'transparent'
                    },
                    headerShown: false,
                    drawerType: 'front'
                }}
            >
                <Drawer.Screen
                    name={"Home"}
                    component={HomeNavigator}
                />
                <Drawer.Screen
                    name={"Sale"}
                    component={SaleNavigator}
                />
                <Drawer.Screen
                    name="CheckInLoyalty"
                    component={CheckInLoyaltyNavigator}
                />
                <Drawer.Screen
                    name={"Restock"}
                    component={RestockNavigator}
                />
                <Drawer.Screen
                    name={"Notification"}
                    component={NotifyStackNavigator}
                />
                <Drawer.Screen
                    name={"Store"}
                    component={StoreNavigator}
                />
                <Drawer.Screen
                    name={"ActiveSimManager"}
                    component={SIMNavigator}
                />
                <Drawer.Screen
                    name={"CodPay"}
                    component={CODNavigator}
                />
                <Drawer.Screen
                    name={"CMMoneyComplaint"}
                    component={VoucherNavigator}
                />
                <Drawer.Screen
                    name={"OrderManagement"}
                    component={OrderManagerNavigator}
                />
                <Drawer.Screen
                    name={"ReceiptManagement"}
                    component={ReceiptManagerNavigator}
                />
                <Drawer.Screen
                    name={"Installment"}
                    component={InstallmentManagerNavigatorBC}
                />
                <Drawer.Screen
                    name={"Inventory"}
                    component={InventoryNavigator}
                />
                <Drawer.Screen
                    name={"InventoryNew"}
                    component={InventoryStackNavigatorNew}
                />
                <Drawer.Screen
                    name={"AdditionalPromotion"}
                    component={AdditionalPromotionNavigator}
                />
                <Drawer.Screen
                    name={"AddSalePromotion"}
                    component={AddSaleStackNavigator}
                />
                <Drawer.Screen
                    name={"SearchImei"}
                    component={SearchImeiStackNavigator}
                />
                <Drawer.Screen
                    name={"PortalF88"}
                    component={F88StackNavigator}
                />
                <Drawer.Screen
                    name={"StickerProtector"}
                    component={StickerProtectorNavigator}
                />
                <Drawer.Screen
                    name={'Oclock'}
                    component={OclockStackNavigator}
                />
                <Drawer.Screen
                    name={'UploadPictureOldProduct'}
                    component={UploadPictureStackNavigator}
                />
                <Drawer.Screen
                    name={'ProductDisplayManager'}
                    component={ProductDisplayStackNavigator}
                />
                <Drawer.Screen
                    name={'ViewPromotion'}
                    component={ViewPromotionNavigator}
                />
                <Drawer.Screen
                    name={"SwitchIMEI"}
                    component={SwitchIMEINavigator}
                />
                <Drawer.Screen
                    name={"ProductReturns"}
                    component={ProductReturnsStackNavigator}
                />
                <Drawer.Screen
                    name={'InOutVoucher'}
                    component={InOutVoucherNavigator}
                />
                <Drawer.Screen
                    name={ENUM.SCREENS.AN_KHANG}
                    component={AnKhangPharmacyStackNavigator}
                />
                <Drawer.Screen
                    name={"SpecialSaleProgram"}
                    component={SpecialSaleProgramNavigator}
                />
                <Drawer.Screen
                    name={"SellCard"}
                    // component={isSaleCardNew ? CardManagerNavigator : CardNavigator}
                    component={CardManagerNavigator}
                />
                <Drawer.Screen
                    name={'PrintPriceList'}
                    component={PrintPriceListStackNavigator}
                />
                <Drawer.Screen
                    name="ScanQRCodeID"
                    component={ScanQRCodeIDNavigator}
                />
                <Drawer.Screen
                    name="NewProductRequest"
                    component={NewProductRequestStackNavigator}
                />
                <Drawer.Screen
                    name="ServiceReceiptManagement"
                    component={ServiceReceiptManagerNavigator}
                />
                <Drawer.Screen
                    name="Tips"
                    component={TipsNavigator}
                />
                <Drawer.Screen
                    name={ENUM.SCREENS.PHARMACY}
                    component={PharmacyStackNavigator}
                />
                <Drawer.Screen
                    name="SearchOutputReceipt"
                    component={SearchOutputReceiptNavigator}
                />
                <Drawer.Screen
                    name="StaffPromotion"
                    component={StaffPromotionNavigator}
                />
                <Drawer.Screen
                    name="CustomerConsultant"
                    component={CustomerConsultantNavigator}
                />
                <Drawer.Screen
                    name="ProductEvaluation"
                    component={ProductEvaluationNavigator}
                />
                <Drawer.Screen
                    name={"MenuCollection"}
                    component={MenuCollectionNavigator}
                />
                <Drawer.Screen
                    name={"CollectionManager"}
                    component={CollectionManagerNavigator}
                />
                <Drawer.Screen
                    name="SimPriceReport"
                    component={SimPriceReportNavigator}
                />
                <Drawer.Screen
                    name={"ClientConsultant"}
                    component={ClientConsultantNavigator}
                />
                <Drawer.Screen
                    name={"CollectAndCloseRepair"}
                    component={CollectAndCloseRepairNavigator}
                />
                <Drawer.Screen
                    name={"ElectricityBill"}
                    component={ElectricityBillNavigator}
                />
                <Drawer.Screen
                    name={"WaterBill"}
                    component={WaterBillNavigator}
                />
                <Drawer.Screen
                    name={"TransactionScreens"}
                    component={PaymentTransactionNavigator}
                />
                <Drawer.Screen
                    name={"PriceMaking"}
                    component={PriceMakingNavigator}
                />
                <Drawer.Screen
                    name={"PreInformation"}
                    component={PreInformationNavigator}
                />
                <Drawer.Screen
                    name={'BankAirtimeService'}
                    component={BankAirtimeServiceNavigator}
                />
                <Drawer.Screen
                    name={"RequestReturn"}
                    component={RequestReturnNavigator}
                />
                <Drawer.Screen
                    name={'ConsumerLoanAirtimeService'}
                    component={ConsumerLoanAirtimeServiceNavigator}
                />
                <Drawer.Screen
                    name={'CancelInstallmentContract'}
                    component={CancelInstallmentContractNavigator}
                />
                <Drawer.Screen
                    name={'HealthGuide'}
                    component={PharmacyStackNavigator}
                    initialParams={{ defaultScreen: "HealthGuide" }}
                />
                <Drawer.Screen
                    name={'CardOpeningService'}
                    component={CardOpeningServiceNavigator}
                />
                <Drawer.Screen
                    name={"SendBank"}
                    component={SendBankNavigator}
                />
                <Drawer.Screen
                    name={"Certificate"}
                    component={CertificateNavigator}
                />

            </Drawer.Navigator>
            {isVisibleFeedbackButton && <TouchableOpacity
                style={{
                    paddingHorizontal: 20,
                    paddingVertical: 10,
                    position: 'absolute',
                    right: 20,
                    bottom: constants.heightBottomSafe + 200,
                    backgroundColor: '#D6F5E5',
                    borderRadius: 10,
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderColor: '#2FB47C',
                    borderWidth: 1,
                    flexDirection: 'row'
                }}
                activeOpacity={0.8}
                onPress={onPressFeedbackButton}
            >
                <Icon
                    iconSet={'Ionicons'}
                    name={'chatbox-ellipses-outline'}
                    color='#2FB47C'
                    size={20}
                />

                <MyText
                    text='Hỗ trợ'
                    style={{
                        fontWeight: 'bold',
                        marginLeft: 10,
                        color: '#2FB47C'
                    }}
                />
                <TouchableOpacity style={{
                    position: 'absolute',
                    right: -5,
                    top: -42
                }}
                    onPress={() => onHideFeedbackButton()}
                    activeOpacity={0.8}>
                    <Icon
                        iconSet={'Ionicons'}
                        name={'close-circle'}
                        color='#A7A7A7'
                        size={35}
                    />
                </TouchableOpacity>
            </TouchableOpacity>}
        </KeyboardAvoidingView>
    );
};

const mapStateToProps = (state) => ({
    userInfo: state.userReducer,
});

const mapDispatchToProps = (dispatch) => ({
    pouchDBAction: bindActionCreators(pouchDBActionCreator, dispatch),
});

export default connect(mapStateToProps, mapDispatchToProps)(DrawerNavigator);

const SALE_CARD_SO = new Set(['2043', '2320', '10513']);
