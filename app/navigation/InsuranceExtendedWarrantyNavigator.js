import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { Keyboard, StyleSheet } from 'react-native';
import { BackHeader, MenuHeader, NoneHeader } from "@header";
import InsuranceExtendedWarranty from '../container/InsuranceExtendedWarranty';
import SaleOrderInsuraneExtended from '../container/InsuranceExtendedWarranty/screen/SaleOrderInsuraneExtended';
import ReplacementService from '../container/InsuranceBrightside/screen/ReplacementService';
import OrderBackHeader from "../container/InsuranceExtendedWarranty/Header/BackHeader";
import SaleOrderDetail from '../container/InsuranceExtendedWarranty/screen/SaleOrderDetail';
import InsuranceInformation from '../container/InsuranceExtendedWarranty/screen/InsuranceInformation';
import SaleOrderPayment from '../container/SaleOrderPayment';
import OrderPaymentHeader from "../container/SaleOrderPayment/Header/BackHeader";
import ReprintSaleOrder from '../container/SaleOrderManager/ReprintSaleOrder';
import DeleteSO from '../container/SaleOrderManager/DeleteSO';
import CancelSO from '../container/SaleOrderManager/CancelSO';
import HistorySellExtendedWarranty from '../container/InsuranceExtendedWarranty/screen/HistorySellExtendedWarranty';
import CancelService from '../container/InsuranceBrightside/screen/CancelService';
const InsuranceExtendedWarrantyStack = createStackNavigator();

const InsuranceExtendedWarrantyNavigator = () => {
    return (
        <InsuranceExtendedWarrantyStack.Navigator
            initialRouteName={"Insurance"}
            headerMode={"screen"}
        >
            <InsuranceExtendedWarrantyStack.Screen
                name={"InsuranceExtendedWarranty"}
                component={InsuranceExtendedWarranty}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"BẢO HIỂM MỞ RỘNG"}
                        key={"InsuranceExtendedWarranty"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceExtendedWarrantyStack.Screen
                name={"ReplacementService"}
                component={ReplacementService}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"BẢO HIỂM MỞ RỘNG"}
                        key={"ReplacementService"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceExtendedWarrantyStack.Screen
                name={"SaleOrderInsuraneExtended"}
                component={SaleOrderInsuraneExtended}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"THU HỘ - BẢO HIỂM MỞ RỘNG PVI"}
                        key={"InsuranceExtendedWarranty"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceExtendedWarrantyStack.Screen
                name={"SaleOrderDetail"}
                component={SaleOrderDetail}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"DeleteSOHeader"}
                        isDelete={true}
                    />),
                    gestureEnabled: false
                }}
            />

            <InsuranceExtendedWarrantyStack.Screen
                name={"InsuranceInformation"}
                component={InsuranceInformation}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"THU HỘ - BẢO HIỂM MỞ RỘNG PVI"}
                        key={"InsuranceInformation"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceExtendedWarrantyStack.Screen
                name={"SaleOrderPayment"}
                component={SaleOrderPayment}
                options={{
                    header: ({ navigation }) => (<OrderPaymentHeader
                        navigation={navigation}
                        key={"OrderPaymentHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceExtendedWarrantyStack.Screen
                name={"ReprintSaleOrder"}
                component={ReprintSaleOrder}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"ReprintSOHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceExtendedWarrantyStack.Screen
                name={"DeleteSO"}
                component={DeleteSO}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"DeleteSOHeader"}
                        isDelete={true}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceExtendedWarrantyStack.Screen
                name={"CancelSO"}
                component={CancelSO}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"CancelSOHeader"}
                        isDelete={true}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceExtendedWarrantyStack.Screen
                name={"HistorySellExtendedWarranty"}
                component={HistorySellExtendedWarranty}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={onGoBack(navigation)}
                        title={"XEM LỊCH SỬ BÁN"}
                        key={"Insurance"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceExtendedWarrantyStack.Screen
                name={"CancelService"}
                component={CancelService}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"BẢO HIỂM MỞ RỘNG"}
                        key={"Insurance"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
        </InsuranceExtendedWarrantyStack.Navigator>

    )
}

export default InsuranceExtendedWarrantyNavigator

const styles = StyleSheet.create({})

const onGoBack = (navigation) => () => {
    navigation.reset({
        index: 0,
        routes: [{ name: 'InsuranceExtendedWarranty' }]
    });
};
