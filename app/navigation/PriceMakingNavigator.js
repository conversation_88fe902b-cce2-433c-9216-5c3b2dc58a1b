import React from 'react';
import { Keyboard } from 'react-native';
import { createStackNavigator } from '@react-navigation/stack';
import { MenuHeader, BackHeader } from '@header';
import PriceMaking from '../container/PriceMaking';
import PriceAdjust from '../container/PriceMaking/Screens/PriceAdjust';

const { Navigator, Screen } = createStackNavigator();

const PriceMakingNavigator = () => {
    return (
        <Navigator initialRouteName="PriceMaking" headerMode="screen">
            <Screen
                name="PriceMaking"
                component={PriceMaking}
                options={{
                    header: ({ navigation }) => (
                        <MenuHeader
                            openDrawer={onOpenDrawer(navigation)}
                            title="SIÊU THỊ LÀM GIÁ"
                            key="PriceMakinglHeader"
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false
                }}
            />
            <Screen
                name="PriceAdjust"
                component={PriceAdjust}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={() => navigation.goBack()}
                            title="SIÊU THỊ LÀM GIÁ"
                            key={"PriceAdjustHeader"}
                        />
                    ),
                    gestureEnabled: false
                }}
            />
        </Navigator>
    );
};

export default PriceMakingNavigator;

const onOpenDrawer = (navigation) => () => {
    Keyboard.dismiss();
    navigation.openDrawer();
};
