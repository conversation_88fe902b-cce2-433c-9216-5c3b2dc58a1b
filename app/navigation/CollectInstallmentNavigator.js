import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { BackHeader, MenuHeader, NoneHeader } from "@header";
const CollectInstallmentStack = createStackNavigator();
import OrderPaymentHeader from "../container/SaleOrderPayment/Header/BackHeader";
import SaleOrderPayment from '../container/SaleOrderPayment';
import PayBillAirtimeService from '../container/CollectInstallmentPayments/index'
import { connect } from 'react-redux';
import HistorySell from '../container/CollectionTransferManager/Screen/HistorySell';
import { Keyboard } from 'react-native';
import { translate } from '@translate';
import QRPayment from '../container/SaleOrderPayment/QRPayment';
import ReprintSaleOrder from '../container/SaleOrderManager/ReprintSaleOrder';
import OrderBackHeader from "../container/SaleOrderManager/Header/BackHeader";
import DeleteSO from '../container/SaleOrderManager/DeleteSO';
import CancelSO from '../container/SaleOrderManager/CancelSO';
import CancelService from '../container/CollectionTransferManager/Screen/CancelService'
import RefundHistory from '../container/CollectionTransferManager/Screen/RefundHistory';
import SearchCollection from '../container/CollectionTransferManager';
import PrintCoupon from '../container/CollectInstallmentPayments/PrintCoupon';
import BankAirtimeService from '../container/BankAirtimeService';
import CollectionNavigator from './CollectionNavigator';

const CollectInstallmentNavigator = ({ itemCatalog }) => {

    const getTitleInsurance = () => {
        const { AirtimeServiceGroupName } = itemCatalog ?? "";
        const conertServiceGroupName = AirtimeServiceGroupName?.toUpperCase();
        return conertServiceGroupName;
    };
    return (
        <CollectInstallmentStack.Navigator
            initialRouteName={"CollectInstallmentPayments"}
            headerMode={"screen"}
        >
            <CollectInstallmentStack.Screen
                name={"PayBillAirtimeService"}
                component={PayBillAirtimeService}
                options={{
                    headerShown: false,
                    gestureEnabled: false
                }}
            />
            <CollectInstallmentStack.Screen
                name={"SaleOrderPayment"}
                component={SaleOrderPayment}
                options={{
                    header: ({ navigation }) => (<OrderPaymentHeader
                        navigation={navigation}
                        key={"OrderPaymentHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <CollectInstallmentStack.Screen
                name={"HistorySell"}
                component={HistorySell}
                options={{
                    header: ({ navigation }) => (<MenuHeader
                        openDrawer={onOpenDrawer(navigation)}
                        title={translate("collection.managerment_multicat_industry_transactions")}
                        key={"HistorySell"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <CollectInstallmentStack.Screen
                name={"QRPayment"}
                component={QRPayment}
                options={{
                    header: NoneHeader,
                    gestureEnabled: false
                }}
            />
            <CollectInstallmentStack.Screen
                name={"ReprintSaleOrder"}
                component={ReprintSaleOrder}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"ReprintSOHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <CollectInstallmentStack.Screen
                name={"DeleteSO"}
                component={DeleteSO}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"DeleteSOHeader"}
                        isDelete={true}
                    />),
                    gestureEnabled: false
                }}
            />
            <CollectInstallmentStack.Screen
                name={"CancelSO"}
                component={CancelSO}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"CancelSOHeader"}
                        isDelete={true}
                    />),
                    gestureEnabled: false
                }}
            />
            <CollectInstallmentStack.Screen
                name={"CancelService"}
                component={CancelService}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={'TẠO YÊU CẦU HOÀN TIỀN'}
                        key={"CollectionOne"}
                    />),
                    gestureEnabled: false
                }}
            />
            <CollectInstallmentStack.Screen
                name={"RefundHistory"}
                component={RefundHistory}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={onGoBack(navigation)}
                        title={'XEM LỊCH SỬ HOÀN TIỀN'}
                        key={"RefundHistory"}
                    />),
                    gestureEnabled: false
                }}
            />
            <CollectInstallmentStack.Screen
                name={"SearchCollection"}
                component={SearchCollection}
                options={{
                    header: ({ navigation }) => (<MenuHeader
                        openDrawer={onOpenDrawer(navigation)}
                        title={translate("collection.managerment_multicat_industry_transactions")}
                        key={"HistorySell"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <CollectInstallmentStack.Screen
                name={"PrintCoupon"}
                component={PrintCoupon}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"PrintCoupon"}
                    />),
                    gestureEnabled: false
                }}
            />
            <CollectInstallmentStack.Screen
                name={"BankAirtimeService"}
                component={BankAirtimeService}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={navigation.goBack}
                            title={getTitleInsurance()}
                            key={"CollectionAirtimeService"}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false,
                }}
            />
            <CollectInstallmentStack.Screen
                name={"CollectionNavigator"}
                component={CollectionNavigator}
                options={{
                    headerShown: false,
                    gestureEnabled: false
                }}
            />
        </CollectInstallmentStack.Navigator>
    );
}

const mapStateToProps = function (state) {
    return {
        itemCatalog: state.collectionReducer.itemCatalog,
    }
}

const mapDispatchToProps = function (dispatch) {
    return {

    }
}

export default connect(mapStateToProps, mapDispatchToProps)(CollectInstallmentNavigator);

const onOpenDrawer = (navigation) => () => {
    Keyboard.dismiss();
    navigation.openDrawer();
}

const onGoBack = (navigation) => () => {
    navigation.reset({
        index: 0,
        routes: [{ name: 'SearchCollection' }]
    });
};
