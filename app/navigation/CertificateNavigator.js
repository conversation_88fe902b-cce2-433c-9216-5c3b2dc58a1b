import React from 'react';
import { Keyboard } from 'react-native';
import { createStackNavigator } from '@react-navigation/stack';
import { MenuHeader } from "@header";
import { translate } from "@translate";
import { CertificateScreen } from "../container/ServiceCertificate/index"
const CertificateStack = createStackNavigator();

const CertificateNavigator = () => {
    return (
        <CertificateStack.Navigator
            initialRouteName={"Certificate"}
            screenOptions={{
                header: ({ navigation }) => (<MenuHeader
                    openDrawer={onOpenDrawer(navigation)}
                    title={"CHỨNG NHẬN DỊCH VỤ"}
                    key={"Certificate"}
                    navigation={navigation}
                />)
            }}
        >
            <CertificateStack.Screen
                name={"ScreenCertificate"}
                component={CertificateScreen}
                options={{ headerShown: "none", gestureEnabled: false }}
            />
        </CertificateStack.Navigator >
    );
};

export default CertificateNavigator;

const onOpenDrawer = (navigation) => () => {
    Keyboard.dismiss();
    navigation.openDrawer();
}