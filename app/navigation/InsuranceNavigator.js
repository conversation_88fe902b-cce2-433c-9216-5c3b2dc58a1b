import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { Keyboard } from 'react-native';
import { BackHeader, MenuHeader, NoneHeader } from "@header";
const InsuranceStack = createStackNavigator();
import { translate } from '@translate';
import Insurance from '../container/Insurance';
import CreateDeclaration from '../container/Insurance/Screen/CreateDeclaration';
import DeclarationInformation from '../container/Insurance/Screen/DeclarationInformation';
import InsuranceManager from '../container/InsuranceManager';
import SaleOrderPayment from '../container/SaleOrderPayment';
import OrderPaymentHeader from "../container/SaleOrderPayment/Header/BackHeader";
import QRPayment from '../container/SaleOrderPayment/QRPayment';

const InsuranceNavigator = () => {
    return (
        <InsuranceStack.Navigator
            initialRouteName={"Insurance"}
            headerMode={"screen"}
        >
            <InsuranceStack.Screen
                name={"Insurance"}
                component={Insurance}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"BHYT/BHXH"}
                        key={"Insurance"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceStack.Screen
                name={"CreateDeclaration"}
                component={CreateDeclaration}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"TẠO TỜ KHAI"}
                        key={"CreateDeclaration"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceStack.Screen
                name={"DeclarationInformation"}
                component={DeclarationInformation}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"TẠO TỜ KHAI"}
                        key={"DeclarationInformation"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceStack.Screen
                name={"InsuranceManager"}
                component={InsuranceManager}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"DANH SÁCH TỜ KHAI"}
                        key={"InsuranceManager"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceStack.Screen
                name={"SaleOrderPayment"}
                component={SaleOrderPayment}
                options={{
                    header: ({ navigation }) => (<OrderPaymentHeader
                        navigation={navigation}
                        key={"OrderPaymentHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <InsuranceStack.Screen
                name={"QRPayment"}
                component={QRPayment}
                options={{
                    header: NoneHeader,
                    gestureEnabled: false
                }}
            />
        </InsuranceStack.Navigator>
    );
}

export default InsuranceNavigator

const onOpenDrawer = (navigation) => () => {
    Keyboard.dismiss();
    navigation.openDrawer();
}

