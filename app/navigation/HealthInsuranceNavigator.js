import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { BackHeader } from "@header";
import HealthInsurance from '../container/HealthInsurance';
import HealthInsuranceSteps from '../container/HealthInsurance/HealthInsuranceSteps';
import SaleOrderPayment from '../container/SaleOrderPayment';
import OrderPaymentHeader from "../container/SaleOrderPayment/Header/BackHeader";
import PrintCertificateMIC from '../container/HealthInsurance/PrintPaperType/PrintCertificateMIC';
import PrintGuaranteeMIC from '../container/HealthInsurance/PrintPaperType/PrintGuaranteeMIC';
import ReprintSaleOrder from '../container/SaleOrderManager/ReprintSaleOrder';
import OrderBackHeader from "../container/SaleOrderManager/Header/BackHeader";
import DeleteSO from '../container/SaleOrderManager/DeleteSO';
import CancelSO from '../container/SaleOrderManager/CancelSO';
import CancelService from '../container/HealthInsurance/HistorySell/CancelService';
import RefundHistory from '../container/HealthInsurance/HistorySell/RefundHistory';

const HealthInsuranceStack = createStackNavigator();

const HealthInsuranceNavigator = () => {
    return (
        <HealthInsuranceStack.Navigator
            initialRouteName={"HealthInsurance"}
            headerMode={"screen"}
        >
            <HealthInsuranceStack.Screen
                name={"HealthInsurance"}
                component={HealthInsurance}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"THU HỘ BẢO HIỂM SỨC KHOẺ"}
                        key={"HealthInsurance"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <HealthInsuranceStack.Screen
                name={"HealthInsuranceSteps"}
                component={HealthInsuranceSteps}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"THU HỘ BẢO HIỂM SỨC KHOẺ"}
                        key={"HealthInsuranceSteps"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <HealthInsuranceStack.Screen
                name={"SaleOrderPayment"}
                component={SaleOrderPayment}
                options={{
                    header: ({ navigation }) => (<OrderPaymentHeader
                        navigation={navigation}
                        key={"OrderPaymentHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <HealthInsuranceStack.Screen
                name={"PrintCertificateMIC"}
                component={PrintCertificateMIC}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"IN GIẤY CHỨNG NHẬN BẢO HIỂM"}
                        key={"Insurance"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <HealthInsuranceStack.Screen
                name={"PrintGuaranteeMIC"}
                component={PrintGuaranteeMIC}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"IN THẺ BẢO LÃNH"}
                        key={"Insurance"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <HealthInsuranceStack.Screen
                name={"ReprintSaleOrder"}
                component={ReprintSaleOrder}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"ReprintSOHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
            <HealthInsuranceStack.Screen
                name={"DeleteSO"}
                component={DeleteSO}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"DeleteSOHeader"}
                        isDelete={true}
                    />),
                    gestureEnabled: false
                }}
            />
            <HealthInsuranceStack.Screen
                name={"CancelSO"}
                component={CancelSO}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"CancelSOHeader"}
                        isDelete={true}
                    />),
                    gestureEnabled: false
                }}
            />
            <HealthInsuranceStack.Screen
                name={"CancelService"}
                component={CancelService}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={navigation.goBack}
                        title={"THU HỘ BẢO HIỂM SỨC KHOẺ"}
                        key={"Insurance"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
            <HealthInsuranceStack.Screen
                name={"RefundHistory"}
                component={RefundHistory}
                options={{
                    header: ({ navigation }) => (<BackHeader
                        onGoBack={onGoBack(navigation)}
                        title={"XEM LỊCH SỬ HOÀN TIỀN"}
                        key={"Insurance"}
                        navigation={navigation}
                    />),
                    gestureEnabled: false
                }}
            />
        </HealthInsuranceStack.Navigator>
    );
}

const onGoBack = (navigation) => () => {
    navigation.reset({
        index: 0,
        routes: [{ name: 'HealthInsurance' }]
    });
};

export default HealthInsuranceNavigator

