import React, { useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { enableScreens } from 'react-native-screens';
import { connect } from 'react-redux';
import Toast from 'react-native-toast-message';
import { CONFIG } from '@constants';
import LoginScreen from '../container/Login';
import SplashScreen from '../container/Splash';
import OtpConfirmScreen from '../container/OtpConfirm';
import DrawerNavigator from "./DrawerNavigator";
import DrawerOflineNavigator from "./DrawerOflineNavigator";
import { TOAST_CONFIG } from '../container/AnKhangNew/constants';
import ScreenshotFeedbackNavigator from './ScreenshotFeedbackNavigator';
import { AudioProvider } from '@context';


enableScreens();
const MainStack = createStackNavigator();

const AppContainer = ({ isLoading, isSignedIn, isSignout, isOffline, isChangeDevice }) => {

    if (isLoading) {
        return <SplashScreen />;
    }
    const ProductionScreen = isChangeDevice ? OtpConfirmScreen : (isOffline ? DrawerOflineNavigator : DrawerNavigator);
    const MainScreen = CONFIG.isPRODUCTION ? ProductionScreen : DrawerNavigator;
    return (
        <NavigationContainer>
            <AudioProvider>
                <MainStack.Navigator
                    initialRouteName={"Main"}
                    screenOptions={{
                        headerShown: false
                    }}>
                    {
                        isSignedIn
                            ?
                            <MainStack.Group>
                                <MainStack.Screen
                                    name={"Main"}
                                    component={MainScreen}
                                />
                                <MainStack.Screen
                                    name={"Feedback"}
                                    component={ScreenshotFeedbackNavigator}
                                />
                            </MainStack.Group>
                            :
                            <MainStack.Group>
                                <MainStack.Screen
                                    name={"Login"}
                                    component={LoginScreen}
                                    options={{
                                        animationTypeForReplace: isSignout ? 'pop' : 'push',
                                    }}
                                />
                            </MainStack.Group>
                    }
                </MainStack.Navigator>
                <Toast
                    ref={(ref) => Toast.setRef(ref)}
                    config={TOAST_CONFIG}
                    position="bottom"
                    visibilityTime={2000}
                />
            </AudioProvider>
        </NavigationContainer>
    );
};

const mapStateToProps = (state) => ({
    isLoading: state.appSwitchReducer.isLoading,
    isSignedIn: state.appSwitchReducer.isSignedIn,
    isSignout: state.appSwitchReducer.isSignout,
    isOffline: state.appSwitchReducer.isOffline,
    isChangeDevice: state.appSwitchReducer.isChangeDevice
});

const mapDispatchToProps = () => ({});

export default connect(mapStateToProps, mapDispatchToProps)(AppContainer);
