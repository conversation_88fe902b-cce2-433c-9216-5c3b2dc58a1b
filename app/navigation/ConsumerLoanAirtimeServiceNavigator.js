import React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import { Keyboard, StyleSheet } from "react-native";
import { BackHeader, MenuHeader, NoneHeader } from "@header";
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import { translate } from '@translate';
const ConsumerLoanAirtimeServiceStack = createStackNavigator();
import ConsumerLoanAirtimeService from "../container/ConsumerLoanAirtimeService";
import MenuConsumerLoan from "../container/ConsumerLoanAirtimeService/Screen/MenuConsumerLoan";
import NoteInformationLoan from "../container/ConsumerLoanAirtimeService/Screen/NoteInformationLoan";
import OTPConsumerAirtimeService from "../container/ConsumerLoanAirtimeService/Screen/OTPConsumerAirtimeService";
import PullConsumerLoanPackage from "../container/ConsumerLoanAirtimeService/Screen/PullConsumerLoanPackage";
import WebViewScreen from "../container/ConsumerLoanAirtimeService/Screen/WebViewScreen";
import QrCodeCathayLife from "../container/ConsumerLoanAirtimeService/Screen/QrCodeCathayLife";
import QrCodeCathayLifeOld from "../container/ConsumerLoanAirtimeService/Screen/QrCodeCathayLifeOld";
import ReprintSaleOrder from "../container/SaleOrderManager/ReprintSaleOrder";
import OrderBackHeader from "../container/SaleOrderManager/Header/BackHeader";

const ConsumerLoanAirtimeServiceNavigator = ({
    itemCatalog,
    updateHeaderAirtime,
}) => {

    const getTitleLoanConsumer = () => {
        const { AirtimeServiceGroupName } = itemCatalog ?? "";
        const conertServiceGroupName = AirtimeServiceGroupName?.toUpperCase();
        return conertServiceGroupName;
    };

    const getTitleAirtimeList = () => {
        const { AirTimeTransactionTypeName } = updateHeaderAirtime ?? "";
        const conertTransactionTypeName = AirTimeTransactionTypeName?.toUpperCase();
        return conertTransactionTypeName;
    };
    return (
        <ConsumerLoanAirtimeServiceStack.Navigator
            initialRouteName={"ConsumerLoanAirtimeServiceNavigator"}
            headerMode={"screen"}
        >
            <ConsumerLoanAirtimeServiceStack.Screen
                name={"ConsumerLoanAirtimeService"}
                component={ConsumerLoanAirtimeService}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={navigation.goBack}
                            title={getTitleLoanConsumer()}
                            key={"ConsumerLoanAirtimeService"}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false,
                }}
            />
            <ConsumerLoanAirtimeServiceStack.Screen
                name={"MenuConsumerLoan"}
                component={MenuConsumerLoan}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={navigation.goBack}
                            title={getTitleAirtimeList()}
                            key={"MenuConsumerLoan"}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false,
                }}
            />
            <ConsumerLoanAirtimeServiceStack.Screen
                name={"NoteInformationLoan"}
                component={NoteInformationLoan}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={navigation.goBack}
                            title={getTitleAirtimeList()}
                            key={"NoteInformationLoan"}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false,
                }}
            />
            <ConsumerLoanAirtimeServiceStack.Screen
                name={"OTPConsumerAirtimeService"}
                component={OTPConsumerAirtimeService}
                options={{
                    header: ({ navigation }) => (
                        <BackHeader
                            onGoBack={navigation.goBack}
                            title={getTitleAirtimeList()}
                            key={"OTPConsumerAirtimeService"}
                            navigation={navigation}
                        />
                    ),
                    gestureEnabled: false,
                }}
            />
            <ConsumerLoanAirtimeServiceStack.Screen
                name={"PullConsumerLoanPackage"}
                component={PullConsumerLoanPackage}
                options={{
                    headerShown: false,
                    gestureEnabled: false,
                }}
            />
            <ConsumerLoanAirtimeServiceStack.Screen
                name={"WebViewScreen"}
                component={WebViewScreen}
                options={{
                    headerShown: false,
                    gestureEnabled: false,
                }}
            />
            <ConsumerLoanAirtimeServiceStack.Screen
                name={"QrCodeCathayLife"}
                component={QrCodeCathayLife}
                options={{
                    header: NoneHeader,
                    gestureEnabled: false
                }}
            />
            <ConsumerLoanAirtimeServiceStack.Screen
                name={"QrCodeCathayLifeOld"}
                component={QrCodeCathayLifeOld}
                options={{
                    header: NoneHeader,
                    gestureEnabled: false
                }}
            />
            <ConsumerLoanAirtimeServiceStack.Screen
                name={"ReprintSaleOrder"}
                component={ReprintSaleOrder}
                options={{
                    header: ({ navigation }) => (<OrderBackHeader
                        navigation={navigation}
                        key={"ReprintSOHeader"}
                    />),
                    gestureEnabled: false
                }}
            />
        </ConsumerLoanAirtimeServiceStack.Navigator>
    );
};

const mapStateToProps = function (state) {
    return {
        updateHeaderAirtime: state.consumerLoanAirtimeServiceReducer.updateHeaderAirtime,
        itemCatalog: state.collectionReducer.itemCatalog,
    };
};

const mapDispatchToProps = function (dispatch) {
    return {

    };
};

const onGoBack = (navigation) => () => {
    navigation.reset({
        index: 0,
        routes: [{ name: "InsuranceAirtimeService" }],
    });
};

export default connect(
    mapStateToProps,
    mapDispatchToProps
)(ConsumerLoanAirtimeServiceNavigator);

const styles = StyleSheet.create({});

const onOpenDrawer = (navigation) => () => {
    Keyboard.dismiss();
    navigation.openDrawer();
}