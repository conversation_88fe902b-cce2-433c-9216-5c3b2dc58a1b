// BottomSheetContext.js
import React, { createContext, useContext, useState, useRef } from 'react';
import { BottomSheet, MyText, Icon } from '@components';
import { StyleSheet, View } from 'react-native';
import { BottomSheetModalProvider, TouchableWithoutFeedback } from '@gorhom/bottom-sheet';
import { COLORS } from '@styles';

const BottomSheetContext = createContext();

export const BottomSheetProvider = ({ children }) => {
    const [isVisible, setIsVisible] = useState(false);
    const [content, setContent] = useState(null);
    const bottomSheetRef = useRef(null);

    const showBottomSheet = (data) => {
        setContent(data);
        setIsVisible(true);
        bottomSheetRef.current?.present();
    };

    const hideBottomSheet = () => {
        setIsVisible(false);
        setContent(null);
        bottomSheetRef.current?.close();
    };

    const handleComponent = () => {
        return (
            <View style={styles.handle_wrapper}>
                <View style={{ flex: 1 }} />
                <View
                    style={{
                        flex: 6,
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}>
                    <MyText
                        style={{
                            fontWeight: 'bold',
                            color: COLORS.txt000000
                        }}
                        text={content.title ?? "THE GIOI DI DONG"}
                    />
                </View>
                <View style={{ flex: 1 }}>
                    <TouchableWithoutFeedback
                        style={{ marginLeft: 10 }}
                        onPress={() => {
                            bottomSheetRef.current?.dismiss();
                        }}>
                        <Icon
                            iconSet={'MaterialIcons'}
                            name={'clear'}
                            color={COLORS.txt000000}
                            size={22}
                        />
                    </TouchableWithoutFeedback>
                </View>
            </View>
        );
    };

    return (
        <BottomSheetModalProvider>
            <BottomSheetContext.Provider value={{ showBottomSheet, hideBottomSheet }}>
                {children}
                {isVisible && (
                    <BottomSheet
                        snapPoints={['70%']}
                        bs={bottomSheetRef}
                        handleComponent={handleComponent}
                        enableContentPanningGesture={false}
                        enableHandlePanningGesture={false}
                        onChangeStatusSheet={(index) => {
                            if (index === -1) {
                                hideBottomSheet()
                            }
                        }}>
                        {content ? content.component : null}
                    </BottomSheet>
                )}
            </BottomSheetContext.Provider>
        </BottomSheetModalProvider>

    );
};

// Hook để dùng trong các component con
export const useBottomSheet = () => {
    const context = useContext(BottomSheetContext);
    if (!context) {
        throw new Error('Cõ lỗi xảy ra.');
    }
    return context;
};

const styles = StyleSheet.create({
    handle_wrapper: {
        backgroundColor: COLORS.bgFFFFFF,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
        height: 50,
        elevation: 2,
        borderTopStartRadius: 22,
        borderTopEndRadius: 22,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: COLORS.bgC4C4C4
    }
});