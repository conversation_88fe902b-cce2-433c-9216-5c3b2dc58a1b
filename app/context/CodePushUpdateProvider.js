import React, { useContext, useMemo, useState, createContext, useRef, useEffect, memo } from 'react';
import CodePush from 'react-native-code-push';
import { ENUM } from '@constants';
import { Alert } from 'react-native';

const { CODE_PUSH_STATUS } = ENUM;
const UPDATE_TIMEOUT = 10 * 1000;

const CodePushUpdateContext = createContext({
	status: CODE_PUSH_STATUS.CHECKING_FOR_UPDATE,
	progress: 0
});

const CodePushUpdateProvider = memo(function ({ children }) {

	const syncTimeout = useRef(null);
	const [status, setStatus] = useState(CODE_PUSH_STATUS.CHECKING_FOR_UPDATE);
	const [progress, setProgress] = useState(0);

	const didMount = () => {
		CodePush.checkForUpdate().then(onUpdateSync).catch(onError);
		return clearSyncTimeout;
	};

	const onSyncBackground = () => {
		syncTimeout.current = CODE_PUSH_STATUS.SYNC_IN_BACKGROUND;
		setStatus(CODE_PUSH_STATUS.SYNC_IN_BACKGROUND);
	};

	const clearSyncTimeout = () => {
		if (syncTimeout.current > 0) {
			clearTimeout(syncTimeout.current);
		}
	};

	const codePushStatusDidChange = (syncStatus) => {
		console.log('codePushStatusDidChange', syncStatus);
		switch (syncStatus) {
			case CODE_PUSH_STATUS.DOWNLOADING_PACKAGE:
				syncTimeout.current = setTimeout(onSyncBackground, UPDATE_TIMEOUT);
				return;
			case CODE_PUSH_STATUS.INSTALLING_UPDATE:
				CodePush.notifyAppReady();
				return;
			case CODE_PUSH_STATUS.UPDATE_INSTALLED:
			case CODE_PUSH_STATUS.UNKNOWN_ERROR:
				clearSyncTimeout();
				return;
			default:
				return;
		}
	};

	const codePushDownloadDidProgress = (progressInfo) => {
		const { receivedBytes, totalBytes } = progressInfo;
		const value = receivedBytes / totalBytes;
		setProgress(value);
	};

	const onSuccess = (remotePackage) => (syncStatus) => {
		if (syncTimeout.current == CODE_PUSH_STATUS.SYNC_IN_BACKGROUND) {
			showUpdateDialog(remotePackage);
		} else {
			setStatus(syncStatus);
		}
	};

	const onError = (error) => {
		console.log('CodePush.sync error', error);
		setStatus(CODE_PUSH_STATUS.UNKNOWN_ERROR);
	};

	const onUpdateSync = (remotePackage) => {
		if (remotePackage?.failedInstall) {
			CodePush.clearUpdates();
		}
		const option = {
			installMode: CodePush.InstallMode.ON_NEXT_RESUME,
			mandatoryInstallMode: CodePush.InstallMode.ON_NEXT_RESUME
		};
		CodePush.sync(
			option,
			codePushStatusDidChange,
			codePushDownloadDidProgress
		).then(onSuccess(remotePackage)).catch(onError);
	};

	useEffect(didMount, []);

	const context = useMemo(() => ({
		status,
		progress
	}), [status, progress]);

	return <CodePushUpdateContext.Provider value={context}>{children}</CodePushUpdateContext.Provider>;
});

const useCodePushUpdateContext = () => useContext(CodePushUpdateContext);

export { CodePushUpdateProvider, CodePushUpdateContext, useCodePushUpdateContext };

const showUpdateDialog = (info) => {
	const butuons = [
		{
			text: 'Bỏ qua',
			style: 'destructive',
			isSelected: !info?.isMandatory
		},
		{
			text: 'Đồng bộ',
			style: 'default',
			onPress: CodePush.restartApp,
			isSelected: true
		},
	];
	Alert.alert(``, `Ứng dụng đã có phiên bản cập nhật mới. Vui lòng đồng bộ dữ liệu để sử dụng chức năng mới nhất!`,
		butuons.filter(ele => ele.isSelected),
		{ cancelable: false }
	);
};