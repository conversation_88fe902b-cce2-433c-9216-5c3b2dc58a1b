import React, {
    useState,
    useEffect,
    createContext,
    useContext,
    useRef
} from 'react';
import { Platform, Alert, Linking } from 'react-native';
import AudioRecord from 'react-native-audio-record';
import { check, PERMISSIONS, request, RESULTS } from 'react-native-permissions';
import RNFS from 'react-native-fs';
import { AppState } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { apiBase, METHOD } from '@config';
import { API_CONST } from '@constants';
import { useSelector } from 'react-redux';

const { API_UPLOAD_FILE_AUDIO } = API_CONST;

const REMINDER_TIME = 180; // 3 phút

export const AudioContext = createContext();

export const AudioProvider = ({ children }) => {
    const [isRecording, setIsRecording] = useState(false);
    const timerRef = useRef(null);
    const navigation = useNavigation();
    const { storeID, userName } = useSelector((state) => state.userReducer);

    const options = {
        sampleRate: 16000,
        channels: 1,
        bitsPerSample: 16,
        wavFile: 'recording.wav'
    };

    useEffect(() => {
        setupAudio();
        return () => {
            if (isRecording) {
                AudioRecord.stop().catch(() => { });
            }
        };
    }, []);

    useEffect(() => {
        return () => {
            if (timerRef.current) {
                clearTimeout(timerRef.current);
            }
        };
    }, []);

    useEffect(() => {
        const subscription = AppState.addEventListener(
            'change',
            (nextAppState) => {
                if (nextAppState == 'active' && isRecording) {
                    Alert.alert(
                        'THÔNG BÁO',
                        'Bạn có muốn tiếp tục ghi âm cuộc Tư Vấn không?',
                        [
                            {
                                text: 'Dừng',
                                onPress: () => stopRecording()
                            },
                            {
                                text: 'Tiếp Tục',
                                onPress: () => { }
                            }
                        ],
                        { cancelable: false }
                    );
                }
            }
        );

        return () => subscription.remove();
    }, [isRecording]);

    useEffect(() => {
        const unsubscribe = navigation.addListener('state', (e) => {
            const currentTabRoute = e.data?.state?.routes?.[e.data.state.index];
            const nestedState = currentTabRoute?.state;
            const nestedRoute = nestedState?.routes?.[nestedState.index];
            const screenName =
                nestedRoute?.state?.routes?.[nestedRoute?.state?.index]?.name;
            const allowedScreens = [
                'Sale',
                'Detail',
                'ShoppingCart',
                'Loyalty',
                'InstallmentOTP',
                'SaleOrderCart'
            ];
            if (!allowedScreens.includes(screenName) && isRecording) {
                stopRecording();
            }
        });

        return unsubscribe;
    }, [navigation, isRecording]);

    const setupAudio = async () => {
        try {
            await AudioRecord.init(options);
        } catch (error) {
            console.error('Failed audio:', error);
        }
    };

    // Xử lý quyền
    const checkAndRequestPermission = async () => {
        const permission = Platform.select({
            android: PERMISSIONS.ANDROID.RECORD_AUDIO,
            ios: PERMISSIONS.IOS.MICROPHONE
        });

        try {
            const status = await check(permission);

            switch (status) {
                case RESULTS.UNAVAILABLE:
                    return 'Thiết bị không hỗ trợ tính năng ghi âm';
                case RESULTS.DENIED:
                    const requestedStatus = await request(permission);
                    return requestedStatus === RESULTS.GRANTED
                        ? ''
                        : 'Ứng dụng cần quyền micro để hoạt động';
                case RESULTS.GRANTED:
                    return '';
                case RESULTS.BLOCKED:
                    return 'Vui lòng mở cài đặt ứng dụng và bật quyền micro để tiếp tục';
                default:
                    return 'Ứng dụng cần quyền micro để hoạt động';
            }
        } catch (error) {
            console.error('Lỗi kiểm tra quyền:', error);
            return 'Ứng dụng cần quyền micro để hoạt động';
        }
    };

    // Bắt đầu ghi âm
    const startRecording = async () => {
        const messPermission = await checkAndRequestPermission();

        if (!!messPermission) {
            Alert.alert('Không thể ghi âm', messPermission, [
                { text: 'Đồng ý', onPress: () => Linking.openSettings() }
            ]);
            return;
        }

        try {
            await AudioRecord.init(options);

            await AudioRecord.start();
            setIsRecording(true);
            console.log('Recording started');

            const startTime = Date.now();
            let reminderCount = 1;

            const updateTimer = () => {
                const currentTime = Math.floor((Date.now() - startTime) / 1000);
                if (currentTime >= REMINDER_TIME * reminderCount) {
                    Alert.alert(
                        'THÔNG BÁO',
                        'Hiện tại cuộc Tư Vấn vẫn đang được ghi âm, anh/chị có cần tạm dừng không?',
                        [
                            {
                                text: 'Dừng',
                                onPress: () => stopRecording()
                            },
                            {
                                text: 'Tiếp tục',
                                onPress: () => {
                                    reminderCount++;
                                    timerRef.current = setTimeout(
                                        updateTimer,
                                        1000
                                    );
                                }
                            }
                        ],
                        { cancelable: false }
                    );
                } else {
                    timerRef.current = setTimeout(updateTimer, 1000);
                }
            };

            timerRef.current = setTimeout(updateTimer, 1000);
        } catch (error) {
            console.error('Failed recording:', error);
        }
    };

    const stopRecording = async (ids) => {
        if (timerRef.current) {
            clearTimeout(timerRef.current);
            timerRef.current = null;
        }
        setIsRecording(false);

        try {
            const filePath = await AudioRecord.stop();
            console.log('Recording stopped, file:', filePath);
            // const publicPath = `${RNFS.DownloadDirectoryPath}/recording.wav`;
            // await RNFS.copyFile(filePath, publicPath);
            // console.log('File copied to:', publicPath);

            // adb pull /sdcard/Download/recording.wav ~/Desktop/
            await processAndUploadAudio(filePath, ids);
        } catch (error) {
            console.log('Failed stop recording:', error);
        }
    };

    const processAndUploadAudio = async (filePath, ids) => {
        try {
            const base64Data = await RNFS.readFile(filePath, 'base64');

            await uploadAudioFile(base64Data, filePath, ids);

            // Xóa file tạm sau khi upload
            const exists = await RNFS.exists(filePath);
            if (exists) {
                await RNFS.unlink(filePath);
            }
        } catch (error) {
            console.log('Error processing file:', error);
            throw error;
        }
    };

    // Gửi file audio lên server
    const uploadAudioFile = async (base64Data, filePath, ids = null) => {
        const formData = new FormData();
        formData.append('Files', {
            uri: `file://${filePath}`,
            type: 'audio/wav',
            name: 'ghiam.wav'
        });
        formData.append('loginStoreId', storeID);
        formData.append('loginUser', userName);
        formData.append('saleOrderId', ids);

        try {
            const dataUpload = await new Promise((resolve, reject) => {
                apiBase(API_UPLOAD_FILE_AUDIO, METHOD.POST, formData, {
                    isCustomToken: true,
                    isUpload: true
                })
                    .then((response) => {
                        console.log('uploadAudioFile success:', response);
                        return response;
                    })
                    .catch((error) => {
                        console.log('uploadAudioFile error:', error);
                        return {};
                    });
            });
            console.log('Audio file ready for upload:', dataUpload);
        } catch (error) {
            console.log('Upload error:', error);
            throw error;
        }
    };

    return (
        <AudioContext.Provider
            value={{
                isRecording,
                startRecording,
                stopRecording,
                setIsRecording
            }}>
            {children}
        </AudioContext.Provider>
    );
};

export const useAudio = () => {
    const context = useContext(AudioContext);
    if (!context) {
        throw new Error('useAudio must be used within AudioProvider');
    }
    return context;
};
