import React, { useState, useRef, useEffect } from 'react';
import { API_CONST, constants, ENUM } from '@constants';
import { View, Keyboard, Alert } from 'react-native';
import { NoteInput, ImeiInput, ExpirationDate, ErrorReason, LinkCamera, ImageAttachment, NewStatus, TrueProductIdInput } from '../components';
import { CaptureCamera, showBlockUI, hideBlockUI, ScanBarcodeML } from '@components';
import { translate } from '@translate';
import { getImageCDN } from '../../../ActiveSimManager/action';
import { launchImageLibrary } from 'react-native-image-picker';
import { helper } from '@common';
import { PRODUCT_STATE_TYPE } from '../ReportProductStateEnum';
import { SelectedProductInfo } from './components';
const { FILE_PATH: { CHANGE_CODE_BUSINESS } } = ENUM

const ChangeCodeBusiness = ({
  reportState,
  errorInfo = {},
  ////////////////
  isRequiredNote,
  note,
  onChangeNote,
  ////////////////
  isRequiredAttachments,
  imageUrls = [],
  setImageUrls,
  isHasImei,
  checkTrueImei,
  productId,
  checkSpareImei,
  isUpdate,
  isCheckedImei = true,
  spareProductImei,
  setSpareProductImei,
  currentAttachments,
  ////////////////
  trueProductSelected,
  onSelectTrueProduct,
  listProductByInventoryProcessCode,
  getListProductByInventoryProcess,
  isShowTrueProductInfo,
  reportQuantity,
  ////////////////
  selectedErrorReason,
  onSelectErrorReason,
  otherErrorReasonNote,
  onChangeOtherErrorReasonNote,
  isRequiredLinkCamera,
  linkCamera,
  onChangeLinkCamera,
  isRequiredBatchNo,
  expirationDateText,
  onChangeExpirationDateText,
  onSubmitExpirationDateText,
  listProductBatch,
  onSelectBatch,
  selectedProductBatch,
  showDatePicker,
  isInvalidChangeInventoryStatusId
}) => {

  const [isVisibleCamera, setIsVisibleCamera] = useState(false);
  const [isVisibleScanCamera, setIsVisibleScanCamera] = useState(false);
  const currentIndex = useRef(0);
  const isScanProductId = useRef(false);

  const [trueProductId, setTrueProductId] = useState("");
  const [trueProductImei, setTrueProductImei] = useState("");
  const [isShowListTrueProduct, setIsShowListTrueProduct] = useState(false);
  const [editableImei, setEditableImei] = useState(false);

  const { listCauErroPIMBO, changeInventoryStatus } = errorInfo;

  const reportStateID = reportState.REALPRODUCTSTATUSTYPEID;
  const isShowTrueProductIdInput = reportStateID == PRODUCT_STATE_TYPE.INCORRECT_PRODUCT_AVA ||
    reportStateID == PRODUCT_STATE_TYPE.INCORRECT_PRODUCT ||
    reportStateID == PRODUCT_STATE_TYPE.WRONG_CODE_PRODUCT;
  const isShowTrueImeiInput = isHasImei && (
    reportStateID == PRODUCT_STATE_TYPE.INCORRECT_PRODUCT_AVA ||
    reportStateID == PRODUCT_STATE_TYPE.INCORRECT_PRODUCT ||
    reportStateID == PRODUCT_STATE_TYPE.WRONG_CODE_PRODUCT
  );
  const isShowSpareImeiInput = isHasImei && (
    reportStateID == PRODUCT_STATE_TYPE.SPARE_PRODUCT_AVA ||
    reportStateID == PRODUCT_STATE_TYPE.SPARE_PRODUCT
  );
  const isShowErrorReason = helper.IsNonEmptyArray(listCauErroPIMBO);
  const isShowLinkCamera = reportStateID == PRODUCT_STATE_TYPE.MANUFACTURE_EXTERNAL_ERROR ||
    reportStateID == PRODUCT_STATE_TYPE.EXTERNAL_ERROR_SALEABLE ||
    reportStateID == PRODUCT_STATE_TYPE.EXTERNAL_ERROR_NOT_SALEABLE ||
    reportStateID == PRODUCT_STATE_TYPE.INTERNAL_ERROR_PRODUCT ||
    reportStateID == PRODUCT_STATE_TYPE.SALEABLE_PRODUCT ||
    reportStateID == PRODUCT_STATE_TYPE.SEAL_ERROR ||
    reportStateID == PRODUCT_STATE_TYPE.PHYSICAL_IMPACT_ERROR ||
    reportStateID == PRODUCT_STATE_TYPE.FLAW_SALEABLE_PRODUCT ||
    reportStateID == PRODUCT_STATE_TYPE.CHANGED_TO_USED_STATUS ||
    reportStateID == PRODUCT_STATE_TYPE.OTHER_CHANGE_CODE_STATUS ||
    reportStateID == PRODUCT_STATE_TYPE.CHANGE_ERROR_STATE_WITH_NEW_SEAL;
  const isShowImageAttachment = reportStateID == PRODUCT_STATE_TYPE.SPARE_PRODUCT_AVA ||
    reportStateID == PRODUCT_STATE_TYPE.SPARE_PRODUCT ||
    reportStateID == PRODUCT_STATE_TYPE.INCORRECT_PRODUCT_AVA ||
    reportStateID == PRODUCT_STATE_TYPE.INCORRECT_PRODUCT ||
    reportStateID == PRODUCT_STATE_TYPE.WRONG_CODE_PRODUCT ||
    reportStateID == PRODUCT_STATE_TYPE.MANUFACTURE_EXTERNAL_ERROR ||
    reportStateID == PRODUCT_STATE_TYPE.EXTERNAL_ERROR_SALEABLE ||
    reportStateID == PRODUCT_STATE_TYPE.EXTERNAL_ERROR_NOT_SALEABLE ||
    reportStateID == PRODUCT_STATE_TYPE.CANCEL_PRODUCT ||
    reportStateID == PRODUCT_STATE_TYPE.INTERNAL_ERROR_PRODUCT ||
    reportStateID == PRODUCT_STATE_TYPE.SALEABLE_PRODUCT ||
    reportStateID == PRODUCT_STATE_TYPE.SEAL_ERROR ||
    reportStateID == PRODUCT_STATE_TYPE.PHYSICAL_IMPACT_ERROR ||
    reportStateID == PRODUCT_STATE_TYPE.FLAW_SALEABLE_PRODUCT ||
    reportStateID == PRODUCT_STATE_TYPE.CHANGED_TO_USED_STATUS ||
    reportStateID == PRODUCT_STATE_TYPE.OTHER_CHANGE_CODE_STATUS ||
    reportStateID == PRODUCT_STATE_TYPE.CHANGE_ERROR_STATE_WITH_NEW_SEAL;
  const isShowNewStatus = !helper.IsEmptyObject(errorInfo) && (reportState.isReqInventoryStatus == 1 || isInvalidChangeInventoryStatusId);

  const listRenderProductItem = listProductByInventoryProcessCode.filter(item => (
    !helper.IsEmptyObject(item) &&
    (helper.removeAccent(item.productname).toLowerCase().includes(helper.removeAccent(trueProductId).toLowerCase()) ||
      helper.removeAccent(item.productid).toLowerCase().includes(helper.removeAccent(trueProductId).toLowerCase()) ||
      helper.removeAccent(item.lotid).toLowerCase().includes(helper.removeAccent(trueProductId).toLowerCase()))
  )).slice(0, 8);

  const listRenderProductBatch = listProductBatch.map(batchItem => ({ ...batchItem, isWarning: batchItem.isCheckStock == 1 && batchItem.quantity < reportQuantity }));

  const newStatusName = isShowNewStatus ? (helper.IsNonEmptyArray(listCauErroPIMBO) ?
    selectedErrorReason.changeInventoryStatusName :
    changeInventoryStatus.changeInventoryStatusName) : '';

  useEffect(() => {
    if (isShowTrueProductInfo) {
      setIsShowListTrueProduct(false);
      // setTrueProductId(trueProductSelected.productid.trim())
      if (isHasImei && helper.IsNonEmptyString(trueProductSelected.imei)) {
        setTrueProductImei(trueProductSelected.imei.trim().toUpperCase());
      }
    }
  }, [isShowTrueProductInfo]);

  useEffect(() => {
    if (!helper.IsEmptyObject(trueProductSelected)) {
      setTrueProductId(trueProductSelected.productid.trim());
      setIsShowListTrueProduct(false);
      setEditableImei(true);
    }
  }, [trueProductSelected]);

  const takePicture = (photo) => {
    setIsVisibleCamera(false);
    showBlockUI();
    if (helper.hasProperty(photo, 'uri')) {
      helper.resizeImage(photo).then(({ path, uri, size, name }) => {
        const body = helper.createFormData({ uri: uri, type: 'image/jpg', name: name, path: CHANGE_CODE_BUSINESS });
        getImageCDN(body)
          .then((response) => {
            const remoteURI = API_CONST.API_GET_IMAGE_CDN_NEW + response[0];
            let newImageUrls = [...imageUrls];
            let itemImg = {
              realProductStatusAttachId: 0,
              imageURL: remoteURI,
              isDeleted: 0,
              fileName: response[0]
            };
            newImageUrls[currentIndex.current] = itemImg;
            setImageUrls(newImageUrls);
            hideBlockUI();
          }).catch((error) => {
            Alert.alert(
              translate('common.notification_uppercase'),
              'Đã xảy ra lỗi trong quá trình upload hình ảnh, vui lòng thử lại!',
              [{
                text: "OK",
                onPress: hideBlockUI
              }]
            );
            console.log('uploadPicture', error);
          });

      }).catch((error) => {
        hideBlockUI();
        console.log("resizeImage", error);
      });
    } else { hideBlockUI(); }
  };

  const selectPicture = () => {
    launchImageLibrary(
      {
        mediaType: 'photo',
        noData: true
      },
      (response) => {
        setIsVisibleCamera(false);
        showBlockUI();
        if (helper.hasProperty(response, 'uri')) {
          helper.resizeImage(response)
            .then(({ path, uri, size, name }) => {
              const body = helper.createFormData({ uri: uri, type: 'image/jpg', name: name, path: CHANGE_CODE_BUSINESS });
              getImageCDN(body)
                .then((response) => {
                  const remoteURI = API_CONST.API_GET_IMAGE_CDN_NEW + response[0];
                  let newImageUrls = [...imageUrls];
                  let itemImg = {
                    realProductStatusAttachId: 0,
                    imageURL: remoteURI,
                    isDeleted: 0,
                    fileName: response[0]
                  };
                  newImageUrls[currentIndex.current] = itemImg;
                  setImageUrls(newImageUrls);
                  hideBlockUI();
                }).catch((error) => {
                  Alert.alert(
                    translate('common.notification_uppercase'),
                    'Đã xảy ra lỗi trong quá trình upload hình ảnh, vui lòng thử lại!',
                    [{
                      text: "OK",
                      onPress: hideBlockUI
                    }]
                  );
                  console.log('uploadPicture', error);
                });
            })
            .catch((error) => {
              hideBlockUI();
              console.log('resizeImage', error);
            });
        } else { hideBlockUI(); }
      }
    );
  };

  const onChangeTrueProductId = (text) => {
    setTrueProductId(text.trim());
    setIsShowListTrueProduct(false);
    if (isShowTrueProductInfo) {
      onSelectTrueProduct({});
      setTrueProductImei("");
      setEditableImei(false);
    }
  };

  const onSubmitProductId = () => {
    if (helper.IsNonEmptyString(trueProductId)) {
      setIsShowListTrueProduct(true);
      Keyboard.dismiss();
      getListProductByInventoryProcess(trueProductId);
    }
  };

  const deleteImage = (index) => {
    let newImageUrls = [...imageUrls];
    if (newImageUrls[index].realProductStatusAttachId !== 0) {
      const indexImage = currentAttachments.findIndex(ele => ele.realProductStatusAttachId == newImageUrls[index].realProductStatusAttachId);
      if (indexImage !== -1) {
        currentAttachments[indexImage].isDeleted = 1;
      }
    }
    newImageUrls[index] = {};
    setImageUrls(newImageUrls);
  };

  const onChangeImei = (text) => {
    setTrueProductImei(text.replace(/\n/g, "").trim());
    if (isShowTrueProductInfo) {
      onSelectTrueProduct({});
    }
  };

  const onChangeSpareImei = (text) => {
    setSpareProductImei(text);
    if (isCheckedImei) {
      checkSpareImei("");
    }
  };

  const onSubmitSpareImei = () => {
    if (helper.IsNonEmptyString(spareProductImei)) {
      Keyboard.dismiss();
      checkSpareImei(spareProductImei.toUpperCase());
    }
  };

  const onSubmitTrueImei = () => {
    if (helper.IsNonEmptyString(trueProductImei)) {
      Keyboard.dismiss();
      checkTrueImei(trueProductImei.toUpperCase(), (
        reportStateID == PRODUCT_STATE_TYPE.INCORRECT_PRODUCT_AVA ||
        reportStateID == PRODUCT_STATE_TYPE.INCORRECT_PRODUCT ||
        reportStateID == PRODUCT_STATE_TYPE.WRONG_CODE_PRODUCT
      ) ? trueProductId : productId);
    }
  };

  const selectTrueProduct = (productItem) => {
    Keyboard.dismiss();
    getListProductByInventoryProcess(productItem.productid, 1);
  };

  const openScanCamera = (scanProduct = true) => {
    isScanProductId.current = scanProduct;
    setIsVisibleScanCamera(true);
  };

  const handleResultBarcode = (barcode) => {
    setIsVisibleScanCamera(false);
    if (isScanProductId.current) {
      setTrueProductId(barcode);
      if (helper.IsNonEmptyString(barcode) && helper.IsEmptyArray(listRenderProductItem)) {
        Keyboard.dismiss();
        getListProductByInventoryProcess(barcode);
      };
      if (!isShowListTrueProduct) {
        setIsShowListTrueProduct(true);
      }
      if (isShowTrueProductInfo) {
        onSelectTrueProduct({});
        setTrueProductImei("");
        setEditableImei(false);
      }
    } else {
      setTrueProductImei(barcode);
      if (helper.IsNonEmptyString(barcode)) {
        Keyboard.dismiss();
        checkTrueImei(barcode.toUpperCase(), (
          reportStateID == PRODUCT_STATE_TYPE.INCORRECT_PRODUCT_AVA ||
          reportStateID == PRODUCT_STATE_TYPE.INCORRECT_PRODUCT ||
          reportStateID == PRODUCT_STATE_TYPE.WRONG_CODE_PRODUCT
        ) ? trueProductId : productId);
      }
    }
  };

  return (
    <View>
      {isShowTrueProductIdInput && <TrueProductIdInput
        trueProductId={trueProductId}
        onChange={onChangeTrueProductId}
        onSubmit={onSubmitProductId}
        isDisabled={isUpdate}
        listRenderProductItem={listRenderProductItem}
        isShowListTrueProduct={isShowListTrueProduct}
        selectTrueProduct={selectTrueProduct}
        openScanCamera={openScanCamera}
      />}
      {isShowSpareImeiInput && <ImeiInput
        title={"Nhập IMEI "}
        placeholder={"IMEI"}
        imei={spareProductImei}
        onChangeImei={onChangeSpareImei}
        onSubmitImei={onSubmitSpareImei}
        isDisabled={isUpdate}
      />}
      {isShowTrueImeiInput && <ImeiInput
        title={translate('inventory.label_enter_true_imei')}
        placeholder={translate('inventory.true_imei_placeholder')}
        imei={trueProductImei}
        onChangeImei={onChangeImei}
        onSubmitImei={onSubmitTrueImei}
        isDisabled={isUpdate || !editableImei}
        isScanBarcode={true}
        openScanCamera={() => {
          openScanCamera(false);
        }}
      />}
      {
        isShowTrueProductInfo && <View style={{
          width: constants.width,
          alignItems: 'center',
          paddingTop: 10
        }}>
          <SelectedProductInfo
            productInfo={trueProductSelected}
            isShowNewStatus={isShowNewStatus}
            newStatusName={newStatusName}
          />
        </View>
      }
      {isRequiredBatchNo && <ExpirationDate
        expirationDateText={expirationDateText}
        onChange={onChangeExpirationDateText}
        onSubmit={(event) => {
          const stringDate =
            event.nativeEvent.text;
          onSubmitExpirationDateText(stringDate);
        }}
        showDatePicker={showDatePicker}
        listProductBatch={listRenderProductBatch}
        onSelectBatch={onSelectBatch}
        selectedProductBatch={selectedProductBatch}
      />}
      {isShowErrorReason && <ErrorReason
        listErrorReason={listCauErroPIMBO}
        selectedErrorReason={selectedErrorReason}
        onSelectErrorReason={onSelectErrorReason}
        otherErrorReasonNote={otherErrorReasonNote}
        onChangeOtherErrorReasonNote={onChangeOtherErrorReasonNote}
      />}
      <NoteInput
        note={note}
        onChangeNote={onChangeNote}
        isRequiredNote={isRequiredNote}
      />
      {isShowLinkCamera && <LinkCamera
        isRequired={isRequiredLinkCamera}
        linkCamera={linkCamera}
        onChangeLinkCamera={onChangeLinkCamera}
      />}
      {isShowImageAttachment && <ImageAttachment
        isRequired={isRequiredAttachments}
        imageUrls={imageUrls}
        setImageUrls={setImageUrls}
        openCamera={(index) => {
          setIsVisibleCamera(true);
          currentIndex.current = index;
        }}
        deleteImage={deleteImage}
        stateId={reportStateID}
      />}
      {isShowNewStatus && <NewStatus statusName={newStatusName} isInvalidChangeInventoryStatusId={isInvalidChangeInventoryStatusId} />}
      <CaptureCamera
        isVisibleCamera={isVisibleCamera}
        takePicture={takePicture}
        closeCamera={() => { setIsVisibleCamera(false); }}
        selectPicture={selectPicture}
      />
      {isVisibleScanCamera &&
        <ScanBarcodeML
          isVisible={isVisibleScanCamera}
          closeCamera={() => {
            setIsVisibleScanCamera(false);
          }}
          resultScanBarcode={handleResultBarcode}
        />
      }
    </View >
  );
};

export default ChangeCodeBusiness;