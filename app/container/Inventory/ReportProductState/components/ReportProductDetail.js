import React from 'react';
import { View } from 'react-native';
import { constants } from '@constants';
import { COLORS } from '@styles';
import ItemExchangeProduct from './ItemExchangeProduct';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import SaveButton from './SaveButton';
import { ChangeCodeBusiness } from '../ChangeCodeBusiness';
import { helper } from '@common';
import { PRODUCT_STATE_TYPE } from '../ReportProductStateEnum';
import TotalReportQuantity from './TotalReportQuantity';
import moment from 'moment';

const ReportProductDetail = ({
    isHasImei,
    isExchange,
    productInfo,
    listProductItem,
    onChangeQuantityExchangeProduct,
    onChangeQuantityNoExchangeProduct,
    note,
    onChangeNote,
    saveRequest,
    reportState,
    trueProductSelected,
    onSelectTrueProduct,
    getListProductByInventoryProcess,
    currentProductInfo,
    currentListProductItem,
    listProductByInventoryProcessCode,
    isShowTrueProductInfo,
    isUpdate,
    isOuterImei,
    imageUrls,
    setImageUrls,
    checkTrueImei,
    checkSpareImei,
    isCheckedImei = true,
    spareProductImei,
    setSpareProductImei,
    currentAttachments,
    errorInfo,
    selectedErrorReason,
    onSelectErrorReason,
    otherErrorReasonNote,
    onChangeOtherErrorReasonNote,
    linkCamera,
    onChangeLinkCamera,
    expirationDateText,
    onChangeExpirationDateText,
    onSubmitExpirationDateText,
    listProductBatch,
    onSelectBatch,
    selectedProductBatch,
    showDatePicker
}) => {
    const { reportQuantity, isManageByBatch, quantityunit, productid } = productInfo;
    const reportStateID = reportState.REALPRODUCTSTATUSTYPEID;

    const totalReportQuantity = isExchange ? listProductItem.reduce((result, current) => {
        const { isExchangeQuantity, reportQuantity, exchangeQuantity } = current
        return result + (isExchangeQuantity ? reportQuantity * exchangeQuantity : reportQuantity)
    }, 0) : reportQuantity;


    const currentReportQuantityDetails = JSON.stringify(currentListProductItem.map(item => item.reportQuantity));
    const reportQuantityDetails = JSON.stringify(listProductItem.map(item => item.reportQuantity))

    const imageUrlFileNames = imageUrls.filter(item => helper.IsNonEmptyString(item.fileName)).map(item => item.fileName)
    const currentImageUrlFileNames = currentAttachments.map(item => item.fileName)

    const currentProduct = isHasImei ? productInfo : (isExchange ? currentListProductItem[0] : currentProductInfo);

    const isRequiredBatchNo = (
        !helper.IsEmptyObject(trueProductSelected) &&
        trueProductSelected.isManageByBatch == 1 &&
        (reportStateID == PRODUCT_STATE_TYPE.INCORRECT_PRODUCT_AVA || reportStateID == PRODUCT_STATE_TYPE.INCORRECT_PRODUCT || reportStateID == PRODUCT_STATE_TYPE.WRONG_CODE_PRODUCT)
    ) ||
        (
            isManageByBatch == 1 &&
            reportStateID != PRODUCT_STATE_TYPE.LOST_PRODUCT_AVA &&
            reportStateID != PRODUCT_STATE_TYPE.LOST_PRODUCT &&
            reportStateID != PRODUCT_STATE_TYPE.INCORRECT_PRODUCT_AVA &&
            reportStateID != PRODUCT_STATE_TYPE.INCORRECT_PRODUCT &&
            reportStateID != PRODUCT_STATE_TYPE.WRONG_CODE_PRODUCT
        ) ||
        (isUpdate && helper.IsNonEmptyString(currentProduct.batchNo));
    const isRequiredLinkCamera = reportStateID == PRODUCT_STATE_TYPE.MANUFACTURE_EXTERNAL_ERROR ||
        reportStateID == PRODUCT_STATE_TYPE.EXTERNAL_ERROR_SALEABLE;
    const isRequiredAttachments = isOuterImei ||
        reportStateID == PRODUCT_STATE_TYPE.INCORRECT_PRODUCT_AVA ||
        reportStateID == PRODUCT_STATE_TYPE.INCORRECT_PRODUCT ||
        reportStateID == PRODUCT_STATE_TYPE.WRONG_CODE_PRODUCT ||
        reportStateID == PRODUCT_STATE_TYPE.SPARE_PRODUCT_AVA ||
        reportStateID == PRODUCT_STATE_TYPE.SPARE_PRODUCT ||
        reportStateID == PRODUCT_STATE_TYPE.MANUFACTURE_EXTERNAL_ERROR ||
        reportStateID == PRODUCT_STATE_TYPE.EXTERNAL_ERROR_SALEABLE ||
        reportStateID == PRODUCT_STATE_TYPE.CANCEL_PRODUCT ||
        reportStateID == PRODUCT_STATE_TYPE.INTERNAL_ERROR_PRODUCT ||
        reportStateID == PRODUCT_STATE_TYPE.SALEABLE_PRODUCT ||
        reportStateID == PRODUCT_STATE_TYPE.SEAL_ERROR ||
        reportStateID == PRODUCT_STATE_TYPE.PHYSICAL_IMPACT_ERROR ||
        reportStateID == PRODUCT_STATE_TYPE.FLAW_SALEABLE_PRODUCT ||
        reportStateID == PRODUCT_STATE_TYPE.CHANGED_TO_USED_STATUS ||
        reportStateID == PRODUCT_STATE_TYPE.OTHER_CHANGE_CODE_STATUS ||
        reportStateID == PRODUCT_STATE_TYPE.CHANGE_ERROR_STATE_WITH_NEW_SEAL
        ;
    const isRequiredNote = reportStateID == PRODUCT_STATE_TYPE.MANUFACTURE_EXTERNAL_ERROR ||
        reportStateID == PRODUCT_STATE_TYPE.EXTERNAL_ERROR_SALEABLE ||
        reportStateID == PRODUCT_STATE_TYPE.CANCEL_PRODUCT ||
        reportStateID == PRODUCT_STATE_TYPE.CANCEL_PRODUCT ||
        reportStateID == PRODUCT_STATE_TYPE.INTERNAL_ERROR_PRODUCT ||
        reportStateID == PRODUCT_STATE_TYPE.SALEABLE_PRODUCT ||
        reportStateID == PRODUCT_STATE_TYPE.SEAL_ERROR ||
        reportStateID == PRODUCT_STATE_TYPE.PHYSICAL_IMPACT_ERROR ||
        reportStateID == PRODUCT_STATE_TYPE.FLAW_SALEABLE_PRODUCT ||
        reportStateID == PRODUCT_STATE_TYPE.CHANGED_TO_USED_STATUS ||
        reportStateID == PRODUCT_STATE_TYPE.OTHER_CHANGE_CODE_STATUS ||
        reportStateID == PRODUCT_STATE_TYPE.CHANGE_ERROR_STATE_WITH_NEW_SEAL
    const isRequiredErrorReason = helper.IsNonEmptyArray(errorInfo.listCauErroPIMBO);

    const changeInventoryStatusId = (helper.IsNonEmptyArray(errorInfo.listCauErroPIMBO) ?
        selectedErrorReason.changeInventoryStatusId :
        errorInfo.changeInventoryStatus?.changeInventoryStatusId)
    const isInvalidChangeInventoryStatusId = changeInventoryStatusId == 0;

    const isValidQUantity = totalReportQuantity > 0;
    const isNotSelectedErrorReason = isRequiredErrorReason && (helper.IsEmptyObject(selectedErrorReason) ||
        (!helper.IsEmptyObject(selectedErrorReason) && selectedErrorReason.isReqDescription && !helper.IsNonEmptyString(otherErrorReasonNote)));
    const isNotSelectedBatch = (isRequiredBatchNo && !helper.IsNonEmptyString(selectedProductBatch.batchNo));
    const isNotAttachedImage = (isRequiredAttachments && helper.IsEmptyArray(imageUrlFileNames));
    const isNotValidNote = isRequiredNote && !helper.IsNonEmptyString(note);
    const isNotValidLinkCamera = isRequiredLinkCamera && !helper.IsNonEmptyString(linkCamera);

    const isUpdateNote = isHasImei ? note != currentProduct.content : note != currentProduct.note;
    const isUpdateImage = imageUrlFileNames.toString() != currentImageUrlFileNames.toString();
    const isUpdateQuantity = isExchange ? currentReportQuantityDetails != reportQuantityDetails : currentProduct.reportQuantity != totalReportQuantity;
    const isUpdateErrorReason = isRequiredErrorReason && !isNotSelectedErrorReason && (selectedErrorReason.realProductSttTypeApplyId != currentProduct.realPrdSTTTApplyCauseErroId ||
        (selectedErrorReason.isReqDescription && otherErrorReasonNote != currentProduct.causeErroContent));
    const isUpdateExpirationDateOrBatch = !isNotSelectedBatch && ((currentProduct.productShelfLife > 0 && moment(new Date(currentProduct.productShelfLife)).format('DD/MM/YYYY') != expirationDateText) ||
        selectedProductBatch.batchNo != currentProduct.batchNo);
    const isUpdateLinkCamera = !isNotValidLinkCamera && (linkCamera != currentProduct.noteLinkCamera);

    let isButtonDisabled = false

    if (errorInfo.isError) {
        isButtonDisabled = true;
    } else if (isHasImei) {
        if (!isUpdate) {
            if (reportStateID == PRODUCT_STATE_TYPE.SPARE_PRODUCT_AVA ||
                reportStateID == PRODUCT_STATE_TYPE.SPARE_PRODUCT
            ) {
                if (!isCheckedImei ||
                    isNotSelectedErrorReason ||
                    isNotSelectedBatch ||
                    isNotAttachedImage ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true
                }
            } else if (reportStateID == PRODUCT_STATE_TYPE.LOST_PRODUCT_AVA || reportStateID == PRODUCT_STATE_TYPE.LOST_PRODUCT) {
                if (
                    isNotSelectedErrorReason ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true
                }
            } else if (
                reportStateID == PRODUCT_STATE_TYPE.INCORRECT_PRODUCT_AVA ||
                reportStateID == PRODUCT_STATE_TYPE.INCORRECT_PRODUCT ||
                reportStateID == PRODUCT_STATE_TYPE.WRONG_CODE_PRODUCT
            ) {
                if (!isShowTrueProductInfo ||
                    (productInfo.realImei == trueProductSelected.imei && productInfo.realProductId == trueProductSelected.productid) ||
                    isNotSelectedErrorReason ||
                    isNotSelectedBatch ||
                    isNotAttachedImage ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true
                }
            } else if (reportStateID == PRODUCT_STATE_TYPE.MANUFACTURE_EXTERNAL_ERROR) {
                if (isNotSelectedErrorReason ||
                    isNotSelectedBatch ||
                    isNotAttachedImage ||
                    isNotValidNote ||
                    isNotValidLinkCamera ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true
                }
            } else if (reportStateID == PRODUCT_STATE_TYPE.EXTERNAL_ERROR_SALEABLE) {
                if (isNotSelectedErrorReason ||
                    isNotSelectedBatch ||
                    isNotAttachedImage ||
                    isNotValidNote ||
                    isNotValidLinkCamera ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true;
                }
            } else if (reportStateID == PRODUCT_STATE_TYPE.EXTERNAL_ERROR_NOT_SALEABLE) {
                if (isNotSelectedErrorReason ||
                    isNotSelectedBatch ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true;
                }
            } else if (reportStateID == PRODUCT_STATE_TYPE.CANCEL_PRODUCT) {
                if (isNotSelectedErrorReason ||
                    isNotSelectedBatch ||
                    isNotAttachedImage ||
                    isNotValidNote ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true;
                }
            } else if (
                reportStateID == PRODUCT_STATE_TYPE.INTERNAL_ERROR_PRODUCT ||
                reportStateID == PRODUCT_STATE_TYPE.SALEABLE_PRODUCT ||
                reportStateID == PRODUCT_STATE_TYPE.SEAL_ERROR ||
                reportStateID == PRODUCT_STATE_TYPE.PHYSICAL_IMPACT_ERROR ||
                reportStateID == PRODUCT_STATE_TYPE.FLAW_SALEABLE_PRODUCT ||
                reportStateID == PRODUCT_STATE_TYPE.CHANGED_TO_USED_STATUS ||
                reportStateID == PRODUCT_STATE_TYPE.OTHER_CHANGE_CODE_STATUS ||
                reportStateID == PRODUCT_STATE_TYPE.CHANGE_ERROR_STATE_WITH_NEW_SEAL
            ) {
                if (isNotSelectedErrorReason ||
                    isNotSelectedBatch ||
                    isNotValidNote ||
                    isNotAttachedImage ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true
                }
            }
        } else {
            if (reportStateID == PRODUCT_STATE_TYPE.SPARE_PRODUCT_AVA ||
                reportStateID == PRODUCT_STATE_TYPE.SPARE_PRODUCT) {
                if ((!isUpdateNote &&
                    !isUpdateImage &&
                    !isUpdateErrorReason &&
                    !isUpdateExpirationDateOrBatch &&
                    !isUpdateLinkCamera
                ) ||
                    isNotAttachedImage ||
                    isNotSelectedErrorReason ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true
                }
            } else if (reportStateID == PRODUCT_STATE_TYPE.LOST_PRODUCT_AVA || reportStateID == PRODUCT_STATE_TYPE.LOST_PRODUCT) {
                if ((!isUpdateNote &&
                    !isUpdateErrorReason &&
                    !isUpdateLinkCamera) ||
                    isNotSelectedErrorReason ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true
                }
            } else if (
                reportStateID == PRODUCT_STATE_TYPE.INCORRECT_PRODUCT_AVA ||
                reportStateID == PRODUCT_STATE_TYPE.INCORRECT_PRODUCT ||
                reportStateID == PRODUCT_STATE_TYPE.WRONG_CODE_PRODUCT
            ) {
                if ((!isUpdateNote &&
                    !isUpdateImage &&
                    !isUpdateErrorReason &&
                    !isUpdateExpirationDateOrBatch &&
                    !isUpdateLinkCamera
                ) ||
                    isNotAttachedImage ||
                    isNotSelectedErrorReason ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true
                }
            } else if (reportStateID == PRODUCT_STATE_TYPE.MANUFACTURE_EXTERNAL_ERROR) {
                if ((!isUpdateNote &&
                    !isUpdateImage &&
                    !isUpdateErrorReason &&
                    !isUpdateExpirationDateOrBatch &&
                    !isUpdateLinkCamera
                ) ||
                    isNotAttachedImage ||
                    isNotValidNote ||
                    isNotSelectedErrorReason ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true
                }
            } else if (reportStateID == PRODUCT_STATE_TYPE.EXTERNAL_ERROR_SALEABLE) {
                if ((!isUpdateNote &&
                    !isUpdateImage &&
                    !isUpdateErrorReason &&
                    !isUpdateExpirationDateOrBatch &&
                    !isUpdateLinkCamera
                ) ||
                    isNotAttachedImage ||
                    isNotValidNote ||
                    isNotSelectedErrorReason ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true
                }
            } else if (reportStateID == PRODUCT_STATE_TYPE.EXTERNAL_ERROR_NOT_SALEABLE) {
                if ((!isUpdateNote &&
                    !isUpdateImage &&
                    !isUpdateErrorReason &&
                    !isUpdateExpirationDateOrBatch &&
                    !isUpdateLinkCamera
                ) ||
                    isNotSelectedErrorReason ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true
                }
            } else if (reportStateID == PRODUCT_STATE_TYPE.CANCEL_PRODUCT) {
                if ((!isUpdateNote &&
                    !isUpdateImage &&
                    !isUpdateErrorReason &&
                    !isUpdateExpirationDateOrBatch &&
                    !isUpdateLinkCamera
                ) ||
                    isNotAttachedImage ||
                    isNotValidNote ||
                    isNotSelectedErrorReason ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true
                }
            } else if (
                reportStateID == PRODUCT_STATE_TYPE.INTERNAL_ERROR_PRODUCT ||
                reportStateID == PRODUCT_STATE_TYPE.SALEABLE_PRODUCT ||
                reportStateID == PRODUCT_STATE_TYPE.SEAL_ERROR ||
                reportStateID == PRODUCT_STATE_TYPE.PHYSICAL_IMPACT_ERROR ||
                reportStateID == PRODUCT_STATE_TYPE.FLAW_SALEABLE_PRODUCT ||
                reportStateID == PRODUCT_STATE_TYPE.CHANGED_TO_USED_STATUS ||
                reportStateID == PRODUCT_STATE_TYPE.OTHER_CHANGE_CODE_STATUS ||
                reportStateID == PRODUCT_STATE_TYPE.CHANGE_ERROR_STATE_WITH_NEW_SEAL
            ) {
                if ((!isUpdateNote &&
                    !isUpdateImage &&
                    !isUpdateErrorReason &&
                    !isUpdateExpirationDateOrBatch &&
                    !isUpdateLinkCamera
                ) ||
                    isNotAttachedImage ||
                    isNotSelectedErrorReason ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true
                }
            }
        }
    } else {
        if (!isUpdate) {
            if (reportStateID == PRODUCT_STATE_TYPE.SPARE_PRODUCT_AVA ||
                reportStateID == PRODUCT_STATE_TYPE.SPARE_PRODUCT) {
                if (!isValidQUantity ||
                    isNotSelectedErrorReason ||
                    isNotSelectedBatch ||
                    isNotAttachedImage ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true
                }
            } else if (reportStateID == PRODUCT_STATE_TYPE.LOST_PRODUCT_AVA || reportStateID == PRODUCT_STATE_TYPE.LOST_PRODUCT) {
                if (!isValidQUantity ||
                    isNotSelectedErrorReason ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true
                }
            } else if (
                reportStateID == PRODUCT_STATE_TYPE.INCORRECT_PRODUCT_AVA ||
                reportStateID == PRODUCT_STATE_TYPE.INCORRECT_PRODUCT ||
                reportStateID == PRODUCT_STATE_TYPE.WRONG_CODE_PRODUCT
            ) {
                if (!isValidQUantity ||
                    isNotSelectedErrorReason ||
                    !isShowTrueProductInfo ||
                    isNotSelectedBatch ||
                    isNotAttachedImage ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true
                }
            } else if (reportStateID == PRODUCT_STATE_TYPE.MANUFACTURE_EXTERNAL_ERROR) {
                if (!isValidQUantity ||
                    isNotSelectedErrorReason ||
                    isNotSelectedBatch ||
                    isNotAttachedImage ||
                    isNotValidNote ||
                    isNotValidLinkCamera ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true
                }
            } else if (reportStateID == PRODUCT_STATE_TYPE.EXTERNAL_ERROR_SALEABLE) {
                if (!isValidQUantity ||
                    isNotSelectedErrorReason ||
                    isNotSelectedBatch ||
                    isNotAttachedImage ||
                    isNotValidNote ||
                    isNotValidLinkCamera ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true;
                }
            } else if (reportStateID == PRODUCT_STATE_TYPE.EXTERNAL_ERROR_NOT_SALEABLE) {
                if (!isValidQUantity ||
                    isNotSelectedErrorReason ||
                    isNotSelectedBatch ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true;
                }
            } else if (reportStateID == PRODUCT_STATE_TYPE.CANCEL_PRODUCT) {
                if (!isValidQUantity ||
                    isNotSelectedErrorReason ||
                    isNotSelectedBatch ||
                    isNotAttachedImage ||
                    isNotValidNote ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true;
                }
            } else if (
                reportStateID == PRODUCT_STATE_TYPE.INTERNAL_ERROR_PRODUCT ||
                reportStateID == PRODUCT_STATE_TYPE.SALEABLE_PRODUCT ||
                reportStateID == PRODUCT_STATE_TYPE.SEAL_ERROR ||
                reportStateID == PRODUCT_STATE_TYPE.PHYSICAL_IMPACT_ERROR ||
                reportStateID == PRODUCT_STATE_TYPE.FLAW_SALEABLE_PRODUCT ||
                reportStateID == PRODUCT_STATE_TYPE.CHANGED_TO_USED_STATUS ||
                reportStateID == PRODUCT_STATE_TYPE.OTHER_CHANGE_CODE_STATUS ||
                reportStateID == PRODUCT_STATE_TYPE.CHANGE_ERROR_STATE_WITH_NEW_SEAL
            ) {
                if (!isValidQUantity ||
                    isNotSelectedErrorReason ||
                    isNotSelectedBatch ||
                    isNotValidNote ||
                    isNotAttachedImage ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true
                }
            }
        } else {
            if (reportStateID == PRODUCT_STATE_TYPE.SPARE_PRODUCT_AVA ||
                reportStateID == PRODUCT_STATE_TYPE.SPARE_PRODUCT) {
                if (!isValidQUantity ||
                    (!isUpdateNote &&
                        !isUpdateImage &&
                        !isUpdateQuantity &&
                        !isUpdateErrorReason &&
                        !isUpdateExpirationDateOrBatch &&
                        !isUpdateLinkCamera
                    ) ||
                    isNotSelectedErrorReason ||
                    isInvalidChangeInventoryStatusId) {
                    isButtonDisabled = true
                }
            } else if (reportStateID == PRODUCT_STATE_TYPE.LOST_PRODUCT_AVA || reportStateID == PRODUCT_STATE_TYPE.LOST_PRODUCT) {
                if (!isValidQUantity ||
                    (!isUpdateNote &&
                        !isUpdateQuantity &&
                        !isUpdateErrorReason &&
                        !isUpdateLinkCamera
                    ) ||
                    isNotSelectedErrorReason ||
                    isInvalidChangeInventoryStatusId) {
                    isButtonDisabled = true
                }
            } else if (
                reportStateID == PRODUCT_STATE_TYPE.INCORRECT_PRODUCT_AVA ||
                reportStateID == PRODUCT_STATE_TYPE.INCORRECT_PRODUCT ||
                reportStateID == PRODUCT_STATE_TYPE.WRONG_CODE_PRODUCT
            ) {
                if (!isValidQUantity ||
                    (!isUpdateNote &&
                        !isUpdateImage &&
                        !isUpdateQuantity &&
                        !isUpdateErrorReason &&
                        !isUpdateExpirationDateOrBatch &&
                        !isUpdateLinkCamera
                    ) ||
                    isNotAttachedImage ||
                    isNotSelectedErrorReason ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true
                }
            } else if (reportStateID == PRODUCT_STATE_TYPE.MANUFACTURE_EXTERNAL_ERROR) {
                if (!isValidQUantity ||
                    (!isUpdateNote &&
                        !isUpdateImage &&
                        !isUpdateQuantity &&
                        !isUpdateErrorReason &&
                        !isUpdateExpirationDateOrBatch &&
                        !isUpdateLinkCamera
                    ) ||
                    isNotAttachedImage ||
                    isNotValidNote ||
                    isNotSelectedErrorReason ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true
                }
            } else if (reportStateID == PRODUCT_STATE_TYPE.EXTERNAL_ERROR_SALEABLE) {
                if (!isValidQUantity ||
                    (!isUpdateNote &&
                        !isUpdateImage &&
                        !isUpdateQuantity &&
                        !isUpdateErrorReason &&
                        !isUpdateExpirationDateOrBatch &&
                        !isUpdateLinkCamera
                    ) ||
                    isNotAttachedImage ||
                    isNotValidNote ||
                    isNotSelectedErrorReason ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true
                }
            } else if (reportStateID == PRODUCT_STATE_TYPE.EXTERNAL_ERROR_NOT_SALEABLE) {
                if (!isValidQUantity ||
                    (!isUpdateNote &&
                        !isUpdateImage &&
                        !isUpdateQuantity &&
                        !isUpdateErrorReason &&
                        !isUpdateExpirationDateOrBatch &&
                        !isUpdateLinkCamera
                    ) ||
                    isNotSelectedErrorReason ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true
                }
            } else if (reportStateID == PRODUCT_STATE_TYPE.CANCEL_PRODUCT) {
                if (!isValidQUantity ||
                    (!isUpdateNote &&
                        !isUpdateImage &&
                        !isUpdateQuantity &&
                        !isUpdateErrorReason &&
                        !isUpdateExpirationDateOrBatch &&
                        !isUpdateLinkCamera
                    ) ||
                    isNotAttachedImage ||
                    isNotValidNote ||
                    isNotSelectedErrorReason ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true
                }
            } else if (
                reportStateID == PRODUCT_STATE_TYPE.INTERNAL_ERROR_PRODUCT ||
                reportStateID == PRODUCT_STATE_TYPE.SALEABLE_PRODUCT ||
                reportStateID == PRODUCT_STATE_TYPE.SEAL_ERROR ||
                reportStateID == PRODUCT_STATE_TYPE.PHYSICAL_IMPACT_ERROR ||
                reportStateID == PRODUCT_STATE_TYPE.FLAW_SALEABLE_PRODUCT ||
                reportStateID == PRODUCT_STATE_TYPE.CHANGED_TO_USED_STATUS ||
                reportStateID == PRODUCT_STATE_TYPE.OTHER_CHANGE_CODE_STATUS ||
                reportStateID == PRODUCT_STATE_TYPE.CHANGE_ERROR_STATE_WITH_NEW_SEAL
            ) {
                if (!isValidQUantity ||
                    (!isUpdateNote &&
                        !isUpdateImage &&
                        !isUpdateQuantity &&
                        !isUpdateErrorReason &&
                        !isUpdateExpirationDateOrBatch &&
                        !isUpdateLinkCamera
                    ) ||
                    isNotAttachedImage ||
                    isNotValidNote ||
                    isNotSelectedErrorReason ||
                    isInvalidChangeInventoryStatusId
                ) {
                    isButtonDisabled = true
                }
            }
        }
    }

    return (
        <View style={{
            width: constants.width,
            flex: 1,
            backgroundColor: COLORS.bgFFFFFF,
        }}>
            <TotalReportQuantity
                isHasImei={isHasImei}
                isExchange={isExchange}
                totalReportQuantity={totalReportQuantity}
                unit={quantityunit}
                onChangeQuantity={isExchange ? onChangeQuantityExchangeProduct : onChangeQuantityNoExchangeProduct}
            />
            <KeyboardAwareScrollView
                style={{
                    flex: 1,
                    backgroundColor: COLORS.bgFFFFFF
                }}
                contentContainerStyle={{
                    alignItems: 'center'
                }}
                enableResetScrollToCoords={true}
                keyboardShouldPersistTaps="always"
                bounces={false}
                overScrollMode="always"
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                extraScrollHeight={0}
            >
                {isExchange && listProductItem.map((item, index) => (
                    <ItemExchangeProduct
                        key={`${index}`}
                        productData={item}
                        index={index}
                        onChangeQuantity={onChangeQuantityExchangeProduct}
                    />
                ))}
                <ChangeCodeBusiness
                    reportState={reportState}
                    errorInfo={errorInfo}
                    isRequiredNote={isRequiredNote}
                    note={note}
                    onChangeNote={onChangeNote}
                    trueProductSelected={trueProductSelected}
                    onSelectTrueProduct={onSelectTrueProduct}
                    listProductByInventoryProcessCode={listProductByInventoryProcessCode}
                    getListProductByInventoryProcess={getListProductByInventoryProcess}
                    isShowTrueProductInfo={isShowTrueProductInfo}
                    reportQuantity={totalReportQuantity}
                    isRequiredAttachments={isRequiredAttachments}
                    imageUrls={imageUrls}
                    setImageUrls={setImageUrls}
                    checkTrueImei={checkTrueImei}
                    productId={productid}
                    checkSpareImei={checkSpareImei}
                    isCheckedImei={isCheckedImei}
                    spareProductImei={spareProductImei}
                    setSpareProductImei={setSpareProductImei}
                    isHasImei={isHasImei}
                    isUpdate={isUpdate}
                    currentAttachments={currentAttachments}
                    selectedErrorReason={selectedErrorReason}
                    onSelectErrorReason={onSelectErrorReason}
                    otherErrorReasonNote={otherErrorReasonNote}
                    onChangeOtherErrorReasonNote={onChangeOtherErrorReasonNote}
                    isRequiredLinkCamera={isRequiredLinkCamera}
                    linkCamera={linkCamera}
                    onChangeLinkCamera={onChangeLinkCamera}
                    isRequiredBatchNo={isRequiredBatchNo}
                    expirationDateText={expirationDateText}
                    onChangeExpirationDateText={onChangeExpirationDateText}
                    onSubmitExpirationDateText={onSubmitExpirationDateText}
                    listProductBatch={listProductBatch}
                    onSelectBatch={onSelectBatch}
                    selectedProductBatch={selectedProductBatch}
                    showDatePicker={showDatePicker}
                    isInvalidChangeInventoryStatusId={isInvalidChangeInventoryStatusId}
                />
                <SaveButton
                    saveRequest={saveRequest}
                    isDisabled={isButtonDisabled}
                />
            </KeyboardAwareScrollView >
        </View >
    )
}

export default ReportProductDetail;
