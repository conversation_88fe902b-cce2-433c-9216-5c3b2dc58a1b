import React, { Component } from 'react';
import { Alert } from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import SafeAreaView from 'react-native-safe-area-view';
import { COLORS } from '@styles';
import { showBlock<PERSON>, hideBlockUI } from '@components';
import { ReportProductDetail, ReportProductHeader, SingleDatePicker } from './components';
import moment from 'moment';
import * as actionInventoryCreator from '../action';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { translate } from '@translate';
import { dateHelper, helper } from '@common';
import { API_CONST } from '@constants';
import { PRODUCT_STATE_TYPE } from './ReportProductStateEnum';

class ReportProductWithImei extends Component {
    constructor(props) {
        super(props);
        this.state = {
            note: '',
            trueProductSelected: {},
            isShowTrueProductInfo: false,
            isOuterImei: false,
            imageUrls: [{}, {}],
            isCheckedImei: false,
            spareImei: '',
            listProductByInventoryProcessCode: [],
            selectedErrorReason: {},
            otherErrorReasonNote: '',
            linkCamera: '',
            expirationDate: new Date(),
            expirationDateText: '',
            isVisible: false,
            listProductBatch: [],
            selectedProductBatch: {},
            errorReasonInfo: {}
        };
        this.spareImeiInputSystemType = 2;
        this.currentListAttachments = [];
    }

    componentDidMount() {
        const { productInfo, listRealProductStatusAttachmentBO, reportState: { REALPRODUCTSTATUSTYPEID }, isUpdate } = this.props.route.params;
        if (isUpdate) {
            let imageUrls = [{}, {}];
            let trueProductSelected = {};
            if (helper.IsNonEmptyArray(listRealProductStatusAttachmentBO)) {
                this.currentListAttachments = listRealProductStatusAttachmentBO;
                imageUrls = listRealProductStatusAttachmentBO.map(item => ({ ...item, filePath: API_CONST.API_GET_IMAGE_INVENTORY + item.filePath }));
            }
            const expirationDate = productInfo.productShelfLife > 0 ? new Date(productInfo.productShelfLife) : new Date();
            if (helper.isString(productInfo.realImei) && helper.IsNonEmptyString(productInfo.realImei)) {
                const { inventoryprocesscode, realInventoryProcessCode } = productInfo;
                trueProductSelected = {
                    imei: productInfo.realImei,
                    inventoryStatusID: productInfo.inventoryStatusID,
                    itemid: productInfo.itemid,
                    productid: productInfo.realProductId,
                    productname: productInfo.realProductName,
                    quantityunit: productInfo.quantityUnit,
                    quantityunitid: productInfo.quantityunitid,
                    inventoryStatusName: productInfo.inventoryStatusName,
                    quantity: 1,
                    isDifferentProcessCode: inventoryprocesscode != realInventoryProcessCode
                };
                this.onSelectTrueProduct(trueProductSelected);
            }
            this.setState({
                imageUrls: imageUrls,
                spareImei: (REALPRODUCTSTATUSTYPEID == PRODUCT_STATE_TYPE.SPARE_PRODUCT_AVA ||
                    REALPRODUCTSTATUSTYPEID == PRODUCT_STATE_TYPE.SPARE_PRODUCT
                ) ? productInfo.imei : '',
                note: helper.IsNonEmptyString(productInfo.content) ? productInfo.content : '',
                trueProductSelected: trueProductSelected,
                isShowTrueProductInfo: !helper.IsEmptyObject(trueProductSelected),
                isCheckedImei: true,
                linkCamera: productInfo.noteLinkCamera,
                otherErrorReasonNote: productInfo.causeErroContent,
                expirationDate: expirationDate,
                selectedProductBatch: {
                    batchNo: productInfo.batchNo
                },
                expirationDateText: moment(expirationDate).format('DD/MM/YYYY')
            });
        }
        if (
            REALPRODUCTSTATUSTYPEID != PRODUCT_STATE_TYPE.INCORRECT_PRODUCT_AVA &&
            REALPRODUCTSTATUSTYPEID != PRODUCT_STATE_TYPE.INCORRECT_PRODUCT &&
            REALPRODUCTSTATUSTYPEID != PRODUCT_STATE_TYPE.WRONG_CODE_PRODUCT
        ) {
            this.getErrorReason();
        } else {
            if (!isUpdate) {
                hideBlockUI();
            }
        }
    }

    saveRequest = () => {
        showBlockUI();
        const {
            note,
            trueProductSelected,
            imageUrls,
            spareImei,
            linkCamera,
            selectedErrorReason,
            otherErrorReasonNote,
            expirationDate,
            selectedProductBatch: { batchNo },
            errorReasonInfo
        } = this.state;
        const { actionInventory, navigation, route } = this.props;
        const { productInfo, reportState: { REALPRODUCTSTATUSTYPEID, ISCHECKSTOCK }, isUpdate } = route.params;
        let inputSystemType;
        let imageUrlsFilter = imageUrls.filter(item => !helper.IsEmptyObject(item));
        let realProductStatusAttachmentBOs = [...imageUrlsFilter, ...this.currentListAttachments];
        if (
            REALPRODUCTSTATUSTYPEID == PRODUCT_STATE_TYPE.INCORRECT_PRODUCT_AVA ||
            REALPRODUCTSTATUSTYPEID == PRODUCT_STATE_TYPE.INCORRECT_PRODUCT ||
            REALPRODUCTSTATUSTYPEID == PRODUCT_STATE_TYPE.WRONG_CODE_PRODUCT
        ) {
            inputSystemType = trueProductSelected.inputSystemType;
        } else if (REALPRODUCTSTATUSTYPEID == PRODUCT_STATE_TYPE.SPARE_PRODUCT_AVA || REALPRODUCTSTATUSTYPEID == PRODUCT_STATE_TYPE.SPARE_PRODUCT) {
            inputSystemType = this.spareImeiInputSystemType;
        } else {
            inputSystemType = 2;
        }
        const imei = (helper.IsNonEmptyString(spareImei) && !isUpdate) ? spareImei : productInfo.imei;
        let productStatusBODetail = [{
            "realProductStatusDetailId": productInfo.realProductStatusDetailId,
            "realProductStatusDTItemId": productInfo.realProductStatusDTItemId,
            "productId": productInfo.productid,
            "imei": imei,
            "quantity": 1,
            "exchangeQuantity": 0,
            "itemId": productInfo.itemid,
            "quantityUnitId": productInfo.quantityunitid,
            "content": note,
            "inputSystemType": inputSystemType,
            "realProductStatusDetailVs": productInfo.realProductStatusDetailVs
        }];
        const newInventoryStatusId = helper.IsNonEmptyArray(errorReasonInfo.listCauErroPIMBO) ?
            selectedErrorReason.changeInventoryStatusId :
            (helper.IsEmptyObject(errorReasonInfo.changeInventoryStatus) ? 0 : errorReasonInfo.changeInventoryStatus.changeInventoryStatusId);
        const realProductId = helper.IsEmptyObject(trueProductSelected) ? productInfo.productid : trueProductSelected.productid.trim();
        const realImei = helper.IsEmptyObject(trueProductSelected) ? imei : trueProductSelected.imei.trim();
        productStatusBODetail = productStatusBODetail.map(item => ({
            ...item,
            "realProductId": realProductId,
            "realImei": realImei,
            "realStockQuantity": 1,
            "realInventoryStatusId": newInventoryStatusId,
            "realQuantityUnitId": helper.IsEmptyObject(trueProductSelected) ? "" : trueProductSelected.quantityunitid,
            "realItemId": helper.IsEmptyObject(trueProductSelected) ? "" : trueProductSelected.itemid,
            "productShelfLife": helper.IsNonEmptyString(batchNo) ? dateHelper.formatDateYYYYMMDD(expirationDate) : "",
            "batchNo": helper.IsNonEmptyString(batchNo) ? batchNo : "",
            "causeErroContent": otherErrorReasonNote,
            "realPrdSTTTApplyCauseErroId": selectedErrorReason.realProductSttTypeApplyId,
            "noteLinkCamera": linkCamera
        }));
        const reportData = {
            productID: productInfo.productid,
            inventoryStatusID: productInfo.inventoryStatusID,
            masterQuantityUnitId: productInfo.quantityunitid,
            masterItemId: productInfo.itemid,
            isExchange: false,
            productStatusBO: {
                "realProductStatusTypeId": REALPRODUCTSTATUSTYPEID,
                "isCheckStock": ISCHECKSTOCK
            },
            productStatusBODetail,
            realProductStatusAttachmentBOs,
            realProductStatusId: productInfo.realProductStatusId
        };
        if (helper.IsNonEmptyString(realImei)) {
            actionInventory.saveReportRequestHasIMEI(reportData, isUpdate).then(() => {
                setTimeout(() => {
                    hideBlockUI();
                    actionInventory.getListReportProductStateRequest();
                    navigation.goBack();
                }, 1000);
            }).catch(({ msgError, errorType }) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: translate('common.btn_accept'),
                            style: 'default',
                            onPress: () => {
                                hideBlockUI();
                                if (errorType == 19) {
                                    actionInventory.resetReportProductStateRequest();
                                    navigation.pop(2);
                                }
                            }
                        }
                    ]
                );
            });
        } else {
            helper.LoggerInfo({ "{REPORT_PRODUCT_STATE save-imei}": reportData });
            Alert.alert(translate('common.notification'), "IMEI đúng của sản phẩm không hợp lệ, vui lòng kiểm tra lại", [
                {
                    text: translate('common.btn_accept'),
                    onPress: hideBlockUI,
                },
            ]);
        }
    };

    getListProductByInventoryProcess = (keyword = "", isProduct = 0) => {
        this.setState({ listProductByInventoryProcessCode: [] });
        showBlockUI();
        const { actionInventory } = this.props;
        const { productInfo: { inventoryprocesscode, productid, isUpdate } } = this.props.route.params;
        actionInventory.searchProductByInventoryProcessCode(keyword, isProduct).then(({ listProduct }) => {
            if (listProduct.length == 1) {
                if (!listProduct[0].isrequestimei && !isUpdate) {
                    Alert.alert(translate('common.notification'), translate('inventory.product_selected_no_imei'), [
                        {
                            text: translate('common.btn_accept'),
                            onPress: hideBlockUI,
                        },
                    ]);
                } else if (listProduct[0].inventoryprocesscode != inventoryprocesscode) {
                    Alert.alert(
                        translate('common.notification_uppercase'),
                        `${translate('inventory.product')} ${listProduct[0].productid.trim()} ${translate('inventory.differ_process_code')} ${productid.trim()}.\n${translate('inventory.import_export')}`,
                        [
                            {
                                text: translate('common.btn_cancel'),
                                style: 'default',
                                onPress: hideBlockUI
                            },
                            {
                                text: translate('common.btn_confirm'),
                                style: 'default',
                                onPress: () => {
                                    hideBlockUI();
                                    this.onSelectTrueProduct({ ...listProduct[0], isDifferentProcessCode: true });
                                }
                            }
                        ]
                    );
                } else {
                    hideBlockUI();
                    this.onSelectTrueProduct({ ...listProduct[0], isDifferentProcessCode: false });
                }
            } else {
                this.setState({ listProductByInventoryProcessCode: listProduct }, hideBlockUI);
            }
        }).catch((msgError) => {
            Alert.alert(translate('common.notification'), msgError, [
                {
                    text: translate('common.btn_accept'),
                    onPress: hideBlockUI,
                },
            ]);
        });
    };

    getListProductBatch = () => {
        showBlockUI();
        const { actionInventory } = this.props;
        const { productInfo: { inventoryStatusID, productid, batchNo }, reportState: { REALPRODUCTSTATUSTYPEID } } = this.props.route.params;
        const { expirationDate, trueProductSelected } = this.state;
        const realProductId = (helper.IsEmptyObject(trueProductSelected) ? productid : trueProductSelected.productid.trim());
        const data = {
            productId: productid,
            inventoryStatusId: inventoryStatusID,
            expirationDate: dateHelper.formatDateYYYYMMDD(expirationDate),
            realProductId,
            realProductStatusTypeId: REALPRODUCTSTATUSTYPEID
        };
        actionInventory.getBatchByExpirationDate(data).then((listProductBatch) => {
            hideBlockUI();
            if (!helper.IsNonEmptyString(batchNo)) {
                const validBatch = listProductBatch.find(batch => (batch.isCheckStock == 1 && batch.quantity >= 1) || batch.isCheckStock == 0);
                this.setState({
                    listProductBatch: listProductBatch,
                    selectedProductBatch: helper.IsEmptyObject(validBatch) ? {} : validBatch
                });
            } else {
                const selectedBatch = listProductBatch.find(batch => batch.batchNo == batchNo);
                this.setState({
                    listProductBatch: listProductBatch,
                    selectedProductBatch: helper.IsEmptyObject(selectedBatch) ? {} : selectedBatch
                });
            }
        }).catch((msgError) => {
            Alert.alert(translate('common.notification'), msgError, [
                {
                    text: translate('common.btn_accept'),
                    onPress: hideBlockUI,
                },
            ]);
        });
    };

    getErrorReason = (realProductId = '') => {
        const { actionInventory } = this.props;
        const { productInfo: { productid, inventoryStatusID, realPrdSTTTApplyCauseErroId, batchNo }, reportState: { REALPRODUCTSTATUSTYPEID, isReqInventoryStatus }, isUpdate } = this.props.route.params;
        showBlockUI();
        const data = {
            realProductStatusTypeId: REALPRODUCTSTATUSTYPEID,
            productId: productid.trim(),
            realProductId: realProductId,
            inventoryStatusId: inventoryStatusID,
            isReqInventoryStatus
        };
        actionInventory.getListErrorReason(data).then(errorInfo => {
            const selectedError = helper.IsNonEmptyArray(errorInfo.listCauErroPIMBO) ?
                errorInfo.listCauErroPIMBO.find(error => error.realProductSttTypeApplyId == realPrdSTTTApplyCauseErroId) : {};
            if (!helper.IsEmptyObject(selectedError)) {
                this.setState({ selectedErrorReason: selectedError, errorReasonInfo: errorInfo });
            } else {
                this.setState({ errorReasonInfo: errorInfo });
            }
            if (helper.IsNonEmptyString(batchNo) && isUpdate) {
                this.getListProductBatch();
            } else {
                hideBlockUI();
            }
        }).catch((msgError) => {
            Alert.alert(translate('common.notification'), msgError, [
                {
                    text: translate('common.btn_accept'),
                    onPress: () => {
                        hideBlockUI();
                        this.setState({ errorReasonInfo: { isError: true } });
                    }
                },
                {
                    text: translate('common.btn_notify_try_again'),
                    style: 'default',
                    onPress: () => this.getErrorReason(realProductId)
                }
            ]);
        });
    };

    onChangeNote = (text) => {
        this.setState({ note: text });
    };

    onSelectTrueProduct = (trueProductInfo) => {
        if (helper.IsEmptyObject(trueProductInfo)) {
            this.setState({ isShowTrueProductInfo: false });
        } else {
            const { isUpdate } = this.props.route.params;
            const { productInfo: { realProductImageURL } } = this.props.route.params;
            const { actionInventory } = this.props;
            this.getErrorReason(trueProductInfo.productid, false);
            if (isUpdate) {
                this.setState({ trueProductSelected: { ...trueProductInfo, imageurl: realProductImageURL, quantity: 1 } });
            } else {
                // showBlockUI();
                actionInventory.getProductImage(trueProductInfo.productidref).then(imageurl => {
                    // hideBlockUI();
                    this.setState({ trueProductSelected: { ...trueProductInfo, imageurl, quantity: 1 } });
                });
            }
        }
    };

    setImageUrls = (newImageUrls) => {
        this.setState({ imageUrls: newImageUrls });
    };

    onSelectErrorReason = (errorReason) => {
        const { selectedErrorReason } = this.state;
        const { productInfo: { causeErroContent }, isUpdate } = this.props.route.params;
        this.setState({
            selectedErrorReason: selectedErrorReason.realProductSttTypeApplyId == errorReason.realProductSttTypeApplyId ? {} : errorReason,
            otherErrorReasonNote: isUpdate ? causeErroContent : ''
        });
    };

    onChangeOtherErrorReasonNote = (note) => {
        this.setState({ otherErrorReasonNote: note });
    };

    onChangeLinkCamera = (linkCamera) => {
        this.setState({ linkCamera: linkCamera });
    };

    onChangeExpirationDateText = (text) => {
        this.setState({
            expirationDateText: text,
            listProductBatch: [],
            selectedProductBatch: {}
        });

    };

    onSubmitExpirationDateText = (text) => {
        const [day, month, year] = text.split('/');
        const dateValue = new Date(year, month - 1, day);
        if (dateHelper.isValidStrDateDDMMYYYY(text)) {
            this.setState({ expirationDate: dateValue }, this.getListProductBatch);
        } else {
            Alert.alert(
                translate('common.notification'),
                translate('inventory.invalid_expiration_date'), [
                {
                    text: translate('common.btn_accept'),
                    onPress: () => {
                        this.setState({ expirationDateText: '' });
                    },
                },
            ]);
        }
    };

    onSelectBatch = (productBatch) => {
        this.setState({ selectedProductBatch: productBatch });
    };

    showDatePicker = () => {
        this.setState({ isVisible: true });
    };

    checkImei = (productImei, trueProductId) => {
        const { productInfo: { imei, productid, inventoryStatusName, inventoryprocesscode } } = this.props.route.params;
        const { actionInventory } = this.props;
        const { trueProductSelected } = this.state;
        const { imeiformatexp } = trueProductSelected;
        if (productImei.trim() == imei.trim() && productid.trim() == trueProductId.trim()) {
            Alert.alert(
                translate('common.notification'),
                `IMEI ${productImei} ${translate('inventory.enter_product_same_product_shot')}`
            );
            return;
        };
        const isDifferentInventoryProcessCode = inventoryprocesscode != trueProductSelected.inventoryprocesscode;
        // if (productImei.trim() == imei.trim() && !isDifferentInventoryProcessCode) {
        //     this.setState({
        //         trueProductSelected: { ...trueProductSelected, imei: productImei, inputSystemType: 2, inventoryStatusName },
        //         isShowTrueProductInfo: true
        //     })
        // } else {
        showBlockUI();
        const data = {
            productID: trueProductId,
            imei: productImei
        };
        actionInventory.checkImei(data).then((res) => {
            if (!helper.IsEmptyObject(res) && (!isDifferentInventoryProcessCode || isDifferentInventoryProcessCode && res.isProduct == 1)) {
                const { inventoryStatusID } = res;
                if (inventoryStatusID !== 0) {
                    Alert.alert(translate('common.notification'),
                        `IMEI ${productImei} ${translate('inventory.import_inventory')}`, [
                        {
                            text: translate('common.btn_accept'),
                            onPress: () => {
                                hideBlockUI();
                                this.setState({ isShowTrueProductInfo: false });
                            },
                        },
                    ]);
                }
                else {
                    this.getProductStockImei(productImei, trueProductId);
                }
            } else {
                if (helper.isString(imeiformatexp)) {
                    const regExpIMEIOfProduct = new RegExp(imeiformatexp);
                    if (!regExpIMEIOfProduct.test(productImei.trim())) {
                        Alert.alert(
                            translate('common.notification'),
                            `IMEI ${productImei} ${translate('inventory.no_import_export_history')}`, [
                            {
                                text: translate('common.btn_accept'),
                                onPress: hideBlockUI,
                            },
                        ]);
                        return;
                    } else {
                        Alert.alert(
                            translate('common.notification_uppercase'),
                            `IMEI ${productImei} ${translate('inventory.no_history_import')}`,
                            [
                                {
                                    text: translate('common.btn_cancel'),
                                    style: 'default',
                                    onPress: hideBlockUI
                                },
                                {
                                    text: translate('common.btn_confirm'),
                                    style: 'default',
                                    onPress: () => {
                                        hideBlockUI();
                                        this.setState({
                                            trueProductSelected: { ...trueProductSelected, imei: productImei, inputSystemType: 1, inventoryStatusName },
                                            isShowTrueProductInfo: true,
                                            isOuterImei: true
                                        });
                                    }
                                }
                            ]
                        );
                    }
                } else {
                    Alert.alert(
                        translate('common.notification_uppercase'),
                        `IMEI ${productImei} ${translate('inventory.no_history_systems')}`,
                        [
                            {
                                text: translate('common.btn_cancel'),
                                style: 'default',
                                onPress: hideBlockUI
                            },
                            {
                                text: translate('common.btn_confirm'),
                                style: 'default',
                                onPress: () => {
                                    hideBlockUI();
                                    this.setState({
                                        trueProductSelected: { ...trueProductSelected, imei: productImei, inputSystemType: 1, inventoryStatusName },
                                        isShowTrueProductInfo: true,
                                        isOuterImei: true
                                    });
                                }
                            }
                        ]
                    );
                }
            }
        }).catch((err) => {
            this.setState({ isShowTrueProductInfo: false });
            Alert.alert(translate('common.notification'), err.msgError, [
                {
                    text: translate('common.btn_accept'),
                    onPress: hideBlockUI,
                },
            ]);
        });
        // }
    };

    checkImeiSpareProduct = (productImei) => {
        if (helper.IsEmptyString(productImei)) {
            this.setState({ isCheckedImei: false });
        } else {
            const { productInfo: { productid, imeiformatexp } } = this.props.route.params;
            const { actionInventory } = this.props;
            const data = {
                productID: productid,
                imei: productImei
            };
            showBlockUI();
            actionInventory.checkImei(data).then((res) => {
                if (!helper.IsEmptyObject(res) && res.isProduct == 1) {
                    const { inventoryStatusID, storeID } = res;
                    if (inventoryStatusID == 0) {
                        hideBlockUI();
                        this.setState({ isCheckedImei: true });
                        this.spareImeiInputSystemType = 2;
                    } else {
                        Alert.alert(translate('common.notification'),
                            `IMEI ${productImei} ${translate('inventory.in_stock')} ${storeID} ${translate('inventory.input_not_allow')}`, [
                            {
                                text: "Ok",
                                onPress: hideBlockUI,
                            },
                        ]);
                        return;
                    }
                } else {
                    if (helper.isString(imeiformatexp)) {
                        const regExpIMEIOfProduct = new RegExp(imeiformatexp);
                        if (!regExpIMEIOfProduct.test(productImei.trim())) {
                            Alert.alert(
                                translate('common.notification'),
                                `IMEI ${productImei} ${translate('inventory.no_import_export_history')}`, [
                                {
                                    text: translate('common.btn_accept'),
                                    onPress: hideBlockUI,
                                },
                            ]);
                            return;
                        } else {
                            Alert.alert(
                                translate('common.notification_uppercase'),
                                `IMEI ${productImei} ${translate('inventory.no_history_systems')}`,
                                [
                                    {
                                        text: translate('common.btn_cancel'),
                                        style: 'default',
                                        onPress: hideBlockUI
                                    },
                                    {
                                        text: translate('common.btn_confirm'),
                                        style: 'default',
                                        onPress: () => {
                                            hideBlockUI();
                                            this.setState({ isCheckedImei: true, isOuterImei: true });
                                            this.spareImeiInputSystemType = 1;
                                        }
                                    }
                                ]
                            );
                        }
                    } else {
                        Alert.alert(
                            translate('common.notification_uppercase'),
                            `IMEI ${productImei} ${translate('inventory.no_history_systems')}`,
                            [
                                {
                                    text: translate('common.btn_cancel'),
                                    style: 'default',
                                    onPress: hideBlockUI
                                },
                                {
                                    text: translate('common.btn_confirm'),
                                    style: 'default',
                                    onPress: () => {
                                        hideBlockUI();
                                        this.setState({ isCheckedImei: true, isOuterImei: true });
                                        this.spareImeiInputSystemType = 1;
                                    }
                                }
                            ]
                        );
                    }
                }
            }).catch((err) => {
                console.log(err);
                this.setState({ isCheckedImei: false });
                Alert.alert(translate('common.notification'), err.msgError, [
                    {
                        text: translate('common.btn_accept'),
                        onPress: hideBlockUI,
                    },
                ]);
            });
        }
    };

    getProductStockImei = (productImei, trueProductId) => {
        const { actionInventory } = this.props;
        const { productInfo: { inventoryStatusName } } = this.props.route.params;
        const { trueProductSelected } = this.state;
        actionInventory.getProductStockIMEI(productImei, trueProductId).then((stockInfo) => {
            if (!helper.IsEmptyObject(stockInfo) && stockInfo.inventorystatusid != 0 && stockInfo.isorder == 0 && stockInfo.isreceived == 1) {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    `IMEI ${productImei} ${translate('inventory.exists_system')}`,
                    [
                        {
                            text: translate('common.btn_accept'),
                            onPress: () => {
                                hideBlockUI();
                                this.setState({ isShowTrueProductInfo: false });
                            }
                        }
                    ]
                );
            } else {
                hideBlockUI();
                this.setState({
                    trueProductSelected: { ...trueProductSelected, imei: productImei, inputSystemType: 2, inventoryStatusName },
                    isShowTrueProductInfo: true
                });
            }
        }).catch(msgError => {
            Alert.alert(
                translate('common.notification_uppercase'),
                msgError,
                [
                    {
                        text: translate('common.btn_accept'),
                        onPress: hideBlockUI
                    }
                ]
            );
        });
    };

    render() {
        const {
            note,
            selectedErrorReason,
            expirationDate,
            isVisible,
            trueProductSelected,
            isShowTrueProductInfo,
            isOuterImei,
            imageUrls,
            isCheckedImei,
            spareImei,
            listProductByInventoryProcessCode,
            otherErrorReasonNote,
            linkCamera,
            expirationDateText,
            listProductBatch,
            selectedProductBatch,
            errorReasonInfo
        } = this.state;
        const { productInfo,
            reportState: { REALPRODUCTSTATUSTYPEID, REALPRODUCTSTATUSTYPENAME },
            reportState,
            listRealProductStatusAttachmentBO,
            isUpdate
        } = this.props.route.params;
        const imageUrlFileNames = imageUrls.filter(item => helper.IsNonEmptyString(item.fileName)).map(item => item.fileName);
        const currentImageUrlFileNames = listRealProductStatusAttachmentBO.map(item => item.fileName);
        // const listProductProcessCode = listProductByInventoryProcessCode.filter(item => item.inventoryprocesscode == inventoryprocesscode)
        return (
            <SafeAreaView
                style={{
                    flex: 1,
                    backgroundColor: COLORS.bgFFFFFF
                }}>
                <KeyboardAwareScrollView
                    style={{
                        flex: 1,
                        backgroundColor: COLORS.bgFFFFFF
                    }}
                    contentContainerStyle={{
                        alignItems: 'center'
                    }}
                    enableResetScrollToCoords={true}
                    keyboardShouldPersistTaps="always"
                    bounces={false}
                    overScrollMode="always"
                    showsHorizontalScrollIndicator={false}
                    showsVerticalScrollIndicator={false}
                    extraScrollHeight={0}
                    nestedScrollEnabled={true}
                >
                    <ReportProductHeader
                        stateName={REALPRODUCTSTATUSTYPENAME}
                        productInfo={productInfo}
                        stateId={REALPRODUCTSTATUSTYPEID}
                    />
                    <ReportProductDetail
                        isHasImei={true}
                        isExchange={false}
                        productInfo={productInfo}
                        listProductItem={[]}
                        onChangeQuantityExchangeProduct={() => { }}
                        onChangeQuantityNoExchangeProduct={() => { }}
                        note={note}
                        onChangeNote={this.onChangeNote}
                        saveRequest={this.saveRequest}
                        reportState={reportState}
                        trueProductSelected={trueProductSelected}
                        onSelectTrueProduct={this.onSelectTrueProduct}
                        getListProductByInventoryProcess={this.getListProductByInventoryProcess}
                        currentProductInfo={{}}
                        currentListProductItem={[]}
                        listProductByInventoryProcessCode={listProductByInventoryProcessCode}
                        isShowTrueProductInfo={isShowTrueProductInfo}
                        isUpdate={isUpdate}
                        isOuterImei={isOuterImei}
                        imageUrls={imageUrls}
                        setImageUrls={this.setImageUrls}
                        checkTrueImei={this.checkImei}
                        checkSpareImei={this.checkImeiSpareProduct}
                        isCheckedImei={isCheckedImei}
                        spareProductImei={spareImei}
                        setSpareProductImei={(text) => {
                            this.setState({ spareImei: text });
                        }}
                        currentAttachments={this.currentListAttachments}
                        errorInfo={errorReasonInfo}
                        selectedErrorReason={selectedErrorReason}
                        onSelectErrorReason={this.onSelectErrorReason}
                        otherErrorReasonNote={otherErrorReasonNote}
                        onChangeOtherErrorReasonNote={this.onChangeOtherErrorReasonNote}
                        linkCamera={linkCamera}
                        onChangeLinkCamera={this.onChangeLinkCamera}
                        expirationDateText={expirationDateText}
                        onChangeExpirationDateText={this.onChangeExpirationDateText}
                        listProductBatch={listProductBatch}
                        onSelectBatch={this.onSelectBatch}
                        selectedProductBatch={selectedProductBatch}
                        showDatePicker={this.showDatePicker}
                    />
                    <SingleDatePicker
                        isVisible={isVisible}
                        hideModal={() => {
                            this.setState({ isVisible: false });
                        }}
                        selectedDate={expirationDate}
                        onSubmit={(date) => {
                            this.setState(
                                {
                                    expirationDate: new Date(date),
                                    expirationDateText:
                                        moment(date).format(
                                            'DD/MM/YYYY'
                                        ),
                                    listProductBatch: [],
                                    selectedProductBatch: {}
                                }, this.getListProductBatch
                            );
                        }}
                    />
                </KeyboardAwareScrollView>
            </SafeAreaView>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        userInfo: state.userReducer,
    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        actionInventory: bindActionCreators(actionInventoryCreator, dispatch)
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(ReportProductWithImei);