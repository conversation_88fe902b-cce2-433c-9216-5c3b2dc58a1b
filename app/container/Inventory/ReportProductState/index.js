import React, { Component } from 'react';
import { View, Alert, FlatList, TouchableOpacity, Keyboard } from 'react-native';
import { ScanBarcodeML, showBlockUI, hide<PERSON>lockUI, BaseLoading, MyText } from "@components";
import SafeAreaView from "react-native-safe-area-view";
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { translate } from "@translate";
import { COLORS } from "@styles";
import { InputSearchAndScanBarCode, InventoryBackHeader } from '../InventoryProduct/components';
import * as actionInventoryCreator from '../action';
import { ModalInventoryProduct, ItemGroupProduct } from './components';
import { helper } from '@common';
import { constants } from '@constants';
import ModalProductStatus from './components/ModalProductStatus';
import { showMessage } from "react-native-flash-message";
import { PRODUCT_STATE_TYPE } from './ReportProductStateEnum';

class ReportProductState extends Component {
    constructor(props) {
        super(props);
        this.state = {
            isVisibleCamera: false,
            valueSearch: "",
            isVisibleModal: false,
            listSearchProductInfo: [],
            isVisible: false,
            isVisibleModalStatus: false,
            listProductStatus: []
        };
        this.baseProductInfo = {};
        this.listProductItem = [];
    }

    getListReportRequest = () => {
        const { actionInventory } = this.props;
        actionInventory.getListReportProductStateRequest();
    };

    onSearchProduct = (keyword, imei = '', isProduct = false) => {
        const { actionInventory } = this.props;
        const { reportState } = this.props.route.params;
        if (helper.IsNonEmptyString(keyword)) {
            this.setState({ valueSearch: keyword });
            showBlockUI();
            const searchData = {
                keyword: helper.IsNonEmptyString(imei) ? imei : keyword,
                imeiProductId: helper.IsNonEmptyString(imei) ? keyword : '',
                isProduct: helper.IsNonEmptyString(imei) ? false : isProduct,
                realProductStatusTypeId: reportState.REALPRODUCTSTATUSTYPEID
            };
            actionInventory.getListReportProduct(searchData).then((response) => {
                const { listRealStatusProductItems, listRealStatusProducts } = response;
                if (helper.IsEmptyArray(listRealStatusProducts)) {
                    Alert.alert(translate('common.notification'), translate('inventory.product_info_not_found'), [
                        {
                            text: translate('common.btn_accept'),
                            onPress: hideBlockUI,
                        },
                    ]);
                } else {
                    this.listProductItem = listRealStatusProductItems.map(item => ({ ...item, reportQuantity: 0 })).sort((first, second) => first.exchangeQuantity - second.exchangeQuantity);
                    if (listRealStatusProducts.length > 1) {
                        hideBlockUI();
                        this.setState({
                            listSearchProductInfo: listRealStatusProducts,
                            isVisibleModal: true
                        });
                    } else {
                        const productInformation = { ...listRealStatusProducts[0] };
                        if (productInformation.isrequestimei) {
                            if (reportState.REALPRODUCTSTATUSTYPEID == PRODUCT_STATE_TYPE.SPARE_PRODUCT_AVA || reportState.REALPRODUCTSTATUSTYPEID == PRODUCT_STATE_TYPE.SPARE_PRODUCT) { // case thừa sản phẩm thì chỉ cho nhập mã sản phẩm rồi vào màn hình chi tiết mới check imei
                                if (helper.IsNonEmptyString(productInformation.imei)) {
                                    Alert.alert(translate('common.notification'), translate('inventory.pls_enter_product_before_enter_imei'), [
                                        {
                                            text: translate('common.btn_accept'),
                                            onPress: hideBlockUI,
                                        },
                                    ]);
                                } else {
                                    hideBlockUI();
                                    this.baseProductInfo = productInformation;
                                    const { productid, subgroupid } = productInformation;
                                    this.getProductStatus(productid, subgroupid);
                                }
                            } else {
                                this.getProductStockImei(productInformation);
                            }
                        } else {
                            this.baseProductInfo = productInformation;
                            const { productid, subgroupid } = productInformation;
                            this.getProductStatus(productid, subgroupid);
                        }
                    }
                }
            }).catch((msgError) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: 'default',
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: 'default',
                            onPress: () => this.onSearchProduct(keyword, imei, isProduct)
                        }
                    ]
                );
            });
        }
    };

    getProductStatus = (productId, subGroupId) => {
        const { actionInventory } = this.props;
        const { reportState } = this.props.route.params;
        const data = {
            productId,
            isCheckStock: reportState.ISCHECKSTOCK,
            subGroupId
        };
        actionInventory.getListReportProductStatus(data)
            .then((listProductStatus) => {
                hideBlockUI();
                this.setState({
                    listProductStatus,
                    isVisibleModalStatus: true
                });
            })
            .catch((msgError) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: 'default',
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: 'default',
                            onPress: () => {
                                this.getProductStatus(productId, subGroupId);
                            }
                        }
                    ]
                );
            });
    };

    onSelectProduct = (productData) => {
        this.setState({ isVisibleModal: false }, () => {
            this.onSearchProduct(productData.productid.trim(), productData.imei, true);
        });
    };

    getProductStockImei = (productInfo) => {
        const { reportState } = this.props.route.params;
        const { navigation, actionInventory, listRequestResponse } = this.props;
        const productIMEI = productInfo.imei.trim();
        actionInventory.getProductStockIMEI(productIMEI, productInfo.productid).then((stockInfo) => {
            if (!helper.IsEmptyObject(stockInfo) && stockInfo.inventorystatusid != 0 && stockInfo.isorder == 0 && stockInfo.isreceived == 1) {
                const existedRequest = listRequestResponse.find(item => helper.IsNonEmptyString(item.imei) && item.imei.trim() == productIMEI);
                if (helper.IsEmptyObject(existedRequest)) {
                    navigation.navigate("ReportProductWithImei", {
                        productInfo,
                        reportState,
                        isUpdate: false,
                        listRealProductStatusAttachmentBO: []
                    });
                } else {
                    this.onPressRequestItem(existedRequest, true);
                }
            } else {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    `IMEI ${productIMEI} ${translate('inventory.not_enough_inventory')}`,
                    [
                        {
                            text: translate('common.btn_accept'),
                            onPress: hideBlockUI
                        }
                    ]
                );
            }
        }).catch(msgError => {
            console.log(msgError);
            Alert.alert(
                translate('common.notification_uppercase'),
                msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: 'default',
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: 'default',
                        onPress: () => {
                            this.getProductStockImei(productInfo);
                        }
                    }
                ]
            );
        });
    };

    onSelectProductStatus = (productStatus) => {
        const { reportState } = this.props.route.params;
        const { navigation, actionInventory, listRequestResponse } = this.props;
        const { INVENTORYSTATUSID, INVENTORYSTATUSNAME } = productStatus;
        this.setState({ isVisibleModalStatus: false }, () => {
            if ((reportState.REALPRODUCTSTATUSTYPEID == PRODUCT_STATE_TYPE.SPARE_PRODUCT_AVA || reportState.REALPRODUCTSTATUSTYPEID == PRODUCT_STATE_TYPE.SPARE_PRODUCT) && this.baseProductInfo.isrequestimei) { // case thừa sản phẩm
                navigation.navigate("ReportProductWithImei", {
                    productInfo: { ...this.baseProductInfo, reportQuantity: 0, inventoryStatusName: INVENTORYSTATUSNAME, inventoryStatusID: INVENTORYSTATUSID },
                    reportState,
                    isUpdate: false,
                    listRealProductStatusAttachmentBO: []
                });
            } else if (reportState.ISCHECKSTOCK == 1) {
                showBlockUI();
                const data = {
                    productId: this.baseProductInfo.productid,
                    inventoryStatusId: INVENTORYSTATUSID,
                    inventoryStatusName: INVENTORYSTATUSNAME
                };
                actionInventory.getProductStock(data).then((stockInfo) => {
                    const existedRequest = listRequestResponse.find(item => item.productid.trim() == this.baseProductInfo.productid && item.inventoryStatusId == INVENTORYSTATUSID);
                    if (helper.IsEmptyObject(existedRequest)) {
                        navigation.navigate("ReportProductNoImei", {
                            productInfo: { ...this.baseProductInfo, reportQuantity: 0, inventoryStatusName: INVENTORYSTATUSNAME },
                            listProductItem: this.listProductItem,
                            reportState,
                            productStatus,
                            isUpdate: false,
                            listRealProductStatusAttachmentBO: []
                        });
                    } else {
                        this.onPressRequestItem(existedRequest);
                    }
                }).catch(msgError => {
                    Alert.alert(
                        translate('common.notification_uppercase'),
                        msgError,
                        [
                            {
                                text: translate('common.btn_accept'),
                                onPress: hideBlockUI
                            }
                        ]
                    );
                });
            } else {
                const existedRequest = listRequestResponse.find(item => item.productid.trim() == this.baseProductInfo.productid && item.inventoryStatusId == INVENTORYSTATUSID);
                if (helper.IsEmptyObject(existedRequest)) {
                    showBlockUI();
                    navigation.navigate("ReportProductNoImei", {
                        productInfo: { ...this.baseProductInfo, reportQuantity: 0, inventoryStatusName: INVENTORYSTATUSNAME },
                        listProductItem: this.listProductItem,
                        reportState,
                        productStatus,
                        isUpdate: false,
                        listRealProductStatusAttachmentBO: []
                    });
                } else {
                    this.onPressRequestItem(existedRequest);
                }
            }
        });
    };

    onGoBack = () => {
        const { reportProductStateRequestId, outSystemReportProductStateRequestId, navigation } = this.props;
        if (helper.IsNonEmptyString(reportProductStateRequestId + outSystemReportProductStateRequestId)) {
            Alert.alert(translate('common.notification_uppercase'), translate('inventory.not_out'));
        } else {
            this.setState({ valueSearch: "" }, navigation.goBack);
        }
    };

    createRequest = () => {
        showBlockUI();
        const { actionInventory, navigation } = this.props;
        actionInventory.finishReportProductState().then(() => {
            showMessage({
                message: translate('inventory.created_success'),
                type: "info", duration: 1500,
                position: 'center',
                style: {
                    backgroundColor: COLORS.bg1E88E5,
                    height: constants.getSize(120),
                    width: constants.getSize(300),
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderColor: COLORS.bd218DEB,
                    borderWidth: 1,
                    backgroundColor: COLORS.bgFFFFFF
                },
                titleStyle: {
                    fontSize: 18,
                    marginTop: 40,
                    color: COLORS.txt333333
                }
            });
            setTimeout(() => {
                hideBlockUI();
                this.setState({ valueSearch: "" }, navigation.goBack);
            }, 1500);
        }).catch(({ msgError, errorType }) => {
            Alert.alert(translate('common.notification'), msgError, [
                {
                    text: translate('common.btn_accept'),
                    onPress: () => {
                        hideBlockUI();
                        if (errorType == 19) {
                            actionInventory.resetReportProductStateRequest();
                            navigation.goBack();
                        }
                    }
                },
            ]);
        });
    };

    onPressRequestItem = (item) => {
        const { actionInventory, navigation } = this.props;
        showBlockUI();
        actionInventory.getReportRequestDetail(item.realProductStatusDetailId).then((response) => {
            const { listRealStatusProducts, listRealProductStatusDetails, listRealProductStatusAttachmentBO } = response;
            const { reportState } = this.props.route.params;
            const { isExchangeQuantity } = listRealStatusProducts[0];

            if (listRealStatusProducts[0].isrequestimei) {
                const { inventoryStatusId, realProductName, inventoryStatusName, content, noteLinkCamera, causeErroContent, productShelfLife, batchNo } = listRealProductStatusDetails[0];
                navigation.navigate("ReportProductWithImei", {
                    productInfo: {
                        ...listRealStatusProducts[0],
                        ...listRealProductStatusDetails[0],
                        reportQuantity: 1,
                        inventoryStatusID: inventoryStatusId,
                        realProductName: realProductName,
                        inventoryStatusName: inventoryStatusName,
                        content: helper.isString(content) ? content : "",
                        noteLinkCamera: helper.isString(noteLinkCamera) ? noteLinkCamera : "",
                        causeErroContent: helper.isString(causeErroContent) ? causeErroContent : "",
                        productShelfLife: productShelfLife,
                        batchNo: helper.isString(batchNo) ? batchNo : "",
                    },
                    reportState,
                    isUpdate: true,
                    listRealProductStatusAttachmentBO
                });
            } else {
                const listProductItem = listRealProductStatusDetails.map(item => ({
                    ...item,
                    reportQuantity: isExchangeQuantity ? item.countQuantity : item.quantity,
                    isExchangeQuantity: isExchangeQuantity,
                    productid: item.exchangeProductId,
                    quantityunit: item.exchangeQuantityUnit,
                    productname: item.exchangeProductName
                })).sort((first, second) => first.exchangeQuantity - second.exchangeQuantity);
                const { inventoryStatusId, inventoryStatusName } = listProductItem[0];
                navigation.navigate("ReportProductNoImei", {
                    productInfo: isExchangeQuantity ? {
                        ...listRealStatusProducts[0],
                        inventoryStatusName,
                        content: listProductItem[0].content,
                        realInventoryProcessCode: listProductItem[0].realInventoryProcessCode,
                        realPrdSTTTApplyCauseErroId: listProductItem[0].realPrdSTTTApplyCauseErroId,
                        noteLinkCamera: listProductItem[0].noteLinkCamera,
                        causeErroContent: listProductItem[0].causeErroContent,
                        productShelfLife: listProductItem[0].productShelfLife,
                        batchNo: listProductItem[0].batchNo,
                    } :
                        {
                            ...listProductItem[0],
                            ...listRealStatusProducts[0],
                            inventoryStatusName
                        },
                    listProductItem: isExchangeQuantity ? listProductItem : [],
                    reportState,
                    productStatus: {
                        INVENTORYSTATUSID: inventoryStatusId,
                        INVENTORYSTATUSNAME: inventoryStatusName
                    },
                    isUpdate: true,
                    listRealProductStatusAttachmentBO
                });
            }
        }).catch((msgError) => {
            Alert.alert(translate('common.notification'), msgError, [
                {
                    text: translate('common.btn_accept'),
                    onPress: hideBlockUI,
                },
            ]);
        });
    };

    onDeleteRequest = (item) => {
        const { reportState: { REALPRODUCTSTATUSTYPENAME } } = this.props.route.params;
        Alert.alert(
            translate('common.notification_uppercase'),
            `Bạn có chắc chắn xoá yêu cầu khai báo sản phẩm ${REALPRODUCTSTATUSTYPENAME}`,
            [
                {
                    text: translate('common.btn_back'),
                    style: 'default',
                    onPress: () => { }
                },
                {
                    text: translate('common.btn_confirm'),
                    style: 'default',
                    onPress: () => {
                        this.deleteRequest(item);
                    }
                }
            ]
        );


    };

    deleteRequest = (requestInfo) => {
        const { actionInventory } = this.props;
        const { reportState: { REALPRODUCTSTATUSTYPEID, ISCHECKSTOCK } } = this.props.route.params;
        showBlockUI();
        actionInventory.deleteReportProductStateRequest({ ...requestInfo, realProductStatusTypeId: REALPRODUCTSTATUSTYPEID, isCheckStock: ISCHECKSTOCK, }).then(() => {
            hideBlockUI();
            this.getListReportRequest();
        }).catch((msgError) => {
            Alert.alert(
                translate('common.notification_uppercase'),
                msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: 'default',
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: 'default',
                        onPress: () => {
                            this.deleteRequest(requestInfo);
                        }
                    }
                ]
            );
        });
    };

    render() {
        const {
            isVisibleCamera,
            valueSearch,
            isVisibleModal,
            listSearchProductInfo,
            isVisibleModalStatus,
            listProductStatus
        } = this.state;
        const {
            listReportProductStateRequest,
            stateListReportProductStateRequest: { isFetching, isEmpty, description, isError },
            reportProductStateRequestId,
            outSystemReportProductStateRequestId
        } = this.props;
        const { reportState: { REALPRODUCTSTATUSTYPENAME, REALPRODUCTSTATUSTYPEID } } = this.props.route.params;
        return (
            <View style={{
                flex: 1
            }}>
                <InventoryBackHeader
                    onGoBack={this.onGoBack}
                    title={translate('inventory.product_list')}
                    key={"ReportProductStateHeader"}
                />
                <SafeAreaView style={{
                    flex: 1,
                    backgroundColor: COLORS.bgFFFFFF,
                }}>
                    <View
                        style={{
                            width: constants.width,
                            backgroundColor: COLORS.bg00A896,
                            paddingVertical: 7,
                            paddingHorizontal: 10
                        }}
                    >
                        <MyText
                            text={translate('inventory.status_1')}
                            style={{
                                color: COLORS.txtFFFFFF,
                                fontWeight: 'bold'
                            }}
                            addSize={3}
                        >
                            <MyText
                                text={REALPRODUCTSTATUSTYPENAME}
                                style={{
                                    fontWeight: 'bold',
                                    color: COLORS.txtFFFF00
                                }}
                                addSize={3}
                            />
                        </MyText>
                    </View>
                    <View style={{
                        flex: 1,
                        paddingHorizontal: 10
                    }}>
                        <InputSearchAndScanBarCode
                            value={valueSearch}
                            onChangeText={(text) => {
                                this.setState({ valueSearch: text });
                            }}
                            onBlur={() => {
                                this.onSearchProduct(valueSearch.trim());
                            }}
                            clearText={() => {
                                this.setState({ valueSearch: "" });
                            }}
                            placeholder={translate('inventory_share.input_lots_code_name')}
                            onPressScanIcon={() => {
                                this.setState({ isVisibleCamera: true, valueSearch: "" }, Keyboard.dismiss);
                            }}
                        />
                        <BaseLoading
                            isLoading={isFetching}
                            isEmpty={isEmpty}
                            textLoadingError={description}
                            isError={isError}
                            onPressTryAgains={this.getListReportRequest}
                            content={
                                <FlatList
                                    data={listReportProductStateRequest}
                                    renderItem={({ item, index }) => <ItemGroupProduct
                                        groupProductItem={item}
                                        onPressItem={this.onPressRequestItem}
                                        onDeleteRequest={this.onDeleteRequest}
                                        realProductStatusTypeId={REALPRODUCTSTATUSTYPEID}
                                    />}
                                    keyExtractor={(item, index) => index.toString()}
                                    showsVerticalScrollIndicator={false}
                                    showsHorizontalScrollIndicator={false}
                                    removeClippedSubviews={true}
                                    keyboardShouldPersistTaps={"always"}
                                    bounces={false}

                                />
                            }
                        />
                    </View>
                    {helper.IsNonEmptyString(reportProductStateRequestId + outSystemReportProductStateRequestId) && <TouchableOpacity
                        style={{
                            backgroundColor: COLORS.btn288AD6,
                            paddingVertical: 10,
                            borderRadius: 5,
                            width: constants.width / 3,
                            alignItems: 'center',
                            alignSelf: 'center',
                            marginVertical: 10
                        }}
                        activeOpacity={0.6}
                        onPress={this.createRequest}
                    >
                        <MyText
                            text={translate('inventory.create_request')}
                            style={{
                                fontWeight: 'bold',
                                color: COLORS.txtFFFFFF
                            }}
                            addSize={2}
                        />
                    </TouchableOpacity>}
                </SafeAreaView>
                {isVisibleCamera &&
                    <ScanBarcodeML
                        isVisible={isVisibleCamera}
                        closeCamera={() => {
                            this.setState({ isVisibleCamera: false });
                        }}
                        resultScanBarcode={(barcode) => {
                            this.setState({
                                valueSearch: barcode.trim(),
                                isVisibleCamera: false,
                            }, () => this.onSearchProduct(barcode.trim()));
                        }}
                    />
                }

                {
                    isVisibleModal &&
                    <ModalInventoryProduct
                        isVisible={isVisibleModal}
                        listSearchProductInfo={listSearchProductInfo}
                        hideModal={() => {
                            this.setState({ isVisibleModal: false });
                        }}
                        onSelectProduct={this.onSelectProduct}
                    />
                }
                {
                    isVisibleModalStatus &&
                    <ModalProductStatus
                        isVisible={isVisibleModalStatus}
                        listProductStatus={listProductStatus}
                        hideModal={() => {
                            this.setState({ isVisibleModalStatus: false });
                        }}
                        onSelectStatus={this.onSelectProductStatus}
                    />
                }

            </View>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        listReportProductStateRequest: state.inventoryReducer.listReportProductStateRequest,
        stateListReportProductStateRequest: state.inventoryReducer.stateListReportProductStateRequest,
        reportProductStateRequestId: state.inventoryReducer.reportProductStateRequestId,
        outSystemReportProductStateRequestId: state.inventoryReducer.outSystemReportProductStateRequestId,
        listRequestResponse: state.inventoryReducer.listRequestResponse
    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        actionInventory: bindActionCreators(actionInventoryCreator, dispatch)
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(ReportProductState);
