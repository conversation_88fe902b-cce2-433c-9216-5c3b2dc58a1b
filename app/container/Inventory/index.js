import React, { Component } from 'react';
import { TouchableOpacity, View, SafeAreaView, StyleSheet, Alert } from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { COLORS } from '@styles';
import { translate } from "@translate";
import { constants } from '@constants';
import { MyText, Icon, showBlockUI, hideBlockUI, BaseLoading } from "@components";
import * as actionInventoryCreator from './action';

class Inventory extends Component {
  constructor(props) {
    super(props)
    const { userInfo: { permissions, brandID, storeGroupID, storeID, areaID } } = props;
    const keyPermissions = new Set(permissions);
    // Inventory
    const isInventoryGroup = (STOREGROUP_APPLY_INV.has(`${storeGroupID}`) || AREAGROUP_APPLY_INV.has(`${areaID}`)) && BRANDGROUP_APPLY_INV.has(`${brandID}`);
    const isInventory = STORE_APPLY_INV.has(`${storeID}`) || BRAND_APPLY_INV.has(`${brandID}`) || isInventoryGroup;
    // ProductState
    const isProductStateGroup = (STOREGROUP_APPLY.has(`${storeGroupID}`) || AREAGROUP_APPLY.has(`${areaID}`)) && BRANDGROUP_APPLY.has(`${brandID}`);
    const isProductState = STORE_APPLY.has(`${storeID}`) || BRAND_APPLY.has(`${brandID}`) || isProductStateGroup;
    const initData = [
      {
        iconSet: "Feather",
        iconName: "map-pin",
        color: COLORS.ic147EFB,
        title: translate('inventory.area_management'),
        name: "AreaManagement",
        isGetInventoryType: false,
        isHasPermission: isInventory,
        // isHasPermission: keyPermissions.has("INV_INVENTORYAREA_AREA")
      },
      {
        iconSet: "Feather",
        iconName: "check-circle",
        color: COLORS.ic00A896,
        title: translate('inventory.inventory'),
        name: "InventoryProduct",
        isGetInventoryType: true.valueOf,
        isHasPermission: isInventory,
        // isHasPermission: keyPermissions.has("INV_INVENTORYTERM_INVENTORY")
      },
      {
        iconSet: "Feather",
        iconName: "file-text",
        color: COLORS.icFF6600,
        title: translate('inventory.view_inventory_result'),
        name: "InventoryDeviant",
        isGetInventoryType: true,
        isHasPermission: isInventory,
        // isHasPermission: keyPermissions.has("INV_INVENTORYPROCESS_VIEWDIFFERENT")
      },
      {
        iconSet: "MaterialCommunityIcons",
        iconName: "account-cash-outline",
        color: COLORS.icD0021B,
        title: translate('inventory.view_arrears_result'),
        name: "ArrearsResult",
        isGetInventoryType: false,
        isHasPermission: isInventory,
        // isHasPermission: keyPermissions.has("INV_INVENTORYDEBTPROCESS_VIEWDEBT")
      },
      {
        iconSet: "Ionicons",
        iconName: "newspaper-outline",
        color: COLORS.icF16667,
        title: translate('inventory.report_product_state'),
        name: "ProductState",
        isGetInventoryType: false,
        isHasPermission: isProductState
      }
    ]
    this.state = {
      inventoryMenu: initData
    }
  }

  componentDidMount() {
    this.getListStatus();
  }

  getListStatus = () => {
    const { actionInventory } = this.props;
    actionInventory.getListProductStatus();
  }

  onPressItem = ({ name, isGetInventoryType, isHasPermission, title }) => () => {
    if (isHasPermission) {
      const { navigation, actionInventory } = this.props;
      if (!isGetInventoryType) {
        navigation.navigate(name);
      } else {
        showBlockUI();
        actionInventory.getListInventoryType().then(() => {
          hideBlockUI();
          navigation.navigate(name);
        }).catch((msgError) => {
          Alert.alert(
            translate('common.notification_uppercase'),
            msgError,
            [{
              text: "OK",
              onPress: hideBlockUI
            }]
          )
        })
      }
    } else {
      const { userInfo: { storeID } } = this.props;
      const msgError = `Kho [${storeID}] không thuộc khu vực triển khai sử dụng chức năng [${title}], vui lòng liên hệ KSNB`;
      Alert.alert(
        translate('common.notification_uppercase'),
        msgError,
        [{
          text: "OK",
          onPress: hideBlockUI
        }]
      )
    }
  }

  render() {
    const { inventoryMenu } = this.state;
    const { stateListProductStatus: { isFetching, isEmpty, description, isError } } = this.props;
    return (
      <SafeAreaView style={{ flex: 1 }}>
        <BaseLoading
          isLoading={isFetching}
          isEmpty={isEmpty}
          textLoadingError={description}
          isError={isError}
          onPressTryAgains={this.getListStatus}
          content={
            <View style={{
              width: constants.width,
              alignItems: "center",
              padding: 10,
              flexDirection: "row",
              justifyContent: "space-between",
              flexWrap: "wrap"
            }}>
              {inventoryMenu.map((ele, index) => {
                const { iconSet, iconName, color, title } = ele;
                return (
                  <ItemMenu
                    iconSet={iconSet}
                    iconName={iconName}
                    color={color}
                    title={title}
                    onPress={this.onPressItem(ele)}
                    key={index.toString()}
                  />
                );
              })}
            </View>
          }
        />
      </SafeAreaView>
    )
  }
}

const mapStateToProps = function (state) {
  return {
    userInfo: state.userReducer,
    stateListProductStatus: state.inventoryReducer.stateListProductStatus
  }
}

const mapDispatchToProps = function (dispatch) {
  return {
    actionInventory: bindActionCreators(actionInventoryCreator, dispatch)
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(Inventory);

const ItemMenu = ({ iconSet, iconName, color, title, onPress }) => {
  return (
    <TouchableOpacity style={{
      width: (constants.width - 40) / 2,
      margin: 4,
      borderColor: COLORS.bd288AD6,
      borderWidth: StyleSheet.hairlineWidth,
      borderRadius: 4,
      paddingVertical: 10,
      paddingHorizontal: 4,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: COLORS.bgFFFFFF
    }}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <Icon
        iconSet={iconSet}
        name={iconName}
        color={color}
        size={40}
      />
      <MyText
        text={title}
        style={{
          color: COLORS.txt434343,
          marginTop: 4,
          textAlign: 'center'
        }}
      />
    </TouchableOpacity>
  );
}
// Inventory
const configStore_INV = ``;
const configBrand_INV = `1,2,15,16`;
const configStoreGroup_INV = ``;
const configAreaGroup_INV = ``;
const configBrandGroup_INV = ``;
const STORE_APPLY_INV = new Set(configStore_INV.split(","));
const BRAND_APPLY_INV = new Set(configBrand_INV.split(","));
const STOREGROUP_APPLY_INV = new Set(configStoreGroup_INV.split(","));
const AREAGROUP_APPLY_INV = new Set(configAreaGroup_INV.split(","));
const BRANDGROUP_APPLY_INV = new Set(configBrandGroup_INV.split(","));
// ProductState
const configStore = `6615`;
const configBrand = `15,20,1,2,16`;
const configStoreGroup = ``;
const configAreaGroup = ``;
const configBrandGroup = ``;
const STORE_APPLY = new Set(configStore.split(","));
const BRAND_APPLY = new Set(configBrand.split(","));
const STOREGROUP_APPLY = new Set(configStoreGroup.split(","));
const AREAGROUP_APPLY = new Set(configAreaGroup.split(","));
const BRANDGROUP_APPLY = new Set(configBrandGroup.split(","));