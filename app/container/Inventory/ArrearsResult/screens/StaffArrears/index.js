import React, { Component } from 'react';
import {
    View,
    Keyboard,
    ScrollView,
    Alert,
    TouchableOpacity
} from 'react-native';
import {
    MyText,
    CaptureCamera,
    showBlockUI,
    hideBlockUI,
    Icon
} from "@components";
import SafeAreaView from "react-native-safe-area-view";
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { translate } from "@translate";
import { COLORS } from "@styles";
import { constants, API_CONST, ENUM } from '@constants';
import { helper } from '@common';
import { launchImageLibrary } from 'react-native-image-picker';
import { ImageProcess, AgentCollectMoney } from '../../components';
import * as actionInventoryCreator from '../../../action';
import { getImageCDN } from '../../../../ActiveSimManager/action';
import ListDataStaffArrears from './components/ListDataStaffArrears';
import OptionButton from './components/OptionButton';
const { FILE_PATH: { STAFF_ARREARS } } = ENUM;

class StaffArrears extends Component {
    constructor(props) {
        super(props);
        this.state = {
            listTermStaffArrears: [],
            staffName: "",
            staffId: "",
            moneyArrears: 0,
            totalInputText: 0,
            imageUrls: [{}, {}],
            imageIndex: 0,
            isVisibleCamera: false,
            isRequiredImage: false,
            isSave: false,
            isCheckImg: false,
            isOpenCamera: false,
            isActived: 1,
            enddatework: "",
            workStoreId: 0
        },
            this.dataImageRoot = [];
    }

    componentDidMount() {
        const { listStaffArrears, amountPaid } = this.props.route.params;
        const totalInput = listStaffArrears.reduce((a, b) => a + b.totalMoney, 0);
        this.setState({
            listTermStaffArrears: listStaffArrears,
            totalInputText: totalInput,
            moneyArrears: amountPaid - totalInput
        });
        this.getListImge();
        if (listStaffArrears.findIndex(item => item.totalMoney >= 1000000) != -1) {
            this.setState({
                isRequiredImage: true
            });
        }
    }

    componentDidUpdate(prevProps, prevState) {
        if (prevState.imageUrls !== this.state.imageUrls) {
            this.setState({ isCheckImg: false });
            this.state.imageUrls.forEach((x) => {
                if (!helper.IsEmptyObject(x)) {
                    this.setState({
                        isCheckImg: true
                    });
                }
            });
        }
    }

    getNameByUsername = (userName) => {
        const { actionInventory } = this.props;
        Keyboard.dismiss();
        if (!userName) return;
        showBlockUI();
        actionInventory.getFullNameByUsername(userName).then((response) => {
            if (response) {
                const { fullname, enddatework, isactived, workstoreid } = response;
                this.setState({
                    staffName: fullname,
                    enddatework: enddatework,
                    isActived: isactived,
                    workStoreId: workstoreid
                });
                hideBlockUI();
            } else {
                Alert.alert(
                    translate('common.notification'),
                    translate('inventory_share.not_found_staff_info'),
                    [
                        {
                            text: 'Ok',
                            onPress: () => {
                                this.setState({ staffName: "" });
                                hideBlockUI();
                            }
                        }
                    ]
                );
            }
        }).catch((err) => {
            Alert.alert(
                translate('common.notification'),
                err.msgError,
                [
                    {
                        text: 'Ok',
                        onPress: () => hideBlockUI()
                    }
                ]
            );
        });
    };

    saveStaff = () => {
        const { amountPaid, inventoryDebtProcessID, dtmDate } = this.props.route.params;
        const {
            totalInputText,
            listTermStaffArrears,
            isCheckImg,
            isRequiredImage,
            imageUrls
        } = this.state;
        const { actionInventory, navigation } = this.props;
        // if (isRequiredImage) {
        //     if (!isCheckImg) {
        //         Alert.alert(
        //             translate('common.notification'),
        //             `${translate('inventory.attched_file')} "${translate('inventory.list_employees_product')}" ${translate('inventory.before_save')}`,
        //             [
        //                 {
        //                     text: 'Ok'
        //                 }
        //             ]
        //         );
        //         return;
        //     }
        // }
        if (totalInputText != amountPaid) {
            Alert.alert(
                translate('common.notification'),
                translate('inventory_share.not_save_emp_is') +
                `: ${helper.convertNum(amountPaid)} ` +
                translate('inventory_share.diff_from_total_amount_is') +
                ` ${helper.convertNum(totalInputText)}`,
                [
                    {
                        text: 'Ok'
                    }
                ]
            );
            return;
        }
        showBlockUI();
        // let inventoryDebtAttachmentList = imageUrls.filter(item => !helper.IsEmptyObject(item));
        let imageUrlsFilter = imageUrls.filter(item => !helper.IsEmptyObject(item));
        let inventoryDebtAttachmentList = [...imageUrlsFilter, ...this.dataImageRoot];
        let params = {
            inventoryDebtUserList: listTermStaffArrears,
            inventoryDebtProcessID: inventoryDebtProcessID,
            inventoryDebtAttachmentList: inventoryDebtAttachmentList,
            processDebtMonth: dtmDate
        };
        actionInventory.saveStaffArrears(params).then((response) => {
            if (response) {
                hideBlockUI();
                navigation.goBack();
            } else {
                Alert.alert(
                    translate('common.notification'),
                    translate('fetchAPI.error_occurred'),
                    [
                        {
                            text: 'Ok',
                            onPress: () => hideBlockUI()
                        }
                    ]
                );
            }
        }).catch((err) => {
            Alert.alert(
                translate('common.notification'),
                err.msgError,
                [
                    {
                        text: 'Ok',
                        onPress: () => hideBlockUI()
                    }
                ]
            );
        });
    };

    deleteStaff = (item) => {
        const { amountPaid } = this.props.route.params;
        let dataTermDelete = [...this.state.listTermStaffArrears];
        let index;
        if (item.debtUserInfoID > 0) {
            index = dataTermDelete.findIndex(
                (x) => x.debtUserInfoID == item.debtUserInfoID
            );
            dataTermDelete[index].isDeleted = 1;
        } else {
            index = dataTermDelete.findIndex(
                (x) =>
                    x.userName == item.userName &&
                    x.debtUserInfoID == item.debtUserInfoID
            );
            dataTermDelete.splice(index, 1);
        }
        this.setState({
            listTermStaffArrears: dataTermDelete,
            totalInputText: this.totalInput(dataTermDelete),
            moneyArrears: amountPaid - this.totalInput(dataTermDelete)
        });
        if (dataTermDelete.findIndex(item => item.totalMoney >= 1000000 && item.isDeleted == 0) == -1) {
            this.setState({
                isRequiredImage: false
            });
        }
    };

    addStaff = () => {
        const { totalInputText, moneyArrears, staffId, staffName, isActived, enddatework, workStoreId } = this.state;
        const { amountPaid } = this.props.route.params;
        Keyboard.dismiss();
        let dataTermAdd = [...this.state.listTermStaffArrears];
        let index;
        let objStaff = dataTermAdd.find((x) => x.userName == staffId && x.isDeleted == 0);
        let obj = {
            debtUserInfoID: 0,
            userName: staffId,
            fullName: staffName,
            totalMoney: moneyArrears,
            isActived,
            isDeleted: 0,
            isActivedUser: isActived,
            workStoreId
        };

        // Tinh user off hơn 30 ngày
        let toDay = new Date().getTime();
        let tamp = enddatework == null ? 0 : ((toDay - enddatework) / (1000 * 3600 * 24));
        const maxAllowDateConfig = helper.getAppConfigNumber("INV_ALLOWADDUSER_MAXNUMDAY");

        if ((maxAllowDateConfig - tamp) < 0) {
            Alert.alert(
                translate('common.notification'),
                `${staffId} - ${staffName} ${translate("inventory.off_30")} ${maxAllowDateConfig} ${translate("inventory.off_30_2")}`,
                [
                    {
                        text: 'Ok',
                        onPress: () => hideBlockUI()
                    }
                ]
            );
            return;
        }

        if (totalInputText + moneyArrears > amountPaid) {
            Alert.alert(
                translate('common.notification'),
                translate('inventory_share.not_add_emp_is') +
                `: ${helper.convertNum(amountPaid)}. ` +
                translate('inventory_share.less_than_total_amount_is') +
                ` ${helper.convertNum(
                    totalInputText + moneyArrears
                )}`,
                [
                    {
                        text: 'Ok',
                        onPress: () => hideBlockUI()
                    }
                ]
            );
            return;
        }
        if (objStaff) {
            if (objStaff.debtUserInfoID > 0) {
                index = dataTermAdd.findIndex(
                    (x) => x.debtUserInfoID == objStaff.debtUserInfoID
                );
                dataTermAdd[index].isDeleted = 1;
            } else {
                index = dataTermAdd.findIndex(
                    (x) =>
                        x.userName == staffId &&
                        x.debtUserInfoID == objStaff.debtUserInfoID
                );
                dataTermAdd[index].totalMoney = moneyArrears;
                this.setDefaultDataAdd(dataTermAdd);
                return;
            }
        }
        //push vào mảng
        dataTermAdd.push(obj);
        this.setDefaultDataAdd(dataTermAdd);
    };

    setDefaultDataAdd = (dataTerm) => {
        const { amountPaid } = this.props.route.params;
        this.setState({
            listTermStaffArrears: dataTerm,
            totalInputText: this.totalInput(dataTerm),
            moneyArrears: amountPaid - this.totalInput(dataTerm),
            staffId: '',
            staffName: '',
        });
        if (dataTerm.findIndex(item => item.totalMoney >= 1000000) != -1) {
            this.setState({
                isRequiredImage: true
            });
            // setIsRequiredImage(true);
        }
    };

    totalInput = (dataTerm) => {
        let totalTerm = 0;
        dataTerm.forEach((x) => {
            if (x.isDeleted == 0) {
                totalTerm += x.totalMoney;
            }
        });
        return totalTerm;
    };

    takePicture = (photo) => {
        this.setState({ isVisibleCamera: false });
        showBlockUI();
        if (helper.hasProperty(photo, 'uri')) {
            helper.resizeImage(photo).then(({ path, uri, size, name }) => {
                const body = helper.createFormData({ uri: uri, type: 'image/jpg', name: name, path: STAFF_ARREARS });
                getImageCDN(body)
                    .then((response) => {
                        const remoteURI = API_CONST.API_GET_IMAGE_CDN_NEW + response[0];
                        let newImageUrls = [...this.state.imageUrls];
                        let itemImg = {
                            attachmentID: 0,
                            inventoryDebtProcessID: 0,
                            filePath: null,
                            fileName: null,
                            createdUser: null,
                            attachmentType: 0,
                            imageURL: remoteURI
                        };
                        newImageUrls[this.state.imageIndex] = itemImg;
                        this.setState({ imageUrls: newImageUrls });
                        hideBlockUI();
                    }).catch((error) => {
                        hideBlockUI();
                        console.log('uploadPicture', error);
                    });

            }).catch((error) => {
                hideBlockUI();
                console.log("resizeImage", error);
            });
        } else { hideBlockUI(); }
    };

    selectPicture = () => {
        launchImageLibrary(
            {
                mediaType: 'photo',
                noData: true
            },
            (response) => {
                this.setState({
                    isVisibleCamera: false
                });
                showBlockUI();
                if (helper.hasProperty(response, 'uri')) {
                    helper.resizeImage(response)
                        .then(({ path, uri, size, name }) => {
                            const body = helper.createFormData({ uri: uri, type: 'image/jpg', name: name, path: STAFF_ARREARS });
                            getImageCDN(body)
                                .then((response) => {
                                    const remoteURI = API_CONST.API_GET_IMAGE_CDN_NEW + response[0];
                                    let newImageUrls = [...this.state.imageUrls];
                                    let itemImg = {
                                        attachmentID: 0,
                                        inventoryDebtProcessID: 0,
                                        filePath: null,
                                        fileName: null,
                                        createdUser: null,
                                        attachmentType: 0,
                                        imageURL: remoteURI
                                    };
                                    newImageUrls[this.state.imageIndex] = itemImg;
                                    // newImageUrls[this.state.imageIndex] = remoteURI;
                                    // setImageUrls(newImageUrls)
                                    this.setState({ imageUrls: newImageUrls });
                                    hideBlockUI();
                                }).catch((error) => {
                                    hideBlockUI();
                                    console.log('uploadPicture', error);
                                });
                        })
                        .catch((error) => {
                            hideBlockUI();
                            console.log('resizeImage', error);
                        });
                } else { hideBlockUI(); }
            }
        );
    };

    getPrintByBase64 = () => {
        showBlockUI();
        const { navigation, actionInventory } = this.props;
        const { inventoryDebtProcessID } = this.props.route.params;
        const { listTermStaffArrears } = this.state;
        let params = {
            inventoryDebtUserList: listTermStaffArrears.filter((x) => x.isDeleted == 0),
            inventoryDebtProcessID: inventoryDebtProcessID
        };
        actionInventory.getBase64Inventory(params).then((res) => {
            if (helper.IsNonEmptyString(res.htmlContent)) {
                hideBlockUI();
                navigation.navigate('PrintByBase64', {
                    html: res.htmlContent,
                    openCamera: () => {
                        this.setState({ isOpenCamera: true });
                    },
                });
            } else {
                Alert.alert(
                    translate('common.notification'),
                    translate('inventory.not_get_content'),
                    [
                        {
                            text: 'Ok',
                            onPress: () => hideBlockUI()
                        }
                    ]
                );
            }
        }).catch((err) => {
            Alert.alert(
                translate('common.notification'),
                err.msgError,
                [
                    {
                        text: 'Ok',
                        onPress: () => hideBlockUI()
                    }
                ]
            );
        });
    };

    getListImge = () => {
        const { inventoryDebtProcessID } = this.props.route.params;
        showBlockUI();
        const { actionInventory } = this.props;
        let params = {
            inventoryDebtProcessID: inventoryDebtProcessID,
            attachmentType: 1
        };
        actionInventory.getImgeInventoryDebt(params).then((res) => {
            let dataRoot = res.filter(item => !helper.IsEmptyObject(item)).map(item => ({ ...item }));
            this.dataImageRoot = dataRoot;
            this.setState({
                imageUrls: res
            });
            res.forEach((x) => {
                if (!helper.IsEmptyObject(x)) {
                    this.setState({
                        isOpenCamera: true
                    });
                }
            });
            hideBlockUI();
        }).catch(() => {
            Alert.alert(
                translate('common.notification'),
                translate('inventory.not_get_list_img'),
                [
                    {
                        text: 'Ok',
                        onPress: () => hideBlockUI()
                    }
                ]
            );
        });
    };

    deleteImage = (index) => {
        const { imageUrls } = this.state;
        let newImageUrls = [...imageUrls];
        if (newImageUrls[index].attachmentID !== 0) {
            const indexImage = this.dataImageRoot.findIndex(ele => ele.attachmentID == newImageUrls[index].attachmentID);
            if (indexImage !== -1) {
                this.dataImageRoot[indexImage].isDeleted = 1;
            }
        }
        newImageUrls[index] = {};
        this.setState({
            imageUrls: newImageUrls
        });
    };

    render() {
        const {
            staffName,
            staffId,
            moneyArrears,
            listTermStaffArrears,
            imageUrls,
            isVisibleCamera,
            totalInputText,
            isRequiredImage,
            isSave,
            isCheckImg,
            isOpenCamera
        } = this.state;
        const { amountPaid, ispost, isprocessed } = this.props.route.params;
        return (
            <SafeAreaView style={{
                flex: 1,
                backgroundColor: COLORS.bgFAFAFA,
            }}>
                <ScrollView
                    keyboardShouldPersistTaps="always"
                >
                    <AgentCollectMoney
                        amountPaid={helper.convertNum(amountPaid)}
                        onChangeText={(text) => {
                            const staffIdReg = new RegExp(/^[0-9]*$/);
                            if (staffIdReg.test(text)) {
                                this.setState({ staffId: text, staffName: "" });
                            }
                        }}
                        onBlur={() => this.getNameByUsername(staffId)}
                        onSubmitEditing={() => this.getNameByUsername(staffId)}
                        clearText={() => {
                            this.setState({
                                staffId: "",
                                staffName: ""
                            });
                        }}
                        staffId={staffId}
                        staffName={staffName}
                        moneyArrears={moneyArrears}
                        totalInputText={helper.convertNum(totalInputText)}
                        onChangeInputMoney={(text) => {
                            this.setState({
                                moneyArrears: text
                            });
                        }}
                        onPressConfirm={this.addStaff}
                        isDisabled={ispost == 1}
                    />
                    {
                        (listTermStaffArrears.length > 0) &&
                        <View style={{
                            paddingTop: 10
                        }}>
                            <View style={{
                                width: "100%",
                                flexDirection: "row",
                                alignItems: "center",
                                justifyContent: "space-between",
                                paddingHorizontal: 10,
                            }}>
                                <View style={{
                                    width: "20%",
                                    alignItems: 'flex-start'
                                }}>

                                    <MyText
                                        text={translate('inventory.staff_id_short')}
                                        style={{
                                            fontWeight: 'bold'
                                        }}
                                        addSize={1}
                                    />
                                </View>
                                <View style={{
                                    width: "40%",
                                    alignItems: 'flex-start'
                                }}>
                                    <MyText
                                        text={translate('inventory.staff_name')}
                                        style={{
                                            fontWeight: 'bold'
                                        }}
                                        addSize={1}
                                    />
                                </View>
                                <View style={{
                                    width: "35%",
                                    alignItems: 'flex-start'
                                }}>
                                    <MyText
                                        text={translate('inventory.cash')}
                                        style={{
                                            fontWeight: 'bold'
                                        }}
                                        addSize={1}
                                    />
                                </View>
                                <View style={{
                                    width: "10%",
                                    alignItems: 'flex-start'
                                }}>
                                    <MyText
                                        text={""}
                                    />
                                </View>
                            </View>
                            {
                                listTermStaffArrears.filter((x) => x.isDeleted == 0).map((item, index) => {
                                    return (
                                        <ListDataStaffArrears
                                            item={item}
                                            key={index}
                                            deleteStaff={this.deleteStaff}
                                            isDisabled={ispost == 1}
                                        />
                                    );
                                })
                            }
                        </View>
                    }
                    <View
                        style={{ width: constants.width }}
                    >
                        <MyText
                            text={translate('inventory.upload_report_image')}
                            style={{
                                paddingLeft: 15,
                                paddingVertical: 10,
                                fontWeight: 'bold',
                                color: COLORS.txt333333
                            }}
                        >
                            <MyText
                                text={translate('inventory.max_5_files')}
                                style={{
                                    paddingVertical: 10,
                                    fontWeight: 'bold',
                                    color: COLORS.txt288AD6
                                }}
                            />
                        </MyText>
                        {/* <MyText
                            text={translate('inventory.note_print_report')}
                            style={{
                                paddingLeft: 15,
                                paddingBottom: 10,
                                fontWeight: 'bold',
                                color: COLORS.txtF50537
                            }}
                            addSize={-2}
                        /> */}
                        <View style={{
                            width: constants.width,
                            flexDirection: 'row',
                            flexWrap: "wrap"
                        }}>
                            {imageUrls.map((url, index) =>
                                <ImageProcess
                                    key={index}
                                    onCamera={() => {
                                        this.setState({
                                            imageIndex: index,
                                            isVisibleCamera: true
                                        });
                                        // if (!isRequiredImage) {
                                        //     this.setState({
                                        //         imageIndex: index,
                                        //         isVisibleCamera: true
                                        //     })
                                        // } else {
                                        //     if (isOpenCamera) {
                                        //         this.setState({
                                        //             imageIndex: index,
                                        //             isVisibleCamera: true
                                        //         })
                                        //     } else {
                                        //         Alert.alert(
                                        //             translate('common.notification'),
                                        //             translate('inventory.please_print_report'),
                                        //             [
                                        //                 {
                                        //                     text: 'Ok',
                                        //                     onPress: () => { }
                                        //                 }
                                        //             ]
                                        //         );
                                        //     }
                                        // }
                                    }}
                                    urlImageLocal={url.imageURL}
                                    urlImageRemote={url.filePath}
                                    deleteImage={() => { this.deleteImage(index); }}
                                    isDisabled={ispost == 1}
                                    index={index}
                                />
                            )}
                            <View style={{
                                width: constants.width / 3,
                                alignItems: 'center',
                                paddingVertical: 5
                            }}>
                                <TouchableOpacity style={{
                                    height: constants.width / 3 - 10,
                                    width: constants.width / 3 - 10,
                                    justifyContent: "center",
                                    alignItems: "center",
                                    alignSelf: "center",
                                    backgroundColor: COLORS.bgFAFAFA,
                                    borderRadius: 5,
                                    borderWidth: 1,
                                    borderColor: COLORS.bd0099E5,
                                    opacity: ispost == 1 ? 0.5 : 1
                                }}
                                    onPress={() => {
                                        if (imageUrls.length < 5) {
                                            this.setState({ imageUrls: [...this.state.imageUrls, {}] });
                                        } else {
                                            Alert.alert(
                                                translate('common.notification'),
                                                translate('inventory.max_5_image'),
                                                [
                                                    {
                                                        text: 'Ok',
                                                        onPress: () => { }
                                                    }
                                                ]
                                            );
                                        }
                                    }}
                                    activeOpacity={0.6}
                                    disabled={ispost == 1}
                                >
                                    <View style={{ justifyContent: "center", alignItems: "center" }}>
                                        <Icon
                                            iconSet={"Ionicons"}
                                            name={"add-circle-outline"}
                                            color={COLORS.icFFB23F}
                                            size={60}
                                        />
                                    </View>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </ScrollView>
                <OptionButton
                    listTermStaffArrears={listTermStaffArrears}
                    saveStaff={this.saveStaff}
                    isSave={isSave}
                    getPrintByBase64={this.getPrintByBase64}
                    isDisabled={ispost == 1}
                    isVisibleSaveButton={ispost == 0 && isprocessed == 0}
                />
                <CaptureCamera
                    isVisibleCamera={isVisibleCamera}
                    takePicture={this.takePicture}
                    closeCamera={() => {
                        this.setState({
                            isVisibleCamera: false
                        });
                    }}
                    selectPicture={this.selectPicture}
                />
            </SafeAreaView>
        );
    }
}

const mapStateToProps = function (state) {
    return {

    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        actionInventory: bindActionCreators(actionInventoryCreator, dispatch)
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(StaffArrears);
