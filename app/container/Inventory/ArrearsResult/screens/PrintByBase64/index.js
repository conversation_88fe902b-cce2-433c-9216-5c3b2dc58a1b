import React, { useState, useEffect } from "react";
import {
    View,
    Alert,
    SafeAreaView,
    Keyboard,
    StyleSheet
} from "react-native";
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import {
    showB<PERSON><PERSON>,
    hideBlockUI,
    TitleInput,
    Button
} from "@components";
import { constants } from "@constants";
import { helper, convertHtml2Pdf } from "@common";
import PrintReport from "../../../../SaleOrderManager/component/PrintReport/index";
import Report from "../../../../SaleOrderManager/component/PrintReport/Report";
import * as actionSaleOrderCreator from "../../../../SaleOrderPayment/action";
import * as actionManagerSOCreator from "../../../../SaleOrderManager/action"
import { translate } from '@translate';
import { COLORS } from "@styles";
import { useRoute } from '@react-navigation/native';

const PrintByBase64 = ({
    statePrinter,
    printerCommon,
    actionSaleOrder,
    defaultReport,
    userInfo,
    navigation
}) => {
    const route = useRoute();
    const [reportCommon, setReportVAT] = useState({});
    const [urlPdf, setUrlPdf] = useState("");

    const getReportPrinter = () => {
        const { storeID, brandID } = userInfo
        const isAnKhang = brandID == 8;
        if (helper.checkConfigStorePrint(storeID) && !isAnKhang) {
            actionSaleOrder.getReportPrinterSocket("8");
        }
        else {
            actionSaleOrder.getReportPrinter("8");
        }
    }

    const effectChangeReport = () => {
        setReportVAT(defaultReport.common);
    }

    useEffect(getReportPrinter, [])

    useEffect(effectChangeReport, [defaultReport])

    const onCheckContent = () => {
        if (helper.IsEmptyObject(reportCommon)) {
            Alert.alert("", translate('f88.please_select_printer'));
        }
        else {
            getPrintContent();
        }
    }

    const getPrintContent = async () => {
        const html = route.params.html;
        const { storeID, brandID } = userInfo
        const isAnKhang = brandID == 8;
        try {
            showBlockUI();
            // const base64PDF = await getBase64PdfByUrl(urlPdf);
            const base64 = await convertHtml2Pdf(html, true);
            if (helper.checkConfigStorePrint(storeID) && !isAnKhang) {
                printBillBrother(base64);
            }
            else {
                onPrintBill(base64);
            }
        } catch (msgError) {
            Alert.alert("", msgError, [{
                text: "OK",
                style: "default",
                onPress: hideBlockUI
            }]);
        }
    }

    const onPrintBill = (data) => {
        const requestAPI = getPrintRequestAPI(data);
        if (helper.IsNonEmptyArray(requestAPI)) {
            printAllRequest(requestAPI);
        }
        else {
            hideBlockUI();
        }
    }

    const getPrintRequestAPI = (content) => {
        const requestAPI = [];
        const report = reportCommon;
        if (!helper.IsEmptyObject(report)) {
            for (let i = 0; i < 1; i++) {
                const printService = getPrintService(report, content);
                requestAPI.push(printService);
            }
        }
        return requestAPI;
    }

    const getPrintService = (report, content) => {
        let printerConfig = {
            strPrinterName: report.PRINTERNAME,
            strPaperSize: report.PAPERSIZE,
            paperwidth: report.PAPERWIDTH,
            parperheight: report.PARPERHEIGHT,
            intCopyCount: 1,
            bolIsDuplex: false,
            bolShrinkToMargin: false,
            strBase64: content,
        };
        let formBody = [];
        for (const property in printerConfig) {
            const encodedKey = encodeURIComponent(property);
            const encodedValue = encodeURIComponent(printerConfig[property]);
            formBody.push(encodedKey + "=" + encodedValue);
        }
        formBody = formBody.join("&");
        return new Promise((resolve, reject) => {
            actionSaleOrderCreator.printBillVoucher(formBody).then(result => {
                resolve(result);
            }).catch(msgError => {
                reject(msgError);
            });
        });
    }

    const printAllRequest = (allPromise) => {
        const openCamera = route.params.openCamera;
        Promise.all(allPromise).then(result => {
            console.log("PRINT RSULT", result);
            Alert.alert("", translate('saleOrderManager.print_success'), [
                {
                    text: "OK",
                    style: "default",
                    onPress: () => {
                        navigation.goBack();
                        openCamera();
                        hideBlockUI();
                    }
                }
            ]);
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError, [
                {
                    text: "OK",
                    style: "default",
                    onPress: hideBlockUI
                }
            ]);
        })
    }

    const printBillBrother = (base64PDF) => {
        const openCamera = route.params.openCamera;
        const { userName } = userInfo;
        const { PRINTERSHORTNAME } = reportCommon;
        actionSaleOrderCreator.printA4Document({
            "Value": base64PDF,
            "Printer": PRINTERSHORTNAME,
            "Type": "Arrears",
            "User": userName,
            "Status": "Contract"
        }).then(result => {
            Alert.alert("", translate('saleOrderManager.print_success'), [
                {
                    text: "OK",
                    style: "default",
                    onPress: () => {
                        navigation.goBack();
                        openCamera();
                        hideBlockUI();
                    }
                }
            ]);
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError, [
                {
                    text: "OK",
                    style: "default",
                    onPress: hideBlockUI
                }
            ]);
        });
    }

    return (
        <SafeAreaView style={{
            flex: 1,
            backgroundColor: COLORS.bgFFFFFF
        }}>
            <KeyboardAwareScrollView
                style={{
                    flex: 1
                }}
                enableResetScrollToCoords={false}
                keyboardShouldPersistTaps={"always"}
                bounces={false}
                overScrollMode="always"
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                extraScrollHeight={60}
            >
                <SafeAreaView style={{
                    flex: 1,
                    backgroundColor: COLORS.bgFFFFFF,
                    // paddingVertical: 10,
                }}>
                    <PrintReport
                        title={translate('saleOrderManager.select_printer')}
                        statePrinter={statePrinter}
                        onTryAgains={getReportPrinter}
                        dataCommon={printerCommon}
                        renderItemCommon={({ item, index }) => (<Report
                            key={`ReportCommon`}
                            info={item}
                            report={reportCommon}
                            onCheck={() => {
                                setReportVAT(item);
                            }}
                        />)}
                    />
                    <ButtonAction
                        onPress={onCheckContent}
                    />
                </SafeAreaView>
            </KeyboardAwareScrollView>
        </SafeAreaView>
    );
}

const mapStateToProps = (state) => ({
    printerCommon: state.saleOrderPaymentReducer.printerCommon,
    defaultReport: state.saleOrderPaymentReducer.defaultReport,
    statePrinter: state.saleOrderPaymentReducer.statePrinter,
    userInfo: state.userReducer
});

const mapDispatchToProps = (dispatch) => ({
    actionManagerSO: bindActionCreators(actionManagerSOCreator, dispatch),
    actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),
});

export default connect(mapStateToProps, mapDispatchToProps)(PrintByBase64);

const ButtonAction = ({ onPress }) => {
    return (
        <View style={{
            alignItems: "center",
            width: constants.width,
            paddingVertical: 10,
            marginTop: 10
        }}>
            <Button
                text={translate('header.print_report')}
                styleContainer={{
                    backgroundColor: COLORS.btn288AD6,
                    borderColor: COLORS.bd288AD6,
                    borderWidth: 1,
                    borderRadius: 4,
                    paddingVertical: 10,
                    paddingHorizontal: 20,
                }}
                styleText={{
                    color: COLORS.txtFFFFFF,
                }}
                onPress={onPress}
            />
        </View>
    );
}