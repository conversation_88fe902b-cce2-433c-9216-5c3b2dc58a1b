import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import { MyText, Icon } from '@components';
import { constants } from '@constants';
import { COLORS } from '@styles';
import { translate } from "@translate";
import moment from 'moment';

const ItemInventoryTerm = ({ inventoryTerm, onPress }) => {
  const { inventorytermid, inventorytermname, finishstatus, begintime, endtime } = inventoryTerm;
  const startDate = moment(new Date(begintime)).format('HH:mm DD/MM/YYYY');
  const endDate = moment(new Date(endtime)).format('HH:mm DD/MM/YYYY');
  const isStarted = (new Date()).getTime() > begintime;

  return (
    <TouchableOpacity style={{
      width: constants.width - 20,
      borderColor: COLORS.bd147EFB,
      borderWidth: 1,
      borderRadius: 4,
      padding: 8,
      marginTop: 10,
      justifyContent: 'center',
      backgroundColor: COLORS.bgFFFFFF,
      shadowColor: COLORS.sd000000,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.4,
      shadowRadius: 3.84,
      elevation: 2
    }}
      activeOpacity={0.6}
      onPress={() => {
        onPress(inventoryTerm)
      }}
      disabled={(!isStarted) || finishstatus == 1}
    >
      <MyText
        text={`${inventorytermid} - ${inventorytermname}`}
        style={{
          fontWeight: 'bold',
          color: COLORS.txt3E78E3
        }}
        addSize={2}
        numberOfLines={2}
      />
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center'
        }}
      >
        <View style={{ justifyContent: 'space-between' }}>
          <MyText
            text={translate('inventory.time_start')}
            style={{
              paddingTop: 5
            }}
          >
            <MyText
              text={startDate}
              style={{
                color: COLORS.txt333333,
                fontWeight: 'bold'
              }}
            />
          </MyText>
          <MyText
            text={translate('inventory.time_end')}
            style={{
              paddingTop: 5
            }}
          >
            <MyText
              text={endDate}
              style={{
                fontWeight: 'bold',
                color: COLORS.txtE91A25
              }}
            />
          </MyText>
        </View>
        <View
          style={{
            paddingHorizontal: 8,
            paddingVertical: 4,
            borderRadius: 10,
            backgroundColor: finishstatus ? COLORS.bg008000 : COLORS.bgFF0000,
            position: 'absolute',
            bottom: 0,
            right: 0
          }}
        >
          <MyText
            text={isStarted ? (finishstatus
              ? translate('inventory_share.completed')
              : translate('inventory_share.not_completed'))
              : translate('inventory.not_started')
            }
            style={{
              color: COLORS.txtFFFFFF
            }}
            addSize={-2.5}
          />
        </View>
      </View>
    </TouchableOpacity>
  )
}

export default ItemInventoryTerm