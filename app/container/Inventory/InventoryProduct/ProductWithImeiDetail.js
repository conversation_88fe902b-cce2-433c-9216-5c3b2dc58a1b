import React, { Component } from 'react';
import { Text, View, FlatList, Alert, TouchableOpacity } from 'react-native';
import { connect } from 'react-redux';
import SafeAreaView from "react-native-safe-area-view";
import { bindActionCreators } from 'redux';
import {
    MyText,
    BaseLoading,
    showBlockUI,
    hideBlockUI,
    Icon,
    ScanBarcodeML
} from '@components';
import { COLORS } from '@styles';
import { BaseProductInfo, InputSearch, ItemImeiByProduct, ButtonGroup } from './components';
import { constants } from '@constants';
import * as actionInventoryCreator from '../action';
import { helper } from '@common';
import { translate } from '@translate';

class ProductWithImeiDetail extends Component {
    constructor(props) {
        super(props);
        this.state = {
            valueImei: "",
            selectedStatus: {},
            listImeiTmp: [],
            isVisibleCamera: false,
            inventoryIDSave: 0
        },
            this.listImeiByProduct = [];
        this.imeiFormatExp = '';
    }

    componentDidMount() {
        const { productInfo: { imeiformatexp } } = this.props.route.params;
        this.checkSearchImei();
        this.imeiFormatExp = getImeiEditingFormat(imeiformatexp);
    }

    checkSearchImei = () => {
        const { productInfo } = this.props.route.params;
        if (helper.IsValidateObject(productInfo.imei)) {
            this.setState({
                valueImei: productInfo.imei
            }, this.getListImeiByProduct());
        } else {
            this.getListImeiByProduct();
        }
    };

    getListImeiByProduct = () => {
        const { inventoryData } = this.props.route.params;
        const { actionInventory } = this.props;
        actionInventory.getListImeiByProduct(inventoryData).then((res) => {
            this.listImeiByProduct = res;
            const dataSort = this.sortListImeiTmp(res);
            this.setState({
                listImeiTmp: dataSort,
                inventoryIDSave: dataSort[0].inventoryID
            });
        }).catch((err) => {
            console.log("err", err);
            this.setState({ listImeiTmp: [] });
        });
    };

    searchImei = (keyword) => {
        if (!helper.IsNonEmptyString(keyword)) {
            return;
        }
        const { listImeiTmp } = this.state;
        const { inventoryData } = this.props.route.params;
        let dataImeis = listImeiTmp;
        let indexInputing = dataImeis.findIndex(
            (x) =>
                x.imei &&
                x.imei.trim().toUpperCase() == keyword.trim().toUpperCase() &&
                x.inputingQuantity == 1
        );
        if (indexInputing > -1) {
            Alert.alert(
                translate('common.notification'),
                translate('inventory_share.product_transit_not_enter'),
                [
                    {
                        text: 'Ok'
                    }
                ]
            );
            return;
        }
        let index = dataImeis.findIndex((x) => x.imei && x.imei.trim().toUpperCase() == keyword.trim().toUpperCase());
        if (index > -1) {
            dataImeis[index].inventoryStatusID = inventoryData.inventoryStatusID;
            dataImeis[index].isInput = 1;
        } else {
            this.checkImei(keyword.trim());
            // Alert.alert(
            //     translate('common.notification'),
            //     "Không đúng định dạng IMEI hoặc không có IMEI trong danh sách kiểm kê",
            //     [
            //         {
            //             text: 'Ok'
            //         }
            //     ]
            // );
            // return;
        }
        const dataSort = this.sortListImeiTmp(dataImeis);
        this.setState({
            listImeiTmp: dataSort,
            valueImei: ""
        });
    };

    saveProductImei = (isOpenCamera = false) => () => {
        const { listProductStatus } = this.props;
        showBlockUI();
        const { actionInventory, navigation } = this.props;
        const { listImeiTmp, inventoryIDSave } = this.state;
        const { openCamera, inventoryData, reloadData, isShowButtonDone } = this.props.route.params;
        const {
            imei,
            productID,
            inventoryStatusID,
            inventoryID,
            inventoryTermID,
            inventoryOrderID,
            beginTime
        } = inventoryData;
        const params = {
            checkedStatus: 1,
            inventoryID: inventoryIDSave,
            inventoryTermID: inventoryTermID,
            inventoryOrderID: inventoryOrderID,
            productID: productID,
            inventoryImeiList: listImeiTmp,
            // inventoryImeiList: checkedStatus == 1 ? listImeiTmp : listImeiTmp.filter(x => x.inventoryStatusID > 0 || x.isDeleted == 1),
            beginTime: beginTime,
            inventoryStatusID: inventoryStatusID
        };
        actionInventory.saveProductImei(params).then((res) => {
            let dataTmp = [];
            dataTmp = listImeiTmp;
            hideBlockUI();
            if (res.length == 0) {
                navigation.goBack();
                isShowButtonDone();
                reloadData();
                if (isOpenCamera) {
                    openCamera();
                }
            }

            if (res.length > 0) {
                let noti = false;
                let listImeiNoti = [];
                res.forEach(x => {
                    if (x.validateStatus == undefined) return;
                    index = listImeiTmp.findIndex(m => m.imei.trim() == x.imei.trim());
                    if (index > -1) {
                        //đã xuất khi kiểm
                        if (x.validateStatus == 1) {
                            if (dataTmp[index].inventoryStatusID == 0) {
                                if (dataTmp[index].inventoryDetailID > 0) {
                                    noti = true;
                                    dataTmp[index].isDeleted = 1;
                                    listImeiNoti.push(dataTmp[index].imei.trim());
                                    dataTmp[index].inventoryStatusID = 0;
                                    dataTmp[index].isInput = 0;
                                    dataTmp[index].validateStatus = 1;
                                }
                                else {
                                    dataTmp.splice(index, 1);
                                }
                                // listImeisDelete.push(x.imei.trim())
                            }
                            else {
                                noti = true;
                                dataTmp[index].validateStatus = 1;
                                dataTmp[index].inventoryStatusID = 0;
                                dataTmp[index].isDeleted = 1;
                                dataTmp[index].isInput = 0;
                                listImeiNoti.push(dataTmp[index].imei.trim());
                            }
                            return;
                        }
                        //nhập hàng với imei đang đi đường
                        if (x.validateStatus == 3) {
                            dataTmp[index].validateStatus = 3;
                            dataTmp[index].inputingQuantity = 0;
                            return;
                        }
                        //nhập kho khi kiểm
                        if (x.validateStatus == 4) {
                            dataTmp[index].validateStatus = 4;
                            dataTmp[index].sysInventoryStatusID = x.sysInventoryStatusID;
                            dataTmp[index].sysInventoryStatusName = listProductStatus.find(m => m.INVENTORYSTATUSID == x.sysInventoryStatusID).INVENTORYSTATUSNAME;
                            return;
                        }
                    }
                    else {
                        //nhập mới
                        if (x.validateStatus == 2) {
                            let params = {
                                imei: x.imei.trim(),
                                product_status: 0,
                                sysInventoryStatusID: x.sysInventoryStatusID,
                                validateStatus: 2,
                                isInput: 1,
                                isInputSystem: x.isInputSystem
                            };
                            dataTmp.push(this.createObjItem(params));
                            return;
                        }
                    }
                });
                if (noti) {
                    Alert.alert(
                        translate('common.notification'),
                        `${translate('inventory.list_imei_export')} ${listImeiNoti.join(', ')}. \n ${translate('inventory.system_cancel_imei')}`,
                        [
                            {
                                text: 'Ok'
                            }
                        ]
                    );
                }
                this.setState({
                    listImeiTmp: dataTmp
                });
            }

        }).catch(msgError => {
            console.log(msgError);
            Alert.alert(
                translate('common.notification_uppercase'),
                msgError.msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: 'cancel',
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: 'default',
                        onPress: this.saveProductImei()
                    }
                ]
            );
        });

    };

    deleteItemImei = (item) => {
        const { listImeiTmp } = this.state;
        let data = listImeiTmp;
        let indexData = listImeiTmp.findIndex((x) => x.imei == item.imei);
        //trường hợp imei có tồn trong hệ thống
        if (item.sysInventoryStatusID != 0) {
            if (item.inventoryDetailID > 0) {
                //imei đã kiểm xong giờ vào update
                if (item.validateStatus == 1 || item.inventoryStatusID > 0) {
                    //imei đã bán hoặc đã kiểm
                    data[indexData].inventoryStatusID = 0;
                    data[indexData].isDeleted = 1;
                    data[indexData].isInput = 0;
                }
            } else {
                //chưa kiểm xong
                //trường hợp imei đã đc bắn hoặc nhập vào và k phải đã xuất khi kiểm
                if (item.inventoryStatusID > 0 && item.validateStatus != 1) {
                    data[indexData].inventoryStatusID = 0;
                    data[indexData].isInput = 0;
                }
                if (item.validateStatus == 1) {
                    //đã xuất khi kiểm thì xóa luôn
                    data.splice(indexData, 1);
                }
            }
        } else {
            //trường hợp imei k tồn trong hệ thống
            if (item.inventoryDetailID == 0) {
                //chưa kiểm xong
                data.splice(indexData, 1);
            } else {
                //đã kiểm xong và update
                data[indexData].isInput = 0;
                data[indexData].isDeleted = 1;
            }
        }
        this.setState({
            listImeiTmp: data
        });
    };

    createObjItem = (params) => {
        const { listProductStatus } = this.props;
        return {
            "inventoryDetailID": 0,
            "inventoryID": 0,
            "imei": params.imei,
            "inputingQuantity": 0,
            "sysInventoryStatusID": params.sysInventoryStatusID,
            "sysInventoryStatusName": params.sysInventoryStatusID > 0 ? listProductStatus.find(m => m.INVENTORYSTATUSID == params.sysInventoryStatusID).INVENTORYSTATUSNAME : null,
            "sysStockQuantity": 0,
            "createdUser": null,
            "inventoryStatusID": params.product_status,
            "createdDate": null,
            "updatedUser": null,
            "updatedDate": null,
            "isDeleted": 0,
            "deletedUser": null,
            "deletedDate": null,
            "checkQuantity": 0,
            "checkedStatus": 0,
            "isInput": params.isInput,
            "saveStatus": 0,
            "validateStatus": params.validateStatus,
            "isInputSystem": params.isInputSystem
        };
    };

    sortListImeiTmp = (arr) => {
        arr.sort(this.dynamicSortMultiple(['validateStatus', 'inventoryStatusID']));
        console.log(arr);
        return arr;
    };

    dynamicSort = (property) => {
        var sortOrder = 1;
        if (property[0] === "-") {
            sortOrder = -1;
            property = property.substr(1);
        }
        return (a, b) => {
            let result = (a[property] < b[property]) ? -1 : (a[property] > b[property]) ? 1 : 0;
            return result * sortOrder;
        };
    };
    //sort mutiple params
    dynamicSortMultiple = (params) => {
        let props = params;
        return (obj1, obj2) => {
            var i = 0, result = 0, numberOfProperties = props.length;
            while (result === 0 && i < numberOfProperties) {
                result = this.dynamicSort(props[i])(obj1, obj2);
                i++;
            }
            return result;
        };
    };

    checkImei = (imei) => {
        showBlockUI();
        const { actionInventory, userInfo } = this.props;
        const { productInfo, inventoryData, productStatus } = this.props.route.params;
        const { listImeiTmp } = this.state;
        let dataImeis = listImeiTmp;
        let params = {
            productID: productInfo.productid,
            imei: imei
        };
        actionInventory.checkImei(params).then((res) => {
            let isInputSystem = 0;
            hideBlockUI();
            if (!helper.IsEmptyObject(res)) {
                const { isProduct, inventoryStatusID, storeID, productName } = res;
                if (isProduct == 1) {
                    if (inventoryStatusID !== 0 && (inventoryStatusID !== productStatus.INVENTORYSTATUSID)) {
                        Alert.alert(translate('common.notification'),
                            translate('inventory.imei_different_status'), [
                            {
                                text: "Ok",
                                onPress: hideBlockUI,
                            },
                        ]);
                        return;
                    }
                    else if (inventoryStatusID > 0 && (storeID != userInfo.storeID)) {
                        Alert.alert(translate('common.notification'),
                            `IMEI ${imei} ${translate('inventory.in_stock')} ${storeID}, ${translate('inventory.contact_supermarket')} ${storeID} ${translate('inventory.processing_check')}`, [
                            {
                                text: "Ok",
                                onPress: hideBlockUI,
                            },
                        ]);
                        return;
                    }
                    else if (inventoryStatusID == 0) {
                        this.addImeiItem(imei, 1);
                    }
                }
                if (isProduct == 0) {
                    Alert.alert(
                        translate('common.notification'),
                        `${translate("inventory.imei_is_belong_to")} ${productName}. ${translate("inventory.please_check_again")}`, [
                        {
                            text: "Ok",
                            onPress: hideBlockUI,
                        },
                    ]);
                    return;
                } else {
                    isInputSystem = 1;
                }
            } else {
                const { imeiformatexp } = productInfo;
                if (helper.isString(imeiformatexp)) {
                    const regExpIMEIOfProduct = new RegExp(imeiformatexp);
                    if (!regExpIMEIOfProduct.test(imei.trim())) {
                        Alert.alert(
                            translate('common.notification'),
                            translate('inventory_share.imei_not_in_format'),
                            [
                                {
                                    text: 'Ok',
                                    style: 'cancel',
                                    onPress: hideBlockUI
                                }
                            ]
                        );
                        return;
                    } else {
                        Alert.alert(
                            translate('common.notification'),
                            translate('inventory_share.imei_outside_system'),
                            [
                                {
                                    text: translate('common.btn_cancel'),
                                    style: 'cancel',
                                    onPress: hideBlockUI
                                },
                                {
                                    text: translate('common.btn_accept'),
                                    style: 'default',
                                    onPress: () => {
                                        this.addImeiItem(imei, 0);
                                    }
                                }
                            ]
                        );
                        return;
                    }
                } else {
                    Alert.alert(
                        translate('common.notification'),
                        translate('inventory_share.imei_outside_system'),
                        [
                            {
                                text: translate('common.btn_cancel'),
                                style: 'cancel',
                                onPress: hideBlockUI
                            },
                            {
                                text: translate('common.btn_accept'),
                                style: 'default',
                                onPress: () => {
                                    this.addImeiItem(imei, 0);
                                }
                            }
                        ]
                    );
                    return;
                }
            }
            const params = {
                imei: imei.trim(),
                product_status: inventoryData.inventoryStatusID,
                sysInventoryStatusID: 0,
                validateStatus: 9,
                isInput: 1,
                isInputSystem: isInputSystem
            };
            dataImeis.push(this.createObjItem(params));
            const dataSort = this.sortListImeiTmp(dataImeis);
            actionInventory.stop_get_list_imei_by_product(dataSort);
            this.setState({
                listImeiTmp: dataSort,
                valueImei: ""
            });
        }).catch((err) => {
            Alert.alert(translate('common.notification'), err.msgError, [
                {
                    text: "Ok",
                    onPress: hideBlockUI,
                },
            ]);
        });
        const dataSort = this.sortListImeiTmp(dataImeis);
        this.setState({
            listImeiTmp: dataSort,
            valueImei: ""
        });
    };

    addImeiItem = (imei, isInputSystem) => {
        const { listImeiTmp } = this.state;
        const { inventoryData } = this.props.route.params;
        const { actionInventory } = this.props;
        let dataImeis = [...listImeiTmp];
        const params = {
            imei: imei.trim(),
            product_status: inventoryData.inventoryStatusID,
            sysInventoryStatusID: 0,
            validateStatus: 9,
            isInput: 1,
            isInputSystem: isInputSystem
        };
        dataImeis.push(this.createObjItem(params));
        const dataSort = this.sortListImeiTmp(dataImeis);
        actionInventory.stop_get_list_imei_by_product(dataSort);
        this.setState({
            listImeiTmp: dataSort,
            valueImei: ""
        });
    };

    onChangeInventoryStatusID = (statusId, item) => {
        const { listImeiTmp } = this.state;
        let data = listImeiTmp;
        let indexData = listImeiTmp.findIndex((x) => x.imei == item);
        data[indexData].inventoryStatusID = statusId;
        this.setState({
            listImeiTmp: data
        });
    };


    render() {
        const { stateListImeiByProduct, listProductStatus, userInfo } = this.props;
        const { productInfo, productStatus, isDeviated, isViewHistory } = this.props.route.params;
        const {
            valueImei,
            selectedStatus,
            listImeiTmp,
            isVisibleCamera
        } = this.state;
        const totalImei = listImeiTmp.filter(x => ((x.sysInventoryStatusID > 0 && x.validateStatus != 1 && x.isDeleted == 1) || x.isDeleted == 0)).length;
        const totalImeiChek = listImeiTmp.filter(x => x.inventoryStatusID > 0).length;
        const checkExported = listImeiTmp.filter(x => (x.validateStatus == 1 && x.isDeleted !== 1)).length;
        return (
            <SafeAreaView style={{
                flex: 1,
                backgroundColor: COLORS.bgFFFFFF
            }}>
                <View style={{
                    flex: 1,
                    alignItems: "center",
                }}>

                    <BaseProductInfo
                        product={productInfo}
                        productStatus={productStatus}
                    />
                    <View style={{
                        width: constants.width,
                        flexDirection: "row",
                        alignItems: "center",
                        justifyContent: "space-between",
                        paddingHorizontal: 10,
                        backgroundColor: COLORS.bgF5F5F5
                    }}>
                        <View style={{
                            alignItems: "center"
                        }}>
                            <InputSearch
                                placeholder={translate('inventory.enter_imei')}
                                // width={constants.getSize(240)}
                                value={valueImei}
                                onChangeText={(text) => {
                                    if (helper.IsNonEmptyString(this.imeiFormatExp)) {
                                        const regExpIMEI = new RegExp(this.imeiFormatExp);
                                        if (regExpIMEI.test(text)) {
                                            this.setState({ valueImei: text });
                                        }
                                    } else {
                                        // Alert.alert(translate('common.notification'), translate('inventory.not_declare_imei_format'), [
                                        //     {
                                        //         text: "Ok",
                                        //         onPress: () => { },
                                        //     },
                                        // ]);
                                        this.setState({ valueImei: text });
                                    }
                                }}
                                onBlur={() => this.searchImei(valueImei)}
                                clearText={() => {
                                    this.setState({ valueImei: "" });
                                }}
                            />
                        </View>
                        <TouchableOpacity
                            activeOpacity={0.6}
                            onPress={() => {
                                this.setState({ isVisibleCamera: true });
                            }}
                        >
                            <Icon
                                iconSet={"MaterialCommunityIcons"}
                                name={"barcode-scan"}
                                color={COLORS.icF89000}
                                size={40}
                            />
                        </TouchableOpacity>
                    </View>
                    <View style={{
                        width: constants.width,
                        padding: 10,
                        flexDirection: "row",
                        alignItems: "center",
                        justifyContent: "space-between",
                        backgroundColor: COLORS.bgFFFFFF
                    }}>
                        <View style={{
                            flexDirection: "row"
                        }}>
                            <View style={{
                                flexDirection: "row",
                                alignItems: "center",
                                justifyContent: "space-between"
                            }}>
                                <View style={{
                                    width: 15,
                                    height: 15,
                                    backgroundColor: COLORS.icF16667,
                                    marginRight: 7
                                }}></View>
                                <MyText
                                    text={translate('inventory.exported')}
                                />
                            </View>
                            <View style={{
                                flexDirection: "row",
                                alignItems: "center",
                                justifyContent: "space-between",
                                paddingLeft: 10
                            }}>
                                <View style={{
                                    width: 15,
                                    height: 15,
                                    backgroundColor: COLORS.icF0C014,
                                    marginRight: 7
                                }}></View>
                                <MyText
                                    text={translate('inventory.input_while_checking')}
                                />
                            </View>
                        </View>
                        <View>
                            <MyText
                                text={`${translate('inventory.quantity')} ${totalImeiChek} / ${totalImei}`}
                                style={{
                                    color: COLORS.txtFF0000,
                                    fontWeight: "bold"
                                }}
                            />
                        </View>
                    </View>
                    <View style={{
                        width: constants.width,
                        paddingHorizontal: 10,
                        flexDirection: "row",
                        alignItems: "center",
                        justifyContent: "space-between",
                        paddingVertical: 8,
                        backgroundColor: COLORS.bgE0E0E0
                    }}>
                        <View style={{
                            flexDirection: "row",
                            alignItems: "center",
                            justifyContent: "flex-start",
                            width: (constants.width / 5) * 2
                        }}>
                            <MyText
                                text="IMEI"
                                style={{
                                    fontWeight: "bold"
                                }}
                                addSize={1.5}
                            />
                        </View>
                        <View style={{
                            flexDirection: "row",
                            alignItems: "center",
                            justifyContent: "flex-start",
                            width: (constants.width / 5) * 2
                        }}>
                            <MyText
                                text={translate('inventory.inventory_status_2')}
                                style={{
                                    fontWeight: "bold"
                                }}
                                addSize={1.5}
                            />
                        </View>
                        <View style={{
                            flexDirection: "row",
                            alignItems: "center",
                            justifyContent: "center",
                            width: constants.width / 5
                        }}>

                        </View>
                    </View>
                    <View style={{
                        flex: 1
                    }}>
                        <BaseLoading
                            isLoading={stateListImeiByProduct.isFetching}
                            isEmpty={stateListImeiByProduct.isEmpty}
                            textLoadingError={stateListImeiByProduct.description}
                            isError={stateListImeiByProduct.isError}
                            onPressTryAgains={this.getListImeiByProduct}
                            content={
                                <View style={{
                                    flex: 1,
                                }}>
                                    <FlatList
                                        contentContainerStyle={{
                                            // paddingHorizontal: 10
                                        }}
                                        data={listImeiTmp}
                                        keyExtractor={(item, index) => `${index}`}
                                        renderItem={({ item, index }) => (
                                            <ItemImeiByProduct
                                                item={item}
                                                userInfo={userInfo}
                                                productStatus={productStatus}
                                                listInventoryStatus={listProductStatus}
                                                selectedStatus={selectedStatus}
                                                onSelectedStatus={(item) => {
                                                    this.setState({ selectedStatus: item });
                                                }}
                                                onDelete={this.deleteItemImei}
                                                onCheckImei={this.searchImei}
                                                onChangeInventoryStatusID={this.onChangeInventoryStatusID}
                                                index={index}
                                            />
                                        )}
                                        removeClippedSubviews={false}
                                        stickySectionHeadersEnabled={false}
                                        alwaysBounceVertical={false}
                                        bounces={false}
                                        scrollEventThrottle={16}
                                    />
                                </View>
                            }
                        />
                    </View>
                    {
                        isDeviated ?
                            <>
                                {
                                    !isViewHistory &&
                                    <TouchableOpacity
                                        style={{
                                            backgroundColor: COLORS.btn147EFB,
                                            paddingVertical: 10,
                                            borderRadius: 5,
                                            width: constants.width / 2.6,
                                            alignItems: 'center',
                                            marginBottom: 10
                                            // opacity: isDisabled ? 0.6 : 1
                                        }}
                                        activeOpacity={0.6}
                                        onPress={this.saveProductImei()}
                                        disabled={(listImeiTmp.length > 0 && checkExported == 0) ? false : true}
                                    >
                                        <MyText
                                            text={translate('inventory.save')}
                                            style={{
                                                // fontWeight: 'bold',
                                                color: COLORS.txtFFFFFF
                                            }}
                                            addSize={2}
                                        />
                                    </TouchableOpacity>
                                }
                            </>
                            :
                            <ButtonGroup
                                saveAndGoBack={this.saveProductImei()}
                                saveAndScan={this.saveProductImei(true)}
                                isDisabled={(listImeiTmp.length > 0 && checkExported == 0) ? false : true}
                            />
                    }

                </View>
                {isVisibleCamera &&
                    <ScanBarcodeML
                        isVisible={isVisibleCamera}
                        closeCamera={() => {
                            this.setState({ isVisibleCamera: false });
                        }}
                        resultScanBarcode={(barcode) => {
                            this.setState({
                                isVisibleCamera: false
                            }, this.searchImei(barcode));
                        }}
                    />
                }
            </SafeAreaView>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        stateListImeiByProduct: state.inventoryReducer.stateListImeiByProduct,
        listProductStatus: state.inventoryReducer.listProductStatus,
        userInfo: state.userReducer,
    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        actionInventory: bindActionCreators(actionInventoryCreator, dispatch)
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(ProductWithImeiDetail);

const getImeiEditingFormat = (imeiFormat = '') => {
    let result = imeiFormat;
    try {
        result = imeiFormat
            .replace(/{\d+,\d+}/g, (match) => match.replace(/{\d+,/, "{0,"))
            .replace(/{\d+}/g, (match) => match.replace(/{/, "{0,"));
    } catch (error) {
        helper.LoggerInfo({ "RETRIEVE_IMEI_FORMAT_ERROR": imeiFormat, error });
    }
    return result;
};
