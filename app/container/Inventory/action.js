import { API_CONST, constants } from '@constants';
import { helper } from '@common';
import { apiBase, METHOD } from '@config';
import { translate } from '@translate';

const ERROR = true;
const EMPTY = true;

const {
    API_GET_ALL_LOCATION,
    API_CHECK_LOCATION,
    API_ADD_LOCATION,
    API_REMOVE_LOCATION,
    API_APPLY_LOCATION,
    API_GET_LIST_ORDER_TYPE,
    API_LOAD_INVENTORY,
    API_GET_LIST_ARREARS,
    API_GET_INVENTORY_TERM_LOAD,
    API_GET_LIST_PRODUCT_ARREARS,
    API_GET_LIST_DETAILS_ARREARS,
    API_GET_LIST_STAFF_ARREARS,
    API_USER_CHECK,
    API_SAVE_STAFF_ARREARS,
    API_GET_TERM_AREA,
    API_GET_STATUS_INVENTORY,
    API_GET_GROUP_PRODUCT_IV,
    API_GET_ALL_INFO_BY_KEYWORD,
    API_LOCK_PRODUCT_TO_USER,
    API_GET_BASE64_INVENTORY,
    API_GET_IMEIS_BY_PRODUCTID,
    API_CHECK_IMEI,
    API_GET_REPORT_PRODUCT_WITHOUT_IMEI,
    API_GET_REPORT_PRODUCT_WITH_IMEI,
    API_SAVE_IMEIS,
    API_PRODUCT_PROCESS_IMEI,
    API_PRODUCT_PROCESS,
    API_GET_IMAGE_INVENTORY_DEBT,
    API_DELETE_INVENTORY_BY_ID,
    API_GET_IMAGE_INVENTORY,
    API_GET_LIST_REPORT_STATE,
    API_CHECK_PRODUCT_STOCK,
    API_CHECK_PRODUCT_STOCK_IMEI,
    API_GET_LIST_REPORT_PRODUCT_STATUS,
    API_GET_LIST_REPORT_PRODUCT,
    API_SAVE_REPORT_PRODUCT_STATE_NO_IMEI,
    API_SAVE_REPORT_PRODUCT_STATE_HAS_IMEI,
    API_GET_LIST_REPORT_PRODUCT_STATE_REQUEST,
    API_DELETE_REPORT_PRODUCT_STATE_REQUEST,
    API_SEARCH_PRODUCT_BY_INVENTORY_PROCESS_CODE,
    API_GET_LIST_ERROR_REASON,
    API_GET_BATCH_BY_EXPIRATION_DATE,
    API_GET_REPORT_REQUEST_DETAIL,
    API_GET_PRODUCT_IMAGE,
    API_FINISH_REPORT_PRODUCT_STATE_REQUEST,
    API_GET_LIST_PROCESSS_RP
} = API_CONST;

const START_GET_LIST_PRODUCT_STATUS = "START_GET_LIST_PRODUCT_STATUS";
const STOP_GET_LIST_PRODUCT_STATUS = "STOP_GET_LIST_PRODUCT_STATUS";
const START_GET_LIST_AREA = "START_GET_LIST_AREA";
const STOP_GET_LIST_AREA = "STOP_GET_LIST_AREA";
const SET_LIST_INVENTORY_TYPE = "SET_LIST_INVENTORY_TYPE";
const START_GET_LIST_INVENTORY_TERM = "START_GET_LIST_INVENTORY_TERM";
const STOP_GET_LIST_INVENTORY_TERM = "STOP_GET_LIST_INVENTORY_TERM";
const START_GET_LIST_INVENTORY_TERM_HISTORY = "START_GET_LIST_INVENTORY_TERM_HISTORY";
const STOP_GET_LIST_INVENTORY_TERM_HISTORY = "STOP_GET_LIST_INVENTORY_TERM_HISTORY";
const START_GET_LIST_TERM_AREA = "START_GET_LIST_TERM_AREA";
const STOP_GET_LIST_TERM_AREA = "STOP_GET_LIST_TERM_AREA";
const START_GET_LIST_ARREARS = "START_GET_LIST_ARREARS";
const STOP_GET_LIST_ARREARS = "STOP_GET_LIST_ARREARS";
const START_GET_LIST_PRODUCT_ARREARS = "START_GET_LIST_PRODUCT_ARREARS";
const STOP_GET_LIST_PRODUCT_ARREARS = "STOP_GET_LIST_PRODUCT_ARREARS";
const START_GET_LIST_PRODUCT_ARREARS_PROCESS = "START_GET_LIST_PRODUCT_ARREARS_PROCESS";
const STOP_GET_LIST_PRODUCT_ARREARS_PROCESS = "STOP_GET_LIST_PRODUCT_ARREARS_PROCESS";
const START_GET_LIST_STAFF_ARREARS = "START_GET_LIST_STAFF_ARREARS";
const STOP_GET_LIST_STAFF_ARREARS = "STOP_GET_LIST_STAFF_ARREARS";
const START_GET_LIST_GROUP_PRODUCT = "START_GET_LIST_GROUP_PRODUCT";
const STOP_GET_LIST_GROUP_PRODUCT = "STOP_GET_LIST_GROUP_PRODUCT";
const SET_INVENTORY_PRODUCT_INFO = "SET_INVENTORY_PRODUCT_INFO";
const START_GET_LIST_DEVIATED_GROUP_PRODUCT = "START_GET_LIST_DEVIATED_GROUP_PRODUCT";
const STOP_GET_LIST_DEVIATED_GROUP_PRODUCT = "STOP_GET_LIST_DEVIATED_GROUP_PRODUCT";
const SET_LIST_INVENTORY_PRODUCTS = "SET_LIST_INVENTORY_PRODUCTS";
const START_GET_LIST_IMEI_BY_PRODUCT = "START_GET_LIST_IMEI_BY_PRODUCT";
const STOP_GET_LIST_IMEI_BY_PRODUCT = "STOP_GET_LIST_IMEI_BY_PRODUCT";
const SET_IS_HAS_NO_IMEI_PRODUCT_CHECKED = "SET_IS_HAS_NO_IMEI_PRODUCT_CHECKED";
const START_GET_PRODUCT_PROCESS_IMEI = "START_GET_PRODUCT_PROCESS_IMEI";
const STOP_GET_PRODUCT_PROCESS_IMEI = "STOP_GET_PRODUCT_PROCESS_IMEI";
const START_GET_LIST_REPORT_STATE = "START_GET_LIST_REPORT_STATE";
const STOP_GET_LIST_REPORT_STATE = "STOP_GET_LIST_REPORT_STATE";
const START_GET_LIST_REPORT_PRODUCT_STATE_REQUEST = "START_GET_LIST_REPORT_PRODUCT_STATE_REQUEST";
const STOP_GET_LIST_REPORT_PRODUCT_STATE_REQUEST = "STOP_GET_LIST_REPORT_PRODUCT_STATE_REQUEST";
const SET_REPORT_PRODUCT_STATE_REQUEST_ID = "SET_REPORT_PRODUCT_STATE_REQUEST_ID";
const SET_OUT_SYSTEM_REPORT_PRODUCT_STATE_REQUEST_ID = 'SET_OUT_SYSTEM_REPORT_PRODUCT_STATE_REQUEST_ID';


export const inventoryAction = {
    START_GET_LIST_AREA,
    STOP_GET_LIST_AREA,
    SET_LIST_INVENTORY_TYPE,
    START_GET_LIST_INVENTORY_TERM,
    STOP_GET_LIST_INVENTORY_TERM,
    START_GET_LIST_INVENTORY_TERM_HISTORY,
    STOP_GET_LIST_INVENTORY_TERM_HISTORY,
    START_GET_LIST_ARREARS,
    STOP_GET_LIST_ARREARS,
    START_GET_LIST_PRODUCT_ARREARS,
    STOP_GET_LIST_PRODUCT_ARREARS,
    START_GET_LIST_PRODUCT_ARREARS_PROCESS,
    STOP_GET_LIST_PRODUCT_ARREARS_PROCESS,
    START_GET_LIST_STAFF_ARREARS,
    STOP_GET_LIST_STAFF_ARREARS,
    START_GET_LIST_GROUP_PRODUCT,
    STOP_GET_LIST_GROUP_PRODUCT,
    SET_INVENTORY_PRODUCT_INFO,
    SET_INVENTORY_PRODUCT_INFO,
    SET_LIST_INVENTORY_PRODUCTS,
    START_GET_LIST_IMEI_BY_PRODUCT,
    STOP_GET_LIST_IMEI_BY_PRODUCT,
    START_GET_LIST_TERM_AREA,
    STOP_GET_LIST_TERM_AREA,
    START_GET_LIST_PRODUCT_STATUS,
    STOP_GET_LIST_PRODUCT_STATUS,
    START_GET_LIST_DEVIATED_GROUP_PRODUCT,
    STOP_GET_LIST_DEVIATED_GROUP_PRODUCT,
    SET_IS_HAS_NO_IMEI_PRODUCT_CHECKED,
    START_GET_PRODUCT_PROCESS_IMEI,
    STOP_GET_PRODUCT_PROCESS_IMEI,
    START_GET_LIST_REPORT_STATE,
    STOP_GET_LIST_REPORT_STATE,
    START_GET_LIST_REPORT_PRODUCT_STATE_REQUEST,
    STOP_GET_LIST_REPORT_PRODUCT_STATE_REQUEST,
    SET_REPORT_PRODUCT_STATE_REQUEST_ID,
    SET_OUT_SYSTEM_REPORT_PRODUCT_STATE_REQUEST_ID
};

export const getListDataArea = function () {
    return async (dispatch, getState) => {
        dispatch(start_get_list_area());
        let body = {
            loginStoreID: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            loginID: getState().userReducer.userName,
            moduleID: getState().userReducer.moduleID
        };
        apiBase(API_GET_ALL_LOCATION, METHOD.POST, body).then((response) => {
            console.log("getListDataArea success", response);
            const { object } = response;
            if (helper.IsNonEmptyArray(object)) {
                dispatch(stop_get_list_area(object));
            }
            else {
                dispatch(stop_get_list_area([], EMPTY, translate("inventory.no_inventory_area_found")));
            }
        }).catch(error => {
            console.log("getListDataArea error", error);
            dispatch(stop_get_list_area([], !EMPTY, error.msgError, ERROR));
        });
    };
};

export const checkLocation = () => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreID: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                loginID: getState().userReducer.userName,
                moduleID: getState().userReducer.moduleID
            };
            apiBase(API_CHECK_LOCATION, METHOD.POST, body)
                .then((res) => {
                    console.log("checkLocation success", res);
                    resolve(res);
                })
                .catch((err) => {
                    console.log("checkLocation error", err);
                    reject(err);
                });
        });
    };
};

export const addLocation = (location) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            const { areaName, isActive, hadArea } = location;
            let body = {
                areaName: areaName,
                isActive: 1,
                loginStoreID: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                loginID: getState().userReducer.userName,
                moduleID: getState().userReducer.moduleID,
                hadArea: hadArea,
            };
            apiBase(API_ADD_LOCATION, METHOD.POST, body)
                .then((res) => {
                    console.log("addLocation success", res);
                    resolve(res);
                })
                .catch((err) => {
                    console.log("addLocation error", err);
                    reject(err);
                });
        });
    };
};

export const removeLocation = (item) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            const { inventoryAreaID, hadArea } = item;
            let body = {
                inventoryAreaID: inventoryAreaID,
                loginID: getState().userReducer.userName,
                moduleID: getState().userReducer.moduleID,
                loginStoreID: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                hadArea: hadArea,
            };
            apiBase(API_REMOVE_LOCATION, METHOD.POST, body)
                .then((res) => {
                    // dispatch(getListDataArea())
                    resolve(res);
                })
                .catch((err) => {
                    reject(err);
                });
        });
    };
};

export const applyLocation = (item) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            const { inventoryAreaID, hadArea } = item;
            let body = {
                inventoryAreaID: inventoryAreaID,
                loginID: getState().userReducer.userName,
                moduleID: getState().userReducer.moduleID,
                loginStoreID: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                hadArea: hadArea,
            };
            apiBase(API_APPLY_LOCATION, METHOD.POST, body)
                .then((res) => {
                    // dispatch(getListLocation())
                    resolve(res);
                })
                .catch((err) => {
                    reject(err);
                });
        });
    };
};

export const getListInventoryType = () => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                companyID: getState().userReducer.companyID,
                languageID: getState().userReducer.languageID,
                loginID: getState().userReducer.userName,
                moduleID: getState().userReducer.moduleID,
                loginStoreID: getState().userReducer.storeID
            };
            apiBase(API_GET_LIST_ORDER_TYPE, METHOD.POST, body)
                .then((response) => {

                    console.log("getListInventoryType success", response);
                    const { object } = response;
                    if (helper.IsNonEmptyArray(object)) {
                        const sortedListInventoryType = object.sort((a, b) => a.INVENTORYORDERTYPEID - b.INVENTORYORDERTYPEID);
                        dispatch(set_list_inventory_type(sortedListInventoryType.slice(0, 2)));
                        resolve();
                    } else {
                        reject(translate("inventory.no_inventory_type_found"));
                    }
                })
                .catch((error) => {
                    console.log("getListInventoryType error", error);
                    reject(error.msgError);
                });
        });
    };
};

export const getListInventoryTerm = function (data) {
    return async (dispatch, getState) => {
        const { inventoryTypeID, fromDate, toDate, isViewHistory } = data;
        if (isViewHistory) {
            dispatch(start_get_list_inventory_term_history());
        } else {
            dispatch(start_get_list_inventory_term());
        }
        let body = {
            intINVENTORYORDERTYPEID: inventoryTypeID,
            loginStoreID: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            loginID: getState().userReducer.userName,
            moduleID: getState().userReducer.moduleID,
            fromDate: fromDate,
            toDate: toDate,
            intFinish: -1
        };
        apiBase(API_LOAD_INVENTORY, METHOD.POST, body).then((response) => {
            console.log("getListInventoryTerm success", response);
            const { object } = response;
            const currentTime = (new Date()).getTime();
            const listInventoryTerm = object.filter(term => isViewHistory ?
                (term.finishstatus == 1 || term.endtime < currentTime) :
                (term.finishstatus == 0 && term.endtime > currentTime));
            if (helper.IsNonEmptyArray(listInventoryTerm)) {
                if (isViewHistory) {
                    dispatch(stop_get_list_inventory_term_history(listInventoryTerm));
                } else {
                    dispatch(stop_get_list_inventory_term(listInventoryTerm));
                }
            } else {
                if (isViewHistory) {
                    dispatch(stop_get_list_inventory_term_history([], EMPTY, translate("inventory.no_inventory_history_found")));
                } else {
                    dispatch(stop_get_list_inventory_term([], EMPTY, translate("inventory.no_inventory_term_found")));
                }
            }
        }).catch(error => {
            console.log("getListInventoryTerm error", error);
            if (isViewHistory) {
                dispatch(stop_get_list_inventory_term_history([], !EMPTY, error.msgError, ERROR));
            } else {
                dispatch(stop_get_list_inventory_term([], !EMPTY, error.msgError, ERROR));
            }
        });
    };
};

export const getListTermArea = (data) => {
    return function (dispatch, getState) {
        dispatch(start_get_list_term_area());
        let body = {
            hadArea: data.hadArea,
            inventoryTermID: data.inventoryTermID,
            loginStoreID: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            loginID: getState().userReducer.userName,
            moduleID: getState().userReducer.moduleID
        };
        apiBase(API_GET_TERM_AREA, METHOD.POST, body)
            .then((response) => {
                console.log("getListTermArea success", response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    dispatch(stop_get_list_term_area(object));
                } else {
                    dispatch(stop_get_list_term_area([], EMPTY, translate("inventory.noti_no_inventory_area_found")));
                }
            })
            .catch((error) => {
                console.log("getListTermArea error", error);
                dispatch(stop_get_list_term_area([], !EMPTY, error.msgError, ERROR));
            });
    };
};

export const getListProductStatus = () => {
    return function (dispatch, getState) {
        dispatch(start_get_list_product_status());
        let languageID = getState().userReducer.languageID;
        apiBase(API_GET_STATUS_INVENTORY, METHOD.POST, languageID)
            .then((response) => {
                console.log("getListProductStatus success", response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    const listStatus = object.sort((first, second) => first.orderIndexInventory - second.orderIndexInventory);
                    dispatch(stop_get_list_product_status(listStatus));
                } else {
                    dispatch(stop_get_list_product_status([], EMPTY, translate("inventory.no_inventory_status_found")));
                }
            })
            .catch((error) => {
                console.log("getListProductStatus error", error);
                dispatch(stop_get_list_product_status([], !EMPTY, error.msgError, ERROR));
            });
    };
};

export const getListGroupProduct = (data) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            dispatch(start_get_list_group_product());
            let body = {
                intInventoryTermId: data.inventoryTermId,
                intinventoryareaid: data.inventoryAreaId,
                loginStoreID: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                loginID: getState().userReducer.userName,
                moduleID: getState().userReducer.moduleID,
                inventoryStatusID: data.inventoryStatusID,
                isRequestImei: data.isHadImei,
                checkedstatus: data.checkedStatus,
                beginTime: data.beginTime
            };
            apiBase(API_GET_GROUP_PRODUCT_IV, METHOD.POST, body)
                .then((response) => {
                    console.log("getListGroupProduct success", response);
                    const { object } = response;
                    if (helper.IsNonEmptyArray(object)) {
                        let listGroupProduct = helper.groupArrayTwoLayers(object, "subgroupid", "subgroupname");

                        listGroupProduct = listGroupProduct.map(groupProduct => ({
                            ...groupProduct,
                            firstLayerData: groupProduct.firstLayerData.reduce((result, current) => {
                                const index = result.findIndex(element => element.productid == current.productid);
                                if (index == -1) return [...result, current];
                                else {
                                    let rs = result;
                                    rs[index].checkedQuantity += current.checkedQuantity;
                                    return rs;
                                }
                            }, [])
                        }));
                        dispatch(stop_get_list_group_product(listGroupProduct));
                        resolve();
                    } else {
                        dispatch(stop_get_list_group_product([], EMPTY, translate("inventory.no_valid_data_found")));
                    }
                })
                .catch((error) => {
                    console.log("getListGroupProduct error", error);
                    dispatch(stop_get_list_group_product([], !EMPTY, error.msgError, ERROR));
                });
        });
    };
};

export const searchInventoryProduct = (data) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                keyword: data.keyword.trim(),
                ishasImei: data.isHasImei,
                intproduct: data.intProduct,
                loginStoreID: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                loginID: getState().userReducer.userName,
                moduleID: getState().userReducer.moduleID,
                itemID: data.itemid ? data.itemid : null,
                intinvenntorytermid: data.inventoryTermId,
                intINVENTORYORDERTYPEID: data.inventoryTypeId,
                intareaid: data.inventoryAreaId,
                haspermission: 1,
                beginTime: data.beginTime,
                inventoryStatusID: data.inventoryStatusID,
                inventoryID: data.inventoryID,
                isProduct: data.isProduct
            };
            apiBase(API_GET_ALL_INFO_BY_KEYWORD, METHOD.POST, body)
                .then((response) => {
                    console.log("searchInventoryProduct success", response);
                    const { object } = response;
                    if (!helper.IsEmptyObject(object) && helper.IsNonEmptyArray(object.listProductsAfterCheckExist)) {
                        let productInfo = helper.deepCopy(object);
                        const { listInventory, listProductItem, listDetailItem } = object;
                        if (!data.isHasImei) {
                            if (object.listProductsAfterCheckExist.length > 1) {
                                // case search ra nhiều sản phẩm
                                dispatch(set_inventory_product_info(productInfo));
                                resolve(productInfo);
                            } else {
                                // case search đích danh
                                const scanProductId = helper.IsNonEmptyString(object.listProductsAfterCheckExist[0].scanProductID) ? object.listProductsAfterCheckExist[0].scanProductID : "";
                                if (helper.IsNonEmptyArray(listProductItem)) {
                                    if (helper.IsNonEmptyArray(listDetailItem)) {
                                        const newListProductItem = listProductItem.map(element => {
                                            const detailProduct = listDetailItem.find(item => item.productID.trim() == element.productid.trim());
                                            return helper.IsEmptyObject(detailProduct) ? element :
                                                {
                                                    ...element,
                                                    checkedQuantity: detailProduct.countQuantity,
                                                    checkingQuantity: data.isViewDetail ? 0 : (element.productid.trim() == scanProductId.trim() ? 1 : 0),
                                                    inventoryDetailItemID: detailProduct.inventoryDetailItemID,
                                                    inventoryID: detailProduct.inventoryID
                                                };
                                        }).sort((first, second) => first.exchangeQuantity - second.exchangeQuantity);
                                        productInfo.listProductItem = newListProductItem;
                                    } else {
                                        productInfo.listProductItem = listProductItem.map((element) => {
                                            const detailProduct = listInventory.find(item => item.productID.trim() == element.productid.trim());
                                            return {
                                                ...element,
                                                checkingQuantity: data.isViewDetail ? 0 : (element.productid.trim() == scanProductId.trim() ? 1 : 0),
                                                checkedQuantity: helper.IsEmptyObject(detailProduct) ? 0 : detailProduct.checkQuantity,
                                                inventoryID: helper.IsEmptyObject(detailProduct) ? 0 : detailProduct.inventoryID,
                                                checkedDate: helper.IsEmptyObject(detailProduct) ? 0 : detailProduct.checkedDate
                                            };
                                        }).sort((first, second) => first.exchangeQuantity - second.exchangeQuantity);
                                    }
                                } else {
                                    const detailProduct = listInventory.find(item => item.productID.trim() == object.listProductsAfterCheckExist[0].productid.trim());
                                    productInfo.listProductItem = [{
                                        ...object.listProductsAfterCheckExist[0],
                                        checkingQuantity: data.isViewDetail ? 0 : 1,
                                        checkedQuantity: helper.IsEmptyObject(detailProduct) ? 0 : detailProduct.checkQuantity,
                                        inventoryID: helper.IsEmptyObject(detailProduct) ? 0 : detailProduct.inventoryID,
                                        checkedDate: helper.IsEmptyObject(detailProduct) ? 0 : detailProduct.checkedDate
                                    }];
                                }
                                dispatch(set_inventory_product_info(productInfo));
                                resolve(productInfo);
                            }
                        } else {
                            //case product with imei
                            dispatch(set_inventory_product_info(object));
                            resolve(object);
                        }
                    } else {
                        const { errorType, toastMessage, errorReason } = response;
                        if (errorType == 4) {
                            reject(errorReason);
                        } else if (errorType == 3) {
                            reject(toastMessage);
                        } else {
                            reject(translate("inventory.product_info_not_found"));
                        }
                    }
                })
                .catch((error) => {
                    console.log("searchInventoryProduct error", error);
                    reject(error.msgError);
                });
        });
    };
};

export const lockInventoryProduct = (data) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                inventoryOrderID: data.inventoryOrderID,
                inventoryAreaID: data.inventoryAreaID,
                productID: data.productID,
                inventoryTermID: data.inventoryTermID,
                loginStoreID: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                loginID: getState().userReducer.userName,
                moduleID: getState().userReducer.moduleID,
                hadArea: data.hadArea,
                inventoryOrderTypeID: data.inventoryOrderTypeID,
                subGroupID: data.subgroupid,
                itemID: data.itemID ? data.itemID : null,
            };
            apiBase(API_LOCK_PRODUCT_TO_USER, METHOD.POST, body)
                .then((response) => {
                    console.log("lockInventoryProduct success", response);
                    const { object } = response;
                    if (object) {
                        resolve();
                    } else {
                        reject(translate("inventory.lock_product_unsuccessful"));
                    }
                })
                .catch((error) => {
                    console.log("lockInventoryProduct error", error);
                    reject(error.msgError);
                });
        });
    };
};

export const saveProductNoIMEI = (data) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                productid: data.productId,
                inventoryareaid: data.inventoryAreaId,
                inventoryorderid: data.inventoryOrderId,
                inventorytermid: data.inventoryTermId,
                checkedStatus: 1,
                loginID: getState().userReducer.userName,
                moduleID: getState().userReducer.moduleID,
                loginStoreID: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                inventoryRequestList: data.inventoryRequestList,
                inventoryStatusID: data.inventoryStatusID,
                beginTime: data.beginTime,
                inventoryType: data.inventoryType,
                isHasItem: data.isHasItem,
                inventoryID: data.inventoryID
            };
            apiBase(API_CONST.API_SAVE_PRODUCT_WITHOUT_IMEI_V2, METHOD.POST, body)
                .then((response) => {
                    console.log("saveProductNoIMEI success", response);
                    resolve();
                })
                .catch((error) => {
                    console.log("saveProductNoIMEI error", error);
                    reject(error.msgError);
                });
        });
    };
};

export const getListArrears = (dtmDate, debtProcessFrom) => {
    return function (dispatch, getState) {
        dispatch(start_get_list_arrears());
        let body = {
            loginStoreID: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            loginID: getState().userReducer.userName,
            moduleID: getState().userReducer.moduleID,
            dtmDate: dtmDate,
            debtProcessFrom: debtProcessFrom
        };
        apiBase(API_GET_LIST_ARREARS, METHOD.POST, body)
            .then((res) => {
                console.log("getListArrears success", res);
                const { object } = res;
                if (helper.IsNonEmptyArray(object)) {
                    let data = object.sort(dynamicSort('inventorydebtprocessid'));
                    dispatch(stop_get_list_arrears(data));
                } else {
                    dispatch(stop_get_list_arrears([], EMPTY, translate("inventory_share.not_found_info_dedution_list")));
                }
            })
            .catch((err) => {
                console.log("getListArrears error", err);
                dispatch(stop_get_list_arrears([], !EMPTY, err.msgError, ERROR));
            });

    };
};

export const getListInventoryPicker = (params) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreID: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                loginID: getState().userReducer.userName,
                moduleID: getState().userReducer.moduleID,
                inventoryTypeExclude: -1,
                intINVENTORYORDERTYPEID: -1,
                intFinish: -1,
                fromDate: params.fromDate,
                toDate: params.toDate,
                debtProcessFrom: params.debtProcessFrom
            };
            apiBase(API_GET_INVENTORY_TERM_LOAD, METHOD.POST, body)
                .then((res) => {
                    console.log("getListInventoryPicker success", res);
                    let dataObject = res.object.filter(x => x.isdebtprocess == 0 && x.processstatus == 1);
                    resolve(dataObject);
                })
                .catch((err) => {
                    console.log("getListInventoryPicker error", err);
                    reject(err);
                });
        });
    };
};

export const getListProductArrears = (dtmDate, termids, realProductStatusIds) => {
    return async (dispatch, getState) => {
        dispatch(start_get_list_product_arrears());
        let body = {
            loginStoreID: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            loginID: getState().userReducer.userName,
            moduleID: getState().userReducer.moduleID,
            termids: termids,
            dtmDate: dtmDate,
            realProductStatusIds: realProductStatusIds
        };
        apiBase(API_GET_LIST_PRODUCT_ARREARS, METHOD.POST, body).then((response) => {
            console.log("getListProductArrears success", response);
            const { object } = response;
            if (helper.IsNonEmptyArray(object)) {
                const totalGroup = object.reduce(
                    (a, b) => a + (b.totalamountchange + b.totalamountinput + b.totalamountoutput + b.totalamountchangestt), 0);
                const dataGroup = helper.groupArrayThreeLayers(object, "maingroupid", "maingroupname", "productid", "productname");
                console.log("getListProductArrears dataGroup", dataGroup);
                dispatch(stop_get_list_product_arrears({ dataGroup: [...dataGroup], totalGroup: totalGroup }));
            }
            else {
                dispatch(stop_get_list_product_arrears([], EMPTY, translate("inventory.no_arrears_product_found")));
            }
        }).catch((error) => {
            console.log("getListDataArea error", error);
            dispatch(stop_get_list_product_arrears([], !EMPTY, error.msgError, ERROR));
        });
    };
};

export const getListProductArrearsProcess = (inventoryDebtProcessID) => {
    return async (dispatch, getState) => {
        dispatch(start_get_list_product_arrears_process());
        let body = {
            inventoryDebtProcessID: inventoryDebtProcessID,
            loginStoreID: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            loginID: getState().userReducer.userName,
            moduleID: getState().userReducer.moduleID
        };
        apiBase(API_GET_LIST_DETAILS_ARREARS, METHOD.POST, body).then((response) => {
            console.log("getListProductArrearsProcess success", response);
            const { object } = response;
            if (helper.IsNonEmptyArray(object)) {
                const totalGroup = object.reduce(
                    (a, b) => a + (b.totalamountchange + b.totalamountinput + b.totalamountoutput + b.totalamountchangestt), 0);
                const dataGroup = helper.groupArrayThreeLayers(object, "maingroupid", "maingroupname", "productid", "productname");
                console.log("getListProductArrearsProcess dataGroup", dataGroup, totalGroup);
                dispatch(stop_get_list_product_arrears_process({ dataGroup: [...dataGroup], totalGroup: totalGroup }));
            }
            else {
                dispatch(stop_get_list_product_arrears_process([], EMPTY, translate("inventory.no_processing_arrears_product_found")));
            }
        }).catch((error) => {
            console.log("getListProductArrearsProcess error", error);
            dispatch(stop_get_list_product_arrears_process([], !EMPTY, error.msgError, ERROR));
        });
    };
};

export const getListStaffArrears = (inventoryDebtProcessID) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            dispatch(start_get_list_staff_arrears());
            let body = {
                loginStoreID: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                loginID: getState().userReducer.userName,
                moduleID: getState().userReducer.moduleID,
                inventoryDebtProcessID: inventoryDebtProcessID
            };
            apiBase(API_GET_LIST_STAFF_ARREARS, METHOD.POST, body).then((response) => {
                console.log('getListStaffArrears success', response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    dispatch(stop_get_list_staff_arrears(object));
                    resolve(object);
                }
                else {
                    resolve([]);
                    dispatch(stop_get_list_staff_arrears([], EMPTY, translate("inventory.no_arrears_staff_info_found")));
                }
            }).catch((err) => {
                console.log('getListStaffArrears error', err);
                reject(err);
                dispatch(stop_get_list_staff_arrears([], !EMPTY, err.msgError, ERROR));
            });
        });
    };
};

export const getFullNameByUsername = (userName) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                userName: userName.trim(),
                loginStoreID: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                loginID: getState().userReducer.userName,
                moduleID: getState().userReducer.moduleID
            };
            apiBase(API_USER_CHECK, METHOD.POST, body).then((res) => {
                console.log("getFullNameByUsername success", res);
                resolve(res.object);
            })
                .catch((err) => {
                    console.log("getFullNameByUsername error", err);
                    reject(err);
                });
        });
    };
};

export const saveStaffArrears = (params) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreID: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                loginID: getState().userReducer.userName,
                moduleID: getState().userReducer.moduleID,
                loginStoreName: getState().userReducer.storeName,
                loginUserFullName: getState().userReducer.fullName,
                companyId: getState().userReducer.companyID,
                processDebtMonth: params.processDebtMonth,
                inventoryDebtUserList: params.inventoryDebtUserList,
                inventoryDebtAttachmentList: params.inventoryDebtAttachmentList,
                inventoryDebtProcessID: params.inventoryDebtProcessID
            };
            apiBase(API_SAVE_STAFF_ARREARS, METHOD.POST, body).then((res) => {
                console.log("saveStaffArrears success", res);
                resolve(res.object);
            }).catch((err) => {
                console.log("saveStaffArrears error", err);
                reject(err);
            });
        });
    };
};

export const getBase64Inventory = (params) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreID: getState().userReducer.storeID,
                loginStoreName: getState().userReducer.storeName,
                languageID: getState().userReducer.languageID,
                loginID: getState().userReducer.userName,
                moduleID: getState().userReducer.moduleID,
                inventoryDebtUserList: params.inventoryDebtUserList,
                inventoryDebtProcessID: params.inventoryDebtProcessID
            };
            apiBase(API_GET_BASE64_INVENTORY, METHOD.POST, body).then((res) => {
                console.log("getBase64Inventory success", res);
                resolve(res.object);
            }).catch((err) => {
                console.log("getBase64Inventory error", err);
                reject(err);
            });
        });
    };
};

export const getListDeviatedGroupProduct = (data) => {
    return function (dispatch, getState) {
        dispatch(start_get_list_deviated_group_product());
        let body = {
            loginStoreID: getState().userReducer.storeID,
            loginID: getState().userReducer.userName,
            moduleID: getState().userReducer.moduleID,
            languageID: getState().userReducer.languageID,
            inventoryTermID: data.inventoryTermId,
            subGroupID: data.subGroupID,
            isDifferent: data.isDifferent,
            pageIndex: data.pageIndex,
            pageSize: data.pageSize,
            isFirst: data.isFirst,
            productIDs: data.productIDs,
            inventoryStatusID: data.inventoryStatusID,
            keyword: data.keyword
        };
        apiBase(data.isHasImei ? API_GET_REPORT_PRODUCT_WITH_IMEI : API_GET_REPORT_PRODUCT_WITHOUT_IMEI, METHOD.POST, body)
            .then((response) => {
                console.log("getListDeviatedGroupProduct success", response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    const listGroupProduct = helper.groupArrayThreeLayersMultiField(
                        object,
                        "subGroupID",
                        "subGroupName",
                        "productID",
                        "productName",
                        data.isHasImei ? "invInventoryStatusID" : "inventoryStatusID",
                        data.isHasImei ? "invInventoryStatusName" : "inventoryStatusName",
                        "inventoryID"
                    );
                    dispatch(stop_get_list_deviated_group_product(listGroupProduct));
                } else {
                    dispatch(stop_get_list_deviated_group_product([], EMPTY, translate("inventory.no_valid_data_found")));
                }
            })
            .catch((error) => {
                console.log("getListDeviatedGroupProduct error", error);
                dispatch(stop_get_list_deviated_group_product([], !EMPTY, error.msgError, ERROR));
            });
    };
};
export const getListImeiByProduct = (params) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            dispatch(start_get_list_imei_by_product());
            let body = {
                inventoryTermID: params.inventoryTermID,
                productID: params.productID,
                loginStoreID: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                loginID: getState().userReducer.userName,
                moduleID: getState().userReducer.moduleID,
                checkedStatus: params.checkedStatus,
                inventoryImeiList: params.inventoryImeiList,
                inventoryStatusID: params.inventoryStatusID,
                beginTime: params.beginTime
            };
            apiBase(API_GET_IMEIS_BY_PRODUCTID, METHOD.POST, body).then((response) => {
                console.log('getListImeiByProduct success', response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    const data = checkImeiInventoryStatusID(object, params.imei, params.inventoryStatusID);
                    dispatch(stop_get_list_imei_by_product(data));
                    resolve(data);
                }
                else {
                    resolve([]);
                    dispatch(stop_get_list_imei_by_product([], EMPTY, translate("inventory.no_imei_found")));
                }
            }).catch((err) => {
                console.log('getListImeiByProduct error', err);
                reject(err);
                dispatch(stop_get_list_imei_by_product([], !EMPTY, err.msgError, ERROR));
            });
        });
    };
};

export const checkImei = (params) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                productID: params.productID.trim(),
                imei: params.imei.trim(),
                loginStoreID: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                loginID: getState().userReducer.userName,
                moduleID: getState().userReducer.moduleID
            };
            apiBase(API_CHECK_IMEI, METHOD.POST, body).then((res) => {
                console.log("checkImei success", res);
                resolve(res.object);
            }).catch((err) => {
                console.log("checkImei error", err);
                reject(err);
            });
        });
    };
};

export const saveProductImei = (params) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                checkedStatus: params.checkedStatus,
                inventoryID: params.inventoryID,
                inventoryTermID: params.inventoryTermID,
                inventoryOrderID: params.inventoryOrderID,
                productID: params.productID.trim(),
                loginStoreID: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                loginID: getState().userReducer.userName,
                moduleID: getState().userReducer.moduleID,
                inventoryImeiList: params.inventoryImeiList,
                beginTime: params.beginTime,
                inventoryStatusID: params.inventoryStatusID
            };
            apiBase(API_SAVE_IMEIS, METHOD.POST, body).then((res) => {
                const { object } = res;
                console.log("saveProductImei success", res);
                if (helper.IsNonEmptyArray(object)) {
                    resolve(object);
                } else {
                    resolve([]);
                }
            }).catch((err) => {
                console.log("saveProductImei error", err);
                reject(err);
            });
        });
    };
};

export const setIsHasNoImeiProductChecked = (isChecked) => {
    return function (dispatch, getState) {
        dispatch(set_is_has_no_imei_product_checked(isChecked));
    };
};

export const getProductProcessImei = function (params, isHasImei) {
    return async (dispatch, getState) => {
        dispatch(start_get_product_process_imei());
        let body = {
            inventoryTermID: params.inventoryTermID,
            inventoryAreaID: params.inventoryAreaID,
            subGroupID: params.subGroupID,
            pageIndex: params.pageIndex,
            pageSize: params.pageSize,
            isFirst: params.isFirst,
            productIDs: params.productIDs,
            inventoryStatusID: params.inventoryStatusID,
            loginID: getState().userReducer.userName,
            moduleID: getState().userReducer.moduleID,
            languageID: getState().userReducer.languageID,
            loginStoreID: getState().userReducer.storeID
        };
        apiBase(isHasImei ? API_PRODUCT_PROCESS_IMEI : API_PRODUCT_PROCESS, METHOD.POST, body).then((response) => {
            console.log("getProductProcessImei success", response);
            const { object } = response;
            if (helper.IsNonEmptyArray(object)) {
                const dataGroup = helper.groupArrayTwoLayers(object, "subgroupid", "subgroupname");
                console.log("getProductProcessImei dataGroup", dataGroup);
                dispatch(stop_get_product_process_imei(dataGroup));
            }
            else {
                dispatch(stop_get_product_process_imei([], EMPTY, translate("inventory.no_product_found")));
            }
        }).catch(error => {
            console.log("getProductProcessImei error", error);
            dispatch(stop_get_product_process_imei([], !EMPTY, error.msgError, ERROR));
        });
    };
};

export const getImgeInventoryDebt = (params) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                languageID: getState().userReducer.languageID,
                inventoryDebtProcessID: params.inventoryDebtProcessID,
                attachmentType: params.attachmentType
            };
            apiBase(API_GET_IMAGE_INVENTORY_DEBT, METHOD.POST, body).then((res) => {
                const { object } = res;
                console.log("getImgeInventoryDebt success", res);
                if (helper.IsNonEmptyArray(object)) {
                    let data = object.map(item => ({ ...item, filePath: API_GET_IMAGE_INVENTORY + item.filePath }));
                    resolve(data);
                } else {
                    resolve([{}, {}, {}]);
                }
            }).catch((err) => {
                console.log("getImgeInventoryDebt error", err);
                reject(err);
            });
        });
    };
};
export const deleteInventoryData = (inventoryTermId, productId, inventoryStatusID) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                inventoryTermID: inventoryTermId,
                loginStoreID: getState().userReducer.storeID,
                loginID: getState().userReducer.userName,
                moduleID: getState().userReducer.moduleID,
                languageID: getState().userReducer.languageID,
                productID: productId,
                inventoryStatusID: inventoryStatusID
            };
            apiBase(API_DELETE_INVENTORY_BY_ID, METHOD.POST, body).then((res) => {
                console.log("deleteInventoryData success", res);
                const { object } = res;
                if (object) {
                    resolve(true);
                } else {
                    reject(translate("inventory.error_delete_inventory_info"));
                }
            }).catch((err) => {
                console.log("deleteInventoryData error", err);
                reject(err.msgError);
            });
        });
    };
};

// actin search sản phẩm khai báo tình trạng thực tế
export const getListReportProduct = (data) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                "keyword": data.keyword,
                "imeiProductId": data.imeiProductId,
                "loginStoreID": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "isProduct": data.isProduct,
                "realProductStatusTypeId": data.realProductStatusTypeId,
                "companyId": getState().userReducer.companyID,
                "brandId": getState().userReducer.brandID,
            };
            apiBase(API_GET_LIST_REPORT_PRODUCT, METHOD.POST, body).then((response) => {
                console.log("getListReportProduct success", response);
                const { object } = response;
                if (!helper.IsEmptyObject(object)) {
                    resolve(object);
                }
                else {
                    reject(translate('inventory.product_not_found'));
                }
            }).catch((error) => {
                console.log("getListReportProduct error", error);
                reject(error.msgError);
            });
        });
    };
};

// action lấy trạng thái sản phẩm theo subGroupID
export const getListReportProductStatus = (data) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                "languageId": getState().userReducer.languageID,
                "loginStoreId": getState().userReducer.storeID,
                "productId": data.productId,
                "isCheckStock": data.isCheckStock,
                "brandId": getState().userReducer.brandID,
                "subgroupId": data.subGroupId
            };
            apiBase(API_GET_LIST_REPORT_PRODUCT_STATUS, METHOD.POST, body).then((response) => {
                console.log("getListReportProductStatus success", response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    resolve(object);
                }
                else {
                    reject(data.isCheckStock == 1 ? `Sản phẩm ${data.productId.trim()} không đủ tồn kho, vui lòng kiểm tra lại!` :
                        translate('inventory.not_declared_business_status'));
                }
            }).catch((error) => {
                console.log("getListReportProductStatus error", error);
                reject(error.msgError);
            });
        });
    };
};

// api lấy tình trạng sản phầm cần khai báo
export const getListReportState = () => {
    return async (dispatch, getState) => {
        dispatch(start_get_list_report_state());
        let body = {
            "languageID": getState().userReducer.languageID,
            "brandId": getState().userReducer.brandID,
            "loginStoreID": getState().userReducer.storeID
        };
        apiBase(API_GET_LIST_REPORT_STATE, METHOD.POST, body).then((response) => {
            console.log("getListReportState success", response);
            const { object } = response;
            if (helper.IsNonEmptyArray(object)) {
                dispatch(stop_get_list_report_state(object));
            }
            else {
                dispatch(stop_get_list_report_state([], EMPTY, translate('inventory.no_report_state_found')));
            }
        }).catch((error) => {
            console.log("getListReportState error", error);
            dispatch(stop_get_list_report_state([], !EMPTY, error.msgError, ERROR));
        });
    };
};

// api kiểm tra tồn của sản phẩm
export const getProductStock = (data) => {
    return async (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const { productId, inventoryStatusId, inventoryStatusName } = data;
            let body = {
                "strProductId": productId,
                "inventoryStatusID": inventoryStatusId,
                "loginStoreID": getState().userReducer.storeID
            };
            apiBase(API_CHECK_PRODUCT_STOCK, METHOD.POST, body).then((response) => {
                console.log("getProductStock success", response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    const { lockQuantity, storechangequantity, scorderlockquantity, statuslockquantity, pcorderlockquantity, quantity } = object[0];
                    if (quantity - (lockQuantity + storechangequantity + scorderlockquantity + statuslockquantity + pcorderlockquantity) > 0) {
                        resolve(object);
                    } else {
                        reject(`${translate('inventory.product')} ${productId.trim()} ${translate('inventory.status_2')} ${inventoryStatusName} ${translate('inventory.not_in_stock')}`);
                    }
                } else {
                    reject(`${translate('inventory.product')} ${productId.trim()} ${translate('inventory.status_2')} ${inventoryStatusName} ${translate('inventory.not_in_stock')}`);
                }
            }).catch((error) => {
                console.log("getProductStock error", error);
                reject(error.msgError);
            });
        });
    };
};

// api kiểm tra tồn của sản phẩm có IMEI
export const getProductStockIMEI = (imei, productId) => {
    return async (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "imei": imei,
                "loginStoreID": getState().userReducer.storeID
            };
            apiBase(API_CHECK_PRODUCT_STOCK_IMEI, METHOD.POST, body).then((response) => {
                console.log("getProductStockIMEI success", response);
                if (helper.IsNonEmptyArray(response.object)) {
                    const stockInfo = response.object.find(item => item.productid.trim() == productId);
                    resolve(stockInfo);
                } else {
                    resolve({});
                }
                resolve(response.object);
            }).catch((error) => {
                console.log("getProductStockIMEI error", error);
                reject(error.msgError);
            });
        });
    };
};

// action lưu yêu cầu khai báo tình trạng sản phẩm không IMEI
export const saveReportRequestNoIMEI = (data) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            const reportProductStateRequestId = getState().inventoryReducer.reportProductStateRequestId;
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "loginId": getState().userReducer.userName,
                "languageID": getState().userReducer.languageID,
                "masterProductId": data.productID,
                "masterInventoryStatusId": data.inventoryStatusID,
                "masterQuantityUnitId": data.masterQuantityUnitId,
                "masterItemId": data.masterItemId,
                "isExchange": data.isExchange,
                "changeQuantity": data.changeQuantity,
                "realProductStatusBO": {
                    ...data.productStatusBO,
                    "realProductStatusId": reportProductStateRequestId
                },
                "realProductStatusDetailBOs": data.productStatusBODetail,
                "lockedQuantity": data.lockedQuantity,
                "realProductStatusAttachmentBOs": data.realProductStatusAttachmentBOs
            };
            apiBase(API_SAVE_REPORT_PRODUCT_STATE_NO_IMEI, METHOD.POST, body).then((response) => {
                console.log("saveReportRequestNoIMEI success", response);
                const { object } = response;
                if (!helper.IsEmptyObject(object) && helper.IsNonEmptyString(object.realProductStatusId)) {
                    if (helper.IsEmptyString(reportProductStateRequestId)) {
                        dispatch(set_report_product_request_state_id(object.realProductStatusId));
                    }
                    resolve();
                }
                else {
                    reject(translate('inventory.save_report_request_error'));
                }
            }).catch((error) => {
                console.log("saveReportRequestNoIMEI error", error);
                reject(error);
            });
        });
    };
};

// action lưu yêu cầu khai báo tình trạng sản phẩm có IMEI
export const saveReportRequestHasIMEI = (data, isUpdate) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            const reportProductStateRequestId = getState().inventoryReducer.reportProductStateRequestId;
            const outSystemReportProductStateRequestId = getState().inventoryReducer.outSystemReportProductStateRequestId;
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "loginId": getState().userReducer.userName,
                "languageID": getState().userReducer.languageID,
                "masterProductId": data.productID,
                "masterInventoryStatusId": data.inventoryStatusID,
                "masterQuantityUnitId": data.masterQuantityUnitId,
                "masterItemId": data.masterItemId,
                "realProductStatusBO": {
                    ...data.productStatusBO,
                    "realProductStatusId": isUpdate ? data.realProductStatusId : reportProductStateRequestId
                },
                "realProductStatusDetailBOs": data.productStatusBODetail,
                "realProductStatusAttachmentBOs": data.realProductStatusAttachmentBOs
            };
            apiBase(API_SAVE_REPORT_PRODUCT_STATE_HAS_IMEI, METHOD.POST, body).then((response) => {
                console.log("saveReportRequestHasIMEI success", response);
                const { object } = response;
                if (!helper.IsEmptyObject(object)) {
                    const { outSystemRealProductStatusId, realProductStatusId } = object;
                    if (helper.IsEmptyString(reportProductStateRequestId) && helper.IsNonEmptyString(realProductStatusId)) {
                        dispatch(set_report_product_request_state_id(realProductStatusId));
                    } else if (helper.IsNonEmptyString(outSystemRealProductStatusId)) {
                        dispatch(set_out_system_report_product_request_state_id(`${outSystemReportProductStateRequestId},${outSystemRealProductStatusId}`));
                    }
                    resolve();
                }
                else {
                    reject(translate('inventory.save_report_request_error'));
                }
            }).catch((error) => {
                console.log("saveReportRequestHasIMEI error", error);
                reject(error);
            });
        });
    };
};

// action reset phiếu khai báo tình trạng hàng hoá
export const resetReportProductStateRequest = () => {
    return function (dispatch, getState) {
        dispatch(set_report_product_request_state_id(""));
        dispatch(set_out_system_report_product_request_state_id(""));
        dispatch(stop_get_list_report_product_state_request([], []));
    };
};

// action lấy danh sách yêu cầu khai báo tình trạng sản phẩm
export const getListReportProductStateRequest = () => {
    return function (dispatch, getState) {
        dispatch(start_get_list_report_product_state_request());
        const reportProductStateRequestId = getState().inventoryReducer.reportProductStateRequestId;
        const outSystemReportProductStateRequestId = getState().inventoryReducer.outSystemReportProductStateRequestId;
        let body = {
            "languageId": getState().userReducer.languageID,
            "realProductStatusId": `${reportProductStateRequestId}${outSystemReportProductStateRequestId}`
        };
        apiBase(API_GET_LIST_REPORT_PRODUCT_STATE_REQUEST, METHOD.POST, body).then((response) => {
            console.log("getListReportProductStateRequest success", response);
            const { object } = response;
            if (helper.IsNonEmptyArray(object)) {
                const dataGroup = helper.groupArrayTwoLayers(object, "subgroupId", "subgroupName");
                dispatch(stop_get_list_report_product_state_request(dataGroup, object));
            }
            else {
                dispatch(set_report_product_request_state_id(""));
                dispatch(set_out_system_report_product_request_state_id(""));
                dispatch(stop_get_list_report_product_state_request([], [], EMPTY, translate('inventory.no_report_requests_found')));
            }
        }).catch((error) => {
            console.log("getListReportProductStateRequest error", error);
            dispatch(stop_get_list_report_product_state_request([], [], !EMPTY, error.msgError, ERROR));
        });
    };
};

// action xoá yêu cầu khai báo tình trạng hàng hoá
export const deleteReportProductStateRequest = (requestInfo) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                "languageId": getState().userReducer.languageID,
                "loginStoreId": getState().userReducer.storeID,
                "realProductStatusBO": {
                    "realProductStatusTypeId": requestInfo.realProductStatusTypeId,
                    "isCheckStock": requestInfo.isCheckStock
                },
                "realProductStatusDetailBOs": [{
                    "realProductStatusId": requestInfo.realProductStatusId,
                    "realProductStatusDetailId": requestInfo.realProductStatusDetailId,
                    "realProductStatusDetailVs": requestInfo.realProductStatusDetailVs,
                    "productId": requestInfo.productid,
                    "inventoryStatusId": requestInfo.inventoryStatusId,
                    "quantity": requestInfo.quantity,
                    "imei": requestInfo.imei,
                    "totalRpSttDetail": requestInfo.totalRpSttDetail,
                    "realProductStatusVs": requestInfo.realProductStatusVs
                }]
            };
            apiBase(API_DELETE_REPORT_PRODUCT_STATE_REQUEST, METHOD.POST, body).then((response) => {
                console.log("deleteReportProductStateRequest success", response);
                const { object } = response;
                if (object == "Xóa phiếu thành công!") {
                    resolve();
                } else {
                    reject("xoá phiếu thất bại");
                }
            }).catch((error) => {
                console.log("deleteReportProductStateRequest error", error);
                reject(error.msgError);
            });
        });
    };
};

// action lấy thông tin sản phẩm dựa theo mã kiểm kê
export const searchProductByInventoryProcessCode = (keyword, isProduct) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "loginId": getState().userReducer.userName,
                "languageID": getState().userReducer.languageID,
                "inventoryProcessCode": "",
                "limit": 10,
                "keyword": keyword,
                "companyId": getState().userReducer.companyID,
                "brandId": getState().userReducer.brandID,
                "isProduct": isProduct
            };
            apiBase(API_SEARCH_PRODUCT_BY_INVENTORY_PROCESS_CODE, METHOD.POST, body).then((response) => {
                console.log("searchProductByInventoryProcessCode success", response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object.listProduct)) {
                    const listProduct = object.listProduct.filter(item => !helper.IsEmptyObject(item));
                    if (helper.IsNonEmptyArray(listProduct)) {
                        resolve({
                            listProduct: listProduct,
                            baseProduct: object.baseProduct
                        });
                    } else {
                        reject(translate('inventory.not_product_with_code_inventory'));
                    }
                } else {
                    reject(translate('inventory.not_product_with_code_inventory'));
                }
            }).catch((error) => {
                console.log("searchProductByInventoryProcessCode error", error);
                reject(error.msgError);
            });
        });
    };
};

// action lấy danh sách các nguyên nhân phát sinh lỗi
export const getListErrorReason = (data) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreID": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "realProductStatusTypeId": data.realProductStatusTypeId,
                "productId": data.productId,
                "realProductId": data.realProductId,
                "inventoryStatusID": data.inventoryStatusId,
                "isReqInventoryStatus": data.isReqInventoryStatus,
                "companyId": getState().userReducer.companyID,
                "brandId": getState().userReducer.brandID,
            };
            apiBase(API_GET_LIST_ERROR_REASON, METHOD.POST, body).then((response) => {
                console.log("getListErrorReason success", response);
                const { object } = response;
                if (helper.IsEmptyObject(object)) {
                    resolve({});
                } else {
                    resolve(object);
                }
            }).catch((error) => {
                console.log("getListErrorReason error", error);
                reject(error.msgError);
            });
        });
    };
};

// action lấy thông tin lô date
export const getBatchByExpirationDate = (data) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                "companyId": getState().userReducer.companyID,
                "brandId": getState().userReducer.brandID,
                "storeId": getState().userReducer.storeID,
                "productId": data.productId,
                "realProductId": data.realProductId,
                "realProductStatusTypeId": data.realProductStatusTypeId,
                "inventoryStatusId": data.inventoryStatusId,
                "expiredDate": data.expirationDate
            };
            apiBase(API_GET_BATCH_BY_EXPIRATION_DATE, METHOD.POST, body).then((response) => {
                console.log("getBatchByExpirationDate success", response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    resolve(object);
                } else {
                    reject("Không có thông tin lô của sản phẩm");
                }
            }).catch((error) => {
                console.log("getBatchByExpirationDate error", error);
                reject(error.msgError);
            });
        });
    };
};

// action lấy chi tiết sản yêu cầu khai báo tình trạng hàng hoá
export const getReportRequestDetail = (reportProductSessionId) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                "languageId": getState().userReducer.languageID,
                "realProductStatusDetailId": reportProductSessionId,
                "loginStoreId": getState().userReducer.storeID
            };
            apiBase(API_GET_REPORT_REQUEST_DETAIL, METHOD.POST, body).then((response) => {
                console.log("getReportRequestDetail success", JSON.stringify(response));
                const { object } = response;
                if (!helper.IsEmptyObject(object) && helper.IsNonEmptyArray(object.listRealStatusProducts) && helper.IsNonEmptyArray(object.listRealProductStatusDetails)) {
                    resolve(object);
                } else {
                    reject(translate('inventory.cannot_get_report_request_detail'));
                }
            }).catch((error) => {
                console.log("getReportRequestDetail error", error);
                reject(error.msgError);
            });
        });
    };
};

// action lấy hình ảnh của sản phẩm
export const getProductImage = (productIdRef) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                "productIdRef": productIdRef,
                "loginStoreID": getState().userReducer.storeID
            };
            apiBase(API_GET_PRODUCT_IMAGE, METHOD.POST, body).then((response) => {
                console.log("getProductImage success", JSON.stringify(response));
                const { object } = response;
                if (!helper.IsEmptyObject(object) && helper.IsNonEmptyString(object.image)) {
                    resolve(object.image);
                } else {
                    resolve("");
                }
            }).catch((error) => {
                console.log("getProductImage error", error);
                resolve("");
            });
        });
    };
};

// action kết thúc khai báo tình trạng hàng hoá
export const finishReportProductState = () => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            const reportProductStateRequestId = getState().inventoryReducer.reportProductStateRequestId;
            const outSystemReportProductStateRequestId = getState().inventoryReducer.outSystemReportProductStateRequestId;
            let body = {
                "realProductStatusIdList": `${reportProductStateRequestId}${outSystemReportProductStateRequestId}`
            };
            apiBase(API_FINISH_REPORT_PRODUCT_STATE_REQUEST, METHOD.POST, body).then((response) => {
                console.log("finishReportProductState success", JSON.stringify(response));
                if (response.object == "Cập nhật thành công!") {
                    dispatch(resetReportProductStateRequest());
                    resolve();
                } else {
                    reject(translate('inventory.update_failed'));
                }
            }).catch((error) => {
                console.log("finishReportProductState error", error);
                reject(error);
            });
        });
    };
};

export const getListProcessRP = (dtmDate) => {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreID: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                loginID: getState().userReducer.userName,
                moduleID: getState().userReducer.moduleID,
                dtmDate: dtmDate,
                termids: null,
                strUserId: null

            };
            apiBase(API_GET_LIST_PROCESSS_RP, METHOD.POST, body)
                .then((res) => {
                    console.log("getListProcessRP success", res);
                    let dataRes = [];
                    res.object.forEach(element => {
                        const isWarning = element.notDebtProcessType != 0;
                        let realProductStatusValue = `${element.realProductStatusId.trim()} - ${element.realProductStatusTypeName}`;
                        let dataElement = {
                            ...element,
                            realProductStatusValue,
                            isWarning,
                            warningText: isWarning ? "Không tham gia truy thu" : ""
                        };
                        dataRes.push(dataElement);
                    });
                    resolve(dataRes);
                })
                .catch((err) => {
                    console.log("getListProcessRP error", err);
                    reject(err);
                });
        });
    };
};

const dynamicSort = (property) => {
    var sortOrder = 1;
    if (property[0] === "-") {
        sortOrder = -1;
        property = property.substr(1);
    }
    return (a, b) => {
        let result = (a[property] < b[property]) ? -1 : (a[property] > b[property]) ? 1 : 0;
        return result * sortOrder;
    };
};

const checkImeiInventoryStatusID = (arr, imei, inventoryStatusID) => {
    if (helper.IsValidateObject(imei)) {
        let index = arr.findIndex((x) => x.imei && x.imei.trim().toUpperCase() == imei.trim().toUpperCase());
        if (index > -1) {
            if (arr[index].isInput == 1) {
                return arr;
            } else {
                arr[index].inventoryStatusID = inventoryStatusID;
                arr[index].isInput = 1;
            }
        }
    }
    return arr;
};

const start_get_list_product_status = () => {
    return ({
        type: START_GET_LIST_PRODUCT_STATUS,
    });
};

const stop_get_list_product_status = (
    listProductStatus,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_LIST_PRODUCT_STATUS,
        listProductStatus,
        isEmpty,
        description,
        isError,
    });
};

const start_get_list_area = () => {
    return ({
        type: START_GET_LIST_AREA,
    });
};

const stop_get_list_area = (
    listDataArea,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_LIST_AREA,
        listDataArea,
        isEmpty,
        description,
        isError,
    });
};

const set_list_inventory_type = (listInventoryType) => {
    return ({
        type: SET_LIST_INVENTORY_TYPE,
        listInventoryType
    });
};

const start_get_list_inventory_term = () => {
    return ({
        type: START_GET_LIST_INVENTORY_TERM,
    });
};

const stop_get_list_inventory_term = (
    listInventoryTerm,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_LIST_INVENTORY_TERM,
        listInventoryTerm,
        isEmpty,
        description,
        isError,
    });
};

const start_get_list_inventory_term_history = () => {
    return ({
        type: START_GET_LIST_INVENTORY_TERM_HISTORY,
    });
};

const stop_get_list_inventory_term_history = (
    listInventoryTermHistory,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_LIST_INVENTORY_TERM_HISTORY,
        listInventoryTermHistory,
        isEmpty,
        description,
        isError,
    });
};

const start_get_list_term_area = () => {
    return ({
        type: START_GET_LIST_TERM_AREA,
    });
};

const stop_get_list_term_area = (
    listTermArea,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_LIST_TERM_AREA,
        listTermArea,
        isEmpty,
        description,
        isError,
    });
};

const start_get_list_group_product = () => {
    return {
        type: START_GET_LIST_GROUP_PRODUCT
    };
};

const stop_get_list_group_product = (
    listGroupProduct,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_LIST_GROUP_PRODUCT,
        listGroupProduct,
        isEmpty,
        description,
        isError,
    });
};

const set_inventory_product_info = (inventoryProductInfo) => {
    return {
        type: SET_INVENTORY_PRODUCT_INFO,
        inventoryProductInfo
    };
};

const start_get_list_arrears = () => {
    return ({
        type: START_GET_LIST_ARREARS,
    });
};

export const stop_get_list_arrears = (
    listArrears,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_LIST_ARREARS,
        listArrears,
        isEmpty,
        description,
        isError,
    });
};

const start_get_list_product_arrears = () => {
    return ({
        type: START_GET_LIST_PRODUCT_ARREARS,
    });
};

export const stop_get_list_product_arrears = (
    listProductArrears,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_LIST_PRODUCT_ARREARS,
        listProductArrears,
        isEmpty,
        description,
        isError,
    });
};

const start_get_list_product_arrears_process = () => {
    return ({
        type: START_GET_LIST_PRODUCT_ARREARS_PROCESS,
    });
};

export const stop_get_list_product_arrears_process = (
    listProductArrearsProcess,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_LIST_PRODUCT_ARREARS_PROCESS,
        listProductArrearsProcess,
        isEmpty,
        description,
        isError,
    });
};

const start_get_list_staff_arrears = () => {
    return ({
        type: START_GET_LIST_STAFF_ARREARS,
    });
};

export const stop_get_list_staff_arrears = (
    listStaffArrears,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_LIST_STAFF_ARREARS,
        listStaffArrears,
        isEmpty,
        description,
        isError,
    });
};

const start_get_list_deviated_group_product = () => {
    return {
        type: START_GET_LIST_DEVIATED_GROUP_PRODUCT
    };
};

const stop_get_list_deviated_group_product = (
    listDeviatedGroupProduct,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_LIST_DEVIATED_GROUP_PRODUCT,
        listDeviatedGroupProduct,
        isEmpty,
        description,
        isError,
    });
};

const start_get_list_imei_by_product = () => {
    return ({
        type: START_GET_LIST_IMEI_BY_PRODUCT,
    });
};

export const stop_get_list_imei_by_product = (
    listImeiByProduct,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_LIST_IMEI_BY_PRODUCT,
        listImeiByProduct,
        isEmpty,
        description,
        isError,
    });
};

const set_is_has_no_imei_product_checked = (isCheck) => {
    return ({
        type: SET_IS_HAS_NO_IMEI_PRODUCT_CHECKED,
        isHasNoImeiProductChecked: isCheck
    });
};

const start_get_product_process_imei = () => {
    return ({
        type: START_GET_PRODUCT_PROCESS_IMEI,
    });
};

export const stop_get_product_process_imei = (
    listProductProcessImei,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_PRODUCT_PROCESS_IMEI,
        listProductProcessImei,
        isEmpty,
        description,
        isError,
    });
};

const start_get_list_report_state = () => {
    return ({
        type: START_GET_LIST_REPORT_STATE,
    });
};

const stop_get_list_report_state = (
    listReportState,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_LIST_REPORT_STATE,
        listReportState,
        isEmpty,
        description,
        isError,
    });
};

const start_get_list_report_product_state_request = () => {
    return ({
        type: START_GET_LIST_REPORT_PRODUCT_STATE_REQUEST
    });
};

const stop_get_list_report_product_state_request = (
    listReportProductStateRequest,
    listRequestResponse,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_LIST_REPORT_PRODUCT_STATE_REQUEST,
        listReportProductStateRequest,
        listRequestResponse,
        isEmpty,
        description,
        isError
    });
};

const set_report_product_request_state_id = (reportProductStateRequestId) => ({
    type: SET_REPORT_PRODUCT_STATE_REQUEST_ID,
    reportProductStateRequestId
});

const set_out_system_report_product_request_state_id = (outSystemReportProductStateRequestId) => ({
    type: SET_OUT_SYSTEM_REPORT_PRODUCT_STATE_REQUEST_ID,
    outSystemReportProductStateRequestId
});
