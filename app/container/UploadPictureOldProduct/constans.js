import { translate } from '@translate';

export const LIST_CATEGORY = [
    {
        id: -1,
        value: translate('oldProduct.cagegory_-1')
    },
    {
        id: 42,
        value: translate('oldProduct.cagegory_42')
    },
    {
        id: 44,
        value: translate('oldProduct.cagegory_44')
    },
    {
        id: 522,
        value: translate('oldProduct.cagegory_522')
    },
    {
        id: 1942,
        value: translate('oldProduct.cagegory_1942')
    },
    {
        id: 1882,
        value: translate('oldProduct.cagegory_1882')
    },
    {
        id: 7077,
        value: translate('oldProduct.cagegory_7077')
    },
    {
        id: 166,
        value: translate('oldProduct.cagegory_166')
    },
    {
        id: 1943,
        value: translate('oldProduct.cagegory_1943')
    },
    {
        id: 1944,
        value: translate('oldProduct.cagegory_1944')
    },
    {
        id: 1962,
        value: translate('oldProduct.cagegory_1962')
    },
    {
        id: 2002,
        value: translate('oldProduct.cagegory_2002')
    },
    {
        id: 2202,
        value: translate('oldProduct.cagegory_2202')
    },
    {
        id: 2065,
        value: translate('oldProduct.cagegory_2065')
    },
    {
        id: 622,
        value: translate('oldProduct.cagegory_622')
    },
    {
        id: 2162,
        value: translate('oldProduct.cagegory_2162')
    },
    {
        id: 165,
        value: translate('oldProduct.cagegory_165')
    },
    {
        id: 2022,
        value: translate('oldProduct.cagegory_2022')
    },
    {
        id: 3305,
        value: translate('oldProduct.cagegory_3305')
    },
    {
        id: 284,
        value: translate('oldProduct.cagegory_284')
    },
    {
        id: 1982,
        value: translate('oldProduct.cagegory_1982')
    },
    {
        id: 7604,
        value: translate('oldProduct.cagegory_7604')
    },
    {
        id: 3385,
        value: translate('oldProduct.cagegory_3385')
    },
    {
        id: 5693,
        value: translate('oldProduct.cagegory_5693')
    },
    {
        id: 5697,
        value: translate('oldProduct.cagegory_5697')
    },
    {
        id: 5698,
        value: translate('oldProduct.cagegory_5698')
    },
    {
        id: 5475,
        value: translate('oldProduct.cagegory_5475')
    },
    {
        id: 1,
        value: translate('oldProduct.cagegory_1')
    },
    {
        id: 54,
        value: translate('oldProduct.cagegory_54')
    },
    {
        id: 2162,
        value: translate('oldProduct.cagegory_2162_1')
    },
    {
        id: 4727,
        value: translate('oldProduct.cagegory_4727')
    },
    {
        id: 9499,
        value: translate('oldProduct.cagegory_9499')
    },
    {
        id: 9118,
        value: translate('oldProduct.cagegory_9118')
    },
    {
        id: 10618,
        value: translate('oldProduct.cagegory_10618')
    },
    {
        id: 86,
        value: translate('oldProduct.cagegory_86')
    },
    {
        id: 862,
        value: translate('oldProduct.cagegory_862')
    },
    {
        id: 57,
        value: translate('oldProduct.cagegory_57')
    },
    {
        id: 1882,
        value: translate('oldProduct.cagegory_1882_1')
    },
    {
        id: 7264,
        value: translate('oldProduct.cagegory_7264')
    },
    {
        id: 2222,
        value: translate('oldProduct.cagegory_2222')
    },//Thêm 02112022
    {
        id: 1989,
        value: translate('oldProduct.cagegory_1989')
    },
    {
        id: 1984,
        value: translate('oldProduct.cagegory_1984')
    },
    {
        id: 1988,
        value: translate('oldProduct.cagegory_1988')
    },
    {
        id: 1992,
        value: translate('oldProduct.cagegory_1992')
    },
    {
        id: 10139,
        value: translate('oldProduct.cagegory_10139')
    },
    {
        id: 1985,
        value: translate('oldProduct.cagegory_1985')
    },
    {
        id: 1991,
        value: translate('oldProduct.cagegory_1991')
    },//Thêm 23112022
    {
        id: 1922,
        value: translate('oldProduct.cagegory_1922')
    },
    {
        id: 1983,
        value: translate('oldProduct.cagegory_1983')
    },
    {
        id: 1987,
        value: translate('oldProduct.cagegory_1987')
    },
    {
        id: 1990,
        value: translate('oldProduct.cagegory_1990')
    },
    {
        id: 9418,
        value: translate('oldProduct.cagegory_9418')
    },//thêm 16/12/2022
    {
        id: 1986,
        value: translate('oldProduct.cagegory_1986')
    },
    {
        id: 2062,
        value: translate('oldProduct.cagegory_2062')
    },
    {
        id: 2064,
        value: translate('oldProduct.cagegory_2064')
    },
    {
        id: 2262,
        value: translate('oldProduct.cagegory_2262')
    },
    {
        id: 2322,
        value: translate('oldProduct.cagegory_2322')
    },
    {
        id: 2428,
        value: translate('oldProduct.cagegory_2428')
    },
    {
        id: 5473,
        value: translate('oldProduct.cagegory_5473')
    },
    {
        id: 7498,
        value: translate('oldProduct.cagegory_7498')
    }
];

export const LIST_STATUS = [
    {
        id: 0,
        value: translate('oldProduct.value_0')
    },
    {
        id: 1,
        value: translate('oldProduct.value_1')
    },
    {
        id: 2,
        value: translate('oldProduct.value_2')
    },
    {
        id: 4,
        value: translate('oldProduct.value_4')
    },
    {
        id: 7,
        value: translate('oldProduct.value_7')
    },
    {
        id: 8,
        value: translate('oldProduct.value_8')
    }
];

//đã lấy từ dữ liệu api
// export const EXCLUDE_CATE_LIST = [1943, 1944, 57, 54, 4727, 9499, 9118, 10618, 86, 1882, 3385, 2222, 7264, 2162,
//     4645, 1990, 10139, 5473, 1983, 3305, 1982, 1986, 2063, 1987, 1989, 2064, 1984, 9418, 1922, 2062, 9998, 7173,
//     4099, 4145, 5292, 4660, 4439, 4159, 4157, 4143, 4743, 4158, 4141, 5000, 4161, 4156, 4155, 4146, 4099, 4152,
//     4150, 4147, 4153, 4142, 4262, 4139, 4160, 4154, 4151, 4149, 3639, 3799, 4140, 2859, 953, 954, 968, 2053, 958,
//     957, 956, 967, 7901, 2262, 2322, 7684, 9599, 1985, 7685, 7358, 7075, 346, 4366, 1988, 9278, 9698, 9941, 5474, 2428, 2302, 7899,
//     10140, 9058, 7858, 9100, 7859, 10119, 9940, 10118, 365, 8968, 9942, 9099, 9660, 366, 9658, 2403, 3187, 3736, 7691, 8558, 7945, 2402, 7692, 5231,
//     6790, 8560, 5230, 7479, 7943, 3738, 3188, 4927, 9008, 10109, 3730, 4347, 4957, 4956, 9239, 4946, 7690, 3728, 7688, 7694, 3732, 7942, 3729, 3729,
//     8762, 7697, 7940, 5228, 2529, 6553, 9318, 9338, 9339, 8579, 9678, 5105, 1992, 7498, 8765, 7696, 8879, 6012, 4928, 7948, 7947, 5226, 5225, 5227,
//     7941, 4930, 5205, 4929, 5229, 4326, 12318, 1991, 11818, 1962, 166
// ];
export const EXCLUDE_PROD_LIST = [
    "0164779000005", "0164779000006", "0164779000017", "0164779000018", "0164779000010",
    "0164779000009", "0164779000015", "0164779000016", "0164779000019", "0164779000021",
    "0164779000002", "0164779000003", "0164779000011", "0164779000012", "0164779000001",
    "0161031000212", "0161031000215", "0164779000024", "0164779000025", "0164779000026",
    "0164779000027", "0164779000014"
];

export const NO_CHECK_STATUS_CATE_LIST = [1943, 1944];


export const LIST_INVENTORY_STATUS = [
    {
        inventoryStatusID: 2,
        inventoryStatusName: translate('oldProduct.inventory_status_name_2')
    },
    {
        inventoryStatusID: 7,
        inventoryStatusName: translate('oldProduct.inventory_status_name_7')
    },
    {
        inventoryStatusID: 4,
        inventoryStatusName: "Cũ (thu mua)"
    }
];

export const REASON_DATA = [
    {
        title: translate('oldProduct.reason_1'),
        resonIndex: 0
    },
    {
        title: translate('oldProduct.reason_2'),
        resonIndex: 1
    },
    {
        title: translate('oldProduct.reason_3'),
        resonIndex: 2
    },
    {
        title: translate('oldProduct.reason_4'),
        resonIndex: 3,

    },
    {
        title: translate('oldProduct.reason_5'),
        resonIndex: 4,
    },
    {
        title: translate('oldProduct.reason_6'),
        resonIndex: 5
    },
    {
        title: translate('oldProduct.other_reason'),
        resonIndex: 6
    }
];