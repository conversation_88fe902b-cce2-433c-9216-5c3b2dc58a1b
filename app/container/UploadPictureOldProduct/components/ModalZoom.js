import React from 'react';
import {
    SafeAreaView,
    View,
    TouchableOpacity,
    Modal,
    StyleSheet
} from 'react-native';
import ImageZoom from 'react-native-image-pan-zoom';
import { Icon, ImageURI } from "@components";
import { constants } from "@constants";
import { COLORS } from "@styles";
const { URL_SALE_COMBO } = constants;

const width = constants.width;
const height = width * 1.6;

const ModalZoom = ({
    isVisible,
    hideModal,
    urlImage,
    urlFastImage,
    check
}) => {
    // console.log(urlImage);
    return (
        <Modal
            visible={isVisible}
            animationType={"fade"}
            transparent={true}
        >
            <View style={styles.container}>
                <SafeAreaView style={styles.wrapper}>
                    <ImageZoom cropWidth={constants.width}
                        cropHeight={constants.height}
                        imageWidth={width}
                        imageHeight={height}>

                        {
                            check != null
                                ?
                                <ImageURI

                                    uri={urlImage}
                                    resizeMode="contain"
                                    style={styles.image}
                                /> : <ImageURI
                                    uri={urlFastImage}
                                    resizeMode="contain"
                                    style={styles.image}
                                />
                        }
                    </ImageZoom>
                    <TouchableOpacity style={styles.btnClose}
                        activeOpacity={0.8}
                        onPress={hideModal}
                    >
                        <Icon
                            iconSet={"Ionicons"}
                            name={"close"}
                            size={30}
                            color={COLORS.icFFFFFF}
                        />
                    </TouchableOpacity>
                </SafeAreaView>
            </View>
        </Modal>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: COLORS.bg000000,
        paddingTop: constants.heightTopSafe,
    },
    image: {
        width: width,
        height: height,
    },
    wrapper: {
        flex: 1,
        justifyContent: "center",
        alignItems: 'center',
    },
    btnClose: {
        width: 50,
        height: 50,
        position: "absolute",
        top: 10,
        right: 0,
        justifyContent: "center",
        alignItems: "center",
        alignSelf: "flex-end",
    }
})

export default ModalZoom;