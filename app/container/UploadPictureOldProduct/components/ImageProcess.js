import React from 'react';
import {
    View,
    TouchableOpacity,
    ActivityIndicator,
    StyleSheet
} from 'react-native';
import {
    MyText,
    Icon,
    ImageURI
} from "@components";
import { COLORS } from "@styles";

const ImageProcess = function ({ onCamera, urlImageRemote, urlImageLocal, isSignature, styleContainer, heightImage }) {

    return (
        (!urlImageRemote && !urlImageLocal)
            ?
            <TouchableOpacity style={styleContainer}
                onPress={onCamera}
                activeOpacity={0.6}
            >
                <View style={styles.centerItem}>
                    <Icon
                        iconSet={isSignature ? "FontAwesome" : "Ionicons"}
                        name={isSignature ? "file-signature" : "ios-camera"}
                        color={COLORS.icFFB23F}
                        size={isSignature ? 50 : 60}
                    />
                </View>
            </TouchableOpacity>
            :
            <View style={styleContainer}>
                <View style={{
                    width: "100%",
                    height: heightImage,
                }}>
                    <ImageURI
                        style={{
                            width: "auto",
                            height: heightImage,
                        }}
                        uri={urlImageRemote ? urlImageRemote : urlImageLocal}
                        resizeMode={"contain"}
                    />
                </View>
                <View
                    style={styles.loadingIndicator}
                >
                    <ActivityIndicator
                        color={COLORS.bg00A98F}
                        style={{ marginLeft: 4 }}
                    />
                </View>
            </View>
    );
}

const styles = StyleSheet.create({
    loadingIndicator: {
        padding: 5,
        justifyContent: "center",
        alignItems: "center",
        position: "absolute",
        top: 0,
        right: 0
    },
    centerItem: {
        justifyContent: "center",
        alignItems: "center"
    }
})

export default ImageProcess;