import React from 'react';
import { View, StyleSheet, TouchableOpacity, Alert, Image, } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { MyText, showBlockUI, hideBlockUI, Button, Icon, ImageURI } from '@components';
import { helper } from '@common';

import { translate } from '@translate';
import { COLORS } from "@styles";
import { constants } from '@constants';
import { getInfoOldProduct, getOldProductAccessories } from '../action';

const { URL_THUMBNAIL } = constants;

function FlatListItem({
    navigation,
    item,
    ind,
    searchData,
    excludeAccessoryCategory
}) {
    const {
        storeID,
        moduleID,
        languageID,
        userName
    } = useSelector(state => state.userReducer);
    const dispatch = useDispatch();
    const handleViewDetail = () => {
        const body = {
            loginStoreID: storeID,
            languageID: languageID,
            moduleID: moduleID,
            loginID: userName,
            productCode: item.ProductCode,
            imei: item.IMEI,
            oldID: item.OldID
        };
        showBlockUI();
        getInfoOldProduct(body).then(async response => {
            const { OldProduct, ProductAccessory, IsCheckPermissionApprove } = response;
            if (OldProduct.StatusID == 3 && excludeAccessoryCategory.includes(OldProduct.CategoryID)) {
                OldProduct.StatusID = 1;
            }
            OldProduct.IMEIStatusID = ProductAccessory.IMEIStatusID;
            OldProduct.AccessoryListTmp = OldProduct.AccessoryList;
            OldProduct.AccessoryList = "";
            await dispatch(getOldProductAccessories(OldProduct));
            hideBlockUI();
            navigation.navigate("DetailUploadPictureOldProduct", {
                item,
                OldProduct,
                ProductAccessory,
                searchData,
                IsCheckPermissionApprove,
                excludeAccessoryCategory
            });
        }).catch((msgError) => {
            hideBlockUI();
            Alert.alert(translate("common.notification_uppercase"), msgError, [
                {
                    text: translate("common.btn_accept")
                }
            ]);
        })
    }
    return (
        <View
            style={[
                styles.flatListContainer,
                { backgroundColor: ind % 2 == 0 ? COLORS.bgFDF9E5 : '#fff' }
            ]}>
            <View style={styles.wrapper}>
                <View style={styles.row}>
                    <View style={styles.wrapImage}>
                        {
                            item.RepresentImage != null
                                ?
                                <TouchableOpacity
                                    onPress={handleViewDetail}
                                >
                                    <ImageURI
                                        uri={helper.getImageURL(item.RepresentImage)}
                                        style={styles.representImage}
                                        resizeMode='contain'

                                    />
                                </TouchableOpacity>
                                :
                                <TouchableOpacity
                                    onPress={handleViewDetail}
                                >
                                    <Icon
                                        iconSet={"MaterialCommunityIcons"}
                                        name={"cloud-upload-outline"}
                                        color={COLORS.ic147EFB}
                                        size={110}
                                    />
                                </TouchableOpacity>
                        }
                    </View>
                    <View style={{ width: "62%" }}>
                        <View style={styles.import_info}>
                            <MyText
                                style={[styles.info_Col_First]}
                                text={translate("oldProduct.name_product")}
                            >
                                <MyText
                                    style={[styles.info_col_second, styles.txtBold]}
                                    text={item.ProductName}
                                />
                            </MyText>
                        </View>
                        <View style={styles.import_info}>
                            <MyText
                                style={styles.info_Col_First}
                                text={translate("oldProduct.imei")}>
                                <MyText
                                    style={[styles.info_col_second, styles.txtBold]}
                                    text={item.IMEI}
                                />
                            </MyText>
                        </View>
                        <View style={styles.import_info}>
                            <MyText
                                style={styles.info_Col_First}
                                text={translate("oldProduct.price")}></MyText>
                            <MyText
                                style={[styles.info_col_second]}
                                text={helper.convertNum(item.Price)}
                            ></MyText>
                        </View>

                        <View style={styles.import_info}>
                            <MyText
                                style={styles.info_Col_First}
                                text={translate("oldProduct.warranty")}></MyText>
                            <MyText
                                style={[styles.info_col_second]}
                                text={item.WarrantyStatus}
                            ></MyText>
                        </View>
                        <View style={styles.import_info}>
                            <MyText
                                style={[styles.info_Col_First]}
                                text={translate("oldProduct.inventory_status_name")}
                            >
                                <MyText
                                    style={[styles.info_col_second, styles.txtBold]}
                                    text={item.InventoryStatusName}
                                />
                            </MyText>
                        </View>
                        <View style={styles.import_info}>
                            <MyText
                                text={`${translate("oldProduct.import_store")} ${helper.convertMaskString(item.CheckInputRealTime)} ${translate("oldProduct.time")}`}></MyText>
                        </View>
                        <View style={styles.import_info}>
                            <MyText style={styles.require}
                                text={item.StatusName} ></MyText>
                        </View>
                    </View>

                </View>

                <View
                    style={styles.wrapBtnViewDetail}>
                    <Button
                        text={translate("oldProduct.view_detail")}
                        onPress={handleViewDetail}
                        styleContainer={styles.btnViewDetail}
                        styleText={styles.txtViewDetail}

                    />

                </View>

            </View>
        </View >
    );
}
const styles = StyleSheet.create({
    wrapper: {
        padding: 6
    },
    flatListContainer: {
        borderWidth: 1,
        borderColor: '#cccccc',
        borderRadius: 10,
        marginHorizontal: 2,
        marginVertical: 4
    },
    import_info: {
        flexDirection: 'row',
        paddingBottom: 5,

    },
    info_Col_First: {
        // width: '29%'
    },
    info_col_second: {
        width: '70%',
    },

    text: {
        color: 'red',
        fontWeight: 'bold',
        paddingHorizontal: 2
    },
    txtBold: {
        fontWeight: 'bold',
    },
    wrapBtnViewDetail: {
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 10
    },
    btnViewDetail: {
        backgroundColor: COLORS.btn2FB47C,
        borderRadius: constants.getSize(5),
        height: constants.getSize(38),
        marginLeft: 5,
        paddingHorizontal: 12
    },
    txtViewDetail: {
        color: '#ffffff',
        fontSize: 13,
        fontWeight: '700',
        marginRight: 5
    },
    require: {
        color: COLORS.txtFF0000
    },
    wrapImage: {
        width: "38%",
        justifyContent: "center",
        alignSelf: "center",
    },
    representImage: {
        width: "95%",
        height: 120,
    },
    row: {
        flexDirection: 'row'
    }
});

export default FlatListItem;
