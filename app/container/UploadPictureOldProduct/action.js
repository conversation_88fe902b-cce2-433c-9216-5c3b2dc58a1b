import { apiBase, METH<PERSON>, SUCCE<PERSON>, EMP<PERSON>, ERROR } from '@config';
import { API_CONST, STORAGE_CONST } from '@constants';
import { helper, storageHelper, base64 } from '@common';
import { translate } from '@translate';
const {
    API_APPROVED_OLD_PRODUCT,
    API_DELETE_OLD_PRODUCT_IMAGE,
    API_GET_LIST_OLD_PRODUCT,
    API_GET_OLD_PRODUCT_ACCESSORIES,
    API_GET_OLD_PRODUCT_INFO,
    API_GET_PRODUCT_ACCESSORIES,
    API_GET_REPORT_DATA,
    API_INSERT_OLD_PRODUCT_IMAGE,
    API_UPDATE_OLD_PRODUCT_INFO,
    API_VALID_OR_GET_OLD_PRODUCTS_IMAGE,
    API_UPDATE_OLDPRODUCT_STATUSDESC,
    API_GET_OLD_PRODUCT_CATEGORY_LIST
} = API_CONST;
const START_SEARCH_UPLOADPICTURE = 'START_SEARCH_UPLOADPICTURE';
const STOP_SEARCH_UPLOADPICTURE = 'STOP_SEARCH_UPLOADPICTURE';
const START_GET_DATA_PRODUCT = 'START_GET_DATA_PRODUCT';
const STOP_GET_DATA_PRODUCT = 'STOP_GET_DATA_PRODUCT';
const START_GET_VALID_OR_GETOldPRODUCTIMAGE = 'START_GET_VALID_OR_GETOldPRODUCTIMAGE';
const STOP_GET_VALID_OR_GETOldPRODUCTIMAGE = 'STOP_GET_VALID_OR_GETOldPRODUCTIMAGE';
const START_GETLISTACESSORY = 'START_GETLISTACESSORY';
const STOP_GETLISTACESSORY = 'STOP_GETLISTACESSORY';
const START_GET_PRODUCT_OLD_ACCESSORY = 'START_GET_PRODUCT_OLD_ACCESSORY';
const STOP_GET_PRODUCT_OLD_ACCESSORY = 'STOP_GET_PRODUCT_OLD_ACCESSORY';
const RESET_OLD_PRODUCT = 'RESET_OLD_PRODUCT';
const STORE_OLD_PRODUCT = 'STORE_OLD_PRODUCT';
export const UploadPictureAction = {
    START_SEARCH_UPLOADPICTURE,
    STOP_SEARCH_UPLOADPICTURE,
    START_GET_DATA_PRODUCT,
    STOP_GET_DATA_PRODUCT,
    START_GET_VALID_OR_GETOldPRODUCTIMAGE,
    STOP_GET_VALID_OR_GETOldPRODUCTIMAGE,
    START_GETLISTACESSORY,
    STOP_GETLISTACESSORY,
    RESET_OLD_PRODUCT,
    STORE_OLD_PRODUCT,
    START_GET_PRODUCT_OLD_ACCESSORY,
    STOP_GET_PRODUCT_OLD_ACCESSORY
};

export const searchUploadPicture = function (body) {
    return new Promise((resolve, reject) => {
        apiBase(
            API_GET_LIST_OLD_PRODUCT,
            METHOD.POST,
            body
        )
            .then((res) => {
                if (!helper.IsEmptyObject(res.object)) {
                    resolve(res.object);
                } else {
                    reject("Không tìm thấy danh sách máy cũ.");
                }
            })
            .catch((err) => {
                reject(err.msgError);
            });
    });

};

export const getInfoOldProduct = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(
            API_GET_OLD_PRODUCT_INFO,
            METHOD.POST,
            body
        )
            .then((res) => {
                if (helper.hasProperty(res, 'object')) {
                    resolve(res.object);
                } else {
                    resolve([]);
                }
            })
            .catch((err) => {
                reject(err.msgError);
            });
    });
}

export const approveOldProduct = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(
            API_APPROVED_OLD_PRODUCT,
            METHOD.POST,
            body
        )
            .then((res) => {
                if (helper.hasProperty(res, 'object')) {
                    resolve(res.object);
                } else {
                    reject("Thất bại.");
                }
            })
            .catch((err) => {
                reject(err.msgError);
            });
    });
}

export const getValidOrgetOldProductsImage = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(
            API_VALID_OR_GET_OLD_PRODUCTS_IMAGE,
            METHOD.POST,
            body
        )
            .then((response) => {
                const { object } = response;
                if (!helper.IsEmptyArray(object)) {
                    object.sort(function (a, b) {
                        return a.DisplayOrder - b.DisplayOrder;
                    });
                    resolve(object)
                }
                else {
                    reject(translate("oldProduct.no_find_image"))
                }
            })
            .catch((error) => {
                reject(error.msgError)
            });
    });
};

export const getListAccessories = function (productID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            dispatch(startGetListAccessories());
            let body = {
                loginStoreID: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                loginID: getState().userReducer.userName,
                productCode: productID.toString()

            };
            apiBase(
                API_GET_PRODUCT_ACCESSORIES,
                METHOD.POST,
                body
            )
                .then((res) => {
                    if (
                        helper.hasProperty(res, 'object') &&
                        helper.isArray(res.object) &&
                        res.object.length > 0
                    ) {
                        resolve(res.object);
                        dispatch(
                            stopGetListAccessories(res.object)
                        );
                    } else {
                        resolve([]);
                        dispatch(
                            stopGetListAccessories(
                                res.object,
                                EMPTY,
                                translate("oldProduct.no_find_accessories"),
                                true
                            )
                        );
                    }
                })
                .catch((err) => {
                    dispatch(
                        stopGetListAccessories(
                            [],
                            true,
                            err.msgError,
                            true
                        )
                    );
                });
        });
    };
};

export const getOldProductAccessories = function (productInfo) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            dispatch(startGetProductOldAccessories());
            let body = {
                loginStoreID: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                loginID: getState().userReducer.userName,
                productCode: productInfo.ProductCode.toString(),
                imei: productInfo.IMEI.toString()
            };
            apiBase(
                API_GET_OLD_PRODUCT_ACCESSORIES,
                METHOD.POST,
                body
            )
                .then((res) => {
                    if (
                        helper.hasProperty(res, 'object') &&
                        helper.isArray(res.object) &&
                        res.object.length > 0
                    ) {
                        if (res.object.filter(x => x.IsSelect).length == 0) {
                            productInfo.AccessoryList = "";
                        } else {
                            productInfo.AccessoryList = productInfo.AccessoryListTmp;
                        }

                        resolve(res.object);
                        dispatch(
                            stopGetProductOldAccessories(res.object)
                        );
                    } else {
                        productInfo.AccessoryList = "";
                        resolve([]);
                        dispatch(
                            stopGetProductOldAccessories(
                                res.object,
                                EMPTY,
                                translate('oldProduct.no_find_accessories_oldproduct'),
                                true
                            )
                        );
                    }
                })
                .catch((err) => {
                    productInfo.AccessoryList = "";
                    dispatch(
                        stopGetListAccessories(

                            [],
                            true,
                            err.msgError,
                            ERROR
                        )
                    );
                });
        });
    };
};

export const removeImageProduct = function (imageID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreID: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                loginID: getState().userReducer.userName,
                moduleID: getState().userReducer.moduleID,
                imageID: imageID
            };

            apiBase(API_DELETE_OLD_PRODUCT_IMAGE, METHOD.POST, body)
                .then((res) => {
                    resolve(res);
                })
                .catch((err) => {
                    reject(err.msgError);
                });
        });
    };
};

export const updateStatusOldProduct = function ({ statusType,
    listAccessoriesID,
    listRemoveAccessoriesID,
    accessoriesNote,
    accessories,
    OldProduct }) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "productCode": OldProduct.ProductCode,
                "imei": OldProduct.IMEI,
                "oldID": OldProduct.OldID,
                "imeiStatusID": statusType,
                "accessories": accessories,
                "accessoriesNote": accessoriesNote,
                "listAccessoriesID": listAccessoriesID,
                "listRemoveAccessoriesID": listRemoveAccessoriesID
            };
            apiBase(API_UPDATE_OLD_PRODUCT_INFO, METHOD.POST, body).then((response) => {
                const { object } = response;
                if (!helper.IsEmptyObject(object)) {
                    resolve(object);
                }
                else {
                    reject("Cập nhật thất bại.");
                }
            }).catch(error => {
                reject(error.msgError);
            })
        })
    }
}

export const insertOldProductImage = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(
            API_INSERT_OLD_PRODUCT_IMAGE,
            METHOD.POST,
            body
        )
            .then((res) => {
                if (helper.hasProperty(res, 'object')) {
                    resolve(res.object);
                } else {
                    reject(translate('oldProduct.up_image_suscess'));
                }
            })
            .catch((err) => {
                reject(err.msgError);
            });
    });
}

export const getReportOldProduct = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(
            API_GET_REPORT_DATA,
            METHOD.POST,
            body
        )
            .then((res) => {
                // console.log('getReportOldProduct success', res.object);
                if (helper.hasProperty(res, 'object')) {
                    resolve(res.object);
                } else {
                    resolve([]);
                }
            })
            .catch((err) => {
                // console.log('getReportOldProduct error', err);
                reject(err.msgError);
            });
    });
}

export const getImageCDN = function (bodyFromData) {
    return new Promise((resolve, reject) => {
        apiBase(
            API_CONST.API_UPLOAD_OLD_IMAGE_CDN_NEW,
            METHOD.POST,
            bodyFromData,
            { "isCustomToken": true, "isUpload": true }
        )
            .then((response) => {
                if (
                    helper.isArray(response.object) &&
                    response.object.length > 0
                ) {
                    resolve(base64.decode(response.object[0] || "")
                    );
                    // console.log('getImageCDN', response.object);

                }
                else {
                    reject("Không lấy được ảnh từ CDN.");
                }
            })
            .catch((error) => {
                reject(error);
                // console.log('getImageCDN', error);
            });
    });
};

export const updateOldProductStatusDesc = function (body) {
    return new Promise((resolve, reject) => {
        apiBase(
            API_UPDATE_OLDPRODUCT_STATUSDESC,
            METHOD.POST,
            body
        )
            .then((res) => {
                if (
                    helper.hasProperty(res, 'object') &&
                    helper.isObject(res.object)
                ) {
                    resolve(res.object);

                } else {
                    resolve({});
                }
            })
            .catch((err) => {
                reject(err);
            });
    });
}

export const getOldProductCategoryList = function (body) {
    return new Promise((resolve, reject) => {
        apiBase(
            API_GET_OLD_PRODUCT_CATEGORY_LIST,
            METHOD.POST,
            body
        )
            .then((res) => {
                if (
                    helper.hasProperty(res, 'object') &&
                    helper.isArray(res.object) &&
                    res.object.length > 0
                ) {
                    resolve(res.object);
                } else {
                    resolve([]);
                }
            })
            .catch((err) => {
                reject(err);
            });
    });
}

export const startSearchUploadpicture = () => ({
    type: START_SEARCH_UPLOADPICTURE
});
export const stopSearchUploadpicture = (
    dataUploadPictures,
    isEmpty,
    description,
    isError
) => ({
    type: STOP_SEARCH_UPLOADPICTURE,
    dataUploadPictures,
    isEmpty,
    description,
    isError
});

export const startGetListAccessories = () => ({
    type: START_GETLISTACESSORY
});
export const stopGetListAccessories = (
    dataAccessories,
    isEmpty,
    description,
    isError
) => ({
    type: STOP_GETLISTACESSORY,
    dataAccessories,
    isEmpty,
    description,
    isError
});

export const startGetProductOldAccessories = () => ({
    type: START_GET_PRODUCT_OLD_ACCESSORY
});
export const stopGetProductOldAccessories = (
    dataOldAccessories,
    isEmpty,
    description,
    isError
) => ({
    type: STOP_GET_PRODUCT_OLD_ACCESSORY,
    dataOldAccessories,
    isEmpty,
    description,
    isError
});

const start_get_data_product = () => ({
    type: START_GET_DATA_PRODUCT
});

const stop_get_data_product = (
    dataProducts,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_DATA_PRODUCT,
    dataProducts,
    isEmpty,
    description,
    isError
});

const start_get_valid_or_getoldproducts_image = () => ({
    type: START_GET_VALID_OR_GETOldPRODUCTIMAGE
});

const stop_get_valid_or_getoldproducts_image = (
    dataOldPicture,
    isEmpty,
    description,
    isError
) => ({
    type: STOP_GET_VALID_OR_GETOldPRODUCTIMAGE,
    dataOldPicture,
    isEmpty,
    description,
    isError
});

