import React, { PureComponent } from 'react';
import { View } from 'react-native';
import { MyText, Icon } from '@components';

class SimDatePicker extends PureComponent {
  render() {
    let { title, text, color, datePicker } = this.props;

    return (
      <View style={{ paddingHorizontal: 10 }}>
        <MyText
          text={title}
          style={{
            fontWeight:
              'bold',
            color: '#333333'
          }}
          children={
            <MyText
              text={'*'}
              style={{ color: 'red' }}
            />
          }
        />
        <View
          style={{
            flex: 1,
            height: 'auto',
            alignItems:
              'center',
            flexDirection:
              'row',
            justifyContent:
              'center'
          }}>
          {datePicker}
        </View>
      </View>
    );
  }
}

export default SimDatePicker
