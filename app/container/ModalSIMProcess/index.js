import React, { Component } from 'react';
import {
    View,
    ScrollView,
    TouchableOpacity,
    Alert,
    Platform,
    Text
} from 'react-native';
import KModal from 'react-native-modal';
// import DatePicker from 'react-native-datepicker';
import ImageResizer from 'react-native-image-resizer';
import { launchImageLibrary } from 'react-native-image-picker';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import Marker, { ImageFormat } from 'react-native-image-marker';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import SafeAreaView from 'react-native-safe-area-view';
import {
    MyText,
    BaseContainer,
    SearchInput,
    Button,
    CaptureCamera,
    RadioButton,
    UIIndicator,
    BarcodeCamera,
    Picker,
    PickerSearch,
    FieldInput,
    DatePicker
} from '@components';
import { constants, API_CONST } from '@constants';
import { helper, dateHelper } from '@common';
import { translate } from '@translate';
import Header from "./Header";
import * as actionGetSimCreator from '../ActiveSimManager/action';
import * as actionShoppingCardCreator from '../ShoppingCart/action';
import NoInfoInputScreen from './NoInfoInputScreen';
import {
    HeaderTextField,
    ImageProcess,
    ModalCustomerInfoViettel,
    MyTextInput,
    SignImage,
    SimDatePicker
} from './components'
import { COLORS } from '@styles';

const TYPE_IMAGE = {
    imgIdentityCardFront: 0,
    imgIdentityCardBack: 1,
    imgPortrait: 2,
    imgSignature: 3
};

class ModalSIMProcess extends Component {
    constructor(props) {
        super(props);
        this.defaultState = {
            isVisibleCamera: false,
            isVisibleSign: false,
            isScanSerial: false,

            simSerial: '',
            simSerialProductID: '',
            simSerialType: 0,
            customerName: '',
            gender: undefined,
            customerBirthday: '',
            customerNationalityID: 241,
            userType: 0,
            identificationType: 1,
            customerIDCard: '',
            idCardIssueDate: '',
            visaExpireDate: '',
            visaNumber: '',
            idCardIssuePlace: '',
            customerPhone: '',
            customerAddress: '',
            provinceID: 0,
            districtID: 0,
            wardID: 0,
            hamlet: '',
            houseNumber: '',
            street: '',
            wardName: '',
            districtName: '',
            provinceName: '',
            // customerType: 0,

            dataIssuePlace: [],
            uriImages: [],
            indexImage: 0,
            isUploadSucess: false,
            signBase64: '',

            validateSimSerial: false,
            errorGetSimSerial: '',

            dataProvince: [],
            dataDistrict: [],
            dataWard: [],

            radioGender: [
                { title: translate('activeSimManager.male'), selected: false, value: 1 },
                { title: translate('activeSimManager.female'), selected: false, value: 0 }
            ],

            radioTypeSim: [
                { title: translate('activeSimManager.physical_sim'), selected: false },
                { title: translate('activeSimManager.esim'), selected: true }
            ],

            msgValidate: '',
            dataCustomerViettel: [],
            isVisibleCustomerInfo: false,
            isInfoByViettel: false,
            infoCustomerViettel: {},
            typeIdentification: 'CMND',

            isDetectImage: true,
            ishow: false,
            isShowUIIndicator: false,
            PGID: 0,
            PackagesTypeID: 0,
            IsEdit: false,
            IsCheckType: false,
            IschekcPicMT: false,
            IschekcPicMS: false,
            IschekcPicCD: false,
            IschekcPicCK: false,
            lstImageOld: [],
            lstUser: [],
            UserID: '',
            UserName: '',
            keyword: '',
            IsEditItel: false,
            isDisable: true
        };
        this.state = helper.deepCopy(this.defaultState);
        this.typeImageDetect = 0;
        this.userTypeList = [
            {
                value: translate('activeSimManager.user_type_1'),
                id: 1
            },
            {
                value: translate('activeSimManager.user_type_2'),
                id: 2
            },
            {
                value: translate('activeSimManager.user_type_3'),
                id: 3
            },
            {
                value: translate('activeSimManager.user_type_4'),
                id: 4
            },
            {
                value: translate('activeSimManager.user_type_5'),
                id: 5
            }
        ];
        this.recordTypeList = [
            {
                value: translate('activeSimManager.id_card_1'),
                id: 1
            },
            {
                value: translate('activeSimManager.id_card_2'),
                id: 2
            },
            {
                value: translate('activeSimManager.passport'),
                id: 3
            }
        ]

    }

    componentDidMount() {
        this.props.actionGetSim.getNationality();
    }

    componentDidUpdate(preProps, preState) {
        let { dataSIMProcessInfo } = this.props.simProcessState;
        let Imagelist = [];
        let ImageLisOLD = [];
        if (
            preProps.simProcessState.dataSIMProcessInfo !==
            dataSIMProcessInfo &&
            !helper.IsEmptyObject(dataSIMProcessInfo)
        ) {
            if (dataSIMProcessInfo.cus_SIMRequestAttachmentBOList != null) {
                for (
                    let i = 0;
                    i <
                    dataSIMProcessInfo.cus_SIMRequestAttachmentBOList.length;
                    i++
                ) {
                    let Uri =
                        dataSIMProcessInfo.cus_SIMRequestAttachmentBOList[i]
                            .cus_ByteArrayImage;
                    Imagelist.push(Uri);
                    let obj = {
                        ShortName:
                            dataSIMProcessInfo.cus_SIMRequestAttachmentBOList[i]
                                .ShortName,
                        Uri: dataSIMProcessInfo.cus_SIMRequestAttachmentBOList[
                            i
                        ].cus_ByteArrayImage
                    };

                    ImageLisOLD.push(obj);
                }
            }

            this.setState({
                simSerial: dataSIMProcessInfo.SIMProcessRequestDetail.SIMSerial,
                customerName: dataSIMProcessInfo.CustomerName || '',
                gender: dataSIMProcessInfo.Gender,
                customerBirthday: dataSIMProcessInfo.CustomerBirthday || '',
                customerNationalityID:
                    dataSIMProcessInfo.CustomerNationalityID || 241,
                userType: dataSIMProcessInfo.UserType || 1,
                identificationType: dataSIMProcessInfo.IdentificationType || 1,
                customerIDCard: dataSIMProcessInfo.CustomerIDCard || '',
                idCardIssueDate: dataSIMProcessInfo.IDCardIssueDate || '',
                visaExpireDate: dataSIMProcessInfo.VisaExpireDate || '',
                visaNumber: dataSIMProcessInfo.VisaNumber || '',
                idCardIssuePlace: dataSIMProcessInfo.IDCardIssuePlace || '',
                customerPhone: dataSIMProcessInfo.CustomerPhone || '',
                customerAddress: dataSIMProcessInfo.CustomerAddress,
                provinceID: dataSIMProcessInfo.ProvinceID || 0,
                districtID: dataSIMProcessInfo.DistrictID || 0,
                wardID: dataSIMProcessInfo.WardID || 0,
                hamlet: dataSIMProcessInfo.Hamlet || '',
                houseuNmber: dataSIMProcessInfo.HouseNumber || '',
                street: dataSIMProcessInfo.Street || '',
                wardName: dataSIMProcessInfo.WardName || '',
                districtName: dataSIMProcessInfo.DistrictName || '',
                provinceName: dataSIMProcessInfo.ProvinceName || '',
                ishow: !dataSIMProcessInfo.IsCheckConectType,
                PGID: dataSIMProcessInfo.PGID,
                PackagesTypeID:
                    dataSIMProcessInfo.SIMProcessRequestDetail.PackagesTypeID,
                // customerType: dataSIMProcessInfo.CustomerType || 0,
                radioGender: [
                    {
                        title: translate('activeSimManager.male'),
                        selected: dataSIMProcessInfo.Gender == 1,
                        value: 1
                    },
                    {
                        title: translate('activeSimManager.female'),
                        selected: dataSIMProcessInfo.Gender == 0,
                        value: 0
                    }
                ],
                simSerialType: dataSIMProcessInfo.SIMSerialType,
                IsEdit: dataSIMProcessInfo.IsEdit,
                validateSimSerial: dataSIMProcessInfo.IsEdit ? true : false,
                simSerialProductID:
                    dataSIMProcessInfo.SIMProcessRequestDetail
                        .SIMSerialProductID,
                isDetectImage:
                    dataSIMProcessInfo.cus_SIMRequestAttachmentBOList != null &&
                        dataSIMProcessInfo.cus_SIMRequestAttachmentBOList.length > 0
                        ? false
                        : true,
                uriImages: Imagelist,
                lstImageOld: ImageLisOLD,
                IsEditItel: dataSIMProcessInfo.IsEditItel,
                signBase64: Imagelist.length > 0 ? Imagelist[3] : ''
            });

            this.getProvince();
            this.getDataIssueplace(
                dataSIMProcessInfo.IdentificationType || 1,
                dataSIMProcessInfo.CustomerNationalityID || 241
            );
            this.getTypeIdentification(
                dataSIMProcessInfo.IdentificationType || 1
            );
            if (dataSIMProcessInfo.ProvinceID) {
                this.getDistrict(dataSIMProcessInfo.ProvinceID);
                if (dataSIMProcessInfo.DistrictID) {
                    this.getWard(
                        dataSIMProcessInfo.ProvinceID,
                        dataSIMProcessInfo.DistrictID
                    );
                }
            }
            this.selectTypeSim(
                this.state.radioTypeSim,
                dataSIMProcessInfo.SIMSerialType
            );
        }
    }

    getProvince = () => {
        this.props.actionGetSim
            .getProvinceMap()
            .then((res) => {
                this.setState({ dataProvince: res });
            })
            .catch((err) => {
                console.log('getProvince', err);
            });
    };

    getDistrict = (provinceID) => {
        this.props.actionGetSim
            .getDistrictMap(provinceID)
            .then((res) => {
                this.setState({ dataDistrict: res });
            })
            .catch((err) => {
                console.log('getDistrict', err);
            });
    };

    getWard = (provinceID, districtID) => {
        this.props.actionGetSim
            .getWardMap(provinceID, districtID)
            .then((res) => {
                this.setState({ dataWard: res });
            })
            .catch((err) => {
                console.log('getWard', err);
            });
    };

    closeCamera = () => {
        this.setState({ isVisibleCamera: false });
    };

    closeSignature = () => {
        this.setState({ isVisibleSign: false });
    };

    getDataIssueplace = (idType = 1, nationalityID = 241) => {
        this.props.actionGetSim
            .getIssueplace(idType, nationalityID, this.state.customerIDCard)
            .then((res) => {
                this.setState({
                    dataIssuePlace: res,
                    idCardIssuePlace:
                        idType != 1
                            ? res[0].idCardIssuePlaceId
                            : this.state.idCardIssuePlace
                });
            })
            .catch((err) => {
                console.log(err);
            });
    };

    resizeImage = (e, imageType = 'JPEG') => {
        return new Promise((resolve, reject) => {
            let max = 640;
            let w = e.width;
            let h = e.height;

            if (e.width > e.height) {
                // orientation = 'landscape';
                if (w > max) {
                    w = max;
                    h = (max * e.height) / e.width;
                }
            } else if (e.width < e.height) {
                //orientation = 'portrait';
                if (h > max) {
                    h = max;
                    w = (max * e.width) / e.height;
                }
            } else {
                //width == height
                //orientation = 'event';
                if (w > max) {
                    w = max;
                    h = (max * e.height) / e.width;
                }
            }
            ImageResizer.createResizedImage(e.uri, w, h, imageType, 100, 0)
                .then(({ path, uri, size, name }) => {
                    resolve({ path, uri, size, name });
                })
                .catch((error) => {
                    reject(error);
                });
        });
    };

    takeSignature = (signature) => {
        this.setState({
            isVisibleSign: false,
            signBase64: signature
        });
    };

    getUserInfo = (username, keyword) => {
        this.props.actionGetSim
            .searchUser(username, keyword)
            .then((res) => {
                if (username != '' || keyword != '') {
                    if (res.length > 0) {
                        this.setState({
                            UserID: res[0].username + '-' + res[0].fullname,
                            UserName: res[0].username
                        });
                    }
                }
            })
            .catch((err) => {
                console.log('getProvince', err);
            });
    };

    setIsScanSerial = () => {
        this.setState({ isScanSerial: true });
    }

    setNotIsScanSerial = () => {
        this.setState({ isScanSerial: false });
    }

    setSimSerialText = (text) => {
        this.setState({
            simSerial: text,
            validateSimSerial: false,
            errorGetSimSerial: ''
        });
    }

    setSimSerialBarcode = (barcode) => {
        this.setState({
            simSerial: barcode,
            validateSimSerial: false,
            errorGetSimSerial: '',
            isScanSerial: false
        })
    }

    setNoErrorGetSerialEsim = () => {
        this.setState({ errorGetSimSerial: '' }, this.getSerialEsim);
    }

    setPackagesTypeID = (item) => {
        this.setState({ PackagesTypeID: item.PackagesTypeID });
    }
    setCustomerName = (text) => {
        this.setState({ customerName: text });
    }

    setPGID = (item) => {
        this.setState({ PGID: item.PGID });
    }

    render() {
        let {
            isVisible,
            hideModal,
            simProcessState,
            nationalityState,
            dataSimRequest,
            userInfo
        } = this.props;
        const { isDisable } = this.state;
        let { dataSIMProcessInfo } = this.props.simProcessState;
        const isCheckSIMType = dataSIMProcessInfo?.cus_IsASim == true || dataSIMProcessInfo?.cus_IsMobi == true;
        const isAllowEditSP = dataSimRequest.IsAllowEditSP;
        console.log('datasim', dataSimRequest, isDisable);

        return (
            <KModal
                onRequestClose={() => {
                    return;
                }}
                isVisible={isVisible}
                style={{ margin: 0 }}
                deviceWidth={constants.width}
                deviceHeight={constants.height}
                backdropTransitionOutTiming={0}
                animationIn="slideInRight"
                animationOut="slideOutRight"
                useNativeDriver={true}
                hideModalContentWhileAnimating={true}>
                <Header
                    title={translate('activeSimManager.sim_process_request') + dataSimRequest.SIMProcessRequestID}
                    onBack={() => {
                        this.setState(helper.deepCopy(this.defaultState));
                        hideModal();
                    }}
                />
                <SafeAreaView
                    style={{
                        flex: 1,
                        justifyContent: 'center'
                    }}>
                    {this.state.ishow ? (
                        <View
                            style={{
                                flex: 1,
                                height: constants.height,
                                backgroundColor: 'white'
                            }}>
                            {this.state.isDetectImage ? (
                                <ScrollView
                                    style={{
                                        flex: 1
                                    }}
                                    contentContainerStyle={{
                                        paddingVertical: 10
                                    }}
                                    showsHorizontalScrollIndicator={false}
                                    showsVerticalScrollIndicator={false}>
                                    <View
                                        style={{
                                            flex: 1,
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            paddingVertical:
                                                constants.getSize(8),
                                            paddingHorizontal:
                                                constants.getSize(10),
                                            marginBottom: constants.getSize(10)
                                        }}>
                                        <RadioButton
                                            style={{
                                                flexDirection: 'row'
                                            }}
                                            containerStyle={{
                                                paddingLeft:
                                                    constants.getSize(10),
                                                alignItems: 'center'
                                            }}
                                            dataItems={[
                                                {
                                                    title: translate('activeSimManager.id_card_1_short'),
                                                    selected:
                                                        this.state
                                                            .identificationType ==
                                                        1
                                                },
                                                {
                                                    title: translate('activeSimManager.id_card_2_short'),
                                                    selected:
                                                        this.state
                                                            .identificationType ==
                                                        2
                                                },
                                                {
                                                    title: translate('activeSimManager.passport'),
                                                    selected:
                                                        this.state
                                                            .identificationType ==
                                                        3
                                                }
                                            ]}
                                            selectItem={(index) => {
                                                this.setState({
                                                    identificationType:
                                                        index + 1,
                                                    idCardIssuePlace: 0
                                                });
                                                this.getDataIssueplace(
                                                    index + 1,
                                                    this.state
                                                        .customerNationalityID
                                                );
                                                this.getTypeIdentification(
                                                    index + 1
                                                );
                                            }}
                                            mainComponent={(item) => {
                                                return (
                                                    <MyText
                                                        text={item.title}
                                                        style={{
                                                            color: item.selected
                                                                ? '#FF8900'
                                                                : '#333333',
                                                            marginLeft: 2
                                                        }}
                                                    />
                                                );
                                            }}
                                        />
                                    </View>

                                    <ImageProcess
                                        title={`${translate('activeSimManager.capture')} ${this.state.typeIdentification} ${translate('activeSimManager.front')}`}
                                        titleAgain={translate('activeSimManager.recapture')}
                                        onCamera={() => {
                                            this.setState({
                                                isVisibleCamera: true,
                                                indexImage: 0,
                                                IschekcPicMT: true
                                            });
                                        }}
                                        urlImage={this.state.uriImages[0]}
                                        isUploadSucess={
                                            this.state.isUploadSucess
                                        }
                                    />
                                    <ImageProcess
                                        title={`${translate('activeSimManager.capture')} ${this.state.typeIdentification} ${translate('activeSimManager.back')}`}
                                        titleAgain={translate('activeSimManager.recapture')}
                                        onCamera={() => {
                                            this.setState({
                                                isVisibleCamera: true,
                                                indexImage: 1,
                                                IschekcPicMS: true
                                            });
                                        }}
                                        urlImage={this.state.uriImages[1]}
                                        isUploadSucess={
                                            this.state.isUploadSucess
                                        }
                                    />
                                    <ImageProcess
                                        title={translate('activeSimManager.portrait')}
                                        titleAgain={translate('activeSimManager.recapture')}
                                        onCamera={() => {
                                            this.setState({
                                                isVisibleCamera: true,
                                                indexImage: 2,
                                                IschekcPicCD: true
                                            });
                                        }}
                                        urlImage={this.state.uriImages[2]}
                                        isUploadSucess={
                                            this.state.isUploadSucess
                                        }
                                    />
                                    <ImageProcess
                                        title={translate('activeSimManager.sign')}
                                        titleAgain={translate('activeSimManager.resign')}
                                        onCamera={() => {
                                            this.setState({
                                                isVisibleSign: true,
                                                indexImage: 3,
                                                IschekcPicCK: true
                                            });
                                        }}
                                        // urlImage={this.state.uriImages[3]}
                                        urlImage={this.state.signBase64}
                                        isUploadSucess={
                                            this.state.isUploadSucess
                                        }
                                    />

                                    <View
                                        style={{
                                            flex: 1,
                                            justifyContent: 'center',
                                            alignItems: 'center'
                                        }}>
                                        <Button
                                            text={translate('common.btn_continue')}
                                            styleContainer={{
                                                width: constants.getSize(180),
                                                height: constants.getSize(50),
                                                backgroundColor: '#288AD6',
                                                borderRadius:
                                                    constants.getSize(4),
                                                marginVertical: 10,
                                                opacity: this.state
                                                    .isUploadSucess
                                                    ? 0.5
                                                    : 1
                                            }}
                                            styleText={{
                                                color: '#FFFFFF',
                                                fontSize: 14
                                            }}
                                            onPress={() => {
                                                this.setState(
                                                    { isDetectImage: false },
                                                    () => {
                                                        if (
                                                            this.state
                                                                .identificationType ==
                                                            1 &&
                                                            dataSimRequest.BrandID ==
                                                            126 &&
                                                            this.state
                                                                .customerIDCard
                                                                .length == 9
                                                        ) {
                                                            this.props.actionGetSim
                                                                .getCustomerInfoViettel(
                                                                    this.state
                                                                        .customerIDCard
                                                                )
                                                                .then((res) => {
                                                                    this.setState(
                                                                        {
                                                                            isVisibleCustomerInfo: true,
                                                                            dataCustomerViettel:
                                                                                res
                                                                        }
                                                                    );
                                                                })
                                                                .catch(
                                                                    (error) => {
                                                                        console.log(
                                                                            error
                                                                        );
                                                                    }
                                                                );
                                                        }
                                                    }
                                                );
                                            }}
                                        />
                                    </View>
                                </ScrollView>
                            ) : (
                                <KeyboardAwareScrollView
                                    style={{
                                        flex: 1
                                    }}
                                    contentContainerStyle={{
                                        paddingVertical: 10
                                    }}
                                    enableResetScrollToCoords={false}
                                    keyboardShouldPersistTaps="always"
                                    keyboardDismissMode="on-drag"
                                    bounces={false}
                                    overScrollMode="always"
                                    showsHorizontalScrollIndicator={false}
                                    showsVerticalScrollIndicator={false}>
                                    <BaseContainer
                                        isLoading={simProcessState.isFetching}
                                        isError={simProcessState.isError}
                                        isEmpty={simProcessState.isEmpty}
                                        textLoadingError={
                                            simProcessState.errorDescription
                                        }
                                        onPressTryAgains={() => {
                                            this.props.actionGetSim.getSIMProcessInfo(
                                                {
                                                    brandID:
                                                        dataSimRequest.BrandID,
                                                    SIMProcessID:
                                                        dataSimRequest.SIMProcessRequestID,
                                                    isGetDetail: true
                                                }
                                            );
                                        }}
                                        content={
                                            !helper.IsEmptyObject(
                                                dataSIMProcessInfo
                                            ) && (
                                                <View
                                                    style={{
                                                        flex: 1
                                                    }}>
                                                    <View
                                                        style={{
                                                            flex: 1,
                                                            paddingHorizontal: 10,
                                                            marginBottom: 10
                                                        }}>

                                                        <HeaderTextField
                                                            title={translate('activeSimManager.request_store')}
                                                            information={dataSIMProcessInfo.RequestStoreID +
                                                                ' - ' +
                                                                dataSIMProcessInfo.RequestStoreName}
                                                            informationColor={'#14A19C'}
                                                        />

                                                        <HeaderTextField
                                                            title={translate('activeSimManager.process_store')}
                                                            information={dataSIMProcessInfo.ProcessStoreID +
                                                                ' - ' +
                                                                dataSIMProcessInfo.ProcessStoreName}
                                                            informationColor={'#14A19C'}
                                                        />

                                                        <HeaderTextField
                                                            title={translate('activeSimManager.sale_agent')}
                                                            information={dataSIMProcessInfo.StaffUser +
                                                                ' - ' +
                                                                dataSIMProcessInfo.StaffUserFullName}
                                                            informationColor={'#77B50B'}
                                                        />

                                                        {this.state
                                                            .IsEditItel ? (
                                                            <View
                                                                style={{
                                                                    flex: 1,
                                                                    flexDirection:
                                                                        'row'
                                                                }}>
                                                                <SearchInput
                                                                    width={
                                                                        constants.width -
                                                                        constants.getSize(
                                                                            20
                                                                        )
                                                                    }
                                                                    height={40}
                                                                    placeholder={
                                                                        translate('activeSimManager.search_input_sale_agent')
                                                                    }
                                                                    value={
                                                                        this
                                                                            .state
                                                                            .UserID
                                                                    }
                                                                    onChangeText={(
                                                                        text
                                                                    ) => {
                                                                        let validateSim =
                                                                            new RegExp(
                                                                                /^[0-9]*$/
                                                                            );
                                                                        if (
                                                                            text.length <
                                                                            this
                                                                                .state
                                                                                .UserID
                                                                        ) {
                                                                            this.setState(
                                                                                {
                                                                                    UserID: text,
                                                                                    keyword:
                                                                                        text
                                                                                }
                                                                            );
                                                                        } else {
                                                                            if (
                                                                                validateSim.test(
                                                                                    text
                                                                                )
                                                                            ) {
                                                                                this.setState(
                                                                                    {
                                                                                        UserID: text
                                                                                    }
                                                                                );
                                                                            } else {
                                                                                this.setState(
                                                                                    {
                                                                                        keyword:
                                                                                            text
                                                                                    }
                                                                                );
                                                                            }
                                                                        }
                                                                    }}
                                                                    returnKeyType="search"
                                                                    keyboardType={
                                                                        'numeric'
                                                                    }
                                                                    onSubmitEditing={this.getUserInfo(
                                                                        this
                                                                            .state
                                                                            .UserID,
                                                                        this
                                                                            .state
                                                                            .keyword
                                                                    )}
                                                                    editable={
                                                                        true
                                                                    }
                                                                />
                                                            </View>
                                                        ) : (
                                                            <View></View>
                                                        )}

                                                        <HeaderTextField
                                                            title={translate('activeSimManager.product')}
                                                            information={helper.isString(
                                                                dataSIMProcessInfo
                                                                    .SIMProcessRequestDetail
                                                                    .ProductID
                                                            )
                                                                ? dataSIMProcessInfo.SIMProcessRequestDetail.ProductID.trim() +
                                                                ' - ' +
                                                                dataSIMProcessInfo
                                                                    .SIMProcessRequestDetail
                                                                    .ProductName
                                                                : ''}
                                                            informationColor={'#14A19C'}
                                                        />


                                                        <MyText
                                                            text={'IMEI: '}
                                                            style={{
                                                                color: '#333333',
                                                                marginBottom: 10
                                                            }}
                                                            children={
                                                                <MyText
                                                                    text={
                                                                        dataSIMProcessInfo
                                                                            .SIMProcessRequestDetail
                                                                            .IMEI
                                                                    }
                                                                    style={{
                                                                        color: '#333333'
                                                                    }}
                                                                />
                                                            }
                                                        />

                                                        {dataSIMProcessInfo.cus_IsViettel || dataSIMProcessInfo.cus_IsITel ? (
                                                            <View
                                                                style={{
                                                                    flex: 1,
                                                                    flexDirection:
                                                                        'row',
                                                                    alignItems:
                                                                        'center',
                                                                    justifyContent:
                                                                        'space-between',
                                                                    paddingHorizontal:
                                                                        constants.getSize(
                                                                            10
                                                                        ),
                                                                    marginBottom:
                                                                        constants.getSize(
                                                                            10
                                                                        )
                                                                }}>
                                                                <RadioButton
                                                                    style={{
                                                                        flexDirection:
                                                                            'row'
                                                                    }}
                                                                    containerStyle={{
                                                                        paddingLeft:
                                                                            constants.getSize(
                                                                                10
                                                                            ),
                                                                        alignItems:
                                                                            'center'
                                                                    }}
                                                                    dataItems={this.state.radioTypeSim}
                                                                    selectItem={(
                                                                        index
                                                                    ) => {
                                                                        this.selectTypeSim(
                                                                            this
                                                                                .state
                                                                                .radioTypeSim,
                                                                            index
                                                                        );
                                                                    }}
                                                                    mainComponent={(
                                                                        item
                                                                    ) => {
                                                                        return (
                                                                            <MyText
                                                                                text={
                                                                                    item.title
                                                                                }
                                                                                style={{
                                                                                    color: item.selected
                                                                                        ? '#FF8900'
                                                                                        : '#333333',
                                                                                    marginLeft: 2
                                                                                }}
                                                                            />
                                                                        );
                                                                    }}
                                                                />
                                                            </View>
                                                        ) : (
                                                            <View></View>
                                                        )}

                                                        {this.state
                                                            .simSerialType ==
                                                            0 ? (
                                                            <View
                                                                style={{
                                                                    flex: 1,
                                                                    flexDirection:
                                                                        'row'
                                                                }}>
                                                                <SearchInput
                                                                    width={
                                                                        constants.width -
                                                                        constants.getSize(
                                                                            100
                                                                        )
                                                                    }
                                                                    height={40}
                                                                    placeholder={
                                                                        translate('activeSimManager.search_input_sim_serial')
                                                                    }
                                                                    rightComponent={
                                                                        !this
                                                                            .state
                                                                            .isUploadSucess && [
                                                                            {
                                                                                source: {
                                                                                    uri: 'ic_barcode_input'
                                                                                },
                                                                                onpress:
                                                                                    () => {
                                                                                        this.setState(
                                                                                            {
                                                                                                isScanSerial: true
                                                                                            }
                                                                                        );
                                                                                    }
                                                                            }
                                                                        ]
                                                                    }
                                                                    value={
                                                                        this
                                                                            .state
                                                                            .simSerial
                                                                    }
                                                                    onChangeText={(
                                                                        text
                                                                    ) => {
                                                                        let validateSim =
                                                                            new RegExp(
                                                                                /^[0-9]*$/
                                                                            );
                                                                        if (
                                                                            validateSim.test(
                                                                                text
                                                                            ) ||
                                                                            text ==
                                                                            ''
                                                                        ) {
                                                                            this.setState(
                                                                                {
                                                                                    simSerial:
                                                                                        text,
                                                                                    validateSimSerial: false,
                                                                                    errorGetSimSerial:
                                                                                        ''
                                                                                }
                                                                            );
                                                                        }
                                                                    }}
                                                                    returnKeyType="search"
                                                                    keyboardType={
                                                                        'numeric'
                                                                    }
                                                                    onSubmitEditing={this.getSerialSim(
                                                                        this
                                                                            .state
                                                                            .simSerial
                                                                    )}
                                                                    editable={
                                                                        !this
                                                                            .state
                                                                            .isUploadSucess
                                                                    }
                                                                />

                                                                <TouchableOpacity
                                                                    style={{
                                                                        height: 40,
                                                                        width: 70,
                                                                        backgroundColor:
                                                                            '#288AD6',
                                                                        justifyContent:
                                                                            'center',
                                                                        alignItems:
                                                                            'center',
                                                                        marginLeft: 10,
                                                                        borderRadius: 4,
                                                                        opacity:
                                                                            this
                                                                                .state
                                                                                .isUploadSucess ||
                                                                                this
                                                                                    .state
                                                                                    .validateSimSerial
                                                                                ? 0.5
                                                                                : 1
                                                                    }}
                                                                    onPress={this.getSerialSim(
                                                                        this
                                                                            .state
                                                                            .simSerial
                                                                    )}
                                                                    disabled={
                                                                        this
                                                                            .state
                                                                            .isUploadSucess ||
                                                                        this
                                                                            .state
                                                                            .validateSimSerial
                                                                    }>
                                                                    <MyText
                                                                        text={
                                                                            translate('activeSimManager.enter')
                                                                        }
                                                                        style={{
                                                                            color: 'white'
                                                                        }}
                                                                    />
                                                                </TouchableOpacity>
                                                            </View>
                                                        ) : (
                                                            <View
                                                                style={{
                                                                    flex: 1,
                                                                    flexDirection:
                                                                        'row'
                                                                }}>
                                                                <SearchInput
                                                                    width={
                                                                        constants.width -
                                                                        constants.getSize(
                                                                            100
                                                                        )
                                                                    }
                                                                    height={40}
                                                                    value={
                                                                        this
                                                                            .state
                                                                            .simSerial
                                                                    }
                                                                    placeholder={
                                                                        translate('activeSimManager.get_sim_serial')
                                                                    }
                                                                    returnKeyType="search"
                                                                    editable={
                                                                        false
                                                                    }
                                                                />

                                                                <TouchableOpacity
                                                                    style={{
                                                                        height: 40,
                                                                        width: 70,
                                                                        backgroundColor:
                                                                            '#288AD6',
                                                                        justifyContent:
                                                                            'center',
                                                                        alignItems:
                                                                            'center',
                                                                        marginLeft: 10,
                                                                        borderRadius: 4,
                                                                        opacity:
                                                                            this
                                                                                .state
                                                                                .isUploadSucess ||
                                                                                this
                                                                                    .state
                                                                                    .validateSimSerial
                                                                                ? 0.5
                                                                                : 1
                                                                    }}
                                                                    onPress={() => {
                                                                        this.setState(
                                                                            {
                                                                                errorGetSimSerial:
                                                                                    ''
                                                                            },
                                                                            this
                                                                                .getSerialEsim
                                                                        );
                                                                    }}
                                                                    disabled={
                                                                        this
                                                                            .state
                                                                            .isUploadSucess ||
                                                                        this
                                                                            .state
                                                                            .validateSimSerial ||
                                                                        this
                                                                            .state
                                                                            .IsEdit
                                                                    }>
                                                                    <MyText
                                                                        text={
                                                                            translate('common.btn_notify_try_again')
                                                                        }
                                                                        style={{
                                                                            color: 'white'
                                                                        }}
                                                                    />
                                                                </TouchableOpacity>
                                                            </View>
                                                        )}

                                                        {!this.state
                                                            .validateSimSerial &&
                                                            !!this.state
                                                                .errorGetSimSerial && (
                                                                <MyText
                                                                    text={
                                                                        this
                                                                            .state
                                                                            .errorGetSimSerial
                                                                    }
                                                                    style={{
                                                                        color: 'red',
                                                                        marginTop: 5
                                                                    }}
                                                                />
                                                            )}

                                                        {helper.isArray(
                                                            dataSIMProcessInfo.lstPackagesType
                                                        ) && (
                                                                <View
                                                                    style={{
                                                                        paddingHorizontal: 10
                                                                    }}>
                                                                    <MyText
                                                                        text={
                                                                            translate('activeSimManager.package_2')
                                                                        }
                                                                        style={{
                                                                            color: '#333333',
                                                                            fontWeight:
                                                                                'bold',
                                                                            marginTop: 10
                                                                        }}
                                                                    // children={
                                                                    //     <MyText
                                                                    //         text={dataSIMProcessInfo.SIMProcessRequestDetail.PackagesTypeName}
                                                                    //         style={{
                                                                    //             color: "#333333"
                                                                    //         }}
                                                                    //     />
                                                                    // }
                                                                    />
                                                                    <PickerSearch
                                                                        label={"PackagesTypeName"}
                                                                        value={"PackagesTypeID"}
                                                                        data={dataSIMProcessInfo.lstPackagesType}
                                                                        valueSelected={this.state.PackagesTypeID}
                                                                        onChange={(item) => {
                                                                            this.setState({
                                                                                PackagesTypeID: item.PackagesTypeID
                                                                            });
                                                                        }}
                                                                        defaultLabel={translate('activeSimManager.select_package')}
                                                                        style={{
                                                                            flex: 1,
                                                                            flexDirection: "row",
                                                                            justifyContent: "center",
                                                                            alignItems: "center",
                                                                            marginVertical: 5,
                                                                            borderWidth: 1,
                                                                            borderRadius: 4,
                                                                            borderColor: '#CCCCCC',
                                                                            backgroundColor: 'white',
                                                                            minHeight: 40
                                                                        }}
                                                                    />
                                                                </View>
                                                            )}
                                                        {
                                                            <View style={{
                                                                flexDirection: 'row',
                                                                marginTop: 10
                                                            }}>
                                                                <FieldInput
                                                                    styleInput={{
                                                                        borderWidth: 1,
                                                                        borderRadius: 4,
                                                                        borderColor:
                                                                            '#CCCCCC',
                                                                        paddingHorizontal: 10,
                                                                        marginBottom: 10,
                                                                        backgroundColor:
                                                                            'white',
                                                                        paddingVertical: 8
                                                                    }}
                                                                    value={this.state.customerIDCard}
                                                                    placeholder={"Vui lòng nhập CMND/CCCD/HC của khách hàng"}
                                                                    onChangeText={(text) => {
                                                                        const regExpIDCard = new RegExp(/^\w{0,12}$/);
                                                                        const isValidate =
                                                                            regExpIDCard.test(text) || text == '';
                                                                        if (isValidate) {
                                                                            this.setState({
                                                                                customerIDCard: text.trim(),
                                                                                isInfoByViettel: false,
                                                                                infoCustomerViettel: {}
                                                                            });
                                                                        }
                                                                    }}
                                                                    editable={true}
                                                                    keyboardType="default"
                                                                    returnKeyType="done"
                                                                    blurOnSubmit
                                                                    width={constants.width - 145}
                                                                    height={40}
                                                                    clearText={() => this.setState({ customerIDCard: '' })}
                                                                />
                                                                <Button
                                                                    text={translate('shoppingCart.btn_check')}
                                                                    onPress={() => this.handleCheckDocument(this.state.customerIDCard, dataSimRequest.SIMProcessRequestID, this.state.simSerial)}
                                                                    styleContainer={{
                                                                        width: '30%',
                                                                        borderRadius: 4,
                                                                        backgroundColor: COLORS.txt147EFB,
                                                                        height: 44,
                                                                        marginLeft: 10
                                                                    }}
                                                                    styleText={{
                                                                        color: COLORS.txtFFFFFF,
                                                                        fontSize: 14,
                                                                        fontWeight: 'bold'
                                                                    }}
                                                                />
                                                            </View>
                                                        }
                                                    </View>

                                                    <View
                                                        style={{
                                                            justifyContent:
                                                                'center',
                                                            padding: 8,
                                                            backgroundColor:
                                                                '#2FB47C',
                                                            width: constants.width,
                                                            height: 40
                                                        }}>
                                                        <MyText
                                                            addSize={2}
                                                            text={
                                                                translate('activeSimManager.customer_information')
                                                            }
                                                            style={{
                                                                fontWeight:
                                                                    'bold',
                                                                color: 'white'
                                                            }}
                                                        />
                                                    </View>

                                                    <BaseContainer
                                                        isLoading={
                                                            nationalityState.isFetchingNationality
                                                        }
                                                        isError={
                                                            nationalityState.isError
                                                        }
                                                        isEmpty={
                                                            nationalityState.isEmpty
                                                        }
                                                        textLoadingError={
                                                            nationalityState.errorDescription
                                                        }
                                                        onPressTryAgains={() => {
                                                            this.props.actionGetSim.getNationality();
                                                        }}
                                                        content={
                                                            <View
                                                                style={{
                                                                    flex: 1
                                                                }}>
                                                                <View
                                                                    style={{
                                                                        flex: 1,
                                                                        paddingVertical: 10
                                                                    }}>

                                                                    <MyText
                                                                        text={translate('activeSimManager.nationality')}
                                                                        style={{
                                                                            fontWeight: 'bold',
                                                                            color: '#333333',
                                                                            marginHorizontal: 10
                                                                        }}
                                                                        children={
                                                                            <MyText
                                                                                text={'*'}
                                                                                style={{ color: 'red' }}
                                                                            />
                                                                        }
                                                                    />
                                                                    <PickerSearch
                                                                        label={"nationality"}
                                                                        value={"nationalityId"}
                                                                        data={nationalityState.dataNationality}
                                                                        valueSelected={this.state.customerNationalityID}
                                                                        onChange={(item) => {
                                                                            if (item.nationalityId != this.state.customerNationalityID) {
                                                                                if (item.nationalityId != 241) {
                                                                                    this.setState({
                                                                                        customerNationalityID: item.nationalityId,
                                                                                        identificationType: 3
                                                                                    });
                                                                                    this.getDataIssueplace(3, item.nationalityId);
                                                                                    this.getTypeIdentification(3);
                                                                                } else {
                                                                                    this.setState({
                                                                                        customerNationalityID: item.nationalityId
                                                                                    });
                                                                                    this.getDataIssueplace(this.state.identificationType, item.nationalityId);
                                                                                }
                                                                            }
                                                                        }}
                                                                        defaultLabel={translate('activeSimManager.select_country')}
                                                                        style={{
                                                                            flex: 1,
                                                                            flexDirection: "row",
                                                                            justifyContent: "center",
                                                                            alignItems: "center",
                                                                            marginVertical: 5,
                                                                            borderWidth: 1,
                                                                            borderRadius: 4,
                                                                            borderColor: '#CCCCCC',
                                                                            backgroundColor: 'white',
                                                                            minHeight: 40,
                                                                            marginHorizontal: 10
                                                                        }}
                                                                        disabled={this.state.isUploadSucess}
                                                                    />

                                                                    <MyText
                                                                        text={translate('activeSimManager.user')}
                                                                        style={{
                                                                            fontWeight: 'bold',
                                                                            color: '#333333',
                                                                            marginHorizontal: 10
                                                                        }}
                                                                        children={
                                                                            <MyText
                                                                                text={'*'}
                                                                                style={{ color: 'red' }}
                                                                            />
                                                                        }
                                                                    />
                                                                    <Picker
                                                                        label={"value"}
                                                                        value={"id"}
                                                                        data={this.userTypeList}
                                                                        valueSelected={this.state.userType}
                                                                        onChange={(item) => {
                                                                            this.setState({ userType: item.id });
                                                                        }}
                                                                        defaultLabel={translate('activeSimManager.select_user')}
                                                                        style={{
                                                                            flex: 1,
                                                                            flexDirection: "row",
                                                                            justifyContent: "center",
                                                                            alignItems: "center",
                                                                            marginVertical: 5,
                                                                            borderWidth: 1,
                                                                            borderRadius: 4,
                                                                            borderColor: '#CCCCCC',
                                                                            backgroundColor: 'white',
                                                                            minHeight: 40,
                                                                            marginHorizontal: 10
                                                                        }}
                                                                        disabled={this.state.isUploadSucess}
                                                                    />

                                                                    <MyText
                                                                        text={translate('activeSimManager.record_type')}
                                                                        style={{
                                                                            fontWeight: 'bold',
                                                                            color: '#333333',
                                                                            marginHorizontal: 10
                                                                        }}
                                                                        children={
                                                                            <MyText
                                                                                text={'*'}
                                                                                style={{ color: 'red' }}
                                                                            />
                                                                        }
                                                                    />
                                                                    <Picker
                                                                        label={"value"}
                                                                        value={"id"}
                                                                        data={this.recordTypeList}
                                                                        valueSelected={this.state.identificationType}
                                                                        onChange={(item) => {
                                                                            if (item.value != this.state.identificationType) {
                                                                                switch (item.id) {
                                                                                    case 1:
                                                                                        this.setState({
                                                                                            identificationType: 1,
                                                                                            idCardIssuePlace: 0
                                                                                        });
                                                                                        break;
                                                                                    case 2:
                                                                                        this.setState({
                                                                                            identificationType: 2,
                                                                                            idCardIssuePlace: 0
                                                                                        });
                                                                                        break;
                                                                                    default:
                                                                                        this.setState({
                                                                                            identificationType: 3,
                                                                                            idCardIssuePlace: 0
                                                                                        });
                                                                                        break;
                                                                                }
                                                                                this.getDataIssueplace(item.id, this.state.customerNationalityID);
                                                                                this.getTypeIdentification(item.id);
                                                                            }
                                                                        }}
                                                                        defaultLabel={translate('activeSimManager.select_record_type')}
                                                                        style={{
                                                                            flex: 1,
                                                                            flexDirection: "row",
                                                                            justifyContent: "center",
                                                                            alignItems: "center",
                                                                            marginVertical: 5,
                                                                            borderWidth: 1,
                                                                            borderRadius: 4,
                                                                            borderColor: '#CCCCCC',
                                                                            backgroundColor: 'white',
                                                                            minHeight: 40,
                                                                            marginHorizontal: 10
                                                                        }}
                                                                        disabled={this.state.isUploadSucess || this.state.customerNationalityID != 241}
                                                                    />

                                                                    <MyTextInput
                                                                        title={translate('activeSimManager.id_number')}
                                                                        required={true}
                                                                        value={this.state.customerIDCard}
                                                                        onChangeText={(text) => {
                                                                            this.setState({
                                                                                customerIDCard: text.trim(),
                                                                                isInfoByViettel: false,
                                                                                infoCustomerViettel: {}
                                                                            });
                                                                        }}
                                                                        onBlur={() => {
                                                                            if (this.state.identificationType == 1 &&
                                                                                dataSimRequest.BrandID == 126 &&
                                                                                this.state.customerIDCard.length == 9
                                                                            ) {
                                                                                this.props.actionGetSim
                                                                                    .getCustomerInfoViettel(
                                                                                        this.state.customerIDCard
                                                                                    )
                                                                                    .then((res) => {
                                                                                        this.setState({
                                                                                            isVisibleCustomerInfo: true,
                                                                                            dataCustomerViettel: res
                                                                                        });
                                                                                    }).catch((error) => {
                                                                                        console.log(error);
                                                                                    });
                                                                            }
                                                                            if (this.state.identificationType == 1 &&
                                                                                this.state.customerNationalityID == 241
                                                                            ) {
                                                                                this.getDataIssueplace(1, 241);
                                                                            }
                                                                        }}
                                                                        editable={!this.state.isUploadSucess}
                                                                        keyboardType={this.state.identificationType != 3
                                                                            ? 'numeric'
                                                                            : 'default'
                                                                        }
                                                                        returnKeyType={'done'}
                                                                        clearText={() => {
                                                                            this.setState({
                                                                                customerIDCard: '',
                                                                                isInfoByViettel: false,
                                                                                infoCustomerViettel: {}
                                                                            });
                                                                        }}
                                                                    />

                                                                    <SimDatePicker
                                                                        title={translate('activeSimManager.date')}
                                                                        text={this
                                                                            .state
                                                                            .idCardIssueDate
                                                                            ? dateHelper.formatStrDateDDMMYYYY(
                                                                                this
                                                                                    .state
                                                                                    .idCardIssueDate
                                                                            )
                                                                            : translate('activeSimManager.select_date')}
                                                                        color={this
                                                                            .state
                                                                            .isUploadSucess ||
                                                                            (this
                                                                                .state
                                                                                .isInfoByViettel &&
                                                                                !!this
                                                                                    .state
                                                                                    .infoCustomerViettel
                                                                                    .IDCardIssueDate)
                                                                            ? '#8e8e93'
                                                                            : '#288AD6'}
                                                                        datePicker={<DatePicker
                                                                            date={
                                                                                this
                                                                                    .state
                                                                                    .idCardIssueDate
                                                                            }
                                                                            maxDate={new Date()}
                                                                            mode="date"
                                                                            format="YYYY-MM-DD"
                                                                            confirmBtnText="Confirm"
                                                                            cancelBtnText="Cancel"
                                                                            onDateChange={(
                                                                                date
                                                                            ) => {
                                                                                this.setState(
                                                                                    {
                                                                                        idCardIssueDate:
                                                                                            date +
                                                                                            'T00:00:00'
                                                                                    }
                                                                                );
                                                                            }}
                                                                            disabled={
                                                                                this
                                                                                    .state
                                                                                    .isUploadSucess ||
                                                                                (this
                                                                                    .state
                                                                                    .isInfoByViettel &&
                                                                                    !!this
                                                                                        .state
                                                                                        .infoCustomerViettel
                                                                                        .IDCardIssueDate)
                                                                            }
                                                                        />}
                                                                    />

                                                                    <MyText
                                                                        text={translate('activeSimManager.place')}
                                                                        style={{
                                                                            fontWeight: 'bold',
                                                                            color: '#333333',
                                                                            marginHorizontal: 10
                                                                        }}
                                                                        children={
                                                                            <MyText
                                                                                text={'*'}
                                                                                style={{ color: 'red' }}
                                                                            />
                                                                        }
                                                                    />
                                                                    <PickerSearch
                                                                        label={"idCardIssuePlaceName"}
                                                                        value={"idCardIssuePlaceId"}
                                                                        data={this.state.dataIssuePlace}
                                                                        valueSelected={this.state.idCardIssuePlace}
                                                                        onChange={(item) => {
                                                                            this.setState({
                                                                                idCardIssuePlace: item.idCardIssuePlaceId
                                                                            });
                                                                        }}
                                                                        defaultLabel={translate('activeSimManager.select_place')}
                                                                        style={{
                                                                            flex: 1,
                                                                            flexDirection: "row",
                                                                            justifyContent: "center",
                                                                            alignItems: "center",
                                                                            marginVertical: 5,
                                                                            borderWidth: 1,
                                                                            borderRadius: 4,
                                                                            borderColor: '#CCCCCC',
                                                                            backgroundColor: 'white',
                                                                            minHeight: 40,
                                                                            marginHorizontal: 10
                                                                        }}
                                                                        disabled={this.state.isUploadSucess ||
                                                                            this.state.identificationType == 0 ||
                                                                            (this.state.isInfoByViettel && this.state.infoCustomerViettel.IDCardIssuePlace != 0)
                                                                        }
                                                                    />

                                                                    {this.state
                                                                        .identificationType !=
                                                                        1 && (
                                                                            <SimDatePicker
                                                                                title={translate('activeSimManager.expired_day')}
                                                                                text={this
                                                                                    .state
                                                                                    .visaExpireDate
                                                                                    ? dateHelper.formatStrDateDDMMYYYY(
                                                                                        this
                                                                                            .state
                                                                                            .visaExpireDate
                                                                                    )
                                                                                    : translate('activeSimManager.select_date')}
                                                                                color={'#288AD6'}
                                                                                datePicker={<DatePicker
                                                                                    date={
                                                                                        this
                                                                                            .state
                                                                                            .visaExpireDate
                                                                                    }
                                                                                    minDate={new Date()}
                                                                                    mode="date"
                                                                                    format="YYYY-MM-DD"
                                                                                    confirmBtnText="Confirm"
                                                                                    cancelBtnText="Cancel"
                                                                                    onDateChange={(
                                                                                        date
                                                                                    ) => {
                                                                                        this.setState(
                                                                                            {
                                                                                                visaExpireDate:
                                                                                                    date +
                                                                                                    'T00:00:00'
                                                                                            }
                                                                                        );
                                                                                    }}
                                                                                    disabled={
                                                                                        this
                                                                                            .state
                                                                                            .isUploadSucess
                                                                                    }
                                                                                />}
                                                                            />
                                                                        )}

                                                                    {this.state
                                                                        .identificationType ==
                                                                        3 && (<View>

                                                                            <MyTextInput
                                                                                title={this.state.customerNationalityID != 241
                                                                                    ? translate('activeSimManager.visa_number')
                                                                                    : translate('activeSimManager.id_number_in_id_card')}
                                                                                required={true}
                                                                                value={this.state.visaNumber}
                                                                                onChangeText={(text) => {
                                                                                    this.setState({
                                                                                        visaNumber: text
                                                                                    });
                                                                                }}
                                                                                editable={!this.state.isUploadSucess}
                                                                                clearText={() => {
                                                                                    this.setState({
                                                                                        visaNumber: ''
                                                                                    });
                                                                                }}
                                                                            />

                                                                        </View>
                                                                        )}
                                                                    <View style={{ flex: 1 }}>
                                                                        <MyText
                                                                            text={translate('activeSimManager.province')}
                                                                            style={{
                                                                                fontWeight: 'bold',
                                                                                color: '#333333',
                                                                                marginHorizontal: 10
                                                                            }}
                                                                            children={
                                                                                <MyText
                                                                                    text={'*'}
                                                                                    style={{ color: 'red' }}
                                                                                />
                                                                            }
                                                                        />
                                                                        <PickerSearch
                                                                            label={"provinceName"}
                                                                            value={"provinceID"}
                                                                            data={this.state.dataProvince}
                                                                            valueSelected={this.state.provinceID}
                                                                            onChange={(item) => {
                                                                                this.setState({
                                                                                    provinceID: item.provinceID,
                                                                                    provinceName: item.provinceName,
                                                                                    districtID: 0,
                                                                                    districtName: '',
                                                                                    wardID: 0,
                                                                                    wardName: ''
                                                                                },
                                                                                    this.getCustomerAddress
                                                                                );
                                                                                this.getDistrict(item.provinceID);
                                                                            }}
                                                                            defaultLabel={translate('activeSimManager.select_province')}
                                                                            style={{
                                                                                flex: 1,
                                                                                flexDirection: "row",
                                                                                justifyContent: "center",
                                                                                alignItems: "center",
                                                                                marginVertical: 5,
                                                                                borderWidth: 1,
                                                                                borderRadius: 4,
                                                                                borderColor: '#CCCCCC',
                                                                                backgroundColor: 'white',
                                                                                minHeight: 40,
                                                                                marginHorizontal: 10
                                                                            }}
                                                                            disabled={this.state.isUploadSucess ||
                                                                                (this.state.isInfoByViettel &&
                                                                                    this.state.infoCustomerViettel.ProvinceID != 0)
                                                                            }
                                                                        />
                                                                    </View>
                                                                    <MyText
                                                                        text={translate('activeSimManager.district')}
                                                                        style={{
                                                                            fontWeight: 'bold',
                                                                            color: '#333333',
                                                                            marginHorizontal: 10
                                                                        }}
                                                                        children={
                                                                            <MyText
                                                                                text={'*'}
                                                                                style={{ color: 'red' }}
                                                                            />
                                                                        }
                                                                    />
                                                                    <PickerSearch
                                                                        label={"districtName"}
                                                                        value={"districtID"}
                                                                        data={this.state.dataDistrict}
                                                                        valueSelected={this.state.districtID}
                                                                        onChange={(item) => {
                                                                            this.setState({
                                                                                districtID: item.districtID,
                                                                                districtName: item.districtName,
                                                                                wardID: 0,
                                                                                wardName: ''
                                                                            },
                                                                                this.getCustomerAddress
                                                                            );
                                                                            this.getWard(this.state.provinceID, item.districtID);
                                                                        }}
                                                                        defaultLabel={translate('activeSimManager.select_district')}
                                                                        style={{
                                                                            flex: 1,
                                                                            flexDirection: "row",
                                                                            justifyContent: "center",
                                                                            alignItems: "center",
                                                                            marginVertical: 5,
                                                                            borderWidth: 1,
                                                                            borderRadius: 4,
                                                                            borderColor: '#CCCCCC',
                                                                            backgroundColor: 'white',
                                                                            minHeight: 40,
                                                                            marginHorizontal: 10
                                                                        }}
                                                                        disabled={this.state.isUploadSucess ||
                                                                            this.state.provinceID == 0 ||
                                                                            (this.state.isInfoByViettel && this.state.infoCustomerViettel.DistrictID != 0)
                                                                        }
                                                                    />

                                                                    <MyText
                                                                        text={translate('activeSimManager.ward')}
                                                                        style={{
                                                                            fontWeight: 'bold',
                                                                            color: '#333333',
                                                                            marginHorizontal: 10
                                                                        }}
                                                                        children={
                                                                            <MyText
                                                                                text={'*'}
                                                                                style={{ color: 'red' }}
                                                                            />
                                                                        }
                                                                    />
                                                                    <PickerSearch
                                                                        label={"wardName"}
                                                                        value={"wardID"}
                                                                        data={this.state.dataWard}
                                                                        valueSelected={this.state.wardID}
                                                                        onChange={(item) => {
                                                                            this.setState({
                                                                                wardID: item.wardID,
                                                                                wardName: item.wardName
                                                                            },
                                                                                this.getCustomerAddress
                                                                            );
                                                                        }}
                                                                        defaultLabel={translate('activeSimManager.select_ward')}
                                                                        style={{
                                                                            flex: 1,
                                                                            flexDirection: "row",
                                                                            justifyContent: "center",
                                                                            alignItems: "center",
                                                                            marginVertical: 5,
                                                                            borderWidth: 1,
                                                                            borderRadius: 4,
                                                                            borderColor: '#CCCCCC',
                                                                            backgroundColor: 'white',
                                                                            minHeight: 40,
                                                                            marginHorizontal: 10
                                                                        }}
                                                                        disabled={this.state.isUploadSucess ||
                                                                            this.state.districtID == 0 ||
                                                                            (this.state.isInfoByViettel && this.state.infoCustomerViettel.PrecinctID != 0)
                                                                        }
                                                                    />

                                                                    <View
                                                                        style={{
                                                                            flex: 1,
                                                                            paddingHorizontal: 10
                                                                        }}>
                                                                        <MyText
                                                                            text={
                                                                                translate('activeSimManager.please_enter_address_on_id_card')
                                                                            }
                                                                            style={{
                                                                                color: 'red'
                                                                            }}
                                                                        />
                                                                    </View>

                                                                    <MyTextInput
                                                                        title={translate('activeSimManager.house_number')}
                                                                        required={false}
                                                                        value={this.state.houseNumber}
                                                                        onChangeText={(text) => {
                                                                            this.setState({
                                                                                houseNumber: text
                                                                            });
                                                                        }}
                                                                        onBlur={this.getCustomerAddress}
                                                                        editable={!this.state.isUploadSucess &&
                                                                            (!this.state.isInfoByViettel ||
                                                                                !this.state.infoCustomerViettel.CustomerAddress)}
                                                                        clearText={() => { this.setState({ houseNumber: '' }); }}
                                                                    />


                                                                    <MyTextInput
                                                                        title={translate('activeSimManager.street')}
                                                                        required={false}
                                                                        value={this.state.street}
                                                                        onChangeText={(text) => {
                                                                            this.setState({ street: text });
                                                                        }}
                                                                        onBlur={this.getCustomerAddress}
                                                                        editable={!this.state.isUploadSucess &&
                                                                            (!this.state.isInfoByViettel ||
                                                                                !this.state.infoCustomerViettel.CustomerAddress)}
                                                                        clearText={() => {
                                                                            this.setState({
                                                                                street: ''
                                                                            });
                                                                        }}
                                                                    />


                                                                    <MyTextInput
                                                                        title={translate('activeSimManager.quarter')}
                                                                        required={false}
                                                                        value={this.state.hamlet}
                                                                        onChangeText={(text) => {
                                                                            this.setState({
                                                                                hamlet: text
                                                                            });
                                                                        }}
                                                                        onBlur={this.getCustomerAddress}
                                                                        editable={!this.state.isUploadSucess &&
                                                                            (!this.state.isInfoByViettel ||
                                                                                !this.state.infoCustomerViettel.CustomerAddress)}
                                                                        clearText={() => {
                                                                            this.setState({
                                                                                hamlet: ''
                                                                            });
                                                                        }}
                                                                    />


                                                                    <MyTextInput
                                                                        title={translate('activeSimManager.address')}
                                                                        required={false}
                                                                        value={this.state.customerAddress}
                                                                        onChangeText={(text) => {
                                                                            this.setState({
                                                                                customerAddress: text
                                                                            });
                                                                        }}
                                                                        multiline={true}
                                                                        numberOfLines={3}
                                                                        textAlignVertical={'top'}
                                                                        scrollEnabled={true}
                                                                        editable={false}
                                                                        clearText={() => {
                                                                            this.setState({
                                                                                customerAddress: ''
                                                                            });
                                                                        }}
                                                                    />


                                                                    <View
                                                                        style={{
                                                                            flex: 1,
                                                                            flexDirection:
                                                                                'row',
                                                                            alignItems:
                                                                                'center',
                                                                            justifyContent:
                                                                                'space-between',
                                                                            paddingVertical:
                                                                                constants.getSize(
                                                                                    8
                                                                                ),
                                                                            paddingHorizontal:
                                                                                constants.getSize(
                                                                                    10
                                                                                ),
                                                                            marginBottom:
                                                                                constants.getSize(
                                                                                    10
                                                                                )
                                                                        }}>
                                                                        <RadioButton
                                                                            style={{
                                                                                flexDirection:
                                                                                    'row'
                                                                            }}
                                                                            containerStyle={{
                                                                                paddingLeft:
                                                                                    constants.getSize(
                                                                                        10
                                                                                    ),
                                                                                alignItems:
                                                                                    'center'
                                                                            }}
                                                                            dataItems={
                                                                                this
                                                                                    .state
                                                                                    .radioGender
                                                                            }
                                                                            selectItem={(
                                                                                index
                                                                            ) => {
                                                                                this.selectItemGender(
                                                                                    this
                                                                                        .state
                                                                                        .radioGender,
                                                                                    index
                                                                                );
                                                                            }}
                                                                            mainComponent={(
                                                                                item
                                                                            ) => {
                                                                                return (
                                                                                    <MyText
                                                                                        text={
                                                                                            item.title
                                                                                        }
                                                                                        style={{
                                                                                            color: item.selected
                                                                                                ? '#FF8900'
                                                                                                : '#333333',
                                                                                            marginLeft: 2
                                                                                        }}
                                                                                    />
                                                                                );
                                                                            }}
                                                                        />
                                                                    </View>


                                                                    <MyTextInput
                                                                        title={translate('activeSimManager.full_name')}
                                                                        required={true}
                                                                        value={this.state.customerName}
                                                                        onChangeText={(text) => {
                                                                            this.setState({ customerName: text });
                                                                        }}
                                                                        editable={!this.state.isUploadSucess &&
                                                                            (!this.state.isInfoByViettel ||
                                                                                !this.state.infoCustomerViettel.CustomerName)}
                                                                        clearText={() => {
                                                                            this.setState({ customerName: '' });
                                                                        }}
                                                                    />




                                                                    <SimDatePicker
                                                                        title={translate('activeSimManager.date_of_birth')}
                                                                        text={this
                                                                            .state
                                                                            .customerBirthday
                                                                            ? dateHelper.formatStrDateDDMMYYYY(
                                                                                this
                                                                                    .state
                                                                                    .customerBirthday
                                                                            )
                                                                            : translate('activeSimManager.select_date')}
                                                                        color={this
                                                                            .state
                                                                            .isUploadSucess ||
                                                                            (this
                                                                                .state
                                                                                .isInfoByViettel &&
                                                                                !!this
                                                                                    .state
                                                                                    .infoCustomerViettel
                                                                                    .CustomerBirthday)
                                                                            ? '#8e8e93'
                                                                            : '#288AD6'}
                                                                        datePicker={<DatePicker
                                                                            date={
                                                                                this
                                                                                    .state
                                                                                    .customerBirthday
                                                                            }
                                                                            maxDate={new Date()}
                                                                            mode="date"
                                                                            format="YYYY-MM-DD"
                                                                            confirmBtnText="Confirm"
                                                                            cancelBtnText="Cancel"
                                                                            onDateChange={(
                                                                                date
                                                                            ) => {
                                                                                this.setState(
                                                                                    {
                                                                                        customerBirthday:
                                                                                            date +
                                                                                            'T00:00:00'
                                                                                    }
                                                                                );
                                                                            }}
                                                                            disabled={
                                                                                this
                                                                                    .state
                                                                                    .isUploadSucess ||
                                                                                (this
                                                                                    .state
                                                                                    .isInfoByViettel &&
                                                                                    !!this
                                                                                        .state
                                                                                        .infoCustomerViettel
                                                                                        .CustomerBirthday)
                                                                            }
                                                                        />}
                                                                    />

                                                                    <MyTextInput
                                                                        title={translate('common.phone_number')}
                                                                        required={true}
                                                                        value={this.state.customerPhone}
                                                                        onChangeText={(text) => {
                                                                            this.setState({
                                                                                customerPhone: text
                                                                            });
                                                                        }}
                                                                        editable={!this.state.isUploadSucess}
                                                                        clearText={() => {
                                                                            this.setState({
                                                                                customerPhone: ''
                                                                            });
                                                                        }}
                                                                    />

                                                                </View>

                                                                <ImageProcess
                                                                    title={`${translate('activeSimManager.capture')} ${this.state.typeIdentification} ${translate('activeSimManager.front')}`}
                                                                    titleAgain={
                                                                        translate('activeSimManager.recapture')
                                                                    }
                                                                    onCamera={() => {
                                                                        this.setState(
                                                                            {
                                                                                isVisibleCamera: true,
                                                                                indexImage: 0,
                                                                                IschekcPicMT: true
                                                                            }
                                                                        );
                                                                    }}
                                                                    urlImage={
                                                                        this
                                                                            .state
                                                                            .uriImages[0]
                                                                    }
                                                                    isUploadSucess={
                                                                        this
                                                                            .state
                                                                            .isUploadSucess
                                                                    }
                                                                />
                                                                <ImageProcess
                                                                    title={`${translate('activeSimManager.capture')} ${this.state.typeIdentification} ${translate('activeSimManager.back')}`}
                                                                    titleAgain={
                                                                        translate('activeSimManager.recapture')
                                                                    }
                                                                    onCamera={() => {
                                                                        this.setState(
                                                                            {
                                                                                isVisibleCamera: true,
                                                                                indexImage: 1,
                                                                                IschekcPicMS: true
                                                                            }
                                                                        );
                                                                    }}
                                                                    urlImage={
                                                                        this
                                                                            .state
                                                                            .uriImages[1]
                                                                    }
                                                                    isUploadSucess={
                                                                        this
                                                                            .state
                                                                            .isUploadSucess
                                                                    }
                                                                />
                                                                <ImageProcess
                                                                    title={
                                                                        translate('activeSimManager.portrait')
                                                                    }
                                                                    titleAgain={
                                                                        translate('activeSimManager.recapture')
                                                                    }
                                                                    onCamera={() => {
                                                                        this.setState(
                                                                            {
                                                                                isVisibleCamera: true,
                                                                                indexImage: 2,
                                                                                IschekcPicCD: true
                                                                            }
                                                                        );
                                                                    }}
                                                                    urlImage={
                                                                        this
                                                                            .state
                                                                            .uriImages[2]
                                                                    }
                                                                    isUploadSucess={
                                                                        this
                                                                            .state
                                                                            .isUploadSucess
                                                                    }
                                                                />

                                                                <ImageProcess
                                                                    title={
                                                                        translate('activeSimManager.sign')
                                                                    }
                                                                    titleAgain={
                                                                        translate('activeSimManager.resign')
                                                                    }
                                                                    onCamera={() => {
                                                                        this.setState(
                                                                            {
                                                                                isVisibleSign: true,
                                                                                indexImage: 3,
                                                                                IschekcPicCK: true
                                                                            }
                                                                        );
                                                                    }}
                                                                    // urlImage={this.state.uriImages[3]}
                                                                    urlImage={
                                                                        this
                                                                            .state
                                                                            .signBase64
                                                                    }
                                                                    isUploadSucess={
                                                                        this
                                                                            .state
                                                                            .isUploadSucess
                                                                    }
                                                                />

                                                                <View
                                                                    style={{
                                                                        // flex: 1,
                                                                        justifyContent:
                                                                            'center',
                                                                        alignItems:
                                                                            'center',
                                                                        flexDirection:
                                                                            'row'
                                                                    }}>
                                                                    {
                                                                        isAllowEditSP == true &&
                                                                        <Button
                                                                            text={
                                                                                dataSimRequest.IsCreateNewSP == false ? translate('activeSimManager.btn_update') : "Tạo YCXL SIM"
                                                                            }
                                                                            styleContainer={{
                                                                                width: constants.getSize(
                                                                                    120
                                                                                ),
                                                                                height: constants.getSize(
                                                                                    50
                                                                                ),
                                                                                backgroundColor:
                                                                                    '#288AD6',
                                                                                borderRadius:
                                                                                    constants.getSize(
                                                                                        4
                                                                                    ),
                                                                                marginVertical: 10,
                                                                                opacity:
                                                                                    this
                                                                                        .state
                                                                                        .isUploadSucess || isDisable && !isCheckSIMType
                                                                                        ? 0.5
                                                                                        : 1
                                                                            }}
                                                                            styleText={{
                                                                                color: '#FFFFFF',
                                                                                fontSize: 14
                                                                            }}
                                                                            onPress={() => {
                                                                                let error =
                                                                                    this.checkValidateSimProcess();
                                                                                if (
                                                                                    error ==
                                                                                    ''
                                                                                ) {
                                                                                    this.uploadPicture();
                                                                                } else {
                                                                                    Alert.alert(
                                                                                        translate('common.notification_uppercase'),
                                                                                        error,
                                                                                        [
                                                                                            {
                                                                                                text: 'OK',
                                                                                                onPress:
                                                                                                    () => { },
                                                                                                style: 'default'
                                                                                            }
                                                                                        ],
                                                                                        {
                                                                                            cancelable: true
                                                                                        }
                                                                                    );
                                                                                }
                                                                            }}
                                                                            disabled={
                                                                                isDisable || this
                                                                                    .state
                                                                                    .isUploadSucess
                                                                            }
                                                                        />
                                                                    }
                                                                    {this.state
                                                                        .IsEdit &&
                                                                        !dataSimRequest.IsConnected &&
                                                                        dataSimRequest.IsOutput &&
                                                                        !dataSIMProcessInfo.IsConnect ? (
                                                                        <Button
                                                                            text={
                                                                                translate('activeSimManager.btn_connect')
                                                                            }
                                                                            styleContainer={{
                                                                                width: constants.getSize(
                                                                                    120
                                                                                ),
                                                                                height: constants.getSize(
                                                                                    50
                                                                                ),
                                                                                backgroundColor:
                                                                                    '#00FFFF',
                                                                                borderRadius:
                                                                                    constants.getSize(
                                                                                        4
                                                                                    ),
                                                                                marginVertical: 10,
                                                                                opacity:
                                                                                    this
                                                                                        .state
                                                                                        .isUploadSucess
                                                                                        ? 0.5
                                                                                        : 1,
                                                                                marginLeft: 50
                                                                            }}
                                                                            styleText={{
                                                                                color: '#FFFFFF',
                                                                                fontSize: 14
                                                                            }}
                                                                            onPress={() => {
                                                                                this.ConnectSim();
                                                                            }}
                                                                        />
                                                                    ) : (
                                                                        <View></View>
                                                                    )}
                                                                </View>
                                                            </View>
                                                        }
                                                    />
                                                </View>
                                            )
                                        }
                                    />
                                </KeyboardAwareScrollView>
                            )}

                            <CaptureCamera
                                isVisibleCamera={this.state.isVisibleCamera}
                                takePicture={this.takePicture}
                                closeCamera={this.closeCamera}
                                selectPicture={this.selectImage}
                            />

                            <SignImage
                                isVisibleSign={this.state.isVisibleSign}
                                takeSignature={(signature) => {
                                    this.takeSignature(signature);
                                }}
                                closeSignature={this.closeSignature}
                            />

                            {this.state.isScanSerial && (
                                <BarcodeCamera
                                    isModal={true}
                                    isVisible={this.state.isScanSerial}
                                    closeCamera={() => {
                                        this.setState({
                                            isScanSerial: false
                                        });
                                    }}
                                    resultScanBarcode={(barcode) => {
                                        if (dataSimRequest.BrandID == 125) {
                                            barcode = barcode.substr(0, 19);
                                        }
                                        this.setState(
                                            {
                                                simSerial: barcode,
                                                validateSimSerial: false,
                                                isScanSerial: false
                                            },
                                            this.getSerialSim(barcode)
                                        );
                                    }}
                                />
                            )}

                            <ModalCustomerInfoViettel
                                isVisible={this.state.isVisibleCustomerInfo}
                                hideModal={() => {
                                    this.setState({
                                        isVisibleCustomerInfo: false
                                    });
                                }}
                                data={this.state.dataCustomerViettel}
                                renderItem={(item, index) =>
                                    this.renderItemCustomerViettel(item, index)
                                }
                            />


                        </View>
                    ) : (
                        <NoInfoInputScreen
                            simSerialType={this.state.simSerialType}
                            isUploadSucess={this.state.isUploadSucess}
                            simSerial={this.state.simSerial}
                            isScanSerial={this.state.isScanSerial}
                            validateSimSerial={this.state.validateSimSerial}
                            IsEdit={this.state.IsEdit}
                            errorGetSimSerial={this.state.errorGetSimSerial}
                            PackagesTypeID={this.state.PackagesTypeID}
                            customerName={this.state.customerName}
                            customerIDCard={this.state.customerIDCard}
                            isInfoByViettel={this.state.isInfoByViettel}
                            infoCustomerViettel={this.state.infoCustomerViettel}
                            PGID={this.state.PGID}
                            simProcessState={this.props.simProcessState}
                            dataSimRequest={this.props.dataSimRequest}
                            dataSIMProcessInfo={this.props.simProcessState.dataSIMProcessInfo}
                            getSerialSim={this.getSerialSim}
                            selectTypeSim={this.selectTypeSim}
                            checkValidateSimProcess={this.checkValidateSimProcess}
                            updateDataSIMConnectype={this.updateDataSIMConnectype}
                            setIsScanSerial={this.setIsScanSerial}
                            setNotIsScanSerial={this.setNotIsScanSerial}
                            setSimSerialText={this.setSimSerialText}
                            setSimSerialBarcode={this.setSimSerialBarcode}
                            setNoErrorGetSerialEsim={this.setNoErrorGetSerialEsim}
                            setPackagesTypeID={this.setPackagesTypeID}
                            setCustomerName={this.setCustomerName}
                            setPGID={this.setPGID}
                            getSIMProcessInfo={this.props.actionGetSim.getSIMProcessInfo}
                            createDataSIMConnectype={this.createDataSIMConnectype}
                            handleCheckDocument={(idCard, simProcess, simSerial) => this.handleCheckDocument(idCard, simProcess, simSerial)}
                            isDisable={isDisable}
                        />
                    )}
                </SafeAreaView>
                <UIIndicator isVisible={this.state.isShowUIIndicator} />
            </KModal>
        );
    }

    uploadPicture = () => {
        this.showUIIndicator();
        let { dataSimRequest } = this.props;
        let bodyFromData = new FormData();
        let numberImages = 0;
        let indexImage = [];
        console.log('uriImages', this.state.uriImages);
        this.state.uriImages.forEach((ele, index) => {
            if (!!ele) {
                console.log('huypic', index);
                if (this.state.IsEdit) {
                    if (
                        (this.state.IschekcPicMT && index == 0) ||
                        (this.state.IschekcPicMS && index == 1) ||
                        (this.state.IschekcPicCD && index == 2)
                    ) {
                        bodyFromData.append('file', {
                            uri: ele,
                            type: 'image/jpg',
                            name:
                                dataSimRequest.SIMProcessRequestID +
                                '_' +
                                dateHelper.getTimestamp() +
                                '_' +
                                index +
                                '.jpg'
                        });
                        indexImage.push(index);
                        numberImages++;
                    }
                } else {
                    bodyFromData.append('file', {
                        uri: ele,
                        type: 'image/jpg',
                        name:
                            dataSimRequest.SIMProcessRequestID +
                            '_' +
                            dateHelper.getTimestamp() +
                            '_' +
                            index +
                            '.jpg'
                    });
                    indexImage.push(index);
                    numberImages++;
                }
            }
        });
        if (this.state.IschekcPicCK && this.state.IsEdit) {
            if (this.state.signBase64) {
                indexImage.push(3);
                numberImages++;
                bodyFromData.append('base64', this.state.signBase64);
            }
        } else {
            if (this.state.signBase64) {
                indexImage.push(3);
                numberImages++;
                bodyFromData.append('base64', this.state.signBase64);
            }
        }
        if (this.state.IsEdit) {
            this.getCDNImage(bodyFromData, indexImage);
        } else {
            if (numberImages < 4) {
                this.hideUIIndicator()
                Alert.alert(
                    translate('common.notification_uppercase'),
                    translate('activeSimManager.photo_and_signature'),
                    [
                        {
                            text: 'OK',
                            onPress:
                                () => { },
                            style: 'default'
                        }
                    ],
                    { cancelable: false }
                );
            } else {
                this.getCDNImage(bodyFromData, []);
            }
        }
    };

    getCDNImage = (bodyFromData, indexImage) => {
        console.log('ImageIndex', indexImage);
        let urlAttachmentFileBO = [];
        let { dataSIMProcessInfo } = this.props.simProcessState;
        let newDataSIMProcessInfo = {
            ...dataSIMProcessInfo,
            CustomerNationalityID: this.state.customerNationalityID,
            UserType: this.state.userType,
            IdentificationType: this.state.identificationType,
            ProvinceID: this.state.provinceID,
            DistrictID: this.state.districtID,
            WardID: this.state.wardID,
            CustomerIDCard: this.state.customerIDCard,
            IDCardIssueDate: this.state.idCardIssueDate,
            IDCardIssuePlace: this.state.idCardIssuePlace,
            VisaExpireDate:
                this.state.identificationType == 1
                    ? ''
                    : this.state.visaExpireDate,
            VisaNumber:
                this.state.identificationType == 3 ? this.state.visaNumber : '',
            CustomerAddress: this.state.customerAddress,
            Gender: this.state.gender,
            CustomerName: this.state.customerName,
            CustomerBirthday: this.state.customerBirthday,
            CustomerPhone: this.state.customerPhone,
            Hamlet: this.state.hamlet,
            HouseNumber: this.state.houseNumber,
            Street: this.state.street,
            // CustomerType: this.state.customerType,
            SIMSerialType: this.state.simSerialType
        };
        newDataSIMProcessInfo.SIMProcessRequestDetail.SIMSerial =
            this.state.simSerial;
        newDataSIMProcessInfo.SIMProcessRequestDetail.SIMSerialProductID =
            this.state.simSerialProductID;
        if (this.state.IsEdit && dataSIMProcessInfo.cus_IsITel) {
            if (this.state.UserName != '') {
                newDataSIMProcessInfo.StaffUser = this.state.UserName;
            }
        }
        if (this.state.IsEdit) {
            if (indexImage.length > 0) {
                actionGetSimCreator
                    .getImageCDN(bodyFromData)
                    .then((cdnImages) => {
                        console.log('uploadPicture', cdnImages);

                        if (this.state.IsEdit) {
                            indexImage.forEach((uriObj, index) => {
                                cdnImages.forEach((uriObj2, index2) => {
                                    if (index == index2) {
                                        console.log('huy count1', index);
                                        urlAttachmentFileBO[index] = {
                                            AttachTypeID: uriObj + 1,
                                            FilePath:
                                                API_CONST.API_GET_IMAGE_CDN +
                                                uriObj2,
                                            IsChange: true
                                            // "FilePath": API_CONST.API_GET_IMAGE_CDN + uriObj.id,
                                        };
                                    }
                                });
                            });
                            if (urlAttachmentFileBO.length < 4) {
                                console.log('huy count', urlAttachmentFileBO);
                                this.state.lstImageOld.forEach(
                                    (item, index) => {
                                        let lstarr = urlAttachmentFileBO.filter(
                                            function (x) {
                                                return (
                                                    x.AttachTypeID == index + 1
                                                );
                                            }
                                        );

                                        if (lstarr.length == 0) {
                                            let obj = {
                                                AttachTypeID: index + 1,
                                                FilePath: item.Uri
                                            };
                                            urlAttachmentFileBO.push(obj);
                                        }
                                    }
                                );
                            }
                        } else {
                            cdnImages.forEach((uriObj, index) => {
                                urlAttachmentFileBO[index] = {
                                    AttachTypeID: index + 1,
                                    FilePath:
                                        API_CONST.API_GET_IMAGE_CDN + uriObj,
                                    IsChange: true
                                };
                            });
                        }

                        newDataSIMProcessInfo.UrlAttachmentFileBO =
                            urlAttachmentFileBO;
                        console.log('huydata1', newDataSIMProcessInfo);
                        this.updateDataSIMProcess(newDataSIMProcessInfo);
                    })
                    .catch((error) => {
                        console.log('uploadPicture', error);
                        Alert.alert(
                            translate('common.notification_uppercase'),
                            translate('activeSimManager.error_update_image'),
                            [
                                {
                                    text: translate('common.btn_skip'),
                                    onPress: this.hideUIIndicator,
                                    style: 'cancel'
                                },
                                {
                                    text: translate('common.btn_notify_try_again'),
                                    onPress: () => {
                                        this.getCDNImage(
                                            bodyFromData,
                                            indexImage
                                        );
                                    },
                                    style: 'default'
                                }
                            ],
                            { cancelable: false }
                        );
                    });
            } else {
                this.state.lstImageOld.forEach((item, index) => {
                    let lstarr = urlAttachmentFileBO.filter(function (x) {
                        return x.AttachTypeID == index + 1;
                    });

                    if (lstarr.length == 0) {
                        let obj = {
                            AttachTypeID: index + 1,
                            FilePath: item.Uri
                            // "FilePath": API_CONST.API_GET_IMAGE_CDN + uriObj.id,
                        };
                        urlAttachmentFileBO.push(obj);
                    }
                });
                newDataSIMProcessInfo.UrlAttachmentFileBO = urlAttachmentFileBO;
                if (this.props.dataSimRequest.IsCreateNewSP == false) {
                    this.updateDataSIMProcess(newDataSIMProcessInfo);
                } else {
                    this.createDataSIMProcess(newDataSIMProcessInfo);
                }

            }
        } else {
            actionGetSimCreator
                .getImageCDN(bodyFromData)
                .then((cdnImages) => {
                    console.log('uploadPicture', cdnImages);
                    cdnImages.forEach((uriObj, index) => {
                        urlAttachmentFileBO[index] = {
                            AttachTypeID: index + 1,
                            FilePath: API_CONST.API_GET_IMAGE_CDN + uriObj,
                            IsChange: true
                            // "FilePath": API_CONST.API_GET_IMAGE_CDN + uriObj.id,
                        };
                    });
                    newDataSIMProcessInfo.UrlAttachmentFileBO =
                        urlAttachmentFileBO;
                    this.updateDataSIMProcess(newDataSIMProcessInfo);
                })
                .catch((error) => {
                    console.log('uploadPicture', error);
                    Alert.alert(
                        translate('common.notification_uppercase'),
                        translate('activeSimManager.error_update_image'),
                        [
                            {
                                text: translate('common.btn_skip'),
                                onPress: this.hideUIIndicator,
                                style: 'cancel'
                            },
                            {
                                text: translate('common.btn_notify_try_again'),
                                onPress: () => {
                                    this.getCDNImage(bodyFromData, indexImage);
                                },
                                style: 'default'
                            }
                        ],
                        { cancelable: false }
                    );
                });
        }
    };

    updateDataSIMConnectype = (updateDataSIMConnectype, updateCustomerName) => {
        let { dataSIMProcessInfo } = this.props.simProcessState;
        let newDataSIMProcessInfo = {
            ...dataSIMProcessInfo,
            CustomerName: updateCustomerName,
            SIMSerialType: this.state.simSerialType,
            PGID: this.state.PGID,
            CustomerIDCard: updateDataSIMConnectype

        };
        newDataSIMProcessInfo.SIMProcessRequestDetail.SIMSerial =
            this.state.simSerial;
        newDataSIMProcessInfo.SIMProcessRequestDetail.SIMSerialProductID =
            this.state.simSerialProductID;
        newDataSIMProcessInfo.SIMProcessRequestDetail.PackagesTypeID =
            this.state.PackagesTypeID;
        this.updateDataSIMProcess(newDataSIMProcessInfo);
    }

    createDataSIMConnectype = (updateDataSIMConnectype, updateCustomerName) => {
        this.showUIIndicator();
        let { dataSIMProcessInfo } = this.props.simProcessState;
        let newDataSIMProcessInfo = {
            ...dataSIMProcessInfo,
            CustomerName: updateCustomerName,
            SIMSerialType: this.state.simSerialType,
            PGID: this.state.PGID,
            CustomerIDCard: updateDataSIMConnectype
        };
        newDataSIMProcessInfo.SIMProcessRequestDetail.SIMSerial =
            this.state.simSerial;
        newDataSIMProcessInfo.SIMProcessRequestDetail.SIMSerialProductID =
            this.state.simSerialProductID;
        newDataSIMProcessInfo.SIMProcessRequestDetail.PackagesTypeID =
            this.state.PackagesTypeID;
        this.createDataSIMProcess(newDataSIMProcessInfo);
    }

    handleCheckDocument = (idCard, simProcessrequestID, simSerial) => {
        this.showUIIndicator();
        this.props.actionGetSim.checkDocumentInformation({
            "customerIDCard": idCard,
            "BrandID": this.props.dataSimRequest?.SIMProcessRequestDetail?.BrandID || this.props.dataSimRequest?.BrandID,
            "Quantity": 1,
            "simProcessrequestID": simProcessrequestID,
            "simSerial": simSerial
        })
            .then((data) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    data.Message,
                    [
                        {
                            text: 'OK',
                            style: '',
                            onPress: () => {
                                this.hideUIIndicator();
                                this.setState({ isDisable: false })
                            }
                        }
                    ]
                );
            })
            .catch((error) => {
                this.setState({ isDisable: true })
                Alert.alert(
                    translate('common.notification_uppercase'),
                    error.msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: 'cancel',
                            onPress: this.hideUIIndicator
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: 'default',
                            onPress: () => this.handleCheckDocument(idCard, simProcessrequestID, simSerial)
                        }
                    ]
                );
            });
    }

    ConnectSim() {
        let { dataSIMProcessInfo } = this.props.simProcessState;
        this.props.actionGetSim
            .ConnectSim(dataSIMProcessInfo.SIMProcessRequestID)
            .then((res) => {
                console.log('huy connect', res);
                Alert.alert(
                    translate('common.notification_uppercase'),
                    translate('activeSimManager.connect_success'),
                    [
                        {
                            text: 'OK',
                            onPress: () => {
                                this.hideUIIndicator();
                                this.props.onUpdateSucess();
                                this.props.hideModal();
                                this.setState(helper.deepCopy(this.defaultState));
                            },
                            style: 'default'
                        }
                    ],
                    { cancelable: false }
                );
            })
            .catch((error) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    error.msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            onPress: this.hideUIIndicator,
                            style: 'cancel'
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            onPress: () => {
                                this.ConnectSim();
                            },
                            style: 'default'
                        }
                    ],
                    { cancelable: false }
                );
            });
    }

    updateDataSIMProcess = (newDataSIMProcessInfo) => {
        this.props.actionGetSim
            .updateSimProcessInfo(newDataSIMProcessInfo)
            .then((res) => {
                this.setState({
                    isUploadSucess: true
                });
                Alert.alert(
                    translate('common.notification_uppercase'),
                    translate('activeSimManager.update_success'),
                    [
                        {
                            text: 'OK',
                            onPress: () => {
                                this.hideUIIndicator();
                                this.props.onUpdateSucess();
                                this.props.hideModal();
                                this.setState(helper.deepCopy(this.defaultState));
                            },
                            style: 'default'
                        }
                    ],
                    { cancelable: false }
                );
            })
            .catch((err) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    err.msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            onPress: this.hideUIIndicator,
                            style: 'cancel'
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            onPress: () => {
                                this.updateDataSIMProcess(
                                    newDataSIMProcessInfo
                                );
                            },
                            style: 'default'
                        }
                    ],
                    { cancelable: false }
                );
            });
    };

    createDataSIMProcess = (newDataSIMProcessInfo) => {
        this.props.actionGetSim
            .createSimProcessInfo(newDataSIMProcessInfo)
            .then((res) => {
                this.setState({
                    isUploadSucess: true
                });
                Alert.alert(
                    translate('common.notification_uppercase'),
                    translate('activeSimManager.update_success'),
                    [
                        {
                            text: 'OK',
                            onPress: () => {
                                this.hideUIIndicator();
                                this.props.onUpdateSucess();
                                this.props.hideModal();
                                this.setState(helper.deepCopy(this.defaultState));
                            },
                            style: 'default'
                        }
                    ],
                    { cancelable: false }
                );
            })
            .catch((err) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    err.msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            onPress: this.hideUIIndicator,
                            style: 'cancel'
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            onPress: () => {
                                this.updateDataSIMProcess(
                                    newDataSIMProcessInfo
                                );
                            },
                            style: 'default'
                        }
                    ],
                    { cancelable: false }
                );
            });
    }

    selectItemGender = (data, index) => {
        if (
            !this.state.isUploadSucess &&
            !this.state.isInfoByViettel &&
            !data[index].selected
        ) {
            data.forEach((item) => {
                item.selected = false;
            });
            data[index].selected = true;
            this.setState({
                radioGender: data,
                gender: data[index].value
            });
        }
    };

    selectTypeSim = (data, index) => {
        if (this.state.IsCheckType) {
            this.setState({
                radioTypeSim: data,
                simSerialType: index,
                validateSimSerial: false,
                errorGetSimSerial: '',
                IsCheckType: true,
                simSerial: '',
                simSerialProductID: ''
            });
        } else {
            this.setState({
                radioTypeSim: data,
                simSerialType: index,
                errorGetSimSerial: '',
                IsCheckType: true
            });
        }
        if (!this.state.isUploadSucess && !data[index].selected) {
            data.forEach((item) => {
                item.selected = false;
            });

            data[index].selected = true;
            if (index == 1) {
                this.getSerialEsim();
            }
        }
    };

    getSerialSim = (simSerial) => () => {
        if (!this.state.validateSimSerial && !!simSerial) {
            let { dataSIMProcessInfo } = this.props.simProcessState;
            this.showUIIndicator();
            this.props.actionGetSim
                .checkSimSerial({
                    simProcessRequestTypeID:
                        dataSIMProcessInfo.SIMProcessRequestTypeID,
                    processStoreID: dataSIMProcessInfo.ProcessStoreID,
                    requestStoreID: dataSIMProcessInfo.RequestStoreID,
                    ConnectType: dataSIMProcessInfo.ConnectType,
                    simProcessRequestDetail: { simSerial: simSerial }
                })
                .then((simSerialProductID) => {
                    this.setState({
                        simSerial: simSerial,
                        simSerialProductID: simSerialProductID,
                        validateSimSerial: true,
                        errorGetSimSerial: '',
                        isShowUIIndicator: false
                    });
                })
                .catch((err) => {
                    this.setState({
                        validateSimSerial: false,
                        errorGetSimSerial: err.msgError,
                        isShowUIIndicator: false
                    });
                });
        }
    };

    getSerialEsim = () => {
        let { dataSIMProcessInfo } = this.props.simProcessState;
        this.showUIIndicator();
        this.props.actionGetSim
            .getSimSerial({
                simProcessRequestTypeID:
                    dataSIMProcessInfo.SIMProcessRequestTypeID,
                processStoreID: dataSIMProcessInfo.ProcessStoreID,
                requestStoreID: dataSIMProcessInfo.RequestStoreID,
                simProcessRequestDetail: {
                    productID:
                        dataSIMProcessInfo.SIMProcessRequestDetail.ProductID
                }
            })
            .then((res) => {
                this.setState({
                    simSerial: res.simSerial,
                    simSerialProductID: res.simSerialProductID,
                    validateSimSerial: true,
                    errorGetSimSerial: '',
                    isShowUIIndicator: false
                });
            })
            .catch((err) => {
                this.setState({
                    validateSimSerial: false,
                    errorGetSimSerial: err.msgError,
                    isShowUIIndicator: false
                });
            });
    };

    checkValidateSimProcess = () => {
        console.log("SIMPROPRO", this)
        let msgValidate = '';
        let isValidate = true;
        let { dataSIMProcessInfo } = this.props.simProcessState;
        if (!this.state.simSerial) {
            isValidate = false;
            this.setState({ msgValidate: translate('activeSimManager.message_1') });
            msgValidate = translate('activeSimManager.message_1');
            return msgValidate;
        }
        // if (!this.state.validateSimSerial) {
        //     isValidate = false;
        //     this.setState({ msgValidate: translate('activeSimManager.message_2') });
        //     msgValidate = translate('activeSimManager.message_2');
        //     return msgValidate;
        // }
        if (!this.state.customerName) {
            isValidate = false;
            this.setState({ msgValidate: translate('activeSimManager.message_3') });
            msgValidate = translate('activeSimManager.message_3');
            return msgValidate;
        }
        if (!this.state.PackagesTypeID) {
            isValidate = false;
            this.setState({ msgValidate: translate('activeSimManager.message_4') });
            msgValidate = translate('activeSimManager.message_4');
            return msgValidate;
        }
        if (!dataSIMProcessInfo.IsCheckConectType) {
            if (this.state.customerNationalityID == 0) {
                isValidate = false;
                this.setState({ msgValidate: translate('activeSimManager.message_5') });
                msgValidate = translate('activeSimManager.message_5');
                return msgValidate;
            }
            if (this.state.userType == 0) {
                isValidate = false;
                this.setState({
                    msgValidate: translate('activeSimManager.message_6')
                });
                msgValidate = translate('activeSimManager.message_6');
                return msgValidate;
            }
            if (this.state.provinceID == 0) {
                isValidate = false;
                this.setState({ msgValidate: translate('activeSimManager.message_7') });
                msgValidate = translate('activeSimManager.message_7');
                return msgValidate;
            }
            if (this.state.districtID == 0) {
                isValidate = false;
                this.setState({ msgValidate: translate('activeSimManager.message_8') });
                msgValidate = translate('activeSimManager.message_8');
                return msgValidate;
            }
            if (this.state.wardID == 0) {
                isValidate = false;
                this.setState({ msgValidate: translate('activeSimManager.message_9') });
                msgValidate = translate('activeSimManager.message_9');
                return msgValidate;
            }
            if (this.state.identificationType == 0) {
                isValidate = false;
                this.setState({ msgValidate: translate('activeSimManager.message_10') });
                msgValidate = translate('activeSimManager.message_10');
                return msgValidate;
            }
            if (!this.state.customerIDCard) {
                isValidate = false;
                this.setState({ msgValidate: translate('activeSimManager.message_11') });
                msgValidate = translate('activeSimManager.message_11');
                return msgValidate;
            }
            if (!this.state.idCardIssueDate) {
                isValidate = false;
                this.setState({
                    msgValidate: translate('activeSimManager.message_12')
                });
                msgValidate = translate('activeSimManager.message_12');
                return msgValidate;
            }
            if (this.state.idCardIssuePlace == 0) {
                isValidate = false;
                this.setState({
                    msgValidate: translate('activeSimManager.message_13')
                });
                msgValidate = translate('activeSimManager.message_13');
                return msgValidate;
            }

            if (
                this.state.identificationType != 1 &&
                !this.state.visaExpireDate
            ) {
                isValidate = false;
                this.setState({
                    msgValidate: translate('activeSimManager.message_14')
                });
                msgValidate = translate('activeSimManager.message_14');
                return msgValidate;
            }
            if (
                this.state.identificationType != 1 &&
                this.state.visaExpireDate && isExpriedTime(this.state.visaExpireDate)
            ) {
                isValidate = false;
                this.setState({
                    msgValidate: "Ngày hết hạn không hợp lệ"
                });
                msgValidate = "Ngày hết hạn không hợp lệ";
                return msgValidate;
            }

            if (this.state.identificationType == 3 && !this.state.visaNumber) {
                isValidate = false;
                this.setState({
                    msgValidate:
                        this.state.customerNationalityID != 241
                            ? translate('activeSimManager.message_15')
                            : translate('activeSimManager.message_16')
                });
                msgValidate =
                    this.state.customerNationalityID != 241
                        ? translate('activeSimManager.message_15')
                        : translate('activeSimManager.message_16');
                return msgValidate;
            }

            if (!this.state.customerBirthday) {
                isValidate = false;
                this.setState({
                    msgValidate: translate('activeSimManager.message_17')
                });
                msgValidate = translate('activeSimManager.message_17');
                return msgValidate;
            }
            if (!this.state.customerPhone) {
                isValidate = false;
                this.setState({ msgValidate: translate('activeSimManager.message_18') });
                msgValidate = translate('activeSimManager.message_18');
                return msgValidate;
            }
            if (this.state.gender !== 0 && this.state.gender !== 1) {
                isValidate = false;
                this.setState({ msgValidate: translate('activeSimManager.validation_gender') });
                msgValidate = translate('activeSimManager.validation_gender');
                return msgValidate;
            }
        }
        if (
            dataSIMProcessInfo.cus_IsVinaPG &&
            dataSIMProcessInfo.IsCheckConectType
        ) {
            if (this.state.PGID == 0) {
                isValidate = false;
                this.setState({ msgValidate: translate('activeSimManager.message_19') });
                msgValidate = translate('activeSimManager.message_19');
                return msgValidate;
            }
        }
        return msgValidate;
    }

    getCustomerAddress = () => {
        let {
            houseNumber,
            street,
            hamlet,
            wardName,
            districtName,
            provinceName
        } = this.state;
        let data = [];
        if (!!houseNumber) {
            data.push(houseNumber);
        }
        if (!!street) {
            data.push(street);
        }
        if (!!hamlet) {
            data.push(hamlet);
        }
        if (!!wardName) {
            data.push(wardName);
        }
        if (!!districtName) {
            data.push(districtName);
        }
        if (!!provinceName) {
            data.push(provinceName);
        }
        if (data.length > 0) {
            this.setState({ customerAddress: data.join(', ') });
        }
    };

    renderItemCustomerViettel = (item, index) => {
        return (
            <TouchableOpacity
                style={{
                    width: constants.width - 20,
                    backgroundColor: 'white',
                    padding: 10,
                    shadowColor: '#000',
                    shadowOffset: {
                        width: 0,
                        height: 2
                    },
                    shadowOpacity: 0.3,
                    shadowRadius: 2.5,
                    elevation: 4,
                    justifyContent: 'center',
                    margin: 10,
                    borderWidth: 1,
                    borderColor: '#CCCCCC'
                }}
                onPress={() => {
                    this.setState({
                        customerName: item.CustomerName,
                        customerAddress: item.CustomerAddress,
                        customerBirthday: item.CustomerBirthday,
                        gender: item.Gender ? 1 : 0,
                        provinceID: item.ProvinceID,
                        districtID: item.DistrictID,
                        wardID: item.PrecinctID,
                        idCardIssueDate: item.IDCardIssueDate,
                        idCardIssuePlace: item.IDCardIssuePlace,
                        // customerType: item.CustomerType,
                        wardName: item.WardName,
                        districtName: item.DistrictName,
                        provinceName: item.ProvinceName,
                        hamlet: '',
                        houseNumber: '',
                        street: '',
                        radioGender: [
                            {
                                title: translate('shoppingCart.male'),
                                selected: item.Gender,
                                value: 1
                            },
                            {
                                title: translate('shoppingCart.female'),
                                selected: !item.Gender,
                                value: 0
                            }
                        ],

                        isVisibleCustomerInfo: false,
                        isInfoByViettel: true,
                        infoCustomerViettel: { ...item }
                    });
                    this.getDistrict(item.ProvinceID);
                    this.getWard(item.ProvinceID, item.DistrictID);
                }}>
                <View
                    style={{
                        flex: 1,
                        flexDirection: 'row',
                        alignItems: 'flex-start',
                        justifyContent: 'flex-start',
                        paddingVertical: 3
                    }}>
                    <MyText
                        text={translate('activeSimManager.name')}
                        style={{
                            width: 80,
                            fontWeight: 'bold'
                        }}
                    />
                    <MyText
                        text={item.CustomerName}
                        style={{
                            maxWidth: '80%'
                        }}
                    />
                </View>
                <View
                    style={{
                        flex: 1,
                        flexDirection: 'row',
                        alignItems: 'flex-start',
                        justifyContent: 'flex-start',
                        paddingVertical: 3
                    }}>
                    <MyText
                        text={translate('activeSimManager.date_of_birth_2')}
                        style={{
                            width: 80,
                            fontWeight: 'bold'
                        }}
                    />
                    <MyText
                        text={
                            !!item.CustomerBirthday ? item.CustomerBirthday : ''
                        }
                        style={{
                            maxWidth: '80%'
                        }}
                    />
                </View>
                <View
                    style={{
                        flex: 1,
                        flexDirection: 'row',
                        alignItems: 'flex-start',
                        justifyContent: 'flex-start',
                        paddingVertical: 3
                    }}>
                    <MyText
                        text={translate('activeSimManager.address_2')}
                        style={{
                            width: 80,
                            fontWeight: 'bold'
                        }}
                    />
                    <MyText
                        text={item.CustomerAddress}
                        style={{
                            maxWidth: '80%'
                        }}
                    />
                </View>
                <View
                    style={{
                        flex: 1,
                        flexDirection: 'row',
                        alignItems: 'flex-start',
                        justifyContent: 'flex-start',
                        paddingVertical: 3
                    }}>
                    <MyText
                        text={translate('activeSimManager.date')}
                        style={{
                            width: 80,
                            fontWeight: 'bold'
                        }}
                    />
                    <MyText
                        text={
                            !!item.IDCardIssueDate ? item.IDCardIssueDate : ''
                        }
                        style={{
                            maxWidth: '80%'
                        }}
                    />
                </View>
                {!!item.IDCardIssuePlaceName && (
                    <View
                        style={{
                            flex: 1,
                            flexDirection: 'row',
                            alignItems: 'flex-start',
                            justifyContent: 'flex-start',
                            paddingVertical: 3
                        }}>
                        <MyText
                            text={translate('activeSimManager.place')}
                            style={{
                                width: 80,
                                fontWeight: 'bold'
                            }}
                        />
                        <MyText
                            text={item.IDCardIssuePlaceName}
                            style={{
                                maxWidth: '80%'
                            }}
                        />
                    </View>
                )}
            </TouchableOpacity>
        );
    };

    getTypeIdentification = (id = 1) => {
        switch (id) {
            case 1:
                this.setState({ typeIdentification: translate('activeSimManager.id_card_1_short') });
                break;
            case 2:
                this.setState({ typeIdentification: translate('activeSimManager.id_card_2_short') });
                break;
            default:
                this.setState({ typeIdentification: translate('activeSimManager.passport') });
                break;
        }
    };

    getTypeImage = (identificationType, indexImage) => {
        switch (identificationType) {
            case 1:
                switch (indexImage) {
                    case 0:
                        return 1;
                    case 1:
                        return 3;
                    default:
                        return 0;
                }
            case 2:
                switch (indexImage) {
                    case 0:
                        return 2;
                    case 1:
                        return 4;
                    default:
                        return 0;
                }
            default:
                return 0;
        }
    };

    takePicture = (photo) => {
        let newURIImages = [...this.state.uriImages];
        if (helper.hasProperty(photo, 'uri')) {
            helper.resizeImage(photo)
                .then(({ path, uri, size, name }) => {
                    if (
                        this.state.indexImage != 3 &&
                        !this.state.isInfoByViettel
                    ) {
                        this.getInfoCustomerByImage(uri);
                    }
                    Marker.markText({
                        backgroundImage: {
                            src: uri,
                            scale: 1
                        },
                        scale: 1,
                        quality: 100,
                        saveFormat: ImageFormat.jpg,
                        watermarkTexts: [{
                            text: dateHelper.formatDateFULL(),
                            position: {
                                X: 5,
                                Y: 5,
                            },
                            style: {
                                bold: true,
                                color: '#fecb2e',
                                fontSize: 30,
                                fontName: Platform.OS === 'ios'
                                    ? 'Courier'
                                    : 'sans-serif-light'
                            },
                        }]
                    })
                        .then((pathMarker) => {
                            newURIImages[this.state.indexImage] =
                                Platform.OS == 'ios'
                                    ? pathMarker
                                    : `file://${pathMarker}`;
                            this.setState({
                                isVisibleCamera: false,
                                uriImages: newURIImages
                            });
                        })
                        .catch((err) => {
                            console.log(err);
                        });
                })
                .catch((error) => {
                    console.log('resizeImage', error);
                });
        }
    };

    selectImage = () => {
        let newURIImages = [...this.state.uriImages];
        launchImageLibrary(
            // options =
            {
                mediaType: 'photo',
                noData: true
            },
            (response) => {
                if (helper.hasProperty(response, 'uri')) {
                    helper.resizeImage(response)
                        .then(({ path, uri, size, name }) => {
                            if (
                                this.state.indexImage != 3 &&
                                !this.state.isInfoByViettel
                            ) {
                                this.getInfoCustomerByImage(uri);
                            }
                            Marker.markText({
                                backgroundImage: {
                                    src: uri,
                                    scale: 1
                                },
                                scale: 1,
                                quality: 100,
                                saveFormat: ImageFormat.jpg,
                                watermarkTexts: [{
                                    text: dateHelper.formatDateFULL(),
                                    position: {
                                        X: 5,
                                        Y: 5,
                                    },
                                    style: {
                                        bold: true,
                                        color: '#fecb2e',
                                        fontSize: 30,
                                        fontName: Platform.OS === 'ios'
                                            ? 'Courier'
                                            : 'sans-serif-light'
                                    },
                                }]
                            })
                                .then((pathMarker) => {
                                    newURIImages[this.state.indexImage] =
                                        Platform.OS == 'ios'
                                            ? pathMarker
                                            : `file://${pathMarker}`;
                                    this.setState({
                                        isVisibleCamera: false,
                                        uriImages: newURIImages
                                    });
                                })
                                .catch((err) => {
                                    console.log(err);
                                });
                        })
                        .catch((error) => {
                            console.log('resizeImage', error);
                        });
                }
            }
        );
    };

    getInfoCustomerByImage = (uriImage) => {
        const { userInfo } = this.props;
        this.typeImageDetect = this.getTypeImage(
            this.state.identificationType,
            this.state.indexImage
        );
        if (this.typeImageDetect != 0) {
            let bodyFromData = new FormData();
            bodyFromData.append('file', {
                uri: uriImage,
                type: 'image/jpg',
                name: 'getInfoCustomerByImage' + dateHelper.getTimestamp()
            });
            bodyFromData.append('client_id', `MWGPOS_${userInfo.userName}`);
            bodyFromData.append('chosen_side', this.typeImageDetect);
            actionGetSimCreator
                .getInfoByImage(bodyFromData)
                .then((res) => {
                    const gender = GENDER[res.sex] != undefined ? GENDER[res.sex] : this.state.gender;
                    switch (this.typeImageDetect) {
                        case 1:
                            this.setState({
                                provinceID: res.cap_1_id,
                                districtID: res.cap_2_id,
                                wardID: res.cap_3_id,
                                provinceName: res.cap1,
                                districtName: res.cap2,
                                wardName: res.cap3,
                                customerBirthday: res.date_of_birth,
                                customerName: res.full_name,
                                customerAddress: res.place_of_permanent,
                                customerIDCard: res.id_no,
                                gender: gender,
                                visaExpireDate: res.expiration_date,
                                radioGender: [
                                    {
                                        title: translate('activeSimManager.male'),
                                        selected: gender == 1,
                                        value: 1
                                    },
                                    {
                                        title: translate('activeSimManager.female'),
                                        selected: gender == 0,
                                        value: 0
                                    }
                                ],
                            });
                            this.getDistrict(res.cap_1_id);
                            this.getWard(res.cap_1_id, res.cap_2_id);
                            break;
                        case 2:
                            this.setState({
                                provinceID: res.cap_1_id,
                                districtID: res.cap_2_id,
                                wardID: res.cap_3_id,
                                provinceName: res.cap1,
                                districtName: res.cap2,
                                wardName: res.cap3,
                                customerBirthday: res.date_of_birth,
                                customerName: res.full_name,
                                customerAddress: res.place_of_permanent,
                                customerIDCard: res.id_no,
                                visaExpireDate: res.expiration_date,
                                gender: gender,
                                radioGender: [
                                    {
                                        title: translate('activeSimManager.male'),
                                        selected: gender == 1,
                                        value: 1
                                    },
                                    {
                                        title: translate('activeSimManager.female'),
                                        selected: gender == 0,
                                        value: 0
                                    }
                                ],
                            });
                            this.getDistrict(res.cap_1_id);
                            this.getWard(res.cap_1_id, res.cap_2_id);
                            break;
                        case 3:
                            this.setState({
                                idCardIssueDate: res.date_of_issue,
                                idCardIssuePlace: res.erp_place_of_issue_id
                            });
                            this.getIssueplaceByName(res.place_of_issue);
                            break;
                        case 4:
                            this.setState({
                                idCardIssueDate: res.date_of_issue,
                                idCardIssuePlace: res.erp_place_of_issue_id
                            });
                            break;
                        default:
                            console.log(res);
                            break;
                    }
                })
                .catch((err) => {
                    console.log(err);
                });
        }
    };

    getIssueplaceByName = (issueplace) => {
        let issueSelect = this.state.dataIssuePlace.find(
            (e) => e.idCardIssuePlaceName == issueplace
        );
        if (!!issueSelect) {
            this.setState({
                idCardIssuePlace: issueSelect.idCardIssuePlaceId
            });
        }
    };

    showUIIndicator = () => {
        this.setState({ isShowUIIndicator: true });
    };

    hideUIIndicator = () => {
        this.setState({ isShowUIIndicator: false });
    };
}

const mapStateToProps = function (state) {
    return {
        simProcessState: state.ActiveSimManagementReducer.lstSIM,
        nationalityState: state.ActiveSimManagementReducer.nationality,
        userInfo: state.userReducer
    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        actionGetSim: bindActionCreators(actionGetSimCreator, dispatch),
        actionShoppingCard: bindActionCreators(actionShoppingCardCreator, dispatch)
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(ModalSIMProcess);

// export { ModalSIMProcess };
const GENDER = {
    "Nam": 1,
    "Nữ": 0
}

const isExpriedTime = (strDate) => {
    let isExpried = true;
    if (strDate) {
        const expriedTime = new Date(strDate).getTime();
        const currentTime = Date.now();
        isExpried = expriedTime < currentTime
    }
    return isExpried;
}