import React, { useState, useEffect, useMemo } from 'react';
import { View, TouchableOpacity, Alert, Text } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import {
    MyText,
    BaseContainer,
    SearchInput,
    Button,
    MyPicker,
    RadioButton,
    FieldInput,
    BarcodeCamera,
    PickerSearch,
    TitleInput
} from '@components';
import { constants } from '@constants';
import { helper } from '@common';
import { translate } from '@translate';
import { COLORS } from '@styles';
import { useSelector } from 'react-redux';
const { BRAND_ID_OF_SIM, STORE_ALLOW_VIETTEL_SIM } = constants

export default NoInfoInputScreen = ({
    simSerialType,
    isUploadSucess,
    simSerial,
    isScanSerial,
    validateSimSerial,
    IsEdit,
    errorGetSimSerial,
    PackagesTypeID,
    customerName,
    customerIDCard,
    isInfoByViettel,
    infoCustomerViettel,
    PGID,
    simProcessState,
    dataSimRequest,
    dataSIMProcessInfo,
    getSerialSim,
    selectTypeSim,
    checkValidateSimProcess,
    updateDataSIMConnectype,
    setIsScanSerial,
    setNotIsScanSerial,
    setSimSerialText,
    setSimSerialBarcode,
    setNoErrorGetSerialEsim,
    setPackagesTypeID,
    setCustomerName,
    setPGID,
    getSIMProcessInfo,
    createDataSIMConnectype,
    handleCheckDocument,
    isDisable
}) => {

    const { storeID } = useSelector((_state) => _state.userReducer)
    const { SIMSerialType, IsCreateNewSP } = dataSIMProcessInfo;
    const [radioTypeSim, setTypeSim] = useState([
        { title: translate('activeSimManager.physical_sim'), selected: false },
        { title: translate('activeSimManager.esim'), selected: false }
    ]);
    const [updateCustomerIDCard, setUpdateCustomerIDCard] = useState('');
    const [updateCustomerName, setUpdateCustomerName] = useState('');

    useEffect(() => {
        setTypeSim([
            { title: translate('activeSimManager.physical_sim'), selected: SIMSerialType == 0 },
            { title: translate('activeSimManager.esim'), selected: SIMSerialType == 1 }
        ])
    }, [SIMSerialType]);

    console.log(dataSimRequest, dataSIMProcessInfo, isDisable, "log data SIM");

    useEffect(() => {
        if (customerIDCard !== '' || customerName !== '') {
            setUpdateCustomerIDCard(customerIDCard);
            setUpdateCustomerName(customerName)
        }
    }, [customerIDCard, customerName]);

    const isAllowChangeType = dataSIMProcessInfo.cus_IsViettel || dataSIMProcessInfo.cus_IsVina || dataSIMProcessInfo.cus_IsMobi || (dataSIMProcessInfo.cus_IsASim)
    const isShowCustomerID = useMemo(
        () => { return checkCustomerID(dataSIMProcessInfo?.SIMProcessRequestDetail?.BrandID, dataSIMProcessInfo?.RequestStoreID) }
        , [dataSIMProcessInfo?.SIMProcessRequestDetail?.BrandID, dataSIMProcessInfo?.RequestStoreID])

    const isCheckSIMType = dataSIMProcessInfo?.cus_IsASim == true || dataSIMProcessInfo?.cus_IsMobi == true;
    const isAllowEditSP = dataSIMProcessInfo.IsAllowEditSP;
    const sIMProcessRequestID = dataSIMProcessInfo.SIMProcessRequestID;

    return (
        <View
            style={{
                flex: 1,
                height: constants.height,
                backgroundColor: 'white'
            }}>
            <KeyboardAwareScrollView
                style={{
                    flex: 1
                }}
                contentContainerStyle={{
                    paddingVertical: 10
                }}
                enableResetScrollToCoords={false}
                keyboardShouldPersistTaps="always"
                keyboardDismissMode="on-drag"
                bounces={false}
                overScrollMode="always"
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}>
                {
                    !helper.IsEmptyObject(
                        dataSIMProcessInfo
                    ) && (
                        <View
                            style={{
                                flex: 1
                            }}>
                            <View
                                style={{
                                    flex: 1,
                                    paddingHorizontal: 10,
                                    marginBottom: 10
                                }}>
                                <MyText
                                    text={translate('activeSimManager.request_store')}
                                    style={{
                                        color: '#333333',
                                        fontWeight: 'bold',
                                        marginBottom: 5
                                    }}
                                    children={
                                        <MyText
                                            text={
                                                dataSIMProcessInfo.RequestStoreID +
                                                ' - ' +
                                                dataSIMProcessInfo.RequestStoreName
                                            }
                                            style={{
                                                color: '#14A19C'
                                            }}
                                        />
                                    }
                                />
                                <MyText
                                    text={translate('activeSimManager.process_store')}
                                    style={{
                                        color: '#333333',
                                        fontWeight: 'bold',
                                        marginBottom: 5
                                    }}
                                    children={
                                        <MyText
                                            text={
                                                dataSIMProcessInfo.ProcessStoreID +
                                                ' - ' +
                                                dataSIMProcessInfo.ProcessStoreName
                                            }
                                            style={{
                                                color: '#14A19C'
                                            }}
                                        />
                                    }
                                />
                                <MyText
                                    text={translate('activeSimManager.sale_agent')}
                                    style={{
                                        color: '#333333',
                                        fontWeight: 'bold',
                                        marginBottom: 5
                                    }}
                                    children={
                                        <MyText
                                            text={
                                                dataSIMProcessInfo.StaffUser +
                                                ' - ' +
                                                dataSIMProcessInfo.StaffUserFullName
                                            }
                                            style={{
                                                color: '#77B50B'
                                            }}
                                        />
                                    }
                                />
                                <MyText
                                    text={translate('activeSimManager.product')}
                                    style={{
                                        color: '#333333',
                                        fontWeight: 'bold',
                                        marginBottom: 5
                                    }}
                                    children={
                                        <MyText
                                            text={
                                                helper.isString(
                                                    dataSIMProcessInfo
                                                        .SIMProcessRequestDetail
                                                        .ProductID
                                                )
                                                    ? dataSIMProcessInfo.SIMProcessRequestDetail.ProductID.trim() +
                                                    ' - ' +
                                                    dataSIMProcessInfo
                                                        .SIMProcessRequestDetail
                                                        .ProductName
                                                    : ''
                                            }
                                            style={{
                                                color: '#14A19C'
                                            }}
                                        />
                                    }
                                />

                                <MyText
                                    text={'IMEI: '}
                                    style={{
                                        color: '#333333',
                                        marginBottom: 10
                                    }}
                                    children={
                                        <MyText
                                            text={
                                                dataSIMProcessInfo
                                                    .SIMProcessRequestDetail
                                                    .IMEI
                                            }
                                            style={{
                                                color: '#333333'
                                            }}
                                        />
                                    }
                                />

                                {isAllowChangeType ? (
                                    <View
                                        style={{
                                            flex: 1,
                                            flexDirection:
                                                'row',
                                            alignItems:
                                                'center',
                                            justifyContent:
                                                'space-between',
                                            paddingHorizontal:
                                                constants.getSize(
                                                    10
                                                ),
                                            marginBottom:
                                                constants.getSize(
                                                    10
                                                )
                                        }}>
                                        <RadioButton
                                            style={{
                                                flexDirection:
                                                    'row'
                                            }}
                                            containerStyle={{
                                                paddingLeft:
                                                    constants.getSize(
                                                        10
                                                    ),
                                                alignItems:
                                                    'center'
                                            }}
                                            dataItems={
                                                radioTypeSim
                                            }
                                            selectItem={(
                                                index
                                            ) => {
                                                selectTypeSim(
                                                    radioTypeSim,
                                                    index
                                                );
                                            }}
                                            mainComponent={(
                                                item
                                            ) => {
                                                return (
                                                    <MyText
                                                        text={
                                                            item.title
                                                        }
                                                        style={{
                                                            color: item.selected
                                                                ? '#FF8900'
                                                                : '#333333',
                                                            marginLeft: 2
                                                        }}
                                                    />
                                                );
                                            }}
                                        />
                                    </View>
                                ) : (
                                    <View></View>
                                )}

                                {simSerialType ==
                                    0 ? (
                                    <View
                                        style={{
                                            flex: 1,
                                            flexDirection:
                                                'row'
                                        }}>
                                        <SearchInput
                                            width={
                                                constants.width -
                                                constants.getSize(
                                                    100
                                                )
                                            }
                                            height={40}
                                            placeholder={
                                                translate('activeSimManager.search_input_sim_serial')
                                            }
                                            rightComponent={
                                                !isUploadSucess && [
                                                    {
                                                        source: {
                                                            uri: 'ic_barcode_input'
                                                        },
                                                        onpress:
                                                            () => { setIsScanSerial() }
                                                    }
                                                ]
                                            }
                                            value={
                                                simSerial
                                            }
                                            onChangeText={(
                                                text
                                            ) => {
                                                let validateSim =
                                                    new RegExp(
                                                        /^[0-9]*$/
                                                    );
                                                if (
                                                    validateSim.test(
                                                        text
                                                    ) ||
                                                    text ==
                                                    ''
                                                ) {
                                                    setSimSerialText(text)
                                                }
                                            }}
                                            returnKeyType="search"
                                            keyboardType={
                                                'numeric'
                                            }
                                            onSubmitEditing={getSerialSim(simSerial)}
                                            editable={
                                                !isUploadSucess
                                            }
                                        />
                                        {
                                            isScanSerial &&
                                            <BarcodeCamera
                                                isModal={true}
                                                isVisible={isScanSerial}
                                                closeCamera={() => { setNotIsScanSerial() }}
                                                resultScanBarcode={(barcode) => {
                                                    if (dataSIMProcessInfo.cus_IsVina) {
                                                        barcode = barcode.substr(0, 19);
                                                    }
                                                    setSimSerialBarcode(barcode);
                                                }}
                                            />
                                        }
                                        <TouchableOpacity
                                            style={{
                                                height: 40,
                                                width: 70,
                                                backgroundColor:
                                                    '#288AD6',
                                                justifyContent:
                                                    'center',
                                                alignItems:
                                                    'center',
                                                marginLeft: 10,
                                                borderRadius: 4,
                                                opacity: isUploadSucess || validateSimSerial ? 0.5 : 1
                                            }}
                                            onPress={getSerialSim(simSerial)}
                                            disabled={
                                                isUploadSucess ||
                                                validateSimSerial
                                            }>
                                            <MyText
                                                text={
                                                    translate('activeSimManager.enter')
                                                }
                                                style={{
                                                    color: 'white'
                                                }}
                                            />
                                        </TouchableOpacity>
                                    </View>
                                ) : (
                                    <View
                                        style={{
                                            flex: 1,
                                            flexDirection:
                                                'row'
                                        }}>
                                        <SearchInput
                                            width={
                                                constants.width -
                                                constants.getSize(
                                                    100
                                                )
                                            }
                                            height={40}
                                            value={
                                                simSerial
                                            }
                                            placeholder={
                                                translate('activeSimManager.get_sim_serial')
                                            }
                                            returnKeyType="search"
                                            editable={false}
                                        />

                                        <TouchableOpacity
                                            style={{
                                                height: 40,
                                                width: 70,
                                                backgroundColor:
                                                    '#288AD6',
                                                justifyContent:
                                                    'center',
                                                alignItems:
                                                    'center',
                                                marginLeft: 10,
                                                borderRadius: 4,
                                                opacity:
                                                    isUploadSucess || validateSimSerial ? 0.5 : 1
                                            }}
                                            onPress={() => { setNoErrorGetSerialEsim() }}
                                            disabled={
                                                isUploadSucess || validateSimSerial || IsEdit
                                            }>
                                            <MyText
                                                text={
                                                    translate('common.btn_notify_try_again')
                                                }
                                                style={{
                                                    color: 'white'
                                                }}
                                            />
                                        </TouchableOpacity>
                                    </View>
                                )}

                                {!validateSimSerial &&
                                    !!errorGetSimSerial && (
                                        <MyText
                                            text={
                                                errorGetSimSerial
                                            }
                                            style={{
                                                color: 'red',
                                                marginTop: 5
                                            }}
                                        />
                                    )}
                                {helper.isArray(
                                    dataSIMProcessInfo.lstPackagesType
                                ) && (
                                        <View
                                            style={{
                                                paddingHorizontal: 10
                                            }}>
                                            <MyText
                                                text={
                                                    translate('activeSimManager.package_2')
                                                }
                                                style={{
                                                    color: '#333333',
                                                    fontWeight:
                                                        'bold',
                                                    marginTop: 10
                                                }}
                                            // children={
                                            //     <MyText
                                            //         text={dataSIMProcessInfo.SIMProcessRequestDetail.PackagesTypeName}
                                            //         style={{
                                            //             color: "#333333"
                                            //         }}
                                            //     />
                                            // }
                                            />
                                            <PickerSearch
                                                label={"PackagesTypeName"}
                                                value={"PackagesTypeID"}
                                                data={dataSIMProcessInfo.lstPackagesType}
                                                valueSelected={PackagesTypeID}
                                                onChange={(item) => { setPackagesTypeID(item) }}
                                                defaultLabel={translate('activeSimManager.select_package')}
                                                style={{
                                                    flex: 1,
                                                    flexDirection: "row",
                                                    justifyContent: "center",
                                                    alignItems: "center",
                                                    marginVertical: 5,
                                                    borderWidth: 1,
                                                    borderRadius: 4,
                                                    borderColor: '#CCCCCC',
                                                    backgroundColor: 'white',
                                                    minHeight: 40
                                                }}
                                                disabled={
                                                    dataSIMProcessInfo.IsConnected
                                                }
                                            />
                                        </View>
                                    )}
                            </View>

                            <View
                                style={{
                                    justifyContent:
                                        'center',
                                    padding: 8,
                                    backgroundColor:
                                        '#2FB47C',
                                    width: constants.width,
                                    height: 40
                                }}>
                                <MyText
                                    addSize={2}
                                    text={
                                        translate('activeSimManager.customer_information')
                                    }
                                    style={{
                                        fontWeight: 'bold',
                                        color: 'white'
                                    }}
                                />
                            </View>
                            {
                                <BaseContainer
                                    content={
                                        <View
                                            style={{
                                                flex: 1
                                            }}>
                                            <View
                                                style={{
                                                    flex: 1,
                                                    paddingVertical: 10
                                                }}>
                                                <View
                                                    style={{
                                                        paddingHorizontal: 10
                                                    }}>
                                                    <MyText
                                                        text={
                                                            translate('activeSimManager.full_name')
                                                        }
                                                        style={{
                                                            fontWeight:
                                                                'bold',
                                                            color: '#333333'
                                                        }}
                                                        children={
                                                            <MyText
                                                                text={
                                                                    '*'
                                                                }
                                                                style={{
                                                                    color: 'red'
                                                                }}
                                                            />
                                                        }
                                                    />
                                                    <FieldInput
                                                        styleInput={{
                                                            // flex: 1,
                                                            borderWidth: 1,
                                                            borderRadius: 4,
                                                            borderColor:
                                                                '#CCCCCC',
                                                            paddingHorizontal: 10,
                                                            marginBottom: 10,
                                                            backgroundColor:
                                                                'white',
                                                            paddingVertical: 8
                                                        }}
                                                        value={
                                                            updateCustomerName
                                                        }
                                                        placeholder={"Vui lòng nhập Họ và Tên: "}
                                                        onChangeText={(text) => { setCustomerName(text) }}
                                                        editable={true}
                                                        width={
                                                            constants.width -
                                                            20
                                                        }
                                                        height={
                                                            40
                                                        }
                                                        clearText={() => { setCustomerName('') }}
                                                    />

                                                    {/* Field cccd */}
                                                    {
                                                        isShowCustomerID &&
                                                        <View>
                                                            <MyText
                                                                text={
                                                                    "CCCD/GCC/HC của khách hàng"
                                                                }
                                                                style={{
                                                                    fontWeight:
                                                                        'bold',
                                                                    color: '#333333'
                                                                }}
                                                                children={
                                                                    <MyText
                                                                        text={
                                                                            '*'
                                                                        }
                                                                        style={{
                                                                            color: 'red'
                                                                        }}
                                                                    />
                                                                }
                                                            />
                                                            <View style={{
                                                                flexDirection: 'row'
                                                            }}>
                                                                <FieldInput
                                                                    styleInput={{
                                                                        borderWidth: 1,
                                                                        borderRadius: 4,
                                                                        borderColor:
                                                                            '#CCCCCC',
                                                                        paddingHorizontal: 10,
                                                                        marginBottom: 10,
                                                                        backgroundColor:
                                                                            'white',
                                                                        paddingVertical: 8
                                                                    }}
                                                                    value={updateCustomerIDCard}
                                                                    placeholder={"Vui lòng nhập CCCD/GCC/HC của khách hàng"}
                                                                    onChangeText={(text) => {
                                                                        const regExpIDCard = new RegExp(/^\w{0,12}$/);
                                                                        const isValidate =
                                                                            regExpIDCard.test(text) || text == '';
                                                                        if (isValidate) {
                                                                            setUpdateCustomerIDCard(text);
                                                                        }
                                                                    }}
                                                                    editable={true}
                                                                    keyboardType="default"
                                                                    returnKeyType="done"
                                                                    blurOnSubmit
                                                                    width={constants.width - 145}
                                                                    height={40}
                                                                    clearText={() => setUpdateCustomerIDCard('')}
                                                                />
                                                                <Button
                                                                    text={translate('shoppingCart.btn_check')}
                                                                    onPress={() => handleCheckDocument(updateCustomerIDCard, sIMProcessRequestID, simSerial)}
                                                                    styleContainer={{
                                                                        width: '30%',
                                                                        borderRadius: 4,
                                                                        backgroundColor: COLORS.txt147EFB,
                                                                        height: 44,
                                                                        marginLeft: 10
                                                                    }}
                                                                    disable={isDisable}
                                                                    styleText={{
                                                                        color: COLORS.txtFFFFFF,
                                                                        fontSize: 14,
                                                                        fontWeight: 'bold'
                                                                    }}
                                                                />
                                                            </View>
                                                        </View>
                                                    }

                                                </View>
                                                {dataSIMProcessInfo.cus_IsVinaPG ? (
                                                    <View
                                                        style={{
                                                            paddingHorizontal: 10
                                                        }}>
                                                        <MyText
                                                            text={
                                                                'PG: '
                                                            }
                                                            style={{
                                                                fontWeight:
                                                                    'bold',
                                                                color: '#333333'
                                                            }}
                                                            children={
                                                                <MyText
                                                                    text={
                                                                        '*'
                                                                    }
                                                                    style={{
                                                                        color: 'red'
                                                                    }}
                                                                />
                                                            }
                                                        />
                                                        <PickerSearch
                                                            label={"PGName"}
                                                            value={"PGID"}
                                                            data={dataSIMProcessInfo.PGBOList}
                                                            valueSelected={PGID}
                                                            onChange={(item) => { setPGID(item) }}
                                                            defaultLabel={translate('activeSimManager.select_pg')}
                                                            style={{
                                                                flex: 1,
                                                                flexDirection: "row",
                                                                justifyContent: "center",
                                                                alignItems: "center",
                                                                marginVertical: 5,
                                                                borderWidth: 1,
                                                                borderRadius: 4,
                                                                borderColor: '#CCCCCC',
                                                                backgroundColor: 'white',
                                                                minHeight: 40
                                                            }}
                                                            disabled={IsEdit}
                                                        />
                                                    </View>
                                                ) : (
                                                    <View></View>
                                                )}
                                                <View
                                                    style={{
                                                        flex: 1,
                                                        justifyContent:
                                                            'center',
                                                        alignItems:
                                                            'center'
                                                    }}>
                                                    {
                                                        IsCreateNewSP == false ?
                                                            (
                                                                isAllowEditSP == true &&
                                                                <Button
                                                                    text={
                                                                        translate('activeSimManager.btn_update')
                                                                    }
                                                                    styleContainer={{
                                                                        width: constants.getSize(
                                                                            180
                                                                        ),
                                                                        height: constants.getSize(
                                                                            50
                                                                        ),
                                                                        backgroundColor:
                                                                            '#288AD6',
                                                                        borderRadius:
                                                                            constants.getSize(
                                                                                4
                                                                            ),
                                                                        marginVertical: 10,
                                                                        opacity:
                                                                            isDisable && !isCheckSIMType
                                                                                ? 0.5
                                                                                : 1
                                                                    }}
                                                                    styleText={{
                                                                        color: '#FFFFFF',
                                                                        fontSize: 14
                                                                    }}
                                                                    disabled={isCheckSIMType ? false : isDisable}
                                                                    onPress={() => {
                                                                        let messageerror =
                                                                            '';
                                                                        messageerror =
                                                                            checkValidateSimProcess();
                                                                        if (
                                                                            messageerror ==
                                                                            ''
                                                                        ) {
                                                                            updateDataSIMConnectype(updateCustomerIDCard, updateCustomerName);
                                                                        } else {
                                                                            Alert.alert(
                                                                                translate('common.notification_uppercase'),
                                                                                messageerror,
                                                                                [
                                                                                    {
                                                                                        text: 'OK',
                                                                                        onPress:
                                                                                            () => { },
                                                                                        style: 'default'
                                                                                    }
                                                                                ],
                                                                                {
                                                                                    cancelable: true
                                                                                }
                                                                            );
                                                                        }
                                                                    }}
                                                                />
                                                            )
                                                            :
                                                            (
                                                                isAllowEditSP == true &&
                                                                <Button
                                                                    text={
                                                                        "Tạo YCXL SIM"
                                                                    }
                                                                    styleContainer={{
                                                                        width: constants.getSize(
                                                                            180
                                                                        ),
                                                                        height: constants.getSize(
                                                                            50
                                                                        ),
                                                                        backgroundColor:
                                                                            '#288AD6',
                                                                        borderRadius:
                                                                            constants.getSize(
                                                                                4
                                                                            ),
                                                                        marginVertical: 10,
                                                                        opacity:
                                                                            isDisable && !isCheckSIMType
                                                                                ? 0.5
                                                                                : 1
                                                                    }}
                                                                    styleText={{
                                                                        color: '#FFFFFF',
                                                                        fontSize: 14
                                                                    }}
                                                                    disabled={isCheckSIMType ? false : isDisable}
                                                                    onPress={() => {
                                                                        let messageerror =
                                                                            '';
                                                                        messageerror =
                                                                            checkValidateSimProcess();
                                                                        if (
                                                                            messageerror ==
                                                                            ''
                                                                        ) {
                                                                            createDataSIMConnectype(updateCustomerIDCard, updateCustomerName);
                                                                        } else {
                                                                            Alert.alert(
                                                                                translate('common.notification_uppercase'),
                                                                                messageerror,
                                                                                [
                                                                                    {
                                                                                        text: 'OK',
                                                                                        onPress:
                                                                                            () => { },
                                                                                        style: 'default'
                                                                                    }
                                                                                ],
                                                                                {
                                                                                    cancelable: true
                                                                                }
                                                                            );
                                                                        }
                                                                    }}
                                                                />
                                                            )
                                                    }
                                                </View>
                                            </View>
                                        </View>
                                    }></BaseContainer>
                            }
                        </View>
                    )
                }

            </KeyboardAwareScrollView>
        </View>)
}

const checkCustomerID = (BrandID, StoreID) => {
    if (BrandID == BRAND_ID_OF_SIM.VINA) return true
    if (BrandID == BRAND_ID_OF_SIM.VIETTEL) return true
    return false
}