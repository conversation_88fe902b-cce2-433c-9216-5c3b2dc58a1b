import {
    Alert,
    Platform,
    SafeAreaView,
    StyleSheet,
    View
} from 'react-native';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { COLORS } from '../../../styles';
import { hideB<PERSON><PERSON>, showBlock<PERSON> } from '../../../components';
import { helper } from '../../../common';
import { ScrollView } from 'react-native-gesture-handler';
import { useNavigation } from '@react-navigation/native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { checkAllowUser, checkUserCapacityRemaining, getPriceAndPromotion } from '../action';
import { useSelector } from 'react-redux';
import { translate } from '../../../translations';
import { checkInstalledQTV } from '../../AnKhangNew/action';
import { TYPE_PROFILE } from '../../../constants/constants';
import { getCustomerProfile } from '../../ShoppingCart/action';
import ActionButton from '../components/ActionButton';
import CustomerInfo from '../components/CustomerInfo';
import Payment from '../components/Payment';
import ProductList from '../components/ProductList';
import MemoryCapacities from '../components/MemoryCapacities';
import ProductColor from '../components/ProductColor';
import Title from '../components/Title';
import InfoImageAndPrice from '../components/InfoImageAndPrice';
import PromotionGroup from '../components/PromotionGroup';

const CollectInformation = ({ route }) => {

    // useState 
    const [productSelected, setProductSelected] = useState({});
    const [sizeSelected, setSizeSelected] = useState({});
    const [colorSelected, setColorSelected] = useState({});
    const [priceProduct, setPriceProduct] = useState({});
    const [promotionSelected, setPromotionSelected] = useState({});
    const [paymentInstallment, setPaymentInstallment] = useState(0);
    const [stateCustomer, setStateCustomer] = useState({
        gender: null,
        customerName: '',
        customerPhone: '',
        isAgreePolicy: 0
    });

    // userReducer
    const { storeID, languageID, moduleID, userName } = useSelector((_state) => _state.userReducer)

    // useNavigation
    const navigation = useNavigation();

    // params
    const { infoPreOrder } = route?.params ?? { infoPreOrder: {} }

    const baseBody = useMemo(() => ({
        loginStoreId: storeID,
        languageID,
        moduleID,
        saleScenarioTypeID: 1,
    }), [storeID, languageID, moduleID]);

    // useEffect
    useEffect(() => {
        if (!helper.IsNonEmptyArray(infoPreOrder.PRODUCTBO)) return;
        const productDefault = infoPreOrder.PRODUCTBO[0]
        const sizeDefault = infoPreOrder.PRODUCTBO[0]?.productGroups?.[0]
        const colorDefault = sizeDefault?.colors?.[0];
        setProductSelected(productDefault);
        setSizeSelected(sizeDefault);
        setColorSelected(colorDefault);
        handleSelectionChange(colorDefault)
    }, [infoPreOrder]);


    // functions
    const handleSelectionChange = useCallback((selection) => {
        if (helper.IsEmptyObject(selection)) return;
        showBlockUI();
        const BODY_PRICE = {
            ...baseBody,
            "deliveryTypeID": 1,
            "inventoryStatusID": null,
            "storeID": storeID,
            "saleProgramID": 0,
            "isInstallment": false,
            "keyword": selection?.productCode,
            "saleOrderID": "",
            "productRequest": null,
            "isGetInfoByIMEI": false,
            "isGetTechSpecs": false,
            "specialSaleProgram": infoPreOrder.SPECIALSALEPROGRAM,
            "isGetPrice": infoPreOrder.EXTENSIONSBO?.ISSHOWPRICE

        }
        const BODY_PROMOTION = {
            ...baseBody,
            "imei": null,
            "productID": selection?.productCode,
            "inventoryStatusID": 1,
            "price": 0,
            "VAT": 0,
            "VATPercent": 0,
            "storeID": storeID,
            "appliedQuantity": 1,
            "outputStoreID": storeID,
            "deliveryTypeID": 1,
            "storeRequests": [],
            "saleProgramID": 0,
            "cartRequest": {},
            "isSpecialSaleProgram": false,
            "isExistStoreChange": false,
            "isGetPromotion": infoPreOrder.EXTENSIONSBO?.ISSHOWPROMOTION,
            "specialSaleProgram": infoPreOrder.SPECIALSALEPROGRAM,

        }
        getPriceAndPromotion({ bodyPrice: BODY_PRICE, bodyPromotion: BODY_PROMOTION }).then(({ responsePrice, responsePromotion }) => {
            hideBlockUI()
            setPriceProduct(responsePrice);
            setPromotionSelected(responsePromotion);
        }).catch((msgError) => {
            Alert.alert(
                translate('common.notification_uppercase'),
                msgError,
                [
                    {
                        text: translate('saleExpress.retry'),
                        style: 'cancel',
                        onPress: () => { handleSelectionChange(selection) }
                    },
                    {
                        text: translate('common.btn_skip'),
                        style: 'cancel',
                        onPress: () => {
                            hideBlockUI();
                            navigation.goBack()
                        }
                    }

                ]
            );
        })
    }, [baseBody, infoPreOrder])

    const onChangeItem = useCallback((itemId) => {
        if (helper.IsEmptyString(itemId)) return;
        const itemSelected =
            infoPreOrder.PRODUCTBO.find((item) => item.productId === itemId) || {};
        const sizeDefault = itemSelected?.productGroups?.[0];
        const colorDefault = sizeDefault?.colors?.[0];
        setProductSelected(itemSelected);
        setSizeSelected(sizeDefault);
        setColorSelected(colorDefault);
        handleSelectionChange(colorDefault)

    }, [infoPreOrder.PRODUCTBO])

    const onChangeSize = useCallback((sizeId) => {
        if (helper.IsEmptyString(sizeId)) return;
        const sizeSelected =
            productSelected.productGroups?.find((item) => item.productGroupId === sizeId) || {};
        const colorDefault = sizeSelected?.colors?.[0];
        setSizeSelected(sizeSelected);
        setColorSelected(colorDefault);
        handleSelectionChange(colorDefault)

    }, [productSelected.productGroups])

    const onChangeColor = useCallback((colorId) => {
        if (helper.IsEmptyString(colorId)) return;
        const colorSelected =
            sizeSelected.colors?.find((item) => item.colorId === colorId) || {};
        setColorSelected(colorSelected);
        handleSelectionChange(colorSelected)

    }, [sizeSelected.colors])

    const handlePayment = useCallback(() => {
        setPaymentInstallment((preState) => !preState);
    }, [])

    const updateCustomerInfo = useCallback((value) =>
        setStateCustomer((prevState) => ({ ...prevState, ...value })), [])

    const validateCustomerInfo = () => {
        const { customerPhone, customerName, gender, isAgreePolicy } = stateCustomer;

        const validations = [
            { condition: gender === null, message: "Vui lòng nhập chọn giới tính khách hàng." },
            { condition: helper.IsEmptyString(customerPhone), message: "Vui lòng nhập số điện thoại khách hàng." },
            { condition: helper.IsEmptyString(customerName), message: "Vui lòng nhập tên khách hàng." },
            { condition: !helper.isValidatePhone(customerPhone), message: translate('shoppingCart.validation_phone_number') },
            { condition: !isAgreePolicy, message: "Vui lòng đồng ý chính sách xử lý dữ liệu cá nhân." },
        ];

        for (let { condition, message } of validations) {
            if (condition) {
                Alert.alert("", message);
                return false;
            }
        }

        return true;
    };


    const handleInformation = async () => {

        const { customerPhone, customerName, gender } = stateCustomer

        if (!validateCustomerInfo()) return
        showBlockUI()

        const customerOTP = {
            customerPhone: customerPhone,
            customerName: customerName
        };

        const dataInsert = {
            ...baseBody,
            "saleScenarioTypeID": 0,
            "CUSTOMERBO": {
                "GEND": gender,
                "PHONENUMBER": customerPhone,
                "NAME": customerName
            },
            "EXTENSIONSBO": {
                "STORE": storeID,
                "INSTALLMENT": +paymentInstallment
            },
            "PRODUCTBO": {
                "PRODUCTID": colorSelected.productCode
            },
            "specialSaleProgram": infoPreOrder.SPECIALSALEPROGRAM
        }
        const body = {
            ...baseBody,
            "loginUser": userName,
            "saleProgramId": helper.getProgramPreByConfig(),
            "phaseId": infoPreOrder.SPECIALSALEPROGRAM,
            "customerPhone": customerPhone,
        }
        const isAllow = await checkAllowUser(body)
        if (isAllow) {
            checkInstalledQTV(customerPhone).then((isExistApp) => {
                hideBlockUI()
                navigation.navigate('SendOTP', { customerInfo: customerOTP, dataInsert, isExistApp });
            }).catch((err) => {
                hideBlockUI()
                navigation.navigate('SendOTP', { customerInfo: customerOTP, dataInsert, isExistApp: false });
            });
        }
        else {
            hideBlockUI()
            Alert.alert("", "Khách hàng đã hết suất đặt hàng.");
        }


    };

    const getInfoProfileByCRM = useCallback(() => {
        const { customerPhone } = stateCustomer
        const regExpPhone = new RegExp(/^[0]\d{9}$/);
        const isValidate = regExpPhone.test(customerPhone);
        if (isValidate) {
            showBlockUI()
            getCustomerProfile({ ...baseBody, phoneNumber: customerPhone, typeProfile: TYPE_PROFILE.CUSTOMER }).then(customerProfile => {
                hideBlockUI()
                const customerInfo = customerProfile[0]
                const newStateCustomer = {
                    gender: customerInfo?.gender,
                    customerName: customerInfo?.customerName,
                    customerPhone: customerPhone,
                    isAgreePolicy: customerInfo.isSigned
                }
                setStateCustomer((prevState) => ({ ...prevState, ...newStateCustomer }));

            }).catch(() => {
                hideBlockUI();
            })

        }
    }, [stateCustomer.customerPhone])

    const title = useMemo(() => {
        return colorSelected.productName
    }, [colorSelected])

    return (

        <SafeAreaView style={styles.container_wrapper}>
            <ScrollView
                contentContainerStyle={{ flexGrow: 1, }}
                contentInsetAdjustmentBehavior="automatic"
                behavior="padding"
                showsVerticalScrollIndicator={false}>
                <KeyboardAwareScrollView
                    style={styles.container}
                    behavior={Platform.OS === 'ios' ? 'padding' : null}
                    keyboardVerticalOffset={Platform.OS === 'ios' ? 96 : 0}>
                    {
                        !helper.IsEmptyObject(infoPreOrder.PRODUCTBO) && (
                            <>
                                <Title addSize={4} title={title || "No Name"} />
                                <View style={styles.container_info}>
                                    <InfoImageAndPrice
                                        product={colorSelected}
                                        priceProduct={priceProduct} />
                                    {infoPreOrder.EXTENSIONSBO?.ISSHOWPROMOTION ?
                                        <PromotionGroup promotion={promotionSelected} /> :
                                        <View style={{ flex: 5.5 }} />
                                    }
                                </View>
                                <ProductList
                                    DATA_PRODUCT={infoPreOrder.PRODUCTBO}
                                    productSelected={productSelected}
                                    onChangeItem={onChangeItem}
                                />
                                <MemoryCapacities
                                    productSelected={productSelected}
                                    sizeSelected={sizeSelected}
                                    onChangeSize={onChangeSize}
                                />
                                <ProductColor
                                    sizeSelected={sizeSelected}
                                    onChangeColor={onChangeColor}
                                    colorSelected={colorSelected}
                                />
                            </>
                        )
                    }
                    {!helper.IsEmptyObject(infoPreOrder.EXTENSIONSBO) &&
                        <Payment
                            onPressPayment={handlePayment}
                            isCheck={paymentInstallment}
                            EXTENSIONSBO={infoPreOrder.EXTENSIONSBO}
                        />}

                    {!helper.IsEmptyObject(infoPreOrder.CUSTOMERBO) &&
                        <CustomerInfo
                            stateCustomer={stateCustomer}
                            updateCustomerInfo={updateCustomerInfo}
                            CUSTOMERBO={infoPreOrder.CUSTOMERBO}
                            getInfoProfileByCRM={getInfoProfileByCRM}

                        />}
                    <ActionButton handleInformation={handleInformation} />
                </KeyboardAwareScrollView>
            </ScrollView>

        </SafeAreaView>
    );
};

export default CollectInformation;

const styles = StyleSheet.create({
    container_wrapper: {
        marginBottom: 20,
        flex: 1,
        backgroundColor: COLORS.bgFFFFFF
    },
    container: {
        flex: 1,
        marginHorizontal: 10
    },
    container_info: {
        flexDirection: 'row',
        paddingVertical: 10,

    },








});










