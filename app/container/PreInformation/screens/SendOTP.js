

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { SafeAreaView, StyleSheet, Alert, Keyboard } from 'react-native';
import { connect } from 'react-redux';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { showBlockUI, hideBlockUI } from '@components';
import { helper } from '@common';
import * as actionShoppingCartCreator from '../../ShoppingCart/action';
import { COLORS } from '@styles';
import { translate } from '@translate';
import Customer from '../../ShoppingCart/component/InstallmentOTP/component/Customer';
import OtpCode from '../../ShoppingCart/component/InstallmentOTP/component/OtpCode';
import Guide from '../../ShoppingCart/component/InstallmentOTP/component/Guide';
import { insertInfo } from '../action';

const SendOTP = ({ route, userInfo, navigation }) => {
    const [info, setInfo] = useState({
        customerPhone: '',
        customerName: ''
    });
    const [expireTime, setExpireTime] = useState(0);
    const [otpCode, setOtpCode] = useState('');
    const [onlySms, setOnlySms] = useState(false);
    const [isCall, setIsCall] = useState(false);

    const intervalId = useRef(null);

    useEffect(() => {
        hideBlockUI();
        const { customerInfo = { customerPhone: '', customerName: '' }, isExistApp = false } = route?.params || {};
        setInfo(customerInfo);
        setOnlySms(!isExistApp);

        return () => {
            if (intervalId.current) {
                clearInterval(intervalId.current);
            }
        };
    }, [route?.params]);

    const countDown = useCallback(() => {
        setExpireTime(prevExpireTime => {
            const newExpireTime = prevExpireTime - 1;
            if (newExpireTime > 0) return newExpireTime;
            resetCountDown();
            return 0;
        });
    }, []);

    const resetCountDown = useCallback(() => {
        if (!isCall) {
            setOnlySms(true);
        }
        if (intervalId.current) {
            clearInterval(intervalId.current);
        }
        setExpireTime(0);
        setOtpCode('');
    }, [isCall]);

    const setCountDown = useCallback(() => {
        setExpireTime(60);
        intervalId.current = setInterval(countDown, 1000);
    }, [countDown]);

    const onCreateOTP = useCallback(async (type) => {
        const { customerPhone } = info;
        const { brandID, typeOTP } = userInfo;
        setIsCall(type === 'CALLCENTER');
        setCountDown();

        try {
            showBlockUI();
            const isSMS = await actionShoppingCartCreator.createOTP({
                type, phoneNumber: customerPhone, typeContent: "PREINFORMATION", lenOtp: 4, brandID, onlySms
            });
            if (!isCall) {
                setOnlySms(isSMS);
            }
            hideBlockUI();
        } catch (msgError) {
            resetCountDown();
            Alert.alert(
                translate('common.notification_uppercase'),
                msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: 'cancel',
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: 'default',
                        onPress: () => onCreateOTP(type)
                    }
                ]
            );
        }
    }, [info, userInfo, isCall, setCountDown, resetCountDown]);

    const onCheckOTP = useCallback(() => {
        Keyboard.dismiss();
        if (validateOTP(otpCode)) {
            verifyOTP(otpCode, info.customerPhone);
        }
    }, [otpCode, info.customerPhone, validateOTP]);

    const verifyOTP = useCallback(async (otpCode, customerPhone) => {
        showBlockUI();
        try {
            const data = await actionShoppingCartCreator.verifyOTP(otpCode, customerPhone);
            insertInfoToCRM();
        } catch (msgError) {
            Alert.alert(
                translate('common.notification_uppercase'),
                msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: 'cancel',
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: 'default',
                        onPress: () => verifyOTP(otpCode, customerPhone)
                    }
                ]
            );
        }
    }, [insertInfoToCRM]);

    const validateOTP = useCallback((code) => {
        const regExpOTP = /^\d{4}$/;
        if (!helper.IsNonEmptyString(code)) {
            Alert.alert('', translate('shoppingCart.validate_empty_otp'));
            return false;
        }
        if (!regExpOTP.test(code)) {
            Alert.alert('', translate('shoppingCart.validate_otp'));
            return false;
        }
        return true;
    }, []);

    const insertInfoToCRM = useCallback(async () => {
        const { dataInsert = {} } = route?.params || {};
        try {
            await insertInfo({ ...dataInsert });
            Alert.alert(
                "",
                "Ghi nhận phiếu thành công.",
                [
                    {
                        text: "Ok",
                        style: 'cancel',
                        onPress: () => {
                            hideBlockUI();
                            navigation.navigate("PreInformation");
                        }
                    }
                ]
            );
        } catch (msgError) {
            Alert.alert(
                translate('common.notification_uppercase'),
                msgError,
                [
                    {
                        text: translate('saleExpress.retry'),
                        style: 'cancel',
                        onPress: insertInfoToCRM
                    },
                    {
                        text: translate('common.btn_skip'),
                        style: 'cancel',
                        onPress: hideBlockUI
                    }
                ]
            );
        }
    }, [route?.params, navigation]);

    return (
        <KeyboardAwareScrollView
            style={styles.scrollView}
            enableResetScrollToCoords={false}
            keyboardShouldPersistTaps="always"
            bounces={false}
            overScrollMode="always"
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            extraScrollHeight={60}>
            <SafeAreaView style={styles.safeAreaView}>
                <Guide type="PREORDER" />
                <Customer info={info} />
                <OtpCode
                    onCreate={onCreateOTP}
                    expireTime={expireTime}
                    code={otpCode}
                    onChange={text => {
                        const regExpOTP = /^\d{0,4}$/;
                        if (regExpOTP.test(text)) {
                            setOtpCode(text);
                        }
                    }}
                    onVerify={onCheckOTP}
                    onlySms={onlySms}
                />
            </SafeAreaView>
        </KeyboardAwareScrollView>
    );
};

const styles = StyleSheet.create({
    scrollView: {
        flex: 1,
        backgroundColor: COLORS.bgFFFFFF
    },
    safeAreaView: {
        flex: 1
    }
});

const mapStateToProps = state => ({
    userInfo: state.userReducer
});

export default connect(mapStateToProps)(SendOTP);
