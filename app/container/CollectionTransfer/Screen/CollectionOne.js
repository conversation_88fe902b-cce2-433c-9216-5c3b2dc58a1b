import { View, Animated, TouchableOpacity, Keyboard, Alert } from 'react-native';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import SafeAreaView from 'react-native-safe-area-view';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { date<PERSON><PERSON>per, helper } from '@common';
import { translate } from '@translate';
import { API_CONST, constants, ENUM } from '@constants';
import { COLORS } from '@styles';
import {
    CameraDOT,
    MyText,
    RadioButton,
    TitleInput,
    CaptureCamera,
    showBlockUI,
    hideBlockUI,
    Button,
    PickerSearch,
    BaseLoading
} from '@components';
import CheckAccountNumber from '../component/CheckAccountNumber';
import InputCheckAMount from '../component/InputCheckAmount';
import * as actionCollectionCreator from "../../CollectionTransfer/action";
import * as installmentAction from "../../InstallmentManagerBC/action";
import { getImageCDN } from "../../ActiveSimManager/action";
const { FILE_PATH: { COLLECTION_ONE } } = ENUM;

class CollectionOne extends Component {
    constructor(props) {
        super(props);
        this.state = {
            scrollY: new Animated.Value(0),
            identificationNumber: '',
            loaderName: '',
            rechargerPhoneNumber: '',
            numberBankAccount: '',
            keyword: '',
            recipientName: '',
            depositAmount: '',
            rechargeFee: '',
            isVisibleCamera: false,
            lstBankAccount: [],
            bankCode: "",
            itemCheckBankAccount: {},
            radioIDCardType: [
                {
                    title: translate("collection.id_card"),
                    sym: translate("collection.sym_id_card"),
                    selected: true,
                    value: 1
                },
                {
                    title: translate("collection.id_card_2"),
                    sym: translate("collection.sym_id_card_2"),
                    selected: false,
                    value: 2
                },
                {
                    title: "Thẻ căn cước",
                    sym: "Thẻ căn cước",
                    selected: false,
                    value: 4
                }
            ],
            cus_FilePathFrontOfIDCard: "",
            cus_FilePathBackOfIDCard: "",
            urlImage: "",
            property: ""
        };
        this.timeOutScroll = null;
        this.isScrolling = false;
        this.keySearch = "";
    }

    componentDidMount() {
        // const { actionCollection } = this.props;
        // actionCollection.clearCustomerBankAccountInfo();
        this.getBankAccountList();
    }

    componentDidUpdate(prevProps) {
        if (this.props.dataQueryCustomerBank !== prevProps.dataQueryCustomerBank) {
            this.setState({
                recipientName: this.props.dataQueryCustomerBank.CustomerName
            })
        }

        // if (this.state.numberBankAccount?.length === 0 && this.state.recipientName?.length > 0) {
        //     this.setState({
        //         recipientName: ""
        //     })
        // }

        if (this.props.dataFeeCashin !== prevProps.dataFeeCashin) {
            this.setState({
                rechargeFee: this.props.dataFeeCashin?.CustFee?.TransFee
            })
        }

    }

    takePictureFE = (imageInfo, property, indexImage, client_IDCardType) => {
        helper.resizeImage(imageInfo).then(({ path, uri, size, name }) => {
            console.log("uri " + uri);
            showBlockUI();
            var urlImg = null;
            this.getBodyUpload({
                uri: uri,
                type: 'image/jpeg',
                name: name,
            }).then(res => {
                urlImg = res.url;
                if (indexImage != 2) {
                    var hardUrl = indexImage == 0 ? "https://drive.google.com/file/d/1z8F5IBLQaVGPLZMgo_s6FGeS72dfOyVT/view"
                        : "https://drive.google.com/file/d/1LnC0PnbDg_kQpqpdcQFN7ZlB-LfbTsPY/view"
                    if (client_IDCardType == 2) {
                        hardUrl = indexImage == 0 ? "http://10.1.12.58:50520/get_origin_image/20210621110839S-3XUskEB5e6BcVyFU4hDPC7-EiFjTpKbW4gyWiGQB9vWEQ"
                            : "http://10.1.12.58:50520/get_origin_image/20210621110757S-3XUskEB5e6BcVyFU4hDPC7-GDHjJar5yVSA5b5WoTK6y9"
                    }
                    this.getInfoCustomerByImage(uri, indexImage)
                        .then((response) => {
                            console.log("getInfoCustomerByImage response");
                            response.image_url = urlImg;
                            hideBlockUI();
                            console.log(response, property, indexImage, client_IDCardType, "response, property, indexImage,client_IDCardType")
                            const {
                                customerIDCard,
                                customerName
                            } = response;
                            if (indexImage == 0) {
                                this.setState({ identificationNumber: customerIDCard });
                                this.setState({ loaderName: customerName })
                            }
                            this.updateInfoCardToState(response, property, indexImage);
                        })
                        .catch(
                            err => {
                                hideBlockUI();
                                this.setState({
                                    isVisibleCamera: false,
                                    [property]: urlImg
                                });
                            }
                        )
                }
                else {
                    hideBlockUI();
                    setState({
                        isVisibleCamera: false,
                        ...state.EPOSTransactionBO,
                        [property]: urlImg
                    });
                }
            }
            )
        }).catch((error) => {
            console.log("resizeImage", error);
        });
    }

    updateInfoCardToState = (objInfoCard, property, indexImage) => {
        //0: mt
        //1: ms
        //2: chan dung
        console.log("obj:", property);
        console.log(objInfoCard.customerName, "objInfoCard.customerName");
        console.log(objInfoCard);
        if (indexImage == 0) {
            var customerName = '';
            var middleName = '';
            var firstName = null;
            var lastName = null;
            if (objInfoCard.customerName != undefined && objInfoCard.customerName.length > 0) {
                customerName = objInfoCard.customerName.split(' ');
                if (customerName.length > 0) {
                    firstName = customerName[customerName.length - 1];
                    lastName = customerName.length > 1 ? customerName[0] : '';
                    if (customerName.length > 2) {
                        for (var i = 1; i < customerName.length - 1; i++) {
                            middleName += customerName[i] + ' ';
                        }
                        //Nếu têm đệm hơn 15 ký tự thì sẽ không gán vào cả họ, tên, tên đệm 
                        if (middleName.toString().trim().length < 15) {
                            middleName = middleName.toString().trim();
                        }
                        else {
                            firstName = '';
                            middleName = '';
                            lastName = '';
                        }
                    }
                }
            }
            var formatDate = helper.IsEmptyObject(objInfoCard.customerBirthday)
                ? null
                : objInfoCard.customerBirthday;
            // : (new Date(objInfoCard.customerBirthday));
            this.setState({
                isVisibleCamera: false,
            });
        }
        var formatIssueDate = helper.IsEmptyObject(objInfoCard.idCardIssueDate)
            ? null
            : objInfoCard.idCardIssueDate;
        // : (new Date(objInfoCard.idCardIssueDate));
        var formatExpiriedDate = helper.IsEmptyObject(objInfoCard.idCardExpiriedDate)
            ? null
            : objInfoCard.idCardExpiriedDate;
        // : (new Date(objInfoCard.idCardExpiriedDate));
        this.setState({
            isVisibleCamera: false,
            [property]: objInfoCard.image_url
        });
        // }
    }



    getInfoCustomerByImage = (uriImage, indexImage) => {
        const {
            userInfo
        } = this.props;
        return new Promise((resolve, reject) => {
            var typeImageDetect = helper.getTypeImage(1, indexImage)
            console.log(typeImageDetect, "typeImageDetect");
            // if (typeImageDetect != 0) {
            let bodyFromData = new FormData();
            bodyFromData.append('file', {
                uri: uriImage,
                type: 'image/jpg',
                name: "getInfoCustomerByImage" + dateHelper.getTimestamp()
            });
            console.log(bodyFromData, "bodyFromData");
            bodyFromData.append('client_id', `MWGPOS_${userInfo.userName}`);
            bodyFromData.append('chosen_side', typeImageDetect);
            console.log(bodyFromData, "bodyFromData");
            console.log(bodyFromData, "bodyFromData==>")
            installmentAction.getInfoByImage(bodyFromData)
                .then(
                    res => {
                        switch (typeImageDetect) {
                            case 1:
                                resolve({
                                    provinceID: res.cap_1_id,
                                    districtID: res.cap_2_id,
                                    wardID: res.cap_3_id,
                                    provinceName: res.cap1,
                                    districtName: res.cap2,
                                    wardName: res.cap3,
                                    customerBirthday: res.date_of_birth,
                                    customerName: res.full_name,
                                    customerAddress: res.place_of_permanent,
                                    customerIDCard: res.id_no,
                                    image_url: res.image_url,
                                    streetAddress: res.cap4,
                                    idCardExpiriedDate: res.expiration_date
                                }
                                );
                                break;
                            case 2:
                                resolve({
                                    provinceID: res.cap_1_id,
                                    districtID: res.cap_2_id,
                                    wardID: res.cap_3_id,
                                    provinceName: res.cap1,
                                    districtName: res.cap2,
                                    wardName: res.cap3,
                                    customerBirthday: res.date_of_birth,
                                    customerName: res.full_name,
                                    customerAddress: res.place_of_permanent,
                                    customerIDCard: res.id_no,
                                    image_url: res.image_url,
                                    streetAddress: res.cap4,
                                    idCardExpiriedDate: res.expiration_date
                                }
                                );
                                break;
                            case 3:
                                resolve({
                                    image_url: res.image_url,
                                    idCardIssueDate: res.date_of_issue,
                                    idCardIssuePlace: res.erp_place_of_issue_id,
                                });
                                break;
                            case 4:
                                resolve({
                                    image_url: res.image_url,
                                    idCardIssueDate: res.date_of_issue,
                                    idCardIssuePlace: res.erp_place_of_issue_id,
                                });
                                break;
                            case 11:
                                resolve({
                                    customerBirthday: res.date_of_birth,
                                    customerName: res.full_name,
                                    customerIDCard: res.id_no,
                                    image_url: res.image_url,
                                }
                                );
                                break;
                            case 12:
                                resolve({
                                    provinceID: res.cap_1_id,
                                    districtID: res.cap_2_id,
                                    wardID: res.cap_3_id,
                                    provinceName: res.cap1,
                                    districtName: res.cap2,
                                    wardName: res.cap3,
                                    streetAddress: res.cap4,
                                    image_url: res.image_url,
                                    idCardIssueDate: res.date_of_issue,
                                    idCardIssuePlace: res.erp_place_of_issue_id,
                                    idCardExpiriedDate: res.expiration_date
                                });
                                break;
                            default:
                                console.log(res)
                                break;
                        }
                    }
                ).catch(
                    err => {
                        console.log("err getInfoCustomerByImage", err);
                        reject(err);
                    }
                );
            // }
        });
    }

    uploadPicture = (fromData) => {
        const { API_GET_IMAGE_CDN, API_GET_IMAGE_CDN_NEW } = API_CONST;
        return new Promise((resolve, reject) => {
            getImageCDN(fromData).then(cdnImages => {
                console.log("uploadPicture url", API_GET_IMAGE_CDN_NEW + cdnImages[0]);
                resolve({ url: `${API_GET_IMAGE_CDN_NEW + cdnImages[0]}` })
            }).catch(error => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    translate('instalmentManager.upload_image_error'),
                    [
                        {
                            text: translate('common.btn_skip'),
                            onPress: hideBlockUI,
                            style: "cancel"
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            onPress: () => this.uploadPicture(fromData),
                            style: "default"
                        },
                    ],
                    { cancelable: false },
                );
            })
        });
    }

    getBodyUpload = (file) => {
        const body = helper.createFormData({ uri: file.uri, type: file.type, name: file.name, path: COLLECTION_ONE });
        return this.uploadPicture(body)
    }

    removePicture = (property, property2) => () => {
        this.setState({
            [property]: "",
            [property2]: "",
        });
    }

    selectItemCardType = (index) => {
        const {
            radioIDCardType
        } = this.state;
        const newRadioIDCardType = radioIDCardType.map((item) => ({
            ...item,
            selected: false
        }));
        newRadioIDCardType[index].selected = true;
        this.setState({ radioIDCardType: newRadioIDCardType });
    };

    getBankAccountList = () => {
        const {
            actionCollection,
            navigation
        } = this.props;
        showBlockUI()
        actionCollection.getBankAccountList().then((reponseBankList) => {
            console.log(reponseBankList, "reponseBankList")
            this.setState({ lstBankAccount: reponseBankList })
            hideBlockUI();
        }).catch(msgError => {
            Alert.alert("", msgError, [{
                text: "OK",
                onPress: () => {
                    navigation.goBack();
                    hideBlockUI()
                }

            }])
        });
    }

    handleCheckBankAccount = () => {
        const {
            identificationNumber,
            loaderName,
            rechargerPhoneNumber,
            bankCode,
            numberBankAccount,
            cus_FilePathFrontOfIDCard,
            cus_FilePathBackOfIDCard
        } = this.state;
        const {
            actionCollection
        } = this.props;
        Keyboard.dismiss();
        // if (cus_FilePathFrontOfIDCard == "") {
        //     Alert.alert("", translate('collection.take_front_id_card'))
        //     return false;
        // }
        // if (cus_FilePathBackOfIDCard == null) {
        //     Alert.alert("", translate("collection.take_back_id_card"))
        //     return false;
        // }
        if (identificationNumber == "") {
            Alert.alert("", translate("collection.enter_card_number"))
            return false;
        }
        if (loaderName == "") {
            Alert.alert("", translate("collection.enter_load_name"))
            return false;
        }
        if (rechargerPhoneNumber == "") {
            Alert.alert("", translate("collection.enter_load_phone_number"))
            return false;
        }
        if (bankCode == "") {
            Alert.alert("", translate("collection.choose_transfer_bank"))
            return false;
        }
        if (numberBankAccount == "") {
            Alert.alert("", translate("collection.enter_account_number"))
            return false;
        } else {
            const data = {
                "Customer": {
                    "Name": loaderName,
                    "IDNumber": "",
                    "IDNo": identificationNumber,
                    "MSISDN": rechargerPhoneNumber,
                    "BenAccountNumber": numberBankAccount
                },
                BenAccNumber: numberBankAccount,
                IsCashInForm: 1,
                BenBankCode: bankCode

            }
            actionCollection.queryCustomerBankAcc(data);
        }
    }

    getFeeCashin = () => {
        Keyboard.dismiss();
        const {
            identificationNumber,
            loaderName,
            rechargerPhoneNumber,
            bankCode,
            numberBankAccount,
            depositAmount
        } = this.state;
        const {
            actionCollection,
            dataQueryCustomerBank
        } = this.props;
        const {
            CustomerName,
            ReponseTransactionIDQC,
            TransIDQC
        } = dataQueryCustomerBank
        // Chú ý một số field đang gán cứng ở đây là do bên phía Service không xử lý được nên trên tần User Interface tạm thời gán cứng trước.
        const data = {
            AirTimeTransactionTypeID: "1513",
            BankCode: bankCode,
            Amount: depositAmount,
            OriginalOrderID: ReponseTransactionIDQC,
            OriginalTransID: TransIDQC,
            Sender: {
                Name: loaderName,
                IDNo: identificationNumber,
                MSISDN: rechargerPhoneNumber
            },
            Receiver: {
                Name: CustomerName,
                BenAccountNumber: numberBankAccount
            }
        }
        const prePaid = (Math.round(depositAmount / 1000) * 1000);
        if (depositAmount == "") {
            Alert.alert("", translate("collection.enter_amount_to_transfer"))
            return false;
        }
        else if (prePaid > depositAmount || prePaid < depositAmount) {
            Alert.alert("", translate("collection.amount_received_must_multiple_of_1000"), [{
                text: "OK",
                onPress: hideBlockUI
            }])
        }
        else {
            showBlockUI();
            actionCollection.getFeeCashinBanks(data).then(() => {
                hideBlockUI();
            }).catch(msgError => {
                Alert.alert("", msgError, [{
                    text: "OK",
                    onPress: hideBlockUI
                }])
            });
        }
    }

    handleNavigationStepTwo = (client_IDCardType) => {
        const {
            navigation,
            dataFeeCashin
        } = this.props;
        const {
            itemCheckBankAccount,
            numberBankAccount,
            recipientName,
            depositAmount,
            rechargeFee,
            loaderName,
            rechargerPhoneNumber,
            identificationNumber,
            cus_FilePathFrontOfIDCard,
            cus_FilePathBackOfIDCard,
        } = this.state;
        const regExpIDCard9 = new RegExp(/^\d{9}$/);
        const regExpIDCard12 = new RegExp(/^\d{12}$/);
        const isValidateIDCard9 = regExpIDCard9.test(identificationNumber);
        const isValidateIDCard12 = regExpIDCard12.test(identificationNumber);
        const isValidateIDCard = isValidateIDCard9 || isValidateIDCard12;
        const regExpPhoneNumber10 = new RegExp(/^\d{10}$/);
        const isValidateRechargerPhoneNumber = regExpPhoneNumber10.test(rechargerPhoneNumber);
        if (!isValidateIDCard) {
            Alert.alert("", translate("collection.check_id_card"));
            return false;
        }
        if (!isValidateRechargerPhoneNumber) {
            Alert.alert("", translate("collection.check_phone"));
            return false;
        }
        if (rechargeFee != "" && dataFeeCashin.CustFee != null) {
            navigation.navigate("CollectionTwo", {
                itemCheckBankAccount,
                numberBankAccount,
                recipientName,
                depositAmount,
                rechargeFee,
                loaderName,
                rechargerPhoneNumber,
                identificationNumber,
                cus_FilePathFrontOfIDCard,
                cus_FilePathBackOfIDCard,
                client_IDCardType
            })
        }
    }

    render() {
        const {
            identificationNumber,
            loaderName,
            rechargerPhoneNumber,
            lstBankAccount,
            numberBankAccount,
            bankCode,
            recipientName,
            depositAmount,
            rechargeFee,
            radioIDCardType,
            isVisibleCamera,

        } = this.state;
        const {
            stateQueryCustomerBank,
            dataFeeCashin,
        } = this.props;
        console.log(this.props.userInfo, "0909")
        const selectedCard = radioIDCardType.find(
            (item) => item.selected
        );
        const client_IDCardType = selectedCard.value;
        console.log("cus_FilePathFrontOfIDCard", this.state.cus_FilePathFrontOfIDCard)
        return (
            <View style={{
                flex: 1,
                backgroundColor: 'white'
            }}>
                <KeyboardAwareScrollView
                    style={{
                        flex: 1,
                    }}
                    enableResetScrollToCoords={false}
                    keyboardShouldPersistTaps="always"
                    bounces={false}
                    overScrollMode="always"
                    showsHorizontalScrollIndicator={false}
                    showsVerticalScrollIndicator={false}
                    extraScrollHeight={60}
                >
                    <SafeAreaView
                        style={{
                            flex: 1,
                            alignItems: 'center'
                        }}>
                        <View
                            style={{
                                width: constants.width - 20,
                                marginTop: 10
                            }}>
                            <MyText style={{
                                marginTop: 10,
                                marginBottom: 10,
                                fontWeight: 'bold',
                                color: COLORS.bg00AAFF,
                                fontSize: 15,
                                fontStyle: 'italic',
                                textDecorationLine: 'underline',
                            }}
                                text={translate("collection.sender_information")}
                            />
                            <View style={{
                                backgroundColor: COLORS.bgFFFFFF,
                                borderRadius: 7,
                                padding: 10,
                                alignItems: 'center',
                                shadowColor: COLORS.bg7F7F7F,
                                shadowOffset: {
                                    width: 0,
                                    height: 0,
                                },
                                shadowOpacity: 0.5,
                                shadowRadius: 1,
                                elevation: 5,
                            }}>
                                <View
                                    style={{ marginVertical: 5, flexDirection: 'row', alignItems: 'center', marginLeft: 10 }}>
                                    <RadioButton
                                        style={{
                                            flexDirection: 'row'
                                        }}
                                        containerStyle={{
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            width: constants.width / 2
                                        }}
                                        dataItems={this.state.radioIDCardType}
                                        selectItem={this.selectItemCardType}
                                        mainComponent={(item) => {
                                            return (
                                                <MyText
                                                    text={item.title}
                                                    style={{
                                                        color: item.selected
                                                            ? COLORS.txtFF8900
                                                            : COLORS.txt333333,
                                                        marginLeft: 2,
                                                        fontSize: 15
                                                    }}
                                                />
                                            );
                                        }}
                                    />
                                </View>
                                <View
                                    style={{
                                        justifyContent: 'center'
                                    }}>
                                    <MyText style={{
                                        marginLeft: 5,
                                        padding: 10
                                    }}
                                        text={`${translate("collection.take_a_shot")} ${selectedCard.sym} ${translate("collection.front")}`}
                                    />
                                    <View
                                        style={{
                                            marginVertical: 0,
                                            paddingHorizontal: 10,
                                            alignItems: 'center',
                                            width: constants.width
                                        }}>
                                        <CameraDOT
                                            uriImage={this.state.cus_FilePathFrontOfIDCard}
                                            onTakePicture={(response) => {
                                                this.takePictureFE(response, "cus_FilePathFrontOfIDCard", 0, client_IDCardType)

                                            }}
                                            onDelete={this.removePicture("cus_FilePathFrontOfIDCard", "client_ImageFrontOfIDCard")}
                                        />
                                    </View>
                                    <MyText
                                        style={{
                                            marginLeft: 5,
                                            padding: 10
                                        }}
                                        text={`${translate("collection.take_a_shot")} ${selectedCard.sym} ${translate("collection.backside")}`}
                                    />
                                    <View
                                        style={{
                                            marginVertical: 0,
                                            paddingHorizontal: 10,
                                            alignItems: 'center',
                                            width: constants.width
                                        }}>
                                        <CameraDOT
                                            uriImage={this.state.cus_FilePathBackOfIDCard}
                                            onTakePicture={(response) => {
                                                this.takePictureFE(response, "cus_FilePathBackOfIDCard", 1, client_IDCardType)
                                            }}
                                            onDelete={this.removePicture("cus_FilePathBackOfIDCard", "client_ImageBackOfIDCard")}
                                        />
                                    </View>
                                </View>
                            </View>
                            <View style={{
                                marginTop: 20,
                            }}>
                                <TitleInput
                                    title={translate("collection.card_number")}
                                    styleInput={{
                                        borderWidth: 1,
                                        borderRadius: 4,
                                        borderColor: COLORS.bdCCCCCC,
                                        marginBottom: 10,
                                        paddingHorizontal: 10,
                                        backgroundColor: COLORS.bgFFFFFF,
                                        paddingVertical: 8
                                    }}
                                    placeholder={translate("collection.placeholder_ID_card_number")}
                                    value={identificationNumber}
                                    onChangeText={(text) => {
                                        let validate = new RegExp(/^\d{0,12}$/);
                                        if (validate.test(text) || text == "") {
                                            this.setState({ identificationNumber: text });
                                        }
                                    }}
                                    keyboardType="numeric"
                                    returnKeyType="done"
                                    blurOnSubmit
                                    width={constants.width - 20}
                                    height={40}
                                    clearText={() => {
                                        this.setState({ identificationNumber: '' });
                                    }}
                                    key="identificationNumber"
                                />
                                <TitleInput
                                    title={translate("collection.sender_name")}
                                    styleInput={{
                                        borderWidth: 1,
                                        borderRadius: 4,
                                        borderColor: COLORS.bdCCCCCC,
                                        marginBottom: 10,
                                        paddingHorizontal: 10,
                                        backgroundColor: COLORS.bgFFFFFF,
                                        paddingVertical: 8,
                                    }}
                                    placeholder={translate("collection.enter_sender_name")}
                                    value={loaderName}
                                    onChangeText={(text) => {
                                        if (helper.isValidateCharVN(text)) {
                                            this.setState({ loaderName: text });
                                        }
                                    }}
                                    keyboardType={"default"}
                                    returnKeyType={"done"}
                                    blurOnSubmit
                                    width={constants.width - 20}
                                    height={40}
                                    clearText={() => {
                                        this.setState({ loaderName: '' });
                                    }}
                                    key="loaderName"
                                />
                                <TitleInput
                                    title={translate("collection.sender_phone")}
                                    styleInput={{
                                        borderWidth: 1,
                                        borderRadius: 4,
                                        borderColor: COLORS.bdCCCCCC,
                                        marginBottom: 5,
                                        paddingHorizontal: 10,
                                        backgroundColor: COLORS.bgFFFFFF,
                                        paddingVertical: 8
                                    }}
                                    placeholder={translate("collection.enter_sender_phone")}
                                    value={rechargerPhoneNumber}
                                    onChangeText={(text) => {
                                        const regExpPhone = new RegExp(/^[0]\d{0,9}$/);
                                        const isValidate = regExpPhone.test(text) || (text == "");
                                        if (isValidate) {
                                            this.setState({ rechargerPhoneNumber: text });
                                        }
                                    }}
                                    keyboardType="numeric"
                                    returnKeyType="done"
                                    blurOnSubmit
                                    width={constants.width - 20}
                                    height={40}
                                    clearText={() => {
                                        this.setState({ rechargerPhoneNumber: '' });
                                    }}
                                    key="rechargerPhoneNumber"
                                    isRequired={true}
                                />

                                <MyText style={{
                                    marginTop: 10,
                                    marginBottom: 10,
                                    fontWeight: 'bold',
                                    color: COLORS.bg00AAFF,
                                    fontSize: 15,
                                    fontStyle: 'italic',
                                    textDecorationLine: 'underline',
                                }}
                                    text={translate("collection.receiver_information")}
                                />

                                <MyText
                                    text={translate("collection.bank")}
                                    addSize={-1.5}
                                    style={{
                                        color: COLORS.txt333333,
                                        fontWeight: "bold",
                                        fontStyle: "italic",
                                        marginBottom: 5
                                    }}>
                                    {
                                        <MyText
                                            text={"*"}
                                            addSize={-1.5}
                                            style={{
                                                color: COLORS.txtFF0000
                                            }}
                                        />
                                    }
                                </MyText>
                                <PickerSearch
                                    label={"bank_name"}
                                    value={"bankCode"}
                                    valueSelected={bankCode}
                                    data={lstBankAccount}
                                    onChange={(item) => {
                                        this.setState({
                                            bankCode: item.bankCode,
                                            itemCheckBankAccount: item,
                                            numberBankAccount: "",
                                            recipientName: "",
                                            depositAmount: "",
                                            rechargeFee: ""

                                        })
                                    }}
                                    style={{
                                        flex: 1,
                                        flexDirection: "row",
                                        justifyContent: "center",
                                        alignItems: "center",
                                        height: 40,
                                        borderRadius: 4,
                                        borderWidth: 1,
                                        borderColor: COLORS.bdE4E4E4,
                                        width: constants.width - 20,
                                        backgroundColor: COLORS.btnFFFFFF,
                                        marginBottom: 10
                                    }}
                                    defaultLabel={translate("collection.choose_bank")}
                                />

                                <CheckAccountNumber
                                    title={translate("collection.account_number")}
                                    placeholder={translate("collection.account_number_required")}
                                    value={numberBankAccount}
                                    onChangeText={(text) => {
                                        let validate = new RegExp(/^[a-zA-Z0-9\-\%\+\/\$\.\/]{0,30}$/);
                                        if ((validate.test(text) || text == "") && this.state.numberBankAccount !== text) {
                                            console.log(" ?? ", text)
                                            this.setState({
                                                numberBankAccount: text,
                                                recipientName: "",
                                                depositAmount: "",
                                                rechargeFee: ""
                                            });
                                        }
                                    }}
                                    handleCheck={() => this.handleCheckBankAccount()}
                                    disabled={false}
                                />

                                {
                                    <BaseLoading
                                        isLoading={stateQueryCustomerBank.isFetching}
                                        isError={stateQueryCustomerBank.isError}
                                        isEmpty={stateQueryCustomerBank.isEmpty}
                                        textLoadingError={stateQueryCustomerBank.description}
                                        onPressTryAgains={() => {
                                            this.handleCheckBankAccount()
                                        }}
                                        content={
                                            !!recipientName && !!numberBankAccount &&
                                            <View>
                                                <TitleInput
                                                    title={translate("collection.recipient_name")}
                                                    styleInput={{
                                                        borderWidth: 1,
                                                        borderRadius: 4,
                                                        borderColor: COLORS.bdCCCCCC,
                                                        marginBottom: 5,
                                                        paddingHorizontal: 10,
                                                        backgroundColor: COLORS.bgF0F0F0,
                                                        paddingVertical: 8,
                                                    }}
                                                    placeholder="0đ"
                                                    value={recipientName}
                                                    onChangeText={(text) => { }}
                                                    keyboardType={"default"}
                                                    returnKeyType={"done"}
                                                    blurOnSubmit
                                                    width={constants.width - 20}
                                                    height={40}
                                                    clearText={() => { }}
                                                    editable={false}
                                                    key="recipientName"
                                                />
                                                <InputCheckAMount
                                                    title={translate("collection.transaction_amount")}
                                                    placeholder={translate("collection.enter_amount_want_to_deposit")}
                                                    value={depositAmount}
                                                    onChange={(text) => {
                                                        this.setState({
                                                            depositAmount: text,
                                                            rechargeFee: ""
                                                        });
                                                    }}
                                                    onCheckMount={() => this.getFeeCashin()}
                                                    disabled={false}
                                                    isRequired={true}
                                                />
                                                {/* <MyText
                                                    text={"Số tiền tối thiểu 50,000đ - tối đa 20,000,000đ"} //Số tiền từ Min -> Max phía Service chưa xử lý được nên tạm thời trên tần User Interface gán cứng trước
                                                    addSize={-1.5}
                                                    style={{
                                                        color: COLORS.txtD0021B,
                                                        fontStyle: "italic",
                                                        marginBottom: 10
                                                    }} /> */}
                                                {
                                                    !!rechargeFee && <TitleInput
                                                        title={translate("collection.transaction_fee")}
                                                        styleInput={{
                                                            borderWidth: 1,
                                                            borderRadius: 4,
                                                            borderColor: COLORS.bdCCCCCC,
                                                            marginBottom: 5,
                                                            paddingHorizontal: 10,
                                                            backgroundColor: COLORS.bgF0F0F0,
                                                            paddingVertical: 8,
                                                        }}
                                                        placeholder="0đ"
                                                        value={`${helper.formatMoney(rechargeFee)}`}
                                                        onChangeText={(text) => { }}
                                                        keyboardType="numeric"
                                                        returnKeyType="done"
                                                        blurOnSubmit
                                                        width={constants.width - 20}
                                                        height={40}
                                                        clearText={() => { }}
                                                        editable={!!rechargeFee}
                                                        key="rechargeFee"
                                                    />
                                                }
                                                <View
                                                    style={{
                                                        justifyContent: 'center',
                                                        alignItems: 'center',
                                                        marginBottom: 10,
                                                        marginTop: 5
                                                    }}>
                                                    <Button
                                                        onPress={() => this.handleNavigationStepTwo(client_IDCardType)}
                                                        text={"TIẾP TỤC"}
                                                        disabled={rechargeFee == "" || dataFeeCashin.CustFee == null}
                                                        styleContainer={{
                                                            borderRadius: 7,
                                                            backgroundColor: COLORS.txtFF8900,
                                                            marginLeft: 10,
                                                            height: 40,
                                                            width: constants.getSize(130),
                                                            opacity: rechargeFee == "" || dataFeeCashin.CustFee == null ? 0.5 : 1
                                                        }}
                                                        styleText={{
                                                            color: COLORS.txtFFFFFF,
                                                            fontSize: 14,
                                                            fontWeight: 'bold'
                                                        }}
                                                    />
                                                </View>
                                            </View>
                                        }
                                    />
                                }
                            </View>

                        </View>
                    </SafeAreaView>
                    <CaptureCamera
                        iisVisibleCamera={isVisibleCamera}
                        disabledUploadImage={true}
                        takePicture={(camera) => {
                            this.takePicture(camera);
                        }}
                        closeCamera={() => {
                            this.setState({ ...state, isVisibleCamera: false })
                        }}
                        selectPicture={() => {
                            selectImage();
                        }}
                    />
                </KeyboardAwareScrollView>
            </View>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        dataQueryCustomerBank: state.collectionReducer.dataQueryCustomerBank,
        stateQueryCustomerBank: state.collectionReducer.stateQueryCustomerBank,
        dataFeeCashin: state.collectionReducer.dataFeeCashin,
        userInfo: state.userReducer
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionCollection: bindActionCreators(actionCollectionCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(CollectionOne);
