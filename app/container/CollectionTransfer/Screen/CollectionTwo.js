import {
    View,
    Alert,
    Platform
} from 'react-native';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import SafeAreaView from 'react-native-safe-area-view';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { constants } from '@constants';
import { COLORS } from '@styles';
import { API_CONST, ENUM } from '@constants';
import {
    MyText,
    TitleInput,
    showBlockUI,
    hideBlockUI,
    Button
} from '@components';
import * as actionCollectionCreator from "../../CollectionTransfer/action";
import SignImage from '../component/SignImage';
import ImageProcess from '../component/ImageProcess';
import CheckBox from '../component/CheckBox';
import { getImageCDN } from '../../ActiveSimManager/action.js';
import { helper } from '@common';
import { translate } from '@translate';
import RNFetchBlob from 'rn-fetch-blob';
import { dateHelper } from '../../../common';
const { FILE_PATH: { COLLECTION_TWO } } = ENUM;

class CollectionTwo extends Component {
    constructor(props) {
        super(props);
        this.state = {
            identificationNumber: "",
            isVisibleSign: false,
            signature: '',
            signBase64: '',
            isCheck: false,
            linkSignImage: "",
            scrollEnabled: true
        };
    }

    componentDidMount() {


    }

    componentDidUpdate() {

    }

    takeSignature = (signature) => {
        this.setState({ isVisibleSign: false });
        showBlockUI();
        let timestamp = Date.now()
        const fs = RNFetchBlob.fs
        const filePathSignature = `${fs.dirs.DocumentDir}/signature/${timestamp}.png`
        fs.writeFile(filePathSignature, signature.split(",")[1], 'base64').then((response) => {
            const uri_path = Platform.OS == 'ios'
                ? filePathSignature
                : `file://${filePathSignature}`;
            const bodyFromData = helper.createFormData({ uri: uri_path, type: 'image/png', name: `takeSignature${dateHelper.getTimestamp()}`, path: COLLECTION_TWO });
            getImageCDN(bodyFromData)
                .then((response) => {
                    const remoteSignatureURI = API_CONST.API_GET_IMAGE_CDN_NEW + response[0];
                    this.setState({ linkSignImage: remoteSignatureURI })
                    this.setState({ signature: remoteSignatureURI });
                    console.log(remoteSignatureURI, "remoteSignatureURI")
                    hideBlockUI();
                }).catch((error) => {
                    Alert.alert("", error.msgError, [{
                        text: "OK",
                        onPress: hideBlockUI
                    }])
                })
        }).catch((error) => {
            hideBlockUI()
            console.log("🚀  ~ fs.createFile ~ error:", error)
        });
    }

    closeSignature = () => {
        this.setState({ isVisibleSign: false });
    };

    handleNavigationStepTwo = (customerData) => {
        showBlockUI();
        const {
            linkSignImage
        } = this.state;
        const {
            numberBankAccount,
            recipientName,
            depositAmount,
            loaderName,
            rechargerPhoneNumber,
            identificationNumber,
            bankCode,
            dataFeeCashin,
            dataQueryCustomerBank,
            cus_FilePathFrontOfIDCard,
            cus_FilePathBackOfIDCard,
            client_IDCardType,
            bank_name
        } = customerData;
        const {
            Fee,
            SumAmount,
            SurchargeFee,
            TransFee,
        } = dataFeeCashin.CustFee;
        // Chú ý một số field đang gán cứng ở đây là do bên phía Service không xử lý được nên trên tần User Interface tạm thời gán cứng trước.
        const data = {
            AirTimeTransactionBO: {
                PhoneNumber: numberBankAccount,
                Amount: depositAmount,
                CustomerName: loaderName,
                AirtimeTransactionTypeID: 1513,
                CustomerPhone: rechargerPhoneNumber,
                Fee: Fee,
                ContactPhone: rechargerPhoneNumber,
                ProductID: "*************",
                ProviderID: 33757,
                Cus_TransactionTransferBO: {
                    Receiver: recipientName,
                    ReceiverPhone: rechargerPhoneNumber,
                    SenderIdentificationType: client_IDCardType,
                    SenderIdCard: identificationNumber,
                    Amount: depositAmount,
                    SenderPhone: rechargerPhoneNumber,
                    SenderName: loaderName,
                    ReceiverAccountNo: numberBankAccount,
                    BankCode: bankCode,
                    Fee: Fee,
                    SurchargeFee: SurchargeFee,
                    TransFee: TransFee,
                    SumAmount: SumAmount,
                    BankName: bank_name
                },
                Cus_AirTimeTransactionAttachBOList: [
                    {
                        "FilePath": cus_FilePathFrontOfIDCard,
                        "Cus_ShortName": "CMT"
                    },
                    {
                        "FilePath": cus_FilePathBackOfIDCard,
                        "Cus_ShortName": "CMS"
                    },
                    {
                        "FilePath": linkSignImage,
                        "Cus_ShortName": "SIGN"
                    }
                ],
                Cus_AirtimeTransactionStateBOList: [

                    {
                        ...dataFeeCashin,
                    },
                    {
                        ...dataQueryCustomerBank,
                    }

                ]
            }

        }
        this.props.actionCollection.inserAndCreateticket(data).then((dataTicket) => {
            hideBlockUI();
            this.props.navigation.navigate("QueryStatus")
        }).catch(msgError => {
            Alert.alert("", msgError, [
                {
                    text: translate("collection.btn_accept"),
                    onPress: hideBlockUI,
                },
                {

                    text: translate("collection.retry"),
                    onPress: () => this.handleNavigationStepTwo(customerData)
                }
            ])
        });
    }

    render() {
        const {
            isCheck,
            linkSignImage
        } = this.state;

        const {
            dataFeeCashin,
            dataQueryCustomerBank
        } = this.props;

        const {
            itemCheckBankAccount,
            numberBankAccount,
            recipientName,
            depositAmount,
            rechargeFee,
            loaderName,
            rechargerPhoneNumber,
            identificationNumber,
            cus_FilePathFrontOfIDCard,
            cus_FilePathBackOfIDCard,
            client_IDCardType
        } = this.props.route.params ?? {};
        const {
            bank_name,
            bankCode
        } = itemCheckBankAccount;
        const customerData = {
            itemCheckBankAccount,
            numberBankAccount,
            recipientName,
            depositAmount,
            rechargeFee,
            loaderName,
            rechargerPhoneNumber,
            identificationNumber,
            bankCode,
            dataFeeCashin,
            dataQueryCustomerBank,
            cus_FilePathFrontOfIDCard,
            cus_FilePathBackOfIDCard,
            client_IDCardType,
            bank_name
        }

        return (
            <View style={{
                flex: 1,
                backgroundColor: 'white'
            }}>
                <KeyboardAwareScrollView
                    style={{
                        flex: 1,
                    }}
                    enableResetScrollToCoords={false}
                    keyboardShouldPersistTaps="always"
                    bounces={false}
                    overScrollMode="always"
                    showsHorizontalScrollIndicator={false}
                    showsVerticalScrollIndicator={false}
                    extraScrollHeight={60}
                    scrollEnabled={this.state.scrollEnabled}
                >
                    <SafeAreaView
                        style={{
                            flex: 1,
                            alignItems: 'center'
                        }}>
                        <View
                            style={{
                                width: constants.width - 20,
                                marginTop: 10
                            }}>
                            <MyText style={{
                                marginTop: 10,
                                marginBottom: 10,
                                fontWeight: 'bold',
                                color: COLORS.bg00AAFF,
                                fontSize: 15,
                                fontStyle: 'italic',
                                textDecorationLine: 'underline',
                            }}
                                text={translate("collection.receiver_information")}
                            />
                            <TitleInput
                                title={translate("collection.bank")}
                                styleInput={{
                                    borderWidth: 1,
                                    borderRadius: 4,
                                    borderColor: COLORS.bdCCCCCC,
                                    marginBottom: 10,
                                    paddingHorizontal: 10,
                                    backgroundColor: COLORS.bgF0F0F0,
                                    paddingVertical: 8,
                                }}
                                placeholder=""
                                value={bank_name}
                                onChangeText={(text) => { }}
                                keyboardType="numeric"
                                returnKeyType="done"
                                blurOnSubmit
                                width={constants.width - 20}
                                height={40}
                                clearText={() => { }}
                                editable={false}
                                key="bank_name"
                                isRequired={true}
                            />
                            <TitleInput
                                title={translate("collection.account_number")}
                                styleInput={{
                                    borderWidth: 1,
                                    borderRadius: 4,
                                    borderColor: COLORS.bdCCCCCC,
                                    marginBottom: 10,
                                    paddingHorizontal: 10,
                                    backgroundColor: COLORS.bgF0F0F0,
                                    paddingVertical: 8,
                                }}
                                placeholder=""
                                value={numberBankAccount}
                                onChangeText={(text) => { }}
                                keyboardType="numeric"
                                returnKeyType="done"
                                blurOnSubmit
                                width={constants.width - 20}
                                height={40}
                                clearText={() => { }}
                                editable={false}
                                key="numberBankAccount"
                                isRequired={true}
                            />
                            <TitleInput
                                title={translate("collection.recipient_name")}
                                styleInput={{
                                    borderWidth: 1,
                                    borderRadius: 4,
                                    borderColor: COLORS.bdCCCCCC,
                                    marginBottom: 10,
                                    paddingHorizontal: 10,
                                    backgroundColor: COLORS.bgF0F0F0,
                                    paddingVertical: 8,
                                }}
                                placeholder=""
                                value={recipientName}
                                onChangeText={(text) => { }}
                                keyboardType="numeric"
                                returnKeyType="done"
                                blurOnSubmit
                                width={constants.width - 20}
                                height={40}
                                clearText={() => { }}
                                editable={false}
                                key="recipientName"
                                isRequired={true}
                            />
                            <TitleInput
                                title={translate("collection.transaction_amount")}
                                styleInput={{
                                    borderWidth: 1,
                                    borderRadius: 4,
                                    borderColor: COLORS.bdCCCCCC,
                                    marginBottom: 10,
                                    paddingHorizontal: 10,
                                    backgroundColor: COLORS.bgF0F0F0,
                                    paddingVertical: 8,
                                }}
                                placeholder=""
                                value={`${helper.formatMoney(depositAmount)}`}
                                onChangeText={(text) => { }}
                                keyboardType="numeric"
                                returnKeyType="done"
                                blurOnSubmit
                                width={constants.width - 20}
                                height={40}
                                clearText={() => { }}
                                editable={false}
                                key="depositAmount"
                                isRequired={true}
                            />
                            <TitleInput
                                title={translate("collection.transaction_fee")}
                                styleInput={{
                                    borderWidth: 1,
                                    borderRadius: 4,
                                    borderColor: COLORS.bdCCCCCC,
                                    marginBottom: 10,
                                    paddingHorizontal: 10,
                                    backgroundColor: COLORS.bgF0F0F0,
                                    paddingVertical: 8,
                                }}
                                placeholder=""
                                value={`${helper.formatMoney(rechargeFee)}`}
                                onChangeText={(text) => { }}
                                keyboardType="numeric"
                                returnKeyType="done"
                                blurOnSubmit
                                width={constants.width - 20}
                                height={40}
                                clearText={() => { }}
                                editable={false}
                                key="rechargeFee"
                                isRequired={true}
                            />
                            <CheckBox
                                onPress={() => this.setState({ isCheck: !isCheck })}
                                isCheck={isCheck}
                                title={translate("collection.agree_to_terms")}
                                link={`${"https://viettelmoney.vn/"}`}
                            />

                            <MyText
                                text={translate("collection.customer_signature")}
                                addSize={-1.5}
                                style={{
                                    color: COLORS.txt333333,
                                    fontWeight: "bold",
                                    fontStyle: "italic",
                                    marginBottom: 10
                                }}>
                                {
                                    <MyText
                                        text={"*"}
                                        addSize={-1.5}
                                        style={{
                                            color: COLORS.txtFF0000
                                        }}
                                    />
                                }
                            </MyText>
                            <View style={{
                                width: '100%',
                                height: linkSignImage ? 170 : 400,
                                justifyContent: 'center',
                            }}>
                                {
                                    linkSignImage ?
                                        <ImageProcess
                                            onCamera={() => {
                                                this.setState({
                                                    isVisibleSign: true
                                                });
                                            }}
                                            urlImageLocal={this.state.signature}
                                            urlImageRemote={this.state.signature}
                                            deleteImage={() => {
                                                this.setState({ signature: '' })
                                                this.setState({ linkSignImage: '' })
                                            }}
                                            isSignature={true}
                                        />
                                        :
                                        <SignImage
                                            onBegin={() => {
                                                this.setState({
                                                    scrollEnabled: false
                                                })
                                            }}
                                            onEnd={() => {
                                                this.setState({
                                                    scrollEnabled: true
                                                })
                                            }}
                                            isVisibleSign={() => this.state.isVisibleSign}
                                            takeSignature={this.takeSignature}
                                            closeSignature={() => { this.setState({ isVisibleSign: false }) }}
                                        />
                                }
                            </View>
                            <View
                                style={{
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    marginBottom: 10,
                                    marginTop: 20,
                                    marginRight: 10
                                }}>
                                <Button
                                    onPress={() => this.handleNavigationStepTwo(customerData)}
                                    text={translate("collection.continue")}
                                    disabled={
                                        isCheck == false
                                        || linkSignImage == ""
                                    }
                                    styleContainer={{
                                        borderRadius: 7,
                                        backgroundColor: COLORS.txtFF8900,
                                        marginLeft: 10,
                                        height: 40,
                                        width: constants.getSize(130),
                                        opacity:
                                            isCheck == false
                                                || linkSignImage == ""
                                                ? 0.5 : 1
                                    }}
                                    styleText={{
                                        color: COLORS.txtFFFFFF,
                                        fontSize: 14,
                                        fontWeight: 'bold'
                                    }}
                                />
                            </View>
                        </View>
                    </SafeAreaView>

                </KeyboardAwareScrollView>
            </View>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        dataQueryCustomerBank: state.collectionReducer.dataQueryCustomerBank,
        stateQueryCustomerBank: state.collectionReducer.stateQueryCustomerBank,
        dataFeeCashin: state.collectionReducer.dataFeeCashin,
        userInfo: state.userReducer
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionCollection: bindActionCreators(actionCollectionCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(CollectionTwo);
