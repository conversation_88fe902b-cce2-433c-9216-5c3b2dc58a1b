import { StyleSheet, Text, View, SafeAreaView, Image, FlatList, Alert, Animated } from 'react-native'
import React, { useEffect, useState } from 'react';
import { BaseLoading, MyText, hideBlockUI, showBlockUI } from '@components';
import { COLORS } from '@styles';
import { helper } from '@common';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { translate } from '@translate';
import { connect } from 'react-redux';
import { constants } from '@constants';
import InputSearch from './component/InputSearch';
import { bindActionCreators } from 'redux';
import * as actionCollectionCreator from "../CollectionTransfer/action";
import * as actionBankAirtimeServiceCreator from "../BankAirtimeService/action";
// import InputSearch from './component/InputSearch';

const MenuCollection = ({ navigation, userInfo, actionCollection, stateCatalogListCollection, dataCatalogListCollection, actionBankAirtimeService }) => {

    if (!helper.isWithinSaleHours(userInfo)) {
        return Alert.alert("", "Bạn chỉ được phép thao tác chức năng này trong giờ bán hàng.", [
            {
                text: "Ok",
                onPress: navigation.goBack

            },
        ]);
    }

    const animatedOpacity = new Animated.Value(0);
    Animated.timing(animatedOpacity, { toValue: 1, duration: 1000 }).start();
    const { isFetching, isError, isEmpty, description } = stateCatalogListCollection ?? {};


    useEffect(() => {
        actionCollection.getCatalogListCollection();
        actionBankAirtimeService.clear_data_customer()
    }, [actionCollection, actionBankAirtimeService])

    const [keyWord, setKeyWork] = useState('');
    // if (MULTICAT_STOREID?.has(`${userInfo.storeID}`)) {
    //     dataMenuCollection[0].data = [
    //         ...dataMenuCollection[0].data,
    //         {
    //             id: 2,
    //             name: 'Recharge internet',
    //             source: require('../../../assets/internet.png'),
    //             screen: 'RechargeInternet'
    //         }
    //     ];
    // }

    const dataMenuCollection = [
        {
            OrderIndex: 1,
            ServiceCatalogID: 1,
            source: require('../../../assets/installment.png'),
        },
        {
            OrderIndex: 2,
            ServiceCatalogID: 2,
            source: require('../../../assets/payment.png'),
        },
        {
            OrderIndex: 3,
            ServiceCatalogID: 3,
            source: require('../../../assets/insurance_catalog.png'),
        },
        {
            OrderIndex: 4,
            ServiceCatalogID: 4,
            source: require('../../../assets/vietnamese_invoice.png'),
        },
        {
            OrderIndex: 5,
            ServiceCatalogID: 5,
            source: require('../../../assets/recharge_phone.png'),
        },
        {
            OrderIndex: 6,
            ServiceCatalogID: 6,
            source: require('../../../assets/financial_services.png'),
        },
        {
            OrderIndex: 7,
            ServiceCatalogID: 9,
            source: require('../../../assets/data_viet_nam.png'),
        },
        {
            OrderIndex: 8,
            ServiceCatalogID: 12,
            source: require('../../../assets/bank.png'),
        },
        {
            OrderIndex: 9,
            ServiceCatalogID: 13,
            source: require('../../../assets/loan_comsumer.png'),
        },
        {
            OrderIndex: 10,
            ServiceCatalogID: 14,
            source: require('../../../assets/open_card_service.png'),
        },
    ]

    const handleNavigatorServiceGroupList = (item) => {
        const { ServiceCatalogID } = item ?? '';
        showBlockUI();
        actionCollection.getServiceGroupListCatalog(ServiceCatalogID).then((reponse) => {
            hideBlockUI();
            navigation.navigate("CatalogCollection", { item })
        }).catch((err) => {
            Alert.alert("", err, [
                {
                    text: "OK",
                    onPress: () => {
                        hideBlockUI();
                    },
                },
            ]);
        });
    }


    const renderItem = ({ item }) => {
        return (
            <Animated.View style={{
                opacity: animatedOpacity,
                marginTop: 15
            }}>
                <View style={{

                }}>
                    <TouchableOpacity
                        onPress={() => {
                            handleNavigatorServiceGroupList(item)
                        }}
                        style={{
                            justifyContent: 'center',
                            alignItems: 'center',
                            width: (constants.width - 20) / 4,
                            marginTop: 15
                        }}>
                        <Image
                            style={{
                                width: 40,
                                height: 40,
                            }}
                            source={item.source}
                        />

                        <Text style={{
                            marginTop: 10,
                            textAlign: 'center',
                            width: 60

                        }}>
                            {item?.Description}
                        </Text>
                    </TouchableOpacity>
                </View>
            </Animated.View>
        );
    };


    const menuCatalogCollection = Array.isArray(dataCatalogListCollection) ? dataCatalogListCollection.map(catalogItem => {
        const correspondingMenuItem = dataMenuCollection.find(menuItem => menuItem.ServiceCatalogID === catalogItem.ServiceCatalogID);
        return {
            ...catalogItem,
            ...correspondingMenuItem || null // Nếu không tìm thấy phần tử tương ứng, trả về null
        };
    }) : [];

    return (
        <SafeAreaView
            style={{
                flex: 1,
                backgroundColor: COLORS.bgF0F0F0,
            }}
        >
            {/* <InputSearch
                value={keyWord}
                onChangeText={(text) => {
                    if (helper.isValidateCharVN(text)) {
                        setKeyWork(text)
                    }
                }}
                clearText={() => setKeyWork('')}
                isShowButtonClose={helper.IsNonEmptyString(keyWord)}
            /> */}
            <BaseLoading
                isLoading={isFetching}
                isError={isError}
                isEmpty={isEmpty}
                textLoadingError={description}
                onPressTryAgains={() => actionCollection.getCatalogListCollection()}
                content={
                    <FlatList
                        style={{
                            backgroundColor: COLORS.bgFFFFFF,
                        }}
                        data={menuCatalogCollection}
                        keyExtractor={(item, index) => `${index}`}
                        renderItem={(item) => renderItem(item)}
                        bounces={false}
                        scrollEventThrottle={16}
                        contentContainerStyle={{
                            flex: 1
                        }}
                        numColumns={4}

                    />
                }
                stickySectionHeadersEnabled={false}
                alwaysBounceVertical={false}
                bounces={false}
                scrollEventThrottle={16}
            />
        </SafeAreaView>
    )
}

const mapStateToProps = (state) => ({
    dataCatalogListCollection: state.collectionReducer.dataCatalogListCollection,
    stateCatalogListCollection: state.collectionReducer.stateCatalogListCollection,
    userInfo: state.userReducer
});
const mapDispatchToProps = (dispatch) => ({
    actionCollection: bindActionCreators(actionCollectionCreator, dispatch),
    actionBankAirtimeService: bindActionCreators(actionBankAirtimeServiceCreator, dispatch),
});
export default connect(mapStateToProps, mapDispatchToProps)(MenuCollection);

const MULTICAT_COMPANY = new Set(["1"]);
const MULTICAT_PROVINCE = new Set(["3"]);
const MULTICAT_BRAND_EXCLUDE = new Set(["8"]);



