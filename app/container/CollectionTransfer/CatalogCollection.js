import { <PERSON><PERSON>ist, SafeAreaView, StyleSheet, Text, View, TouchableOpacity, Image, Animated, Alert, ScrollView, Keyboard } from 'react-native'
import React, { useEffect, useState } from 'react'
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as actionCollectionCreator from "../CollectionTransfer/action";
import InputSearch from './component/InputSearch';
import { BaseLoading, Icon } from '@components';
import { COLORS } from '@styles';
import { helper } from '@common';
import { constants } from '@constants';
import { BarcodeCamera } from '../../components';
import Table from '../BankAirtimeService/component/Table/Table'
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useFocusEffect } from '@react-navigation/native';
import { useCallback } from 'react';

const CatalogCollection = ({ navigation, route, dataServiceGroupListCatalog, stateServiceGroupListCatalogn, actionCollection, userInfo }) => {
    const animatedOpacity = new Animated.Value(0);
    Animated.timing(animatedOpacity, { toValue: 1, duration: 1000 }).start();
    const [keyWord, setKeyWork] = useState('');
    const [isVisibleScan, setIsVisibleScan] = useState(false);
    const [fadeAnim] = useState(new Animated.Value(1));
    const { isFetching, isError, isEmpty, description } = stateServiceGroupListCatalogn ?? {};
    const { userName, provinceID, storeID } = userInfo;
    const { item = {} } = route?.params || {};
    const { ServiceCatalogID, isVisibleScanQrCode } = item;
    const isCollection = ServiceCatalogID == 4 ?? "";

    useFocusEffect(
        useCallback(() => {
            console.log("ServiceCatalogID:", ServiceCatalogID);
            console.log("isVisibleScanQrCode:", isVisibleScanQrCode);
            if (ServiceCatalogID === 4 && isVisibleScanQrCode === true) {
                const timer = setTimeout(() => {
                    setIsVisibleScan(true);
                }, 100);

                return () => clearTimeout(timer);
            }
        }, [ServiceCatalogID, isVisibleScanQrCode])
    );

    useEffect(() => {
        const timer = setTimeout(() => {
            Animated.timing(fadeAnim, {
                toValue: 0,
                duration: 1000,
                useNativeDriver: true
            }).start();
        }, 3000);
        return () => clearTimeout(timer);

    }, [fadeAnim]);

    let dataServiceGroupListCataloc = [
        {
            AirtimeServiceGroupID: -1,
            source: require('../../../assets/bank.png'),
            screen: "CollectionOne",
        },
        {
            AirtimeServiceGroupID: 15,
            source: require('../../../assets/topup_sim.png'),
            screen: "",
        },
        {
            AirtimeServiceGroupID: 16,
            source: require('../../../assets/insurance.png'),
            screen: "InsuranceNavigator",
        },
        {
            AirtimeServiceGroupID: 17,
            source: require('../../../assets/topup_data.png'),
            screen: "",
        },
        {
            AirtimeServiceGroupID: 19,
            source: require('../../../assets/insurance_bike.png'),
            screen: "",
        },
        {
            AirtimeServiceGroupID: 24,
            screen: "",
        },
        {
            AirtimeServiceGroupID: 25,
            screen: "",
        },
        {
            AirtimeServiceGroupID: 26,
            source: require('../../../assets/insurance_bike.png'),
            screen: "",
        },
        {
            AirtimeServiceGroupID: 30,
            source: require('../../../assets/insurance_mic.png'),
            // screen: "HealthInsuranceNavigator",
            screen: "InsuranceAirtimeServiceNavigator",
        },
        {
            AirtimeServiceGroupID: 33,
            source: require('../../../assets/insurance_mobile.png'),
            screen: "InsuranceAirtimeServiceNavigator",
        },
        {
            AirtimeServiceGroupID: 1,
            source: require('../../../assets/collect_phone.png'),
            screen: "CollectInstallmentNavigator",
        },
        {
            AirtimeServiceGroupID: 2,
            source: require('../../../assets/light.png'),
            screen: "CollectInstallmentNavigator",
        },
        {
            AirtimeServiceGroupID: 3,
            source: require('../../../assets/water_bill.png'),
            screen: "CollectInstallmentNavigator",
        },
        {
            AirtimeServiceGroupID: 4,
            source: require('../../../assets/vietnamese_invoice.png'),
            screen: "CollectInstallmentNavigator",
        },
        {
            AirtimeServiceGroupID: 5,
            source: require('../../../assets/recharge_phone.png'),
            screen: "CollectInstallmentNavigator",
        },
        {
            AirtimeServiceGroupID: 6,
            source: require('../../../assets/financial_services.png'),
            screen: "CollectInstallmentNavigator",
        },
        {
            AirtimeServiceGroupID: 7,
            source: require('../../../assets/collect_insurance.png'),
            screen: "CollectInstallmentNavigator",
        },
        {
            AirtimeServiceGroupID: 8,
            source: require('../../../assets/installment_service.png'),
            screen: "CollectInstallmentNavigator",
        },
        {
            AirtimeServiceGroupID: 10,
            source: require('../../../assets/deposit.png'),
            screen: "CollectInstallmentNavigator",
        },
        {
            AirtimeServiceGroupID: 11,
            source: require('../../../assets/credit.png'),
            screen: "CollectInstallmentNavigator",
        },
        {
            AirtimeServiceGroupID: 12,
            source: require('../../../assets/driver.png'),
            screen: "CollectInstallmentNavigator",
        },
        {
            AirtimeServiceGroupID: 13,
            source: require('../../../assets/tuition.png'),
            screen: "CollectInstallmentNavigator",
        },
        {
            AirtimeServiceGroupID: 14,
            source: require('../../../assets/ticket.png'),
            screen: "CollectInstallmentNavigator",
        },
        {
            AirtimeServiceGroupID: 1,
            source: require('../../../assets/collect_phone.png'),
            screen: "CollectInstallmentNavigator",
        },
        {
            AirtimeServiceGroupID: 2,
            source: require('../../../assets/light.png'),
            screen: "CollectInstallmentNavigator",
        },
        {
            AirtimeServiceGroupID: 3,
            source: require('../../../assets/water_bill.png'),
            screen: "CollectInstallmentNavigator",
        },
        {
            AirtimeServiceGroupID: 4,
            source: require('../../../assets/vietnamese_invoice.png'),
            screen: "CollectInstallmentNavigator",
        },
        {
            AirtimeServiceGroupID: 5,
            source: require('../../../assets/recharge_phone.png'),
            screen: "CollectInstallmentNavigator",
        },
        {
            AirtimeServiceGroupID: 6,
            source: require('../../../assets/financial_services.png'),
            screen: "CollectInstallmentNavigator",
        },
        {
            AirtimeServiceGroupID: 7,
            source: require('../../../assets/collect_insurance.png'),
            screen: "CollectInstallmentNavigator",
        },
        {
            AirtimeServiceGroupID: 8,
            source: require('../../../assets/installment_service.png'),
            screen: "CollectInstallmentNavigator",
        },
        {
            AirtimeServiceGroupID: 11,
            source: require('../../../assets/credit.png'),
            screen: "CollectInstallmentNavigator",
        },
        {
            AirtimeServiceGroupID: 12,
            source: require('../../../assets/driver.png'),
            screen: "CollectInstallmentNavigator",
        },
        {
            AirtimeServiceGroupID: 13,
            source: require('../../../assets/tuition.png'),
            screen: "CollectInstallmentNavigator",
        },
        {
            AirtimeServiceGroupID: 14,
            source: require('../../../assets/ticket.png'),
            screen: "CollectInstallmentNavigator",
        },
        {
            AirtimeServiceGroupID: 28,
            source: require('../../../assets/pvi_insurance.png'),
            screen: "InsuranceAirtimeServiceNavigator",
        },
        {
            AirtimeServiceGroupID: 29,
            source: require('../../../assets/insurance_warranty.png'),
            screen: "InsuranceAirtimeServiceNavigator",
        },
        {
            AirtimeServiceGroupID: 31,
            source: require('../../../assets/loan_insurance.png'),
            screen: "InsuranceAirtimeServiceNavigator",
        },
        {
            AirtimeServiceGroupID: 35,
            source: require('../../../assets/withdrawal.png'),
            screen: "BankAirtimeServiceNavigator",
        },
        {
            AirtimeServiceGroupID: 37,
            source: require('../../../assets/deposit_bank.png'),
            screen: "BankAirtimeServiceNavigator",
        },
        {
            AirtimeServiceGroupID: 38,
            source: require('../../../assets/loanservice.png'),
            screen: "ConsumerLoanAirtimeServiceNavigator",
        },
        {
            AirtimeServiceGroupID: 39,
            source: require('../../../assets/card_service.png'),
            screen: "CardOpeningServiceNavigator",
        },
        {
            AirtimeServiceGroupID: 40,
            source: require('../../../assets/buy_now_pay_later.png'),
            screen: "CardOpeningServiceNavigator",
        },
        {
            AirtimeServiceGroupID: 41,
            source: require('../../../assets/samsung_insu.png'),
            screen: "InsuranceAirtimeServiceNavigator",
        },
        {
            AirtimeServiceGroupID: 42,
            source: require('../../../assets/payment_card.png'),
            screen: "CardOpeningServiceNavigator",
        },
        {
            AirtimeServiceGroupID: 43,
            source: require('../../../assets/insurance_screen_protecor.png'),
            screen: "InsuranceAirtimeServiceNavigator",
        },
        {
            AirtimeServiceGroupID: 44,
            source: require('../../../assets/car_insurance.png'),
            screen: "InsuranceAirtimeServiceNavigator",
        },
        {
            AirtimeServiceGroupID: 45,
            source: require('../../../assets/car_physical_damage.png'),
            screen: "InsuranceAirtimeServiceNavigator",
        }
    ]

    // if (MULTICAT_USER_DEFINED3.has(`${userName}`)) {
    //     dataServiceGroupListCataloc = [
    //         ...dataServiceGroupListCataloc,

    //     ]
    // }

    const renderItem = ({ item }) => {
        return (
            <Animated.View style={{
                opacity: animatedOpacity,
                marginTop: 15
            }}>
                <View style={{

                }}>
                    <TouchableOpacity
                        disabled={!item.screen}
                        onPress={() => {
                            actionCollection.updateItemCatalog(item)
                            if (item.screen) {
                                navigation.navigate(item.screen, {
                                    screen: item.screen, params: {
                                        item
                                    }
                                })
                            }
                        }}
                        style={{
                            justifyContent: 'center',
                            alignItems: 'center',
                            width: (constants.width - 20) / 4,
                            marginTop: 15
                        }}>
                        <Image
                            style={{
                                width: 40,
                                height: 40,
                                resizeMode: 'stretch',
                            }}
                            source={item.source}
                        />
                        {item.screen !== '' ?
                            <Text style={{
                                marginTop: 10,
                                textAlign: 'center',
                                width: 60,
                                color: !item.source ? COLORS.bgE0E0E0 : COLORS.bg000000

                            }}>
                                {item?.AirtimeServiceGroupName}
                            </Text>
                            :
                            <Text style={{
                                marginTop: 10,
                                textAlign: 'center',
                                width: 60,
                                color: COLORS.bgE0E0E0
                            }}>
                                {item?.AirtimeServiceGroupName} (Bảo trì)
                            </Text>
                        }
                    </TouchableOpacity>
                </View>
            </Animated.View>
        );
    };

    const serviceGroupListCatalog = Array.isArray(dataServiceGroupListCatalog) ? dataServiceGroupListCatalog.map(serviceGroupListItem => {
        const correspondingServiceGroupListItem = dataServiceGroupListCataloc.find(menuItem => menuItem.AirtimeServiceGroupID === serviceGroupListItem.AirtimeServiceGroupID);
        return {
            ...serviceGroupListItem,
            ...correspondingServiceGroupListItem || null // Nếu không tìm thấy phần tử tương ứng, trả về null
        };
    }) : [];

    const handleGeneratorQrcode = (airtimeServiceGroupID, productID, phoneNumber) => {
        const result = {
            AirtimeServiceGroupID: airtimeServiceGroupID,
            ProductID: productID,
            PhoneNumber: phoneNumber,
        }
        const filterAirtime = serviceGroupListCatalog?.filter((pre) => pre.AirtimeServiceGroupID == airtimeServiceGroupID);
        const dataOutputMapping = Object.assign({}, filterAirtime?.[0], result);
        actionCollection.updateItemCatalog(dataOutputMapping);
        navigation.navigate("CollectInstallmentNavigator");
    }

    const lstLimitByStore = serviceGroupListCatalog?.[0]?.ServiceGroupTransactionLimit?.TransactionLimitByStore?.[0]?.LimitInfo;
    const lstLimitByUser = serviceGroupListCatalog?.[0]?.ServiceGroupTransactionLimit?.TransactionLimitByUser?.[0]?.LimitInfo;
    const headerByStore = serviceGroupListCatalog?.[0]?.ServiceGroupTransactionLimit?.TransactionLimitByStore?.[0]?.Label;
    const headerByUser = serviceGroupListCatalog?.[0]?.ServiceGroupTransactionLimit?.TransactionLimitByUser?.[0]?.Label;

    return (
        <View
            style={{
                flex: 1,
                backgroundColor: "white",
            }}
        >
            <KeyboardAwareScrollView
                style={{
                    flex: 1,
                }}
                enableResetScrollToCoords={false}
                keyboardShouldPersistTaps="always"
                bounces={false}
                overScrollMode="always"
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                extraScrollHeight={60}
            >
                <SafeAreaView
                    style={{
                        flex: 1,
                        backgroundColor: COLORS.bgF0F0F0,
                    }}
                >
                    <View style={{
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}>
                        <InputSearch
                            value={keyWord}
                            onFocus={() => {
                                Keyboard.dismiss();
                            }}
                            onChangeText={(text) => {
                                if (helper.isValidateCharVN(text)) {
                                    setKeyWork(text)
                                }
                            }}
                            clearText={() => setKeyWork('')}
                            isShowButtonClose={helper.IsNonEmptyString(keyWord)}
                            isCollection={isCollection}
                        />
                        {
                            ServiceCatalogID == 4 &&
                            <Icon
                                onPress={() => {
                                    setIsVisibleScan(true);
                                }}
                                iconSet={'MaterialCommunityIcons'}
                                name={'barcode-scan'}
                                color={COLORS.bgF49B0C}
                                size={30}
                                style={{
                                    marginLeft: 6
                                }}
                            />
                        }
                    </View>
                    <BaseLoading
                        isLoading={isFetching}
                        isError={isError}
                        isEmpty={isEmpty}
                        textLoadingError={description}
                        onPressTryAgains={() => actionCollection.getCatalogListCollection()}
                        content={
                            <View style={{
                                flex: 1
                            }}>
                                <FlatList
                                    style={{
                                        backgroundColor: COLORS.bgFFFFFF,
                                    }}
                                    data={serviceGroupListCatalog}
                                    keyExtractor={(item, index) => `${index}`}
                                    renderItem={(item) => renderItem(item)}
                                    bounces={false}
                                    scrollEventThrottle={16}
                                    contentContainerStyle={{
                                        flex: 1
                                    }}
                                    numColumns={4}
                                />
                                {
                                    item.ServiceCatalogID == 12 &&
                                    <View style={{
                                        backgroundColor: COLORS.bgFFFFFF,
                                    }}>
                                        <View>
                                            {
                                                helper.IsNonEmptyArray(lstLimitByUser) &&
                                                <View style={{
                                                    paddingHorizontal: 10,
                                                    marginTop: 10
                                                }}>
                                                    <Text style={{
                                                        fontWeight: 'bold',
                                                        color: COLORS.bgEA1D5D
                                                    }}>
                                                        {headerByUser}
                                                    </Text>
                                                    <ScrollView
                                                        showsHorizontalScrollIndicator={false}
                                                        contentContainerStyle={{
                                                            width: 710,
                                                        }}
                                                        horizontal>
                                                        <Table
                                                            showHeader={true}
                                                            fuorColumsHeader={true}
                                                            fuorColumsDraw={true}
                                                            columsOne={'Nhân viên'}
                                                            columsTwo={'Hạn mức còn lại trong ngày'}
                                                            columsThree={'Số tiền giao dịch đã thực hiện'}
                                                            columsFour={'Hạn mức nhân viên'}
                                                            children={
                                                                <View>
                                                                    {lstLimitByUser?.map((item, index) => {
                                                                        return (
                                                                            <View key={index} style={styles.containerTable}>
                                                                                <View style={[styles.widthTable, {
                                                                                    borderTopWidth: 1,
                                                                                    width: 230,
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'center',
                                                                                    backgroundColor: COLORS.bgE0E0E0
                                                                                }]}>
                                                                                    <Text>{item.UserID}</Text>
                                                                                </View>
                                                                                <View style={[styles.widthTable, {
                                                                                    borderTopWidth: 1,
                                                                                    width: 150,
                                                                                    alignItems: 'flex-end',
                                                                                    paddingHorizontal: 10,
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'center',
                                                                                    backgroundColor: COLORS.bgE0E0E0
                                                                                }]}>
                                                                                    <Text>{item.LimitByDayRemain}</Text>
                                                                                </View>
                                                                                <View style={[styles.widthTable, {
                                                                                    borderTopWidth: 1,
                                                                                    width: 150,
                                                                                    alignItems: 'flex-end',
                                                                                    paddingHorizontal: 10,
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'center',
                                                                                    backgroundColor: COLORS.bgE0E0E0
                                                                                }]}>
                                                                                    <Text>{item.LimitUsedByDay}</Text>
                                                                                </View>
                                                                                <View style={[styles.widthTable, {
                                                                                    borderTopWidth: 1,
                                                                                    borderRightWidth: 1,
                                                                                    width: 150,
                                                                                    alignItems: 'flex-end',
                                                                                    paddingHorizontal: 10,
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'center',
                                                                                    backgroundColor: COLORS.bgE0E0E0
                                                                                }]}>
                                                                                    <Text>{item.LimitByDay}</Text>
                                                                                </View>
                                                                            </View>
                                                                        )
                                                                    })}
                                                                </View>
                                                            }
                                                        />
                                                    </ScrollView>
                                                    <Animated.View style={{
                                                        width: 100,
                                                        alignSelf: 'flex-end',
                                                        alignItems: 'flex-end',
                                                        right: 5,
                                                        opacity: fadeAnim,
                                                    }}>
                                                        <Image
                                                            resizeMode='stretch'
                                                            source={require('../../../assets/slide.gif')}
                                                            style={{
                                                                width: 30,
                                                                height: 35,
                                                                marginTop: -10,
                                                            }}
                                                        />
                                                        <Text style={{
                                                            color: COLORS.bg288AD6
                                                        }}>Kéo sang trái</Text>
                                                    </Animated.View>
                                                </View>
                                            }
                                        </View>
                                        <View style={{

                                        }}>
                                            {
                                                helper.IsNonEmptyArray(lstLimitByStore) &&
                                                <View>
                                                    <View style={{
                                                        paddingHorizontal: 10,
                                                        marginTop: 10,
                                                    }}>
                                                        <Text style={{
                                                            fontWeight: 'bold',
                                                            color: COLORS.bgEA1D5D,
                                                            marginBottom: 10
                                                        }}>
                                                            {headerByStore}
                                                        </Text>
                                                        <Table
                                                            showHeader={false}
                                                            market={'Siêu thị'}
                                                            tileLimit={'Hạn mức theo ngày'}
                                                            storeID={lstLimitByStore?.[0]?.Value}
                                                            limitDate={lstLimitByStore?.[1]?.Value}
                                                            tileUsed={'Hạn mức ngày sử dụng'}
                                                            usedDate={lstLimitByStore?.[3]?.Value}
                                                            tileRemain={'Hạn mức ngày lại'}
                                                            remainDate={lstLimitByStore?.[5]?.Value}
                                                        />


                                                    </View>
                                                </View>
                                            }
                                        </View>
                                    </View>
                                }
                            </View>
                        }
                        stickySectionHeadersEnabled={false}
                        alwaysBounceVertical={false}
                        bounces={false}
                        scrollEventThrottle={16}
                    />
                    {isVisibleScan && (
                        <BarcodeCamera
                            isVisible={isVisibleScan}
                            closeCamera={() => setIsVisibleScan(false)}
                            resultScanBarcode={(barcode) => {
                                const partsScanQR = barcode.split("-");
                                const airtimeServiceGroupID = partsScanQR[0];
                                const productID = partsScanQR[1];
                                const phoneNumber = partsScanQR.slice(2).join("-");
                                const isCheckDataQRGen = !!airtimeServiceGroupID && !!productID && !!phoneNumber
                                if (isCheckDataQRGen) {
                                    setIsVisibleScan(false);
                                    handleGeneratorQrcode(airtimeServiceGroupID, productID, phoneNumber);
                                } else {
                                    setIsVisibleScan(false);
                                    Alert.alert("", "Không tìm thấy loại thanh toán ứng với mã hợp đồng. Vui lòng liên hệ IT để được hổ trợ (user: 61804)")
                                }

                            }}
                        />
                    )}
                </SafeAreaView>
            </KeyboardAwareScrollView>
        </View>
    )
}

const mapStateToProps = (state) => ({
    dataServiceGroupListCatalog: state.collectionReducer.dataServiceGroupListCatalog,
    stateServiceGroupListCatalogn: state.collectionReducer.stateServiceGroupListCatalogn,
    userInfo: state.userReducer,
});
const mapDispatchToProps = (dispatch) => ({
    actionCollection: bindActionCreators(actionCollectionCreator, dispatch),
});

export default connect(mapStateToProps, mapDispatchToProps)(CatalogCollection);


const styles = StyleSheet.create({
    container: {
        backgroundColor: COLORS.bgFFFFFF,
        paddingTop: 20,
    },
    containerTable: {
        flexDirection: 'row',
        height: 30,
        alignSelf: 'center',
        borderColor: COLORS.bg2FB47C,
    },
    itemIdText: {
        color: 'white',
    },
    itemId: {
        // flex: 1,
        // alignItems: 'flex-start',
        // justifyContent: 'center',
        // borderLeftWidth: 1,
        // borderColor: COLORS.bgFFFFFF,
        // backgroundColor: COLORS.bg2FB47C,
        // paddingHorizontal: 5
    },
    itemTitle: {
        flex: 1,
        borderLeftWidth: 1,
        borderRightWidth: 1,
        borderColor: COLORS.bgFFFFFF,
        justifyContent: 'center',
        paddingLeft: 7,
    },
    widthTable: {
        // flex: 1,
        borderLeftWidth: 1,
        borderColor: COLORS.bgFFFFFF,
        justifyContent: 'center',
        paddingLeft: 5
    }
})

const MULTICAT_STORE_DEFINED = new Set(["2534", "1603", "5950", "403", "1601", "13371", "1639", "9252", "7105", "2441", "2053", "294", "2416", "2663", "1379", "548", "2073", "911", "4442", "1337", "7744", "9194", "517", "17861", "7728", "907", "193", "7696", "1223", "816", "878", "906", "5439", "12322", "4656", "11499", "3797", "479", "2234", "6322", "396", "97", "2043", "2320", "2278", "1700", "580", "1524", "616", "924", "5324", "254", "81", "1496", "1656", "1878", "14876", "2448", "939", "173", "5129", "5940", "1478", "796", "5916", "14875", "6173", "10532", "2777", "11527", "4566", "13865", "2406", "2138", "15925", "7874", "5379", "4335", "1495", "4579", "4734", "8692", "2104", "4772", "4777", "6372", "2662", "5044", "4564", "12964", "12631", "8696", "546", "125", "13934", "7017", "27901", "1919", "2539", "5121", "7339", "2359", "4773", "4190", "4874", "12261", "1416", "4873", "2080", "324", "12701", "2466", "2465", "2088", "4858", "2176", "4491", "12795", "8995", "16860", "2175", "2331", "10708", "5357", "860", "8112", "2779", "5797", "12325", "5359", "8646", "4909", "1208", "14868", "9170", "12500", "1582", "1500", "6242", "1499", "9505", "2188", "1964", "4196", "4599", "11875", "7025", "1788", "9006", "9104", "12326", "13954", "12814", "8996", "15875", "12484", "15879", "9025", "1209", "821", "705", "1341", "733", "704", "1259", "235", "2141", "170", "678", "19", "1211", "177", "356", "2275", "754", "723", "477", "1251", "1556", "95", "12957", "1280", "96", "686", "1014", "547", "1485", "316", "335", "803", "1785", "779", "17", "440", "47", "607", "267", "761", "295", "9453", "555", "333", "1412", "292", "1827", "185", "807", "1328", "778", "2007", "463", "298", "431", "1336", "1884", "1254", "10600", "769", "664", "12", "1673", "12275", "10701", "1210", "889", "12680", "13907", "15", "15889", "392", "253", "1874", "240", "1986", "6357", "623", "10979", "1287", "452", "504", "1107", "418", "334", "793", "1278", "182", "762", "777", "12380", "1562", "8722", "1713", "875", "22", "209", "15877", "363", "420", "365", "1286", "12211", "487", "15874", "10710", "732", "1563", "1026", "281", "1039", "172", "1910", "519", "196", "6462", "1965", "9360", "103", "1711", "174", "876", "241", "5651", "146", "9099", "250", "1825", "1349", "1110", "1410", "903", "1113", "16886", "9169", "613", "800", "10824", "13897", "13893", "13963", "12560", "13895", "12850", "10833", "12937", "9816", "12959", "9815", "10142", "12565", "12840", "12958", "12608", "12847", "12849", "10602", "11534", "10557", "11531", "13957", "12610", "12960", "10599", "13959"]);
const MULTICAT_USER_DEFINED3 = new Set(["85790", "143227", "59336", "48402", "162821", "165616", "70247", "61804", "142173", "165065", "165103", "162850", "87328", "111524", "40841"]);
