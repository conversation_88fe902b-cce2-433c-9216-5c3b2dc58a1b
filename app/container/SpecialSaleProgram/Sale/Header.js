import React, { useState } from 'react';
import { View, Image, TouchableOpacity } from 'react-native';
import { connect } from 'react-redux';
import { MyText, Icon } from '@components';
import { constants } from "@constants";
import { COLORS } from '@styles';
import { HeaderRight, HeaderLeft, BaseHeader } from '@header';
import { translate } from "@translate";
import ModalCombo from './component/ModalCombo';

const Header = ({ openDrawer, netInfo, userInfo, title }) => {
    const [visible, setVisible] = useState(false);
    const switchModal = (value) => () => {
        setVisible(value);
    }
    return (
        <BaseHeader>
            <HeaderLeft
                onPress={openDrawer}
                iconInfo={{
                    iconSet: "SimpleLineIcons",
                    name: "menu",
                    color: COLORS.icFFFFFF,
                    size: 24
                }}
            />
            <HeaderCenter
                title={title}
                info={userInfo}
                onPress={switchModal(true)}
            />
            <HeaderRight info={netInfo} />
            {/* <ModalCombo
                isVisible={visible}
                hideModal={switchModal(false)}
            /> */}
        </BaseHeader>
    );
}

const mapStateToProps = (state) => ({
    netInfo: state.networkReducer,
    userInfo: state.userReducer
});

export default connect(mapStateToProps)(Header);

const HeaderCenter = ({ title, info, onPress }) => {
    const { userName, storeID } = info;
    return (
        <View style={{
            height: 54,
            width: constants.width - 100,
            flexDirection: "row"
        }}>
            <View style={{
                justifyContent: "center",
                height: 54,
                width: constants.width - 150
            }}>
                <MyText
                    style={{
                        color: COLORS.txtF4F7B9,
                        fontWeight: "bold"
                    }}
                    text={title}
                />
                <View style={{
                    flexDirection: "row",
                    alignItems: "center"
                }}>
                    <Image
                        style={{ width: 11.5, height: 11.5 }}
                        source={{ uri: "logo_tgdd" }}
                    />
                    <MyText
                        style={{
                            color: COLORS.txtFFFFFF,
                            marginTop: 2
                        }}
                        text={` ${translate('common.store')} ${storeID} - User ${userName}`}
                        addSize={-2}
                    />
                </View>
            </View>
            <TouchableOpacity style={{
                justifyContent: "center",
                alignItems: "center",
                height: 54,
                width: 50,
            }}
                activeOpacity={0.8}
                onPress={onPress}
            >
                <Icon
                    iconSet={"Foundation"}
                    name={"burst-sale"}
                    color={COLORS.icFFFC00}
                    size={44}
                />
            </TouchableOpacity>
        </View>
    );
}