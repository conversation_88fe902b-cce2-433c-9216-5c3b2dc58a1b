import React, { useEffect, useRef, useState, useCallback } from 'react';
import {
    View,
    Text,
    FlatList,
    TouchableOpacity,
    StyleSheet,
    Animated,
    Image
} from 'react-native';
import { COLORS } from '../../../styles';
import { helper } from '@common';

const FilterProduct = ({ data, toggleSelection }) => {
    const [modifyData, setMofifyData] = useState([]);
    const [expandedGroups, setExpandedGroups] = useState({});

    useEffect(() => {
        if (data?.length > 0) {
            setMofifyData(data);
        }
    }, [data]);

    useEffect(() => {
        if (modifyData?.length > 0) {
            toggleSelection(modifyData);
        }
    }, [modifyData]);

    // Khởi tạo animation cho từng item
    const animatedValues = useRef(
        data.map(() => new Animated.Value(0))
    ).current;

    // Callback xử lý sự kiện khi item hiển thị
    const onViewableItemsChanged = useCallback(
        ({ viewableItems }) => {
            viewableItems.forEach(({ index }) => {
                if (index !== null) {
                    Animated.timing(animatedValues[index], {
                        toValue: 1,
                        duration: 500, // Animation 0.5s
                        useNativeDriver: true
                    }).start();
                }
            });
        },
        [animatedValues]
    );

    // Dùng useRef để tránh thay đổi onViewableItemsChanged mỗi lần render
    const viewabilityConfigCallbackPairs = useRef([{ onViewableItemsChanged }]);

    const toggleExpand = (groupIndex) => {
        setExpandedGroups((prev) => ({
            ...prev,
            [groupIndex]: !prev[groupIndex] // Toggle trạng thái nhóm
        }));
    };

    return (
        <View
            style={{
                flex: 1,
                flexDirection: 'row',
                backgroundColor: COLORS.bgFFFFFF,
                padding: 10
            }}>
            <FlatList
                showsVerticalScrollIndicator={false}
                data={modifyData}
                keyExtractor={(item) => item.PropertyID.toString()}
                viewabilityConfigCallbackPairs={
                    viewabilityConfigCallbackPairs.current
                }
                renderItem={({ item, index: groupIndex }) => {
                    if (item.ProductPropValueBOLst?.length <= 0) return null;
                    const isExpanded = expandedGroups[groupIndex] || false;
                    const displayedItems = isExpanded
                        ? item.ProductPropValueBOLst
                        : item.ProductPropValueBOLst.slice(0, 4);
                    return (
                        <View
                            style={{
                                margin: 10,
                                flex: 1,
                                backgroundColor: '#fff',
                                borderRadius: 12,
                                padding: 10,
                                shadowColor: '#000',
                                shadowOffset: { width: 0, height: 2 },
                                shadowOpacity: 0.1,
                                shadowRadius: 4,
                                elevation: 3 // For Android shadow effect,
                            }}>
                            <Animated.View
                                style={{
                                    paddingVertical: 5,
                                    paddingHorizontal: 10,
                                    opacity: animatedValues[groupIndex], // Fade-in
                                    transform: [
                                        {
                                            translateY: animatedValues[
                                                groupIndex
                                            ].interpolate({
                                                inputRange: [0, 1],
                                                outputRange: [30, 0] // Trượt lên
                                            })
                                        }
                                    ]
                                }}>
                                <Text
                                    style={{
                                        fontSize: 15,
                                        fontWeight: 'bold',
                                        marginBottom: 10
                                    }}>
                                    {item.PropertyName}
                                </Text>

                                <FlatList
                                    data={displayedItems}
                                    keyExtractor={(value) =>
                                        value.ValueID.toString()
                                    }
                                    numColumns={2}
                                    renderItem={({
                                        item: value,
                                        index: valueIndex
                                    }) => (
                                        <TouchableOpacity
                                            onPress={() => {
                                                const newData =
                                                    helper.deepCopy(modifyData);
                                                newData[
                                                    groupIndex
                                                ].ProductPropValueBOLst[
                                                    valueIndex
                                                ].selected =
                                                    !newData[groupIndex]
                                                        .ProductPropValueBOLst[
                                                        valueIndex
                                                    ].selected;
                                                setMofifyData(newData);
                                            }}
                                            style={{
                                                flex: 1,
                                                margin: 5,
                                                padding: 10,
                                                borderRadius: 5,
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                borderWidth: value.selected
                                                    ? 1
                                                    : StyleSheet.hairlineWidth,
                                                borderColor: value.selected
                                                    ? '#A5D6A7'
                                                    : COLORS.bgA7A7A7,
                                                minHeight: 40,
                                                backgroundColor: value.selected
                                                    ? '#E8F5E9'
                                                    : COLORS.bgFFFFFF,
                                                maxWidth: '50%'
                                            }}>
                                            {item.IsManu ? (
                                                <View style={{}}>
                                                    <Image
                                                        source={{
                                                            uri: value.SmallLogo
                                                        }}
                                                        style={{
                                                            width: 100,
                                                            height: 30
                                                        }}
                                                        resizeMode="contain"
                                                    />
                                                </View>
                                            ) : (
                                                <Text
                                                    numberOfLines={2}
                                                    ellipsizeMode="tail"
                                                    style={{
                                                        fontWeight:
                                                            value.selected
                                                                ? 'bold'
                                                                : null,
                                                        color: value.selected
                                                            ? '#2E7D32'
                                                            : COLORS.bg000000,
                                                        textAlign: 'center',
                                                        width: '100%',
                                                        fontSize: 13
                                                    }}>
                                                    {value.Value}
                                                </Text>
                                            )}
                                        </TouchableOpacity>
                                    )}
                                />

                                {item.ProductPropValueBOLst.length > 4 && (
                                    <TouchableOpacity
                                        onPress={() => toggleExpand(groupIndex)}
                                        style={{
                                            marginTop: 10,
                                            alignItems: 'center',
                                            padding: 8,
                                            borderRadius: 5,
                                            alignSelf: 'flex-end'
                                        }}>
                                        <Text
                                            style={{
                                                color: COLORS.bg1E88E5,
                                                textDecorationLine: 'underline',
                                                fontSize: 13
                                            }}>
                                            {isExpanded
                                                ? 'Thu gọn'
                                                : 'Xem thêm'}
                                        </Text>
                                    </TouchableOpacity>
                                )}
                            </Animated.View>
                        </View>
                    );
                }}
            />
        </View>
    );
};

export default FilterProduct;
