
import { API_CONST } from '@constants';
import { helper } from "@common";
import {
    apiBase,
    METHOD,
    EMPTY
} from '@config';
import { translate } from '@translate';


const {
    API_SEARCH_PRODUCT,
    API_GET_CATEGORIES_FILTER,
    API_GET_PROP_BY_CATEGORY,
    API_GET_MENU,
    API_GET_MANU_BY_CATEGORY,
    API_GET_PRODUCTS_BY_FILTER,
} = API_CONST;

const SET_PRODUCT_SEARCH = "SET_PRODUCT_SEARCH";
const SET_STATE_FILTER = "SET_STATE_FILTER"

export const saleAction = {
    SET_PRODUCT_SEARCH,
    SET_STATE_FILTER
}

const regExpIMEI = new RegExp(/^[a-zA-Z0-9\-\%\+\/\$\.\/]{14,22}$/);
const regExpPRODID = new RegExp(/^\d{13}$/);
const regExpSIM = new RegExp(/^\d{10}$/);
const regWarranty = new RegExp('bảo hành', 'i');
const mapSiteID = (brandID) => {
    switch (true) {
        case (brandID == '11'):
            return '6';
        case (brandID == '15'):
            return '17';
        case (brandID == '17'):
            return '18';
        default:
            return brandID;
    }
}

export const searchKeywordProduct = function (keyword) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const brandID = getState().userReducer.brandID;
            const siteID = mapSiteID(brandID);
            let isSearchErp = regExpPRODID.test(keyword) ||
                regExpSIM.test(keyword) ||
                regExpIMEI.test(keyword);
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "saleScenarioTypeID": getState().specialSaleProgramReducer.saleScenarioTypeID,
                "siteID": siteID,
                "keyword": keyword,
                "isSearchErp": isSearchErp,
                "type": 0
            };
            apiBase(API_SEARCH_PRODUCT, METHOD.POST, body, { setTimeOut: 5000 }).then((response) => {
                console.log("searchKeywordProduct success", response);
                const { object } = response;
                if (helper.isArray(object) && object.length > 0) {
                    resolve({
                        isEmpty: !EMPTY,
                        description: "",
                        data: object
                    });
                }
                else {
                    resolve({
                        isEmpty: EMPTY,
                        description: translate("inventory_share.not_found_product_info"),
                        data: []
                    });
                }
            }).catch(error => {
                console.log("searchKeywordProduct error", error);
                reject(error.msgError);
            })
        })
    }
}

export const getCategories = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_GET_CATEGORIES_FILTER, METHOD.POST, body).then((result) => {
            console.log("getCategories success", result);

            if (result.object?.length > 0) {
                resolve(result.object)
            }
            else {
                reject("Không có thông tin bộ lọc")
            }
        }).catch((error) => {
            console.log("getCategories error", error);
            reject(error.msgError)
        })
    })
}

export const getFilterPropByCategories = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_GET_PROP_BY_CATEGORY, METHOD.POST, body).then((result) => {
            console.log("getFilterPropByCategories success", result);

            if (result.object?.length > 0) {
                resolve(result.object)
            }
            else {
                reject("Không có danh sách sản phẩm")
            }
        }).catch((error) => {
            console.log("getFilterPropByCategories error", error);
            reject(error.msgError)
        })
    })
}

export const getMenu = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_GET_MENU, METHOD.POST, body).then((result) => {
            console.log("getMenu success", result);

            if (result.object?.length > 0) {
                resolve(result.object)
            }
            else {
                reject("Không có danh sách menu")
            }
        }).catch((error) => {
            console.log("getMenu error", error);
            reject(error.msgError)
        })
    })
}

export const getManuByCategoryID = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_GET_MANU_BY_CATEGORY, METHOD.POST, body).then((result) => {
            console.log("getManuByCategoryID success", result);

            if (result.object?.length > 0) {
                resolve(result.object)
            }
            else {
                reject("Không có danh sách hãng")
            }
        }).catch((error) => {
            console.log("getManuByCategoryID error", error);
            reject(error.msgError)
        })
    })
}

export const getProductsByFilter = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_GET_PRODUCTS_BY_FILTER, METHOD.POST, body).then((result) => {
            console.log("getProductsByFilter success", result);

            if (result.object?.length > 0) {
                resolve(result.object)
            }
            else {
                reject("Không có danh sách sản phẩm")
            }
        }).catch((error) => {
            console.log("getProductsByFilter error", error);
            reject(error.msgError)
        })
    })
}




export const setProductSearch = function (product) {
    return (dispatch, getState) => {
        dispatch(set_product_search(product));
    }
}

export const set_product_search = (productSearch) => {
    return ({
        type: SET_PRODUCT_SEARCH,
        productSearch
    });
}


export const setStateFilter = (states) => {
    return ({
        type: SET_STATE_FILTER,
        states
    });
};