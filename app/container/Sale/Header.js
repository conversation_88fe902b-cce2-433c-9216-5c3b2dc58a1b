import React, { useState } from 'react';
import { View, Image, TouchableOpacity } from 'react-native';
import { connect, useSelector } from 'react-redux';
import { MyText, Icon } from '@components';
import { constants, ENUM } from "@constants";
import { COLORS } from '@styles';
import { HeaderLeft, BaseHeader } from '@header';
import { translate } from "@translate";
import ModalCombo from './component/ModalCombo';

const Header = ({ openDrawer, userInfo, title, navigation }) => {
    const [visible, setVisible] = useState(false);
    const switchModal = (value) => () => {
        setVisible(value);
    }
    return (
        <BaseHeader>
            <HeaderLeft
                onPress={openDrawer}
                iconInfo={{
                    iconSet: "SimpleLineIcons",
                    name: "menu",
                    color: COLORS.icFFFFFF,
                    size: 24
                }}
            />
            <HeaderCenter
                title={title}
                info={userInfo}
                onPress={switchModal(true)}
            />
            <HeaderRight goToHomeScreen={() => {
                navigation.reset({
                    index: 0,
                    routes: [{ name: 'Home' }],
                });
            }} />
            {/* <ModalCombo
                isVisible={visible}
                hideModal={switchModal(false)}
            /> */}
        </BaseHeader>
    );
}

const mapStateToProps = (state) => ({
    netInfo: state.networkReducer,
    userInfo: state.userReducer
});

export default connect(mapStateToProps)(Header);

const HeaderCenter = ({ title, info, onPress }) => {
    const { userName, storeID, brandID } = info;
    const isCombo = (`${brandID}` == 1) || (`${brandID}` == 2);
    const { saleScenarioTypeID } = useSelector(
        (_state) => _state.specialSaleProgramReducer
    );
    let newTitle;
    switch (saleScenarioTypeID) {
        case ENUM.SALE_SCENARIO_TYPE.PRE_ORDER:
            newTitle = `${title} (PRE)`;
            break;
        case ENUM.SALE_SCENARIO_TYPE.STAFF_PROMOTION:
            newTitle = `${title} (UDNV)`;
            break;
        default:
            newTitle = title;
    }
    return (
        <View style={{
            height: 54,
            width: constants.width - 100,
            flexDirection: "row",
            paddingRight: 14
        }}>
            <View style={{
                justifyContent: "center",
                height: 54,
                width: constants.width - 164
            }}>
                <MyText
                    style={{
                        color: COLORS.txtF4F7B9,
                        fontWeight: "bold"
                    }}
                    text={newTitle}
                />
                <View style={{
                    flexDirection: "row",
                    alignItems: "center"
                }}>
                    <Image
                        style={{ width: 11.5, height: 11.5 }}
                        source={{ uri: "logo_tgdd" }}
                    />
                    <MyText
                        style={{
                            color: COLORS.txtFFFFFF,
                            marginTop: 2
                        }}
                        text={` ${translate('common.store')} ${storeID} - User ${userName}`}
                        addSize={-2}
                    />
                </View>
            </View>
            {/* {isCombo && <TouchableOpacity style={{
                justifyContent: "center",
                alignItems: "center",
                height: 54,
                width: 50,
            }}
                activeOpacity={0.8}
                onPress={onPress}
            >
                <Icon
                    iconSet={"Foundation"}
                    name={"burst-sale"}
                    color={'yellow'}
                    size={44}
                />
            </TouchableOpacity>} */}
        </View>
    );
}

const HeaderRight = ({ goToHomeScreen }) => {
    return (
        <TouchableOpacity style={{
            justifyContent: "center",
            alignItems: "center",
            height: 54,
            width: 50,
        }}
            activeOpacity={0.8}
            onPress={goToHomeScreen}
        >
            <Icon
                iconSet={"Ionicons"}
                name={"home"}
                color={COLORS.icFFFFFF}
                size={30}
            />
        </TouchableOpacity>
    );
}