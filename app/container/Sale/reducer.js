
import { saleState } from './state';
import { saleAction } from './action';

const saleReducer = function (state = saleState, action) {
    switch (action.type) {
        case saleAction.SET_PRODUCT_SEARCH:
            return {
                ...state,
                productSearch: action.productSearch,
            };
        case saleAction.SET_STATE_FILTER:
            return {
                ...state,
                stateFilter: action.states,
            };

        default:
            return state;
    }
}

export { saleReducer };