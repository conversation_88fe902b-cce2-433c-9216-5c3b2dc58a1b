import {
    FlatList,
    KeyboardAvoidingView,
    Platform,
    StyleSheet,
    TextInput,
    TouchableOpacity,
    View,
    Alert
} from 'react-native';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { COLORS } from '@styles';
import {
    BaseLoading,
    Icon,
    MyText,
    hideBlockUI,
    showBlockUI
} from '@components';
import SafeAreaView from 'react-native-safe-area-view';
import { constants } from '@constants';
import { dateHelper, helper } from '@common';
import { useSelector } from 'react-redux';
import { Button } from '../AnKhangNew/components';
import ProductItem from './Component/ProductItem';
import {
    getInfoRequestReturn,
    searchRequestReturn,
    updateRequestReturn
} from './action';
import ModalSupportFee from './Component/ModalSupportFee';
import Title from './Component/Title';

const initialState = {
    isFetching: false,
    description: '',
    isError: false,
    data: []
};

const RequestReturn = () => {
    const [keyword, setKeyword] = useState('');
    const [stateRequestReturn, setStateRequestReturn] = useState(initialState);
    const [stateRequestInfo, setStateRequestInfo] = useState({
        isShowModal: false,
        dataRequest: {},
        isBlockModal: false
    });
    const [numberDateSearch, setNumberDateSearch] = useState(0);
    const viewMoreDay = useRef(
        new Date(new Date().setDate(new Date().getDate() - numberDateSearch))
    );
    const { storeID, userName } = useSelector((state) => state.userReducer);
    const handleReadBarcode = ({ keyWord, searchDate }) => {
        setStateRequestReturn({
            ...initialState,
            isFetching: true
        });
        const body = {
            keyWord,
            searchDate,
            loginUser: userName,
            loginStoreId: storeID
        };
        searchRequestReturn(body)
            .then((listRequest) => {
                setStateRequestReturn({
                    ...initialState,
                    data: listRequest
                });
            })
            .catch((msgError) => {
                setStateRequestReturn({
                    ...initialState,
                    isError: true,
                    description: msgError
                });
            });
    };
    const loadMore = () => {
        setNumberDateSearch((pre) => pre + 1);
    };
    const getSupportFee = (item) => () => {
        const body = {
            "errCode": item.errCode.trim(),
            "loginUser": userName,
            "loginStoreId": storeID
        };
        showBlockUI();
        getInfoRequestReturn(body)
            .then((infoRequest) => {
                let newInfoRequest = { ...infoRequest }
                newInfoRequest?.feeBOs?.forEach(({ feeDetailBOs }, index) => {
                    if (feeDetailBOs.length == 1 && feeDetailBOs[0].isReturn) {
                        newInfoRequest?.feeBOs?.splice(index, 1);
                    }
                });
                newInfoRequest.TotalFee = item.totalFee;
                newInfoRequest.actionId = item.actionId;
                hideBlockUI();
                setStateRequestInfo({
                    isShowModal: true,
                    dataRequest: newInfoRequest,
                    isBlockModall: false
                });
            })
            .catch((msgError) => {
                Alert.alert('THÔNG BÁO', msgError, [
                    {
                        text: 'BỎ QUA',
                        style: 'cancel',
                        onPress: hideBlockUI
                    },
                    {
                        text: 'THỬ LẠI',
                        style: 'default',
                        onPress: getSupportFee(item)
                    }
                ]);
            })
    };
    const handleReject = (item) => {
        const body = {
            "actionType": "CANCLE",
            "errId": item.errId,
            "saleOrderId": item.saleOrderId,
            "actionId": item.actionId,
            "requestedTime": item.requestedTime,
            "loginUser": userName,
            "loginStoreId": storeID
        }
        showBlockUI()
        updateRequestReturn(body)
            .then(() => {
                Alert.alert('THÔNG BÁO', 'Cập nhật thành công.', [
                    {
                        text: 'Ok',
                        style: 'default',
                        onPress: () => {
                            hideBlockUI()
                            setKeyword("")
                            handleReadBarcode({
                                keyWord: "",
                                searchDate: new Date(viewMoreDay.current).getTime()
                            });

                        }
                    }
                ]);
            })
            .catch((msgError) => {
                Alert.alert('THÔNG BÁO', msgError, [
                    {
                        text: 'BỎ QUA',
                        style: 'cancel',
                        onPress: hideBlockUI
                    },
                    {
                        text: 'THỬ LẠI',
                        style: 'default',
                        onPress: () => handleReject(item)
                    }
                ]);
            });

    }
    const confirmFee = (requestItem) => () => {
        const {
            errId,
            saleOrderBO: { saleOrderId, isRetrievalInvoice, contractExternalProcessing },
            actionId,
            requestedTime,
            feeBOs,
            attachmentBOs
        } = requestItem;
        const body = {
            actionType: 'ACCEPT',
            errId,
            saleOrderId,
            actionId,
            requestedTime,
            loginUser: userName,
            loginStoreId: storeID,
            feeBOs,
            isRetrievalInvoice,
            contractExternalProcessing,
            attachmentBOs
        };
        setStateRequestInfo({
            ...stateRequestInfo,
            isBlockModal: true
        });
        updateRequestReturn(body)
            .then(() => {
                Alert.alert('THÔNG BÁO', 'Cập nhật phiếu thành công.', [
                    {
                        text: 'Ok',
                        style: 'default',
                        onPress: () => {
                            setStateRequestInfo({
                                isShowModal: false,
                                dataRequest: {},
                                isBlockModal: false
                            });
                            setKeyword("")
                            handleReadBarcode({
                                keyWord: "",
                                searchDate: new Date(viewMoreDay.current).getTime()
                            });

                        }
                    }
                ]);
            })
            .catch((msgError) => {
                Alert.alert('THÔNG BÁO', msgError, [
                    {
                        text: 'BỎ QUA',
                        style: 'cancel',
                        onPress: () => {
                            setStateRequestInfo({
                                ...stateRequestInfo,
                                isBlockModal: false
                            });
                        }
                    },
                    {
                        text: 'THỬ LẠI',
                        style: 'default',
                        onPress: confirmFee(requestItem)
                    }
                ]);
            });
    };

    const onSearch = () => {
        handleReadBarcode({
            keyWord: keyword,
            searchDate: new Date(
                viewMoreDay.current
            ).getTime()
        });
    }

    useEffect(onSearch, [])

    useEffect(() => {
        hideBlockUI();
        // if (numberDateSearch <= 0) {
        //     return;
        // }
        viewMoreDay.current = new Date(
            new Date().setDate(new Date().getDate() - numberDateSearch)
        );
        handleReadBarcode({
            keyWord: keyword,
            searchDate: new Date(viewMoreDay.current).getTime()
        });
    }, [numberDateSearch]);
    const searchHeight = useRef(0);

    return (
        <SafeAreaView style={styles.container}>
            <KeyboardAvoidingView
                style={styles.mainScreen}
                behavior={Platform.OS === 'ios' ? 'padding' : null}
                keyboardVerticalOffset={Platform.OS === 'ios' ? 96 : 0}>
                <Title
                    title={"Danh sách phiếu yêu cầu đổi trả"}
                    addSize={3}
                />

                <View
                    style={styles.searchWrapper}
                    onLayout={({ nativeEvent }) => {
                        searchHeight.current = nativeEvent.layout.height;
                    }}>
                    <View
                        style={{
                            flexDirection: 'row',
                            width: constants.width - 50,
                            marginBottom: 4,
                            borderWidth: 1,
                            borderColor: COLORS.bdE4E4E4,
                            height: 38,
                            backgroundColor: COLORS.bgFFFFFF,
                            borderRadius: 19,
                            opacity: 1
                        }}>
                        <TextInput
                            value={keyword}
                            onChangeText={setKeyword}
                            placeholder="Tìm theo ERR, nhân viên tạo"
                            placeholderTextColor={COLORS.txt808080}
                            style={{
                                height: 36,
                                paddingLeft: 20,
                                fontSize: 12.5,
                                flex: 1
                            }}
                            onSubmitEditing={onSearch}
                        />
                        <TouchableOpacity
                            style={{
                                height: 38,
                                width: 27,
                                marginRight: 8,
                                justifyContent: 'center',
                                alignItems: 'flex-end'
                            }}
                            onPress={() =>
                                handleReadBarcode({
                                    keyWord: keyword,
                                    searchDate: new Date(
                                        viewMoreDay.current
                                    ).getTime()
                                })
                            }>
                            <Icon
                                iconSet="Ionicons"
                                name="search"
                                color={"#EE4D2D"}
                                size={22}
                            />
                        </TouchableOpacity>
                    </View>
                </View>

                <View style={{ flex: 1 }}>
                    <BaseLoading
                        isLoading={stateRequestReturn.isFetching}
                        textLoadingError={stateRequestReturn.description}
                        isError={stateRequestReturn.isError}
                        onPressTryAgains={onSearch}
                        content={
                            <FlatList
                                data={stateRequestReturn.data}
                                renderItem={({ item, index }) => {
                                    const backgroundColor =
                                        index % 2 === 0
                                            ? COLORS.bgFFFFFF
                                            : COLORS.bgF0F0F0;
                                    return (
                                        <ProductItem
                                            index={index}
                                            backgroundColor={backgroundColor}
                                            data={item}
                                            handleSupportFee={getSupportFee(item)}
                                            handleReject={() => { handleReject(item) }}
                                        />
                                    );
                                }}
                                keyExtractor={(item) => item.id}
                            />
                        }
                    />
                </View>
            </KeyboardAvoidingView>
            <View style={styles.buttonContainer}>
                <Button
                    text={`XEM THÊM ${dateHelper.formatDateDDMMYYYY(
                        new Date(
                            new Date().setDate(
                                new Date().getDate() - numberDateSearch - 1
                            )
                        )
                    )}`}
                    styleContainer={[styles.button]}
                    styleText={styles.buttonText}
                    onPress={loadMore}
                />
            </View>
            {stateRequestInfo.isShowModal && (
                <ModalSupportFee
                    isBlockModal={stateRequestInfo.isBlockModal}
                    isShow={stateRequestInfo.isShowModal}
                    hideModal={() => {
                        setStateRequestInfo({
                            ...stateRequestInfo,
                            isShowModal: false,
                        });
                    }}
                    confirm={confirmFee}
                    detailRequest={stateRequestInfo.dataRequest}
                    reject={() => {
                        setStateRequestInfo({
                            isShowModal: false,
                            dataRequest: {}
                        });
                    }}
                />
            )}
        </SafeAreaView>
    );
};

export default RequestReturn;

const styles = StyleSheet.create({
    button: {
        backgroundColor: "#EE4D2D",
        borderRadius: constants.getSize(5),
        height: constants.getSize(40),
        marginHorizontal: 10,
        paddingHorizontal: 20
    },
    buttonContainer: {
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: constants.heightBottomSafe > 0 ? 0 : 10,
        marginTop: 20
    },
    buttonText: {
        color: COLORS.bgFFFFFF,
        fontSize: 14,
        fontWeight: 'bold'
    },
    container: {
        backgroundColor: COLORS.bgFFFFFF,
        flex: 1
    },
    mainScreen: { flex: 1 },
    searchWrapper: {
        alignItems: 'center',
        flexDirection: 'row',
        marginHorizontal: 18,
        marginVertical: 10
    }
});
