
import RNFetchBlob from 'rn-fetch-blob';
import { helper } from '@common';
import { apiBase, METHOD } from '@config';
import { API_CONST, CONFIG } from '@constants';
const { API_GET_STORE_BY_ID } = API_CONST;

const one_second = 1000;
const SPEED = [0, 20, 40];
const LEVEL = [0, 1, 2, 3];
const TIME_OUT = [10, 30, 60, 90].map(ele => ele * one_second);
const IMAGE_SIZE_KB = 100.24
const IMAGE_URL = 'https://eu.httpbin.org/stream-bytes/500000';

const UPDATE_CONFIG_NETWORK_INFO = "UPDATE_CONFIG_NETWORK_INFO";
export const networkAction = {
    UPDATE_CONFIG_NETWORK_INFO
}

export const updateConfigNetworkInfo = (netInfo) => {
    return function (dispatch, getState) {
        CONFIG.isPRODUCTION && dispatch(update_config_network_info(netInfo));
    }
}

export const getMeasureConnection = (onchangelistener) => {
    let delay = 0;
    let timerID = setTimeout(async function getNetWorkMeasure() {
        const { speed, level } = await getSpeedLevelNetwork();
        onchangelistener({ speed, level });
        switch (level) {
            case LEVEL[3]:
                delay = TIME_OUT[3];
                break;
            case LEVEL[2]:
                delay = TIME_OUT[2];
                break;
            case LEVEL[1]:
                delay = TIME_OUT[1];
                break;
            default:
                delay = TIME_OUT[0];
                break;
        }
        timerID = setTimeout(getNetWorkMeasure, delay);
    }, delay)
}

export const findStoreByIP = function (ipAddress) {
    let body = { "ip": ipAddress };
    return new Promise((resolve, reject) => {
        apiBase(API_GET_STORE_BY_ID, METHOD.POST, body).then(response => {
            console.log("findStoreByIP success", response);
            const { object } = response;
            if (helper.IsNonEmptyArray(object)) {
                resolve(true);
            }
            else {
                resolve(false);
            }
        }).catch(error => {
            console.log("findStoreByIP error", error);
            resolve(true);
        })
    })
}

const getSpeedLevelNetwork = async () => {
    let speed = 0;
    let level = 0;
    try {
        const start = new Date().getTime();
        await RNFetchBlob.config({fileCache: false}).fetch(METHOD.GET, IMAGE_URL, null);
        const end = new Date().getTime();
        const duration = (end - start) / one_second;
        const speedKBs = (IMAGE_SIZE_KB / duration);
        speed = speedKBs.toFixed(1);
        level = get_level_signal(speed);
    } catch (error) {
        console.log("getSpeedLevelNetwork error", error);
    }
    return { speed, level };
}

const get_level_signal = (speed) => {
    if (speed <= SPEED[0]) {
        return LEVEL[0];
    }
    if (speed < SPEED[1]) {
        return LEVEL[1];
    }
    if (speed < SPEED[2]) {
        return LEVEL[2];
    }
    return LEVEL[3];
}

const update_config_network_info = (netInfo) => {
    return {
        type: UPDATE_CONFIG_NETWORK_INFO,
        netInfo
    };
}
