import React, { useState } from 'react';
import {
    View,
    TouchableOpacity,
    StyleSheet,
    Alert
} from 'react-native';
import {
    Icon,
    showBlockUI,
    hideBlockUI,
    MyText
} from "@components";
import { constants } from '@constants';
import { translate } from '@translate';
import { COLORS } from "@styles";
import { helper } from '@common';
import TitleProduct from './component/TitleProduct';
import ModalSearchSim from '../../../ShoppingCart/component/Modal/ModalSearchSim';

const SimInfo = ({
    product,
    onSearchSim,
    simPrice
}) => {
    const {
        productName,
        imei,
    } = product;

    return (
        <View style={{
            width: constants.width - 16,
            alignSelf: "center",
            marginTop: 4
        }}>
            <View style={{
                width: constants.width - 16,
                flexDirection: "row",
                paddingHorizontal: 10,
            }}>
                <Icon
                    iconSet={"Ionicons"}
                    name={"checkmark"}
                    size={16}
                    color={COLORS.ic288AD6}
                    style={{ marginTop: 2 }}
                />
                <MyText
                    text={`${productName} (${translate('editSaleOrder.quantity_short')}: ${1} )`}
                    style={{
                        color: COLORS.txt333333,
                        width: constants.width - 72
                    }}
                />
            </View>
            {
                simPrice > 0 &&
                <View style={{
                    paddingLeft: 26,
                    width: constants.width - 16,
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                }}>
                    <MyText
                        text={helper.convertNum(simPrice)}
                        style={{
                            color: COLORS.txtD0021B,
                            fontWeight: 'normal'
                        }}
                    />
                </View>
            }
            <View style={{
                paddingLeft: 26,
                marginTop: 4,
                width: constants.width - 16,
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
            }}>
                <MyText
                    text={`${translate('shoppingCart.IMEI')} ${imei}`}
                    style={{
                        color: COLORS.txt333333
                    }}
                />
                <TouchableOpacity style={{
                    flexDirection: "row",
                    alignItems: "center"
                }}
                    onPress={onSearchSim}
                >
                    <Icon
                        iconSet={"MaterialIcons"}
                        name={"sim-card"}
                        color={COLORS.ic147EFB}
                        size={16}
                    />
                    <MyText
                        text={translate('shoppingCart.type_IMEI_SIM')}
                        style={{
                            color: COLORS.txt808080,
                            color: COLORS.txt147EFB,
                            textDecorationLine: 'underline'
                        }}
                    />
                </TouchableOpacity>
            </View>
        </View>
    );
}

const SimProduct = ({
    product,
    onUpdateSim,
    outputStoreID,
    simPrice,
    actionShoppingCart
}) => {
    const [isVisibleSearchSim, setIsVisibleSearchSim] = useState(false);
    const [dataPackage, setDataPackage] = useState([]);
    const getSimPackage = (productID, promotionListId, promotionID) => () => {
        showBlockUI();
        actionShoppingCart.getPackageSimPromotion({
            productID,
            promotionListId,
            promotionID
        }).then(data => {
            hideBlockUI();
            setDataPackage(data);
            setIsVisibleSearchSim(true);
        }).catch(msgError => {
            Alert.alert(translate("common.notification_uppercase"),
                msgError,
                [
                    {
                        text: translate("common.btn_skip"),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate("common.btn_notify_try_again"),
                        style: "default",
                        onPress: getSimPackage(productID, promotionListId, promotionID)
                    }
                ]
            );
        });
    }

    return (
        <View style={[{
            backgroundColor: COLORS.bgFDF9E5,
            width: constants.width,
            padding: 8
        }]}>
            <TitleProduct
                product={product}
            />
            <SimInfo
                simPrice={simPrice}
                product={product}
                onSearchSim={getSimPackage(product.productID, product.promotionListId, product.promotionID)}
            />
            {
                isVisibleSearchSim &&
                <ModalSearchSim
                    isVisible={isVisibleSearchSim}
                    hideModal={() => {
                        setIsVisibleSearchSim(false);
                    }}
                    dataPackage={dataPackage}
                    simInfo={{
                        "ProductID": product.productID,
                        "OutputStoreID": outputStoreID,
                    }}
                    updateSim={(info, data) => {
                        onUpdateSim({
                            "imei": info.IMEI,
                            "packagesTypeId": info.PackagesTypeID,
                            "salePrice": data.salePrice,
                            "vat": product.vat,
                            "vatPercent": product.vatPercent
                        });
                        setIsVisibleSearchSim(false);
                    }}
                />
            }
        </View>
    );
}

export default SimProduct;