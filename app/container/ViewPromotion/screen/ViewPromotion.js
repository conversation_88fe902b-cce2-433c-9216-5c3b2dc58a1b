/**
 * Sample React Native App
 *
 *
 * @format
 * @flow strict-local
 */

import React, { Component } from 'react';
import { <PERSON>, Alert, BackHandler, Dimensions, TouchableOpacity } from 'react-native';
import { IndicatorViewPager, PagerTitleIndicator } from 'react-native-best-viewpager';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { date<PERSON><PERSON><PERSON>, helper } from '@common';
import { constants } from '@constants';
import moment from 'moment';
import { BaseLoading, showBlockUI, hideBlockUI, MyText, Icon } from '@components';

import { translate } from '@translate';
import { COLORS } from '@styles';
import ModalFilterSearch from '../components/ModalFilterSearch';
import Promotion from '../../Detail/Promotion/index';
import SalePromtion from '../../Detail/SalePromotion/index';

import * as actionDetailCreator from '../../Detail/action';
import * as actionLocationCreator from '../../Location/action';
import * as actionCartCreator from '../../ShoppingCart/action';
import * as actionPouchCreator from '../../PouchRedux/action';

class ViewPromotion extends Component {
    constructor() {
        super();
        this.state = {
            setKeyPromotionSelected: new Set(),
            setGroupIDCondition: new Set(),
            setGroupIDPhoneValidate: new Set(),
            phoneValidate: '',
            isApplyPhone: false,

            inventoryStatusID: 1,
            quantity: 1,
            quantityInStock: 1,

            productInfo: {},
            productSecondInfo: {},
            productExhibitInfo: {},

            packagesTypeId: 0,
            retailPriceVAT: 0,

            isVisibleInstallment: false,
            saleProgramInfo: {
                logo: '',
                partnerID: undefined,
                saleProgramID: 0,
                saleProgramName: ''
            },
            isHasSaleProgram: false,

            deliveryType: 1,
            receiveType: 1,

            outputStoreID: 0,
            storeRequests: [],

            expandPromotion: [],
            expandPromotionDelivery: [],

            isVisibleFIFO: false,
            fifoProduct: {},
            isVisibleFeature: false,
            isVisibleConfig: false,
            isVisibleLockInfo: false,
            defaultDelivery: {
                gender: true,
                contactPhone: '',
                contactName: '',
                contactAddress: '',
                provinceID: 0,
                districtID: 0,
                wardID: 0
            },
            defaultReceiveInfo: {},
            isVisiblePreOrder: false,

            isVisibleDrawerFilterSearch: false,

            promotionDate: dateHelper.convertJStoCwithString(new Date()).split('.')[0]
        };
        this.expDataPromotion = {};
        this.expDataPromotionDelivery = {};
        this.defaultSaleProgram = {
            logo: '',
            partnerID: undefined,
            saleProgramID: 0,
            saleProgramName: ''
        };
        this.FLOAT_ACTION = [
            {
                text: 'Xem lock hàng pre-order',
                icon: { uri: 'bt_lock_product' },
                name: '0',
                tintColor: 'transparent'
            },
            {
                text: translate('detail.view_lock_product'),
                icon: { uri: 'bt_lock_product' },
                name: '1',
                tintColor: 'transparent'
            },
            {
                text: translate('detail.go_to_cart'),
                icon: { uri: 'ic_cartshopping' },
                name: '2',
                tintColor: 'transparent'
            }
        ];
    }

    componentDidMount() {
        this.getDataSearch();
        BackHandler.addEventListener('addEventListener', this.onBackButtonPressed);
    }

    componentDidUpdate(preProps, preState) {
        if (preProps.productSearch !== this.props.productSearch) {
            this.onChangeProductSearch();
        }
        if (preProps.allKeyPromotion !== this.props.allKeyPromotion) {
            const { allKeyPromotion, allGroupID, defaultKeyPromotion } = this.props;
            this.keepKeyPromotionSelected(allKeyPromotion, allGroupID, defaultKeyPromotion);
        }
    }

    onBackButtonPressed = () => {
        return true;
    };

    componentWillUnmount() {
        // this.backHandler.remove();
        BackHandler.removeEventListener('hardwareBackPress', this.onBackButtonPressed);
    }

    render() {
        const {
            setKeyPromotionSelected,
            setGroupIDCondition,
            setGroupIDPhoneValidate,
            phoneValidate,
            isApplyPhone,

            inventoryStatusID,
            deliveryType,

            productInfo,
            productSecondInfo,
            productExhibitInfo,

            outputStoreID,
            expandPromotion,

            isVisibleDrawerFilterSearch,
            promotionDate,

            saleProgramInfo: { saleProgramID },
            saleProgramInfo
        } = this.state;
        const {
            searchInfo: { imei, isImeiSim },
            promotion,
            salePromotion,
            statePromotion,
            stateProduct,
            stateSecond,
            userInfo: { storeID, brandID },
            cartRequest,
            actionDetail,
            productSearch
        } = this.props;
        const isEmptyPromotion = promotion.length == 0;
        const isEmptySalePromotion = salePromotion.length == 0;
        const disabledPager = getValueDisabledPager(
            inventoryStatusID,
            productInfo,
            productSecondInfo,
            productExhibitInfo
        );
        const productOrder = this.getProductInfoByStatus();
        return (
            <View style={{ flex: 1, backgroundColor: COLORS.bgFFFFFF }}>
                <View style={{ flexDirection: 'row' }}>
                    <View style={{ flex: 1 }}>
                        <ProductInfo info={productSearch} />
                    </View>

                    <TouchableOpacity
                        style={{
                            marginRight: 10,
                            width: 40,
                            alignItems: 'center',
                            justifyContent: 'center'
                        }}
                        onPress={() => {
                            this.setState({
                                isVisibleDrawerFilterSearch: true
                            });
                        }}
                        activeOpacity={0.7}>
                        <Icon
                            iconSet="FontAwesome"
                            name="filter"
                            style={{
                                color: COLORS.ic2C8BD7,
                                fontSize: 18,
                                marginLeft: 20
                            }}
                        />
                    </TouchableOpacity>
                </View>

                <IndicatorViewPager
                    style={{ flex: 1, flexDirection: 'column-reverse' }}
                    indicator={
                        <PagerTitleIndicator
                            initialPage={0}
                            style={{ backgroundColor: COLORS.bg00A98F }}
                            titles={[translate('detail.promotions'), translate('detail.attachments')]}
                            trackScroll
                            selectedBorderStyle={{
                                backgroundColor: COLORS.bgFFF000,
                                height: 2,
                                position: 'absolute',
                                bottom: 0,
                                left: 0,
                                right: 0
                            }}
                            renderTitle={(index, title, isSelected) => {
                                return (
                                    <View
                                        style={{
                                            width: Dimensions.get('window').width / 2 - 30
                                        }}>
                                        <MyText
                                            style={{
                                                color: isSelected
                                                    ? COLORS.txtFFF000
                                                    : disabledPager
                                                        ? COLORS.txtC0C0C0
                                                        : COLORS.txtFFFFFF,
                                                fontWeight: 'bold',
                                                textAlign: 'center'
                                            }}
                                            text={title}
                                            addSize={isSelected ? 2 : 0}
                                        />
                                    </View>
                                );
                            }}
                            disabled={disabledPager}
                        />
                    }
                    horizontalScroll={!disabledPager}
                    keyboardShouldPersistTaps="always">
                    <View style={{ flex: 1 }} key="Promotion">
                        {!disabledPager && (
                            <BaseLoading
                                isLoading={statePromotion.isFetching}
                                isEmpty={isEmptyPromotion}
                                textLoadingError={
                                    statePromotion.isError
                                        ? statePromotion.description
                                        : translate('detail.no_promotion')
                                }
                                isError={statePromotion.isError}
                                onPressTryAgains={this.getPromotion}
                                content={
                                    <Promotion
                                        dataPromotion={promotion}
                                        setKeyPromotionSelected={setKeyPromotionSelected}
                                        updateKeyPromotionSelected={this.updateKeyPromotionSelected}
                                        setGroupIDCondition={setGroupIDCondition}
                                        updateGroupIDCondition={(
                                            newSetGroupIDCondition,
                                            newSetKeyPromotionSelected,
                                            newSetGroupIDPhoneValidate
                                        ) => {
                                            this.setState({
                                                setGroupIDCondition: newSetGroupIDCondition,
                                                setKeyPromotionSelected: newSetKeyPromotionSelected,
                                                setGroupIDPhoneValidate: newSetGroupIDPhoneValidate
                                            });
                                        }}
                                        phoneValidate={phoneValidate}
                                        setGroupIDPhoneValidate={setGroupIDPhoneValidate}
                                        updateGroupIDPhoneValidate={(phoneNumber, newSetGroupIDPhoneValidate) => {
                                            this.setState({
                                                phoneValidate: phoneNumber,
                                                setGroupIDPhoneValidate: newSetGroupIDPhoneValidate
                                            });
                                        }}
                                        cancelPhoneValidate={(
                                            newSetGroupIDPhoneValidate,
                                            newSetKeyPromotionSelected
                                        ) => {
                                            this.setState({
                                                setGroupIDPhoneValidate: newSetGroupIDPhoneValidate,
                                                setKeyPromotionSelected: newSetKeyPromotionSelected
                                            });
                                        }}
                                        productInfo={{
                                            productID: productOrder.productID,
                                            storeID: outputStoreID
                                        }}
                                        isFloating
                                        isApplyPhone={isApplyPhone}
                                        productOrder={productOrder}
                                        deliveryInfo={{
                                            storeID,
                                            outputStoreID,
                                            deliveryTypeID: deliveryType
                                        }}
                                        actionDetail={actionDetail}
                                    />
                                }
                            />
                        )}
                    </View>

                    <View style={{ flex: 1 }} key="SalePromtion">
                        {!disabledPager && (
                            <BaseLoading
                                isLoading={statePromotion.isFetching}
                                isEmpty={isEmptySalePromotion}
                                textLoadingError={
                                    statePromotion.isError
                                        ? statePromotion.description
                                        : translate('detail.no_attachments')
                                }
                                isError={statePromotion.isError}
                                onPressTryAgains={this.getPromotion}
                                content={
                                    <SalePromtion
                                        dataSalePromotion={salePromotion}
                                        setKeyPromotionSelected={setKeyPromotionSelected}
                                        updateKeyPromotionSelected={this.updateKeyPromotionSelected}
                                        setGroupIDCondition={setGroupIDCondition}
                                        updateGroupIDCondition={(
                                            newSetGroupIDCondition,
                                            newSetKeyPromotionSelected
                                        ) => {
                                            this.setState(
                                                {
                                                    setGroupIDCondition: newSetGroupIDCondition,
                                                    setKeyPromotionSelected: newSetKeyPromotionSelected
                                                },
                                                this.removeExpandPromotion
                                            );
                                        }}
                                        productOrder={productOrder}
                                        deliveryInfo={{
                                            storeID,
                                            outputStoreID,
                                            deliveryTypeID: deliveryType
                                        }}
                                        expandPromotion={expandPromotion}
                                        getExpandPromotion={this.getExpandPromotion}
                                        phoneValidate={phoneValidate}
                                        setGroupIDPhoneValidate={setGroupIDPhoneValidate}
                                        updateGroupIDPhoneValidate={(phoneNumber, newSetGroupIDPhoneValidate) => {
                                            this.setState({
                                                phoneValidate: phoneNumber,
                                                setGroupIDPhoneValidate: newSetGroupIDPhoneValidate
                                            });
                                        }}
                                        cancelPhoneValidate={(
                                            newSetGroupIDPhoneValidate,
                                            newSetKeyPromotionSelected
                                        ) => {
                                            this.setState(
                                                {
                                                    setGroupIDPhoneValidate: newSetGroupIDPhoneValidate,
                                                    setKeyPromotionSelected: newSetKeyPromotionSelected
                                                },
                                                this.removeExpandPromotion
                                            );
                                        }}
                                        isFloating
                                        actionDetail={actionDetail}
                                    />
                                }
                            />
                        )}
                    </View>
                </IndicatorViewPager>

                <ModalFilterSearch
                    isVisible={isVisibleDrawerFilterSearch}
                    hideModal={() => {
                        this.setState({ isVisibleDrawerFilterSearch: false });
                    }}
                    oldFilter={{ promotionDate, saleProgramID }}
                    searchFilterResponse={(resFilter) => {
                        this.setState(
                            (prevState) => ({
                                isVisibleDrawerFilterSearch: false,
                                promotionDate: resFilter.promotionDate,
                                saleProgramInfo: {
                                    ...prevState.saleProgramInfo,
                                    saleProgramID: resFilter.saleProgramID
                                }
                            }),
                            () => this.getPromotion()
                        );
                    }}
                    getPartnerInstallment={this.getPartnerInstallment}
                    getProgramInstallment={this.getProgramInstallment}
                    outputStoreID={storeID}
                    saleProgramInfo={saleProgramInfo}
                    mainProduct={{
                        productID: productSearch.productID,
                        salePriceVAT: productSearch.salePriceVAT,
                        inventoryStatusID: productSearch.inventoryStatusID,
                        promotionListGroupID: null
                    }}
                />
            </View>
        );
    }

    getProductInfoByStatus = () => {
        const { inventoryStatusID, productInfo, productSecondInfo, productExhibitInfo } = this.state;
        switch (inventoryStatusID) {
            case 2:
                return productSecondInfo;
            case 3:
                return productExhibitInfo;
            default:
                return productInfo;
        }
    };

    onChangeProductSearch = () => {
        const { defaultDelivery } = this.state;
        const {
            productSearch,
            userInfo: { storeID },
            cartRequest,
            actionDetail,
            searchInfo: { isImeiSim }
        } = this.props;

        if (!helper.IsEmptyObject(productSearch)) {
            const {
                imei,
                productID,
                inventoryStatusID,
                salePrice,
                vat,
                vatPercent,
                quantity: quantityInStock,
                salePriceVAT
            } = productSearch;
            const {
                quantity,
                saleProgramInfo: { saleProgramID },
                deliveryType,
                outputStoreID,
                promotionDate
            } = this.state;
            this.setState(
                {
                    productInfo: productSearch,
                    quantityInStock,
                    retailPriceVAT: salePriceVAT,
                    deliveryType: defaultDelivery.deliveryType,
                    receiveType: defaultDelivery.receiveType
                },
                this.getPartnerInstallment
            );
            actionDetail.getPromotionForView({
                imei,
                productID,
                inventoryStatusID,
                price: salePrice,
                VAT: vat,
                VATPercent: vatPercent,
                storeID,
                appliedQuantity: quantity,
                outputStoreID,
                deliveryTypeID: deliveryType,
                storeRequests: [],
                saleProgramID,
                cartRequest,
                promotionDate: promotionDate
            });
            if (isImeiSim) {
                actionDetail.getSimPackage({
                    productID,
                    salePrice,
                    vat,
                    vatPercent
                });
            }
        }
    };

    getDataSearch = () => {
        const {
            searchInfo: { imei, inventoryStatusID, isImeiSim, productID, productIDERP },
            userInfo: { storeID },
            actionDetail
        } = this.props;
        const {
            saleProgramInfo: { saleProgramID },
            outputStoreID
        } = this.state;
        const isInstalment = saleProgramID > 0;
        actionDetail.getInfoProductSearch({
            imei,
            productID: productIDERP,
            productIDRef: productID,
            inventoryStatusID: inventoryStatusID || 1,
            storeID,
            saleProgramID,
            isInstalment,
            isImeiSim
        });
    };

    getPromotion = () => {
        const productOrder = this.getProductInfoByStatus();
        const {
            userInfo: { storeID },
            cartRequest,
            actionDetail,
            productSearch
        } = this.props;
        const {
            quantity,
            outputStoreID,
            deliveryType,
            storeRequests,
            saleProgramInfo: { saleProgramID },
            promotionDate
        } = this.state;

        actionDetail.getPromotionForView({
            imei: productOrder.imei,
            productID: productOrder.productID,
            inventoryStatusID: productOrder.inventoryStatusID,
            price: productOrder.salePrice,
            VAT: productOrder.vat,
            VATPercent: productOrder.vatPercent,
            storeID,
            appliedQuantity: quantity,
            outputStoreID,
            deliveryTypeID: deliveryType,
            storeRequests,
            saleProgramID,
            cartRequest,
            promotionDate: promotionDate
        });
    };

    getExpandPromotion = (product, keyPromotion, promotionGroup, subIndex) => {
        const {
            deliveryType,
            saleProgramInfo: { saleProgramID },
            outputStoreID,
            storeRequests,
            quantity
        } = this.state;
        const {
            userInfo: { storeID },
            cartRequest,
            actionDetail
        } = this.props;
        showBlockUI();
        actionDetail
            .getExpandSalePromotion({
                productID: product.productID,
                inventoryStatusID: product.inventoryStatusID,
                price: product.salePrice,
                VAT: product.vat,
                VATPercent: product.vatPercent,
                promotionGroupID: promotionGroup.promotionGroupID,
                isApplyTotalPromotion: promotionGroup.isApplyTotalPromotion,
                storeID,
                appliedQuantity: product.quantity,
                outputStoreID,
                deliveryTypeID: deliveryType,
                storeRequests,
                saleProgramID,
                cartRequest,
                promotionListId: product.promotionListId
            })
            .then(({ expPromotion, expPromotionDelivery, isApplyTotalPromotion }) => {
                promotionGroup.isApplyTotalPromotion = isApplyTotalPromotion;
                hideBlockUI();
                if (helper.IsNonEmptyArray(expPromotion)) {
                    this.expDataPromotion[keyPromotion] = {
                        data: expPromotion,
                        title: product.productName,
                        subIndex,
                        productID: product.productID
                    };
                    const newExpPromotion = Object.entries(this.expDataPromotion);
                    this.setState({ expandPromotion: newExpPromotion });
                }
                if (helper.IsNonEmptyArray(expPromotionDelivery)) {
                    this.expDataPromotionDelivery[keyPromotion] = {
                        data: expPromotionDelivery,
                        title: product.productName,
                        subIndex,
                        productID: product.productID
                    };
                    const newExpPromotionDelivery = Object.entries(this.expDataPromotionDelivery);
                    this.setState({
                        expandPromotionDelivery: newExpPromotionDelivery
                    });
                }
            })
            .catch((msgError) => {
                Alert.alert(translate('common.notification_uppercase'), msgError, [
                    {
                        text: translate('common.btn_skip'),
                        style: 'cancel',
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: 'default',
                        onPress: () => this.getExpandPromotion(product, keyPromotion, promotionGroup, subIndex)
                    }
                ]);
            });
    };

    removeExpandPromotion = () => {
        const { setKeyPromotionSelected, setGroupIDCondition, setGroupIDPhoneValidate } = this.state;
        Object.keys(this.expDataPromotion).forEach((keyPromotion) => {
            if (!setKeyPromotionSelected.has(keyPromotion)) {
                const { data } = this.expDataPromotion[keyPromotion];
                data.forEach((groupPromotion) => {
                    const { promotionGroupID, promotionProducts } = groupPromotion;
                    promotionProducts.forEach((product, index) => {
                        const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
                        const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
                        setKeyPromotionSelected.delete(key);
                        setGroupIDCondition.delete(promotionGroupID);
                        setGroupIDPhoneValidate.delete(promotionGroupID);
                    });
                });
                delete this.expDataPromotion[keyPromotion];
            }
        });
        const newExpPromotion = Object.entries(this.expDataPromotion);
        Object.keys(this.expDataPromotionDelivery).forEach((keyPromotion) => {
            if (!setKeyPromotionSelected.has(keyPromotion)) {
                const { data } = this.expDataPromotionDelivery[keyPromotion];
                data.forEach((groupPromotion) => {
                    const { promotionGroupID, promotionProducts } = groupPromotion;
                    promotionProducts.forEach((product, index) => {
                        const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
                        const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
                        setKeyPromotionSelected.delete(key);
                        setGroupIDCondition.delete(promotionGroupID);
                        setGroupIDPhoneValidate.delete(promotionGroupID);
                    });
                });
                delete this.expDataPromotionDelivery[keyPromotion];
            }
        });
        const newExpPromotionDelivery = Object.entries(this.expDataPromotionDelivery);
        this.setState({
            expandPromotion: newExpPromotion,
            expandPromotionDelivery: newExpPromotionDelivery,
            setKeyPromotionSelected,
            setGroupIDCondition,
            setGroupIDPhoneValidate
        });
    };

    updateKeyPromotionSelected = (setKeyPromotionSelected, excludePromotionIDs, saleProductGroupID, keyProduct) => {
        const { promotion, salePromotion, promotionDelivery, salePromotionDelivery } = this.props;
        if (helper.IsEmptyArray(excludePromotionIDs)) {
            this.setState({ setKeyPromotionSelected }, this.removeExpandPromotion);
        } else {
            let expandGift = [];
            let expandGiftDelivery = [];
            if (!helper.IsEmptyObject(this.expDataPromotion[keyProduct])) {
                expandGift = this.expDataPromotion[keyProduct].data;
            }
            if (!helper.IsEmptyObject(this.expDataPromotionDelivery[keyProduct])) {
                expandGiftDelivery = this.expDataPromotionDelivery[keyProduct].data;
            }
            const allPromotion = [...promotion, ...promotionDelivery, ...expandGift, ...expandGiftDelivery];
            const alllSalePromotion = [...salePromotion, ...salePromotionDelivery];
            const newSetKeyPromotionSelected = helper.excludeKeyPromotionSelected(
                allPromotion,
                alllSalePromotion,
                setKeyPromotionSelected,
                excludePromotionIDs,
                saleProductGroupID
            );
            this.setState({ setKeyPromotionSelected: newSetKeyPromotionSelected }, this.removeExpandPromotion);
        }
    };

    keepKeyPromotionSelected = (allKeyPromotion, allGroupID, defaultKeyPromotion) => {
        const { setKeyPromotionSelected, setGroupIDCondition, setGroupIDPhoneValidate } = this.state;
        const isEmptyKeySelected = setKeyPromotionSelected.size == 0;
        const isEmptyAllKey = allKeyPromotion.size == 0;
        this.expDataPromotion = {};
        this.expDataPromotionDelivery = {};
        if (isEmptyAllKey) {
            this.setState({
                setKeyPromotionSelected: new Set(),
                setGroupIDCondition: new Set(),
                setGroupIDPhoneValidate: new Set(),
                expandPromotion: [],
                expandPromotionDelivery: []
            });
        } else if (isEmptyKeySelected) {
            this.setState({
                setKeyPromotionSelected: defaultKeyPromotion,
                setGroupIDCondition: new Set(),
                setGroupIDPhoneValidate: new Set(),
                expandPromotion: [],
                expandPromotionDelivery: []
            });
        } else {
            for (const key of setKeyPromotionSelected) {
                if (!allKeyPromotion.has(key)) {
                    setKeyPromotionSelected.delete(key);
                }
            }
            for (const key of setGroupIDCondition) {
                if (!allGroupID.has(key)) {
                    setGroupIDCondition.delete(key);
                }
            }
            for (const key of setGroupIDPhoneValidate) {
                if (!allGroupID.has(key)) {
                    setGroupIDPhoneValidate.delete(key);
                }
            }
            this.setState({
                setKeyPromotionSelected,
                setGroupIDCondition,
                setGroupIDPhoneValidate,
                expandPromotion: [],
                expandPromotionDelivery: []
            });
        }
    };

    getPartnerInstallment = () => {
        const {
            actionDetail,
            userInfo: { storeID }
        } = this.props;
        actionDetail.getPartnerInstallment(storeID);
    };

    getProgramInstallment = (data) => () => {
        const { actionDetail } = this.props;
        actionDetail.getProgramInstallment(data);
    };

    getEmployee = (storeID) => {
        const { actionDetail } = this.props;
        actionDetail.getEmployeeAtStore(storeID);
    };

    getFiFoProduct = (fifoProduct) => {
        const { actionDetail } = this.props;
        const { productID } = fifoProduct;
        actionDetail.getFifoInfo(productID);
    };

    getConfigProduct = (productInfo) => {
        const { productIDRef } = productInfo;
        const { actionDetail } = this.props;
        actionDetail.getConfigInfo(productIDRef);
    };

    getFeatureProduct = (productInfo) => {
        const { productIDRef } = productInfo;
        const { actionDetail } = this.props;
        actionDetail.getFeatureInfo(productIDRef);
    };

    getListPromotionSelected = () => {
        const {
            setKeyPromotionSelected,
            expandPromotion,
            expandPromotionDelivery,
            phoneValidate,
            setGroupIDPhoneValidate
        } = this.state;
        const {
            promotion,
            salePromotion,
            // PROMOTION_DELIVERY
            promotionDelivery,
            salePromotionDelivery
        } = this.props;
        let expPromotion = [];
        expandPromotion.forEach((ele) => {
            const { data } = ele[1];
            expPromotion = [...expPromotion, ...data];
        });
        let expPromotionDelivery = [];
        expandPromotionDelivery.forEach((ele) => {
            const { data } = ele[1];
            expPromotionDelivery = [...expPromotionDelivery, ...data];
        });
        const allPromotion = [...promotion, ...expPromotion, ...promotionDelivery, ...expPromotionDelivery];
        const allSalePromotion = [...salePromotion, ...salePromotionDelivery];
        const listPromotion = [];
        let isValidate = true;
        let msgValidate = '';
        let isWarning = false;
        allPromotion.forEach((groupPromotion) => {
            const {
                promotionProducts,
                promotionGroupID,
                isRequired,
                isCheckCustomer,
                promotionGroupName,
                promotionID
            } = groupPromotion;
            const isNonEmpty = helper.isArray(promotionProducts);
            if (isNonEmpty) {
                const isValidatePhone = setGroupIDPhoneValidate.has(promotionGroupID);
                const isCheckWarning = !isCheckCustomer || isValidatePhone;
                const isCheckRequire = isRequired && isCheckWarning;
                groupPromotion.applyToCustomerPhone = isCheckCustomer ? phoneValidate : '';
                const productSelected = promotionProducts.filter((product, index) => {
                    const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
                    const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
                    return setKeyPromotionSelected.has(key);
                });
                if (productSelected.length > 0) {
                    listPromotion.push({
                        ...groupPromotion,
                        promotionProducts: productSelected
                    });
                } else {
                    const allExcludeID = helper.getExcludePromotionID(
                        allPromotion,
                        allSalePromotion,
                        setKeyPromotionSelected
                    );
                    const isExclude = allExcludeID.has(promotionID);
                    if (!isExclude) {
                        if (isCheckRequire) {
                            msgValidate += isValidate ? `\t${promotionGroupName}` : `\n\t${promotionGroupName}`;
                            isValidate = false;
                        }
                        if (isCheckWarning) {
                            isWarning = true;
                        }
                    }
                }
            }
        });
        allSalePromotion.forEach((subPromotion) => {
            const { promotionGroups } = subPromotion;
            promotionGroups.forEach((groupPromotion) => {
                const { promotionProducts, promotionGroupID } = groupPromotion;
                const productSelected = promotionProducts.filter((product, index) => {
                    const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
                    const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
                    return setKeyPromotionSelected.has(key);
                });
                if (productSelected.length > 0) {
                    listPromotion.push({
                        ...groupPromotion,
                        promotionProducts: productSelected
                    });
                }
            });
        });
        return { listPromotion, isValidate, msgValidate, isWarning };
    };

    checkValidatePromotion = (productOrder, delivery, storeRequests) => {
        const {
            quantity,
            outputStoreID,
            deliveryType,
            saleProgramInfo: { saleProgramID },
            packagesTypeId,
            phoneValidate
        } = this.state;
        const {
            cartRequest,
            searchInfo: { isImeiSim },
            statePromotion
        } = this.props;
        const { listPromotion, isValidate, msgValidate, isWarning } = this.getListPromotionSelected();
        const isRequirePackage = isImeiSim && packagesTypeId == 0;
        if (isRequirePackage) {
            Alert.alert('', translate('detail.please_select_sim_package'));
        } else if (statePromotion.isError) {
            Alert.alert('', translate('detail.warning_get_promotion'));
        } else if (!isValidate) {
            Alert.alert(translate('detail.please_select_promotion'), msgValidate);
        } else if (isWarning) {
            Alert.alert('', translate('detail.dismiss_promotion'), [
                {
                    text: translate('common.btn_skip'),
                    style: 'cancel'
                },
                {
                    text: translate('common.btn_continue'),
                    style: 'default',
                    onPress: () => {
                        this.addToShoppingCart({
                            mainProduct: {
                                productID: productOrder.productID,
                                imei: productOrder.imei,
                                inventoryStatusID: productOrder.inventoryStatusID,
                                pointLoyalty: productOrder.pointLoyalty,
                                outputTypeID: 3,
                                appliedQuantity: quantity,
                                outputStoreID: delivery.deliveryStoreID,
                                deliveryTypeID: delivery.deliveryTypeID,
                                saleProgramID,
                                packagesTypeId
                            },
                            storeRequests,
                            delivery,
                            promotionGroups: listPromotion,
                            cartRequest
                        });
                    }
                }
            ]);
        } else {
            this.addToShoppingCart({
                mainProduct: {
                    productID: productOrder.productID,
                    imei: productOrder.imei,
                    inventoryStatusID: productOrder.inventoryStatusID,
                    pointLoyalty: productOrder.pointLoyalty,
                    outputTypeID: 3,
                    appliedQuantity: quantity,
                    outputStoreID: delivery.deliveryStoreID,
                    deliveryTypeID: delivery.deliveryTypeID,
                    saleProgramID,
                    packagesTypeId
                },
                storeRequests,
                delivery,
                promotionGroups: listPromotion,
                cartRequest
            });
        }
    };

    addToShoppingCart = (data) => {
        const { navigation, actionPouch, actionCart } = this.props;
        showBlockUI();
        actionCart
            .addToShoppingCart(data)
            .then((dataCart) => {
                hideBlockUI();
                navigation.navigate('ShoppingCart');
                actionPouch.setDataCartApply(dataCart);
            })
            .catch((error) => {
                Alert.alert(translate('common.notification_uppercase'), error.msgError, [
                    {
                        text: translate('common.btn_skip'),
                        style: 'cancel',
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: 'default',
                        onPress: () => this.addToShoppingCart(data)
                    }
                ]);
            });
    };

    checkProductFavorite = (dataFavorite, productInfo) => {
        if (helper.isArray(dataFavorite)) {
            const indexProduct = dataFavorite.findIndex((ele) => ele.productID == productInfo.productID);
            return indexProduct > -1;
        } else {
            return false;
        }
    };

    moveToShoppingCart = () => {
        const { cartRequest, dataCartApply, navigation, actionCart } = this.props;
        const isCartEmpty = helper.IsEmptyObject(cartRequest);
        const isCartApplyEmpty = helper.IsEmptyObject(dataCartApply);
        if (isCartEmpty && isCartApplyEmpty) {
            Alert.alert('', translate('detail.no_product_in_cart'));
        } else {
            actionCart.setDataShoppingCart(dataCartApply);
            navigation.navigate('ShoppingCart');
        }
    };

    getLockProductInfo = (productOrder) => {
        const {
            actionDetail,
            userInfo: { storeID }
        } = this.props;
        actionDetail.getLockProductInfo({
            storeID,
            imei: productOrder.imei,
            productID: productOrder.productID,
            productIDRef: productOrder.productIDRef,
            inventoryStatusID: productOrder.inventoryStatusID
        });
    };

    moveToLockCart = (productOrder) => {
        this.getLockProductInfo(productOrder);
        this.setState({ isVisibleLockInfo: true });
    };

    getDataPackage = (data) => {
        const { actionDetail } = this.props;
        actionDetail.getSimPackage({ data });
    };

    updateSalePriceProduct = (infoProduct) => {
        const { actionDetail } = this.props;
        actionDetail.getSalePriceProduct(infoProduct).then(({ salePriceVAT }) => {
            this.setState({ retailPriceVAT: salePriceVAT });
        });
    };

    getPreOrderLockProductInfo = (productOrder) => {
        const {
            actionDetail,
            userInfo: { storeID }
        } = this.props;
        actionDetail.getPreOrderLockProductInfo({
            storeID,
            productID: productOrder.productID
        });
    };

    moveToLockPreOrderCart = (productOrder) => {
        this.getPreOrderLockProductInfo(productOrder);
        this.setState({ isVisiblePreOrder: true });
    };
}

const mapStateToProps = function (state) {
    return {
        searchInfo: state.saleReducer.productSearch,
        userInfo: state.userReducer,
        dataProduct: state.detailReducer.dataProduct,
        productSearch: state.detailReducer.productSearch,
        secondStock: state.detailReducer.secondStock,
        exhibitStock: state.detailReducer.exhibitStock,
        promotion: state.detailReducer.promotion,
        salePromotion: state.detailReducer.salePromotion,
        allKeyPromotion: state.detailReducer.allKeyPromotion,
        allGroupID: state.detailReducer.allGroupID,
        defaultKeyPromotion: state.detailReducer.defaultKeyPromotion,
        employee: state.detailReducer.employee,
        dataProvince: state.locationReducer.dataProvince,
        dataDistrict: state.locationReducer.dataDistrict,
        storeNearest: state.detailReducer.storeNearest,
        storeShipping: state.detailReducer.storeShipping,
        storeOther: state.detailReducer.storeOther,
        storeAtHome: state.detailReducer.storeAtHome,
        partnerInstallment: state.detailReducer.partnerInstallment,
        programInstallment: state.detailReducer.programInstallment,
        dataFifo: state.detailReducer.dataFifo,
        dataConfig: state.detailReducer.dataConfig,
        dataFeature: state.detailReducer.dataFeature,
        dataLock: state.detailReducer.dataLock,
        dataPackage: state.detailReducer.dataPackage,
        statePromotion: state.detailReducer.statePromotion,
        stateProduct: state.detailReducer.stateProduct,
        stateSecond: state.detailReducer.stateSecond,
        stateExhibit: state.detailReducer.stateExhibit,
        stateStoreNearest: state.detailReducer.stateStoreNearest,
        stateStoreShipping: state.detailReducer.stateStoreShipping,
        stateEmployee: state.detailReducer.stateEmployee,
        stateProvince: state.locationReducer.stateProvince,
        stateDistrict: state.locationReducer.stateDistrict,
        stateStoreOther: state.detailReducer.stateStoreOther,
        stateStoreAtHome: state.detailReducer.stateStoreAtHome,
        statePartnerInstallment: state.detailReducer.statePartnerInstallment,
        stateProgramInstallment: state.detailReducer.stateProgramInstallment,
        stateFifo: state.detailReducer.stateFifo,
        stateConfig: state.detailReducer.stateConfig,
        stateFeature: state.detailReducer.stateFeature,
        stateLock: state.detailReducer.stateLock,
        statePackage: state.detailReducer.statePackage,
        cartRequest: state.shoppingCartReducer.dataShoppingCart,
        dataFavorite: state.pouchFavorite.dataFavorite,
        dataCartApply: state.pouchCartApply.dataCartApply,
        promotionDelivery: state.detailReducer.promotionDelivery,
        salePromotionDelivery: state.detailReducer.salePromotionDelivery,
        dataPreOrderLock: state.detailReducer.dataPreOrderLock,
        statePreOrderLock: state.detailReducer.statePreOrderLock
    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        actionDetail: bindActionCreators(actionDetailCreator, dispatch),
        actionLocation: bindActionCreators(actionLocationCreator, dispatch),
        actionCart: bindActionCreators(actionCartCreator, dispatch),
        actionPouch: bindActionCreators(actionPouchCreator, dispatch)
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(ViewPromotion);

const getSaleProgramInfo = (cartRequest, storeID) => {
    const { SaleOrderDetails, ApplyPromotionToCustomerPhone, IsAutoCreateEP } = cartRequest;
    const isNonEmpty = helper.IsNonEmptyArray(SaleOrderDetails);
    let isHasSaleProgram = false;
    let saleProgramInfo = {
        logo: '',
        partnerID: undefined,
        saleProgramID: 0,
        saleProgramName: ''
    };
    let deliveryInfo = {
        gender: true,
        contactPhone: '',
        contactName: '',
        contactAddress: '',
        provinceID: 0,
        districtID: 0,
        wardID: 0,
        deliveryType: 1,
        receiveType: 1
    };
    let receiveInfo = {};
    let deliveryType = 1;
    let outputStoreID = storeID;
    const phoneValidate = ApplyPromotionToCustomerPhone || '';
    if (isNonEmpty) {
        const dataDetails = [...SaleOrderDetails].reverse();
        const isAtHome = dataDetails[0].DeliveryInfoRequest.DeliveryTypeID != 1;
        const infoSaleProgram = SaleOrderDetails.find((saleorder) => !!saleorder.SaleProgramInfo);
        const infoDelivery = dataDetails.find((saleorder) => {
            const {
                DeliveryInfoRequest: { DeliveryTypeID }
            } = saleorder;
            return DeliveryTypeID != 1;
        });
        if (infoSaleProgram) {
            const {
                SaleProgramInfo: { Logo, PartnerInstallmentID, SaleProgramID, SaleProgramName },
                DeliveryInfoRequest
            } = infoSaleProgram;
            saleProgramInfo = {
                logo: Logo,
                partnerID: PartnerInstallmentID,
                saleProgramID: SaleProgramID,
                saleProgramName: SaleProgramName
            };
            isHasSaleProgram = true;
            if (IsAutoCreateEP) {
                receiveInfo = {
                    deliveryInfo: DeliveryInfoRequest,
                    storeRequests: []
                };
                deliveryType = DeliveryInfoRequest.DeliveryTypeID;
                outputStoreID = DeliveryInfoRequest.DeliveryStoreID;
            }
        }
        if (infoDelivery) {
            const {
                DeliveryInfoRequest: {
                    ContactGender,
                    ContactName,
                    ContactPhone,
                    DeliveryAddress,
                    DeliveryProvinceID,
                    DeliveryDistrictID,
                    DeliveryWardID,
                    DeliveryTypeID
                }
            } = infoDelivery;
            deliveryInfo = {
                gender: ContactGender,
                contactPhone: ContactPhone,
                contactName: ContactName,
                contactAddress: DeliveryAddress,
                provinceID: DeliveryProvinceID,
                districtID: DeliveryDistrictID,
                wardID: DeliveryWardID,
                deliveryType: isAtHome ? DeliveryTypeID : 1,
                receiveType: isAtHome ? 3 : 1
            };
        }
    }
    return {
        saleProgramInfo,
        deliveryInfo,
        phoneValidate,
        isHasSaleProgram,
        receiveInfo,
        deliveryType,
        outputStoreID
    };
};

const getValueDisabledPager = (inventoryStatusID, productInfo, productSecondInfo, productExhibitInfo) => {
    switch (inventoryStatusID) {
        case 2:
            return helper.IsEmptyObject(productSecondInfo);
        case 3:
            return helper.IsEmptyObject(productExhibitInfo);
        default:
            return helper.IsEmptyObject(productInfo);
    }
};

const ProductInfo = ({ info }) => {
    const { productName } = info;
    return (
        <View
            style={{
                width: constants.width,
                justifyContent: 'center',
                alignItems: 'center',
                paddingHorizontal: 10,
                paddingVertical: 10,
                backgroundColor: COLORS.bgF0F0F0
            }}>
            <MyText
                style={{
                    color: COLORS.txt147EFB,
                    fontWeight: 'bold'
                }}
                numberOfLines={1}
                text={productName || ''}
            />
        </View>
    );
};
