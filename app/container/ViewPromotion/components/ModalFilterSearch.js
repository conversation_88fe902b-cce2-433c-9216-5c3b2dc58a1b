import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';
import {
    View,
    SafeAreaView,
    TouchableOpacity,
    StyleSheet,
    Text,
    Platform
} from 'react-native';
import moment from 'moment';
import KModal from 'react-native-modal';
// import DatePicker from 'react-native-datepicker';
import DateTimePicker from '@react-native-community/datetimepicker';
import { MyText, Icon, Button , DatePicker} from '@components';
import { constants } from '@constants';
import { translate } from '@translate';
import { COLORS } from '@styles';
import { dateHelper } from '@common';

import ModalInstallment from '../../Detail/component/Modal/ModalInstallment';

const ModalFilterSearch = ({
    oldFilter,
    hideModal,
    isVisible,
    searchFilterResponse,
    getPartnerInstallment,
    getProgramInstallment,
    outputStoreID,
    saleProgramInfo,
    mainProduct
}) => {
    const [filter, setFilter] = React.useState({
        promotionDate: oldFilter.promotionDate,
        promotionTime: oldFilter.promotionDate,
        saleProgramID: oldFilter.saleProgramID
    });
    const [showDate, setShowDate] = React.useState(false);
    const [showTime, setShowTime] = React.useState(false);
    const [state, setState] = React.useState({
        isVisibleInstallment: false,
        saleProgramInfo
    });
    const {
        partnerInstallment,
        statePartnerInstallment,
        programInstallment,
        stateProgramInstallment
    } = useSelector((state) => state.detailReducer);
    const formatStringDate = (dateStr) => {
        const splittedDate = dateStr.split(' ');
        const [day, month, year] = splittedDate[0].split('/');
        return `${year}-${month}-${day}T${splittedDate[1]}:00`;
    };
    const formatDateTime = (date, time) => {
        let dateStr = date.split('T')[0];
        let timeStr = time.split('T')[1];
        return dateStr + 'T' + timeStr;
    }
    const formatDate = (date) => {
        let dateStr = dateHelper.formatDateDDMMYYYY(date);
        let timeStr = dateHelper.formatTimeHHMM(date);
        return formatStringDate(dateStr + ' ' + timeStr);
    }
    const BtnSearch = () => {
        return (
            <View
                style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginTop: 10
                }}>
                <TouchableOpacity
                    onPress={() => {
                        const { promotionDate, promotionTime, saleProgramID } =
                            filter;
                        const date =
                            Platform.OS === 'ios'
                                ? promotionDate
                                : formatDateTime(filter.promotionDate, filter.promotionTime);
                        searchFilterResponse({
                            promotionDate: date,
                            saleProgramID
                        });
                    }}
                    style={{
                        backgroundColor: COLORS.btn2C8BD7,
                        padding: 10,
                        borderRadius: 5
                    }}>
                    <MyText
                        text={translate(
                            'instalmentManager.btn_search_uppercase'
                        )}
                        style={{ color: COLORS.txtFFFFFF }}
                    />
                </TouchableOpacity>
            </View>
        );
    };

    const AndroidDateTimePicker = () => {
        return (
            <>
                <TouchableOpacity
                    style={{
                        borderWidth: 1,
                        borderRadius: 4,
                        borderColor: COLORS.bdCCCCCC,
                        paddingLeft: 10,
                        marginBottom: 0,
                        backgroundColor: COLORS.bgFFFFFF,
                        opacity: 1,
                        paddingVertical: 8,
                        alignItems: 'center',
                        flexDirection: 'row'
                    }}
                    onPress={() => setShowDate(true)}>
                    <Text style={{ color: COLORS.bg000000, marginRight: 5 }}>
                        {convertDateTime()}
                    </Text>
                    <Icon
                        iconSet="Feather"
                        name="calendar"
                        style={{
                            fontSize: 26,
                            color: COLORS.ic2C8BD7,
                            marginRight: 3
                        }}
                    />
                </TouchableOpacity>
                {showDate && (
                    <DateTimePicker
                        mode="date"
                        value={dateHelper.convert_string_to_date(filter.promotionDate)}
                        onChange={onChangeDate}
                    />
                )}
                {showTime && (
                    <DateTimePicker
                        mode="time"
                        value={dateHelper.convert_string_to_date(filter.promotionTime)}
                        onChange={onChangeTime}
                    />
                )}
            </>
        );
    };

    const onChangeDate = (event, selectedDate) => {
        setShowDate(false);
        if (dateHelper.isValidTime(selectedDate)) {
            setShowTime(true);
            setFilter({
                ...filter,
                promotionDate: formatDate(selectedDate)
            });
        }
    };

    const onChangeTime = (event, selectedDate) => {
        setShowTime(false);
        if (dateHelper.isValidTime(selectedDate)) {
            setFilter((prevFilter) => ({
                ...prevFilter,
                promotionTime: formatDate(selectedDate)
            }));
        }
    };
    const convertDateTime = () => {
        const { promotionDate, promotionTime } = filter;
        console.log('promotion', promotionDate);
        const [year, month, day] = promotionDate.split('T')[0].split('-');
        const [hour, min] = promotionTime.split('T')[1].split(':');
        return `${day}/${month}/${year} ${hour}:${min}`


    };
    useEffect(() => {
        if (state.saleProgramInfo.partnerID > 0) {
            getProgramInstallment({
                "storeID": outputStoreID,
                "partnerInstallmentID": state.saleProgramInfo.partnerID,
                "productID": mainProduct.productID,
                "salePriceVAT": mainProduct.salePriceVAT,
                "inventoryStatusID": mainProduct.inventoryStatusID,
                "promotionListGroupID": mainProduct.promotionGroupID
            })();
        }
    }, [state.saleProgramInfo.partnerID])
    return (
        <KModal
            onRequestClose={() => { }}
            isVisible={isVisible}
            style={{
                margin: 0,
                justifyContent: 'space-between',
                flexDirection: 'row'
            }}
            deviceWidth={constants.width / 2}
            deviceHeight={constants.height}
            backdropTransitionOutTiming={0}
            animationIn="slideInRight"
            animationOut="slideOutRight"
            useNativeDriver
            hideModalContentWhileAnimating>
            <TouchableOpacity
                style={{ width: '15%', alignItems: 'flex-start' }}
                activeOpacity={1}
                onPress={() => {
                    hideModal();
                    setFilter({
                        promotionDate: oldFilter.promotionDate,
                        promotionTime: oldFilter.promotionDate,
                        saleProgramID: oldFilter.saleProgramID
                    });
                }}
            />
            <View
                style={{
                    backgroundColor: COLORS.bgFFFFFF,
                    width: '85%',
                    height: '100%'
                }}>
                <SafeAreaView
                    style={{ marginTop: Platform.OS === 'ios' ? 50 : 20 }}>
                    <View
                        style={{
                            width: '90%',
                            flexDirection: 'row',
                            justifyContent: 'center',
                            paddingBottom: 10,
                            alignItems: 'center'
                        }}>
                        <MyText
                            text={translate(
                                'modalFilter.select_promotion_date'
                            )}
                            style={{
                                marginLeft: 10,
                                flex: 1
                            }}
                        />
                        {Platform.OS === 'ios' ? (
                            <DatePicker
                                date={dateHelper.convert_string_to_date(filter.promotionDate)}
                                format="DD/MM/YYYY HH:mm"
                                onDateChange={(txtDate) => {
                                    setFilter({
                                        ...filter,
                                        promotionDate: formatStringDate(txtDate)
                                    });
                                }}
                            />
                        ) : (
                            <AndroidDateTimePicker />
                        )}
                    </View>
                    <View
                        style={{
                            width: '90%',
                            flexDirection: 'row',
                            justifyContent: 'center',
                            paddingBottom: 10,
                            alignItems: 'center'
                        }}>
                        <MyText
                            text={translate(
                                'modalFilter.select_sale_program_id'
                            )}
                            style={{
                                marginVertical: 10,
                                marginLeft: 10,
                                flex: 1
                            }}
                        />
                        <MyText
                            text={filter.saleProgramID}
                            style={{ marginRight: 10, fontWeight: 'bold' }}
                        />
                        <Button
                            iconRight={{
                                iconSet: 'MaterialCommunityIcons',
                                name: 'pencil-box',
                                size: 30,
                                color: COLORS.ic2C8BD7
                            }}
                            styleContainer={{
                                height: 40,
                                justifyContent: 'flex-end',
                                alignItems: 'center',
                                backgroundColor: COLORS.txtFFFFFF,
                                flexDirection: 'row'
                            }}
                            styleText={{
                                color: COLORS.ic2C8BD7
                            }}
                            onPress={() =>
                                setState({
                                    ...state,
                                    isVisibleInstallment: true
                                })
                            }
                        />
                        {filter.saleProgramID > 0 && (
                            <Button
                                iconRight={{
                                    iconSet: 'MaterialIcons',
                                    name: 'clear',
                                    size: 30,
                                    color: COLORS.icFF0000
                                }}
                                styleContainer={{
                                    height: 40,
                                    justifyContent: 'flex-end',
                                    alignItems: 'center',
                                    backgroundColor: COLORS.txtFFFFFF,
                                    flexDirection: 'row'
                                }}
                                styleText={{
                                    color: COLORS.icFF0000
                                }}
                                onPress={() =>
                                    setFilter({ ...filter, saleProgramID: 0 })
                                }
                            />
                        )}
                    </View>

                    <BtnSearch />
                    <ModalInstallment
                        isVisible={state.isVisibleInstallment}
                        hideModal={() => {
                            setState({ ...state, isVisibleInstallment: false });
                        }}
                        dataInstallment={partnerInstallment}
                        statePartner={statePartnerInstallment}
                        onPartnerTryAgains={getPartnerInstallment}
                        dataInterestRate={programInstallment}
                        stateProgram={stateProgramInstallment}
                        onProgramTryAgains={getProgramInstallment({
                            storeID: outputStoreID,
                            partnerInstallmentID:
                                state.saleProgramInfo.partnerID,
                            productID: mainProduct.productID,
                            salePriceVAT: mainProduct.salePriceVAT,
                            inventoryStatusID: mainProduct.inventoryStatusID,
                            promotionListGroupID: mainProduct.promotionGroupID
                        })}
                        onChangePartner={(partnerID, logo) => {
                            if (partnerID > 0) {
                                setState({
                                    ...state,
                                    saleProgramInfo: {
                                        ...saleProgramInfo,
                                        partnerID
                                    }
                                });
                            }
                        }}
                        onChangeProgram={(programInfo, logo) => {
                            setFilter({
                                ...filter,
                                saleProgramID: programInfo.saleProgramID
                            });
                            setState({
                                ...state,
                                isVisibleInstallment: false,
                                saleProgramInfo: {
                                    ...saleProgramInfo,
                                    saleProgramID: programInfo.saleProgramID,
                                    saleProgramName:
                                        programInfo.saleProgramName,
                                    logo
                                }
                            });
                        }}
                    />
                </SafeAreaView>
            </View>
        </KModal>
    );
};
export default ModalFilterSearch;
