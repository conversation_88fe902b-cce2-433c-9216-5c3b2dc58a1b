import React, { Component } from 'react';
import {
    View,
    SafeAreaView,
    StyleSheet,
    FlatList,
    TouchableOpacity,
    Alert
} from 'react-native';
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import { DotIndicator } from "react-native-indicators";
import { translate } from "@translate";
import { constants } from '@constants';
import { helper } from '@common';
import { BaseLoading, MyText, Icon, showBlockUI, hideBlockUI } from "@components";
import { COLORS } from "@styles";
import * as actionNotifyCreator from "./action";
import ItemContent from "./component/ItemContent";

class Notification extends Component {
    constructor(props) {
        super(props);
        this.state = {
        };
        this.refsArray = [];
    }

    componentDidMount() {
        this.getDataNotify();
    }

    getDataNotify = () => {
        const { actionNotify, userInfo } = this.props;
        actionNotify.getDataNotification(userInfo.userName);
    }

    loadMoreNotify = () => {
        const {
            actionNotify,
            userInfo,
            dataNotify,
            isLoadMore
        } = this.props;
        if (isLoadMore) {
            const { id } = dataNotify[dataNotify.length - 1]
            actionNotify.getMoreDataNotify({
                "userName": userInfo.userName,
                "lastId": id
            });
        }
    }

    render() {
        const { stateNotify, dataNotify, stateLoadMore, actionNotify } = this.props;
        return (
            <SafeAreaView style={{ flex: 1, backgroundColor: "#fafafa" }}>
                <BaseLoading
                    isLoading={stateNotify.isFetching}
                    isError={stateNotify.isError}
                    isEmpty={stateNotify.isEmpty}
                    textLoadingError={stateNotify.description}
                    onPressTryAgains={this.getDataNotify}
                    content={
                        <FlatList
                            data={dataNotify}
                            renderItem={({ item, index }) => (<ItemContent
                                refs={this.refsArray}
                                index={index}
                                info={item}
                                color={`${index % 10}`}
                                onSeen={() => {
                                    showBlockUI();
                                    actionNotify.seenNotification(item.id).then((notify) => {
                                        console.log("seen notification success", notify);
                                        hideBlockUI();
                                    }).catch(error => {
                                        hideBlockUI();
                                        // console.log("seen notification error", error);
                                        // Alert.alert(
                                        //     "",
                                        //     error,
                                        //     [
                                        //         {
                                        //             text: "OK",
                                        //             style: 'cancel',
                                        //             onPress: () => { }
                                        //         }
                                        //     ]
                                        // );
                                    })
                                }}
                                onRemove={() => {
                                    showBlockUI();
                                    actionNotify.removeNotification(item.id).then(() => {
                                        hideBlockUI();
                                    }).catch(error => {
                                        console.log("remove notification error", error);
                                        hideBlockUI();
                                    })
                                }}
                            />)}
                            keyExtractor={(item, index) => `${index}`}
                            ItemSeparatorComponent={Separator}
                            onEndReachedThreshold={0.5}
                            ListFooterComponent={<LoadIndicator
                                state={stateLoadMore}
                                onRetry={this.loadMoreNotify}
                            />}
                            onEndReached={this.loadMoreNotify}
                        />}
                />
            </SafeAreaView>
        )
    }
}

const mapStateToProps = (state) => ({
    userInfo: state.userReducer,
    dataNotify: state.notifyReducer.dataNotify,
    stateNotify: state.notifyReducer.stateNotify,
    isLoadMore: state.notifyReducer.isLoadMore,
    stateLoadMore: state.notifyReducer.stateLoadMore,
});

const mapDispatchToProps = (dispatch) => ({
    actionNotify: bindActionCreators(actionNotifyCreator, dispatch),
});

export default connect(mapStateToProps, mapDispatchToProps)(Notification);

const Separator = () => (
    <View style={{
        width: constants.width,
        height: StyleSheet.hairlineWidth,
        backgroundColor: COLORS.bg8E8E93
    }}
    />
)

const LoadIndicator = ({ state, onRetry }) => {
    const { isFetching, isError } = state;
    if (isError) {
        return (<TouchableOpacity style={{
            paddingVertical: 10,
            width: constants.width,
            alignSelf: "center",
            justifyContent: "center",
            alignItems: "center",
            flexDirection: "row",
            backgroundColor: COLORS.bgFFFFFF
        }}
            activeOpacity={0.8}
            onPress={onRetry}
        >
            <Icon
                iconSet={"Ionicons"}
                name={"reload-outline"}
                size={16}
                color={COLORS.txt147EFB}
            />
            <MyText
                text={"Tải lại"}
                style={{ color: COLORS.txt147EFB, marginLeft: 4 }}
            />
        </TouchableOpacity>);
    }
    return (<View style={{
        alignSelf: "center",
        justifyContent: "center",
        alignItems: "center",
        width: constants.width,
        backgroundColor: COLORS.bgFAFAFA,
        height: 18
    }}>
        {
            isFetching &&
            <DotIndicator
                size={5}
                color={COLORS.ic147EFB}
                count={5}
            />
        }
    </View>);
}