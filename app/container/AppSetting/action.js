
import { helper } from '@common';
import { apiBase, METHOD } from '@config';
import { API_CONST } from '@constants';
import { appSettingState } from './state';

const { API_GET_APP_SETTING } = API_CONST;

const UPDATE_APP_SETTING = "UPDATE_APP_SETTING";
export const appSettingAction = {
    UPDATE_APP_SETTING
}

export const getAppSetingConfig = () => {
    return function (dispatch, getState) {
        const body = {
            "cluster": "ERP",
            "map": "SYSTEM_APPLICATIONCONFIG_CACHE",
            "key": "MASTERDATA.SYSTEM_APPLICATIONCONFIG"
        };
        global.config = appSettingState;
        apiBase(API_GET_APP_SETTING, METHOD.POST, body).then(response => {
            console.log("getAppSetingConfig success", response);
            const { object } = response;
            if (helper.IsNonEmptyArray(object)) {
                const config = getKeyValueConfig(object);
                global.config = config;
                dispatch(update_app_setting(config));
            }
        }).catch(error => {
            console.log("getAppSetingConfig error", error);
        })
    }
}

const getKeyValueConfig = (data) => {
    const defaultValue = { ...appSettingState };
    data.forEach(ele => {
        const { CONFIGID, CONFIGVALUE } = ele;
        if (helper.hasProperty(defaultValue, CONFIGID)) {
            defaultValue[CONFIGID] = CONFIGVALUE;
        }
    })
    console.log(defaultValue);
    return defaultValue;
}

const update_app_setting = (config) => {
    return {
        type: UPDATE_APP_SETTING,
        config
    };
}
