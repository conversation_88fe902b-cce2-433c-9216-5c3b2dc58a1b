import React, { useState } from 'react';
import {
    View,
    TouchableOpacity,
    TextInput,
    StyleSheet,
    Alert,
    ScrollView
} from 'react-native';
import { MyText, BaseContainer , DatePicker} from '@components';
// import DatePicker from 'react-native-datepicker';
import moment from 'moment';
import AsyncStorage from '@react-native-community/async-storage';
import { translate } from '@translate';
import { COLORS } from "@styles";

const SearchHistory = (props) => {
    const {
        getHistory,
        historyState,
        navigation,
        intINVENTORYORDERTYPEID,
        INVENTORYORDERTYPEID,
        getTerms
    } = props;
    const { isFetching, isError, isEmty, errorDiscription } = historyState;
    const [fromDate, setFromDate] = useState(
        moment(new Date()).format('DD-MM-YYYY')
    );

    let dateFromPlace = new Date();
    dateFromPlace.setFullYear(new Date().getFullYear() - 2);
    dateFromPlace.setDate(new Date().getDate());
    dateFromPlace.setMonth(new Date().getMonth());

    const [toDate, setToDate] = useState(
        moment(new Date()).format('DD-MM-YYYY')
    );

    const getEndDate = () => {
        return moment(fromDate, 'DD-MM-YYYY').add(1, 'M').format('DD-MM-YYYY');
    };

    const setFromDateCus = (time) => {
        setFromDate(moment(time, 'DD-MM-YYYY').format('DD-MM-YYYY'));
        setToDate(moment(time, 'DD-MM-YYYY').add(1, 'M').format('DD-MM-YYYY'));
    };

    const setToDateCus = (time) => {
        setToDate(moment(time, 'DD-MM-YYYY').format('DD-MM-YYYY'));
    };

    const [show, setShow] = useState(true);
    const _onPress = () => {
        getHistory(
            {
                intINVENTORYORDERTYPEID: intINVENTORYORDERTYPEID,
                fromDate: fromDate.split('-').reverse().join('-'),
                toDate: toDate.split('-').reverse().join('-'),
                intFinish: 1
            },
            true
        )
            .then((res) => {
                // setResult(res);
                props.setResults(res);
            })
            .catch((err) => {
                Alert.alert(translate('common.notification'), String(err));
            });
    };

    const ItemResult = ({ item, index }) => {
        // console.log(props)
        const RenderDate = () => {
            const fromDate = new Date(item.begintime);
            const endDate = new Date(item.endtime);
            // console.log(fromDate);
            return (
                <View
                    style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between'
                    }}>
                    <MyText
                        text={
                            translate('inventory_share.get_start') +
                            ': ' +
                            moment(fromDate).format('HH:mm DD/MM/YYYY')
                        }
                        style={styles.date}
                        addSize={-2}
                    />
                    <MyText
                        text={
                            translate('inventory_share.finished') +
                            ': ' +
                            moment(endDate).format('HH:mm DD/MM/YYYY')
                        }
                        style={styles.date}
                        addSize={-2}
                    />
                </View>
            );
        };
        const navigateInventoryProduct = () => {
            let params = {
                intINVENTORYORDERTYPEID: intINVENTORYORDERTYPEID,
                inventorytermid: item.inventorytermid,
                inventoryorderid: item.inventoryorderid,
                hadarea: item.hadarea,
                finishstatus: item.finishstatus,
                begintime: item.begintime,
                islock: item.islock,
                INVENTORYORDERTYPEID: INVENTORYORDERTYPEID
            };
            AsyncStorage.setItem(
                'productWithoutImei',
                JSON.stringify({
                    inventorytermid: item.inventorytermid,
                    inventoryorderid: item.inventoryorderid,
                    statusterm: item.finishstatus,
                    begintime: item.begintime,
                    islock: item.islock
                })
            );
            navigation.navigate('InventoryProduct', {
                inventoryPeriod: params,
                getDataPeriod: getTerms
            });
        };
        return (
            <TouchableOpacity
                style={{ margin: 10, marginLeft: 20 }}
                onPress={() => {
                    navigateInventoryProduct();
                }}>
                <View
                    style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignItems: 'flex-start'
                    }}>
                    <MyText
                        text={item.inventorytermid + ' - ' + item.inventorytermname}
                        style={styles.title}
                    />
                </View>
                <RenderDate />
            </TouchableOpacity>
        );
    };
    return (
        <View style={{ flex: 1 }}>
            <TouchableOpacity
                onPress={() => setShow(!show)}
                style={{ marginTop: 20, marginLeft: 20 }}>
                <MyText
                    text={'+ ' + translate('inventory_share.history')}
                    style={{ fontWeight: '800' }}
                    addSize={2}
                />
            </TouchableOpacity>
            {show && (
                <View style={{ marginTop: 10, flex: 1 }}>
                    <View style={styles.containerFilterDate}>
                        <View style={styles.pickDateContainer}>
                            <TextInput
                                value={fromDate}
                                placeholder={fromDate}
                                style={{
                                    paddingLeft: 10,
                                    width: '85%',
                                    height: 40
                                }}
                                editable={false}
                            />
                            <DatePicker
                                date={fromDate}
                                onDateChange={(date)=> setFromDateCus(date)}
                                format={'DD-MM-YYYY'}
                                maxDate={new Date()}
                            />
                        </View>
                        <View style={styles.pickDateContainer}>
                            <TextInput
                                value={toDate}
                                placeholder={toDate}
                                style={{
                                    paddingLeft: 10,
                                    width: '85%',
                                    height: 40
                                }}
                                editable={false}
                            />

                            <DatePicker
                                date={toDate}
                                onDateChange={(date) =>setToDateCus(date)}
                                format={'DD-MM-YYYY'}
                                minDate={fromDate}
                                maxDate={getEndDate()}
                            />
                        </View>
                        <TouchableOpacity
                            style={styles.searchButton}
                            onPress={_onPress}>
                            <MyText
                                text={translate('inventory_share.search')}
                                style={{ color: COLORS.txtFFFFFF }}
                            />
                        </TouchableOpacity>
                    </View>

                    <View style={{ marginTop: 20, flex: 1 }}>
                        <BaseContainer
                            isLoading={isFetching}
                            isError={isError}
                            textLoadingError={
                                isEmty
                                    ? translate(
                                        'inventory_share.not_found_inven_history'
                                    )
                                    : errorDiscription
                            }
                            isEmpty={isEmty}
                            content={
                                <View style={{ flex: 1 }}>
                                    <ScrollView
                                        showsVerticalScrollIndicator={false}>
                                        {props.Results.map((item, index) => {
                                            return (
                                                <ItemResult
                                                    item={item}
                                                    index={index}
                                                    key={index}
                                                    intINVENTORYORDERTYPEID={
                                                        intINVENTORYORDERTYPEID
                                                    }
                                                />
                                            );
                                        })}
                                    </ScrollView>
                                </View>
                            }
                        />
                    </View>
                </View>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    pickDateContainer: {
        width: '40%',
        height: 36,
        borderRadius: 5,
        flexDirection: 'row',
        alignItems: 'center',
        borderColor: COLORS.bd46A0E0,
        borderWidth: 1
    },
    searchButton: {
        width: 70,
        height: 36,
        backgroundColor: COLORS.btn46A0E0,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 5,
        paddingHorizontal: 5
    },
    containerFilterDate: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 10
    },
    inputDate: {
        width: '80%',
        height: '90%',
        paddingLeft: 10
    },
    titleItem: {
        fontSize: 16,
        fontWeight: '900'
    },
    time: {
        fontWeight: '900',
        fontSize: 12
    },
    date: {
        fontWeight: '800',
        color: COLORS.txt333333,
        marginTop: 3
    }
});
export default SearchHistory;
