import { useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import { COLORS } from '@styles';
import { MyText, BottomSheet, Icon } from '@components';
import { TouchableWithoutFeedback } from 'react-native-gesture-handler';

const BottomSheetHandle = ({ title, onClose }) => (
    <View style={styles.handle}>
        <View style={{ flex: 1 }} />
        <View style={styles.handleTitleContainer}>
            <MyText
                addSize={1}
                style={styles.handleTitleText}
                text={title}
            />
        </View>
        <View style={{ flex: 1 }}>
            <TouchableWithoutFeedback
                style={styles.closeIconContainer}
                onPress={onClose}
            >
                <Icon
                    iconSet="MaterialIcons"
                    name="clear"
                    color={COLORS.txt000000}
                    size={22}
                />
            </TouchableWithoutFeedback>
        </View>
    </View>
);

const SearchUserSheet = ({
    bottomSheetRef,
    onChangeStatusSheet,
    handleClose,
    children,
    title = "<PERSON> kiếm user bán hàng",
    staffInfo: { name }
}) => {
    const snapPoints = useMemo(() => ['90%'], []);

    return (
        <BottomSheet
            bs={bottomSheetRef}
            handleComponent={() => (
                <BottomSheetHandle
                    title={name}
                    onClose={handleClose || (() => bottomSheetRef.current?.dismiss())}
                />
            )}
            snapPoints={snapPoints}
            onChangeStatusSheet={onChangeStatusSheet}
            enableHandlePanningGesture={false}
            enableContentPanningGesture={false}
            disabledBackdrop
        >
            {children}
        </BottomSheet>
    );
};

// Styles
const styles = StyleSheet.create({
    handle: {
        backgroundColor: COLORS.bgFFFFFF,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
        height: 50,
        elevation: 2,
        borderTopStartRadius: 22,
        borderTopEndRadius: 22,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: COLORS.bgC4C4C4,
    },
    handleTitleContainer: {
        flex: 6,
        justifyContent: 'center',
        alignItems: 'center',
    },
    handleTitleText: {
        fontWeight: 'bold',
        color: COLORS.txt000000,
    },
    closeIconContainer: {
        marginLeft: 10,
    },
});

export default SearchUserSheet;
