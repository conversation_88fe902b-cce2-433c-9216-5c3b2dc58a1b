import React, { Component } from 'react';
import {
    SafeAreaView,
    StyleSheet,
    ScrollView,
    Alert,
    Keyboard,
    Linking
} from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import {
    show<PERSON><PERSON>UI,
    hideBlockUI,
} from "@components";
import { helper, storageHelper } from "@common";
import Guide from "./component/Guide";
import Customer from "./component/Customer";
import OtpCode from "./component/OtpCode";
import * as actionShoppingCartCreator from "../../../ShoppingCart/action";
import * as actionSaleOrderCreator from "../../../SaleOrderCart/action";
import * as actionEditSaleOrderCreator from "../../action";
import * as actionManagerSOCreator from "../../../SaleOrderManager/action";
import { translate } from '@translate';
import { SO_TYPE } from '../../../../constants/constants';
import { COLORS } from "@styles";

class InstallmentOTP extends Component {

    constructor() {
        super();
        this.state = {
            info: {
                customerPhone: '',
                customerName: '',
            },
            expireTime: 0,
            otpCode: "",
            isVisible: false,
        }
        this.intervalId = null;
        this.onlySms = false;
        this.isCall = false;
    }

    componentDidMount() {
        const { dataCartInstallment, userInfo: { storeID, provinceID } } = this.props;
        const memberInfo = getMemberInfo(dataCartInstallment);
        this.setState({ info: memberInfo });
    }

    componentWillUnmount() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }
    }

    render() {
        const {
            info,
            expireTime,
            otpCode,
        } = this.state;
        return (
            <KeyboardAwareScrollView
                style={{
                    flex: 1,
                    backgroundColor: COLORS.bgFFFFFF,
                }}
                enableResetScrollToCoords={false}
                keyboardShouldPersistTaps="always"
                bounces={false}
                overScrollMode="always"
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                extraScrollHeight={60}
            >

                {
                    helper.validateOtpSend("INSTALLMENT") ? <SafeAreaView style={{ paddingBottom: 10, marginTop: 10 }}>
                        <OTPBackup
                            onPress={() => this.onCheckLoyaltyPoint(this.props.dataCartInstallment)}
                            label={"Khách hàng đồng ý ủy quyền cho TGDĐ thu thập và chuyển giao thông tin cho công ty tài chính để làm hồ sơ trả góp."}
                        >
                            <Customer
                                info={info}
                            />
                        </OTPBackup>
                    </SafeAreaView> : <SafeAreaView style={{
                        flex: 1,
                    }}>
                        <ScrollView style={{
                            flex: 1
                        }}
                            keyboardShouldPersistTaps={"always"}
                        >
                            <Guide />
                            <Customer
                                info={info}
                            />
                            <OtpCode
                                onCreate={this.onCreateOTP}
                                expireTime={expireTime}
                                code={otpCode}
                                onChange={(text) => {
                                    const regExpOTP = new RegExp(/^\d{0,4}$/);
                                    const isValidate = regExpOTP.test(text);
                                    if (isValidate) {
                                        this.setState({ otpCode: text });
                                    }
                                }}
                                onVerify={this.onCheckOTP}
                                onlySms={this.onlySms}
                            />
                        </ScrollView>
                    </SafeAreaView>
                }
            </KeyboardAwareScrollView>
        );
    }

    countDown = () => {
        const { expireTime } = this.state;
        const second = expireTime - 1;
        if (second > 0) {
            this.setState({ expireTime: second });
        }
        else {
            this.resetCountDown();
        }
    }

    resetCountDown = () => {
        if (!this.isCall) {
            this.onlySms = true;
        }
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }
        this.setState({
            expireTime: 0,
            otpCode: "",
        });
    }

    setCountDown = () => {
        this.setState({
            expireTime: 60
        })
        this.intervalId = setInterval(
            this.countDown,
            1000
        );
    }

    onCreateOTP = (type) => {
        const { info: {
            customerPhone
        } } = this.state;
        const { userInfo: { brandID } } = this.props;
        this.isCall = type == 'CALLCENTER';
        showBlockUI();
        actionShoppingCartCreator.createOTP({
            "type": type,
            "phoneNumber": customerPhone,
            "typeContent": "INSTALLMENT",
            "lenOtp": 4,
            "brandID": brandID,
            "onlySms": this.onlySms
        }).then(isSMS => {
            if (!this.isCall) {
                this.onlySms = isSMS;
            }
            hideBlockUI();
            this.setCountDown();
        }).catch(msgError => {
            this.resetCountDown();
            Alert.alert(translate('common.notification_uppercase'), msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: () => this.onCreateOTP(type)
                    }
                ]
            )
        });
    }

    onCheckOTP = () => {
        Keyboard.dismiss();
        const {
            otpCode,
            info: {
                customerPhone
            }
        } = this.state;
        const isValidate = this.validateOTP(otpCode);
        if (isValidate) {
            this.verifyOTP(otpCode, customerPhone);
        }
    }

    verifyOTP = (otpCode, customerPhone) => {
        const { dataCartInstallment } = this.props;
        showBlockUI();
        actionShoppingCartCreator.verifyOTP(otpCode, customerPhone)
            .then(data => {
                this.onCheckLoyaltyPoint(dataCartInstallment);
            }).catch(msgError => {
                Alert.alert(translate('common.notification_uppercase'), msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: "cancel",
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: "default",
                            onPress: () => this.verifyOTP(otpCode, customerPhone)
                        }
                    ]
                )
            })
    }

    validateOTP = (code) => {
        const regExpOTP = new RegExp(/^\d{4}$/);
        const isValidate = regExpOTP.test(code);
        if (helper.IsEmptyString(code)) {
            Alert.alert("", translate('editSaleOrder.OTP_alert'));
            return false;
        }
        if (!isValidate) {
            Alert.alert("", translate('editSaleOrder.OTP_full_alert'));
            return false;
        }
        return true;
    }

    addToSaleOrderCart = (data) => {
        showBlockUI();
        this.props.actionEditSaleOrder.createSaleOrderCart(data).then(orderInfo => {
            const { actionManagerSO, paramFilter } = this.props;
            actionManagerSO.getDataSearchSO(paramFilter);
            storageHelper.updateTopCustomerInfo(data.customerInfo);
            this.showAlertContent(orderInfo);
        }).catch(error => {
            const { errorType, msgError } = error
            const newData = { ...data }
            newData.cartRequest.NotifyPre = msgError
            if (errorType == SO_TYPE.LOCK_PRE) {
                Alert.alert(translate('common.notification_uppercase'), msgError, [
                    {
                        text: translate('common.btn_skip'),
                        onPress: hideBlockUI,
                    },
                    {
                        text: translate('common.btn_continue'),
                        onPress: () => this.addToSaleOrderCart(newData),
                    },
                ]);
            }
            else {
                Alert.alert(translate('common.notification_uppercase'), msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: "cancel",
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: "default",
                            onPress: () => this.addToSaleOrderCart(data)
                        }
                    ]
                )
            }
        });
    }

    showAlertContent = (orderInfo) => {
        let content = translate('editSaleOrder.create_order_successfully');
        const { SaleOrders } = orderInfo;
        const { SaleOrderID, EPOSTransactionID, IsCheckBHRV, MesBHRV } = SaleOrders[0];
        if (helper.IsNonEmptyString(SaleOrderID)) {
            content += `\n${translate('editSaleOrder.SO_code')} ${SaleOrderID}`;
        }
        if (helper.IsNonEmptyString(EPOSTransactionID)) {
            content += `\n${translate('editSaleOrder.EP_code')} ${EPOSTransactionID}`;
        }
        if (IsCheckBHRV) {
            content += `\n${MesBHRV}`;
        }
        Alert.alert("", content,
            [
                {
                    text: "OK",
                    style: "default",
                    onPress: () => {
                        hideBlockUI();
                        this.props.actionShoppingCart.deleteShoppingCart();
                        this.props.navigation.navigate("OrderManagement");
                    }
                }
            ]
        )
    }

    getDataLoyalty = (customerPhone, dataSaleOrder) => {
        this.props.actionShoppingCart.checkCredentialExist(customerPhone, dataSaleOrder)
            .then(success => {
                hideBlockUI();
                this.props.navigation.pop();
                this.props.navigation.navigate("OrderLoyalty");
            })
    }

    onCheckLoyaltyPoint = (dataSaleOrder) => {
        const {
            cartRequest: { TotalPointLoyalty, IsAllowParticipationLoyalty, cus_IsHasVoucherConcernAuthenticate },
            customerInfo: { customerPhone }
        } = dataSaleOrder;
        const isLoyalty = IsAllowParticipationLoyalty;
        const isValidatePhone = helper.IsNonEmptyString(customerPhone);
        const isValidatePoint = (TotalPointLoyalty > 0);
        const isMoveToLoyalty = isValidatePoint && isValidatePhone && isLoyalty && !cus_IsHasVoucherConcernAuthenticate;
        if (isMoveToLoyalty) {
            this.getDataLoyalty(customerPhone, dataSaleOrder);
        }
        else {
            this.addToSaleOrderCart(dataSaleOrder);
        }
    }
}

const styles = StyleSheet.create({
});

const mapStateToProps = function (state) {
    return {
        dataCartInstallment: state.shoppingCartReducer.dataCartInstallment,
        paramFilter: state.managerSOReducer.paramFilter,
        userInfo: state.userReducer,
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionShoppingCart: bindActionCreators(actionShoppingCartCreator, dispatch),
        actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),
        actionEditSaleOrder: bindActionCreators(actionEditSaleOrderCreator, dispatch),
        actionManagerSO: bindActionCreators(actionManagerSOCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(InstallmentOTP);

const getMemberInfo = (dataCartInstallment) => {
    let info = {
        customerPhone: '',
        customerName: '',
    };
    if (!helper.IsEmptyObject(dataCartInstallment)) {
        const {
            customerInfo,
        } = dataCartInstallment;
        info = {
            customerPhone: customerInfo.customerPhone,
            customerName: customerInfo.customerName,
        };
    }
    return info;
}

const onLinkingMWGApp = () => {
    Linking.canOpenURL("mwgapp://mwgpos").then(supported => {
        if (supported) {
            Linking.openURL("mwgapp://mwgpos");
        }
    });
}