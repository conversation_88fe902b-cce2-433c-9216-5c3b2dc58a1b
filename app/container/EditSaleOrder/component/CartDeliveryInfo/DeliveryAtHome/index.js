/**
 * Sample React Native App
 * 
 *
 * @format
 * @flow strict-local
 */

import React, { useEffect, useState } from 'react';
import {
  View,
  TouchableOpacity,
  Keyboard,
  Alert,
  SafeAreaView
} from 'react-native';
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { helper, storageHelper } from "@common";
import {
  BaseLoading,
  FieldInput,
  PickerLocation,
  MyText,
  showBlockUI,
  hideBlockUI,
  BouncyCheckboxCustom,
} from "@components";
import { constants, STORAGE_CONST } from "@constants";
import StoreInfo from "./component/StoreInfo";
import RadioGender from "../../../component/Radio/RadioGender";
import ModalDatePicker from "../component/Modal/ModalDatePicker";
import ButtonAddToCart from "../component/ButtonAddToCart";
import Title from "../component/Title";
import { getDistrict, getWard, } from "../../../../Location/action";
import { getCustomerByPhone, getCustomerProfile, getGender, insertProfileReceive, set_map_customer_confirm_policy, updateProfileReceive } from "../../../../ShoppingCart/action";
import { translate } from '@translate';
import { COLORS } from "@styles";
import { useSelector } from 'react-redux';
import CustomerProfileInfo from '../../../../Detail/component/CustomerProfileInfo';
import AddressReceivePicker from '../../../../Detail/DeliveryInfo/DeliveryAtHome/component/AddressReceicePicker';
import CustomerReceivePicker from '../../../../Detail/DeliveryInfo/DeliveryAtHome/component/CustomerReceivePicker';
import ModalAddressReceive from '../../../../Detail/DeliveryInfo/DeliveryAtHome/component/ModalAddressReceive';
import ModalCustomerReceive from '../../../../Detail/DeliveryInfo/DeliveryAtHome/component/ModalCustomerReceive';
import { useDispatch } from 'react-redux';

const { TYPE_PROFILE } = constants
const TYPE_MODAL = {
  EDIT: 2,
  INSERT: 1
};

const DeliveryAtHome = ({
  province,
  deliveryInfo,
  dataStore,
  onUpdateInfo,
  stateStoreAtHome,
  // getDataStore,
  actionEditSaleOrder,
  stockProducts,
  saleProgramID,
  defaultDistrict,
  defaultWard,
  dataSO,
  defaultProvinceID,
  defaultStoreDelivery,
  receiverPhone,
  deliveryTypeID_UI
}) => {
  const dispatch = useDispatch()
  const [provinceID, setProvinceID] = useState(defaultProvinceID);
  const [district, setDistrict] = useState(defaultDistrict);
  const [districtID, setDistrictID] = useState(0);
  const [ward, setWard] = useState(defaultWard);
  const [wardID, setWardID] = useState(0);
  const [indexPager, setIndexPager] = useState(0);
  const [isShowIndicator, setIsShowIndicator] = useState(false);

  const [gender, setGender] = useState(true);
  const [contactPhone, setContactPhone] = useState("");
  const [contactName, setContactName] = useState("");
  const [contactAddress, setContactAddress] = useState("");
  const [storeInfo, setStoreInfo] = useState({});
  const [deliveryTime, setDeliveryTime] = useState("");
  const [vehicleTypeID, setVehicleTypeID] = useState(0);

  const [minDate, setMinDate] = useState("");
  const [maxDate, setMaxDate] = useState("");
  const [dataDate, setDataDate] = useState({});
  const [currentDate, setCurrentDate] = useState("");
  const [currentHour, setCurrentHour] = useState("");

  const [deliveryStoreInfo, setDeliveryStoreInfo] = useState({});
  const [isDeliveryStore, setIsDeliveryStore] = useState(false);
  const [deliveryTypeInfo, setDeliveryTypeInfo] = useState({});
  const [deliveryTypes, setDeliveryTypes] = useState([])


  const [stateProfileReceivePicker, setStateProfileReceivePicker] = useState(
    {
      [TYPE_PROFILE.CUSTOMER_RECEIVE]:
      {
        isShow: false,
        itemSelected: {},
        title: "",
        labelButton: "",
        data: [],
      },
      [TYPE_PROFILE.ADDRESS_RECEIVE]:
      {
        isShow: false,
        itemSelected: {},
        title: "",
        labelButton: "",
        data: [],
      }
    }
  )

  const [stateModalProfileReceive, setStateModalProfileReceive] = useState({
    [TYPE_PROFILE.CUSTOMER_RECEIVE]:
    {
      isShow: false,
      typeModal: 0,
      itemSelected: {},
      title: ''
    },
    [TYPE_PROFILE.ADDRESS_RECEIVE]:
    {
      isShow: false,
      typeModal: 0,
      itemSelected: {},
      title: ''
    }
  });



  const [stateCustomerInfo, setStateCustomerInfo] = useState({
    profileID: null,
    customerPhone: "",
    customerName: "",
    gender: null,
  })
  const [extraAddress, setExtraAddress] = useState("");
  const [isFocus, setIsFocus] = useState(false);
  const [disabledProfile, setDisableProfile] = useState(false);
  const [blockUI, setBlockUI] = useState(false);
  const [isSameCustomer, setIsSameCustomer] = useState(false);
  const [disabledPhone, setDisablePhone] = useState(false);



  const { storeID, languageID, moduleID } = useSelector((state) => state.userReducer);
  const { phoneApply, dataShoppingCart } = useSelector((state) => state.editSaleOrderReducer)
  const { numberPhoneCreateAtHome } = useSelector((state) => state.detailReducer)

  const { customerConfirmPolicy } = useSelector((state) => state.shoppingCartReducer)
  const { IsSendPartner } = dataSO;

  const {
    DisableApplyCustomerPhone: ApplyPromotionToCustomerPhone,
    CustomerInfo
  } = dataShoppingCart;

  const phoneCustomerInfo = customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]?.[0]?.phoneNumber


  const getStoreShippingInfo = (eleStore) => {
    const isNotNull = helper.IsNonEmptyArray(eleStore.suggestTimes);
    let isEmpty = true
    // if (isNotNull) {
    //   const isHasData = eleStore.suggestTimes.find(ele => !ele.isWarning);
    //   isEmpty = !isHasData;
    // }
    if (isEmpty) {
      showBlockUI();
      actionEditSaleOrder.getStoreInfoShippingSO({
        "provinceID": provinceID,
        "districtID": districtID,
        "wardID": wardID,
        "deliveryAddress": contactAddress,
        "storeID": eleStore.storeID,
        "distance": eleStore.distance,
        "stockProducts": stockProducts,
        "saleProgramID": dataSO.saleProgramID,
        "createDateSO": dataSO.createDateSO,
        "saleOrderIDDelete": dataSO.saleOrderIDDelete,
        "saleOrderTypeID": dataSO.saleOrderTypeID,
        "deliveryTypeID": deliveryInfo.DeliveryTypeID,
        "deliveryTime": deliveryInfo.DeliveryTime,
        "deliveryVehicles": deliveryInfo.DeliveryVehicles,
        "isNotChangeDeliveryAddress": false
      }).then(({ storeInfo, storeDelivery }) => {
        setDeliveryStoreInfo(storeDelivery)
        setIsDeliveryStore(false)
        hideBlockUI();
        setStoreInfo(storeInfo);
        setVehicleTypeID(storeInfo.vehicleTypeID);
      }).catch(msgError => {
        hideBlockUI();
        setDeliveryStoreInfo({})
        setIsDeliveryStore(false)
        setStoreInfo(eleStore);
        setVehicleTypeID(eleStore.vehicleTypeID);
      });
    }
    else {
      setDeliveryStoreInfo({})
      setIsDeliveryStore(false)
      setStoreInfo(eleStore);
      setVehicleTypeID(eleStore.vehicleTypeID);
    }
  }

  const getInfoDelivery = (deliveryInfo) => {
    actionEditSaleOrder.getDataAtHomeSO({
      "deliveryAddress": contactAddress,
      "stockProducts": stockProducts,
      "saleProgramID": saleProgramID,
      "createDateSO": dataSO.createDateSO,
      "saleOrderIDDelete": dataSO.saleOrderIDDelete,
      "saleOrderTypeID": dataSO.saleOrderTypeID,
      ...deliveryInfo
    }).then(({ storeDelivery }) => {
      setDeliveryStoreInfo(storeDelivery);
    });
  }

  const onchangeDeliveryType = (ele) => {
    setDeliveryTypeInfo(ele)
  }

  const effectChangeDataStore = () => {
    if (wardID > 0 && helper.IsNonEmptyArray(dataStore)) {
      setStoreInfo(dataStore[0]);
      setVehicleTypeID(dataStore[0].vehicleTypeID);
    }
  }

  const effectChangeStore = () => {
    if (!helper.IsEmptyObject(storeInfo)) {
      getSuggestTimes(storeInfo);
    }
  }

  const effectSetDefaultInfo = () => {
    const {
      ContactGender,
      ContactPhone,
      ContactName,
      DeliveryProvinceName,
      DeliveryDistrictName,
      DeliveryWardName,
      DeliveryAddress,
      DeliveryProvinceID,
      DeliveryDistrictID,
      DeliveryWardID,
      DeliveryTypeID,
      CustomerPhone
    } = deliveryInfo;
    const address = getAddressContent({
      DeliveryProvinceName,
      DeliveryDistrictName,
      DeliveryWardName,
      DeliveryAddress,
    });
    if (DeliveryTypeID != 1) {
      setStateCustomerInfo({
        ...stateCustomerInfo,
        customerName: ContactName,
        customerPhone: phoneCustomerInfo || receiverPhone || CustomerInfo.CustomerPhone || phoneApply || "",
        gender: ContactGender
      })
      setGender(ContactGender);
      setContactPhone(ContactPhone);
      setContactName(ContactName);
      setContactAddress(address);
      setStoreInfo(dataStore[0]);
      setVehicleTypeID(dataStore[0].vehicleTypeID);
      setProvinceID(DeliveryProvinceID);
      setDistrictID(DeliveryDistrictID);
      setWardID(DeliveryWardID);
      setExtraAddress(` ${DeliveryWardName} - ${DeliveryDistrictName} - ${DeliveryProvinceName}`)
    }
  }

  const getDataDistrict = (provinceID) => {
    setIsShowIndicator(true);
    getDistrict(provinceID).then(data => {
      setDistrict(data);
      setIndexPager(1);
      setIsShowIndicator(false);
    }).catch(msgError => {
      Alert.alert(translate('common.notification_uppercase'), msgError,
        [
          {
            text: translate('common.btn_skip'),
            style: "cancel",
            onPress: () => setIsShowIndicator(false)
          },
          {
            text: translate('common.btn_notify_try_again'),
            style: "default",
            onPress: () => getDataDistrict(provinceID)
          }
        ]
      )
    });
  }

  const getDataWard = (provinceID, districtID) => {
    setIsShowIndicator(true);
    getWard(provinceID, districtID).then(data => {
      setWard(data);
      setIndexPager(2);
      setIsShowIndicator(false);
    }).catch(msgError => {
      Alert.alert(translate('common.notification_uppercase'), msgError,
        [
          {
            text: translate('common.btn_skip'),
            style: "cancel",
            onPress: () => setIsShowIndicator(false)
          },
          {
            text: translate('common.btn_notify_try_again'),
            style: "default",
            onPress: () => getDataWard(provinceID, districtID)
          }
        ]
      )
    });
  }

  const effectChangeProvince = () => {
    if (provinceID > 0) {
      getDataDistrict(provinceID);
    }
  }

  const effectChangeDistrict = () => {
    if (districtID > 0) {
      getDataWard(provinceID, districtID);
    }
  }

  const effectChangeWard = () => {
    if (wardID > 0) {
      // getInfoDelivery();
    }
  }

  useEffect(
    effectChangeProvince,
    [provinceID]
  )

  useEffect(
    effectChangeDistrict,
    [districtID]
  )

  useEffect(
    effectChangeWard,
    [wardID]
  )



  useEffect(
    effectChangeDataStore,
    [dataStore]
  )

  useEffect(
    effectChangeStore,
    [storeInfo]
  )

  useEffect(() => {
    if (helper.IsEmptyObject(deliveryStoreInfo)) return;
    if (!isDeliveryStore && dataStore[0].deliveryTypeID == 2) {
      getInfoDelivery(wardID)
    }
    getSuggestTimes(isDeliveryStore ? deliveryStoreInfo : storeInfo)
  }, [isDeliveryStore])
  useEffect(() => {
    if (helper.IsEmptyObject(defaultStoreDelivery)) return;
    if (dataStore[0].deliveryTypeID == 2) {
      setIsDeliveryStore(true)
    }
    setDeliveryStoreInfo(defaultStoreDelivery)
  }, [defaultStoreDelivery])

  useEffect(() => {
    if (deliveryTypeID_UI != 1) {
      setStateCustomerInfo({
        ...stateCustomerInfo,
        customerPhone: phoneCustomerInfo || receiverPhone || CustomerInfo.CustomerPhone || phoneApply || "",
      })
      if (ApplyPromotionToCustomerPhone) {
        setDisablePhone(true)
      }
      else {
        setDisablePhone(false)
      }
    }
  }, [deliveryTypeID_UI])


  const getSuggestTimes = (info) => {
    const { suggestTimes } = info;
    const newSuggestTimes = isDeliveryStore ? deliveryStoreInfo.suggestTimes : suggestTimes
    if (helper.IsNonEmptyArray(newSuggestTimes)) {
      const {
        beginDate,
        endDate,
        data,
        defaultDate
      } = helper.getDateTimeSuggest(newSuggestTimes);
      const { deliveryValue } = beginDate;
      const min = deliveryValue.split("T")[0];
      const max = endDate.deliveryValue.split("T")[0];
      setMinDate(min);
      setMaxDate(max);
      setDataDate(data);

      if (defaultDate) {
        const current = defaultDate.deliveryValue.split("T")[0];
        const hour = defaultDate ? defaultDate.deliveryText : "";
        const deliveryTime = defaultDate ? defaultDate.deliveryValue : "";
        setCurrentDate(current);
        setCurrentHour(hour);
        setDeliveryTime(deliveryTime);
        setDeliveryTypeInfo(defaultDate);
        setDeliveryTypes(defaultDate.deliveryTypelst)
      } else {
        setCurrentDate(min);
        setCurrentHour("");
        setDeliveryTime("");
      }
    }
    else if (!helper.IsEmptyObject(deliveryStoreInfo)) return
    else {
      Alert.alert("", translate('editSaleOrder.expired_time'),
        [
          {
            text: "OK",
            style: "default",
            onPress: () => {
              setStoreInfo({});
              setVehicleTypeID(0);
            }
          }
        ]
      )
    }
  }

  const renderContactInfo = () => {
    return (
      <>
        <View style={{
          width: constants.width - 20,
          flexDirection: "row",
          paddingVertical: 4,
          justifyContent: "space-between",
          // backgroundColor: COLORS.bgFF0000
        }}>
          <RadioGender
            gender={gender}
            onSwitchGender={(value) => {
              setGender(value);
            }}
          />
          <TouchableOpacity style={{
            justifyContent: "center",
            alignItems: "center",
          }}
            onPress={getOldCustomerInfo}
          >
            <MyText
              text={translate('editSaleOrder.old_customer')}
              style={{
                color: COLORS.txtFFA500,
                textDecorationLine: 'underline',
                fontWeight: "bold",
              }} />
          </TouchableOpacity>
        </View>

        <FieldInput
          styleInput={{
            borderWidth: 1,
            borderRadius: 4,
            borderColor: COLORS.bdCCCCCC,
            marginVertical: 5,
            paddingHorizontal: 10,
            backgroundColor: COLORS.bgFFFFFF,
            paddingVertical: 8,
          }}
          placeholder={translate('detail.text_input_phone_number_contact')}
          value={contactPhone}
          onChangeText={(text) => {
            const regExpPhone = new RegExp(/^[0]\d{0,9}$/);
            const isValidate = regExpPhone.test(text) || (text == "");
            if (isValidate) {
              setContactPhone(text);
            }
          }}
          keyboardType={"numeric"}
          returnKeyType={"done"}
          blurOnSubmit={true}
          onBlur={() => getContactInfo(contactPhone)}
          width={constants.width - 20}
          height={40}
          clearText={() => {
            setContactPhone("");
          }}
        />

        <FieldInput
          styleInput={{
            borderWidth: 1,
            borderRadius: 4,
            borderColor: COLORS.bdCCCCCC,
            marginVertical: 5,
            paddingHorizontal: 10,
            backgroundColor: COLORS.bgFFFFFF,
            paddingVertical: 8
          }}
          placeholder={translate('detail.text_input_name_contact')}
          value={contactName}
          onChangeText={(text) => {
            if (helper.isValidateCharVN(text)) {
              setContactName(text);
            }
          }}
          returnKeyType={"default"}
          width={constants.width - 20}
          height={40}
          clearText={() => {
            setContactName("");
          }}
          maxLength={50}
        />

        <FieldInput
          styleInput={{
            borderWidth: 1,
            borderRadius: 4,
            borderColor: COLORS.bdCCCCCC,
            marginVertical: 5,
            paddingHorizontal: 10,
            backgroundColor: IsSendPartner ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
            justifyContent: 'center',
            paddingVertical: 8
          }}
          textAlignVertical={'center'}
          underlineColorAndroid={'transparent'}
          placeholder={translate('detail.text_input_address_contact')}
          value={contactAddress}
          onChangeText={(text) => {
            if (helper.isValidateCharVN(text)) {
              setContactAddress(text);
            }
          }}
          returnKeyType={"default"}
          blurOnSubmit={true}
          onSubmitEditing={() => { Keyboard.dismiss() }}
          width={constants.width - 20}
          multiline={true}
          height={40}
          clearText={() => {
            setContactAddress("");
          }}
          maxLength={300}
          editable={!IsSendPartner}
        />
      </>
    );
  }

  // const getContactInfo = (phoneNumber) => {
  //   const regExpPhone = new RegExp(/^[0]\d{8,9}$/);
  //   const isValidate = regExpPhone.test(phoneNumber);
  //   if (isValidate) {
  //     getCustomerByPhone(phoneNumber).then(info => {
  //       const {
  //         gender,
  //         customerName,
  //         customerAddress,
  //       } = info;
  //       setContactPhone(phoneNumber);
  //       setGender(gender);
  //       setContactName(customerName);
  //       if (!IsSendPartner) {
  //         setContactAddress(customerAddress || contactAddress);
  //       }
  //     })
  //   }
  // }

  const getContactInfo = () => {
    setStateCustomerInfo({
      ...stateCustomerInfo,
      customerName: "",
      gender: null,
      profileID: 0
    })
    const {
      customerPhone,
    } = stateCustomerInfo
    const regExpPhone = new RegExp(/^[0]\d{8,9}$/);
    const isValidate = regExpPhone.test(customerPhone);
    if (isValidate) {
      getCustomerProfile({ phoneNumber: customerPhone, typeProfile: TYPE_PROFILE.CUSTOMER }).then(customerProfile => {
        if (customerProfile == null) return
        const customerInfo = { ...customerProfile[0] }
        setStateCustomerInfo({
          customerName: customerInfo.customerName || "",
          customerPhone: customerPhone,
          gender: customerInfo.gender,
          profileID: customerInfo.profileId
        })
        dispatch(set_map_customer_confirm_policy({
          type: TYPE_PROFILE.CUSTOMER,
          infoCustomerCRM: customerProfile
        }));
        // setContactPhone(contactPhone);
        // setGender(getGender(customerInfo.gender));
        // setContactName(customerInfo.customerName);
      }).catch(err => {
        console.log("🚀 ~ getCustomerProfile ~ err:", err)
      }).finally(() => {
        setDisableProfile(false);
      })
    }
  }

  const getOldCustomerInfo = () => {
    if (contactPhone) {
      getContactInfo(contactPhone);
    }
    else {
      storageHelper.getItem(STORAGE_CONST.CUSTOMER_INFO).then(result => {
        if (helper.IsNonEmptyString(result)) {
          const dataTopInfo = JSON.parse(result);
          const customerInfo = dataTopInfo.find(ele => helper.IsEmptyString(ele.taxID));
          if (customerInfo) {
            const {
              gender,
              customerName,
              customerAddress,
              customerPhone,
            } = customerInfo;
            setGender(gender);
            setContactPhone(customerPhone);
            setContactName(customerName);
            if (!IsSendPartner) {
              setContactAddress(customerAddress || contactAddress);
            }
          }
        }
      }).catch(
        error => {
          console.log("getOldCustomerInfo error", error);
        }
      );
    }
  }

  const checkValidateInfo = () => {
    // const regExpPhone = new RegExp(/^[0]\d{8,9}$/);
    const isValidatePhone = helper.isValidatePhone(contactPhone);
    if (!helper.IsNonEmptyString(contactPhone)) {
      Alert.alert("", translate('editSaleOrder.please_enter_contact_phone_number'));
      return false;
    }
    if (!isValidatePhone) {
      Alert.alert("", translate('editSaleOrder.please_enter_10_digits_phone_number'));
      return false;
    }
    if (!helper.isValidatePhonePrefix(contactPhone)) {
      Alert.alert("", translate('editSaleOrder.please_enter_correct_phone_number_header'));
      return false;
    }
    if (!helper.IsNonEmptyString(contactName)) {
      Alert.alert("", translate('editSaleOrder.please_enter_contact_name'));
      return false;
    }
    if (!helper.IsNonEmptyString(contactAddress)) {
      Alert.alert("", translate('editSaleOrder.please_enter_delivery_address'));
      return false;
    }
    if (!helper.IsNonEmptyString(deliveryTime)) {
      Alert.alert("", translate('editSaleOrder.please_select_delivery_time'));
      return false;
    }
    return true;
  }

  const onAddToCart = () => {
    Keyboard.dismiss();
    const isValidate = checkValidateInfo();
    if (isValidate) {
      const {
        storeRequests,
        storeID,
        deliveryTypeID,
        distance,
        shippingCost,
      } = storeInfo;
      let dataProfileSelected = {}
      if (!helper.IsEmptyObject(stateProfileReceivePicker[TYPE_PROFILE.CUSTOMER_RECEIVE].itemSelected)) {
        // dataProfileSelected = {
        //   ...dataProfileSelected,
        //   [TYPE_PROFILE.CUSTOMER_RECEIVE]: [stateProfileReceivePicker[TYPE_PROFILE.CUSTOMER_RECEIVE].itemSelected]
        // }
        dispatch(set_map_customer_confirm_policy({
          type: TYPE_PROFILE.CUSTOMER_RECEIVE,
          infoCustomerCRM: [stateProfileReceivePicker[TYPE_PROFILE.CUSTOMER_RECEIVE].itemSelected]
        }));
      }
      if (!helper.IsEmptyObject(stateProfileReceivePicker[TYPE_PROFILE.ADDRESS_RECEIVE].itemSelected)) {
        // dataProfileSelected = {
        //   ...dataProfileSelected,
        //   [TYPE_PROFILE.ADDRESS_RECEIVE]: [stateProfileReceivePicker[TYPE_PROFILE.ADDRESS_RECEIVE].itemSelected]
        // }
        dispatch(set_map_customer_confirm_policy({
          type: TYPE_PROFILE.ADDRESS_RECEIVE,
          infoCustomerCRM: [stateProfileReceivePicker[TYPE_PROFILE.ADDRESS_RECEIVE].itemSelected]
        }));
      }
      let StoreChangeOrders = [];
      if (helper.IsNonEmptyArray(storeRequests)) {
        StoreChangeOrders = storeRequests.map(ele => ({
          "StockStoreID": ele.stockStoreID,
          "StoreChangeQuantity": ele.storeChangeQuantity,
          "ShippingCost": ele.shippingCost,
          "TransportTypeID": ele.transportTypeID,
          "StockStoreName": ele.stockStoreName,
          "StockStoreAddress": ele.stockStoreAddress,
          "GetStockType": ele.getStockType
        }));
      }
      onUpdateInfo({
        "DeliveryTime": deliveryTime,
        "DeliveryProvinceID": provinceID,
        "DeliveryDistrictID": districtID,
        "DeliveryWardID": wardID,
        "ContactPhone": contactPhone,
        "ContactName": contactName,
        "ContactGender": gender,
        "DeliveryAddress": contactAddress,
        "DeliveryStoreID": storeID,
        "DeliveryTypeID": isDeliveryStore ? 2 : deliveryTypeInfo.deliveryTypeID,
        "DeliveryVehicles": vehicleTypeID,
        "DeliveryDistance": distance,
        "ShippingCost": shippingCost,
        "StoreChangeOrders": StoreChangeOrders,
        "customerPhone": stateCustomerInfo.customerPhone,
      }
      );
    }
  }

  const getProfileAddressReceive = () => {
    const {
      customerPhone,
      profileID
    } = stateCustomerInfo
    if (!!profileID) {
      showBlockUI();
      getCustomerProfile({ phoneNumber: customerPhone, typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE }).then(address => {
        hideBlockUI()
        if (address == null) return handleStateModalProfileReceive({
          title: "Thêm địa chỉ giao hàng",
          typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE,
          typeModal: TYPE_MODAL.INSERT,
          itemSelected: {},
          isShow: true
        })
        handleStateProfileReceivePicker({ isShow: true, typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE, data: address, title: "Địa chỉ giao hàng" })
      }).catch(msgError => {
        Alert.alert(translate('common.notification_uppercase'), msgError,
          [
            {
              text: translate('common.btn_skip'),
              style: "cancel",
              onPress: hideBlockUI
            },
            {
              text: translate('common.btn_notify_try_again'),
              style: "default",
              onPress: getProfileAddressReceive
            }
          ]
        )
      })
    }
    else {
      handleStateModalProfileReceive({
        title: "Thêm địa chỉ giao hàng",
        typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE,
        typeModal: TYPE_MODAL.INSERT,
        isShow: true
      })

    }

  }
  const getProfileCustomerReceive = () => {
    const {
      customerPhone,
      profileID
    } = stateCustomerInfo
    if (!!profileID) {
      showBlockUI()
      getCustomerProfile({ phoneNumber: customerPhone, typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE }).then(customerProfile => {
        hideBlockUI()
        if (customerProfile == null) return handleStateModalProfileReceive({
          title: "Thêm người nhận",
          typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE,
          typeModal: TYPE_MODAL.INSERT,
          itemSelected: {},
          isShow: true
        })
        handleStateProfileReceivePicker({ isShow: true, typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE, data: customerProfile, title: "Người Nhận" })
      }).catch(msgError => {

        Alert.alert(translate('common.notification_uppercase'), msgError,
          [
            {
              text: translate('common.btn_skip'),
              style: "cancel",
              onPress: hideBlockUI
            },
            {
              text: translate('common.btn_notify_try_again'),
              style: "default",
              onPress: getProfileCustomerReceive
            }
          ]
        )
      })
    }
    else {
      handleStateModalProfileReceive({
        title: "Thêm người nhận",
        typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE,
        typeModal: TYPE_MODAL.INSERT,
        isShow: true
      })
    }

  }
  const handleAPICustomerReceive = (newCustomer) => {
    const { typeModal } = stateModalProfileReceive[TYPE_PROFILE.CUSTOMER_RECEIVE]
    if (typeModal === TYPE_MODAL.INSERT) return handleInsertReceiveCustomer(newCustomer)
    handleUpdateReceiveCustomer(newCustomer)
  }
  const handleInsertReceiveCustomer = (newCustomer) => {
    if (!!(stateCustomerInfo.profileID)) {
      const {
        profileID
      } = stateCustomerInfo
      setBlockUI(true)
      const body = {
        "loginStoreId": storeID,
        "languageID": languageID,
        "moduleID": moduleID,
        "profile": {
          "6": [
            {
              "receiverId": null,
              "receiverGender": newCustomer.gender,
              "receiverName": newCustomer.contactName,
              "receiverPhone": newCustomer.contactPhone,
              "profileId": profileID,
              "type": TYPE_PROFILE.CUSTOMER_RECEIVE,
              "versionCode": null,
              "isModify": null,
              "isSigned": null,
              "signatureId": null
            }
          ]
        },
        typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE
      }
      insertProfileReceive(body).then(customerReceive => {
        setBlockUI(false)
        handleStateProfileReceivePicker({
          typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE,
          isShow: false,
          itemSelected: customerReceive

        })
        handleStateModalProfileReceive({
          typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE,
          isShow: false,
          itemSelected: customerReceive
        })
      }).catch((msgError) => {
        Alert.alert(translate('common.notification_uppercase'), msgError,
          [
            {
              text: translate('common.btn_skip'),
              style: "cancel",
              onPress: () => setBlockUI(false)

            },
            {
              text: translate('common.btn_notify_try_again'),
              style: "default",
              onPress: () => handleInsertReceiveCustomer(newCustomer)
            }
          ]
        )

      })
    }
    else {
      const defaultCustomerReceive = {
        "receiverId": null,
        "receiverGender": newCustomer.gender,
        "receiverName": newCustomer.contactName,
        "receiverPhone": newCustomer.contactPhone,
        "profileId": null,
        "type": TYPE_PROFILE.CUSTOMER_RECEIVE,
        "versionCode": null,
        "isModify": null,
        "isSigned": null,
        "signatureId": null
      }
      handleStateModalProfileReceive({
        typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE,
        itemSelected: defaultCustomerReceive,
        isShow: false

      })
      handleStateProfileReceivePicker({
        typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE,
        itemSelected: defaultCustomerReceive,
        isShow: false
      })
    }


  }
  const handleUpdateReceiveCustomer = (newCustomer) => {
    const { itemSelected } = stateModalProfileReceive[TYPE_PROFILE.CUSTOMER_RECEIVE]
    const { receiverId, profileId, versionCode, isSigned, signatureId, isModify } = itemSelected
    setBlockUI(true)
    const body = {
      "loginStoreId": storeID,
      "languageID": languageID,
      "moduleID": moduleID,
      "profile": {
        "6": [
          {
            "receiverId": receiverId,
            "receiverGender": newCustomer.gender,
            "receiverName": newCustomer.contactName,
            "receiverPhone": newCustomer.contactPhone,
            "profileId": profileId,
            "type": TYPE_PROFILE.CUSTOMER_RECEIVE,
            "versionCode": versionCode,
            "isModify": isModify,
            "isSigned": isSigned,
            "signatureId": signatureId
          }
        ]
      },
      typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE
    }
    updateProfileReceive(body).then(customerReceive => {
      setBlockUI(false)
      handleStateProfileReceivePicker({
        typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE,
        isShow: false,
        itemSelected: customerReceive
      })
      handleStateModalProfileReceive({
        typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE,
        isShow: false,
        itemSelected: customerReceive

      })
    }).catch((msgError) => {
      Alert.alert(translate('common.notification_uppercase'), msgError,
        [
          {
            text: translate('common.btn_skip'),
            style: "cancel",
            onPress: () => setBlockUI(false)

          },
          {
            text: translate('common.btn_notify_try_again'),
            style: "default",
            onPress: () => handleUpdateReceiveCustomer(newCustomer)
          }
        ]
      )

    })
  }
  const handleAPIAddressReceive = (newAddress) => {
    const { typeModal } = stateModalProfileReceive[TYPE_PROFILE.ADDRESS_RECEIVE]
    if (typeModal === TYPE_MODAL.INSERT) return handleInsertAddressCustomer(newAddress)
    handleUpdateAddressCustomer(newAddress)
  }
  const handleInsertAddressCustomer = (newAddress) => {
    const {
      address,
      wardID,
      districtID,
      provinceID,
      provinceName,
      districtName,
      wardName
    }
      = newAddress
    const { profileID } = stateCustomerInfo
    if (!!(stateCustomerInfo.profileID)) {
      setBlockUI(true)
      const body = {
        "loginStoreId": storeID,
        "languageID": languageID,
        "moduleID": moduleID,
        "profile": {
          [TYPE_PROFILE.ADDRESS_RECEIVE]: [
            {
              "deliveryId": null,
              "address": address,
              "wardId": wardID,
              "provinceId": provinceID,
              "countryId": 2,
              "districtId": districtID,
              "profileId": profileID,
              "type": TYPE_PROFILE.ADDRESS_RECEIVE,
              "versionCode": null,
              "isModify": null,
              "isSigned": null,
              "signatureId": null,
              "provinceName": provinceName,
              "districtName": districtName,
              "wardName": wardName,
            }
          ]
        },
        typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE
      }
      insertProfileReceive(body).then(customerReceive => {
        const { wardId, provinceId, districtId } = customerReceive
        setBlockUI(false)
        handleStateProfileReceivePicker({
          typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE,
          isShow: false,
          itemSelected: customerReceive
        })
        handleStateModalProfileReceive({
          typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE,
          isShow: false,
          itemSelected: customerReceive
        })

        setProvinceID(provinceId);
        setDistrictID(districtId);
        setWardID(
          wardId
        );
        setContactAddress(address)
      }).catch((msgError) => {
        Alert.alert(translate('common.notification_uppercase'), msgError,
          [
            {
              text: translate('common.btn_skip'),
              style: "cancel",
              onPress: () => setBlockUI(false)

            },
            {
              text: translate('common.btn_notify_try_again'),
              style: "default",
              onPress: () => handleInsertAddressCustomer(newAddress)
            }
          ]
        )

      })

    }
    else {
      const defaultAddress = {
        address: address,
        countryId: null,
        deliveryId: null,
        districtId: districtID,
        isModify: null,
        isSigned: null,
        profileId: null,
        provinceId: provinceID,
        signatureId: null,
        type: TYPE_PROFILE.ADDRESS_RECEIVE,
        versionCode: null,
        wardId: wardID,
        wardName,
        districtName,
        provinceName
      }
      handleStateModalProfileReceive({
        typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE,
        itemSelected: defaultAddress,
        isShow: false
      })
      handleStateProfileReceivePicker({
        typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE,
        itemSelected: defaultAddress,
        isShow: false
      })

      setProvinceID(provinceID);
      setDistrictID(districtID);
      setWardID(
        wardID
      );
      setContactAddress(address)
    }

  }
  const handleUpdateAddressCustomer = (newAddress) => {
    const { itemSelected } = stateModalProfileReceive[TYPE_PROFILE.ADDRESS_RECEIVE]
    const { address,
      districtID,
      provinceID,
      wardID,
      provinceName,
      districtName,
      wardName
    } = newAddress
    const { deliveryId, profileId, versionCode, isSigned, signatureId, isModify } = itemSelected
    setBlockUI(true)
    const body = {
      "loginStoreId": storeID,
      "languageID": languageID,
      "moduleID": moduleID,
      "profile": {
        "2": [
          {
            "deliveryId": deliveryId,
            "address": address,
            "wardId": wardID,
            "provinceId": provinceID,
            "countryId": "2",
            "districtId": districtID,
            "profileId": profileId,
            "type": 2,
            "versionCode": versionCode,
            "isModify": isModify,
            "isSigned": isSigned,
            "signatureId": signatureId,
            provinceName,
            districtName,
            wardName
          }
        ]
      },
      typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE
    }
    updateProfileReceive(body).then(customerReceive => {
      setBlockUI(false)
      handleStateModalProfileReceive({
        typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE,
        isShow: false,
        itemSelected: customerReceive,
      })
      handleStateProfileReceivePicker({
        typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE,
        itemSelected: customerReceive,
        isShow: false
      });

    }).catch((msgError) => {
      Alert.alert(translate('common.notification_uppercase'), msgError,
        [
          {
            text: translate('common.btn_skip'),
            style: "cancel",
            onPress: () => setBlockUI(false)

          },
          {
            text: translate('common.btn_notify_try_again'),
            style: "default",
            onPress: () => handleUpdateAddressCustomer(newAddress)
          }
        ]
      )

    })
  }

  const handleHideModal = (typeProfile) => {
    setStateModalProfileReceive({
      ...stateModalProfileReceive, [typeProfile]: {
        ...stateModalProfileReceive[typeProfile],
        isShow: false,
        typeModal: 0,
        title: '',
      }
    })
  }

  const handleStateModalProfileReceive = (newState) => {
    const { typeProfile } = newState
    setStateModalProfileReceive({
      ...stateModalProfileReceive, [typeProfile]: {
        ...stateModalProfileReceive[typeProfile],
        ...newState
      }
    })
  }

  const handleStateProfileReceivePicker = (newState) => {
    const { typeProfile } = newState;
    setStateProfileReceivePicker({
      ...stateProfileReceivePicker, [typeProfile]: {
        ...stateProfileReceivePicker[typeProfile],
        ...newState
      }
    })
  }

  useEffect(() => {
    if (helper.IsEmptyObject(stateProfileReceivePicker[TYPE_PROFILE.ADDRESS_RECEIVE].itemSelected)) return;
    const { provinceId, districtId, wardId, address, districtName, wardName, provinceName } = stateProfileReceivePicker[TYPE_PROFILE.ADDRESS_RECEIVE].itemSelected;
    setProvinceID(provinceId);
    setDistrictID(districtId);
    setWardID(wardId);
    setContactAddress(address)
    setExtraAddress(` ${wardName} - ${districtName} - ${provinceName}`)
    // if (wardId == wardID) {
    getInfoDelivery({ wardID: wardId, districtID: districtId, provinceID: provinceId, deliveryAddress: address });
    // }

  }, [stateProfileReceivePicker[TYPE_PROFILE.ADDRESS_RECEIVE].itemSelected])

  useEffect(() => {
    if (helper.IsEmptyObject(stateProfileReceivePicker[TYPE_PROFILE.CUSTOMER_RECEIVE].itemSelected)) return;
    const { receiverGender, receiverName, receiverPhone } = stateProfileReceivePicker[TYPE_PROFILE.CUSTOMER_RECEIVE].itemSelected;
    setContactName(receiverName)
    setContactPhone(receiverPhone)
    setGender(receiverGender)
  }, [stateProfileReceivePicker[TYPE_PROFILE.CUSTOMER_RECEIVE].itemSelected])

  useEffect(() => {
    const {
      customerPhone,
      customerName,
      gender
    } = stateCustomerInfo
    if (!isSameCustomer) return setContactPhone("");
    setContactPhone(customerPhone)
    setContactName(customerName)
    setGender(gender)
  }, [isSameCustomer])


  useEffect(() => {
    const {
      customerPhone,
    } = stateCustomerInfo
    if (!helper.IsNonEmptyString(customerPhone)) return setDisableProfile(true)
    const isValidatePhone = helper.isValidatePhone(customerPhone);
    if (isValidatePhone) {
      // if (isFocus) return
      getContactInfo()
    }
    else {
      handleClearDataDelivery()
    }
  }, [stateCustomerInfo.customerPhone])
  // useEffect(() => {
  //   const {
  //     customerPhone,
  //   } = stateCustomerInfo
  //   const isValidatePhone = helper.isValidatePhone(customerPhone);
  //   if (isValidatePhone) {
  //     if (isFocus) return
  //     getContactInfo()
  //   }
  //   else {
  //     setDisableProfile(true)
  //     setIsSameCustomer(false)
  //     handleStateModalProfileReceive({
  //       typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE,
  //       isShow: false,
  //       typeModal: 0,
  //       itemSelected: {},
  //       title: ''
  //     })
  //     handleStateModalProfileReceive({
  //       typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE,
  //       isShow: false,
  //       typeModal: 0,
  //       itemSelected: {},
  //       title: ''
  //     })
  //     handleStateProfileReceivePicker({
  //       typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE,
  //       isShow: false,
  //       itemSelected: {},
  //       title: "",
  //       labelButton: "",
  //       data: [],
  //     })
  //     handleStateProfileReceivePicker({
  //       typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE,
  //       isShow: false,
  //       itemSelected: {},
  //       title: "",
  //       labelButton: "",
  //       data: [],
  //     })

  //     setProvinceID(0);
  //     setDistrictID(0);
  //     setWardID(
  //       0
  //     );
  //     setContactAddress("")
  //     setStateCustomerInfo({
  //       ...stateCustomerInfo,
  //       customerName: "",
  //       gender: null,
  //       profileID: 0
  //     })
  //     setExtraAddress("")
  //     setContactName("")
  //     setContactPhone("")
  //     if (helper.IsNonEmptyString(customerPhone)) return setContactPhone("");
  //   }
  // }, [stateCustomerInfo.customerPhone])
  const handleClearDataDelivery = () => {
    const {
      customerPhone,
    } = stateCustomerInfo
    setDisableProfile(true)
    setIsSameCustomer(false)
    handleStateModalProfileReceive({
      typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE,
      isShow: false,
      typeModal: 0,
      itemSelected: {},
      title: ''
    })
    handleStateModalProfileReceive({
      typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE,
      isShow: false,
      typeModal: 0,
      itemSelected: {},
      title: ''
    })
    handleStateProfileReceivePicker({
      typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE,
      isShow: false,
      itemSelected: {},
      title: "",
      labelButton: "",
      data: [],
    })
    handleStateProfileReceivePicker({
      typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE,
      isShow: false,
      itemSelected: {},
      title: "",
      labelButton: "",
      data: [],
    })

    setProvinceID(0);
    setDistrictID(0);
    setWardID(
      0
    );
    setContactAddress("")
    setStateCustomerInfo({
      ...stateCustomerInfo,
      customerName: "",
      gender: null,
      profileID: 0
    })
    setExtraAddress("")
    setContactName("")
    setContactPhone("")
    if (helper.IsNonEmptyString(customerPhone)) return setContactPhone("");
  }
  useEffect(
    effectSetDefaultInfo,
    []
  )

  return (
    <SafeAreaView style={{
      flex: 1,
      backgroundColor: COLORS.bgFAFAFA
    }}>
      <KeyboardAwareScrollView
        style={{
          flex: 1
        }}
        enableResetScrollToCoords={false}
        keyboardShouldPersistTaps="always"
        bounces={false}
        overScrollMode="always"
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        extraScrollHeight={60}
      >
        <View style={{
          width: constants.width,
          paddingVertical: 10,
          alignItems: "center"
        }}>
          {/* <PickerLocation
            dataProvince={{
              data: province,
              id: "provinceID",
              value: "provinceName"
            }}
            dataDistrict={{
              data: district,
              id: "districtID",
              value: "districtName"
            }}
            dataWard={{
              data: ward,
              id: "wardID",
              value: "wardName"
            }}
            wardID={wardID}
            districtID={districtID}
            provinceID={provinceID}
            onSelectProvince={(item) => {
              setProvinceID(item.provinceID);
              setDistrictID(0);
              setWardID(0);
            }}
            onSelectDistrict={(item) => {
              setDistrictID(item.districtID);
              setWardID(0);
            }}
            onSelectWard={(item) => {
              if (item.wardID != wardID) {
                setStoreInfo({});
                setVehicleTypeID(0);
                setWardID(item.wardID);
                getInfoDelivery(item.wardID);
              }
            }}
            indexPager={indexPager}
            onShowPicker={(index) => {
              setIndexPager(index);
            }}
            updatePager={(index) => {
              setIndexPager(index);
            }}
            isShowIndicator={isShowIndicator}
            disabled={IsSendPartner}
          /> */}
          {/* {renderContactInfo()} */}
          <CustomerProfileInfo
            isSameCustomer={isSameCustomer}
            stateCustomerInfo={stateCustomerInfo}
            setStateCustomerInfo={setStateCustomerInfo}
            contactAddress={contactAddress + extraAddress}
            contactName={contactName}
            contactPhone={contactPhone}
            disabled={disabledProfile}
            getProfileInfo={getContactInfo}
            getProfileAddressReceive={getProfileAddressReceive}
            getProfileCustomerReceive={getProfileCustomerReceive}
            onPressSameCustomer={() => {
              setIsSameCustomer(!isSameCustomer)
              if (helper.IsNonEmptyArray(customerConfirmPolicy[TYPE_PROFILE.CUSTOMER_RECEIVE])) {
                const customerReceive = customerConfirmPolicy[TYPE_PROFILE.CUSTOMER_RECEIVE].map((item) => {
                  return { ...item, isSameCustomer: !isSameCustomer }
                })
                dispatch(set_map_customer_confirm_policy({
                  type: TYPE_PROFILE.CUSTOMER_RECEIVE,
                  infoCustomerCRM: customerReceive
                }));
              }

            }}
            disabledPhone={disabledPhone}
            setIsFocus={setIsFocus}
            onClear={handleClearDataDelivery}
          />
          {
            wardID > 0 &&
            <BaseLoading
              isLoading={stateStoreAtHome.isFetching}
              isEmpty={stateStoreAtHome.isEmpty}
              textLoadingError={stateStoreAtHome.description}
              isError={stateStoreAtHome.isError}
              onPressTryAgains={() => getInfoDelivery({ wardID, districtID, provinceID, deliveryAddress: contactAddress })}
              content={
                <View style={{
                  width: constants.width,
                  alignItems: "center"
                }}>
                  <StoreInfo
                    dataStore={dataStore}
                    storeInfo={storeInfo}
                    onChangeStore={(newStoreInfo) => {
                      getStoreShippingInfo(newStoreInfo);
                    }}
                    onChangeVehicle={setVehicleTypeID}
                    vehicleTypeID={vehicleTypeID}
                    isDeliveryStore={isDeliveryStore}
                    deliveryTypeInfo={deliveryTypeInfo}
                    deliveryTypes={deliveryTypes}
                    onchangeDeliveryType={onchangeDeliveryType}
                  />
                  {
                    !helper.IsEmptyObject(storeInfo) &&
                    <View style={{
                      width: constants.width,
                      alignItems: "center"
                    }}>
                      {
                        !helper.IsEmptyObject(deliveryStoreInfo) &&
                        <View style={{ width: constants.width - 20, paddingBottom: 10 }}>
                          <BouncyCheckboxCustom
                            isChecked={isDeliveryStore}
                            onToggle={setIsDeliveryStore}
                            label="Hình thức giao: Siêu thị đi giao"
                            iconColor={COLORS.bgF49B0C}
                          />
                        </View>
                      }
                      <ModalDatePicker
                        key={"ModalDatePicker"}
                        minDate={minDate}
                        maxDate={maxDate}
                        dataDate={dataDate}
                        currentDate={currentDate}
                        currentHour={currentHour}
                        onChangeDay={(dateString) => {
                          setCurrentDate(dateString);
                          setCurrentHour("");
                          setDeliveryTime("");
                        }}
                        onChangeHour={(ele) => {
                          const {
                            deliveryValue,
                            deliveryText,
                            deliveryTypelst
                          } = ele;
                          setCurrentHour(deliveryText);
                          setDeliveryTime(deliveryValue);
                          setDeliveryTypeInfo(ele);
                          setDeliveryTypes(deliveryTypelst)
                        }}
                        actionEditSaleOrder={actionEditSaleOrder}
                        storeInfo={{
                          "provinceID": provinceID,
                          "districtID": districtID,
                          "wardID": wardID,
                          "deliveryAddress": contactAddress,
                          "stockProducts": stockProducts,
                          "saleProgramID": dataSO.saleProgramID,
                          "createDateSO": dataSO.createDateSO,
                          "saleOrderIDDelete": dataSO.saleOrderIDDelete,
                          "saleOrderTypeID": dataSO.saleOrderTypeID,
                          "storeID": storeInfo.storeID,
                          "distance": storeInfo.distance,
                          "deliveryTypeID": deliveryTypeInfo.deliveryTypeID ? deliveryTypeInfo.deliveryTypeID : storeInfo.deliveryTypeID,
                          "deliveryVehicles": storeInfo.vehicleTypeID,
                        }}
                        updateData={setDataDate}
                      />
                      <ButtonAddToCart
                        onAddToCart={onAddToCart}
                        disabled={false}
                      />
                    </View>
                  }
                </View>
              }
            />
          }
          {
            stateProfileReceivePicker[TYPE_PROFILE.ADDRESS_RECEIVE].isShow &&
            <AddressReceivePicker
              isShow={stateProfileReceivePicker[TYPE_PROFILE.ADDRESS_RECEIVE].isShow}
              hideModal={() => {
                handleStateProfileReceivePicker({ isShow: false, typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE })
              }}
              onSelect={(info) => {
                handleStateProfileReceivePicker({ isShow: false, itemSelected: info, typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE })
              }}
              valueSelect={stateProfileReceivePicker[TYPE_PROFILE.ADDRESS_RECEIVE].itemSelected}
              data={stateProfileReceivePicker[TYPE_PROFILE.ADDRESS_RECEIVE].data}
              title={stateProfileReceivePicker[TYPE_PROFILE.ADDRESS_RECEIVE].title}
              blockUI={blockUI}
              onInsertAddressReceive={() => {
                handleStateModalProfileReceive({ isShow: true, title: "Thêm địa chỉ giao hàng", typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE, typeModal: TYPE_MODAL.INSERT, itemSelected: {} })
              }}
              onUpdateAddressReceive={(item) => {
                handleStateModalProfileReceive({ isShow: true, title: "Chỉnh sửa địa chỉ nhận", typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE, typeModal: TYPE_MODAL.EDIT, itemSelected: item })
              }}
              //props modal profile
              stateModalProfileReceive={stateModalProfileReceive}
              hideModalAddressReceive={(type) => {
                handleStateProfileReceivePicker({ typeProfile: type, isShow: false })
                handleStateModalProfileReceive({ typeProfile: type, isShow: false })
              }}
              handleAPIAddressReceive={handleAPIAddressReceive}
            />
          }
          {
            stateProfileReceivePicker[TYPE_PROFILE.CUSTOMER_RECEIVE].isShow &&
            <CustomerReceivePicker
              isShow={stateProfileReceivePicker[TYPE_PROFILE.CUSTOMER_RECEIVE].isShow}
              hideModal={() => {
                handleStateProfileReceivePicker({ isShow: false, typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE })
              }}
              onSelect={(info) => {
                handleStateProfileReceivePicker({ isShow: false, typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE, itemSelected: info })
              }}
              valueSelect={stateProfileReceivePicker[TYPE_PROFILE.CUSTOMER_RECEIVE].itemSelected}
              data={stateProfileReceivePicker[TYPE_PROFILE.CUSTOMER_RECEIVE].data}
              title={stateProfileReceivePicker[TYPE_PROFILE.CUSTOMER_RECEIVE].title}
              blockUI={blockUI}
              onInsertCustomerReceive={() => {
                handleStateModalProfileReceive({ isShow: true, title: "Thêm người nhận", typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE, typeModal: TYPE_MODAL.INSERT, itemSelected: {} })
              }}
              onUpdateCustomerReceive={(item) => {
                handleStateModalProfileReceive({ isShow: true, title: "Chỉnh sửa người nhận", typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE, typeModal: TYPE_MODAL.EDIT, itemSelected: item })
              }}
              // props modal profile
              stateModalProfileReceive={stateModalProfileReceive}
              handleAPICustomerReceive={handleAPICustomerReceive}
              hideModalCustomerReceive={(type) => {
                handleStateProfileReceivePicker({ typeProfile: type, isShow: false })
                handleStateModalProfileReceive({ typeProfile: type, isShow: false })
              }}
            />
          }

          {stateModalProfileReceive[TYPE_PROFILE.ADDRESS_RECEIVE].isShow && (
            <ModalAddressReceive
              isShow={stateModalProfileReceive[TYPE_PROFILE.ADDRESS_RECEIVE].isShow}
              hideModal={() =>
                handleHideModal(TYPE_PROFILE.ADDRESS_RECEIVE)
              }
              title={stateModalProfileReceive[TYPE_PROFILE.ADDRESS_RECEIVE].title}
              blockUI={blockUI}
              onPress={handleAPIAddressReceive}
              itemSelected={
                stateModalProfileReceive[TYPE_PROFILE.ADDRESS_RECEIVE].itemSelected
              }
            />
          )}

          {stateModalProfileReceive[TYPE_PROFILE.CUSTOMER_RECEIVE].isShow && (
            <ModalCustomerReceive
              isShow={stateModalProfileReceive[TYPE_PROFILE.CUSTOMER_RECEIVE].isShow}
              hideModal={() =>
                handleHideModal(TYPE_PROFILE.CUSTOMER_RECEIVE)
              }
              title={stateModalProfileReceive[TYPE_PROFILE.CUSTOMER_RECEIVE].title}
              blockUI={blockUI}
              onPress={handleAPICustomerReceive}
              itemSelected={
                stateModalProfileReceive[TYPE_PROFILE.CUSTOMER_RECEIVE].itemSelected
              }
            />
          )}
        </View>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
}

export default DeliveryAtHome;

const getAddressContent = ({
  DeliveryProvinceName,
  DeliveryDistrictName,
  DeliveryWardName,
  DeliveryAddress,
}) => {
  let address = "";
  if (helper.IsNonEmptyString(DeliveryAddress)) {
    const supStr = `, ${DeliveryWardName}, ${DeliveryDistrictName}, ${DeliveryProvinceName}`;
    const sliceIndex = DeliveryAddress.lastIndexOf(supStr);
    if (sliceIndex > 0) {
      address = DeliveryAddress.slice(0, sliceIndex);
    }
    else {
      address = DeliveryAddress;
    }
  }
  return address;
}