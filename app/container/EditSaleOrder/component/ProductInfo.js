import React, { useState, useEffect, useCallback } from 'react';
import {
    View,
    TouchableOpacity,
    Alert,
    Image,
    StyleSheet
} from 'react-native';
import {
    BarcodeCamera,
    hideBlockUI,
    Icon,
    MyText,
    showBlockUI,
} from "@components";
import { constants } from '@constants';
import { debounce, helper } from "@common";
import PromotionGift from "./PromotionGift";
import PromotionSale from "./PromotionSale";
import { translate } from '@translate';
import { COLORS } from "@styles";
import BatchInfo from '../../AnKhangPharmacy/components/BatchInfo';
import { useDispatch, useSelector } from 'react-redux';
import { loadInforBatchNoByCart, setBatchNoByCart } from '../../ShoppingCart/action';
import { SearchBar } from '../../AnKhangNew/components';
import { validateIMEIWithPartner } from '../../Detail/action';

const MainProduct = ({
    product,
    onDelete,
    isApplyCoupon,
    applyDetailIDs,
    isAnKhang,
    handleShowLot,
    onChangeSheet,
    ISSOThreePrice,
    deleteIMEI,
    inputIMEI,
    onChangeIMEI,
    handleCamera
}) => {
    const { storeID } = useSelector((state) => state.userReducer);
    const {
        ProductName,
        SaleProgramInfo,
        IMEI,
        Quantity,
        QuantityUnitName,
        RetailPriceVAT,
        OutputStoreID,
        OutputStoreName,
        AdjustPriceTypeID,
        AdjustPrice,
        InventoryStatusID,
        InventoryStatusName,
        SaleOrderDetailID,
        SalePriceBKVAT,
        IsAdditionalPromotion,
        cus_IsRequiredBatchNO,
        cus_IsEditQuantity,
        IsRequestIMEI,
        IMEITemp
    } = product;
    const isAdjust = !isApplyCoupon && (AdjustPrice != 0);
    const statusName = (InventoryStatusID != 1) ? ` (${InventoryStatusName})` : "";
    const isNonCartPromotion = (applyDetailIDs.size == 0);
    const IsApplyTotalPromotion = isNonCartPromotion || applyDetailIDs.has(SaleOrderDetailID);
    const isThreePrice = (helper.IsEmptyObject(product.PricePolicyApplyBO))
    const isShowLot = (cus_IsEditQuantity || cus_IsRequiredBatchNO) && isAnKhang;
    // Handle label
    const labels = [];
    cus_IsEditQuantity && labels.push(translate("pharmacy.quantity_acronym"));
    cus_IsRequiredBatchNO && labels.push(translate("pharmacy.lot_date"));
    if (
        labels.length === 1 &&
        labels.includes(translate("pharmacy.quantity_acronym"))
    ) {
        // Change `SL` into `Số Lượng`
        labels[0] = translate("pharmacy.quantity_header");

    }
    const isAloneQuantity = cus_IsEditQuantity && !cus_IsRequiredBatchNO
    const labelKey = !!SaleProgramInfo ? translate(constants.ADJUST_PRICE_LABEL[AdjustPriceTypeID]) : (helper.configStoreCreatePrice(storeID) ? "Phiếu mua hàng hỗ trợ chiến giá" : translate(constants.ADJUST_PRICE_LABEL[AdjustPriceTypeID]));

    const onPressDelete = () => {
        Alert.alert("",
            `${translate('editSaleOrder.caution_delete_coupon')} "${ProductName}" ${translate('editSaleOrder.from_the_cart')}`,
            [
                {
                    text: translate('common.btn_skip'),
                    style: "cancel",
                },
                {
                    text: translate('common.btn_continue'),
                    style: "default",
                    onPress: onDelete
                }
            ]
        )
    }

    return (
        <View style={{
            width: constants.width,
            paddingTop: 10,
            paddingBottom: 4,
            backgroundColor: COLORS.bgFFFFFF
        }}>
            <View style={{
                paddingLeft: 10,
                paddingRight: 40,
                marginBottom: 8,
                width: constants.width,
            }}>
                <MyText
                    addSize={2}
                    style={{
                        color: COLORS.txt0000FF,
                        fontWeight: 'bold',
                    }}
                    text={ProductName}>
                    <MyText
                        addSize={2}
                        style={{
                            color: COLORS.txt444444,
                            fontWeight: 'normal',
                        }}
                        text={statusName}
                    />
                    {
                        !!SaleProgramInfo &&
                        <MyText
                            style={{
                                color: COLORS.txtFF6200,
                                fontWeight: 'normal',
                                fontStyle: "italic"
                            }}
                            text={translate('editSaleOrder.installment')}
                        />
                    }
                </MyText>
                {
                    !IsApplyTotalPromotion &&
                    <View style={{
                        paddingRight: 10,
                        marginTop: 4,
                        width: constants.width - 10,
                        justifyContent: "center",
                    }}>
                        <MyText style={{
                            color: COLORS.txtFF8900,
                            fontStyle: "italic"
                        }}
                            addSize={-2}
                            text={translate('editSaleOrder.product_not_valid_for_order_promotion')}
                        />
                    </View>
                }
            </View>
            {
                !!IMEI ?
                    <View style={{
                        paddingHorizontal: 10,
                        marginBottom: 8,
                        width: constants.width,
                    }}>
                        <MyText
                            style={{
                                color: COLORS.txt333333,
                            }}
                            text={`IMEI: ${IMEI}`}
                        />
                    </View> : (
                        IsRequestIMEI && (
                            <View style={{
                                paddingHorizontal: 10,
                                marginBottom: 10,
                                width: constants.width,
                            }}>
                                {
                                    (!!IMEITemp) ?
                                        <View style={{
                                            borderTopWidth: StyleSheet.hairlineWidth,
                                            borderTopColor: COLORS.bgC4C4C4,
                                            flexDirection: "row", alignItems: "center", justifyContent: "space-between",
                                            paddingTop: 5
                                        }}>
                                            <MyText
                                                style={{
                                                    color: COLORS.txt333333,
                                                }}
                                                text={`IMEI: ${IMEITemp}`}
                                            />
                                            <TouchableOpacity
                                                onPress={deleteIMEI}
                                            >
                                                <Icon
                                                    iconSet="AntDesign"
                                                    name="delete"
                                                    color={"red"}
                                                    size={20}
                                                    style={[
                                                        {
                                                            shadowColor: "red",
                                                            padding: 4
                                                        }
                                                    ]}
                                                />
                                            </TouchableOpacity>
                                        </View>
                                        :
                                        <SearchBar
                                            onFocus={() => { }}
                                            setShowListSearch={() => { }}
                                            value={inputIMEI}
                                            placeholder={"Nhập IMEI"}
                                            onChangeText={(value) => {
                                                if (regExpIMEI.test(value)) {
                                                    onChangeIMEI(value)
                                                }
                                            }}
                                            onPressBarcode={handleCamera}
                                            onSearch={() => { }}
                                        />

                                }

                            </View>
                        )
                    )
            }
            <View style={{
                paddingHorizontal: 10,
                marginBottom: 8,
                width: constants.width,
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
            }}>
                <MyText
                    style={{
                        color: COLORS.txt333333,
                    }}
                    text={`${translate('editSaleOrder.quantity_full')} ${Quantity} ${QuantityUnitName}`}
                />
                {
                    isShowLot &&
                    <TouchableOpacity
                        onPress={() => handleShowLot(isAloneQuantity)}
                    >
                        <MyText
                            text={labels.join('/')}
                            style={{
                                color: COLORS.txt0000FF,
                                textDecorationLine: 'underline'
                            }}
                        />
                    </TouchableOpacity>
                }
                {
                    IsAdditionalPromotion
                        ? <MyText
                            style={{
                                color: COLORS.txt333333,
                                fontWeight: "bold"
                            }}
                            text={translate('common.price')}>
                            <MyText
                                style={{
                                    color: COLORS.txtD0021B,
                                    fontWeight: "normal"
                                }}
                                text={helper.convertNum(SalePriceBKVAT)}
                            />
                        </MyText>
                        :
                        (isThreePrice
                            ?
                            <MyText
                                style={{
                                    color: COLORS.txt333333,
                                    fontWeight: "bold"
                                }}
                                text={translate('common.price')}>
                                <MyText
                                    style={{
                                        color: COLORS.txtD0021B,
                                        fontWeight: "normal"
                                    }}
                                    text={helper.convertNum(RetailPriceVAT)}
                                />
                            </MyText>
                            :
                            <TouchableOpacity
                                onPress={onChangeSheet}
                                style={{
                                    flexDirection: "row",
                                    alignItems: "center",
                                    justifyContent: "center",
                                }}>
                                <Image
                                    style={{ width: 17, height: 17, marginHorizontal: 4, marginBottom: 4 }}
                                    source={require('../../../../assets/box.png')}
                                />
                                <MyText
                                    text={translate('common.price')}
                                    style={{
                                        color: COLORS.txt333333,
                                        fontWeight: 'bold'
                                    }}>
                                    <MyText
                                        text={helper.convertNum(RetailPriceVAT)}
                                        style={{
                                            color: COLORS.txtD0021B,
                                            fontWeight: 'normal'
                                        }}
                                    />
                                </MyText>
                            </TouchableOpacity>)
                }
            </View>

            {
                isAdjust &&
                <View style={{
                    flexDirection: "row",
                    width: constants.width,
                    paddingHorizontal: 10,
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginTop: 8
                }}>
                    <MyText style={{
                        color: COLORS.txt333333,
                        fontWeight: "bold"
                    }}
                        text={labelKey}
                    />

                    <MyText style={{
                        color: COLORS.txtD0021B,
                    }}
                        text={helper.convertNum(AdjustPrice)}
                    />
                </View>
            }
            {
                !ISSOThreePrice && <TouchableOpacity style={{
                    width: 40,
                    height: 26,
                    top: 0,
                    right: 0,
                    borderBottomLeftRadius: 20,
                    alignItems: 'center',
                    justifyContent: "center",
                    backgroundColor: COLORS.btnF5F5F5,
                    position: 'absolute',
                }}
                    onPress={onPressDelete}
                >
                    <Icon
                        iconSet={"MaterialIcons"}
                        name={"close"}
                        color={COLORS.icD0021B}
                        size={20}
                    />
                </TouchableOpacity>
            }


        </View>
    );
}

const TotalMoney = ({ total }) => {
    return (
        <View style={{
            backgroundColor: COLORS.bgEEF0DB,
            flexDirection: 'row',
            width: constants.width,
            justifyContent: 'space-between',
            paddingHorizontal: 10,
            height: 40,
        }}>
            <View style={{
                flexDirection: 'row',
                alignSelf: 'flex-end',
                alignItems: 'center',
                height: 40,
            }}>
                <MyText style={{
                    color: COLORS.txt147EFB,
                    fontWeight: 'bold'
                }}
                    text={translate('editSaleOrder.product_price')}>
                    <MyText style={{
                        color: COLORS.txt666666,
                        fontStyle: "italic",
                        fontWeight: 'normal'
                    }}
                        text={translate('editSaleOrder.not_rounded')}
                    />
                </MyText>
            </View>

            <View style={{
                alignSelf: 'flex-end',
                justifyContent: 'center',
                height: 40,
            }}>
                <MyText style={{
                    color: COLORS.txtD0021B,
                    fontWeight: 'bold'
                }}
                    addSize={2}
                    text={helper.convertNum(total)}
                />
            </View>
        </View>
    );
}

const ProductInfo = ({
    mainProduct,
    onDelete,
    onDeleteSaleProduct,
    couponDiscount,
    onUpdateSimProduct,
    applyDetailIDs,
    actionShoppingCart,
    isAnKhang,
    onUpdateSaleOrder,
    onChangeSheet,
    ISSOThreePrice,
    onValidIMEI,
    deleteIMEI,
}) => {
    const {
        ProductName,
        RetailPriceVAT,
        DeliveryInfoRequest,
        giftSaleOrders,
        saleSaleOrders,
        Quantity,
        AdjustPrice,
        // PROMOTION_DELIVERY
        giftDeliverySaleOrders,
        saleDeliverySaleOrders,
        SalePriceBKVAT,
        IsAdditionalPromotion,
        cus_IsEditQuantity,
        cus_InstockQuantity,
        cus_AllowChageQuantity,
        giftLostSaleSOs,
        SaleProgramInfo,
        ProductID,
        InventoryStatusID
    } = mainProduct;
    const giftLostSaleSOsNew = giftLostSaleSOs ?? []
    const isApplyCoupon = (couponDiscount > 0);
    const [sumPrice, setSumPrice] = useState(0);
    const [inputIMEI, setInputIMEI] = useState("")
    const [isScan, setIsScan] = useState(false)

    const { batchNoByCart } = useSelector(
        (state) => state.shoppingCartReducer
    );
    const { dataShoppingCart } = useSelector(
        (state) => state.editSaleOrderReducer
    );
    const { storeID, moduleID, languageID, userName } = useSelector((state) => state.userReducer);
    const [batchModal, setBatchModal] = useState({
        value: [],
        visible: false,
        ProductName: '',
        totalQuantity: 0,
        productKey: '',
        id: '',
        allowToChangeLess: false
    });
    const dispatch = useDispatch();
    const handleShowLot =
        (name, totalQuantity, id, allowToChangeLess, isAloneQuantity) => (productKey) => {
            handleApiLoadBatchNo(
                {
                    ProductName: name,
                    SaleOrderDetailID: id,
                    Quantity: totalQuantity,
                    allowToChangeLess,
                    isAloneQuantity
                },
                productKey
            );
        };
    const handleApiLoadBatchNo = (product, productKey) => {
        const batches = batchNoByCart[product.SaleOrderDetailID];
        if (batches || product.isAloneQuantity) {
            setBatchModal({
                visible: true,
                value: batches ?? [],
                ProductName: product.ProductName,
                id: product.SaleOrderDetailID,
                totalQuantity: product.Quantity,
                productKey,
                allowToChangeLess: product.allowToChangeLess
            });
        } else {
            showBlockUI();
            dispatch(
                loadInforBatchNoByCart({
                    saleOrderDetailID: product.SaleOrderDetailID,
                    cartRequest: dataShoppingCart
                })
            )
                .then((newBatches) => {
                    hideBlockUI();
                    setBatchModal({
                        visible: true,
                        value: newBatches,
                        ProductName: product.ProductName,
                        id: product.SaleOrderDetailID,
                        totalQuantity: product.Quantity,
                        productKey,
                        allowToChangeLess: product.allowToChangeLess
                    });
                    dispatch(
                        setBatchNoByCart({
                            id: product.SaleOrderDetailID,
                            batchNo: newBatches
                        })
                    );
                })
                .catch((msgError) => {
                    hideBlockUI();
                    Alert.alert(
                        translate('common.notification_uppercase'),
                        msgError,
                        [{ text: 'OK' }]
                    );
                });
        }
    };
    const generateNewSaleOrder = (productKey, batch, id) => {
        const keys = [
            'giftSaleOrders',
            'saleSaleOrders',
            'giftDeliverySaleOrders',
            'saleDeliverySaleOrders'
        ];
        const newMainProduct = helper.deepCopy(mainProduct);
        if (keys.includes(productKey)) {
            const index = newMainProduct[productKey].findIndex(
                (product) => product.SaleOrderDetailID === id
            );
            if (index !== -1) {
                newMainProduct[productKey][
                    index
                ].cus_SaleOrderDetailInfoBOList = batch;
            }
        } else {
            // main product
            newMainProduct.cus_SaleOrderDetailInfoBOList = batch;
        }
        return newMainProduct;
    };

    const handleSubmitBatch = (objBatch, productKey, id) => {
        const { batch, clientProductQuantity } = objBatch;
        const newSaleOrder = generateNewSaleOrder(productKey, batch, id);
        dispatch(
            setBatchNoByCart({
                id: batchModal.id,
                batchNo: batch
            })
        );
        onUpdateSaleOrder(newSaleOrder, clientProductQuantity);
    };




    const onChangeIMEI = useCallback((value) => {
        setInputIMEI(value);
        if (helper.IsNonEmptyString(value)) {
            debounceSearchProducts(value);
        }
    }, [SaleProgramInfo]);
    const debounceSearchProducts = useCallback(
        debounce((value) => handleOnSearchProduct(value), 2500),
        [inputIMEI, SaleProgramInfo]
    );
    const handleOnSearchProduct = (value) => {
        const body = {
            loginStoreId: storeID,
            languageID: languageID,
            moduleID: moduleID,
            "IMEI": value,
            "ProductID": ProductID,
            "InventoryStatusID": InventoryStatusID,
            "StoreID": storeID,
            "CheckFormatIMEI": true,
            "PartnerInstallmentID": SaleProgramInfo?.PartnerInstallmentID,
            "UserName": userName

        }
        showBlockUI()
        validateIMEIWithPartner(body).then(() => {
            onValidIMEI(value)
            setInputIMEI("")
            hideBlockUI()
        }).catch((msgError) => {
            Alert.alert(
                translate('common.notification_uppercase'),
                msgError ?? "Đã có lỗi xảy ra. Vui lòng thử lại!",
                [
                    {
                        text: translate('common.btn_skip'),
                        style: 'cancel',
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: 'default',
                        onPress: () => handleOnSearchProduct(value)
                    }
                ]
            );
        })
    };


    const effectMainProduct = () => {
        const newAdjustPrice = !helper.IsEmptyObject(SaleProgramInfo) ? AdjustPrice : (helper.configStoreCreatePrice(storeID) ? 0 : AdjustPrice)
        const salePriceAdjust = isApplyCoupon ? 0 : newAdjustPrice;
        let sumMoney = (IsAdditionalPromotion ? (SalePriceBKVAT + salePriceAdjust) : RetailPriceVAT + salePriceAdjust) * Quantity;
        sumMoney += getSumPriceGift(giftSaleOrders, Quantity);
        sumMoney += getSumPriceSale(saleSaleOrders, isApplyCoupon);
        // PROMOTION_DELIVERY
        sumMoney += getSumPriceGift(giftDeliverySaleOrders, Quantity);
        sumMoney += getSumPriceSale(saleDeliverySaleOrders, isApplyCoupon);
        // PROMOTION_LOSTSALE
        if (!isAnKhang) {
            sumMoney += getSumPriceGift(giftLostSaleSOsNew, Quantity);
        }
        setSumPrice(sumMoney);
    }

    useEffect(
        effectMainProduct,
        [mainProduct]
    )
    const checkDisableKey = batchModal.productKey !== "giftSaleOrders" && batchModal.productKey !== "giftDeliverySaleOrders"
    const allowEditQuantity = (cus_IsEditQuantity && checkDisableKey)

    return (
        <View style={{
            width: constants.width,
            backgroundColor: COLORS.bgFFFFFF,
            shadowColor: COLORS.sd000000,
            shadowOffset: {
                width: 0,
                height: 2,
            },
            shadowOpacity: 0.25,
            shadowRadius: 2.6,
            elevation: 4,
        }}>
            <MainProduct
                ISSOThreePrice={ISSOThreePrice}
                onChangeSheet={onChangeSheet}
                product={mainProduct}
                isApplyCoupon={isApplyCoupon}
                onDelete={onDelete}
                applyDetailIDs={applyDetailIDs}
                isAnKhang={isAnKhang}
                handleShowLot={(isAloneQuantity) => {
                    handleShowLot(
                        ProductName,
                        Quantity,
                        mainProduct.SaleOrderDetailID,
                        cus_AllowChageQuantity,
                        isAloneQuantity
                    )('mainProduct');
                }}
                deleteIMEI={deleteIMEI}
                inputIMEI={inputIMEI}
                onChangeIMEI={onChangeIMEI}
                handleCamera={() => { setIsScan(true) }}
            />
            <PromotionGift
                title={translate('editSaleOrder.promotion')}
                giftSaleOrders={giftSaleOrders}
                mainProduct={mainProduct}
                onUpdateSim={(data) => {
                    onUpdateSimProduct(data, "giftSaleOrders");
                }}
                actionShoppingCart={actionShoppingCart}
                onShowLot={(name, totalQuantity, id, allowToChangeLess) =>
                    handleShowLot(name, totalQuantity, id, allowToChangeLess)('giftSaleOrders')
                }
            />
            <PromotionSale
                title={translate('editSaleOrder.bundle_sale')}
                saleSaleOrders={saleSaleOrders}
                onDeleteSalePromotion={(data) => {
                    onDeleteSaleProduct(data, "saleSaleOrders")
                }}
                onUpdateSim={(data) => {
                    onUpdateSimProduct(data, "saleSaleOrders");
                }}
                isApplyCoupon={isApplyCoupon}
                applyDetailIDs={applyDetailIDs}
                actionShoppingCart={actionShoppingCart}
                onShowLot={(name, totalQuantity, id, allowToChangeLess) =>
                    handleShowLot(name, totalQuantity, id, allowToChangeLess)('saleSaleOrders')
                }
            />
            {
                // PROMOTION_DELIVERY
                <PromotionGift
                    title={translate('editSaleOrder.promotion_delivery_type')}
                    giftSaleOrders={giftDeliverySaleOrders}
                    mainProduct={mainProduct}
                    onUpdateSim={(data) => {
                        onUpdateSimProduct(data, "giftDeliverySaleOrders");
                    }}
                    actionShoppingCart={actionShoppingCart}
                    onShowLot={(name, totalQuantity, id, allowToChangeLess) =>
                        handleShowLot(name, totalQuantity, id, allowToChangeLess)('giftDeliverySaleOrders')
                    }
                />
            }
            {
                // PROMOTION_DELIVERY
                <PromotionSale
                    title={translate('editSaleOrder.bundle_sale_delivery_type')}
                    saleSaleOrders={saleDeliverySaleOrders}
                    onDeleteSalePromotion={(data) => {
                        onDeleteSaleProduct(data, "saleDeliverySaleOrders")
                    }}
                    onUpdateSim={(data) => {
                        onUpdateSimProduct(data, "saleDeliverySaleOrders");
                    }}
                    isApplyCoupon={isApplyCoupon}
                    applyDetailIDs={applyDetailIDs}
                    actionShoppingCart={actionShoppingCart}
                    onShowLot={(name, totalQuantity, id, allowToChangeLess) =>
                        handleShowLot(name, totalQuantity, id, allowToChangeLess)('saleDeliverySaleOrders')
                    }
                />
            }
            {
                // PROMOTION_LOSTSALE
                <PromotionGift
                    title={"Khuyến mãi LostSale"}
                    giftSaleOrders={giftLostSaleSOsNew}
                    mainProduct={mainProduct}
                    onUpdateSim={(data) => {
                        onUpdateSimProduct(data, 'giftLostSaleSOsNew');
                    }}
                    actionShoppingCart={actionShoppingCart}
                    onShowLot={(name, totalQuantity, id, allowToChangeLess) =>
                        handleShowLot(
                            name,
                            totalQuantity,
                            id,
                            allowToChangeLess
                        )('giftLostSaleSOsNew')
                    }
                />
            }
            <TotalMoney
                total={sumPrice}
            />
            <BatchInfo
                data={batchModal.value}
                isShowModal={batchModal.visible}
                onClose={() =>
                    setBatchModal({
                        visible: false,
                        value: [],
                        ProductName: '',
                        totalQuantity: 0,
                        id: '',
                        productKey: '',
                        allowToChangeLess: false
                    })
                }
                productName={batchModal.ProductName}
                totalQuantity={batchModal.totalQuantity}
                editable
                quantityEditable={allowEditQuantity}
                totalInStock={10}
                allowToChangeLess={batchModal.allowToChangeLess}
                onSubmit={(objBatch) =>
                    handleSubmitBatch(
                        objBatch,
                        batchModal.productKey,
                    )
                }
            />
            {
                isScan &&
                <BarcodeCamera
                    isVisible={isScan}
                    closeCamera={() => {
                        setIsScan(false)
                    }}
                    resultScanBarcode={(barcode) => {
                        setIsScan(false)
                        if (regExpIMEI.test(barcode)) {
                            handleOnSearchProduct(barcode);
                        }
                        else {
                            const value = barcode.replace(regExpNOTIMEI, "");
                            if (helper.IsNonEmptyString(value)) {
                                handleOnSearchProduct(value);
                            }
                        }
                    }}
                />
            }
        </View>
    );
}

export default ProductInfo;

const getSumPriceGift = (giftSaleOrders, quantity) => {
    let sumMoney = 0;
    giftSaleOrders.forEach(giftPromotion => {
        if (helper.hasProperty(giftPromotion, 'IsPercentDiscount')) {
            const {
                IsPercentDiscount,
                DiscountMoneyVATSODetail,
                DiscountValue
            } = giftPromotion;
            const moneyDiscount = IsPercentDiscount
                ? DiscountMoneyVATSODetail
                : DiscountValue;
            sumMoney -= moneyDiscount * quantity;
        }
    })
    return sumMoney;
}

const getSumPriceSale = (saleSaleOrders, isApplyCoupon) => {
    let sumMoney = 0;
    saleSaleOrders.forEach(salePromotion => {
        const {
            SalePriceBKVAT,
            Quantity,
            giftSaleOrders,
            AdjustPrice
        } = salePromotion;
        const salePriceAdjust = isApplyCoupon ? 0 : AdjustPrice;
        sumMoney += (SalePriceBKVAT + salePriceAdjust) * Quantity;
        // PROMOTION_DELIVERY
        if (giftSaleOrders) {
            sumMoney += getSumPriceGift(giftSaleOrders, Quantity);
        }
    })
    return sumMoney;
}

const regExpIMEI = new RegExp(/^[a-zA-Z0-9\-\%\+\/\$\.\/]{0,50}$/);
const regExpNOTIMEI = new RegExp(/[^a-zA-Z0-9\-\%\+\/\$\.\/:]/g);
