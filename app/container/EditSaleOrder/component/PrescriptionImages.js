import React, { useEffect, useState } from 'react';
import { COLORS } from '@styles';
import {
    SafeAreaView,
    StyleSheet,
    TouchableOpacity,
    View,
    Modal
} from 'react-native';
import {
    BaseLoading,
    Button,
    hide<PERSON>lock<PERSON>,
    MyText,
    showBlockUI,
    Icon,
    CaptureCamera
} from '@components';
import { constants, API_CONST, ENUM } from '@constants';
import { helper } from '@common';
// eslint-disable-next-line import/no-unresolved
import { launchImageLibrary } from 'react-native-image-picker';
import ImageViewer from 'react-native-image-zoom-viewer';
import { useDispatch, useSelector } from 'react-redux';
import { getImageCDN } from '../../ActiveSimManager/action';
import ImageProcess from '../../AnKhangPharmacy/components/ImageProcess';
import { getImageSaleOrder } from '../action';
const { FILE_PATH: { PRESCRIPTION_IMAGES } } = ENUM;

const PrescriptionImages = ({ handleStringImage }) => {
    const dispatch = useDispatch();
    const [strImage, setStrImage] = useState([
        {
            AttachmentID: 0,
            FileTypeID: 4,
            UrlFile: ''
        }
    ]);
    const [currentPicture, setCurrentPicture] = useState('');
    const [currentStrImageIndex, setCurrentStrImageIndex] = useState(0);
    const [isVisibleCamera, setIsVisibleCamera] = useState(false);
    const [showImage, setShowImage] = useState(false);
    const [isShouldLoad, setIsShouldLoad] = useState(true);
    const [indexImage, setIndexImage] = useState(0);
    const [isShowImageViewer, setIsShowImageViewer] = useState(false);

    const { dataShoppingCart } = useSelector(
        (state) => state.editSaleOrderReducer
    );
    const [state, setState] = useState({
        isFetching: false,
        isEmpty: false,
        description: '',
        isError: false
    });
    const getImage = () => {
        setState({
            isFetching: true,
            isEmpty: false,
            description: '',
            isError: false
        });
        const data = {
            saleOrderID: dataShoppingCart.SaleOrderID,
            saleOrderDetailID: '',
            attachmentTypeList: '4'
        };
        dispatch(getImageSaleOrder(data))
            .then((res) => {
                if (res?.length > 0) {
                    const lstImage = res.map((item) => ({
                        AttachmentID: item.AttachmentID,
                        FileTypeID: 4,
                        UrlFile: item.UrlFile
                    }));
                    setStrImage(lstImage);
                }
                setIsShouldLoad(false);
                setState({
                    isEmpty: false,
                    description: '',
                    isFetching: false,
                    isError: false
                });
            })
            .catch((msgError) => {
                setState({
                    description: msgError,
                    isFetching: false,
                    isEmpty: false,
                    isError: true
                });
            });
    };

    useEffect(() => {
        handleStringImage(strImage);
    }, [strImage]);
    const takePicture = (photo) => {
        setIsVisibleCamera(false);
        showBlockUI();
        if (helper.hasProperty(photo, 'uri')) {
            helper
                .resizeImage(photo)
                .then(({ path, uri, size, name }) => {

                    const body = helper.createFormData({ uri: uri, type: 'image/jpg', name: name, path: PRESCRIPTION_IMAGES });
                    getImageCDN(body)
                        .then((response) => {
                            const remoteURI =
                                API_CONST.API_GET_IMAGE_CDN_NEW +
                                response[0];
                            switch (currentPicture) {
                                case 'strImage':
                                    const newContract = [...strImage];
                                    newContract[
                                        currentStrImageIndex
                                    ].UrlFile = remoteURI;
                                    setStrImage(newContract);
                                    break;
                                default:
                                    break;
                            }
                            hideBlockUI();
                        })
                        .catch((error) => {
                            hideBlockUI();
                            console.log('uploadPicture', error);
                        });
                })
                .catch((error) => {
                    hideBlockUI();
                    console.log('resizeImage', error);
                });
        } else {
            hideBlockUI();
        }
    };
    const selectPicture = () => {
        launchImageLibrary(
            {
                mediaType: 'photo',
                noData: true
            },
            (response) => {
                setIsVisibleCamera(false);
                showBlockUI();
                if (helper.hasProperty(response, 'uri')) {
                    helper
                        .resizeImage(response)
                        .then(({ path, uri, size, name }) => {
                            const body = helper.createFormData({ uri: uri, type: 'image/jpg', name: name, path: PRESCRIPTION_IMAGES });
                            getImageCDN(body)
                                .then((response) => {
                                    const remoteURI =
                                        API_CONST.API_GET_IMAGE_CDN_NEW +
                                        response[0];
                                    switch (currentPicture) {
                                        case 'strImage':
                                            const newContract = [...strImage];
                                            newContract[
                                                currentStrImageIndex
                                            ].UrlFile = remoteURI;
                                            setStrImage(newContract);
                                            break;
                                        default:
                                            break;
                                    }
                                    hideBlockUI();
                                })
                                .catch((error) => {
                                    hideBlockUI();
                                    console.log('uploadPicture', error);
                                });
                        })
                        .catch((error) => {
                            hideBlockUI();
                            console.log('resizeImage', error);
                        });
                } else {
                    hideBlockUI();
                }
            }
        );
    };
    useEffect(() => {
        if (showImage && isShouldLoad) {
            getImage();
        }
    }, [showImage]);
    const onShowImage = () => {
        setShowImage(!showImage);
    };
    const handleDeleteImage = (index) => {
        const defaultImage = {
            AttachmentID: 0,
            FileTypeID: 4,
            UrlFile: ''
        };
        const newImages =
            strImage.length > 1
                ? strImage.filter((_, position) => position !== index)
                : [defaultImage];
        console.log('newImages ', newImages);
        setStrImage(newImages);
    };
    return (
        <SafeAreaView
            style={{
                flex: 1,
                width: constants.width,
                alignItems: 'center'
            }}>
            <View
                style={{
                    alignItems: 'center',
                    backgroundColor: COLORS.btn5B9A68,
                    height: 40,
                    paddingHorizontal: 10
                }}>
                <Button
                    text="Hình ảnh toa thuốc"
                    onPress={() => {
                        onShowImage();
                    }}
                    styleContainer={{
                        flexDirection: 'row',
                        height: 40,
                        justifyContent: 'space-between',
                        width: constants.width - 10
                    }}
                    styleText={{
                        color: COLORS.txtFFFFFF,
                        fontSize: 16,
                        marginRight: 8,
                        fontWeight: 'bold'
                    }}
                    iconRight={{
                        iconSet: 'FontAwesome',
                        name: showImage ? 'chevron-up' : 'chevron-down',
                        size: 14,
                        color: COLORS.icFFFFBC
                    }}
                />
            </View>
            {showImage && (
                <BaseLoading
                    isLoading={state.isFetching}
                    isEmpty={state.isEmpty}
                    textLoadingError={state.description}
                    isError={state.isError}
                    onPressTryAgains={getImage}
                    content={
                        !state.isFetching && (
                            <View>
                                <View
                                    style={{
                                        justifyContent: 'center',
                                        alignItems: 'center'
                                    }}>
                                    <MyText
                                        text="Bạn vui lòng tải hình ảnh hoặc chụp hình ảnh toa thuốc. "
                                        style={{
                                            color: COLORS.txt333333,
                                            marginTop: 10,
                                            fontSize: 15
                                        }}
                                    />
                                </View>

                                <View
                                    style={{
                                        borderWidth: StyleSheet.hairlineWidth,
                                        margin: 10,
                                        paddingBottom: 15,
                                        paddingHorizontal: 10,
                                        borderRadius: 5,
                                        width: constants.width - 40
                                    }}>
                                    {strImage.map((item, index) => (
                                        <ImageProcess
                                            onCamera={() => {
                                                setIsVisibleCamera(true);
                                                setCurrentPicture('strImage');
                                                setCurrentStrImageIndex(index);
                                            }}
                                            onPreview={() => {
                                                setIndexImage(index);
                                                setIsShowImageViewer(true);
                                            }}
                                            urlImageLocal={item.UrlFile}
                                            urlImageRemote={item.UrlFile}
                                            deleteImage={() => {
                                                handleDeleteImage(index);
                                            }}
                                            key={index.toString()}
                                        />
                                    ))}
                                    {strImage.length < 3 && (
                                        <TouchableOpacity
                                            style={{
                                                flex: 1,
                                                height: 150,
                                                width: 150,
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                                alignSelf: 'center',
                                                marginTop: 15,
                                                backgroundColor:
                                                    COLORS.btnF5F5F5,
                                                marginHorizontal: 2
                                            }}
                                            onPress={() => {
                                                setStrImage([
                                                    ...strImage,
                                                    {
                                                        AttachmentID: 0,
                                                        FileTypeID: 4,
                                                        UrlFile: ''
                                                    }
                                                ]);
                                            }}
                                            activeOpacity={0.6}>
                                            <View
                                                style={{
                                                    justifyContent: 'center',
                                                    alignItems: 'center'
                                                }}>
                                                <Icon
                                                    iconSet="Ionicons"
                                                    name="add-circle-outline"
                                                    color="#40B93C"
                                                    size={60}
                                                />
                                            </View>
                                        </TouchableOpacity>
                                    )}
                                </View>
                            </View>
                        )
                    }
                />
            )}

            <CaptureCamera
                isVisibleCamera={isVisibleCamera}
                takePicture={takePicture}
                closeCamera={() => {
                    setIsVisibleCamera(false);
                }}
                selectPicture={selectPicture}
            />
            <Modal visible={isShowImageViewer} transparent>
                <ImageViewer
                    renderHeader={() => (
                        <TouchableOpacity
                            style={{
                                position: 'absolute',
                                right: 5,
                                top: constants.heightTopSafe + 5,
                                zIndex: 100
                            }}
                            onPress={() => setIsShowImageViewer(false)}>
                            <Icon
                                iconSet="Ionicons"
                                name="close"
                                color={COLORS.bgFFFFFF}
                                size={40}
                            />
                        </TouchableOpacity>
                    )}
                    imageUrls={strImage.map((item) => ({
                        url: item.UrlFile
                    }))}
                    index={indexImage}
                    enableSwipeDown
                    onCancel={() => setIsShowImageViewer(false)}
                />
            </Modal>
        </SafeAreaView>
    );
};
export default PrescriptionImages;
