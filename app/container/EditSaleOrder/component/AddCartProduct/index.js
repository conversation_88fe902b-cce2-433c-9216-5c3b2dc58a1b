/**
 * Sample React Native App
 *
 *
 * @format
 * @flow strict-local
 */

import React, { Component } from 'react';
import {
    <PERSON>,
    Alert,
    BackHandler,
    StyleSheet,
} from 'react-native';
import {
    IndicatorViewPager,
    PagerTitleIndicator,
} from 'react-native-best-viewpager';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import Toast from 'react-native-toast-message';
import { helper } from "@common";
import { constants, ENUM, CONFIG } from "@constants";
import {
    BaseLoading,
    showBlockUI,
    hideBlockUI,
    MyText
} from "@components";
import NewStock from "./ProductInfo/NewStock/index";
import SecondStock from "./ProductInfo/SecondStock/index";
import ExhibitStock from "./ProductInfo/ExhibitStock/index";
import TabInventoryStatus from "./ProductInfo/component/TabInventoryStatus";
import ModalFIFO from "./component/Modal/ModalFIFO";
import ModalFeature from "./component/Modal/ModalFeature";
import ModalConfig from "./component/Modal/ModalConfig";
import ModalProductOtherInfo from '../../../Detail/component/Modal/ModalProductOtherInfo';
import SimStock from "./SimInfo/index";
import PromotionDelivery from "./PromotionDelivery/index";
import DeliveryDefault from './DeliveryInfo/DeliveryDefault/index';
import Promotion from "./Promotion/index";
import SalePromtion from "./SalePromotion/index";
import * as actionDetailCreator from "../../../Detail/action";
import * as actionPouchCreator from "../../../PouchRedux/action";
import * as actionEditSaleOrderCreator from "../../action";
import * as actionShoppingCartCreator from "../../../ShoppingCart/action";
import * as actionLoyaltyCreator from "../../../Loyalty/action";
import { COLORS } from "@styles";

import { translate } from '@translate'
import { TIMEOUT_WOW_POINTS } from '../../../Loyalty/constants';
import { PRODUCT_STATUS_ID } from '../../../Detail/constants';
import { PackagePriceSheet } from '../../../Detail/Sheets';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';

const { AN_KHANG_PHARMACY } = ENUM.SALE_SCENARIO_TYPE;
class AddCartProduct extends Component {
    constructor() {
        super();
        this.state = {
            setKeyPromotionSelected: new Set(),
            setGroupIDCondition: new Set(),
            setGroupIDPhoneValidate: new Set(),
            phoneValidate: "",
            isApplyPhone: false,

            inventoryStatusID: 1,
            quantity: 1,
            quantityInStock: 1,

            productInfo: {},
            productSecondInfo: {},
            productExhibitInfo: {},

            packagesTypeId: 0,
            retailPriceVAT: 0,

            isVisibleInstallment: false,
            saleProgramInfo: {
                logo: "",
                partnerID: undefined,
                saleProgramID: 0,
                saleProgramName: "",
                isSpecial: false
            },
            isHasSaleProgram: false,

            storeRequests: [],

            expandPromotion: [],
            expandPromotionDelivery: [],

            isVisibleFIFO: false,
            fifoProduct: {},
            isVisibleFeature: false,
            isVisibleConfig: false,
            isVisibleLockInfo: false,
            defaultDelivery: {
                gender: true,
                contactPhone: "",
                contactName: "",
                contactAddress: "",
            },
            isVisibleProductOtherInfo: false,
            webInfo: {},
            defaultStatusPrice: '',
            defaultPackagePrice: {},
            topInset: 0,
            listPackageService: [],
        }
        this.timeoutChangeProduct = null;
        this.timeoutChangeQuantity = null;
        this.timeoutChangeDelivery = null;
        this.timeoutChangeReceive = null;
        this.isGetSecondStock = false;
        this.isGetExhibitStock = false;
        this.expDataPromotion = {};
        this.expDataPromotionDelivery = {};
        this.defaultSaleProgram = {
            logo: "",
            partnerID: undefined,
            saleProgramID: 0,
            saleProgramName: ""
        };
        this.packagePriceSheetRef = React.createRef(null);

    }

    componentDidMount() {
        if (this.props.saleScenarioTypeID === AN_KHANG_PHARMACY) {
            this.onChangeProductSearch();
        } else {
            this.getInstallmentSearch();
        }
        //addEventListener "addEventListener"
        BackHandler.addEventListener('addEventListener', this.onBackButtonPressed);
    }

    componentDidUpdate(preProps, preState) {
        if (preProps.productSearch !== this.props.productSearch) {
            this.onChangeProductSearch();
        }
        if (preProps.allKeyPromotion !== this.props.allKeyPromotion) {
            const { allKeyPromotion, allGroupID, defaultKeyPromotion } = this.props;
            this.keepKeyPromotionSelected(allKeyPromotion, allGroupID, defaultKeyPromotion);
        }
    }

    onBackButtonPressed = () => {
        return true;
    }

    componentWillUnmount() {
        // this.backHandler.remove();
        BackHandler.removeEventListener('hardwareBackPress', this.onBackButtonPressed);
    }

    render() {
        const {
            setKeyPromotionSelected,
            setGroupIDCondition,
            setGroupIDPhoneValidate,
            phoneValidate,
            isApplyPhone,

            inventoryStatusID,

            productInfo,
            productSecondInfo,
            productExhibitInfo,

            expandPromotion,

            isVisibleFIFO,
            fifoProduct,
            isVisibleFeature,
            isVisibleConfig,

            quantity,
            quantityInStock,
            expandPromotionDelivery,
            isVisibleProductOtherInfo,
            webInfo
        } = this.state;
        const {
            searchInfo: {
                imei,
                isImeiSim,
            },
            promotion,
            salePromotion,
            statePromotion,
            stateProduct,
            stateSecond,
            userInfo: {
                storeID,
                brandID
            },
            dataFifo,
            stateFifo,
            dataConfig,
            stateConfig,
            dataFeature,
            stateFeature,
            cartRequest,
            promotionDelivery,
            salePromotionDelivery,
            allExcludeDisabled,
            actionDetail,
            promotionLostSale
        } = this.props;
        const { DeliveryInfo } = cartRequest;
        const isEmptyPromotion = (promotion.length == 0);
        const isEmptySalePromotion = (salePromotion.length == 0);
        const disabledPager = getValueDisabledPager(
            inventoryStatusID,
            productInfo,
            productSecondInfo,
            productExhibitInfo
        );
        const disabledTabInventory = stateSecond.isFetching;
        const productOrder = this.getProductInfoByStatus();
        const isTabStatus = !imei && (brandID != 16);
        return (
            <View style={{ flex: 1, backgroundColor: COLORS.bgFFFFFF }}>
                <BottomSheetModalProvider>
                    <ProductInfo
                        info={productOrder}
                    />
                    <IndicatorViewPager
                        style={{ flex: 1, flexDirection: "column-reverse" }}
                        indicator={<PagerTitleIndicator
                            initialPage={0}
                            style={{ backgroundColor: COLORS.bg00A98F }}
                            titles={[translate('editSaleOrder.product'), translate('editSaleOrder.promotion'), translate('editSaleOrder.bundle_sale'), translate('editSaleOrder.delivery_method')]}
                            trackScroll={true}
                            selectedBorderStyle={{
                                backgroundColor: COLORS.bgFFF000,
                                height: 2,
                                position: 'absolute',
                                bottom: 0,
                                left: 0,
                                right: 0
                            }}
                            renderTitle={(index, title, isSelected) => {
                                return (
                                    <MyText style={{
                                        color: isSelected
                                            ? COLORS.txtFFF000
                                            : (disabledPager ? COLORS.txtC0C0C0 : COLORS.txtFFFFFF)
                                        ,
                                        fontWeight: "bold"
                                    }}
                                        text={title}
                                        addSize={isSelected ? 2 : 0}
                                    />
                                )
                            }}
                            disabled={disabledPager}
                        />}
                        horizontalScroll={!disabledPager}
                    >
                        <View style={{ flex: 1 }}
                            key={'ProductInfo'}
                        >
                            <BaseLoading
                                isLoading={stateProduct.isFetching}
                                isEmpty={stateProduct.isEmpty}
                                textLoadingError={stateProduct.description}
                                isError={stateProduct.isError}
                                onPressTryAgains={() => this.getDataSearch(false, true)}
                                content={
                                    isImeiSim
                                        ? this.renderSimProduct()
                                        : <>
                                            {
                                                isTabStatus &&
                                                <TabInventoryStatus
                                                    status={inventoryStatusID}
                                                    onChangeTab={this.onChangeTabInventoryStatus}
                                                    disabled={disabledTabInventory}
                                                />
                                            }
                                            {this.renderProductByStatus(inventoryStatusID)}
                                        </>
                                }
                            />
                        </View>

                        <View style={{ flex: 1 }}
                            key={'Promotion'}
                        >
                            {
                                !disabledPager &&
                                <BaseLoading
                                    isLoading={statePromotion.isFetching}
                                    isEmpty={isEmptyPromotion}
                                    textLoadingError={
                                        statePromotion.isError
                                            ? statePromotion.description
                                            : translate('editSaleOrder.no_promotion')
                                    }
                                    isError={statePromotion.isError}
                                    onPressTryAgains={this.getPromotion}
                                    content={
                                        <Promotion
                                            dataPromotion={promotion}
                                            setKeyPromotionSelected={setKeyPromotionSelected}
                                            updateKeyPromotionSelected={this.updateKeyPromotionSelected}
                                            setGroupIDCondition={setGroupIDCondition}
                                            updateGroupIDCondition={(newSetGroupIDCondition, newSetKeyPromotionSelected, newSetGroupIDPhoneValidate) => {
                                                this.setState({
                                                    setGroupIDCondition: newSetGroupIDCondition,
                                                    setKeyPromotionSelected: newSetKeyPromotionSelected,
                                                    setGroupIDPhoneValidate: newSetGroupIDPhoneValidate
                                                });
                                            }}
                                            phoneValidate={phoneValidate}
                                            setGroupIDPhoneValidate={setGroupIDPhoneValidate}
                                            updateGroupIDPhoneValidate={(phoneNumber, newSetGroupIDPhoneValidate, promotionId) => {
                                                this.props.loyaltyAction.setWowPointsMessage({ promotionId });
                                                this.checkWowPoints(phoneNumber);
                                                this.setState({
                                                    phoneValidate: phoneNumber,
                                                    setGroupIDPhoneValidate: newSetGroupIDPhoneValidate
                                                });
                                            }}
                                            cancelPhoneValidate={(newSetGroupIDPhoneValidate, newSetKeyPromotionSelected) => {
                                                this.setState({
                                                    setGroupIDPhoneValidate: newSetGroupIDPhoneValidate,
                                                    setKeyPromotionSelected: newSetKeyPromotionSelected,
                                                });
                                            }}
                                            productInfo={{
                                                "productID": productOrder.productID,
                                                "storeID": DeliveryInfo.DeliveryStoreID,
                                            }}
                                            isFloating={true}
                                            isApplyPhone={isApplyPhone}
                                            productOrder={productOrder}
                                            deliveryInfo={{
                                                "storeID": storeID,
                                                "outputStoreID": DeliveryInfo.DeliveryStoreID,
                                                "deliveryTypeID": DeliveryInfo.DeliveryTypeID
                                            }}
                                            allExcludeDisabled={allExcludeDisabled}
                                            actionDetail={actionDetail}
                                            cartRequest={cartRequest}
                                        />
                                    }
                                />
                            }
                        </View>

                        <View style={{ flex: 1 }}
                            key={'SalePromtion'}
                        >
                            {
                                !disabledPager &&
                                <BaseLoading
                                    isLoading={statePromotion.isFetching}
                                    isEmpty={isEmptySalePromotion}
                                    textLoadingError={
                                        statePromotion.isError
                                            ? statePromotion.description
                                            : translate('editSaleOrder.no_attachments')
                                    }
                                    isError={statePromotion.isError}
                                    onPressTryAgains={this.getPromotion}
                                    content={
                                        <SalePromtion
                                            dataSalePromotion={salePromotion}
                                            setKeyPromotionSelected={setKeyPromotionSelected}
                                            updateKeyPromotionSelected={this.updateKeyPromotionSelected}
                                            setGroupIDCondition={setGroupIDCondition}
                                            updateGroupIDCondition={(newSetGroupIDCondition, newSetKeyPromotionSelected) => {
                                                this.setState({
                                                    setGroupIDCondition: newSetGroupIDCondition,
                                                    setKeyPromotionSelected: newSetKeyPromotionSelected
                                                }, this.removeExpandPromotion);
                                            }}
                                            productOrder={productOrder}
                                            deliveryInfo={{
                                                "storeID": storeID,
                                                "outputStoreID": DeliveryInfo.DeliveryStoreID,
                                                "deliveryTypeID": DeliveryInfo.DeliveryStoreID,
                                            }}

                                            expandPromotion={expandPromotion}
                                            getExpandPromotion={this.getExpandPromotion}
                                            isApplyPhone={isApplyPhone}
                                            phoneValidate={phoneValidate}
                                            setGroupIDPhoneValidate={setGroupIDPhoneValidate}
                                            updateGroupIDPhoneValidate={(phoneNumber, newSetGroupIDPhoneValidate) => {
                                                this.setState({
                                                    phoneValidate: phoneNumber,
                                                    setGroupIDPhoneValidate: newSetGroupIDPhoneValidate
                                                });
                                            }}
                                            cancelPhoneValidate={(newSetGroupIDPhoneValidate, newSetKeyPromotionSelected) => {
                                                this.setState({
                                                    setGroupIDPhoneValidate: newSetGroupIDPhoneValidate,
                                                    setKeyPromotionSelected: newSetKeyPromotionSelected,
                                                }, this.removeExpandPromotion);
                                            }}
                                            isFloating={true}
                                            allExcludeDisabled={allExcludeDisabled}
                                            actionDetail={actionDetail}
                                            cartRequest={cartRequest}
                                        />
                                    }
                                />
                            }
                        </View>

                        <View style={{ flex: 1 }}
                            key={'DeliveryInfo'}
                        >
                            {
                                !disabledPager &&
                                <DeliveryDefault
                                    quantity={quantity}
                                    quantityInStock={quantityInStock}
                                    deliveryInfo={DeliveryInfo}
                                    addToShoppingCart={() => {
                                        this.checkValidatePromotion(productOrder);
                                    }}
                                    children={<PromotionDelivery
                                        statePromotion={statePromotion}
                                        promotion={promotionDelivery}
                                        salePromotion={salePromotionDelivery}
                                        onPromotionTryAgains={this.getPromotion}
                                        updateKeyPromotionSelected={this.updateKeyPromotionSelected}
                                        setKeyPromotionSelected={setKeyPromotionSelected}
                                        setGroupIDCondition={setGroupIDCondition}
                                        updateGroupIDCondition={(newSetGroupIDCondition, newSetKeyPromotionSelected, newSetGroupIDPhoneValidate) => {
                                            this.setState({
                                                setGroupIDCondition: newSetGroupIDCondition,
                                                setKeyPromotionSelected: newSetKeyPromotionSelected,
                                                setGroupIDPhoneValidate: newSetGroupIDPhoneValidate
                                                    ? newSetGroupIDPhoneValidate
                                                    : setGroupIDPhoneValidate
                                            });
                                        }}
                                        phoneValidate={phoneValidate}
                                        setGroupIDPhoneValidate={setGroupIDPhoneValidate}
                                        updateGroupIDPhoneValidate={(phoneNumber, newSetGroupIDPhoneValidate) => {
                                            this.setState({
                                                phoneValidate: phoneNumber,
                                                setGroupIDPhoneValidate: newSetGroupIDPhoneValidate
                                            });
                                        }}
                                        cancelPhoneValidate={(newSetGroupIDPhoneValidate, newSetKeyPromotionSelected) => {
                                            this.setState({
                                                setGroupIDPhoneValidate: newSetGroupIDPhoneValidate,
                                                setKeyPromotionSelected: newSetKeyPromotionSelected,
                                            });
                                        }}
                                        productInfo={{
                                            "productID": productOrder.productID,
                                            "storeID": DeliveryInfo.DeliveryStoreID,
                                        }}
                                        isApplyPhone={isApplyPhone}
                                        productOrder={productOrder}
                                        deliveryInfo={{
                                            "storeID": storeID,
                                            "outputStoreID": DeliveryInfo.DeliveryStoreID,
                                            "deliveryTypeID": DeliveryInfo.DeliveryStoreID,
                                        }}
                                        allExcludeDisabled={allExcludeDisabled}
                                        actionDetail={actionDetail}
                                        expandPromotion={expandPromotionDelivery}
                                        cartRequest={cartRequest}
                                        promotionLostSale={promotionLostSale}

                                    />}
                                />
                            }
                        </View>
                    </IndicatorViewPager>
                    <ModalFIFO
                        isVisible={isVisibleFIFO}
                        hideModal={() => {
                            this.setState({ isVisibleFIFO: false })
                        }}
                        dataFifo={dataFifo}
                        stateFifo={stateFifo}
                        fifoProduct={fifoProduct}
                        onFiFoTryAgains={() => this.getFiFoProduct(fifoProduct)}
                    />

                    <ModalFeature
                        isVisible={isVisibleFeature}
                        hideModal={() => {
                            this.setState({ isVisibleFeature: false })
                        }}
                        dataFeature={dataFeature}
                        stateFeature={stateFeature}
                        onFeatureTryAgains={() => this.getFeatureProduct(productOrder)}
                    />

                    <ModalConfig
                        isVisible={isVisibleConfig}
                        hideModal={() => {
                            this.setState({ isVisibleConfig: false })
                        }}
                        dataConfig={dataConfig}
                        stateConfig={stateConfig}
                        onConfigTryAgains={() => this.getConfigProduct(productOrder)}
                    />
                    {
                        isVisibleProductOtherInfo &&
                        <ModalProductOtherInfo
                            isVisible={isVisibleProductOtherInfo}
                            hideModal={() => {
                                this.setState({ isVisibleProductOtherInfo: false });
                            }}
                            productInfo={productOrder}
                            webInfo={webInfo}
                            brandID={brandID}
                        />
                    }
                    {helper.IsNonEmptyArray(
                        this.state.listPackageService
                    ) && (
                            <PackagePriceSheet
                                ref={this.packagePriceSheetRef}
                                snapPoints={['99.99999%']}
                                product={this.state.defaultPackagePrice}
                                defaultTab={this.state.defaultStatusPrice}
                                listPackageService={this.state.listPackageService}
                                onGoNext={(data) => { }}
                                getCurrentIndex={() => { }}
                                disabled={false}
                                topInset={this.state.topInset}
                                onCloseSheet={() => {
                                    // this.setState({ blockUI: false });
                                }}
                            />

                        )}

                </BottomSheetModalProvider>
            </View>
        );
    }

    renderProductByStatus = (inventoryStatusID) => {
        const {
            quantity,
            productInfo,
            productSecondInfo,
            productExhibitInfo,
            saleProgramInfo,
            retailPriceVAT
        } = this.state;
        const {
            searchInfo: {
                imageUrl,
                imei,
                isImeiSim
            },
            dataProduct,
            secondStock,
            exhibitStock,
            stateSecond,
            stateExhibit,
            dataConfig,
            dataFeature,
            dataFavorite,
            cartRequest,
            actionPouch,
            actionDetail,
            userInfo: { brandID },
            stateInventoryTab
        } = this.props;
        const { DeliveryInfo } = cartRequest;
        const isCartEmpty = helper.IsEmptyObject(cartRequest);
        const isFavorite = this.checkProductFavorite(dataFavorite, productInfo);
        const isFiFO = (brandID != 8) && (inventoryStatusID == 1);
        switch (inventoryStatusID) {
            case 2:
                return (
                    <BaseLoading
                        isLoading={stateSecond.isFetching}
                        isEmpty={stateSecond.isEmpty}
                        textLoadingError={stateSecond.description}
                        isError={stateSecond.isError}
                        onPressTryAgains={this.getDataSecondStock}
                        content={
                            <SecondStock
                                dataSecondStock={secondStock}
                                productInfo={productSecondInfo}
                                onChangeProduct={(newProduct) => {
                                    this.setState({
                                        productSecondInfo: newProduct,
                                        quantityInStock: newProduct.quantity,
                                        retailPriceVAT: newProduct.salePriceVAT,
                                        saleProgramInfo: this.defaultSaleProgram,
                                        quantity: 1,
                                        storeRequests: []
                                    });
                                    if (this.timeoutChangeProduct) {
                                        clearTimeout(this.timeoutChangeProduct);
                                    }
                                    this.timeoutChangeProduct = setTimeout(this.getPromotion, 400);
                                }}
                                saleProgramInfo={saleProgramInfo}
                                onShowInstallment={() => {
                                    this.setState({ isVisibleInstallment: true });
                                }}
                                removeInstallment={() => {
                                    this.setState({
                                        saleProgramInfo: {
                                            logo: "",
                                            partnerID: undefined,
                                            saleProgramID: 0,
                                            saleProgramName: ""
                                        },
                                    }, this.getPromotion);
                                }}
                                isCartEmpty={isCartEmpty}
                                retailPriceVAT={retailPriceVAT}
                                quantity={quantity}
                                onChangeQuantity={(number) => {
                                    this.setState({
                                        quantity: number,
                                        outputStoreID: DeliveryInfo.DeliveryStoreID,
                                        storeRequests: []
                                    });
                                    if (number > 0) {
                                        if (this.timeoutChangeQuantity) {
                                            clearTimeout(this.timeoutChangeQuantity);
                                        }
                                        this.timeoutChangeQuantity = setTimeout(this.getPromotion, 400);
                                    }
                                }}
                            />
                        }
                    />
                );
            case 3:
                return (
                    <BaseLoading
                        isLoading={stateExhibit.isFetching}
                        isEmpty={stateExhibit.isEmpty}
                        textLoadingError={stateExhibit.description}
                        isError={stateExhibit.isError}
                        onPressTryAgains={this.getDataExhibitStock}
                        content={
                            <ExhibitStock
                                dataExhibitStock={exhibitStock}
                                productInfo={productExhibitInfo}
                                onChangeProduct={(newProduct) => {
                                    this.setState({
                                        productExhibitInfo: newProduct,
                                        quantityInStock: newProduct.quantity,
                                        retailPriceVAT: newProduct.salePriceVAT,
                                        saleProgramInfo: this.defaultSaleProgram,
                                        quantity: 1,
                                        storeRequests: []
                                    });
                                    if (this.timeoutChangeProduct) {
                                        clearTimeout(this.timeoutChangeProduct);
                                    }
                                    this.timeoutChangeProduct = setTimeout(this.getPromotion, 400);
                                }}
                                saleProgramInfo={saleProgramInfo}
                                onShowInstallment={() => {
                                    this.setState({ isVisibleInstallment: true });
                                }}
                                removeInstallment={() => {
                                    this.setState({
                                        saleProgramInfo: {
                                            logo: "",
                                            partnerID: undefined,
                                            saleProgramID: 0,
                                            saleProgramName: ""
                                        },
                                    }, this.getPromotion);
                                }}
                                isCartEmpty={isCartEmpty}
                                retailPriceVAT={retailPriceVAT}
                                quantity={quantity}
                                onChangeQuantity={(number) => {
                                    this.setState({
                                        quantity: number,
                                        outputStoreID: DeliveryInfo.DeliveryStoreID,
                                        storeRequests: []
                                    });
                                    if (number > 0) {
                                        if (this.timeoutChangeQuantity) {
                                            clearTimeout(this.timeoutChangeQuantity);
                                        }
                                        this.timeoutChangeQuantity = setTimeout(this.getPromotion, 400);
                                    }
                                }}
                            />
                        }
                    />
                );
            default:
                return (
                    <BaseLoading
                        isLoading={stateInventoryTab.isFetching}
                        isEmpty={stateInventoryTab.isEmpty}
                        textLoadingError={stateInventoryTab.description}
                        isError={stateInventoryTab.isError}
                        onPressTryAgains={() => { this.getDataSearch(inventoryStatusID == 8) }}
                        content={
                            <NewStock
                                imageUrl={imageUrl}
                                dataNewStock={dataProduct}
                                quantity={quantity}
                                onChangeQuantity={(number) => {
                                    this.setState({
                                        quantity: number,
                                        outputStoreID: DeliveryInfo.DeliveryStoreID,
                                        storeRequests: []
                                    });
                                    if (number > 0) {
                                        if (this.timeoutChangeQuantity) {
                                            clearTimeout(this.timeoutChangeQuantity);
                                        }
                                        this.timeoutChangeQuantity = setTimeout(this.getPromotion, 400);
                                    }
                                }}
                                productInfo={productInfo}
                                onChangeProduct={(newProduct) => {
                                    this.setState({
                                        productInfo: newProduct,
                                        quantityInStock: newProduct.quantity,
                                        retailPriceVAT: newProduct.salePriceVAT,
                                        saleProgramInfo: this.defaultSaleProgram,
                                        outputStoreID: DeliveryInfo.DeliveryStoreID,
                                        storeRequests: []
                                    });
                                    this.isGetSecondStock = false;
                                    this.isGetExhibitStock = false;
                                    if (this.timeoutChangeProduct) {
                                        clearTimeout(this.timeoutChangeProduct);
                                    }
                                    this.timeoutChangeProduct = setTimeout(this.getPromotion, 400);
                                }}
                                isImei={!!imei}
                                saleProgramInfo={saleProgramInfo}
                                onShowInstallment={() => {
                                    this.setState({ isVisibleInstallment: true });
                                }}
                                getFifoInfo={(product) => {
                                    this.setState({
                                        isVisibleFIFO: true,
                                        fifoProduct: product
                                    });
                                    this.getFiFoProduct(product);
                                }}
                                getFeatureInfo={() => {
                                    this.setState({
                                        isVisibleFeature: true,
                                    });
                                    if (dataFeature.length == 0) {
                                        this.getFeatureProduct(productInfo);
                                    }
                                }}
                                getConfigInfo={() => {
                                    this.setState({
                                        isVisibleConfig: true,
                                    });
                                    if (dataConfig.length == 0) {
                                        this.getConfigProduct(productInfo);
                                    }
                                }}
                                isFavorite={isFavorite}
                                onFavorite={(product) => {
                                    if (isFavorite) {
                                        actionPouch.removeProductFavorite(product);
                                    }
                                    else {
                                        product.imageUrl = imageUrl;
                                        actionPouch.addProductFavorite(product);
                                    }
                                }}
                                removeInstallment={() => {
                                    const { NEW_STOCK } = PRODUCT_STATUS_ID;

                                    if (NEW_STOCK == inventoryStatusID) {
                                        this.onchangeProgramThreePrice({ productOrder: productInfo }, isDelete = true)
                                    }
                                    else {
                                        this.setState({
                                            saleProgramInfo: {
                                                logo: "",
                                                partnerID: undefined,
                                                saleProgramID: 0,
                                                saleProgramName: "",
                                                isSpecial: false
                                            },
                                        }, this.getPromotion);
                                    }

                                }}
                                isCartEmpty={isCartEmpty}
                                retailPriceVAT={retailPriceVAT}
                                actionDetail={actionDetail}
                                isFiFO={isFiFO}
                                onShowModalProductOtherInfo={webInfo => {
                                    this.setState({
                                        isVisibleProductOtherInfo: true,
                                        webInfo: webInfo
                                    })
                                }}
                                statusPrice={this.state.defaultStatusPrice}
                                listPrice={this.state.listPackageService}
                                onChangeTab={(packagePrice) => {
                                    this.onChangeTabPackagePrice(packagePrice);
                                }}
                                onchangeSheet={() => {
                                    this.packagePriceSheetRef.current.present();
                                    // this.setState({ blockUI: true });
                                }}
                                packagePriceSheetRef={this.packagePriceSheetRef}
                            />}
                    />);
        }
    }

    renderSimProduct = () => {
        const {
            productInfo,
            packagesTypeId
        } = this.state;
        const {
            searchInfo: {
                imageUrl,
            },
            dataPackage,
            statePackage,
        } = this.props;
        return (<SimStock
            imageUrl={imageUrl}
            productInfo={productInfo}
            packagesId={packagesTypeId}
            dataPackage={dataPackage}
            statePackage={statePackage}
            getPackage={this.getDataPackage}
            updateSimInfo={(salePriceVAT, packagesId) => {
                this.setState({
                    packagesTypeId: packagesId,
                    productInfo: {
                        ...productInfo,
                        "salePriceVAT": salePriceVAT
                    }
                });
            }}
        />);
    }

    onChangeTabInventoryStatus = (status) => {
        if (this.timeoutChangeProduct) {
            clearTimeout(this.timeoutChangeProduct);
        }
        switch (status) {
            case 2:
                this.setState({
                    inventoryStatusID: status,
                    productExhibitInfo: {},
                    saleProgramInfo: this.defaultSaleProgram,
                    quantity: 1,
                });
                if (!this.isGetSecondStock) {
                    this.isGetSecondStock = true;
                    this.getDataSecondStock();
                }
                break;
            case 3:
                this.setState({
                    inventoryStatusID: status,
                    productSecondInfo: {},
                    saleProgramInfo: this.defaultSaleProgram,
                    quantity: 1,
                });
                if (!this.isGetExhibitStock) {
                    this.isGetExhibitStock = true;
                    this.getDataExhibitStock();
                }
                break;
            case 8:
                this.setState({
                    inventoryStatusID: status,
                    inventoryStatusID: status,
                    productSecondInfo: {},
                    productExhibitInfo: {},
                    saleProgramInfo: this.defaultSaleProgram,
                    quantityInStock: quantity,
                    retailPriceVAT: salePriceVAT,
                    quantity: 1,
                }, () => {
                    this.getDataSearch(true);
                })
                break;
            default:
                const { productInfo: {
                    quantity,
                    salePriceVAT
                } } = this.state;
                this.setState({
                    inventoryStatusID: status,
                    productSecondInfo: {},
                    productExhibitInfo: {},
                    saleProgramInfo: this.defaultSaleProgram,
                    quantityInStock: quantity,
                    retailPriceVAT: salePriceVAT,
                    quantity: 1,
                }, () => {
                    this.getDataSearch();
                });
                break;
        }
    }

    getProductInfoByStatus = () => {
        const {
            inventoryStatusID,
            productInfo,
            productSecondInfo,
            productExhibitInfo,
        } = this.state;
        switch (inventoryStatusID) {
            case 2:
                return productSecondInfo;
            case 3:
                return productExhibitInfo;
            default:
                return productInfo;
        }
    }

    onChangeProductSearch = () => {
        const {
            productSearch,
            userInfo: {
                storeID
            },
            cartRequest,
            actionDetail,
            searchInfo: {
                isImeiSim,
            },
            packageServices,
        } = this.props;
        const { DeliveryInfo } = cartRequest;
        if (!helper.IsEmptyObject(productSearch)) {
            const {
                imei,
                productID,
                inventoryStatusID,
                salePrice,
                vat,
                vatPercent,
                quantity: quantityInStock,
                salePriceVAT,
                clientQuantity
            } = productSearch;
            const {
                quantity,
                saleProgramInfo: {
                    saleProgramID,
                    partnerID,
                    isSpecial
                },
            } = this.state;
            // Handle three price
            const { inventoryStatusID: newInventoryStatusID } = this.state;
            let finalRetailPriceVAT = 0;
            let finalRetailPrice = 0;
            const { NEW_STOCK, NEW_STOCK_DISCOUNT } = PRODUCT_STATUS_ID;

            if (newInventoryStatusID === NEW_STOCK) {
                const id = `${productID}_${saleProgramID}`;
                const packageServiceArray = packageServices[id];

                if (helper.IsNonEmptyArray(packageServiceArray)) {
                    const newPackageService = packageServiceArray.map(item => {
                        const newSalePrice = item.originTotalAmountNOVAT + salePrice;
                        const newSalePriceVAT = item.originTotalAmount + salePriceVAT;
                        return {
                            ...item,
                            TotalAmountNOVAT: newSalePrice,
                            TotalAmount: newSalePriceVAT
                        };
                    });

                    actionDetail.set_package_services({ id, package: newPackageService });

                    const defaultPrice = newPackageService.find(item => item.IsSelected) || {};
                    finalRetailPriceVAT = defaultPrice.TotalAmount || 0;
                    finalRetailPrice = defaultPrice.TotalAmountNOVAT || 0;

                    this.setState({
                        listPackageService: newPackageService,
                        defaultStatusPrice: defaultPrice.packageStatus || '',
                        defaultPackagePrice: defaultPrice,
                        topInset: (newInventoryStatusID === NEW_STOCK || newInventoryStatusID === NEW_STOCK_DISCOUNT) ? 120 : 180
                    });
                } else {
                    this.setState({
                        listPackageService: [],
                        defaultStatusPrice: '',
                        defaultPackagePrice: {},
                        topInset: 0
                    });
                }
            }
            this.setState({
                productInfo: productSearch,
                quantityInStock: quantityInStock,
                retailPriceVAT: finalRetailPriceVAT || salePriceVAT,
                quantity: clientQuantity ?? quantity // clientQuantity for AnKhang, quantity for default
            });
            actionDetail.getPromotion({
                "imei": imei,
                "productID": productID,
                "inventoryStatusID": inventoryStatusID,
                "price": finalRetailPrice || salePrice,
                "VAT": vat,
                "VATPercent": vatPercent,
                "storeID": storeID,
                "appliedQuantity": quantity,
                "outputStoreID": DeliveryInfo.DeliveryStoreID,
                "deliveryTypeID": DeliveryInfo.DeliveryTypeID,
                "storeRequests": [],
                "saleProgramID": saleProgramID,
                "cartRequest": cartRequest,
                "partnerID": partnerID,
                "isSpecial": isSpecial,
                "isEdit": true
            });
            if (isImeiSim) {
                actionDetail.getSimPackage({
                    "productID": productID,
                    "salePrice": salePrice,
                    "vat": vat,
                    "vatPercent": vatPercent,
                })
            }
        }
        else {
            this.setState({ productInfo: {} })
        }
    }

    getInstallmentSearch = () => {
        const {
            cartRequest,
            phoneApply
        } = this.props;
        const {
            saleProgramInfo,
            deliveryInfo,
            isHasSaleProgram,
        } = getSaleProgramInfo(cartRequest);
        const { DeliveryInfo } = cartRequest;
        const isApplyPhone = true;
        this.defaultSaleProgram = saleProgramInfo;
        this.setState({
            saleProgramInfo: saleProgramInfo,
            defaultDelivery: deliveryInfo,
            phoneValidate: phoneApply,
            isApplyPhone: isApplyPhone,
            isHasSaleProgram: isHasSaleProgram,
            deliveryType: DeliveryInfo.DeliveryTypeID,
            outputStoreID: DeliveryInfo.DeliveryStoreID
        }, () => { this.getDataSearch(false, true) });
    }

    getDataSearch = (isOnSale = false, isFirstCall = false) => {
        const {
            searchInfo: {
                imei,
                inventoryStatusID,
                isImeiSim,
                productID,
                productIDERP
            },
            actionDetail,
            cartRequest
        } = this.props;
        const {
            saleProgramInfo: {
                saleProgramID
            },
        } = this.state;
        let statusID = isOnSale ? 8 : (inventoryStatusID || 1);
        const { DeliveryInfo } = cartRequest;
        const isInstalment = (saleProgramID > 0);
        actionDetail.getInfoProductSearch({
            "imei": imei,
            "productID": productIDERP,
            "productIDRef": productID,
            "inventoryStatusID": statusID,
            "storeID": DeliveryInfo.DeliveryStoreID,
            "saleProgramID": saleProgramID,
            "isInstalment": isInstalment,
            "isImeiSim": isImeiSim,
            "deliveryType": DeliveryInfo.DeliveryTypeID,
            "isCallThreePrice": true

        }, isFirstCall);
    }

    getDataSecondStock = () => {
        const {
            productInfo: {
                productID,
                productIDERP
            }
        } = this.state;
        const {
            userInfo: {
                storeID
            },
            actionDetail
        } = this.props;
        const { saleProgramInfo: {
            saleProgramID
        } } = this.state;
        const isInstalment = (saleProgramID > 0);
        actionDetail.getInfoProductSecond({
            "imei": "",
            "productID": productID,
            "productIDRef": productIDERP,
            "storeID": storeID,
            "saleProgramID": saleProgramID,
            "isInstalment": isInstalment,
        });
    }

    getDataExhibitStock = () => {
        const {
            productInfo: {
                productID,
                productIDERP
            }
        } = this.state;
        const {
            userInfo: {
                storeID
            },
            actionDetail
        } = this.props;
        const { saleProgramInfo: {
            saleProgramID
        } } = this.state;
        const isInstalment = (saleProgramID > 0);
        actionDetail.getInfoProductExhibit({
            "imei": "",
            "productID": productID,
            "productIDRef": productIDERP,
            "storeID": storeID,
            "saleProgramID": saleProgramID,
            "isInstalment": isInstalment,
        });
    }

    getPromotion = () => {
        const productOrder = this.getProductInfoByStatus();
        const {
            userInfo: {
                storeID
            },
            cartRequest,
            actionDetail
        } = this.props;
        const {
            quantity,
            storeRequests,
            saleProgramInfo: {
                saleProgramID,
                partnerID,
                isSpecial
            },
        } = this.state;
        const { DeliveryInfo } = cartRequest;
        let newPrice = productOrder.salePrice
        if (!helper.IsEmptyObject(this.state.defaultPackagePrice)) {
            newPrice = this.state.defaultPackagePrice.TotalAmountNOVAT;
        }
        actionDetail.getPromotion({
            "imei": productOrder.imei,
            "productID": productOrder.productID,
            "inventoryStatusID": productOrder.inventoryStatusID,
            "price": newPrice,
            "VAT": productOrder.vat,
            "VATPercent": productOrder.vatPercent,
            "storeID": storeID,
            "appliedQuantity": quantity,
            "outputStoreID": DeliveryInfo.DeliveryStoreID,
            "deliveryTypeID": DeliveryInfo.DeliveryTypeID,
            "storeRequests": storeRequests,
            "saleProgramID": saleProgramID,
            "cartRequest": cartRequest,
            "partnerID": partnerID,
            "isSpecial": isSpecial
        });
    }

    getExpandPromotion = (
        product,
        keyPromotion,
        promotionGroup,
        subIndex
    ) => {
        const {
            saleProgramInfo: {
                saleProgramID,
                partnerID,
                isSpecial
            },
            storeRequests,
            quantity
        } = this.state;
        const {
            userInfo: {
                storeID
            },
            cartRequest,
            actionDetail
        } = this.props;
        const { DeliveryInfo } = cartRequest;
        showBlockUI();
        actionDetail.getExpandSalePromotion({
            "productID": product.productID,
            "inventoryStatusID": product.inventoryStatusID,
            "price": product.salePrice,
            "VAT": product.vat,
            "VATPercent": product.vatPercent,
            "promotionGroupID": promotionGroup.promotionGroupID,
            "isApplyTotalPromotion": promotionGroup.isApplyTotalPromotion,
            "storeID": storeID,
            "appliedQuantity": product.quantity,
            "outputStoreID": DeliveryInfo.DeliveryStoreID,
            "deliveryTypeID": DeliveryInfo.DeliveryTypeID,
            "storeRequests": storeRequests,
            "saleProgramID": saleProgramID,
            "cartRequest": cartRequest,
            "partnerID": partnerID,
            "isSpecial": isSpecial,
            "promotionListId": product.promotionListId
        }).then(({ expPromotion, expPromotionDelivery, isApplyTotalPromotion }) => {
            promotionGroup.isApplyTotalPromotion = isApplyTotalPromotion;
            hideBlockUI();
            if (helper.IsNonEmptyArray(expPromotion)) {
                this.expDataPromotion[keyPromotion] = {
                    "data": expPromotion,
                    "title": product.productName,
                    "subIndex": subIndex,
                    "productID": product.productID
                };
                const newExpPromotion = Object.entries(this.expDataPromotion);
                this.setState({ expandPromotion: newExpPromotion });
            }
            if (helper.IsNonEmptyArray(expPromotionDelivery)) {
                this.expDataPromotionDelivery[keyPromotion] = {
                    "data": expPromotionDelivery,
                    "title": product.productName,
                    "subIndex": subIndex,
                    "productID": product.productID
                };
                const newExpPromotionDelivery = Object.entries(this.expDataPromotionDelivery);
                this.setState({ expandPromotionDelivery: newExpPromotionDelivery });
            }
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: () => this.getExpandPromotion(
                            product,
                            keyPromotion,
                            promotionGroup,
                            subIndex
                        )
                    }
                ]
            )
        });
    }

    removeExpandPromotion = () => {
        const {
            setKeyPromotionSelected,
            setGroupIDCondition,
            setGroupIDPhoneValidate
        } = this.state;
        Object.keys(this.expDataPromotion).forEach(keyPromotion => {
            if (!setKeyPromotionSelected.has(keyPromotion)) {
                const { data } = this.expDataPromotion[keyPromotion];
                data.forEach((groupPromotion) => {
                    const { promotionGroupID, promotionProducts } = groupPromotion;
                    promotionProducts.forEach((product, index) => {
                        const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
                        const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
                        setKeyPromotionSelected.delete(key);
                        setGroupIDCondition.delete(promotionGroupID);
                        setGroupIDPhoneValidate.delete(promotionGroupID);
                    });
                });
                delete this.expDataPromotion[keyPromotion];
            }
        });
        const newExpPromotion = Object.entries(this.expDataPromotion);
        Object.keys(this.expDataPromotionDelivery).forEach(keyPromotion => {
            if (!setKeyPromotionSelected.has(keyPromotion)) {
                const { data } = this.expDataPromotionDelivery[keyPromotion];
                data.forEach((groupPromotion) => {
                    const { promotionGroupID, promotionProducts } = groupPromotion;
                    promotionProducts.forEach((product, index) => {
                        const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
                        const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
                        setKeyPromotionSelected.delete(key);
                        setGroupIDCondition.delete(promotionGroupID);
                        setGroupIDPhoneValidate.delete(promotionGroupID);
                    });
                });
                delete this.expDataPromotionDelivery[keyPromotion];
            }
        });
        const newExpPromotionDelivery = Object.entries(this.expDataPromotionDelivery);
        this.setState({
            expandPromotion: newExpPromotion,
            expandPromotionDelivery: newExpPromotionDelivery,
            setKeyPromotionSelected: setKeyPromotionSelected,
            setGroupIDCondition: setGroupIDCondition,
            setGroupIDPhoneValidate: setGroupIDPhoneValidate
        });
    }

    updateKeyPromotionSelected = (setKeyPromotionSelected, excludePromotionIDs, saleProductGroupID, keyProduct) => {
        const {
            promotion,
            salePromotion,
            promotionDelivery,
            salePromotionDelivery,
            promotionLostSale
        } = this.props;
        if (helper.IsEmptyArray(excludePromotionIDs)) {
            this.setState({ setKeyPromotionSelected: setKeyPromotionSelected }, this.removeExpandPromotion);
        }
        else {
            let expandGift = [];
            let expandGiftDelivery = [];
            if (!helper.IsEmptyObject(this.expDataPromotion[keyProduct])) {
                expandGift = this.expDataPromotion[keyProduct].data;
            }
            if (!helper.IsEmptyObject(this.expDataPromotionDelivery[keyProduct])) {
                expandGiftDelivery = this.expDataPromotionDelivery[keyProduct].data;
            }
            const allPromotion = [...promotion, ...promotionDelivery, ...expandGift, ...expandGiftDelivery, ...promotionLostSale];
            const alllSalePromotion = [...salePromotion, ...salePromotionDelivery];
            const newSetKeyPromotionSelected = helper.excludeKeyPromotionSelected(
                allPromotion,
                alllSalePromotion,
                setKeyPromotionSelected,
                excludePromotionIDs,
                saleProductGroupID
            );
            this.setState({ setKeyPromotionSelected: newSetKeyPromotionSelected }, this.removeExpandPromotion);
        }
    }

    keepKeyPromotionSelected = (allKeyPromotion, allGroupID, defaultKeyPromotion) => {
        const {
            setKeyPromotionSelected,
            setGroupIDCondition,
            // setGroupIDPhoneValidate
        } = this.state;
        const isEmptyKeySelected = (setKeyPromotionSelected.size == 0);
        const isEmptyAllKey = (allKeyPromotion.size == 0);
        this.expDataPromotion = {};
        this.expDataPromotionDelivery = {};
        if (isEmptyAllKey) {
            this.setState({
                setKeyPromotionSelected: new Set(),
                setGroupIDCondition: new Set(),
                setGroupIDPhoneValidate: new Set(),
                expandPromotion: [],
                expandPromotionDelivery: []
            });
        }
        else if (isEmptyKeySelected) {
            this.setState({
                setKeyPromotionSelected: defaultKeyPromotion,
                setGroupIDCondition: new Set(),
                setGroupIDPhoneValidate: new Set(),
                expandPromotion: [],
                expandPromotionDelivery: []
            });
        }
        else {
            for (let key of setKeyPromotionSelected) {
                if (!allKeyPromotion.has(key)) {
                    setKeyPromotionSelected.delete(key);
                }
            }
            for (let key of setGroupIDCondition) {
                if (!allGroupID.has(key)) {
                    setGroupIDCondition.delete(key);
                }
            }
            // for (let key of setGroupIDPhoneValidate) {
            //     if (!allGroupID.has(key)) {
            //         setGroupIDPhoneValidate.delete(key);
            //     }
            // }
            this.setState({
                setKeyPromotionSelected: setKeyPromotionSelected,
                setGroupIDCondition: setGroupIDCondition,
                setGroupIDPhoneValidate: new Set(),
                expandPromotion: [],
                expandPromotionDelivery: []
            });
        }
    }

    getFiFoProduct = (fifoProduct) => {
        const { actionDetail } = this.props;
        const { productID } = fifoProduct;
        actionDetail.getFifoInfo(productID);
    }

    getConfigProduct = (productInfo) => {
        const { productIDRef } = productInfo;
        const { actionDetail } = this.props;
        actionDetail.getConfigInfo(productIDRef);
    }

    getFeatureProduct = (productInfo) => {
        const { productIDRef } = productInfo;
        const { actionDetail } = this.props;
        actionDetail.getFeatureInfo(productIDRef);
    }

    getListPromotionSelected = () => {
        const {
            setKeyPromotionSelected,
            expandPromotion,
            expandPromotionDelivery,
            phoneValidate,
            setGroupIDPhoneValidate
        } = this.state;
        const {
            promotion,
            salePromotion,
            // PROMOTION_DELIVERY
            promotionDelivery,
            salePromotionDelivery,
            promotionLostSale
        } = this.props;
        let expPromotion = [];
        expandPromotion.forEach(ele => {
            const { data } = ele[1];
            expPromotion = [...expPromotion, ...data];
        });
        let expPromotionDelivery = [];
        expandPromotionDelivery.forEach(ele => {
            const { data } = ele[1];
            expPromotionDelivery = [...expPromotionDelivery, ...data];
        });
        // const allPromotion = [...promotion, ...expPromotion];
        // PROMOTION_DELIVERY
        const allPromotion = [...promotion, ...expPromotion, ...promotionDelivery, ...expPromotionDelivery, ...promotionLostSale];
        // const allSalePromotion = [...salePromotion];
        // PROMOTION_DELIVERY
        const allSalePromotion = [...salePromotion, ...salePromotionDelivery];
        let listPromotion = [];
        let isValidate = true;
        let msgValidate = "";
        let isWarning = false;
        allPromotion.forEach(groupPromotion => {
            const {
                promotionProducts,
                promotionGroupID,
                isRequired,
                isCheckCustomer,
                promotionGroupName,
                promotionID
            } = groupPromotion;
            const isNonEmpty = helper.isArray(promotionProducts);
            if (isNonEmpty) {
                const isValidatePhone = setGroupIDPhoneValidate.has(promotionGroupID);
                const isCheckWarning = (!isCheckCustomer || isValidatePhone);
                const isCheckRequire = isRequired && isCheckWarning;
                groupPromotion.applyToCustomerPhone = isCheckCustomer ? phoneValidate : "";
                let productSelected = promotionProducts.filter((product, index) => {
                    const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
                    const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
                    return setKeyPromotionSelected.has(key);
                });
                if (productSelected.length > 0) {
                    listPromotion.push({
                        ...groupPromotion,
                        promotionProducts: productSelected
                    });
                }
                else {
                    const allExcludeID = helper.getExcludePromotionID(
                        allPromotion,
                        allSalePromotion,
                        setKeyPromotionSelected
                    );
                    const isExclude = allExcludeID.has(promotionID);
                    if (!isExclude) {
                        if (isCheckRequire) {
                            msgValidate += (
                                isValidate
                                    ? `\t${promotionGroupName}`
                                    : `\n\t${promotionGroupName}`
                            );
                            isValidate = false;
                        }
                        if (isCheckWarning) {
                            isWarning = true;
                        }
                    }
                }
            }
        })
        allSalePromotion.forEach(subPromotion => {
            const { promotionGroups } = subPromotion;
            promotionGroups.forEach(groupPromotion => {
                const { promotionProducts, promotionGroupID } = groupPromotion;
                let productSelected = promotionProducts.filter((product, index) => {
                    const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
                    const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
                    return setKeyPromotionSelected.has(key);
                });
                if (productSelected.length > 0) {
                    listPromotion.push({
                        ...groupPromotion,
                        promotionProducts: productSelected
                    })
                }
            })
        })
        return { listPromotion, isValidate, msgValidate, isWarning };
    }

    checkValidatePromotion = (productOrder) => {
        const {
            quantity,
            saleProgramInfo: {
                saleProgramID
            },
            packagesTypeId,
            phoneValidate,
            defaultPackagePrice
        } = this.state;
        const {
            cartRequest,
            searchInfo: {
                isImeiSim,
            },
        } = this.props;
        const { DeliveryInfo } = cartRequest;
        const {
            listPromotion,
            isValidate,
            msgValidate,
            isWarning
        } = this.getListPromotionSelected();
        const {
            expirationDate,
            isSetExpirationDate,
            isRequiredBatchNo,
            batchNo,
            isBatchManagement,
            manufactureBatchNO
        } = productOrder;
        const extraPropsForAnKhang = {
            expirationDate,
            isSetExpirationDate,
            isrequiredBatchNO: isRequiredBatchNo,
            batchNO: batchNo,
            isBatchManagement,
            manufactureBatchNO
        };
        let extraPropsThreePrice = {}
        const { NEW_STOCK } = PRODUCT_STATUS_ID;

        if (!helper.IsEmptyObject(defaultPackagePrice) && this.state.inventoryStatusID == NEW_STOCK) {
            extraPropsThreePrice.extensionProperty = {
                "PricePolicyApplyBO": {
                    "PricePolicyProgramID": defaultPackagePrice.pricePolicyProgramID,
                    "SalePricePolicyID": defaultPackagePrice.SalePricePolicyID,
                    "SalePricePolicyName": defaultPackagePrice.SalePricePolicyName,
                    "ProductID": productOrder.productID,
                    "lstSaleOrderPricePolicyApplyDetailBO": defaultPackagePrice.DetailPolicies
                }
            }
        }
        const isRequirePackage = isImeiSim && (packagesTypeId == 0);
        if (isRequirePackage) {
            Alert.alert("", translate('editSaleOrder.please_select_sim_package'));
        }
        else if (!isValidate) {
            Alert.alert(translate('editSaleOrder.please_select_promotion'), msgValidate);
        }
        else if (isWarning) {
            Alert.alert("",
                translate('editSaleOrder.dismiss_promotion'),
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                    },
                    {
                        text: translate('common.btn_continue'),
                        style: "default",
                        onPress: () => {
                            this.addToShoppingCart({
                                "mainProduct": {
                                    "productID": productOrder.productID,
                                    "imei": productOrder.imei,
                                    "inventoryStatusID": productOrder.inventoryStatusID,
                                    "pointLoyalty": productOrder.pointLoyalty,
                                    "outputTypeID": 3,
                                    "appliedQuantity": quantity,
                                    "outputStoreID": DeliveryInfo.DeliveryStoreID,
                                    "deliveryTypeID": DeliveryInfo.DeliveryTypeID,
                                    "saleProgramID": saleProgramID,
                                    "packagesTypeId": packagesTypeId,
                                    "saleOrderID": cartRequest.SaleOrderID,
                                    ...extraPropsForAnKhang,
                                    ...extraPropsThreePrice
                                },
                                "promotionGroups": listPromotion,
                                "saleOrder": cartRequest,
                                "promotionResultList": [],
                                "currentContinue": null,
                                "lastContinue": null
                            });
                        }
                    }
                ]
            )
        }
        else {
            this.addToShoppingCart({
                "mainProduct": {
                    "productID": productOrder.productID,
                    "imei": productOrder.imei,
                    "inventoryStatusID": productOrder.inventoryStatusID,
                    "pointLoyalty": productOrder.pointLoyalty,
                    "outputTypeID": 3,
                    "appliedQuantity": quantity,
                    "outputStoreID": DeliveryInfo.DeliveryStoreID,
                    "deliveryTypeID": DeliveryInfo.DeliveryTypeID,
                    "saleProgramID": saleProgramID,
                    "packagesTypeId": packagesTypeId,
                    "saleOrderID": cartRequest.SaleOrderID,
                    ...extraPropsForAnKhang,
                    ...extraPropsThreePrice
                },
                "promotionGroups": listPromotion,
                "saleOrder": cartRequest,
                "promotionResultList": [],
                "currentContinue": null,
                "lastContinue": null
            });
        }
    }

    addToShoppingCart = (data) => {
        showBlockUI();
        this.props.actionEditSaleOrder.addProductToSaleOrder(data).then(isContinue => {
            if (isContinue) {
                hideBlockUI();
                this.props.navigation.navigate("AddCartPromotion");
            }
            else {
                this.addPromotionToSaleOrder(data);
            }
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: () => this.addToShoppingCart(data)
                    }
                ]
            )
        });
    }

    addPromotionToSaleOrder = (data) => {
        this.props.actionEditSaleOrder.addPromotionToSaleOrder(data).then(dataCart => {
            console.log("dataCart", dataCart);
            this.getMultiSalePromotion(dataCart);
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: () => this.addPromotionToSaleOrder(data)
                    }
                ]
            )
        });
    }

    checkProductFavorite = (dataFavorite, productInfo) => {
        if (helper.isArray(dataFavorite)) {
            const indexProduct = dataFavorite.findIndex(ele => ele.productID == productInfo.productID);
            return indexProduct > -1;
        }
        else {
            return false;
        }
    }

    getDataPackage = (data) => {
        const {
            actionDetail
        } = this.props;
        actionDetail.getSimPackage({ data })
    }

    getMultiSalePromotion = (dataCart) => {
        this.props.actionEditSaleOrder.getMultiSalePromotion({
            "cartRequest": dataCart,
            "discountCode": "",
            "giftCode": "",
            "promotionGroups": [],
        }, true).then(success => {
            hideBlockUI();
            this.props.navigation.navigate("EditSaleOrder");
        });
    }

    checkWowPoints = (phoneNumber) => {
        // this.props.loyaltyAction.checkWowPoints(phoneNumber, true)
        //     .then((wowPointsMessage) => {
        //         if (wowPointsMessage) {
        //             setTimeout(() => {
        //                 this.props.loyaltyAction.setWowPointsMessage({
        //                     phoneNumber: '',
        //                     message: ''
        //                 });
        //             }, TIMEOUT_WOW_POINTS);
        //         }
        //     })
        //     .catch((error) => {
        //         !CONFIG.isPRODUCTION && Toast.show({
        //             type: 'error',
        //             text1: error ?? 'Lỗi: Hỏi Trần Nghĩa - 165059'
        //         });
        //         console.log("checkWowPoints ", error);
        //     })
    }
    onChangeTabPackagePrice = (packagePrice) => {
        const { productInfo } = this.state;
        const { packageStatus, TotalAmount } = packagePrice;

        this.setState(
            {
                defaultStatusPrice: packageStatus,
                defaultPackagePrice: packagePrice,
                productInfo,
                retailPriceVAT: TotalAmount
            },
            this.getPromotion
        );
    };

    onchangeProgramThreePrice = async (dataProgram, isDelete) => {
        const { userInfo: { storeID, brandID, moduleID, languageID }, saleScenarioTypeID } = this.props;
        const { productOrder, programInfo = {} } = dataProgram;
        const newProductOrder = { ...productOrder };

        this.setState({ isVisibleInstallment: false });

        try {
            showBlockUI();

            const bodyThreePrice = {
                loginStoreId: storeID,
                languageID,
                moduleID,
                saleScenarioTypeID,
                productProlicyBO: {
                    productID: newProductOrder.productID,
                    imei: newProductOrder.imei,
                    inventoryStatusID: newProductOrder.inventoryStatusID
                },
                outputStoreID: storeID,
                dateTime: null,
                saleProgramID: programInfo.saleProgramID ?? 0,
                deliveryTypeID: newProductOrder.deliveryTypeID ?? 1,
                customerInfoProlicyBO: {
                    customerPhone: ""
                }
            };

            const dataPackageService = await this.props.actionDetail.getPackageService(bodyThreePrice);
            const finalRetailPriceVAT = this.handlePackageService(dataPackageService, productOrder, programInfo);

            this.setState({
                productInfo: newProductOrder,
                retailPriceVAT: finalRetailPriceVAT || newProductOrder.salePriceVAT,
            }, () => this.handleSaleProgram(dataProgram, isDelete));

        } catch (error) {
            console.error("Error in onchangeProgramThreePrice:", error);
            Alert.alert(
                translate('common.notification'),
                error.msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: 'cancel',
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: 'default',
                        onPress: () => this.onchangeProgramThreePrice(dataProgram, isDelete)
                    }
                ]
            );
        }
    }
    handleSaleProgram = ({ programInfo = {}, productOrder, logo = "" }, isDelete) => {
        const { saleProgramInfo } = this.state;

        const newSaleProgramInfo = isDelete ? {
            ...saleProgramInfo,
            logo: '',
            partnerID: undefined,
            saleProgramID: 0,
            saleProgramName: '',
            isSpecial: false
        } : {
            ...saleProgramInfo,
            partnerID: programInfo.partnerInstallmentID,
            saleProgramID: programInfo.saleProgramID,
            saleProgramName: programInfo.saleProgramName,
            logo,
            isSpecial: programInfo.isSpecial
        };

        this.setState({ saleProgramInfo: newSaleProgramInfo }, this.getPromotion);
    }
    handlePackageService = (dataPackageService, productInfo, saleProgramInfo) => {
        const newProductOrder = { ...productInfo };
        const newPackageService = dataPackageService.map(item => ({
            ...item,
            TotalAmountNOVAT: item.originTotalAmountNOVAT + newProductOrder.salePrice,
            TotalAmount: item.originTotalAmount + newProductOrder.salePriceVAT
        }));

        const newSaleProgramID = saleProgramInfo.saleProgramID ?? 0;
        this.props.actionDetail.set_package_services({
            id: `${newProductOrder.productID}_${newSaleProgramID}`,
            package: newPackageService
        });

        const { NEW_STOCK, NEW_STOCK_DISCOUNT } = PRODUCT_STATUS_ID;
        const { inventoryStatusID } = this.state;

        if (helper.IsNonEmptyArray(newPackageService)) {
            const defaultPrice = newPackageService.find(item => item.IsSelected) || {};
            const finalRetailPriceVAT = defaultPrice.TotalAmount || 0;

            this.setState({
                listPackageService: newPackageService,
                defaultStatusPrice: defaultPrice.packageStatus || '',
                defaultPackagePrice: defaultPrice,
                topInset: (inventoryStatusID === NEW_STOCK || inventoryStatusID === NEW_STOCK_DISCOUNT) ? 120 : 180
            });

            return finalRetailPriceVAT;
        } else {
            this.setState({
                listPackageService: [],
                defaultStatusPrice: '',
                defaultPackagePrice: {},
                topInset: 0
            });
            return 0;
        }
    }
}

const mapStateToProps = function (state) {
    return {
        searchInfo: state.saleReducer.productSearch,
        userInfo: state.userReducer,
        dataProduct: state.detailReducer.dataProduct,
        productSearch: state.detailReducer.productSearch,
        secondStock: state.detailReducer.secondStock,
        exhibitStock: state.detailReducer.exhibitStock,
        promotion: state.detailReducer.promotion,
        salePromotion: state.detailReducer.salePromotion,
        allKeyPromotion: state.detailReducer.allKeyPromotion,
        allGroupID: state.detailReducer.allGroupID,
        allExcludeDisabled: state.detailReducer.allExcludeDisabled,
        defaultKeyPromotion: state.detailReducer.defaultKeyPromotion,
        dataFifo: state.detailReducer.dataFifo,
        dataConfig: state.detailReducer.dataConfig,
        dataFeature: state.detailReducer.dataFeature,
        dataPackage: state.detailReducer.dataPackage,

        statePromotion: state.detailReducer.statePromotion,
        stateProduct: state.detailReducer.stateProduct,
        stateInventoryTab: state.detailReducer.stateInventoryTab,
        stateSecond: state.detailReducer.stateSecond,
        stateExhibit: state.detailReducer.stateExhibit,
        stateFifo: state.detailReducer.stateFifo,
        stateConfig: state.detailReducer.stateConfig,
        stateFeature: state.detailReducer.stateFeature,
        statePackage: state.detailReducer.statePackage,

        cartRequest: state.editSaleOrderReducer.dataCartProduct,
        phoneApply: state.editSaleOrderReducer.phoneApply,
        dataFavorite: state.pouchFavorite.dataFavorite,
        saleScenarioTypeID: state.specialSaleProgramReducer.saleScenarioTypeID,
        // PROMOTION_DELIVERY
        promotionDelivery: state.detailReducer.promotionDelivery,
        salePromotionDelivery: state.detailReducer.salePromotionDelivery,
        promotionLostSale: state.detailReducer.promotionLostSale,

        packageServices: state.detailReducer.packageServices,

    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionDetail: bindActionCreators(actionDetailCreator, dispatch),
        actionPouch: bindActionCreators(actionPouchCreator, dispatch),
        actionEditSaleOrder: bindActionCreators(actionEditSaleOrderCreator, dispatch),
        actionShoppingCart: bindActionCreators(actionShoppingCartCreator, dispatch),
        loyaltyAction: bindActionCreators(actionLoyaltyCreator, dispatch)
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(AddCartProduct);

const getSaleProgramInfo = (cartRequest) => {
    const {
        SaleOrderDetails,
        CustomerPhone,
        OutputStoreID,
        DeliveryTypeID,
        SaleOrderSaleProgramInfo
    } = cartRequest;
    let isHasSaleProgram = false;
    let saleProgramInfo = {
        logo: "",
        partnerID: undefined,
        saleProgramID: 0,
        saleProgramName: ""
    };
    let deliveryInfo = {
        gender: true,
        contactPhone: "",
        contactName: "",
        contactAddress: "",
    };
    let deliveryType = DeliveryTypeID;
    let outputStoreID = OutputStoreID;
    const phoneValidate = CustomerPhone || "";
    if (!helper.IsEmptyObject(SaleOrderSaleProgramInfo)) {
        const {
            Logo,
            PartnerInstallmentID,
            SaleProgramID,
            SaleProgramName,
            IsSpecial
        } = SaleOrderSaleProgramInfo;
        saleProgramInfo = {
            logo: Logo,
            partnerID: PartnerInstallmentID,
            saleProgramID: SaleProgramID,
            saleProgramName: SaleProgramName,
            isSpecial: IsSpecial
        };
        isHasSaleProgram = true;
    }
    return {
        saleProgramInfo,
        deliveryInfo,
        phoneValidate,
        isHasSaleProgram,
        deliveryType,
        outputStoreID
    };
}

const getValueDisabledPager = (
    inventoryStatusID,
    productInfo,
    productSecondInfo,
    productExhibitInfo
) => {
    switch (inventoryStatusID) {
        case 2:
            return helper.IsEmptyObject(productSecondInfo);
        case 3:
            return helper.IsEmptyObject(productExhibitInfo);
        default:
            return helper.IsEmptyObject(productInfo);
    }
}

const ProductInfo = ({ info }) => {
    const { productName } = info;
    return (
        <View style={{
            width: constants.width,
            justifyContent: "center",
            alignItems: "center",
            paddingHorizontal: 10,
            paddingVertical: 4,
            backgroundColor: COLORS.bgF0F0F0
        }}>
            <View style={{
                width: constants.width - 20,
                height: 32,
                borderWidth: StyleSheet.hairlineWidth,
                borderColor: COLORS.bd333333,
                borderRadius: 4,
                backgroundColor: COLORS.bgFFFFFF,
                justifyContent: "center",
                alignItems: "center",
                paddingHorizontal: 8
            }}>
                <MyText style={{
                    color: COLORS.txt147EFB,
                    fontWeight: "bold"
                }}
                    numberOfLines={1}
                    text={productName || ""}
                />
            </View>
        </View>
    );
}