import React, { useState } from 'react';
import {
    // Text,
    View,
    Keyboard,
    ScrollView,
    Alert
} from 'react-native';
import { FieldInput } from "@components";
import { constants } from "@constants";
import { translate } from "@translate";
import { helper } from "@common";
import StoreInfo from "./component/StoreInfo";
import ButtonAddToCart from "../component/ButtonAddToCart";
import { COLORS } from "@styles";

const DeliveryDefault = ({
    quantity,
    quantityInStock,
    deliveryInfo,
    children,
    addToShoppingCart
}) => {
    const quantityMissing = quantity - quantityInStock;
    const isMissingQuantity = quantityMissing > 0;
    const isVisible = !helper.IsEmptyObject(deliveryInfo);
    const [contactNote, setContactNote] = useState("");
    const onAddToCart = () => {
        if (isMissingQuantity) {
            Alert.alert("", translate('editSaleOrder.not_enough_inventory'));
        }
        else {
            addToShoppingCart();
        }
    }
    return (
        isVisible &&
        <ScrollView
            keyboardShouldPersistTaps={"always"}
            contentContainerStyle={{
                flexGrow: 1,
            }}>
            <View style={{
                width: constants.width,
                alignItems: "center",
                flex: 1
            }}>

                <StoreInfo
                    deliveryInfo={deliveryInfo}
                />
                <FieldInput
                    styleInput={{
                        borderWidth: 1,
                        borderRadius: 4,
                        borderColor: COLORS.bdCCCCCC,
                        marginVertical: 10,
                        paddingHorizontal: 10,
                        backgroundColor: COLORS.bgFFFFFF,
                        justifyContent: 'center',
                        paddingVertical: 8,
                    }}
                    textAlignVertical={'center'}
                    underlineColorAndroid={'transparent'}
                    placeholder={translate('editSaleOrder.note')}
                    value={contactNote}
                    onChangeText={(text) => {
                        if (helper.isValidateCharVN(text)) {
                            setContactNote(text);
                        }
                    }}
                    returnKeyType={"default"}
                    blurOnSubmit={true}
                    onSubmitEditing={() => { Keyboard.dismiss() }}
                    width={constants.width - 20}
                    multiline={true}
                    height={40}
                    clearText={() => {
                        setContactNote("");
                    }}
                    maxLength={500}
                />
                {
                    // PROMOTION_DELIVERY
                    children
                }
                <ButtonAddToCart
                    onAddToCart={addToShoppingCart}
                    disabled={false}
                />
            </View>
        </ScrollView>
    );
}

export default DeliveryDefault;