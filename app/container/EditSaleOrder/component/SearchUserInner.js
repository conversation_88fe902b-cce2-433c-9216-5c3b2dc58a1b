import React, { useEffect, useState } from 'react';
import {
    View,
    Text,
    TextInput,
    FlatList,
    TouchableOpacity,
    StyleSheet,
    Button,
    Alert
} from 'react-native';
import { searchUserPos } from '../action';
import { translate } from '@translate';
import { hideBlockUI, showBlockUI } from '@components';

const SearchUserInner = ({ handleSelectUser }) => {
    const [keyword, setKeyword] = useState('');
    const [results, setResults] = useState([]);

    const handleSearchUser = async () => {
        try {
            showBlockUI()
            const response = await searchUserPos(keyword);
            hideBlockUI()
            setResults(response);
            setKeyword("")
        } catch (msgError) {
            Alert.alert(translate('common.notification_uppercase'), msgError, [
                {
                    text: translate('editSaleOrder.btn_skip_uppercase'),
                    onPress: () => {
                        hideBlockUI()
                        setResults([])
                    }
                },
                {
                    text: translate('editSaleOrder.btn_retry_uppercase'),
                    onPress: handleSearchUser
                }
            ]);
        }
    };



    return (
        <View style={styles.container}>
            <View style={styles.searchContainer}>
                <TextInput
                    placeholder="Nhập thông tin nhân viên"
                    value={keyword}
                    onChangeText={setKeyword}
                    style={styles.input}
                    onSubmitEditing={handleSearchUser}
                    returnKeyType="done"
                />
                {keyword.length > 0 && (
                    <TouchableOpacity onPress={() => setKeyword('')} style={styles.clearButton}>
                        <Text style={styles.clearText}>✕</Text>
                    </TouchableOpacity>
                )}
            </View>

            <FlatList
                data={results}
                keyExtractor={(item) => item.UserName}
                renderItem={({ item }) => (
                    <TouchableOpacity
                        style={styles.item}
                        onPress={() => handleSelectUser(item)}>
                        <Text style={styles.name}>{item.FullName}</Text>
                        <Text style={styles.username}>
                            Username: {item.UserName}
                        </Text>
                        {item.Mobi && (
                            <Text style={styles.mobi}>Phone: {item.Mobi}</Text>
                        )}
                        {item.Address && (
                            <Text style={styles.address}>
                                Address: {item.Address}
                            </Text>
                        )}
                    </TouchableOpacity>
                )}
            />
        </View>
    );
};

export default SearchUserInner;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 16,
        backgroundColor: '#F9FAFB'
    },
    title: {
        fontSize: 22,
        fontWeight: '600',
        marginBottom: 16,
        color: '#333'
    },
    input: {
        borderWidth: 1,
        borderColor: '#D1D5DB',
        borderRadius: 10,
        paddingHorizontal: 14,
        paddingVertical: 12,
        fontSize: 14,
        backgroundColor: '#FFFFFF',
        marginBottom: 12
    },
    selectedBox: {
        padding: 12,
        marginBottom: 16,
        backgroundColor: '#E0F7FA',
        borderRadius: 10,
        borderLeftWidth: 4,
        borderLeftColor: '#00BCD4'
    },
    item: {
        backgroundColor: '#FFFFFF',
        padding: 16,
        marginBottom: 12,
        borderRadius: 10,
        shadowColor: '#000',
        shadowOpacity: 0.05,
        shadowOffset: { width: 0, height: 2 },
        shadowRadius: 4,
        elevation: 2 // Android
    },
    name: {
        fontSize: 18,
        fontWeight: '600',
        marginBottom: 4,
        color: '#111'
    },
    username: {
        fontSize: 14,
        color: '#555',
        marginBottom: 2
    },
    mobi: {
        fontSize: 14,
        color: '#555',
        marginBottom: 2
    },
    address: {
        fontSize: 14,
        color: '#555'
    },
    searchContainer: {
        position: 'relative',
        marginBottom: 12,
    },
    input: {
        borderWidth: 1,
        borderColor: '#D1D5DB',
        borderRadius: 10,
        paddingHorizontal: 14,
        paddingVertical: 10,
        fontSize: 16,
        backgroundColor: '#FFFFFF',
    },
    clearButton: {
        position: 'absolute',
        right: 12,
        top: '50%',
        transform: [{ translateY: -10 }],
        backgroundColor: '#E5E7EB',
        borderRadius: 12,
        paddingHorizontal: 6,
        paddingVertical: 2,
    },
    clearText: {
        fontSize: 14,
        color: '#333',
    },
});
