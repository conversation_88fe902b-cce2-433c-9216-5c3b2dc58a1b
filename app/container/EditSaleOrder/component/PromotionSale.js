import React, { useState } from 'react';
import {
    View,
    TouchableOpacity,
    StyleSheet,
    Alert
} from 'react-native';
import {
    Icon,
    showBlockUI,
    hideBlockUI,
    MyText
} from "@components";
import { constants } from '@constants';
import { helper } from "@common";
import PromotionHeader from "./PromotionHeader";
import PromotionExpand from "./PromotionExpand";
import ModalSearchSim from "./Modal/ModalSearchSim";
import { translate } from '@translate';
import { COLORS } from "@styles";

const SaleProduct = ({
    salePromotion,
    onDelete,
    marginTop,
    onSearchSim,
    isApplyCoupon,
    applyDetailIDs,
    isShowLot,
    onShowLot
}) => {
    const {
        ProductName,
        Quantity,
        QuantityUnitName,
        SalePriceBKVAT,
        AdjustPriceTypeID,
        AdjustPrice,
        giftSaleOrders,
        BrandIDOfSIM,
        IMEI,
        SaleOrderDetailID
    } = salePromotion;
    const isSIM = !!BrandIDOfSIM;
    const isAdjust = !isApplyCoupon && (AdjustPrice != 0);
    const isNonCartPromotion = (applyDetailIDs.size == 0);
    const IsApplyTotalPromotion = isNonCartPromotion || applyDetailIDs.has(SaleOrderDetailID);
    const labelKey = constants.ADJUST_PRICE_LABEL[AdjustPriceTypeID];


    const onPressDelete = () => {
        Alert.alert("",
            `${translate('editSaleOrder.caution_delete_coupon')} "${ProductName}" ${translate('editSaleOrder.from_the_bundle_list')}`,
            [
                {
                    text: translate('common.btn_skip'),
                    style: "cancel",
                },
                {
                    text: translate('common.btn_continue'),
                    style: "default",
                    onPress: onDelete
                }
            ]
        )
    }
    return (
        <View style={{
            backgroundColor: COLORS.bgFFFFFF,
            marginTop: marginTop,
            paddingVertical: 4,
            borderWidth: StyleSheet.hairlineWidth,
            borderColor: COLORS.bdE4E4E4,
            width: constants.width - 10,
            alignSelf: "center",
            borderRadius: 4
        }}>
            <View style={{
                width: constants.width - 10,
                flexDirection: "row",
                paddingHorizontal: 10,
            }}>
                <Icon
                    iconSet={"Ionicons"}
                    name={"checkmark"}
                    size={16}
                    color={COLORS.ic288AD6}
                    style={{ marginTop: 2 }}
                />
                <MyText style={{
                    color: COLORS.txt333333,
                    width: constants.width - 84,
                }}
                    text={ProductName}
                />
            </View>
            {
                !IsApplyTotalPromotion &&
                <View style={{
                    paddingRight: 10,
                    paddingLeft: 26,
                    width: constants.width - 10,
                    justifyContent: "center",
                }}>
                    <MyText style={{
                        color: COLORS.txtFF8900,
                        fontStyle: "italic"
                    }}
                        addSize={-2}
                        text={translate('editSaleOrder.product_not_valid_for_order_promotion')}
                    />
                </View>
            }
            {
                isSIM &&
                <View style={{
                    paddingRight: 10,
                    paddingLeft: 26,
                    marginTop: 4,
                    width: constants.width - 10,
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                }}>
                    <MyText
                        style={{
                            color: COLORS.txt333333,
                        }}
                        text={`IMEI: ${IMEI}`}
                    />
                    <TouchableOpacity style={{
                        flexDirection: "row",
                        alignItems: "center"
                    }}
                        onPress={onSearchSim}
                    >
                        <Icon
                            iconSet={"MaterialIcons"}
                            name={"sim-card"}
                            color={COLORS.ic147EFB}
                            size={16}
                        />
                        <MyText
                            style={{
                                color: COLORS.txt808080,
                                color: COLORS.txt147EFB,
                                textDecorationLine: "underline"
                            }}
                            text={translate('editSaleOrder.type_IMEI_SIM')}
                        />
                    </TouchableOpacity>
                </View>
            }
            <View style={{
                paddingRight: 10,
                paddingLeft: 26,
                marginTop: 4,
                width: constants.width - 10,
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
            }}>
                <MyText
                    style={{
                        color: COLORS.txt333333,
                    }}
                    text={`${translate('editSaleOrder.quantity_full')} ${Quantity} ${QuantityUnitName}`}
                />

                {isShowLot && (
                    <TouchableOpacity
                        style={{ paddingHorizontal: 10, marginLeft: 32 }}
                        onPress={onShowLot}>
                        <MyText
                            text={translate('pharmacy.lot_date')}
                            style={{
                                color: COLORS.txt0000FF,
                                textDecorationLine: 'underline'
                            }}
                        />
                    </TouchableOpacity>
                )}

                <MyText
                    style={{
                        color: COLORS.txt333333,
                        fontWeight: "bold"
                    }}
                    text={translate('common.price')}>
                    <MyText
                        style={{
                            color: COLORS.txtD0021B,
                            fontWeight: "normal"
                        }}
                        text={helper.convertNum(SalePriceBKVAT)}
                    />
                </MyText>
            </View>
            {
                isAdjust &&
                <View style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "center",
                    paddingRight: 10,
                    paddingLeft: 26,
                    marginTop: 4,
                    width: constants.width - 10,
                }}>
                    <MyText style={{
                        color: COLORS.txt333333,
                        fontWeight: "bold"
                    }}
                        text={translate(labelKey)}
                    />

                    <MyText style={{
                        color: COLORS.txtD0021B,
                    }}
                        text={helper.convertNum(AdjustPrice)}
                    />
                </View>
            }
            {
                !!giftSaleOrders &&
                <PromotionExpand
                    giftSaleOrders={giftSaleOrders}
                    mainProduct={salePromotion}
                />
            }
            {/* <TouchableOpacity style={{
                width: 38,
                height: 24,
                top: 0,
                right: 0,
                borderBottomLeftRadius: 20,
                alignItems: 'center',
                justifyContent: "center",
                backgroundColor: COLORS.btnF5F5F5,
                position: 'absolute',
            }}
                onPress={onPressDelete}
            >
                <Icon
                    iconSet={"MaterialIcons"}
                    name={"close"}
                    color={COLORS.icD0021B}
                    size={20}
                />
            </TouchableOpacity> */}
        </View>
    );
}

const PromotionSale = ({
    saleSaleOrders,
    onDeleteSalePromotion,
    title,
    onUpdateSim,
    isApplyCoupon,
    applyDetailIDs,
    actionShoppingCart,
    onShowLot
}) => {
    const [isShowDetail, setIsShowDetail] = useState(true);
    const [isVisibleSearchSim, setIsVisibleSearchSim] = useState(false);
    const [dataPackage, setDataPackage] = useState([]);
    const [indexSim, setIndexSim] = useState(0);
    const isNonEmty = (saleSaleOrders.length > 0);

    const onShowDetail = () => {
        setIsShowDetail(!isShowDetail);
    }

    const getSimPackage = (productID, promotionListId, promotionID, index) => () => {
        showBlockUI();
        actionShoppingCart.getPackageSimPromotion({
            productID,
            promotionListId,
            promotionID
        }).then(data => {
            hideBlockUI();
            setIndexSim(index);
            setDataPackage(data);
            setIsVisibleSearchSim(true);
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'),
                msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: getSimPackage(productID, promotionListId, promotionID, index)
                    }
                ]
            );
        });
    }

    return (
        isNonEmty &&
        <View style={[{
            width: constants.width,
            backgroundColor: COLORS.bgFFFFFF,
            paddingVertical: 4,
        }]}>
            <PromotionHeader
                title={title}
                onShow={onShowDetail}
                isShowDetail={isShowDetail}
            />
            {
                isShowDetail &&
                saleSaleOrders.map((salePromotion, index) => {
                    const {
                        ProductName,
                        Quantity,
                        SaleOrderDetailID,
                        ProductID,
                        cus_IsRequiredBatchNO,
                        PromotionListID,
                        PromotionID
                    } = salePromotion;
                    const keyExtractor = `${SaleOrderDetailID} Sale${index}`;
                    const marginTop = (index != 0) ? 4 : 0;
                    const showLot =
                        cus_IsRequiredBatchNO;
                    return (<SaleProduct
                        salePromotion={salePromotion}
                        marginTop={marginTop}
                        key={keyExtractor}
                        onDelete={() => {
                            const newSaleSaleOrders = saleSaleOrders.filter(
                                (promtion, position) => position != index
                            );
                            onDeleteSalePromotion(newSaleSaleOrders);
                        }}
                        onSearchSim={getSimPackage(ProductID, PromotionListID, PromotionID, index)}
                        isApplyCoupon={isApplyCoupon}
                        applyDetailIDs={applyDetailIDs}
                        onShowLot={() =>
                            onShowLot(
                                ProductName,
                                Quantity,
                                SaleOrderDetailID
                            )
                        }
                        isShowLot={showLot}
                    />)
                })
            }
            {
                isVisibleSearchSim &&
                <ModalSearchSim
                    isVisible={isVisibleSearchSim}
                    hideModal={() => {
                        setIsVisibleSearchSim(false);
                    }}
                    dataPackage={dataPackage}
                    simInfo={saleSaleOrders[indexSim]}
                    updateSim={(promtion) => {
                        const newSaleSaleOrders = [...saleSaleOrders];
                        newSaleSaleOrders[indexSim] = promtion;
                        onUpdateSim(newSaleSaleOrders);
                        setIsVisibleSearchSim(false);
                    }}
                />
            }
        </View>
    );
}

export default PromotionSale;