
import React, { useState, useEffect, useRef } from 'react';
import {
    SafeAreaView,
    View,
    Modal,
    FlatList,
    TouchableOpacity,
    TextInput,
    Alert
} from 'react-native';
import { useSelector } from 'react-redux';
import { constants } from "@constants";
import { Icon, BaseLoading, MyText, UIIndicator } from "@components";
import { helper } from "@common";
import { searchSimPromotion, checkPackageSimPrice } from "../../../ShoppingCart/action";
import { translate, keys } from '@translate';
import { COLORS } from "@styles";

const regExpSim = new RegExp(/^[0]\d{8,9}$/);

const ModalSearchSim = ({
    isVisible,
    hideModal,
    dataPackage,
    simInfo,
    updateSim
}) => {
    const [keyWord, setKeyWord] = useState("");
    const [stateSearch, setStateSearch] = useState({
        isFetching: false,
        isEmpty: false,
        description: "",
        isError: false,
    });
    const [product, setProduct] = useState({});
    const [packageId, setPackageId] = useState(0);
    const [isFocus, setIsFocus] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const isValidateSim = regExpSim.test(keyWord)
    const { storeID, languageID, moduleID } = useSelector(
        (state) => state.userReducer
    );

    let keyChange = useRef("");


    const handleApiCheckPackageSimPrice = (sim, promotionID) => {
        // blocking
        const body = {
            loginStoreId: storeID,
            languageID,
            moduleID,
            imei: keyWord,
            ...sim,
            promotionID
        };
        setIsLoading(true);
        return checkPackageSimPrice(body)
            .then(() => {
                setIsLoading(false);
                return true;
            })
            .catch((error) => {
                setIsLoading(false);
                Alert.alert(translate(keys.common.notification), error);
                return false;
            });
    };

    const handleSelectSimPackage = async (sim, promotionID) => {
        const { packagesTypeId } = sim;
        if (packageId !== packagesTypeId) {
            const isValid = await handleApiCheckPackageSimPrice(sim, promotionID);
            if (isValid) {
                setPackageId(packagesTypeId);
            }
        }
    };

    const searchKeyWord = (barcode) => {
        searchSimPromotion({
            "imei": barcode,
            "productID": simInfo.ProductID,
            "storeID": simInfo.OutputStoreID,
        }).then(({ isEmpty, description, product }) => {
            if (keyChange.current) {
                setProduct(product);
                setStateSearch({
                    isFetching: false,
                    isEmpty: isEmpty,
                    description: description,
                    isError: false,
                });
            }
        }
        ).catch((description) => {
            setProduct({});
            setStateSearch({
                isFetching: false,
                isEmpty: false,
                description: description,
                isError: true,
            });
        });
    }

    const effectKeyWord = () => {
        if (isValidateSim) {
            setStateSearch({
                isFetching: true,
                isEmpty: false,
                description: "",
                isError: false,
            });
            searchKeyWord(keyWord);
        }
        else {
            setProduct({});
            setStateSearch({
                isFetching: false,
                isEmpty: false,
                description: "",
                isError: false,
            });
        }
    }

    // const effectPackage = () => {
    //     if (helper.IsNonEmptyArray(dataPackage)) {
    //         setPackageId(dataPackage[0].packagesTypeId);
    //     }
    // }

    useEffect(
        effectKeyWord,
        [keyWord]
    )

    // useEffect(
    //     effectPackage,
    //     [dataPackage]
    // )

    const onDoneSelect = () => {
        const isValidate = checkValidateSim();
        if (isValidate) {
            const newSimInfo = {
                ...simInfo,
                "PackagesTypeID": packageId,
                "IMEI": keyWord
            };
            updateSim(newSimInfo);
        }
    }

    const checkValidateSim = () => {
        if (!isValidateSim) {
            return false;
        }
        if (helper.IsEmptyObject(product)) {
            return false;
        }
        if (packageId == 0) {
            return false;
        }
        return true;
    }

    const autoFocus = () => {
        setIsFocus(true);
    }

    const renderItemPackage = (ele, index) => {
        const {
            packagesTypeId,
            packagesTypeName
        } = ele;
        const isCheck = (packageId == packagesTypeId);
        return (
            <TouchableOpacity style={{
                flexDirection: "row",
                marginBottom: 10,
                width: constants.width - 20,
                justifyContent: "flex-start",
                paddingHorizontal: 8,
            }}
                key={index}
                activeOpacity={0.8}
                onPress={() => {
                    handleSelectSimPackage(ele, simInfo.PromotionID);
                }}>
                <Icon
                    iconSet={"MaterialIcons"}
                    name={isCheck ? "radio-button-on" : "radio-button-off"}
                    color={isCheck ? COLORS.icFF8900 : COLORS.ic333333}
                    size={16}
                    style={{ marginTop: 2 }}
                />
                <MyText
                    text={packagesTypeName}
                    style={{
                        color: COLORS.txt333333,
                        marginLeft: 4
                    }} />
            </TouchableOpacity>
        );
    }

    return (
        <Modal
            visible={isVisible}
            animationType={"fade"}
            onShow={autoFocus}
        >
            <View style={{
                flex: 1
            }}>
                <View style={{
                    backgroundColor: COLORS.bg00A896,
                    height: constants.heightTopSafe,
                    width: constants.width,
                }} />
                <SafeAreaView
                    style={{
                        flex: 1,
                        width: constants.width,
                        backgroundColor: COLORS.bgFFFFFF,
                    }}
                >
                    <View
                        style={{
                            width: constants.width,
                            flexDirection: "row",
                            backgroundColor: COLORS.bg00A896,
                            alignItems: "center",
                            height: 50,
                        }}
                    >
                        <TouchableOpacity
                            style={{
                                width: 34,
                                justifyContent: "center",
                                alignItems: "center",
                                height: 40,
                            }}
                            onPress={hideModal}
                        >
                            <Icon
                                iconSet={"Ionicons"}
                                name={"chevron-back"}
                                size={30}
                                color={COLORS.icFFFFFF}
                            />
                        </TouchableOpacity>
                        {
                            isFocus &&
                            <TextInput
                                placeholder={translate('editSaleOrder.text_input_IMEI_SIM')}
                                placeholderTextColor={COLORS.txtFFFFFF6}
                                style={{
                                    width: constants.width - 105,
                                    height: 40,
                                    color: COLORS.txtFFFFFF,
                                    paddingRight: 10
                                }}
                                value={keyWord}
                                onChangeText={(text) => {
                                    const regExpPhone = new RegExp(/^[0]\d{0,9}$/);
                                    const isValidate = regExpPhone.test(text) || (text == "");
                                    if (isValidate) {
                                        keyChange.current = text;
                                        setKeyWord(text);
                                    }
                                }}
                                selectionColor={COLORS.bgFFFFFF}
                                autoFocus={true}
                                keyboardType={"numeric"}
                                returnKeyType={"done"}
                            />
                        }
                        <View style={{
                            width: 1,
                            backgroundColor: COLORS.bgFFFFFF,
                            height: 30,
                        }} />
                        <TouchableOpacity
                            style={{
                                flexDirection: "row",
                                justifyContent: "center",
                                alignItems: "center",
                                width: 70,
                                height: 40,
                            }}
                            onPress={onDoneSelect}
                        >
                            <MyText
                                text={translate('editSaleOrder.txt_done')}
                                style={{
                                    color: COLORS.txtFFFFFF,
                                    fontWeight: "bold"
                                }} />
                        </TouchableOpacity>
                    </View>
                    {
                        isValidateSim &&
                        <BaseLoading
                            isLoading={stateSearch.isFetching}
                            isEmpty={stateSearch.isEmpty}
                            textLoadingError={stateSearch.description}
                            isError={stateSearch.isError}
                            onPressTryAgains={() => searchKeyWord(keyWord)}
                            content={
                                <View style={{
                                    width: constants.width,
                                    flex: 1,
                                    paddingHorizontal: 10,
                                }}>
                                    <MyText
                                        text={translate('editSaleOrder.select_package')}
                                        style={{
                                            color: COLORS.txtEA1D5D,
                                            width: constants.width,
                                            marginVertical: 10,
                                            fontWeight: "bold"
                                        }} />
                                    <FlatList
                                        data={dataPackage}
                                        renderItem={({ item, index }) => renderItemPackage(item, index)}
                                        keyExtractor={(item, index) => index.toString()}
                                        showsVerticalScrollIndicator={false}
                                        showsHorizontalScrollIndicator={false}
                                        removeClippedSubviews={true}
                                        keyboardShouldPersistTaps={"always"}
                                    />
                                </View>
                            }
                        />
                    }
                </SafeAreaView>
            </View>
            <UIIndicator isVisible={isLoading} />
        </Modal>
    );
}

export default ModalSearchSim;

