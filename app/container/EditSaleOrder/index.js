import React, { Component } from 'react';
import {
    View,
    FlatList,
    StyleSheet,
    TouchableOpacity,
    Alert,
    Keyboard,
    BackHandler,
    TextInput,
    Image
} from 'react-native';
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import SafeAreaView from 'react-native-safe-area-view';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import Toast from 'react-native-toast-message';
import {
    Icon,
    Button,
    showBlockUI,
    hideBlockUI,
    TitleInput,
    MyText,
    FieldInput,
    ImageURI,
    Picker,
    BarcodeCamera,
    OTPSheet
} from "@components";
import { constants, API_CONST, STORAGE_CONST, DEVICE, CONFIG } from '@constants';
import { helper, storageHelper } from "@common";
import ProductInfo from "./component/ProductInfo";
import LockProductInfo from "./component/LockProductInfo";
import Coupon from '../ShoppingCart/component/Coupon';

import CartPromotion from "./component/CartPromotion";
import RadioGender from "./component/Radio/RadioGender";
import RadioRelation from "./component/Radio/RadioRelation";
import RadioInstallment from "./component/Radio/RadioInstallment";
import ModalDeposit from "./component/Modal/ModalDeposit";
import ModalLoyalty from "./component/Modal/ModalLoyalty";
import PopupPhone from "./component/PopupPhone";
import TitleInputMoney from "./component/TitleInputMoney";
import ModalFee from "./component/Modal/ModalFee";
import InsuranceInfo from "./component/InsuranceInfo";
import LockCoupon from "./component/LockCoupon";
import LockCartPromotion from "./component/LockCartPromotion";
import DeliveryInfo from "./component/DeliveryInfo";
import ModalCancelEP from "./component/Modal/ModalCancelEP";
import ModalCMVoucher from "./component/CreateCMVoucher/index";
import ContractInfo from "./component/ContractInfo";
import ModalProfitPromotion from "./component/Modal/ModalProfitPromotion";
import CartPromotionProfit from "./component/CartPromotionProfit";
import LockAdditionalProductInfo from "./component/LockAdditionalProductInfo";
import * as actionShoppingCartCreator from "../ShoppingCart/action";
import * as actionSaleOrderCreator from "../SaleOrderCart/action";
import * as actionPouchCreator from "../PouchRedux/action";
import * as actionEditSaleOrderCreator from "./action";
import * as actionManagerSOCreator from "../SaleOrderManager/action";
import * as actionPaymentOrderCreator from "../SaleOrderPayment/action";
import * as loyaltyActionCreator from "../Loyalty/action";
import * as actionDetailCreator from "../Detail/action"
import { translate } from '@translate';
import { COLORS } from "@styles";
import { PrescriptionScreen } from '../AnKhangPharmacy/screens';
import PrescriptionImages from './component/PrescriptionImages';
import PrescriptionQuantity from '../AnKhangPharmacy/components/PrescriptionQuantity';
import AnimationFloatingButton from '../ShoppingCart/component/AnimationFloatingButton';
import { customerInfoValidation, checkMedicineControlSpecs, checkMaxQuantity, getGender, checkApplyDiscount } from '../ShoppingCart';
import {
    validateStudentImages,
    validateStudentCouponImages,
    FILE_TYPE
} from '../ShoppingCart/component/StudentInfo/helper';
import { PackagePriceSheet } from '../Detail/Sheets';
import { BlockUI } from '../Detail';
import QuestionModal from '../ShoppingCart/component/QuestionModal';
import { PARTNER_ID, SO_TYPE, TYPE_PROFILE } from '../../constants/constants';
import CheckBoxPolicy from '../ShoppingCart/component/CheckBoxPolicy';
import ModalFees from '../Detail/component/Modal/ModalFees';
import { checkDataInvalid } from '../ShoppingCart/component/FormPromotion';
import SaleProgramInfo from '../ShoppingCart/component/SaleProgramInfo';
import OTPInner from '../ShoppingCart/component/OTPInner';
import { SearchUserSheet } from './sheets'
import SearchUserInner from './component/SearchUserInner'
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import BNPLInformation from '../ShoppingCart/component/BNPLInformation';

const { BRAND_ID_OF_SIM, CONNECTED_TYPE_SIM } = constants;


const {
    API_LOGO_PARTNER_INSTALLMENT
} = API_CONST;

class EditSaleOrder extends Component {

    constructor(props) {
        super(props);
        this.state = {
            discountCode: "",
            candidateNo: "",

            gender: null,
            customerPhone: "",
            customerName: "",
            customerAddress: "",
            taxID: "",
            contactPhone: "",
            contactName: "",
            contactAddress: "",
            note: "",

            customerIDCard: "",
            totalPrePaid: 0,
            termLoan: 0,
            isInsuranceFee: false,
            isInsuranceGood: false,
            goodInsuranceID: 0,
            paymentMonthly: 0,

            relationShipType: null,
            isCompany: false,

            isVisibleDeposit: false,
            isVisibleLoyalty: false,

            isRequirePhone: false,
            isVisibleVoucher: false,

            isHasSaleProgram: false,
            isLockCustomer: false,
            isAllowInvoice: true,
            isLockPhone: false,
            isLockName: false,
            isLockAddress: false,
            isLockTax: false,

            isVisibleCancelEP: false,
            dataReason: [],
            isVisibleCMVoucher: false,
            dataCMVoucher: {},

            contractID: "",
            dataContract: {},
            isVisibleProfit: false,
            isHadCheck: false,
            keyPromotion: new Set(),
            showDrug: false,
            isShowCustomer: false,
            isVisibleQuantity: false,
            idOriginProduct: null,
            isOriginQuantityChange: true,
            isRequireCustomerInfo: false,
            // clone state from redux, cannot find diffs between prevProps and this.props
            propsCartChange: -1,
            dataCartPromotion: [],
            errorMessageImage: "",
            lstImagePromotion: [],
            isOpenBarcode: false,
            dataPackagePrice: [],
            blockUI: false,
            isShowModalQuestion: false,
            questionList: [],
            tempSaleOrders: {},
            isSelectedPolicy: false,
            disabledPolicy: false,
            typeOTP: "",
            saleUserInfo: {},
        };
        this.isAllowTypePayment = true;
        this.dataCancelEP = {};
        this.dataInOutVoucher = {};
        this.timeoutApplyPromotion = null;
        this.timeOutAlertMessage = 0;
        this.isSeenLog = false;
        this.packagePriceSheetRef = React.createRef(null);
        this.isFirstRenderProfile = true;
        this.isChangeCustomer = React.createRef(false);
        this.onFocus = false;
        this.isValidCandidateNo = true;
        this.OTPSheetRef = React.createRef(null)
        this.SearchUserSheetRef = React.createRef(null)

    }

    componentDidMount() {
        hideBlockUI();
        // customer default
        this.getDefaultCustomerInfo();
        //addEventListener "addEventListener"
        BackHandler.addEventListener('addEventListener', this.onBackButtonPressed);
    }

    onBackButtonPressed = () => {
        return true;
    };

    componentWillUnmount() {
        // this.backHandler.remove();
        BackHandler.removeEventListener('hardwareBackPress', this.onBackButtonPressed);
        this.props.actionShoppingCart.resetCartPromotion();
    }

    componentDidUpdate(preProps, preState) {
        if (
            (this.props.cartChange !== this.state.propsCartChange ||
                this.state.isRequireCustomerInfo !== preState.isRequireCustomerInfo) &&
            this.props.dataShoppingCart.IsSOAnKhang
        ) {
            const {
                SaleOrderDetails: mainProducts,
                giftShoppingCartSaleOrders: giftProducts,
                saleShoppingCartSaleOrders: saleProducts
            } = this.props.dataShoppingCart;
            const hasSpecialSpecsMainProduct = checkMedicineControlSpecs(mainProducts);
            const hasSpecialSpecsGiftProduct = checkMedicineControlSpecs(giftProducts);
            const hasSpecialSpecsSaleProduct = checkMedicineControlSpecs(saleProducts);
            const isRequireCustomerInfo =
                hasSpecialSpecsMainProduct ||
                hasSpecialSpecsGiftProduct ||
                hasSpecialSpecsSaleProduct;

            this.setState((prevState) => {
                return {
                    ...prevState,
                    isRequireCustomerInfo,
                    propsCartChange: this.props.cartChange
                };
            });
        }
        if (preState.customerPhone !== this.state.customerPhone) {
            this.checkGiftVoucher();
        }
        if (preProps.cartPromotion !== this.props.cartPromotion) {
            const { cartPromotion } = this.props;
            if (helper.IsNonEmptyArray(cartPromotion)) {
                this.setState((prevState) => ({
                    ...prevState,
                    isVisibleVoucher: false,
                    keyPromotion: new Set()
                }), this.checkGiftVoucher);
            }
        }
        if (preProps.dataShoppingCart !== this.props.dataShoppingCart) {
            const { CustomerPhone, applyPhone } = this.props.dataShoppingCart;
            if (this.state.customerPhone != CustomerPhone) {
                this.isFirstRenderProfile = true;
                this.setState({ customerPhone: CustomerPhone }, () => {
                    this.handleAPIGetCustomerProfile(CustomerPhone);
                    if (CustomerPhone !== applyPhone) {
                        this.isChangeCustomer.current = true;
                    }


                });
            }
            const { dataShoppingCart } = this.props;
            const { SaleOrderDetails } = dataShoppingCart;
            const isCartNonEmpty = helper.IsNonEmptyArray(SaleOrderDetails);
            if (isCartNonEmpty) {
                this.updateCartInfo(dataShoppingCart);
            }
        }
        this.handleChangeValueProfiles({ preProps, preState });

    }

    handleChangeValueProfiles = ({ preProps, preState }) => {
        const { customerConfirmPolicy } = this.props;
        if (!helper.IsNonEmptyArray(customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]) || this.isFirstRenderProfile) return;

        const { isChange, oldInfoCustomer, infoCustomer } = this.handleGetValueChange({ preProps, preState });
        if (isChange) {
            const {
                isSigned,
            } = customerConfirmPolicy?.[TYPE_PROFILE.CUSTOMER]?.[0] ?? {};
            const hasChangeValueProfile = helper.checkChangeValueOfPrototype(infoCustomer, oldInfoCustomer);
            if (hasChangeValueProfile) {
                this.setState({
                    isSelectedPolicy: false,
                    disabledPolicy: false
                });

            }
            else {
                this.setState({
                    isSelectedPolicy: isSigned,
                    disabledPolicy: isSigned == 1 ? true : false
                });

            }

        }
        if (preState.isCompany !== this.state.isCompany && !this.state.isCompany) {
            delete this.props.customerConfirmPolicy?.[TYPE_PROFILE.COMPANY];
        }
    };
    handleGetValueChange = ({ preState, preProps }) => {
        const {
            isCompany,
            customerName,
            customerPhone,
            gender,
            contactName,
            contactPhone,
            customerIDCard
        } = this.state;
        const { customerConfirmPolicy } = this.props;
        let isChange = false;
        let infoCustomer = {};
        let oldInfoCustomer = {
            "customerName": customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]?.[0]?.customerName,
            "customerPhone": customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]?.[0]?.phoneNumber,
            "gender": customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]?.[0]?.gender,
            "cardCustomerId": customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]?.[0]?.cardCustomerId
        };
        if (!helper.IsNonEmptyArray(customerConfirmPolicy[TYPE_PROFILE.CUSTOMER])) return {
            isChange,
            infoCustomer,
            oldInfoCustomer
        };

        if (
            !isCompany &&
            (preState.customerPhone !== customerPhone ||
                preState.customerName !== customerName ||
                preState.gender !== gender ||
                preState.customerIDCard !== customerIDCard)) {

            return {
                isChange: true,
                infoCustomer: {
                    "customerName": customerName,
                    "customerPhone": customerPhone,
                    "gender": gender,
                    "cardCustomerId": customerIDCard
                },
                oldInfoCustomer
            };
        }
        else if (
            isCompany &&
            (preState.contactPhone != contactPhone ||
                preState.contactName != contactName ||
                preState.gender != gender ||
                preState.customerIDCard !== customerIDCard)) {
            console.log("Please select", contactPhone);
            return {
                isChange: true,
                infoCustomer: {
                    "customerName": contactName,
                    "customerPhone": contactPhone,
                    "gender": gender,
                    "cardCustomerId": customerIDCard
                },
                oldInfoCustomer
            };
        }
        return {
            isChange,
            infoCustomer,
            oldInfoCustomer
        };
    };

    onShowCustomerInfo = () => {
        const { isShowCustomer } = this.state;
        const { actionEditSaleOrder, dataShoppingCart } = this.props;
        if (!this.isSeenLog && !isShowCustomer) {
            showBlockUI();
            actionEditSaleOrder.logSeenCustomerInfo(dataShoppingCart.SaleOrderID).then(success => {
                this.isSeenLog = true;
                hideBlockUI();
                this.setState({ isShowCustomer: true });
            }).catch(error => {
                Alert.alert("Không thể xem thông tin", error.msgError, [{
                    text: "OK",
                    onPress: hideBlockUI
                }]);
            });
        }
        else {
            this.setState({ isShowCustomer: !isShowCustomer });
        }
    };

    renderCustomerInfo = (isVisibleContact, disabled) => {
        const {
            gender,
            customerPhone,
            customerName,
            customerAddress,
            taxID,
            contactPhone,
            contactName,
            contactAddress,
            isCompany,
            isLockCustomer,
            isAllowInvoice,
            isLockPhone,
            isLockName,
            isLockAddress,
            isLockTax,
            customerIDCard,
            isHasSaleProgram,
            isShowCustomer,
            isRequireCustomerInfo,
            relationShipType,
            saleUserInfo
        } = this.state;
        const { dataShoppingCart, wowPoints } = this.props;
        const saleProgramInfo = getSaleProgramInfo(dataShoppingCart);
        const {
            SaleOrderDetails,
            IsSOAnKhang,
            EditFieldNames = {},
            StaffUser,
            StaffFullName
        } = dataShoppingCart || {};
        const { StaffUser: StaffUserFromEditField = false } = EditFieldNames ?? {};
        const isVisibleIDNumber = isExistVina(SaleOrderDetails);
        const isHadVinaSim = checkHadVinaSim(SaleOrderDetails);
        const isHadViettelSim = checkHadViettelSim(SaleOrderDetails);
        const isCheckVisibleCardID = (isVisibleIDNumber || isHadVinaSim) && !isHasSaleProgram;
        const isVisibleChangeType = (!isLockCustomer && !isLockTax && isAllowInvoice);
        const isVisibleCustomer = isShowCustomer;
        const titleUser = !!saleUserInfo.UserName ? `${saleUserInfo.UserName} - ${saleUserInfo.FullName}` : `${StaffUser} - ${StaffFullName}` || ""
        return (
            <View style={{
                width: constants.width,
                alignItems: "center",
                paddingBottom: 2
            }}>
                {StaffUserFromEditField && <View
                    style={{
                        alignItems: 'center',
                        backgroundColor: COLORS.btn5482AB,
                        height: 40,
                        paddingHorizontal: 10,
                        marginBottom: 8
                    }}>
                    <Button
                        text={"NVBH: " + titleUser}
                        onPress={() => { this.SearchUserSheetRef.current?.present() }}
                        styleContainer={{
                            flexDirection: 'row',
                            width: constants.width,
                            height: 40,
                            justifyContent: 'space-between',
                            width: constants.width - 10
                        }}
                        styleText={{
                            color: COLORS.txtFFFFFF,
                            fontSize: 14,
                            marginRight: 8,
                            fontWeight: 'bold'
                        }}
                        iconRight={{
                            iconSet: 'Feather',
                            name: "edit",
                            size: 20,
                            color: COLORS.icFFFFBC
                        }}
                    />
                </View>}

                <View
                    style={{
                        alignItems: 'center',
                        backgroundColor: COLORS.btn5B9A68,
                        height: 40,
                        paddingHorizontal: 10
                    }}>
                    <Button
                        text={translate('shoppingCart.customer_info')}
                        onPress={this.onShowCustomerInfo}
                        styleContainer={{
                            flexDirection: 'row',
                            width: constants.width,
                            height: 40,
                            justifyContent: 'space-between',
                            width: constants.width - 10
                        }}
                        styleText={{
                            color: COLORS.txtFFFFFF,
                            fontSize: 14,
                            marginRight: 8,
                            fontWeight: 'bold'
                        }}
                        iconRight={{
                            iconSet: 'Ionicons',
                            name: isShowCustomer ? 'eye-outline' : 'eye-off-outline',
                            size: 20,
                            color: COLORS.icFFFFBC
                        }}
                    />
                </View>
                {
                    isVisibleCustomer ?
                        <View style={{
                            width: constants.width,
                            alignItems: "center",
                        }}>
                            <View
                                pointerEvents={disabled ? "none" : null}
                                style={{
                                    width: constants.width,
                                    alignItems: "center",
                                }}
                            >
                                {
                                    isVisibleChangeType &&
                                    <TouchableOpacity style={{
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        marginTop: 10,
                                        marginHorizontal: 10,
                                        alignSelf: "flex-start"
                                    }}
                                        onPress={this.onChangTypeCustomer(!isCompany)}
                                    >
                                        <Icon
                                            iconSet={"Ionicons"}
                                            name={
                                                isCompany
                                                    ? "ios-checkbox-outline"
                                                    : "ios-square-outline"
                                            }
                                            color={isCompany ? COLORS.icF89000 : COLORS.ic147EFB}
                                            size={16}
                                        />
                                        <MyText
                                            style={{
                                                color: isCompany ? COLORS.icF89000 : COLORS.txt147EFB,
                                                fontWeight: 'bold',
                                                marginLeft: 5
                                            }}
                                            text={translate('editSaleOrder.customer_print_company_bill')}
                                        />
                                    </TouchableOpacity>
                                }
                                {
                                    isCompany
                                        ? <View style={{
                                            width: constants.width - 20,
                                            marginTop: 10,
                                        }}>
                                            <TitleInput
                                                title={translate('editSaleOrder.text_input_tax_id')}
                                                isRequired={true}
                                                styleInput={{
                                                    borderWidth: 1,
                                                    borderRadius: 4,
                                                    borderColor: COLORS.bdCCCCCC,
                                                    marginBottom: 5,
                                                    paddingHorizontal: 10,
                                                    paddingVertical: 8,
                                                    backgroundColor: isLockTax ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                                                }}
                                                placeholder={translate('editSaleOrder.placeholder_customer_tax')}
                                                value={taxID}
                                                onChangeText={(text) => {
                                                    const regExpTax = new RegExp(/^[0-9-]{0,14}$/);
                                                    const isValidate = regExpTax.test(text) || (text == "");
                                                    if (isValidate) {
                                                        this.setState({ taxID: text });
                                                    }
                                                }}
                                                keyboardType={DEVICE.isIOS ? "numbers-and-punctuation" : "numeric"}
                                                returnKeyType={"done"}
                                                blurOnSubmit={true}
                                                // onBlur={() => this.getCompanyInfo(taxID)}
                                                onBlur={() => this.getCompanyProfile(taxID)}
                                                width={constants.width - 20}
                                                height={40}
                                                clearText={() => {
                                                    this.setState({ taxID: "" });
                                                }}
                                                editable={!isLockTax}
                                            />

                                            <TitleInput
                                                title={translate('editSaleOrder.text_input_customer_company_name')}
                                                isRequired={true}
                                                styleInput={{
                                                    borderWidth: 1,
                                                    borderRadius: 4,
                                                    borderColor: COLORS.bdCCCCCC,
                                                    marginBottom: 5,
                                                    paddingHorizontal: 10,
                                                    justifyContent: 'center',
                                                    paddingVertical: 8,
                                                    backgroundColor: isLockName ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                                                }}
                                                textAlignVertical={'center'}
                                                underlineColorAndroid={'transparent'}
                                                placeholder={"Nhập tên công ty"}
                                                value={customerName}
                                                onChangeText={(text) => {
                                                    if (helper.isValidateCharVN(text)) {
                                                        this.setState({ customerName: text });
                                                    }
                                                }}
                                                keyboardType={"default"}
                                                returnKeyType={"done"}
                                                blurOnSubmit={true}
                                                onSubmitEditing={Keyboard.dismiss}
                                                width={constants.width - 20}
                                                multiline={true}
                                                height={40}
                                                clearText={() => {
                                                    this.setState({ customerName: "" });
                                                }}
                                                editable={!isLockName}
                                                key={"companyName"}
                                                maxLength={300}
                                            />

                                            <TitleInput
                                                title={translate('editSaleOrder.text_input_customer_company_address')}
                                                isRequired={true}
                                                styleInput={{
                                                    borderWidth: 1,
                                                    borderRadius: 4,
                                                    borderColor: COLORS.bdCCCCCC,
                                                    marginBottom: 5,
                                                    paddingHorizontal: 10,
                                                    justifyContent: 'center',
                                                    paddingVertical: 8,
                                                    backgroundColor: isLockAddress ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                                                }}
                                                textAlignVertical={'center'}
                                                underlineColorAndroid={'transparent'}
                                                placeholder={"Nhập địa chỉ"}
                                                value={customerAddress}
                                                onChangeText={(text) => {
                                                    if (helper.isValidateCharVN(text)) {
                                                        this.setState({ customerAddress: text });
                                                    }
                                                }}
                                                keyboardType={"default"}
                                                returnKeyType={"done"}
                                                blurOnSubmit={true}
                                                onSubmitEditing={Keyboard.dismiss}
                                                width={constants.width - 20}
                                                multiline={true}
                                                height={40}
                                                clearText={() => {
                                                    this.setState({ customerAddress: "" });
                                                }}
                                                editable={!isLockAddress}
                                                key={"companyAddress"}
                                                maxLength={300}
                                            />

                                            {/* <TitleInput
                                            title={translate('editSaleOrder.text_input_customer_company_phone')}
                                            // isRequired={true}
                                            styleInput={{
                                                borderWidth: 1,
                                                borderRadius: 4,
                                                borderColor: COLORS.bdCCCCCC,
                                                marginBottom: 5,
                                                paddingHorizontal: 10,
                                                paddingVertical: 8,
                                                backgroundColor: isLockPhone ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                                            }}
                                            placeholder={translate('editSaleOrder.placeholder_phone_number')}
                                            value={customerPhone}
                                            onChangeText={(text) => {
                                                let regExpPhone = new RegExp(/^[0]\d{0,9}$/);
                                                const isValidate = regExpPhone.test(text) || (text == "");
                                                if (isValidate) {
                                                    this.setState({
                                                        customerPhone: text,
                                                        // contactPhone: text
                                                    });
                                                }
                                            }}
                                            keyboardType={"numeric"}
                                            returnKeyType={"done"}
                                            blurOnSubmit={true}
                                            onBlur={this.getContactInfo(contactPhone)}
                                            width={constants.width - 20}
                                            height={40}
                                            clearText={() => {
                                                this.setState({ customerPhone: "" });
                                            }}
                                            editable={!isLockPhone}
                                            key={"companyPhone"}
                                        /> */}
                                            {
                                                isVisibleContact &&
                                                <View style={{
                                                    width: constants.width - 20,
                                                }}>
                                                    <View style={{
                                                        width: constants.width - 20,
                                                        flexDirection: "row",
                                                        marginBottom: 4,
                                                        justifyContent: "space-between"
                                                    }}>
                                                        <RadioGender
                                                            gender={gender}
                                                            onSwitchGender={(value) => {
                                                                this.setState({ gender: value });
                                                            }}
                                                        />
                                                        <TouchableOpacity style={{
                                                            justifyContent: "center",
                                                            alignItems: "center",
                                                        }}
                                                            onPress={this.getOldCompanyInfo}
                                                        >
                                                            <MyText
                                                                style={{
                                                                    color: COLORS.txtFFA500,
                                                                    textDecorationLine: 'underline',
                                                                    fontWeight: 'bold'
                                                                }}
                                                                text={translate('editSaleOrder.old_customer')}
                                                            />
                                                        </TouchableOpacity>
                                                    </View>

                                                    <TitleInput
                                                        title={translate('editSaleOrder.text_input_buyer_phone')}
                                                        styleInput={{
                                                            borderWidth: 1,
                                                            borderRadius: 4,
                                                            borderColor: COLORS.bdCCCCCC,
                                                            marginBottom: 5,
                                                            paddingHorizontal: 10,
                                                            backgroundColor: isLockPhone ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                                                            paddingVertical: 8,
                                                        }}
                                                        placeholder={translate('editSaleOrder.text_input_phone_number')}
                                                        value={contactPhone}
                                                        onChangeText={(text) => {
                                                            const regExpPhone = new RegExp(/^[0]\d{0,9}$/);
                                                            const isValidate = regExpPhone.test(text) || (text == "");
                                                            if (isValidate) {
                                                                this.setState({ contactPhone: text, customerPhone: text });
                                                            }
                                                        }}
                                                        keyboardType={"numeric"}
                                                        returnKeyType={"done"}
                                                        blurOnSubmit={true}
                                                        // onBlur={this.getContactInfo(contactPhone)}
                                                        onBlur={() => {
                                                            this.handleAPIGetCustomerProfile(contactPhone);
                                                        }}
                                                        width={constants.width - 20}
                                                        height={40}
                                                        clearText={() => {
                                                            this.setState({ contactPhone: "" });
                                                        }}
                                                        key={"contactPhone"}
                                                        editable={!isLockPhone}
                                                    />

                                                    <TitleInput
                                                        title={translate('editSaleOrder.text_input_buyer_name')}
                                                        styleInput={{
                                                            borderWidth: 1,
                                                            borderRadius: 4,
                                                            borderColor: COLORS.bdCCCCCC,
                                                            marginBottom: 5,
                                                            paddingHorizontal: 10,
                                                            backgroundColor: COLORS.bgFFFFFF,
                                                            paddingVertical: 8
                                                        }}
                                                        placeholder={"Nhập họ tên"}
                                                        value={contactName}
                                                        onChangeText={(text) => {
                                                            if (helper.isValidateCharVN(text)) {
                                                                this.setState({ contactName: text });
                                                            }
                                                        }}
                                                        keyboardType={"default"}
                                                        returnKeyType={"done"}
                                                        blurOnSubmit={true}
                                                        onSubmitEditing={Keyboard.dismiss}
                                                        width={constants.width - 20}
                                                        height={40}
                                                        clearText={() => {
                                                            this.setState({ contactName: "" });
                                                        }}
                                                        key={"contactName"}
                                                        maxLength={50}
                                                    />

                                                    {/* <TitleInput
                                                    title={translate('editSaleOrder.text_input_buyer_address')}
                                                    styleInput={{
                                                        borderWidth: 1,
                                                        borderRadius: 4,
                                                        borderColor: COLORS.bdCCCCCC,
                                                        marginBottom: 5,
                                                        paddingHorizontal: 10,
                                                        backgroundColor: COLORS.bgFFFFFF,
                                                        justifyContent: 'center',
                                                        paddingVertical: 8
                                                    }}
                                                    textAlignVertical={'center'}
                                                    underlineColorAndroid={'transparent'}
                                                    placeholder={"Nhập địa chỉ"}
                                                    value={contactAddress}
                                                    onChangeText={(text) => {
                                                        if (helper.isValidateCharVN(text)) {
                                                            this.setState({ contactAddress: text });
                                                        }
                                                    }}
                                                    keyboardType={"default"}
                                                    returnKeyType={"done"}
                                                    blurOnSubmit={true}
                                                    onSubmitEditing={Keyboard.dismiss}
                                                    width={constants.width - 20}
                                                    multiline={true}
                                                    height={40}
                                                    clearText={() => {
                                                        this.setState({ contactAddress: "" });
                                                    }}
                                                    key={"contactAddress"}
                                                    maxLength={300}
                                                /> */}
                                                </View>
                                            }
                                        </View>
                                        : <View style={{
                                            width: constants.width - 20,
                                            marginTop: 10,
                                        }}>
                                            <View style={{
                                                width: constants.width - 20,
                                                flexDirection: "row",
                                                marginBottom: 4,
                                                justifyContent: "space-between"
                                            }}>
                                                <RadioGender
                                                    gender={gender}
                                                    onSwitchGender={(value) => {
                                                        this.setState({ gender: value });
                                                    }}
                                                />
                                                <TouchableOpacity style={{
                                                    justifyContent: "center",
                                                    alignItems: "center",
                                                }}
                                                    onPress={this.getOldCustomerInfo}
                                                >
                                                    <MyText
                                                        style={{
                                                            color: COLORS.txtFFA500,
                                                            textDecorationLine: 'underline',
                                                            fontWeight: 'bold'
                                                        }}
                                                        text={translate('editSaleOrder.old_customer')}
                                                    />
                                                </TouchableOpacity>
                                            </View>

                                            <TitleInput
                                                title={translate('editSaleOrder.text_input_phone')}
                                                isRequired={isRequireCustomerInfo}
                                                styleInput={{
                                                    borderWidth: 1,
                                                    borderRadius: 4,
                                                    borderColor: COLORS.bdCCCCCC,
                                                    marginBottom: 5,
                                                    paddingHorizontal: 10,
                                                    paddingVertical: 8,
                                                    backgroundColor: isLockPhone ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                                                }}
                                                placeholder={translate('editSaleOrder.placeholder_phone_number')}
                                                value={customerPhone}
                                                onChangeText={(text) => {
                                                    const regExpPhone = new RegExp(/^[0]\d{0,9}$/);
                                                    const isValidate = regExpPhone.test(text) || (text == "");
                                                    if (isValidate) {
                                                        this.setState({ customerPhone: text });
                                                    }
                                                }}
                                                keyboardType={"numeric"}
                                                returnKeyType={"done"}
                                                blurOnSubmit={true}
                                                // onBlur={() => this.getCustomerInfo(customerPhone)}
                                                onBlur={() => this.handleAPIGetCustomerProfile(customerPhone)}
                                                width={constants.width - 20}
                                                height={40}
                                                clearText={() => {
                                                    this.setState({ customerPhone: "" });
                                                }}
                                                editable={!isLockPhone}
                                                alertMessage={wowPoints.message}
                                                onTooltipClose={() => {
                                                    if (this.timeOutAlertMessage) {
                                                        clearTimeout(this.timeOutAlertMessage);
                                                    }
                                                    this.props.loyaltyAction.setWowPointsMessage({
                                                        phoneNumber: '',
                                                        message: ''
                                                    });
                                                }}
                                                key={"customerPhone"}
                                            />

                                            <TitleInput
                                                title={translate('editSaleOrder.text_input_name_2')}
                                                isRequired={true}
                                                styleInput={{
                                                    borderWidth: 1,
                                                    borderRadius: 4,
                                                    borderColor: COLORS.bdCCCCCC,
                                                    marginBottom: 5,
                                                    paddingHorizontal: 10,
                                                    paddingVertical: 8,
                                                    backgroundColor: isLockName ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                                                }}
                                                placeholder={"Nhập họ tên"}
                                                value={customerName}
                                                onChangeText={(text) => {
                                                    if (helper.isValidateCharVN(text)) {
                                                        this.setState({ customerName: text });
                                                    }
                                                }}
                                                keyboardType={"default"}
                                                returnKeyType={"done"}
                                                blurOnSubmit={true}
                                                onSubmitEditing={Keyboard.dismiss}
                                                width={constants.width - 20}
                                                height={40}
                                                clearText={() => {
                                                    this.setState({ customerName: "" });
                                                }}
                                                editable={!isLockName}
                                                key={"customerName"}
                                                maxLength={50}
                                            />
                                            {/* Nhập căn cước công dân */}
                                            {
                                                isCheckVisibleCardID &&
                                                <View>
                                                    <MyText
                                                        text={translate('shoppingCart.text_input_cccd')}
                                                        addSize={-1.5}
                                                        style={{
                                                            color: COLORS.txt333333,
                                                            fontWeight: 'bold',
                                                            fontStyle: 'italic',
                                                        }}>
                                                        <MyText
                                                            text={'*'}
                                                            addSize={-1.5}
                                                            style={{
                                                                color: COLORS.txtFF0000
                                                            }}
                                                        />
                                                    </MyText>
                                                    <View
                                                        style={{
                                                            width: constants.width - 30,
                                                            flexDirection: 'row'
                                                        }}>
                                                        <TextInput
                                                            style={{
                                                                width: '70%',
                                                                borderWidth: 1,
                                                                borderRadius: 4,
                                                                borderColor: COLORS.bdCCCCCC,
                                                                marginBottom: 5,
                                                                paddingHorizontal: 10,
                                                                paddingVertical: 8,
                                                                backgroundColor: COLORS.bgFFFFFF
                                                            }}
                                                            placeholder={translate('shoppingCart.placeholder_cccd')}
                                                            value={customerIDCard}
                                                            onChangeText={(text) => {
                                                                const regExpIDCard = new RegExp(/^\d{0,12}$/);
                                                                const isValidate =
                                                                    regExpIDCard.test(text) || text == '';
                                                                if (isValidate) {
                                                                    this.setState({ customerIDCard: text, isHadCheck: false });
                                                                }
                                                            }}
                                                            keyboardType={'numeric'}
                                                            returnKeyType={'done'}
                                                            blurOnSubmit={true}
                                                            onSubmitEditing={Keyboard.dismiss}
                                                            clearText={() => {
                                                                this.setState({ customerIDCard: '' });
                                                            }}
                                                            key={'customerIDCard'}
                                                        />
                                                        <Button
                                                            text={translate('shoppingCart.btn_check')}
                                                            onPress={this.handleCheckDocument}
                                                            styleContainer={{
                                                                width: '30%',
                                                                borderRadius: 4,
                                                                backgroundColor: COLORS.txt147EFB,
                                                                height: 44,
                                                                marginLeft: 10
                                                            }}
                                                            styleText={{
                                                                color: COLORS.txtFFFFFF,
                                                                fontSize: 14,
                                                                fontWeight: 'bold'
                                                            }}
                                                        />
                                                    </View>
                                                </View>
                                            }
                                            {
                                                isHadViettelSim &&
                                                <View >
                                                    <MyText
                                                        text={translate('shoppingCart.text_input_cccd_hc')}
                                                        addSize={-1.5}
                                                        style={{
                                                            color: COLORS.txt333333,
                                                            fontWeight: 'bold',
                                                            fontStyle: 'italic',
                                                        }}>
                                                        <MyText
                                                            text={'*'}
                                                            addSize={-1.5}
                                                            style={{
                                                                color: COLORS.txtFF0000
                                                            }}
                                                        />
                                                    </MyText>
                                                    <TitleInput
                                                        styleInput={{
                                                            borderWidth: 1,
                                                            borderRadius: 4,
                                                            borderColor: COLORS.bdCCCCCC,
                                                            marginBottom: 5,
                                                            paddingHorizontal: 10,
                                                            justifyContent: 'center',
                                                            paddingVertical: 8,
                                                            marginTop: -10,
                                                            backgroundColor: COLORS.bgFFFFFF,
                                                            width: '100%',

                                                        }}
                                                        width={constants.width - 20}
                                                        multiline={true}
                                                        height={40}
                                                        placeholder={translate('shoppingCart.placeholder_cccd_hc')}
                                                        value={customerIDCard}
                                                        onChangeText={(text) => {
                                                            const regExpIDCard = new RegExp(/^\w{0,12}$/);
                                                            const isValidate =
                                                                regExpIDCard.test(text) || text == '';
                                                            if (isValidate) {
                                                                this.setState({ customerIDCard: text, isHadCheck: true });
                                                            }
                                                        }}
                                                        keyboardType={'numeric'}
                                                        returnKeyType={'done'}
                                                        blurOnSubmit={true}
                                                        onSubmitEditing={Keyboard.dismiss}
                                                        clearText={() => {
                                                            this.setState({ customerIDCard: '' });
                                                        }}
                                                        key={'customerIDCard'}
                                                    />
                                                </View>}
                                            {/* <TitleInput
                                            title={translate('editSaleOrder.text_input_address_2')}
                                            isRequired={isRequireCustomerInfo}
                                            styleInput={{
                                                borderWidth: 1,
                                                borderRadius: 4,
                                                borderColor: COLORS.bdCCCCCC,
                                                marginBottom: 5,
                                                paddingHorizontal: 10,
                                                justifyContent: 'center',
                                                paddingVertical: 8,
                                                backgroundColor: isLockAddress ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                                            }}
                                            textAlignVertical={'center'}
                                            underlineColorAndroid={'transparent'}
                                            placeholder={"Nhập địa chỉ"}
                                            value={customerAddress}
                                            onChangeText={(text) => {
                                                if (helper.isValidateCharVN(text)) {
                                                    this.setState({ customerAddress: text });
                                                }
                                            }}
                                            keyboardType={"default"}
                                            returnKeyType={"done"}
                                            blurOnSubmit={true}
                                            onSubmitEditing={Keyboard.dismiss}
                                            width={constants.width - 20}
                                            multiline={true}
                                            height={40}
                                            clearText={() => {
                                                this.setState({ customerAddress: "" });
                                            }}
                                            editable={!isLockAddress}
                                            key={"customerAddress"}
                                            maxLength={300}
                                        /> */}
                                        </View>
                                }
                                {this.renderCustomerIDCard(dataShoppingCart, saleProgramInfo)}
                            </View>
                            {this.renderAttachmentCoupon(dataShoppingCart)}
                        </View>
                        :
                        <View pointerEvents={disabled ? "none" : null}
                            style={{
                                width: constants.width,
                                alignItems: "center",
                                paddingTop: 7
                            }}>
                            <TitleInput
                                title={isCompany ? translate('editSaleOrder.text_input_customer_company_name') : translate('editSaleOrder.text_input_name_2')}
                                isRequired={true}
                                styleInput={{
                                    borderWidth: 1,
                                    borderRadius: 4,
                                    borderColor: COLORS.bdCCCCCC,
                                    marginBottom: 5,
                                    paddingHorizontal: 10,
                                    paddingVertical: 8,
                                    backgroundColor: isLockName ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                                }}
                                placeholder={"Nhập họ tên"}
                                value={customerName}
                                onChangeText={(text) => {
                                    if (helper.isValidateCharVN(text)) {
                                        this.setState({ customerName: text });
                                    }
                                }}
                                keyboardType={"default"}
                                returnKeyType={"done"}
                                blurOnSubmit={true}
                                onSubmitEditing={Keyboard.dismiss}
                                width={constants.width - 20}
                                height={40}
                                clearText={() => {
                                    this.setState({ customerName: "" });
                                }}
                                editable={!isLockName}
                                key={"customerName"}
                                maxLength={50}
                            />
                        </View>
                }

                <View style={{
                    width: constants.width,
                    alignItems: "center",
                }}
                    pointerEvents={disabled ? "none" : null}
                >
                    {this.renderInstallment(dataShoppingCart, saleProgramInfo, disabled)}
                    {this.renderContractInfo(dataShoppingCart, saleProgramInfo, disabled)}
                </View>
            </View>
        );
    };

    renderAttachmentCoupon = (dataShoppingCart) => {
        const { candidateNo } = this.state;
        const {
            cus_IsRequireAttachmentSMSPromotion,
            cus_IsPromotionType19Exist,
            cus_IsRequiredCandidateInput,
            cus_IsDisplayCandidateInput
        } = dataShoppingCart;
        return cus_IsRequireAttachmentSMSPromotion &&
            <View style={{
                width: constants.width,
                alignItems: 'center'
            }}>
                {!cus_IsPromotionType19Exist && <View style={{
                    width: constants.width - 20,
                    flexDirection: "row",
                    alignItems: "flex-end",
                    justifyContent: "space-between"
                }}>
                    <View style={{ width: 120 }} />
                    <MyText
                        addSize={-2}
                        style={{
                            color: COLORS.txtEB1478,
                            width: constants.width - 140,
                            textDecorationLine: 'underline',
                            fontWeight: 'bold',
                            textAlign: 'right'
                        }}
                        onPress={() => this.getImage(dataShoppingCart)}
                        text={translate('editSaleOrder.take_ID_and_student_card_pictures')}
                    />
                </View>}
                {cus_IsDisplayCandidateInput && <TitleInput
                    title={"Số báo danh:"}
                    isRequired={cus_IsRequiredCandidateInput}
                    styleInput={{
                        borderWidth: 1,
                        borderRadius: 4,
                        borderColor: COLORS.bdCCCCCC,
                        marginBottom: 5,
                        paddingHorizontal: 10,
                        paddingVertical: 8,
                        backgroundColor: COLORS.bgFFFFFF,
                    }}
                    placeholder={"Nhập số báo danh"}
                    value={candidateNo}
                    onChangeText={(text) => {
                        if (helper.isValidateCharVN(text)) {
                            this.isValidCandidateNo = false;
                            this.setState({ candidateNo: text });
                        }
                    }}
                    keyboardType={"default"}
                    returnKeyType={"done"}
                    blurOnSubmit={true}
                    width={constants.width - 20}
                    height={40}
                    clearText={() => {
                        this.setState({ candidateNo: "" });
                    }}
                    key={"customerName"}
                    maxLength={50}
                    onFocus={() => {
                        this.onFocus = true
                    }}
                    onBlur={() => {
                        this.onFocus = false
                        this.checkCandidateNo();
                    }}
                />}
            </View>;
    };

    renderCustomerIDCard = (dataShoppingCart, saleProgramInfo) => {
        const { customerIDCard, isHasSaleProgram } = this.state;
        const {
            SaleOrderCusPromotion,
            EPOSTransactionID,
            IsCreateSO,
            IsSaleProgaramNotExist,
            SaleOrderDetails,
            cus_UrlFilesShoppingCart
        } = dataShoppingCart;
        const { IsCardPartner } = saleProgramInfo;
        const isRequired = !IsCardPartner;
        const isSupImages = helper.hasProperty(SaleOrderCusPromotion, "UrlFiles");
        const isVisble = isHasSaleProgram || isSupImages;
        const isHasEP = helper.IsNonEmptyString(EPOSTransactionID);
        const isCheckedCardID = isSupImages && !IsCreateSO;
        const disabled = isHasEP || isCheckedCardID;
        const isVisibleIDNumber = isExistVina(SaleOrderDetails);
        const isHadVinaSim = checkHadVinaSim(SaleOrderDetails);
        const isHadViettelSim = checkHadViettelSim(SaleOrderDetails);
        const isVisibleCardID = (isVisibleIDNumber || isHadVinaSim || isHadViettelSim);
        return (
            isVisble &&
            <View style={{
                width: constants.width,
                alignItems: "center",
                backgroundColor: COLORS.bgFFFFFF,
            }}>
                <View style={{
                    width: constants.width - 20,
                    backgroundColor: COLORS.bgFFFFFF,
                    flexDirection: "row",
                    alignItems: "flex-end",
                    justifyContent: "space-between"
                }}>
                    <MyText
                        addSize={-1.5}
                        style={{
                            color: COLORS.txt333333,
                            fontWeight: 'bold',
                            fontStyle: 'italic',
                            width: 120
                        }}
                        text={translate('editSaleOrder.id_card_number')}
                        children={
                            isRequired && (
                                <MyText
                                    addSize={-1.5}
                                    style={{
                                        color: COLORS.txtFF0000
                                    }}
                                    text={'*'}
                                />
                            )
                        }
                    />
                    {
                        isSupImages &&
                        <MyText
                            addSize={-2}
                            style={{
                                color: COLORS.txtEB1478,
                                width: constants.width - 140,
                                textDecorationLine: 'underline',
                                fontWeight: 'bold',
                                textAlign: 'right'
                            }}
                            onPress={() => this.getImage(dataShoppingCart)}
                            text={translate('editSaleOrder.take_ID_and_student_card_pictures')}
                        />
                    }
                </View>
                {
                    isVisibleCardID
                        ?
                        <View >
                            <View
                                style={{
                                    width: constants.width - 20,
                                    flexDirection: 'row',
                                    height: 46
                                }}>
                                <TextInput
                                    style={{
                                        width: '70%',
                                        borderWidth: 1,
                                        borderRadius: 4,
                                        borderColor: COLORS.bdCCCCCC,
                                        marginBottom: 5,
                                        paddingHorizontal: 10,
                                        paddingVertical: 8,
                                        color: COLORS.txt333333,
                                        backgroundColor: disabled ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                                    }}
                                    placeholder={translate('shoppingCart.placeholder_cccd')}
                                    value={customerIDCard}
                                    onChangeText={(text) => {
                                        const regExpIDCard = new RegExp(/^\d{0,12}$/);
                                        const isValidate =
                                            regExpIDCard.test(text) || text == '';
                                        if (isValidate) {
                                            this.setState({ customerIDCard: text, isHadCheck: false });
                                        }
                                    }}
                                    keyboardType={'numeric'}
                                    returnKeyType={'done'}
                                    blurOnSubmit={true}
                                    onSubmitEditing={Keyboard.dismiss}

                                    clearText={() => {
                                        this.setState({ customerIDCard: '' });
                                    }}
                                    key={'customerIDCard'}
                                    editable={!disabled}
                                />
                                <Button
                                    text={translate('shoppingCart.btn_check')}
                                    onPress={
                                        this.handleCheckDocument
                                    }
                                    styleContainer={{
                                        width: '27%',
                                        borderRadius: 4,
                                        backgroundColor: COLORS.txt147EFB,
                                        height: 39,
                                        marginLeft: 10
                                    }}
                                    styleText={{
                                        color: COLORS.txtFFFFFF,
                                        fontSize: 14,
                                        fontWeight: 'bold'
                                    }}
                                />
                            </View>
                            <MyText style={{ paddingBottom: 5, color: COLORS.txtF50537 }} text={"( Nhấn kiểm tra khi có sim Vinaphone )"}></MyText>
                        </View>
                        :
                        <FieldInput
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                paddingVertical: 8,
                                backgroundColor: COLORS.bgFFFFFF,
                                backgroundColor: disabled ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                            }}
                            placeholder={translate('editSaleOrder.placeholder_CMND')}
                            value={customerIDCard}
                            onChangeText={(text) => {
                                const regExpIDCard = new RegExp(/^\d{0,12}$/);
                                const isValidate = regExpIDCard.test(text) || (text == "");
                                if (isValidate) {
                                    this.setState({ customerIDCard: text });
                                }
                            }}
                            keyboardType={"numeric"}
                            returnKeyType={"done"}
                            blurOnSubmit={true}
                            onSubmitEditing={Keyboard.dismiss}
                            width={constants.width - 20}
                            height={40}
                            clearText={() => {
                                this.setState({ customerIDCard: "" });
                            }}
                            key={"customerIDCard"}
                            editable={!disabled}
                        />
                }
            </View>
        );
    };

    renderInstallment = (dataShoppingCart, saleProgramInfo) => {
        const {
            totalPrePaid,
            termLoan,
            isHasSaleProgram,
            isInsuranceFee,
            isInsuranceGood,
            goodInsuranceID,
            paymentMonthly
        } = this.state;
        const { SHAmount, IsAutoCreateEP, ShippingCost } = dataShoppingCart;
        const {
            SaleProgramName,
            SaleProgramID,
            RecordsProcessingFee,
            IsCardPartner,
            MinPrepaid,
            MaxPrepaid,
            TermLoanList,
            InsuranceFees,
            MasterGoodsInsurance,
        } = saleProgramInfo;
        const totalAmount = (SHAmount - ShippingCost);
        const percent = (parseFloat(totalPrePaid) / totalAmount) * 100;
        const isVisble = isHasSaleProgram && IsAutoCreateEP;
        return (
            isVisble &&
            <View style={{
                width: constants.width,
                alignItems: "center",
                backgroundColor: COLORS.bgFDF9E5,
                paddingVertical: 8
            }}>
                <MyText
                    style={{
                        width: constants.width - 20,
                        marginBottom: 2,
                        fontWeight: 'bold',
                        color: COLORS.txt288AD6
                    }}
                    text={`${SaleProgramID} - ${SaleProgramName}`}
                />
                <TitleInputMoney
                    title={translate('editSaleOrder.text_input_cost_contract')}
                    style={{
                        borderWidth: 1,
                        borderRadius: 4,
                        borderColor: COLORS.bdCCCCCC,
                        backgroundColor: COLORS.bgFFFFFF,
                        color: COLORS.txt333333,
                        height: 40,
                        marginBottom: 5,
                        paddingHorizontal: 10,
                        width: constants.width - 20,
                        backgroundColor: COLORS.bgF0F0F0,
                    }}
                    value={RecordsProcessingFee}
                    editable={false}
                    key={"processingFee"}
                    placeholder={"0"}
                />
                <TitleInputMoney
                    title={`${translate('editSaleOrder.lf_deposit')}`}
                    percent={percent}
                    isRequired={true}
                    style={{
                        borderWidth: 1,
                        borderRadius: 4,
                        borderColor: COLORS.bdCCCCCC,
                        backgroundColor: COLORS.bgFFFFFF,
                        color: COLORS.txt333333,
                        height: 40,
                        marginBottom: 5,
                        paddingHorizontal: 10,
                        width: constants.width - 20,
                        backgroundColor: IsCardPartner ? COLORS.bgF0F0F0 : COLORS.bgFFFFFF,
                    }}
                    placeholder={"0"}
                    value={totalPrePaid}
                    onChange={(value) => {
                        this.setState({ totalPrePaid: value });
                    }}
                    onBlur={() => {
                        let prePaid = totalPrePaid;
                        if (prePaid < MinPrepaid) {
                            prePaid = MinPrepaid;
                        }
                        else if (prePaid > MaxPrepaid) {
                            prePaid = MaxPrepaid;
                        }
                        else {
                            prePaid = (Math.round(prePaid / 1000) * 1000);
                        }
                        this.setState({ totalPrePaid: prePaid, }, this.getPaymentMonthly);
                    }}
                    editable={!IsCardPartner}
                    key={"totalPrePaid"}
                />
                {
                    helper.isArray(TermLoanList) &&
                    <Picker
                        label={"TermLoanName"}
                        value={"TermLoanNumber"}
                        defaultLabel={"Kỳ hạn vay"}
                        valueSelected={termLoan}
                        data={TermLoanList}
                        onChange={(item) => {
                            this.setState({
                                termLoan: item.TermLoanNumber,
                            }, this.getPaymentMonthly);
                        }}
                        numColumns={4}
                        title={translate('editSaleOrder.picker_loan_term')}
                        isRequired={true}
                    />
                }
                <InsuranceInfo
                    dataFee={InsuranceFees}
                    dataMasterGood={MasterGoodsInsurance}
                    isCheckFee={isInsuranceFee}
                    isCheckGood={isInsuranceGood}
                    goodId={goodInsuranceID}
                    onPressFee={() => {
                        this.setState({
                            isInsuranceFee: !isInsuranceFee,
                            // paymentMonthly: 0
                        }, this.getPaymentMonthly);
                    }}
                    onPressGood={(goodID) => {
                        this.setState({
                            isInsuranceGood: !isInsuranceGood,
                            goodInsuranceID: goodID,
                            // paymentMonthly: 0
                        }, this.getPaymentMonthly);
                    }}
                    onPressInsurance={(value) => {
                        this.setState({
                            goodInsuranceID: value,
                            // paymentMonthly: 0
                        }, this.getPaymentMonthly);
                    }}
                    disabled={false}
                />
                <TitleInputMoney
                    title={translate('editSaleOrder.text_input_payment_per_month')}
                    style={{
                        borderWidth: 1,
                        borderRadius: 4,
                        borderColor: COLORS.bdCCCCCC,
                        backgroundColor: COLORS.bgFFFFFF,
                        color: COLORS.txt333333,
                        height: 40,
                        marginBottom: 5,
                        paddingHorizontal: 10,
                        width: constants.width - 20,
                        backgroundColor: COLORS.bgF0F0F0,
                    }}
                    value={paymentMonthly}
                    editable={false}
                    key={"amountMonthly"}
                    placeholder={""}
                />
            </View>
        );
    };

    renderContractInfo = (dataShoppingCart, saleProgramInfo) => {
        const { PartnerInstallmentID } = saleProgramInfo;
        const { SHAmount, IsAutoCreateEP, ShippingCost } = dataShoppingCart;
        const { contractID, dataContract, isHasSaleProgram } = this.state;
        const isBNPLInformation = saleProgramInfo.PartnerInstallmentID == 34 || saleProgramInfo.PartnerInstallmentID == 35;

        const isVisible = isHasSaleProgram && !IsAutoCreateEP;
        if (!isVisible) return null;

        const totalAmount = SHAmount - ShippingCost;
        const isNewFollowPartner = helper.isNewFollowPartner(PartnerInstallmentID);

        if (isBNPLInformation) return (
            <BNPLInformation
                info={saleProgramInfo}
                data={dataContract}
                contractID={contractID}
                updateData={(data) => {
                    this.setState({ dataContract: data });
                }}
            />
        )

        if (isNewFollowPartner) {
            return (
                <SaleProgramInfo
                    totalAmount={totalAmount}
                    saleProgramInfo={saleProgramInfo}
                />
            );
        }

        return (
            <ContractInfo
                info={saleProgramInfo}
                data={dataContract}
                contractID={contractID}
                onChange={contractID => this.setState({ contractID })}
                onClear={() => this.setState({ contractID: "", dataContract: {} })}
                getData={this.getContractInfo}
                totalAmount={totalAmount}
                updateData={dataContract => this.setState({ dataContract })}
            />
        );
    };

    getContractInfo = (data) => {
        showBlockUI();
        this.props.actionPaymentOrder.getContractInfo(data).then(info => {
            hideBlockUI();
            this.setState({
                dataContract: {
                    ContractID: info.contactID,
                    PGProcessUserID: info.pgProcessUserID,
                    PGProcessUserName: info.pgProcessUserName,
                    packageRates: info.packageRates,
                    TotalPrePaid: info.totalPrePaid,
                    TermLoan: info.termLoan,
                    PaymentAmountMonthly: info.paymentAmountMonthly,
                    isPartnerConnectAPI: info.isPartnerConnectAPI
                }
            });
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError, [
                {
                    text: translate('editSaleOrder.btn_skip_uppercase'),
                    onPress: () => {
                        hideBlockUI();
                        this.setState({
                            contractID: "",
                            dataContract: {}
                        });
                    }
                },
                {
                    text: translate('editSaleOrder.btn_retry_uppercase'),
                    onPress: () => this.getContractInfo(data)
                }
            ]);
        });
    };

    getImage = (dataShoppingCart) => {
        const { actionEditSaleOrder: { setDataSaleOrderCart } } = this.props;
        const { lstImagePromotion } = this.state;
        if (lstImagePromotion.length > 0) {
            const studentCouponUrlFiles = dataShoppingCart.cus_UrlFilesShoppingCart?.filter(
                (img) => img.FileTypeID === FILE_TYPE.STUDENT_COUPON
            );
            const res = [...dataShoppingCart.SaleOrderCusPromotion?.UrlFiles ?? [], ...studentCouponUrlFiles ?? []];
            this.props.navigation.navigate("OrderStudentInfo",
                {
                    supImages: res,
                    dataShoppingCart,
                    setDataShoppingCart: setDataSaleOrderCart,
                    isStudentCoupon: dataShoppingCart.cus_IsRequireAttachmentSMSPromotion,
                    isStudentPromotion: dataShoppingCart.cus_IsPromotionType19Exist
                }
            );
        }
        else {
            let attachmentTypeList = getAttachmentType(dataShoppingCart);
            const data = {
                saleOrderID: dataShoppingCart.SaleOrderID,
                saleOrderDetailID: "",
                attachmentTypeList
            };
            showBlockUI();
            this.props.actionEditSaleOrder.getImageSaleOrder(data).then(res => {
                hideBlockUI();
                this.setState({ lstImagePromotion: res ?? [] });
                this.onSupplementImage(res)();
            }).catch(msgError => {
                Alert.alert(translate('common.notification_uppercase'), msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: "cancel",
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: "default",
                            onPress: () => this.getImage(dataShoppingCart)
                        }
                    ]
                );
            });
        }
    };

    onUpdateImage = (dataShoppingCart) => {
        const { actionEditSaleOrder: { setDataSaleOrderCart } } = this.props;
        setDataSaleOrderCart(dataShoppingCart);
        const studentUrlFiles = (dataShoppingCart.SaleOrderCusPromotion?.UrlFiles ?? []);
        const studentCouponUrlFiles = dataShoppingCart.cus_UrlFilesShoppingCart?.filter(
            (img) => img.FileTypeID === FILE_TYPE.STUDENT_COUPON
        );
        const supImages = [...studentUrlFiles ?? [], ...studentCouponUrlFiles ?? []];
        this.setState({ lstImagePromotion: supImages });

    };

    onSupplementImage = (dataImage) => () => {
        const { dataShoppingCart, actionEditSaleOrder: { setDataSaleOrderCart } } = this.props;
        const studentUrlFiles = dataImage?.filter(
            (img) => img.FileTypeID === FILE_TYPE.STUDENT
        );
        const studentCouponUrlFiles = dataImage?.filter(
            (img) => img.FileTypeID === FILE_TYPE.STUDENT_COUPON
        );
        if (dataShoppingCart.SaleOrderCusPromotion?.UrlFiles) {
            dataShoppingCart.SaleOrderCusPromotion.UrlFiles = studentUrlFiles;
        }
        if (helper.isArray(dataShoppingCart.cus_UrlFilesShoppingCart)) {
            dataShoppingCart.cus_UrlFilesShoppingCart = [
                ...studentCouponUrlFiles,
            ];
        }
        setDataSaleOrderCart(dataShoppingCart);
        this.props.navigation.navigate("OrderStudentInfo",
            {
                supImages: dataImage,
                dataShoppingCart,
                setDataShoppingCart: this.onUpdateImage,
                isStudentCoupon: dataShoppingCart.cus_IsRequireAttachmentSMSPromotion,
                isStudentPromotion: dataShoppingCart.cus_IsPromotionType19Exist
            }
        );
    };

    handleUpdateSaleOrderWithNewBatch = (newSaleOrder, index, newQuantity) => {
        const { dataShoppingCart } = this.props;
        if (dataShoppingCart.SaleOrderDetails[index].Quantity !== newQuantity) {
            if (newQuantity === 0) {
                Alert.alert("", "Số lượng bạn nhập không hợp lệ. Vui lòng kiểm tra lại");
            }
            else {
                this.setState({ idOriginProduct: newSaleOrder.ProductID });
                dataShoppingCart.SaleOrderDetails[index] = newSaleOrder;
                dataShoppingCart.SaleOrderDetails[index].Quantity = newQuantity;
                dataShoppingCart.cus_PromDiscountExtraMaster = null;
                this.modifyShoppingCart({
                    cartRequest: dataShoppingCart,
                    discountCode: "",
                    giftCode: "",
                    promotionGroups: [],
                    isChangeQuantity: true
                });
            }
        } else {
            dataShoppingCart.SaleOrderDetails[index] = newSaleOrder;
        }
    };

    handleSubmitQuantity = (newSaleOrder) => {
        const { dataShoppingCart } = this.props;
        const { SaleOrderDetails } = dataShoppingCart;;
        const map = {};
        for (const itemSaleOrder of SaleOrderDetails) {
            map[itemSaleOrder.SaleOrderDetailID] = itemSaleOrder.Quantity;
        }

        const resultSaleOrderDetails = newSaleOrder.filter(
            (newItemSaleOrder) =>
                map[newItemSaleOrder.SaleOrderDetailID] !== newItemSaleOrder.Quantity
        );
        if (resultSaleOrderDetails.length > 0) {
            const filterSaleOrderDetail = SaleOrderDetails.filter((item) => (!item.cus_IsEditQuantity));
            const newSaleOrderDetail =
                filterSaleOrderDetail.concat(newSaleOrder);
            const cartShoppingModify = helper.deepCopy(dataShoppingCart);
            cartShoppingModify.SaleOrderDetails = newSaleOrderDetail;;
            cartShoppingModify.cus_PromDiscountExtraMaster = null;
            this.modifyShoppingCart({
                cartRequest: cartShoppingModify,
                discountCode: '',
                giftCode: '',
                promotionGroups: []
            });;
        }
    };

    handleGetDataDrug = (dataDrug) => {
        const { dataShoppingCart } = this.props;
        this.setState({ showDrug: false });
        dataShoppingCart.cus_SaleOrderDoSageBOList = dataDrug;
    };

    handleStringImage = (imageUrls) => {
        const { dataShoppingCart } = this.props;
        if (imageUrls.length > 0) {
            dataShoppingCart.cus_UrlFilesShoppingCart = imageUrls;
        }
    };

    onOpenBarcode = () => {
        this.setState({ isOpenBarcode: true });
    };
    onCloseBarcode = () => {
        this.setState({ isOpenBarcode: false });
    };
    handleBarcode = (barcode) => {
        this.setState({ isOpenBarcode: false, discountCode: barcode }, () => {
            this.checkDiscountCode();
        });
    };

    updateIMEI = ({ indexProduct, IMEITemp }) => {
        const { dataShoppingCart, actionEditSaleOrder: { setDataSaleOrderCart } } = this.props;
        const { SaleOrderDetails } = dataShoppingCart;

        const updatedSaleOrderDetails = SaleOrderDetails.map((product, index) =>
            indexProduct == index
                ? {
                    ...product,
                    IMEITemp: IMEITemp

                }
                : product
        );

        setDataSaleOrderCart({ ...dataShoppingCart, SaleOrderDetails: updatedSaleOrderDetails });
    };

    handleValidIMEI = (imei, indexProduct) => {
        this.updateIMEI({ indexProduct, IMEITemp: imei });
    };

    handleDeleteIMEI = (indexProduct) => {
        this.updateIMEI({ indexProduct, IMEITemp: "" });
    };





    render() {
        const {
            dataShoppingCart,
            cartPromotion,
            voucherPromotion,
            stateCartPromotion,
            applyDetailIDs,
            actionShoppingCart
        } = this.props;
        const {
            SaleOrderDetails,
            SHCouponDiscountAmount,
            DiscountCode,
            SHAmount,
            SHDepositAmount,
            TotalPointLoyalty,
            PointPaid,
            SHChangeTranferAmountFee,
            SHForwarderFeeAmount,
            TotalAdvance,
            ShippingCost,
            IsInstallment,
            SaleOrderSaleProgramInfo,
            giftShoppingCartSaleOrders,
            saleShoppingCartSaleOrders,
            IsCreateSO,
            DeliveryInfo: deliveryInfo,
            DeliveryTypeID,
            IsSendPartnerInstallment,
            UserConfirmDeleteEP,
            SaleOrderID,
            cus_PromDiscountExtraMaster,
            IsSOAdditionalPromotion,
            IsSaleProgaramNotExist,
            OutputStoreID,
            IsBrandBlueWorld,
            EPOSTransactionID,
            IsSOAnKhang,
            cus_SaleOrderDoSageBOList,
            PermissionUpdate,
            cus_FeeAndDepositList,
            FeeAndDepositBO,
            IsOnlyEditCustomerInfo,
            SOTypeMessage,
            ExtensionProperty,
            StaffUser,
            StaffFullName
        } = dataShoppingCart;
        const {
            discountCode,
            isVisibleDeposit,
            isVisibleLoyalty,
            isRequirePhone,
            isVisibleVoucher,
            relationShipType,
            isVisibleFee,
            note,
            isVisibleCancelEP,
            dataReason,
            isVisibleCMVoucher,
            dataCMVoucher,
            isVisibleProfit,
            keyPromotion,
            isVisibleQuantity,
            idOriginProduct,
            isOriginQuantityChange,
            isOpenBarcode,
            saleUserInfo
        } = this.state;
        const ISSOThreePrice = false;
        const saleCartSaleOrders = saleShoppingCartSaleOrders ?? [];
        const isApplyCoupon = (SHCouponDiscountAmount > 0);
        const isVisibleContact = (DeliveryTypeID == 1);
        const isCartNonEmpty = helper.IsNonEmptyArray(SaleOrderDetails);
        const saleProgramInfo = getSaleProgramInfo(dataShoppingCart);
        const isNewFollowPartner = helper.isNewFollowPartner(saleProgramInfo.PartnerInstallmentID);
        const ISSOPackagePrice = (SaleOrderDetails?.findIndex((saleOrder) => !helper.IsEmptyObject(saleOrder.PricePolicyApplyBO))) !== -1;

        const isLockProduct = IsSOAdditionalPromotion || IsSaleProgaramNotExist || IsOnlyEditCustomerInfo || isNewFollowPartner;
        const isLockInstallment = IsSOAdditionalPromotion || !isCartNonEmpty || IsSOAnKhang || ISSOThreePrice || IsOnlyEditCustomerInfo || ExtensionProperty?.BNPLTCheckPayment;
        const isLockCoupon = IsInstallment && helper.IsNonEmptyString(EPOSTransactionID) || ISSOThreePrice || IsOnlyEditCustomerInfo || isNewFollowPartner;
        const isLockDelivery = IsSaleProgaramNotExist || ISSOThreePrice || IsOnlyEditCustomerInfo || IsSOAnKhang || ISSOPackagePrice;
        const isLockNote = IsSaleProgaramNotExist || ISSOThreePrice || IsOnlyEditCustomerInfo || IsSOAnKhang;
        const isLockChangePayment = false;
        const disabled = IsSaleProgaramNotExist
        const isLockAdd = IsSOAdditionalPromotion || IsSaleProgaramNotExist || ISSOThreePrice || IsOnlyEditCustomerInfo || isNewFollowPartner || ISSOPackagePrice;
        const isShowDosage = IsSOAnKhang && cus_SaleOrderDoSageBOList?.length > 0;
        const dataEditQuantity = SaleOrderDetails?.filter(item => item.cus_IsEditQuantity);
        const isEditQuantity = IsSOAnKhang && dataEditQuantity?.length > 0;
        const promoRequireInfors = this.getPromoRequireInfors(dataShoppingCart);
        let feePlusMoneyBO = {};
        if (helper.IsNonEmptyArray(cus_FeeAndDepositList)) {
            feePlusMoneyBO = this.calculateFeePlusMoneyBO(cus_FeeAndDepositList, SHChangeTranferAmountFee);
        } else if (!helper.IsEmptyObject(FeeAndDepositBO) && helper.IsNonEmptyArray(FeeAndDepositBO?.cus_FeeBOList)) {
            feePlusMoneyBO = this.calculateFeePlusMoneyBO(FeeAndDepositBO.cus_FeeBOList, SHChangeTranferAmountFee);
        }
        const amountAdjust = helper.getTotalAmountAdjustPrice(SaleOrderDetails)
        const titleUser = !!saleUserInfo.UserName ? `${saleUserInfo.UserName} - ${saleUserInfo.FullName}` : `${StaffUser} - ${StaffFullName}` || ""

        return (
            !helper.IsEmptyObject(dataShoppingCart) &&
            <View style={{ flex: 1 }}>
                <BottomSheetModalProvider>
                    <KeyboardAwareScrollView
                        style={{
                            flex: 1
                        }}
                        enableResetScrollToCoords={false}
                        keyboardShouldPersistTaps="always"
                        bounces={false}
                        overScrollMode="always"
                        showsHorizontalScrollIndicator={false}
                        showsVerticalScrollIndicator={false}
                        extraScrollHeight={60}
                    >
                        <SafeAreaView
                            style={{
                                flex: 1,
                                backgroundColor: COLORS.bgFAFAFA
                            }}
                        >
                            <FlatList
                                data={SaleOrderDetails}
                                renderItem={({ item, index }) => (
                                    isLockProduct
                                        ? <LockAdditionalProductInfo
                                            mainProduct={item}
                                            couponDiscount={SHCouponDiscountAmount}
                                            isAnKhang={IsSOAnKhang}
                                        />
                                        : <ProductInfo
                                            ISSOThreePrice={ISSOPackagePrice}
                                            onChangeSheet={() => {
                                                if (!helper.IsEmptyObject(item.PricePolicyApplyBO)) {
                                                    const dataPackagePrice = [
                                                        {
                                                            ...item.PricePolicyApplyBO,
                                                            DetailPolicies: item.PricePolicyApplyBO.lstSaleOrderPricePolicyApplyDetailBO
                                                        }
                                                    ];
                                                    this.setState({ dataPackagePrice: dataPackagePrice }, () => {
                                                        this.packagePriceSheetRef.current?.present();
                                                    });
                                                }
                                                else {
                                                    Alert.alert(translate("common.notification"), "Không có thông tin dịch vụ.", [
                                                        {
                                                            text: 'OK',
                                                            style: 'cancel'
                                                        }
                                                    ]);
                                                }
                                            }}
                                            mainProduct={item}
                                            onDelete={this.removeItemMainProduct(SaleOrderDetails, index)}
                                            onDeleteSaleProduct={(data, fieldName) => {
                                                this.removeItemSaleProduct(data, fieldName, index);
                                            }}
                                            couponDiscount={SHCouponDiscountAmount}
                                            onUpdateSimProduct={(data, fieldName) => {
                                                this.onUpdateSimProduct(data, fieldName, index);
                                            }}
                                            applyDetailIDs={applyDetailIDs}
                                            actionShoppingCart={actionShoppingCart}
                                            isAnKhang={IsSOAnKhang}
                                            onUpdateSaleOrder={(newSaleOrder, newQuantity) =>
                                                this.handleUpdateSaleOrderWithNewBatch(newSaleOrder, index, newQuantity)
                                            }
                                            onValidIMEI={(imei) => { this.handleValidIMEI(imei, index) }}
                                            deleteIMEI={() => this.handleDeleteIMEI(index)}
                                        />)}
                                keyExtractor={(item, index) => 'keyProductInfo' + index}
                                keyboardShouldPersistTaps={"always"}
                                removeClippedSubviews={false}
                                ListHeaderComponent={
                                    <View style={{
                                        width: constants.width
                                    }}>
                                        {
                                            helper.IsNonEmptyString(SOTypeMessage) && <MyText
                                                text={SOTypeMessage}
                                                style={{
                                                    color: COLORS.txtD0021B,
                                                    fontStyle: 'italic',
                                                    padding: 5
                                                }}
                                            />
                                        }
                                        <InstallmentInfo
                                            programInfo={SaleOrderSaleProgramInfo}
                                            value={IsInstallment}
                                            onSwitch={(value) => {
                                                if (!isLockChangePayment) {
                                                    this.onChangeInstallment(value, IsCreateSO);
                                                }
                                            }}
                                            disabled={isLockInstallment}
                                            onChange={this.moveToInstallment(IsSendPartnerInstallment)}
                                            isSaleProgaramNotExist={IsSaleProgaramNotExist}
                                            outputStoreID={OutputStoreID}
                                        />
                                        <View style={{
                                            backgroundColor: COLORS.bg6A9C84,
                                            width: constants.width,
                                            height: 40,
                                            paddingLeft: 10,
                                            flexDirection: "row",
                                            justifyContent: 'space-between',
                                            alignItems: "center"
                                        }}>
                                            <MyText
                                                style={{
                                                    color: COLORS.txtFFFFFF,
                                                    fontWeight: 'bold',
                                                    width: constants.width - 58
                                                }}
                                                text={translate('editSaleOrder.products_list_2')}
                                            />
                                            {
                                                !isLockAdd &&
                                                <Button
                                                    iconLeft={{
                                                        iconSet: "MaterialIcons",
                                                        name: "add-box",
                                                        size: 30,
                                                        color: COLORS.icFFFFFF
                                                    }}
                                                    text={""}
                                                    styleContainer={{
                                                        width: 48,
                                                        height: 40,
                                                        justifyContent: "flex-end",
                                                        alignItems: "center",
                                                        backgroundColor: COLORS.bg6A9C84,
                                                        flexDirection: "row",
                                                        paddingRight: 8
                                                    }}
                                                    styleText={{
                                                        color: COLORS.txtFFFFFF,
                                                    }}
                                                    onPress={this.onPressAddCart(dataShoppingCart, isCartNonEmpty)}
                                                />
                                            }
                                        </View>
                                    </View>
                                }
                                ListFooterComponent={
                                    isCartNonEmpty &&
                                    <View style={{
                                        width: constants.width
                                    }}>
                                        {
                                            !isLockCoupon &&
                                            <ButtonAdjust
                                                onAdjustPrice={this.onPressAdjustPrice(isApplyCoupon)}
                                                onCreatePrice={this.onPressCreatePrice(isApplyCoupon)}
                                                isHiddenAdjustPrice={IsBrandBlueWorld || IsSOAnKhang}
                                            />
                                        }
                                        {
                                            isLockCoupon
                                                ? <LockCoupon
                                                    discountCode={DiscountCode}
                                                    couponDiscount={SHCouponDiscountAmount}
                                                />
                                                : <Coupon
                                                    discountCode={discountCode}
                                                    onchangeDiscountCode={(text) => {
                                                        if (helper.isValidateCharVN(text)) {
                                                            this.setState({ discountCode: text });
                                                        }
                                                    }}
                                                    couponDiscount={SHCouponDiscountAmount}
                                                    applyDiscountCode={this.checkDiscountCode}
                                                    cancelDiscountCode={this.cancelDiscountCode}
                                                    openBarcode={this.onOpenBarcode}
                                                />

                                        }
                                        <CartPromotionProfit
                                            data={cus_PromDiscountExtraMaster}
                                            onShow={() => {
                                                this.setState({ isVisibleProfit: true });
                                            }}
                                        />
                                        {
                                            IsCreateSO
                                                ? <CartPromotion
                                                    promotion={cartPromotion}
                                                    statePromotion={stateCartPromotion}
                                                    applyPromotion={(promotionGroups, setKeyPromotion) => {
                                                        if (this.timeoutApplyPromotion) {
                                                            clearTimeout(this.timeoutApplyPromotion);
                                                        }
                                                        this.timeoutApplyPromotion = setTimeout(this.applyCartPromotion(promotionGroups, setKeyPromotion), 200);
                                                    }}
                                                    onRetryCartPromotion={this.getCartPromotion}
                                                    isVisibleVoucher={isVisibleVoucher}
                                                    voucherPromotion={voucherPromotion}
                                                    keyPromotion={keyPromotion}
                                                    actionDetail={actionShoppingCart}
                                                />
                                                : <LockCartPromotion
                                                    giftSaleOrders={giftShoppingCartSaleOrders}
                                                    saleSaleOrders={saleCartSaleOrders}
                                                />
                                        }
                                        <FeeAmount
                                            total={ShippingCost}
                                            onShow={() => {
                                                this.setState({ isVisibleFee: true });
                                            }}
                                        />
                                        <PointLoyalty
                                            // total={TotalPointLoyalty}
                                            total={PointPaid}
                                            onShow={() => {
                                                this.setState({ isVisibleLoyalty: true });
                                            }}
                                        />
                                        <TotalAmount
                                            total={SHAmount}
                                        />
                                        <DepositAmount
                                            total={TotalAdvance}
                                            onShow={() => {
                                                this.setState({ isVisibleDeposit: true });
                                            }}
                                        />
                                        <DeliveryInfo
                                            deliveryInfo={deliveryInfo}
                                            note={note}
                                            onChangeNote={(text) => {
                                                this.setState({ note: text });
                                            }}
                                            clearNote={() => {
                                                this.setState({ note: "" });
                                            }}
                                            onEdit={this.getDataCartDelivery}
                                            disabled={isLockDelivery}
                                            isLockNote={isLockNote}
                                        />
                                        {helper.IsNonEmptyArray(promoRequireInfors) && <View style={{
                                            width: constants.width,
                                            justifyContent: "center",
                                            alignItems: "center",
                                            backgroundColor: COLORS.btn5482AB,

                                        }}>

                                            <Button
                                                text={"Chụp hình bổ sung"}
                                                onPress={() => {
                                                    this.props.navigation.navigate("FormPromotion", { promoRequireInfors: promoRequireInfors });
                                                }}
                                                styleContainer={{
                                                    flexDirection: 'row',
                                                    width: constants.width - 10,
                                                    height: 40,
                                                    backgroundColor: COLORS.btn5482AB,
                                                    borderTopWidth: StyleSheet.hairlineWidth,
                                                    borderTopColor: COLORS.bdFFFFFF,
                                                    justifyContent: 'space-between',
                                                }}
                                                styleText={{
                                                    color: "white",
                                                    fontSize: 16,
                                                    marginRight: 8,
                                                    fontWeight: "bold",
                                                }}
                                                iconRight={{
                                                    iconSet: "FontAwesome",
                                                    name: "chevron-right",
                                                    size: 16,
                                                    color: COLORS.icFFFFBC
                                                }}
                                            />
                                        </View>}
                                        {this.renderCustomerInfo(isVisibleContact, disabled)}
                                        <CheckBoxPolicy
                                            isSelected={this.state.isSelectedPolicy}
                                            onSelectPolicy={() => {
                                                this.setState({ isSelectedPolicy: !this.state.isSelectedPolicy });
                                            }}
                                            disabled={this.state.disabledPolicy}
                                        />
                                        {dataShoppingCart.IsSOAnKhang && (
                                            <PrescriptionImages
                                                handleStringImage={this.handleStringImage}
                                                dataImage={dataShoppingCart.cus_UrlFilesShoppingCart}
                                            />
                                        )}
                                        {
                                            !IsSOAnKhang &&
                                            <RadioRelationShip
                                                type={relationShipType}
                                                onChangeType={(value) => {
                                                    this.setState({ relationShipType: value });
                                                }}
                                                disabled={disabled}
                                            />
                                        }

                                        <View style={{
                                            width: constants.width,
                                            alignItems: "center",
                                            justifyContent: "center",
                                            flexDirection: "row",
                                            paddingVertical: 10
                                        }}>
                                            {
                                                IsCreateSO
                                                    ? <Button
                                                        text={translate('editSaleOrder.btn_create_order')}
                                                        styleContainer={{
                                                            borderRadius: 4,
                                                            backgroundColor: COLORS.btnF49B0C,
                                                            height: 44,
                                                            width: 140,
                                                        }}
                                                        styleText={{
                                                            color: COLORS.txtFFFFFF,
                                                            fontSize: 14,
                                                            fontWeight: "bold"
                                                        }}
                                                        onPress={this.onCreateSaleOrder}
                                                        disabled={stateCartPromotion.isFetching}
                                                    />
                                                    : <Button
                                                        text={translate('editSaleOrder.btn_update_uppercase')}
                                                        styleContainer={{
                                                            borderRadius: 4,
                                                            backgroundColor: COLORS.btn288AD6,
                                                            height: 44,
                                                            width: 140,
                                                            opacity: PermissionUpdate == false ? 0.6 : 1,
                                                        }}
                                                        activeOpacity={disabled ? 0.5 : 1}
                                                        styleText={{
                                                            color: COLORS.txtFFFFFF,
                                                            fontSize: 14,
                                                            fontWeight: "bold"
                                                        }}
                                                        onPress={this.onUpdateSaleOrder}
                                                        disabled={PermissionUpdate == false}
                                                    />
                                            }
                                        </View>
                                    </View>
                                }
                            />
                            <ModalDeposit
                                isVisible={isVisibleDeposit}
                                hideModal={() => {
                                    this.setState({ isVisibleDeposit: false });
                                }}
                                saleOrderDetails={SaleOrderDetails}
                            />
                            {/* <ModalFee
                            isVisible={isVisibleFee}
                            hideModal={() => {
                                this.setState({ isVisibleFee: false })
                            }}
                            SHChangeTranferAmountFee={SHChangeTranferAmountFee}
                            SHForwarderFeeAmount={SHForwarderFeeAmount}
                            ShippingCost={ShippingCost}
                        /> */}
                            {
                                isVisibleFee &&
                                <ModalFees
                                    isShow={isVisibleFee}
                                    hideModal={() => { this.setState({ isVisibleFee: false }); }}
                                    feePlusMoneyBO={feePlusMoneyBO}
                                    title={"Phụ phí tạm tính"}
                                />
                            }
                            <ModalLoyalty
                                isVisible={isVisibleLoyalty}
                                hideModal={() => {
                                    this.setState({ isVisibleLoyalty: false });
                                }}
                                saleOrderDetails={SaleOrderDetails}
                            />
                            <ModalProfitPromotion
                                isVisible={isVisibleProfit}
                                hideModal={() => {
                                    this.setState({ isVisibleProfit: false });
                                }}
                                data={cus_PromDiscountExtraMaster}
                            />
                            {
                                isRequirePhone &&
                                <PopupPhone
                                    isVisible={isRequirePhone}
                                    title={translate('editSaleOrder.popup_phone_coupon_requires_phone')}
                                    onCancel={() => {
                                        this.setState({ isRequirePhone: false });
                                    }}
                                    onConFirm={(phone) => {
                                        this.setState({
                                            customerPhone: phone,
                                            isRequirePhone: false,
                                        }, () => {
                                            this.checkDiscountCode(),
                                                this.handleAPIGetCustomerProfile(phone);
                                        });
                                    }}
                                />
                            }
                            {
                                isVisibleCancelEP &&
                                <ModalCancelEP
                                    isVisible={isVisibleCancelEP}
                                    onBack={() => {
                                        this.setState({ isVisibleCancelEP: false });
                                    }}
                                    onConfirm={(data) => {
                                        this.setState({ isVisibleCancelEP: false });
                                        this.checkGiftVIP(data);
                                    }}
                                    isHasRight={helper.IsNonEmptyString(UserConfirmDeleteEP)}
                                    dataSO={this.dataCancelEP}
                                    dataReason={dataReason}
                                />
                            }
                            {
                                isVisibleCMVoucher &&
                                <ModalCMVoucher
                                    isVisible={isVisibleCMVoucher}
                                    onConfirm={(data) => {
                                        this.setState({ isVisibleCMVoucher: false });
                                        this.onCheckCancelEP(data);
                                    }}
                                    dataCMVoucher={dataCMVoucher}
                                    dataSO={this.dataInOutVoucher}
                                    onClose={() => {
                                        this.setState({ isVisibleCMVoucher: false });
                                    }}
                                    saleOrderID={SaleOrderID}
                                />
                            }
                            {
                                isOpenBarcode &&
                                <BarcodeCamera
                                    isVisible={isOpenBarcode}
                                    closeCamera={this.onCloseBarcode}
                                    resultScanBarcode={this.handleBarcode}
                                />
                            }
                            <BlockUI
                                visible={this.state.blockUI}
                                onChangeVisible={(value) => {
                                    this.setState({
                                        blockUI: value
                                    });
                                    this.packagePriceSheetRef.current?.dismiss();
                                }}
                            />
                        </SafeAreaView>
                    </KeyboardAwareScrollView>
                    <View style={{ position: 'absolute', bottom: 55 }}>
                        {
                            isEditQuantity &&
                            <AnimationFloatingButton screenWidth={constants.width}>
                                <PrescriptionQuantity
                                    data={dataEditQuantity}
                                    isShowModal={isVisibleQuantity}
                                    onClose={() =>
                                        this.setState({ isVisibleQuantity: false })
                                    }
                                    onSubmit={(objBatch) => {
                                        this.handleSubmitQuantity(objBatch);
                                    }}
                                    isOriginQuantityChange={isOriginQuantityChange}
                                    idOriginProduct={idOriginProduct}
                                    temp
                                />
                                <TouchableOpacity
                                    onPress={() => {
                                        this.setState({ isVisibleQuantity: true });
                                    }}
                                    style={styles.fabMedicine}>
                                    <Icon
                                        name="edit"
                                        iconSet="MaterialIcons"
                                        size={22}
                                        color={COLORS.txtFFFFFF}
                                    />
                                </TouchableOpacity>
                            </AnimationFloatingButton>
                        }
                        {isShowDosage && (
                            <AnimationFloatingButton screenWidth={constants.width}>
                                <PrescriptionScreen
                                    dosage={
                                        dataShoppingCart.cus_SaleOrderDoSageBOList ?? []
                                    }
                                    isVisible={this.state.showDrug}
                                    hideModal={() => this.setState({ showDrug: false })}
                                    handleGetDataDrug={this.handleGetDataDrug}
                                    disabled={false}
                                />
                                <TouchableOpacity
                                    onPress={() => {
                                        this.setState({ showDrug: true });
                                    }}
                                    style={styles.fabMedicine}>
                                    <Icon
                                        name="pill"
                                        iconSet="MaterialCommunityIcons"
                                        size={22}
                                        color={COLORS.txtFFFFFF}
                                    />
                                </TouchableOpacity>
                            </AnimationFloatingButton>
                        )}
                    </View>
                    {
                        <PackagePriceSheet
                            ref={this.packagePriceSheetRef}
                            snapPoints={['99.99999%']}
                            product={this.state.dataPackagePrice}
                            listPackageService={this.state.dataPackagePrice}
                            onGoNext={(data) => { }}
                            getCurrentIndex={() => {
                                console.log('');
                            }}
                            disabled={false}
                            topInset={200}
                            onCloseSheet={() => {
                                this.setState({ blockUI: false });
                            }}
                        />
                    }
                    <QuestionModal
                        isShow={this.state.isShowModalQuestion}
                        onPress={this.applyAnswerQuestion}
                        questions={this.state.questionList}
                        onSwitchAnswer={this.handleChangeAnswer}
                    />
                    <OTPSheet
                        bottomSheetRef={this.OTPSheetRef}
                        onChangeStatusSheet={() => { }}

                    >
                        <OTPInner
                            customerInfo={{
                                customerPhone: this.state.customerPhone,
                                customerName: this.state.customerName,
                            }}
                            onConfirm={this.handleAddToCartWithAdjustPrice}
                            typeOTP={this.state.typeOTP}
                            price={amountAdjust}
                        />
                    </OTPSheet>
                    <SearchUserSheet
                        bottomSheetRef={this.SearchUserSheetRef}
                        onChangeStatusSheet={() => { }}
                        staffInfo={{ name: titleUser }}
                    >
                        <SearchUserInner
                            handleSelectUser={(user) => {
                                this.setState({ saleUserInfo: user })
                                this.SearchUserSheetRef.current?.close()
                            }}
                        />
                    </SearchUserSheet>
                </BottomSheetModalProvider>
            </View>
        );
    }

    onChangTypeCustomer = (isCompany) => () => {
        const {
            taxID,
            customerName,
            customerAddress,
            customerPhone,
            isAllowInvoice,
            isLockTax,
            gender,
            contactPhone,
            contactName,
            contactAddress
        } = this.state;
        this.setState({
            isCompany: isCompany,
            taxID: isLockTax ? taxID : "",
            customerPhone: (!isCompany && !!customerPhone && customerPhone.length > 10) ? "" : customerPhone,
            customerName: customerName,
            customerAddress: customerAddress,
            contactPhone: contactPhone,
            contactName: contactName,
            contactAddress: contactAddress,
            gender: gender,
            isSelectedPolicy: false,
            disabledPolicy: false
        }, () => {
            helper.IsNonEmptyString(this.state.contactPhone) && this.handleAPIGetCustomerProfile(this.state.contactPhone);
            if (!this.state.isCompany) {
                this.isChangeCustomer.current = true;
            }

        });
        // if (!isAllowInvoice && isCompany) {
        //     Alert.alert("", "Đơn hàng không cho phép xuất hóa đơn công ty");
        // }
        // else {
        // }
    };

    getCustomerInfo = (phoneNumber) => {
        const regExpPhone = new RegExp(/^[0]\d{8,9}$/);
        const isValidate = regExpPhone.test(phoneNumber);
        if (isValidate) {
            const {
                customerName,
                customerAddress,
                isLockName,
                isLockAddress
            } = this.state;
            this.checkWowPoints(phoneNumber);
            actionShoppingCartCreator.getCustomerByPhone(phoneNumber).then(info => {
                this.setState({
                    customerPhone: phoneNumber,
                    gender: info.gender,
                    customerName: isLockName
                        ? customerName
                        : (info.customerName || customerName),
                    customerAddress: isLockAddress
                        ? customerAddress
                        : (info.customerAddress || customerAddress),
                    taxID: "",
                    contactPhone: "",
                    contactName: "",
                    contactAddress: "",
                });
            });
        }
    };

    handleAPIGetCustomerProfile = (phoneNumber, isFirstRender) => {
        const {
            userInfo: { storeID, languageID, moduleID },
        } = this.props;
        const baseBody = {
            "loginStoreId": storeID,
            "languageID": languageID,
            "moduleID": moduleID,
        };
        const regExpPhone = new RegExp(/^[0]\d{8,9}$/);
        const isValidate = regExpPhone.test(phoneNumber);
        if (isValidate) {
            showBlockUI();
            const {
                customerName,
                isLockName,
                isCompany,
                contactName
            } = this.state;
            const { customerConfirmPolicy, dataShoppingCart: { OldSaleOrderDetails, CustomerInfo } } = this.props;
            this.isChangeCustomer.current = false;
            if (customerConfirmPolicy?.[TYPE_PROFILE.CUSTOMER]?.[0]?.phoneNumber != phoneNumber) {
                this.props.actionShoppingCart.set_map_customer_confirm_policy({
                    type: TYPE_PROFILE.CUSTOMER,
                    infoCustomerCRM: []
                });
            }
            actionShoppingCartCreator.getCustomerProfile({ ...baseBody, phoneNumber, typeProfile: TYPE_PROFILE.CUSTOMER }).then(customerProfile => {
                hideBlockUI();
                if (customerProfile == null) {
                    if (isFirstRender) {
                        return this.setState({
                            isSelectedPolicy: false,
                            disabledPolicy: false
                        }, () => {
                            this.props.actionShoppingCart.set_map_customer_confirm_policy({
                                type: TYPE_PROFILE.CUSTOMER,
                                infoCustomerCRM: []
                            });
                        });
                    } else {
                        if (this.state.isCompany) {
                            return this.setState({
                                contactPhone: phoneNumber,
                                contactName: "",
                                customerIDCard: "",
                                isSelectedPolicy: false,
                                disabledPolicy: false
                            }, () => {
                                this.props.actionShoppingCart.set_map_customer_confirm_policy({
                                    type: TYPE_PROFILE.CUSTOMER,
                                    infoCustomerCRM: []
                                });
                            });
                        }
                        else {
                            return this.setState({
                                customerPhone: phoneNumber,
                                gender: null,
                                customerName: "",
                                taxID: "",
                                contactPhone: "",
                                contactName: "",
                                customerIDCard: "",
                                isSelectedPolicy: false,
                                disabledPolicy: false
                            }, () => {
                                this.props.actionShoppingCart.set_map_customer_confirm_policy({
                                    type: TYPE_PROFILE.CUSTOMER,
                                    infoCustomerCRM: []
                                });
                            });
                        }
                    }
                }
                const customerInfo = customerProfile[0];
                const CustomerName = isLockName ? customerName : (customerInfo.customerName || customerName);
                if ([customerConfirmPolicy?.[TYPE_PROFILE.CUSTOMER]?.[0]?.profileId, OldSaleOrderDetails[0].Profile?.[TYPE_PROFILE.CUSTOMER]?.[0]?.profileId].findIndex((_id) => _id != customerInfo.profileId) !== -1) {
                    this.isChangeCustomer.current = true;
                }
                if (isFirstRender) {
                    this.setState({
                        isSelectedPolicy: customerInfo.isSigned,
                        disabledPolicy: customerInfo.isSigned == 1 ? true : false,
                    }, () => {
                        this.props.actionShoppingCart.set_map_customer_confirm_policy({
                            type: TYPE_PROFILE.CUSTOMER,
                            infoCustomerCRM: customerProfile
                        });
                    });
                } else {
                    if (isCompany) {
                        this.setState({
                            contactPhone: phoneNumber,
                            gender: getGender(customerInfo.gender),
                            contactName: customerInfo.customerName || contactName,
                            isSelectedPolicy: customerInfo.isSigned,
                            disabledPolicy: customerInfo.isSigned == 1 ? true : false,
                            customerIDCard: customerInfo.cardCustomerId || CustomerInfo.CustomerIDCard,
                            customerPhone: phoneNumber,
                        }, () => {
                            this.props.actionShoppingCart.set_map_customer_confirm_policy({
                                type: TYPE_PROFILE.CUSTOMER,
                                infoCustomerCRM: customerProfile
                            });
                        });
                    }
                    else {
                        this.setState({
                            customerPhone: phoneNumber,
                            gender: getGender(customerInfo.gender),
                            customerName: CustomerName,
                            taxID: "",
                            contactPhone: "",
                            contactName: "",
                            customerIDCard: customerInfo.cardCustomerId || this.state.customerIDCard,
                            isSelectedPolicy: customerInfo.isSigned,
                            disabledPolicy: customerInfo.isSigned == 1 ? true : false
                        }, () => {
                            this.props.actionShoppingCart.set_map_customer_confirm_policy({
                                type: TYPE_PROFILE.CUSTOMER,
                                infoCustomerCRM: customerProfile
                            });
                        });
                    }
                }
            }).catch((error) => {
                console.log("🚀 ~ EditSaleOrder ~ actionShoppingCartCreator.getCustomerProfile ~ error:", error);
                hideBlockUI();
            }).finally(() => {
                this.isFirstRenderProfile = false;
            });


        }
    };

    checkWowPoints = (phoneNumber) => {
        // this.props.loyaltyAction.checkWowPoints(phoneNumber)
        //     .then((message) => {
        //         if (message) {
        //             // clear message after 5s
        //             this.timeOutAlertMessage = setTimeout(() => {
        //                 this.props.loyaltyAction.setWowPointsMessage({
        //                     phoneNumber: '',
        //                     message: ''
        //                 });
        //             }, 5000);
        //         }
        //     })
        //     .catch((error) => {
        //         !CONFIG.isPRODUCTION && Toast.show({
        //             type: 'error',
        //             text1: error ?? 'Lỗi: Hỏi Trần Nghĩa - 165059'
        //         });
        //         console.log("checkWowPoints ", error);
        //     });
    };

    getContactInfo = (phoneNumber) => () => {
        const regExpPhone = new RegExp(/^[0]\d{8,9}$/);
        const isValidate = regExpPhone.test(phoneNumber);
        if (isValidate) {
            const {
                contactName,
                contactAddress
            } = this.state;
            actionShoppingCartCreator.getCustomerByPhone(phoneNumber).then(info => {
                this.setState({
                    contactPhone: phoneNumber,
                    gender: info.gender,
                    contactName: info.customerName || contactName,
                    contactAddress: info.customerAddress || contactAddress,
                });
            });
        }
    };

    // getCompanyInfo = (taxID) => {
    //     const regExpTax10 = new RegExp(/^\d{10}$/);
    //     const regExpTax14 = new RegExp(/^\d{10}[-]\d{3}$/);
    //     const isValidateTax10 = regExpTax10.test(taxID);
    //     const isValidateTax14 = regExpTax14.test(taxID);
    //     const isValidate = (isValidateTax10 || isValidateTax14);
    //     if (isValidate) {
    //         actionShoppingCartCreator.getCompanyByTax(taxID).then(info => {
    //             const {
    //                 customerPhone,
    //                 customerName,
    //                 customerAddress,
    //                 contactPhone,
    //                 contactName,
    //                 contactAddress,
    //                 isLockPhone,
    //                 isLockName,
    //                 isLockAddress,
    //             } = this.state;
    //             this.setState({
    //                 taxID: taxID,
    //                 gender: info.gender,
    //                 customerPhone: isLockPhone
    //                     ? customerPhone
    //                     : (info.customerPhone || customerPhone),
    //                 customerName: isLockName
    //                     ? customerName
    //                     : (info.customerName || customerName),
    //                 customerAddress: isLockAddress
    //                     ? customerAddress
    //                     : (info.customerAddress || customerAddress),
    //                 contactPhone: info.contactPhone || contactPhone,
    //                 contactName: info.contactName || contactName,
    //                 contactAddress: info.deliveryAddress || contactAddress,
    //             })
    //         })
    //     }
    // }

    getCompanyProfile = (taxID, isFirstRender) => {
        const {
            userInfo: { storeID, languageID, moduleID },
        } = this.props;
        const {
            customerPhone,
            isLockPhone
        } = this.state;
        const baseBody = {
            "loginStoreId": storeID,
            "languageID": languageID,
            "moduleID": moduleID,
        };
        const regExpTax10 = new RegExp(/^\d{10}$/);
        const regExpTax14 = new RegExp(/^\d{10}[-]\d{3}$/);
        const isValidateTax10 = regExpTax10.test(taxID);
        const isValidateTax14 = regExpTax14.test(taxID);
        const isValidate = (isValidateTax10 || isValidateTax14);
        if (isValidate) {
            actionShoppingCartCreator.getCustomerProfile({ ...baseBody, phoneNumber: taxID, typeProfile: TYPE_PROFILE.COMPANY }).then(customerProfile => {
                if (customerProfile == null) return this.props.actionShoppingCart.set_map_customer_confirm_policy({
                    type: TYPE_PROFILE.COMPANY,
                    infoCustomerCRM: []
                });
                const customerInfo = customerProfile[0];
                if (isFirstRender) {
                    this.props.actionShoppingCart.set_map_customer_confirm_policy({
                        type: TYPE_PROFILE.COMPANY,
                        infoCustomerCRM: customerProfile
                    });
                }
                else {
                    this.setState({
                        taxID: taxID,
                        customerName: customerInfo.companyName,
                        customerAddress: customerInfo.address

                        // customerPhone: isLockPhone
                        //     ? customerPhone
                        //     : customerInfo.companyPhone
                    }, () => {
                        this.props.actionShoppingCart.set_map_customer_confirm_policy({
                            type: TYPE_PROFILE.COMPANY,
                            infoCustomerCRM: customerProfile
                        });
                    });
                }
            });
        }
    };

    getOldCustomerInfo = () => {
        const {
            customerPhone,
            customerName,
            customerAddress,
            isLockName,
            isLockAddress
        } = this.state;
        if (customerPhone) {
            // this.getCustomerInfo(customerPhone);
            this.handleAPIGetCustomerProfile(customerPhone);
        }
        else {
            storageHelper.getItem(STORAGE_CONST.CUSTOMER_INFO).then(result => {
                if (helper.IsNonEmptyString(result)) {
                    const dataTopInfo = JSON.parse(result);
                    const customerInfo = dataTopInfo.find(ele => helper.IsEmptyString(ele.taxID));
                    if (customerInfo) {
                        this.setState({
                            gender: customerInfo.gender,
                            customerPhone: customerInfo.customerPhone,
                            customerName: isLockName
                                ? customerName
                                : customerInfo.customerName,
                            customerAddress: isLockAddress
                                ? customerAddress
                                : customerInfo.customerAddress,
                            customerIDCard: customerInfo.customerIDCard
                        }, () => {
                            this.handleAPIGetCustomerProfile(customerInfo.customerPhone);
                        });
                    }
                }
            }).catch(error => {
                console.log("getOldCustomerInfo error", error);
            });
        }
    };

    getOldCompanyInfo = () => {
        const {
            taxID,
            customerPhone,
            customerName,
            customerAddress,
            isLockPhone,
            isLockName,
            isLockAddress,
        } = this.state;
        if (taxID) {
            // this.getCompanyProfile(taxID);
        }
        else {
            storageHelper.getItem(STORAGE_CONST.CUSTOMER_INFO).then(result => {
                if (helper.IsNonEmptyString(result)) {
                    const dataTopInfo = JSON.parse(result);
                    const customerInfo = dataTopInfo.find(ele => helper.IsNonEmptyString(ele.taxID));
                    if (customerInfo) {
                        this.setState({
                            taxID: customerInfo.taxID,
                            customerPhone: isLockPhone
                                ? customerPhone
                                : customerInfo.customerPhone,
                            customerName: isLockName
                                ? customerName
                                : customerInfo.customerName,
                            customerAddress: isLockAddress
                                ? customerAddress
                                : customerInfo.customerAddress,
                            customerIDCard: customerInfo.customerIDCard,
                            gender: customerInfo.contactGender,
                            contactAddress: customerInfo.deliveryAddress,
                            contactPhone: customerInfo.contactPhone,
                            contactName: customerInfo.contactName,
                        });
                    }
                }
            }).catch(error => {
                console.log("getOldCompanyInfo error", error);
            });
        }
    };

    getCartPromotion = () => {
        const { dataShoppingCart } = this.props;
        this.props.actionShoppingCart.getCartPromotion(dataShoppingCart);
    };

    getMultiSalePromotion = () => {
        const { discountCode } = this.state;
        const { dataShoppingCart } = this.props;
        const isApplyCoupon = (dataShoppingCart.SHCouponDiscountAmount > 0);
        setTimeout(() => {
            showBlockUI();
            this.props.actionEditSaleOrder.getMultiSalePromotion({
                "discountCode": isApplyCoupon ? discountCode : "",
                "cartRequest": dataShoppingCart,
                "giftCode": "",
                "promotionGroups": [],
            }).then(success => {
                hideBlockUI();
            });
        }, 200);
    };

    modifyShoppingCart = (data) => {
        showBlockUI();
        this.props.actionEditSaleOrder.modifySaleOrderCart(data).then(dataCart => {
            hideBlockUI();
            this.getMultiSalePromotion();
            if (data.isChangeQuantity) {
                this.setState({ isOriginQuantityChange: !this.state.isOriginQuantityChange });
            }
        }).catch(error => {
            Alert.alert(translate('common.notification_uppercase'), error.msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: () => this.modifyShoppingCart(data)
                    }
                ]
            );
        });
    };

    removeItemMainProduct = (SaleOrderDetails, indexProduct) => () => {
        const isResetCart = (SaleOrderDetails.length == 1);
        if (isResetCart) {
            this.props.navigation.navigate('SearchProduct');
            const { dataShoppingCart } = this.props;
            const { customerPhone } = this.state;
            const cartShoppingModify = helper.deepCopy(dataShoppingCart);
            cartShoppingModify.SaleOrderDetails = [];
            cartShoppingModify.SaleOrderPromotionDiscounts = [];
            cartShoppingModify.giftShoppingCartSaleOrders = [];
            cartShoppingModify.saleShoppingCartSaleOrders = [];
            this.props.actionEditSaleOrder.deleteSaleOrderCart(cartShoppingModify);
            this.props.actionEditSaleOrder.set_apply_phone(customerPhone);
        }
        else {
            const { dataShoppingCart } = this.props;
            const cartShoppingModify = helper.deepCopy(dataShoppingCart);
            cartShoppingModify.SaleOrderDetails = SaleOrderDetails.filter(
                (saleOrder, position) => position != indexProduct
            );
            cartShoppingModify.cus_PromDiscountExtraMaster = null;
            this.modifyShoppingCart({
                cartRequest: cartShoppingModify,
                discountCode: "",
                giftCode: "",
                promotionGroups: []
            });
        }
    };

    removeItemSaleProduct = (data, fieldName, indexProduct) => {
        const { dataShoppingCart } = this.props;
        const cartShoppingModify = helper.deepCopy(dataShoppingCart);
        cartShoppingModify.SaleOrderDetails[indexProduct][fieldName] = data;
        cartShoppingModify.cus_PromDiscountExtraMaster = null;
        this.modifyShoppingCart({
            cartRequest: cartShoppingModify,
            discountCode: "",
            giftCode: "",
            promotionGroups: []
        });
    };

    onUpdateSimProduct = (data, fieldName, indexProduct) => {
        const { dataShoppingCart } = this.props;
        const cartShoppingModify = helper.deepCopy(dataShoppingCart);
        cartShoppingModify.SaleOrderDetails[indexProduct][fieldName] = data;
        this.modifyShoppingCart({
            cartRequest: cartShoppingModify,
            discountCode: "",
            giftCode: "",
            promotionGroups: []
        });
    };

    checkDiscountCode = () => {
        Keyboard.dismiss();
        const { discountCode, customerPhone } = this.state;
        const { dataShoppingCart } = this.props;
        const { SaleOrderDetails } = dataShoppingCart;
        const isValidateCode = helper.IsNonEmptyString(discountCode);
        const isValidateAdjust = checkApplyCoupon(SaleOrderDetails);
        if (!isValidateCode) {
            Alert.alert("", translate('editSaleOrder.please_enter_coupon'));
        }
        else if (!isValidateAdjust) {
            Alert.alert("", translate('editSaleOrder.notification_update_cart'));
        }
        else {
            const cartRequest = {
                ...dataShoppingCart,
                "CustomerInfo": {
                    "CustomerIDCard": "",
                    "CustomerPhone": customerPhone,
                },
                "CustomerPhone": customerPhone
            };
            this.applyDiscountCode({
                cartRequest: cartRequest,
                discountCode: discountCode,
                giftCode: "",
                promotionGroups: []
            });
        }
    };

    applyDiscountCode = (data) => {
        showBlockUI();
        this.props.actionEditSaleOrder.modifySaleOrderCart(data).then(dataCart => {
            hideBlockUI();
            this.getMultiSalePromotion();
        }).catch(error => {
            const { msgError } = error;
            if (msgError == 'IsRequirePhone') {
                hideBlockUI();
                this.setState({ isRequirePhone: true });
            }
            else {
                Alert.alert(translate('common.notification_uppercase'), msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: "cancel",
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: "default",
                            onPress: () => this.applyDiscountCode(data)
                        }
                    ]
                );
            }
        });
    };

    cancelDiscountCode = () => {
        const { dataShoppingCart } = this.props;
        const { hasConfigCoupon } = this.getDiscountCode();
        if (hasConfigCoupon) {
            this.setState({ discountCode: "" });
        }
        this.modifyShoppingCart({
            cartRequest: {
                ...dataShoppingCart,
                "CustomerInfo": null
            },
            discountCode: "",
            giftCode: "",
            promotionGroups: []
        });
    };

    applyCartPromotion = (promotionGroups, setKeyPromotion) => () => {
        const { discountCode } = this.state;
        const { dataShoppingCart } = this.props;
        const { SHCouponDiscountAmount } = dataShoppingCart;
        const isApplyCoupon = (SHCouponDiscountAmount > 0);
        showBlockUI();
        this.props.actionEditSaleOrder.modifySaleOrderCart({
            cartRequest: dataShoppingCart,
            discountCode: isApplyCoupon ? discountCode : "",
            giftCode: "",
            promotionGroups: promotionGroups
        }).then(dataCart => {
            hideBlockUI();
            this.setState({
                keyPromotion: setKeyPromotion,
                dataCartPromotion: promotionGroups
            });
        }).catch(error => {
            Alert.alert(translate('common.notification_uppercase'), error.msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: this.applyCartPromotion(promotionGroups, setKeyPromotion)
                    }
                ]
            );
        });
    };

    onPressAdjustPrice = (isApplyCoupon) => () => {
        if (isApplyCoupon) {
            Alert.alert("",
                translate('editSaleOrder.notification_cart_coupon')
            );
        }
        else {
            this.setState({
                totalPrePaid: 0,
                termLoan: 0,
                isInsuranceFee: false,
                isInsuranceGood: false,
                goodInsuranceID: 0,
                paymentMonthly: 0,
            });
            const { dataShoppingCart } = this.props;
            const dataAjust = helper.deepCopy(dataShoppingCart);
            this.props.actionEditSaleOrder.getDataAdjustPrice(dataAjust).then(
                success => {
                    this.props.navigation.navigate('OrderCartAdjustPrice');
                }
            );
        }
    };

    getdataCreatePrice = (dataCreate) => {
        showBlockUI();
        this.props.actionEditSaleOrder.getDataCreatePrice4SO(dataCreate).then(success => {
            hideBlockUI();
            this.props.navigation.navigate('OrderCreatePriceCart');
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: () => this.getdataCreatePrice(dataCreate)
                    }
                ]
            );
        });
    };

    onPressCreatePrice = (isApplyCoupon) => () => {
        if (isApplyCoupon) {
            Alert.alert("",
                translate('editSaleOrder.notification_cart_coupon_1')
            );
        }
        else {
            this.setState({
                totalPrePaid: 0,
                termLoan: 0,
                isInsuranceFee: false,
                isInsuranceGood: false,
                goodInsuranceID: 0,
                paymentMonthly: 0,
            });
            const { dataShoppingCart } = this.props;
            const dataCreate = helper.deepCopy(dataShoppingCart);
            this.getdataCreatePrice(dataCreate);
        }
    };

    checkValidateCustomerInfo = () => {
        const {
            customerPhone,
            customerName,
            customerAddress,
            relationShipType,
            taxID,
            isCompany,
            isHasSaleProgram,
            customerIDCard,
            totalPrePaid,
            termLoan,
            isHadCheck,
            isRequireCustomerInfo,
            dataContract,
            gender,
            candidateNo
        } = this.state;
        const {
            dataShoppingCart,
            stateCartPromotion,
            userInfo: { brandID, userName, storeID }
        } = this.props;
        const {
            SaleOrderDetails,
            IsAutoCreateEP,
            SaleOrderCusPromotion,
            cus_UrlFilesShoppingCart,
            cus_IsPromotionType19Exist,
            cus_IsRequireAttachmentSMSPromotion,
            IsCreateSO,
            cus_IsRequiredCandidateInput,
            cus_IsDisplayCandidateInput
        } = dataShoppingCart;
        const {
            TotalPrePaid,
            TermLoan,
            isPartnerConnectAPI
        } = dataContract;
        const isSupImages =
            helper.hasProperty(SaleOrderCusPromotion, "UrlFiles") ||
            helper.isArray(cus_UrlFilesShoppingCart);
        const saleProgramInfo = getSaleProgramInfo(dataShoppingCart);
        // const regExpPhone = new RegExp(/^[0]\d{8,9}$/);
        // const isValidatePhone = regExpPhone.test(customerPhone);
        const isValidatePhone = helper.isValidatePhone(customerPhone);
        const regExpTax10 = new RegExp(/^\d{10}$/);
        const regExpTax14 = new RegExp(/^\d{10}[-]\d{3}$/);
        const isValidateTax10 = regExpTax10.test(taxID);
        const isValidateTax14 = regExpTax14.test(taxID);
        const isValidateTax = isValidateTax10 || isValidateTax14;
        const regExpIDCard9 = new RegExp(/^\d{9}$/);
        const regExpIDCard12 = new RegExp(/^\d{12}$/);
        const isValidateIDCard9 = regExpIDCard9.test(customerIDCard);
        const isValidateIDCard12 = regExpIDCard12.test(customerIDCard);
        const isValidateIDCard = isValidateIDCard9 || isValidateIDCard12;
        const isRequireImei = checkRequireImeiSim(SaleOrderDetails);
        const isHadVinaSim = checkHadVinaSim(SaleOrderDetails);
        const isVisibleIDNumber = isExistVina(SaleOrderDetails);
        const checkVibeIDCard = isVisibleIDNumber || isHadVinaSim;
        const isHadViettelSim = checkHadViettelSim(SaleOrderDetails);
        const isTPBankEvo = saleProgramInfo.PartnerInstallmentID == 34;
        const isCakeQTV = saleProgramInfo.PartnerInstallmentID == 35;
        if (
            IsCreateSO &&
            !helper.checkConfigCreateSO(userName) &&
            !helper.checkConfigTimeSale(brandID) &&
            !helper.checkConfigTimeSalePreOrder(storeID)
        ) {
            Alert.alert("", "Bạn chỉ được phép thao tác chức năng này trong giờ bán hàng");
            return false;
        }
        if (stateCartPromotion.isError) {
            Alert.alert("", "Dữ liệu khuyến mãi tổng đơn không hợp lệ. Vui lòng kiểm tra lại.");
            return false;
        }
        if (isCompany) {
            if (!helper.IsNonEmptyString(taxID)) {
                Alert.alert("", translate('editSaleOrder.please_enter_tax_code'));
                return false;
            };
            if (!isValidateTax) {
                Alert.alert("", translate('editSaleOrder.validation_tax'));
                return false;
            };
            if (!helper.IsNonEmptyString(customerName)) {
                Alert.alert("", translate('editSaleOrder.validation_company_name'));
                return false;
            };
            // if (!helper.IsNonEmptyString(customerAddress)) {
            //     Alert.alert("", translate('editSaleOrder.validation_company_address'));
            //     return false;
            // };
        };
        if (!helper.IsNonEmptyString(customerName)) {
            Alert.alert("", translate('editSaleOrder.validation_customer_name'));
            return false;
        };
        if ((isHadViettelSim || isHadVinaSim) && !helper.IsNonEmptyString(customerIDCard)) {
            Alert.alert('', translate('shoppingCart.validation_ID_number'));
            return false;
        };
        if (isHadVinaSim && !isHadCheck) {
            Alert.alert('', translate('shoppingCart.validation_CARD_ID_number'));
            return false;
        };
        if (helper.IsNonEmptyString(customerPhone)) {
            if (!isValidatePhone) {
                Alert.alert("", translate('editSaleOrder.validation_phone_number'));
                return false;
            };
            if (!helper.isValidatePhonePrefix(customerPhone)) {
                Alert.alert("", translate('editSaleOrder.validation_phone_number_1'));
                return false;
            }
        };
        if (isRequireCustomerInfo) {
            const validCustomerInfo = customerInfoValidation({ customerAddress, customerName, customerPhone });
            if (!validCustomerInfo) {
                return false;
            }
        }
        if (helper.IsNonEmptyString(customerIDCard)) {
            if (!isValidateIDCard) {
                Alert.alert("", translate('editSaleOrder.validation_CMND_number'));
                return false;
            }
        };
        if (isSupImages) {
            if (
                !helper.IsNonEmptyString(customerIDCard) &&
                cus_IsPromotionType19Exist
            ) {
                // Chỉ kiểm tra CMND khi có tham gia KM 19
                Alert.alert("", translate('editSaleOrder.validation_CMND_rim'));
                return false;
            };
            let isValidStudentImage = true;
            let isValidStudentCouponImage = true;
            if (cus_IsPromotionType19Exist) {
                isValidStudentImage = validateStudentImages(
                    SaleOrderCusPromotion.UrlFiles
                );
            }
            if (cus_IsRequireAttachmentSMSPromotion) {
                if (cus_IsRequiredCandidateInput || !cus_IsDisplayCandidateInput) {
                    isValidStudentCouponImage = validateStudentCouponImages(
                        cus_UrlFilesShoppingCart
                    );
                }
                if (cus_IsRequiredCandidateInput && cus_IsDisplayCandidateInput && !helper.IsNonEmptyString(candidateNo)) {
                    Alert.alert("", "Vui lòng nhập số báo danh");
                    return false;
                }
                if (helper.IsNonEmptyString(candidateNo) && !this.isValidCandidateNo) {
                    Alert.alert("", "Số báo danh nhập vào không khớp với số báo danh phát hành coupon");
                    return false;
                }
            }
            const isEnoughFile = isValidStudentImage && isValidStudentCouponImage;
            if (!isEnoughFile) {
                Alert.alert("", translate('shoppingCart.validation_image'));
                return false;
            }
        }
        if (isHasSaleProgram) {
            const {
                MinPrepaid,
                MaxPrepaid,
                IsCardPartner
            } = saleProgramInfo;
            if (!helper.IsNonEmptyString(customerPhone)) {
                Alert.alert("", translate('editSaleOrder.validation_phone_number_rim'));
                return false;
            };
            if (!IsCardPartner) {
                if (!helper.IsNonEmptyString(customerIDCard)) {
                    Alert.alert("", translate('editSaleOrder.validation_CMND_rim'));
                    return false;
                };
            }
            if (TermLoan === 0 && (isTPBankEvo || isCakeQTV)) {
                Alert.alert("", translate('shoppingCart.please_choose_term_loan'));
                return false;
            }
            if (IsAutoCreateEP) {
                const isValidateMin = MinPrepaid <= totalPrePaid;
                const isValidateMax = totalPrePaid <= MaxPrepaid;
                const isValidatePrePaid = isValidateMin && isValidateMax;
                if (!isValidatePrePaid) {
                    Alert.alert("", translate('editSaleOrder.validation_prepayment'));
                    return false;
                }
                if (termLoan == 0) {
                    Alert.alert("", translate('editSaleOrder.please_choose_term_loan'));
                    return false;
                }
            }
            if (isPartnerConnectAPI) {
                if (TermLoan === undefined) {
                    Alert.alert("", translate('shoppingCart.please_choose_term_loan'));
                    return false;
                }
                if (TotalPrePaid === undefined || helper.IsEmptyString(TotalPrePaid)) {
                    Alert.alert("", translate('shoppingCart.pls_enter_total_prepaid'));
                    return false;
                }
            }
        }
        if (relationShipType == null) {
            Alert.alert("", translate('editSaleOrder.please_choose_relation_ship_type'));
            return false;
        };
        if (isRequireImei) {
            Alert.alert("", translate('editSaleOrder.please_enter_information_IMEI_SIM'));
            return false;
        }
        if (gender == null) {
            Alert.alert("", translate('shoppingCart.validation_gender'));
            return false;
        }
        if (!this.state.isSelectedPolicy && helper.IsNonEmptyString(customerPhone) && !isCompany) {
            Alert.alert("", "Khách hàng vui lòng đồng ý với chính sách xử lý dữ liệu cá nhân.");
            return false;
        }
        return true;
    };

    /// check số lần hữu dụng
    handleCheckValue = (data) => {
        Alert.alert(
            translate('common.notification_uppercase'),
            data.Message,
            [
                {
                    text: translate('common.btn_accept'),
                    style: 'cancel',
                    onPress: hideBlockUI
                },

            ]
        );
    };
    /// callAPI check điều kiện giáy tờ
    handleCheckDocument = () => {
        const {
            customerIDCard,
        } = this.state;
        const { dataShoppingCart } = this.props;
        const { SaleOrderDetails } = dataShoppingCart;
        const isNonEmpty = helper.IsNonEmptyArray(SaleOrderDetails);
        const regExpIDCard9 = new RegExp(/^\d{9}$/);
        const regExpIDCard12 = new RegExp(/^\d{12}$/);
        const isValidateIDCard9 = regExpIDCard9.test(customerIDCard);
        const isValidateIDCard12 = regExpIDCard12.test(customerIDCard);
        const isValidateIDCard = isValidateIDCard9 || isValidateIDCard12;
        if (isNonEmpty) {
            if (!isValidateIDCard) {
                Alert.alert("", translate('shoppingCart.validation_CMND_number'));
                return false;
            }
            else {
                const quantitySimVina = SaleOrderDetails.filter(item => item.BrandIDOfSIM === '125').length;
                showBlockUI();
                this.props.actionShoppingCart
                    .checkDocumentInformation({
                        "customerIDCard": customerIDCard,
                        "BrandID": "125",
                        "Quantity": quantitySimVina,
                    })
                    .then((data) => {
                        hideBlockUI();
                        this.setState({ isHadCheck: true });
                        this.handleCheckValue(data);
                    })
                    .catch((error) => {
                        Alert.alert(
                            translate('common.notification_uppercase'),
                            error.msgError,
                            [
                                {
                                    text: translate('common.btn_skip'),
                                    style: 'cancel',
                                    onPress: hideBlockUI
                                },
                                {
                                    text: translate('common.btn_notify_try_again'),
                                    style: 'default',
                                    onPress: () => this.handleCheckDocument()
                                }
                            ]
                        );
                    });
            }

        }
    };

    checkCandidateNo = () => {
        showBlockUI();
        const { candidateNo, discountCode } = this.state;
        this.props.actionShoppingCart.checkCandidateDiscount({ candidateNo, discountCode }).then(() => {
            hideBlockUI();
            this.isValidCandidateNo = true;
        }).catch(msgError => {
            Alert.alert('Thông báo', msgError, [{ onPress: hideBlockUI }]);
        });
    }

    onCreateSaleOrder = () => {
        if (this.onFocus) return Keyboard.dismiss();
        Keyboard.dismiss();
        const {
            gender,
            customerPhone,
            customerName,
            customerAddress,
            relationShipType,
            taxID,
            contactPhone,
            contactName,
            contactAddress,
            isCompany,
            isHasSaleProgram,
            customerIDCard,
            totalPrePaid,
            termLoan,
            isInsuranceFee,
            isInsuranceGood,
            goodInsuranceID,
            paymentMonthly,
            note,
            isVisibleVoucher,
            dataContract,
            contractID,
            keyPromotion,
            candidateNo
        } = this.state;
        const { dataShoppingCart, cartPromotion, voucherPromotion } = this.props;
        const dataCart = { ...dataShoppingCart }
        const {
            TotalPointLoyalty,
            CustomerInfo,
            DeliveryTypeID,
            IsAutoCreateEP,
            SHAmount,
            ShippingCost,
            IsSOAnKhang
        } = dataCart;
        const {
            ContactPhone,
            ContactName,
            ContactGender,
            DeliveryAddress,
        } = CustomerInfo;
        const isVisibleContact = (DeliveryTypeID == 1);
        const isChangeContact = (isCompany && isVisibleContact);
        const isWarningPhone = !helper.IsNonEmptyString(customerPhone);
        const alwaysPromotion = cartPromotion.filter(ele => !ele.invisibleByCustomerPhone);
        const dataPromotion = isVisibleVoucher
            ? [...alwaysPromotion, ...voucherPromotion]
            : alwaysPromotion;
        const {
            isValidatePromotion,
            msgRequirePromotion,
            isWarningPromotion
        } = getMsgRequirePromotion(dataPromotion, keyPromotion);
        const msgWarningQuantity = checkMaxQuantity(dataCart);
        let msgConfirm = "";
        if (isWarningPhone && !IsSOAnKhang) {
            msgConfirm = translate('editSaleOrder.warning_no_enter_phone_number_customer');
        }
        else if (isWarningPromotion) {
            msgConfirm = translate('editSaleOrder.warning_no_choose_promotion');
        }
        const isValidateCustomerInfo = this.checkValidateCustomerInfo();
        const promoRequireInfors = this.getPromoRequireInfors(dataCart);

        if (isValidateCustomerInfo) {
            if (!!msgWarningQuantity) {
                Alert.alert("", msgWarningQuantity);
            }
            else if (!isValidatePromotion) {
                Alert.alert(translate('editSaleOrder.validation_promotion'), msgRequirePromotion);
            }
            else if (checkDataInvalid(promoRequireInfors)) {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    `Vui lòng chụp hình ảnh bổ sung`,
                    [

                        {
                            text: "OK",
                            style: 'cancel',
                            onPress: () => this.props.navigation.navigate("FormPromotion", { promoRequireInfors: promoRequireInfors })

                        }
                    ]
                );
            }
            else {
                if (isHasSaleProgram) {
                    const saleProgramInfo = getSaleProgramInfo(dataCart);
                    const { IsCardPartner } = saleProgramInfo;
                    const totalAmount = (SHAmount - ShippingCost);
                    const newSaleProgramInfo = IsAutoCreateEP
                        ? {
                            ...saleProgramInfo,
                            "customerIDCard": customerIDCard,
                            'TotalPrePaid': totalPrePaid,
                            "TermLoan": termLoan,
                            "IsSelectedLifeInsurance": isInsuranceFee,
                            "IsSelectedGoodsInsurance": isInsuranceGood,
                            "GoodsInsuranceID": goodInsuranceID,
                            "PaymentAmountMonthly": paymentMonthly
                        }
                        : {
                            ...saleProgramInfo,
                            ...dataContract,
                            "customerIDCard": customerIDCard,
                            'TotalPrePaid': (IsCardPartner && !helper.IsNonEmptyString(contractID)) ? totalAmount : dataContract.TotalPrePaid,
                        };
                    dataCart.SaleOrderDetails.forEach(saleOrder => {
                        saleOrder.SaleProgramInfo = newSaleProgramInfo;
                    });
                }
                dataCart.cus_CandidateNO = candidateNo;
                dataCart.SaleOrderDetails = dataCart.SaleOrderDetails.map(saleOrder =>
                    saleOrder.IMEITemp && !helper.IsNonEmptyString(saleOrder.IMEI)
                        ? { ...saleOrder, IMEI: saleOrder.IMEITemp }
                        : { ...saleOrder }
                );

                const dataSaleOrder = {
                    "customerInfo": {
                        "customerName": customerName,
                        "customerAddress": customerAddress || "x",
                        "customerPhone": customerPhone,
                        "taxID": taxID,
                        "gender": isCompany ? 1 : gender,
                        "contactName": isChangeContact ? contactName : ContactName,
                        "contactPhone": isChangeContact ? contactPhone : ContactPhone,
                        "deliveryAddress": isChangeContact ? contactAddress : DeliveryAddress,
                        "contactGender": isChangeContact ? gender : ContactGender,
                        "customerIDCard": customerIDCard,
                        "ageID": "",
                        "birthday": ""
                    },
                    "relationShip": {
                        "relationShipType": relationShipType
                    },
                    "cartRequest": {
                        ...dataCart,
                        "Note": note
                    },
                    "requestIDLoyalty": null,
                    "customerIDLoyalty": null,
                };
                if (msgConfirm) {
                    Alert.alert("", msgConfirm,
                        [
                            {
                                text: translate('common.btn_skip'),
                                style: "cancel",
                            },
                            {
                                text: translate('common.btn_continue'),
                                style: "default",
                                onPress: () => this.handleModifyCustomerrofile(dataSaleOrder, isCreate = true)
                            }
                        ]
                    );
                }
                else {
                    this.handleModifyCustomerrofile(dataSaleOrder, isCreate = true);
                }
            }
        }
    };
    handleModifyCustomerrofile = async (dataSaleOrder, isCreate) => {
        const { userInfo: { brandID } } = this.props
        if (!checkApplyDiscount(dataSaleOrder.cartRequest, brandID) && isCreate) {
            const isDiscount = await this.handleAPICheckScreensProtector(dataSaleOrder);
            if (isDiscount == null) return
            dataSaleOrder.cartRequest.CheckNumberOfSlot = isDiscount
        }
        const profile = await this.handleAPIProfile(isCreate, dataSaleOrder);
        hideBlockUI();
        if (!helper.IsNonEmptyArray(profile[TYPE_PROFILE.CUSTOMER])) {
            delete profile?.[TYPE_PROFILE.CUSTOMER];
        }
        if (!helper.IsNonEmptyArray(profile[TYPE_PROFILE.COMPANY])) {
            delete profile?.[TYPE_PROFILE.COMPANY];
        }
        if (!helper.IsNonEmptyArray(profile[TYPE_PROFILE.ADDRESS_RECEIVE])) {
            delete profile?.[TYPE_PROFILE.ADDRESS_RECEIVE];
        }
        if (!helper.IsNonEmptyArray(profile[TYPE_PROFILE.CUSTOMER_RECEIVE])) {
            delete profile?.[TYPE_PROFILE.CUSTOMER_RECEIVE];
        }
        if (isCreate) {
            let newProfile = { ...profile };
            dataSaleOrder.cartRequest.OldSaleOrderDetails.forEach(({ Profile }) => {
                if (!helper.IsEmptyObject(Profile)) {
                    Object.entries(newProfile).forEach(([key, value]) => {
                        if (`${TYPE_PROFILE.CUSTOMER_RECEIVE},${TYPE_PROFILE.ADDRESS_RECEIVE}`.includes(`${key}`)) {
                            for (let index = 0; index < value.length; index++) {
                                value[index].relationShipTypeId = Profile?.[key]?.[0].relationShipTypeId;
                            }
                            if (helper.IsNonEmptyArray(Profile[TYPE_PROFILE.CUSTOMER]) && key == TYPE_PROFILE.CUSTOMER_RECEIVE) {
                                const profileContact = Profile[TYPE_PROFILE.CUSTOMER].find(item => item.customerInfoType == "CONTACT");
                                if (!helper.IsEmptyObject(profileContact)) {
                                    for (let index = 0; index < value.length; index++) {
                                        value[index].relationShipTypeId = profileContact.relationShipTypeId;
                                    }
                                }
                            }
                        }
                    });
                }
            });
            dataSaleOrder.cartRequest.SaleOrderDetails.forEach(saleOrderDetail => {
                saleOrderDetail.Profile = profile;
            });
            this.handleGetQuestion(dataSaleOrder);
        }
        else {
            let newProfile = { ...profile };
            dataSaleOrder.cartRequest.OldSaleOrderDetails.forEach(({ Profile }) => {
                if (!helper.IsEmptyObject(Profile)) {
                    Object.entries(newProfile).forEach(([key, value]) => {
                        if (`${TYPE_PROFILE.CUSTOMER_RECEIVE},${TYPE_PROFILE.ADDRESS_RECEIVE}`.includes(`${key}`)) {
                            for (let index = 0; index < value.length; index++) {
                                value[index].soProfileId = Profile?.[key]?.[0].soProfileId;
                            }
                            if (helper.IsNonEmptyArray(Profile[TYPE_PROFILE.CUSTOMER]) && key == TYPE_PROFILE.CUSTOMER_RECEIVE) {
                                const profileContact = Profile[TYPE_PROFILE.CUSTOMER].find(item => item.customerInfoType == "CONTACT");
                                if (!helper.IsEmptyObject(profileContact)) {
                                    for (let index = 0; index < value.length; index++) {
                                        value[index].soProfileId = profileContact.soProfileId;
                                    }
                                }
                            }
                        }
                    });
                }
            });
            /// Người mua là người nhận
            const customerInfo_typeContact = newProfile[TYPE_PROFILE.CUSTOMER_RECEIVE]?.[0];
            if (!helper.IsEmptyObject(customerInfo_typeContact) && customerInfo_typeContact?.isSameCustomer) {
                newProfile[TYPE_PROFILE.CUSTOMER] = [...newProfile[TYPE_PROFILE.CUSTOMER], {
                    ...newProfile[TYPE_PROFILE.CUSTOMER]?.[0], soProfileId: customerInfo_typeContact.soProfileId, customerInfoType: "CONTACT"
                }];
                delete newProfile?.[TYPE_PROFILE.CUSTOMER_RECEIVE];
            }
            dataSaleOrder.cartRequest.SaleOrderDetails.forEach(saleOrderDetail => {
                saleOrderDetail.Profile = newProfile;
            });
            this.updateSaleOrder(dataSaleOrder);

        }
    };
    handleAPIProfile = async (isCreate, dataSaleOrder) => {
        // showBlockUI()
        const {
            customerName,
            customerPhone,
            contactName,
            contactPhone,
            gender,
            isCompany,
            taxID,
            customerIDCard,
            customerAddress
        } = this.state;
        let profileAddressAndRecieve = {};
        let profileCustomer = {};
        let profileCompany = {};
        let extraCustomerConfirmPolicy = {};
        let profileModify = {};
        let isInsertAddress = false;
        let newPhoneNumber = isCompany ? contactPhone : customerPhone;
        // người mua là người nhận
        let isSameCustomer = true;

        const {
            userInfo: { storeID, languageID, moduleID }, customerConfirmPolicy, dataShoppingCart, numberPhoneCreateAtHome } = this.props;
        const {
            CustomerInfo: { CustomerPhone },
            DeliveryTypeID
        } = dataShoppingCart;
        const deliveryAtHome = dataShoppingCart.SaleOrderDetails.findIndex(saleorder => {
            const { DeliveryInfoRequest: {
                DeliveryTypeID } } = saleorder;
            return (DeliveryTypeID != 1);
        });
        const isVisibleContact = DeliveryTypeID == 1;
        let newProfile = { ...customerConfirmPolicy };
        if ((deliveryAtHome !== -1) && helper.IsNonEmptyString(newPhoneNumber)) {
            dataShoppingCart.SaleOrderDetails.forEach(({ Profile }) => {
                if (!helper.IsEmptyObject(Profile)) {
                    Object.entries(Profile).forEach(([key, value]) => {
                        if (key == TYPE_PROFILE.CUSTOMER_RECEIVE) {
                            isSameCustomer = false;
                            return;
                        }
                    });
                }

            });
        }
        if (deliveryAtHome === -1) {
            delete newProfile?.[TYPE_PROFILE.ADDRESS_RECEIVE];
            delete newProfile?.[TYPE_PROFILE.CUSTOMER_RECEIVE];
        }
        try {
            if (!helper.IsEmptyObject(newProfile)) {
                Object.entries(newProfile).forEach(([key, value]) => {
                    if (`${TYPE_PROFILE.CUSTOMER_RECEIVE},${TYPE_PROFILE.ADDRESS_RECEIVE}`.includes(`${key}`)) {
                        for (let index = 0; index < value.length; index++) {
                            isInsertAddress = value[index]?.profileId == null || value[index]?.profileId != newProfile[TYPE_PROFILE.CUSTOMER]?.[0]?.profileId;
                        }
                    }

                });
            }
            // if (helper.IsNonEmptyArray(customerConfirmPolicy[TYPE_PROFILE.CUSTOMER])) {
            const defaultCustomerInfo = {
                "customerName": "",
                "cardCustomerId": null,
                "gender": null,
                "profileId": 0,
                "type": 1,
                "versionCode": "",
                "phoneNumber": "",
                "isModify": 0,
                "isSigned": 0,
                "signatureId": 0,
                "soProfileId": null,
                "relationshipTypeId": 0,
                "relationshipId": 0
            };
            const companyDefault = {
                "companyId": 0,
                "companyName": "",
                "companyPhone": null,
                "address": "",
                "email": null,
                "taxNo": "",
                "profileId": 0,
                "type": 5,
                "versionCode": "",
                "phoneNumber": null,
                "isModify": 0,
                "isSigned": 0,
                "signatureId": 0,
                "soProfileId": null,
                "relationshipTypeId": 0,
                "relationshipId": 0
            };
            const customerInfo = helper.IsNonEmptyArray(customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]) ? { ...customerConfirmPolicy[TYPE_PROFILE.CUSTOMER][0] } : defaultCustomerInfo;
            const oldInfoCustomer = {
                "customerName": customerInfo.customerName,
                "customerPhone": customerInfo.phoneNumber,
                "gender": customerInfo.gender,
                "cardCustomerId": customerInfo.cardCustomerId || ""
            };
            const infoCustomer = {
                "customerName": this.state.customerName,
                "customerPhone": this.state.customerPhone,
                "gender": this.state.gender,
                "cardCustomerId": this.state.customerIDCard
            };
            const infoCustomerContact = {
                "customerName": this.state.contactName,
                "customerPhone": this.state.contactPhone,
                "gender": this.state.gender,
                "cardCustomerId": this.state.customerIDCard
            };
            const infoSignCustomer = isCompany ? (isVisibleContact ? infoCustomerContact : oldInfoCustomer) : infoCustomer;
            const hasChangeValueProfile = helper.checkChangeValueOfPrototype(infoSignCustomer, oldInfoCustomer);
            if ((hasChangeValueProfile || !customerInfo.isSigned)) {
                customerInfo.isModify = 1;
                if (customerInfo.phoneNumber != newPhoneNumber) {
                    customerInfo.versionCode = "";
                    customerInfo.profileId = 0;
                }
                customerInfo.customerName = isCompany ? contactName : customerName;
                customerInfo.phoneNumber = isCompany ? contactPhone : customerPhone;
                customerInfo.gender = typeof gender == "boolean" ? (gender ? 1 : 0) : gender;
                customerInfo.cardCustomerId = customerIDCard;
                profileCustomer = { [TYPE_PROFILE.CUSTOMER]: [customerInfo] };
                newProfile[TYPE_PROFILE.CUSTOMER] = await newProfile[TYPE_PROFILE.CUSTOMER].map((profileCustomer) => {
                    return { ...profileCustomer, phoneNumber: newPhoneNumber };
                });
            }
            // }
            if (isCompany) {
                const companyInfo = helper.IsNonEmptyArray(customerConfirmPolicy[TYPE_PROFILE.COMPANY]) ? { ...customerConfirmPolicy[TYPE_PROFILE.COMPANY][0] } : companyDefault;
                const { companyName, companyPhone, taxNo, address } = companyInfo;
                const profiles = dataSaleOrder.cartRequest.SaleOrderDetails?.[0]?.Profile;
                if (companyName != customerName || address != customerAddress || taxNo != taxID) {
                    companyInfo.isModify = 1;
                    companyInfo.companyName = customerName;
                    companyInfo.address = customerAddress;
                    companyInfo.taxNo = taxID;
                    companyInfo.companyId = profiles?.[TYPE_PROFILE.COMPANY]?.[0]?.companyId;
                    profileCompany = { [TYPE_PROFILE.COMPANY]: [companyInfo] };
                }
            }
            // insert địa chỉ củ của giao về nhà cho khách hàng mới nếu như thay đổi số điện thoại
            if ((deliveryAtHome !== -1) && helper.IsNonEmptyString(newPhoneNumber)) {
                dataShoppingCart.SaleOrderDetails.forEach(({ SaleOrderDetailID }) => {
                    Object.entries(customerConfirmPolicy).forEach(([key, value]) => {
                        if (`${TYPE_PROFILE.CUSTOMER_RECEIVE},${TYPE_PROFILE.ADDRESS_RECEIVE}`.includes(`${key}`)) {
                            for (let index = 0; index < value.length; index++) {
                                if (isInsertAddress) {
                                    if (key == TYPE_PROFILE.ADDRESS_RECEIVE) {
                                        value[index].profileId = customerInfo.profileId;
                                        value[index].deliveryId = 0;
                                        value[index].isModify = 0;
                                    }
                                    if (key == TYPE_PROFILE.CUSTOMER_RECEIVE) {
                                        value[index].profileId = customerInfo.profileId;
                                        value[index].receiverId = 0;
                                        value[index].isModify = 0;
                                    }
                                    value[index].soProfileId = SaleOrderDetailID;
                                    profileAddressAndRecieve = { ...profileAddressAndRecieve, [key]: [...profileAddressAndRecieve?.[key] ?? [], value[index]] };
                                }
                            }
                        }

                    });
                });
            }
            const profileRequest = {
                ...profileAddressAndRecieve, ...profileCompany, ...profileCustomer
            };
            if (!helper.IsEmptyObject(profileRequest)) {
                const body = {
                    "loginStoreId": storeID,
                    "languageID": languageID,
                    "moduleID": moduleID,
                    "profile": profileRequest
                };
                profileModify = await actionShoppingCartCreator.modifyCustomerProfile(body);
            }
            newProfile[TYPE_PROFILE.CUSTOMER] = profileModify[TYPE_PROFILE.CUSTOMER] || newProfile[TYPE_PROFILE.CUSTOMER]?.filter((_, index) => (index == 0)) || [];
            newProfile[TYPE_PROFILE.COMPANY] = profileModify[TYPE_PROFILE.COMPANY] || newProfile[TYPE_PROFILE.COMPANY]?.filter((_, index) => (index == 0)) || [];
            newProfile[TYPE_PROFILE.ADDRESS_RECEIVE] = profileModify[TYPE_PROFILE.ADDRESS_RECEIVE] || newProfile[TYPE_PROFILE.ADDRESS_RECEIVE] || [];
            newProfile[TYPE_PROFILE.CUSTOMER_RECEIVE] = profileModify[TYPE_PROFILE.CUSTOMER_RECEIVE] || newProfile[TYPE_PROFILE.CUSTOMER_RECEIVE] || [];
            /// Người mua là người nhận phải đổi thông tin của contact

            if (isSameCustomer && !helper.IsEmptyObject(newProfile[TYPE_PROFILE.CUSTOMER]?.[0])) {
                const { customerName, phoneNumber, gender } = newProfile[TYPE_PROFILE.CUSTOMER][0];
                dataSaleOrder.cartRequest.SaleOrderDetails.forEach(saleOrderDetail => {
                    saleOrderDetail.DeliveryInfoRequest = {
                        ...saleOrderDetail.DeliveryInfoRequest,
                        ContactGender: gender,
                        ContactName: customerName,
                        ContactPhone: phoneNumber
                    };
                });
                dataSaleOrder.customerInfo = {
                    ...dataSaleOrder.customerInfo,
                    contactName: customerName,
                    contactPhone: phoneNumber,
                    contactGender: gender,
                };
            }
            if (isCreate) return newProfile;
            let profileSOIDOfCompany = "";
            /// lây dữ liệu cho thông tin khách hàng cônng ty
            if (helper.IsNonEmptyArray(newProfile[TYPE_PROFILE.COMPANY])) {
                const profiles = dataSaleOrder.cartRequest.SaleOrderDetails?.[0]?.Profile;
                const newProfileCompany = await newProfile[TYPE_PROFILE.COMPANY].map((profileCompany) => {
                    return { ...profileCompany, soProfileId: profiles?.[TYPE_PROFILE.COMPANY]?.[0]?.soProfileId };
                })?.[0];
                newProfile[TYPE_PROFILE.COMPANY] = [newProfileCompany];
            }
            if (helper.IsNonEmptyArray(newProfile[TYPE_PROFILE.CUSTOMER])) {
                /// lây dữ liệu cho thông tin khách hàng
                const profiles = dataSaleOrder.cartRequest.OldSaleOrderDetails?.[0]?.Profile;
                if (!helper.IsEmptyObject(profiles) && helper.IsNonEmptyArray(profiles[TYPE_PROFILE.CUSTOMER])) {
                    newProfile[TYPE_PROFILE.CUSTOMER] = await profiles[TYPE_PROFILE.CUSTOMER].map((profileCustomer) => {
                        return { ...newProfile[TYPE_PROFILE.CUSTOMER][0], soProfileId: profileCustomer.soProfileId, customerInfoType: profileCustomer.customerInfoType };
                    }).filter((item) => {
                        if (helper.IsNonEmptyArray(newProfile[TYPE_PROFILE.COMPANY])) {
                            if (item.customerInfoType == "INVOICECUSTOMER") {
                                profileSOIDOfCompany = item.soProfileId;
                            }
                            return item.customerInfoType == "CUSTOMER";
                        }
                        else {
                            return item;
                        }
                    });
                    if (helper.IsNonEmptyArray(newProfile[TYPE_PROFILE.COMPANY]) && !!profileSOIDOfCompany) {
                        const newProfileCompany = await newProfile[TYPE_PROFILE.COMPANY].map((profileCompany) => {
                            return { ...profileCompany, soProfileId: profileSOIDOfCompany };
                        })?.[0];
                        newProfile[TYPE_PROFILE.COMPANY] = [newProfileCompany];
                    }
                    const customerInfo_typeCompany = profiles[TYPE_PROFILE.COMPANY]?.[0];
                    if (!helper.IsEmptyObject(customerInfo_typeCompany)) {
                        newProfile[TYPE_PROFILE.CUSTOMER] = [...newProfile[TYPE_PROFILE.CUSTOMER], {
                            ...newProfile[TYPE_PROFILE.CUSTOMER]?.[0], soProfileId: customerInfo_typeCompany.soProfileId, customerInfoType: "INVOICECUSTOMER"
                        }];
                    }
                }
            }
            if (!hasChangeValueProfile && !this.isChangeCustomer.current) {
                delete newProfile?.[TYPE_PROFILE.CUSTOMER];
            }
            if (!this.state.isCompany) {
                delete newProfile?.[TYPE_PROFILE.COMPANY];
            }
            return newProfile;
        } catch (error) {
            console.log("🚀 ~ ShoppingCart ~ handleAPIProfile= ~ error:", error);
            return {};
        } finally {
            hideBlockUI();
        }

    };
    handleGetQuestion = async (dataSaleOrder) => {
        const questions = await this.handleAnswerQuestion(dataSaleOrder);
        if (!helper.IsEmptyObject(questions)) {
            this.setState({ isShowModalQuestion: true, tempSaleOrders: dataSaleOrder, questionList: questions });
        }
        else {
            this.checkValidPromotion(dataSaleOrder);
        }

    };
    handleAnswerQuestion = async (dataSaleOrder) => {
        showBlockUI();
        try {
            const {
                userInfo: { storeID, languageID, moduleID } } = this.props;
            const body = {
                "loginStoreId": storeID,
                "languageID": languageID,
                "moduleID": moduleID,
                "shoppingCartRequest": dataSaleOrder.cartRequest
            };
            let questionList = await actionShoppingCartCreator.getQuestions(body);
            if (!helper.IsEmptyObject(this.state.questionList)) {
                questionList = this.state.questionList;
                Object.entries(questionList).forEach(([key, value]) => {
                    if (!Object.keys(this.state.questionList).includes(key)) {
                        questionList = { ...this.state.questionList, key: value };
                    }

                });
            } return questionList;
        } catch (error) {
            console.log("🚀 ~ ShoppingCart ~ handleAnswerQuestion= ~ error:", error);
            return {};
        }
    };
    handleChangeAnswer = ({ questionId, keyQuestion }) => (answerSelected) => {
        const newQuestionList = { ...this.state.questionList };
        for (const [key, value] of Object.entries(newQuestionList)) {
            if (keyQuestion == key) {
                value[questionId]?.answers?.forEach((item) => {
                    if (item.answerId == answerSelected.answerId) {
                        item.isSelected = 1;
                    }
                    else {
                        item.isSelected = 0;
                    }
                });
            }

        }
        this.setState({ questionList: newQuestionList });
    };

    applyAnswerQuestion = () => {
        if (this.validateAnswer()) {
            this.setState({ isShowModalQuestion: false }, () => {
                const dataSaleOrder = this.state.tempSaleOrders;
                dataSaleOrder.cartRequest.QuestionData = this.state.questionList;
                this.checkValidPromotion(dataSaleOrder);
            });
        }

    };
    validateAnswer = () => {
        for (const [key, questions] of Object.entries(this.state.questionList)) {
            for (const [key, value] of Object.entries(questions)) {
                const newAnswer = value.answers.filter((answers) => !answers.isSelected);
                if (newAnswer.length == value.answers.length) {
                    Alert.alert("", `Bạn chưa chọn câu trả lời cho câu hỏi ${value.questionValue}`);
                    return false;
                }
            }
        }
        return true;
    };
    checkValidPromotion = (dataSaleOrder) => {
        const { cartRequest } = dataSaleOrder;
        const { SaleOrderDetails } = cartRequest;
        const outputType = getOutputType(SaleOrderDetails);
        const { dataCartPromotion } = this.state;
        const outputTypeIDList = outputType.filter((item,
            index) => outputType.indexOf(item) === index);
        const promotionIDList = dataCartPromotion.map((item) => (item.promotionID).toString());
        const checkMultiSalePromotion = !helper.IsNonEmptyArray(dataCartPromotion);
        if (checkMultiSalePromotion) {
            this.onCheckInOutVoucher(dataSaleOrder);
        }
        else {
            showBlockUI();
            this.props.actionShoppingCart.checkValidPromotion({ promotionIDList, outputTypeIDList })
                .then(res => {
                    const { Message } = res;
                    let checkMessagePromotion = !helper.IsNonEmptyString(Message);
                    if (checkMessagePromotion) {
                        this.onCheckInOutVoucher(dataSaleOrder);
                    }
                    else {
                        Alert.alert(translate('common.notification_uppercase'), Message,
                            [{
                                text: "OK",
                                style: "default",
                                onPress: () => this.modifyShoppingCart({
                                    cartRequest: cartRequest,
                                    discountCode: "",
                                    giftCode: "",
                                    promotionGroups: []
                                })
                            }]
                        );
                    }
                }).catch(msgError => {
                    Alert.alert(translate('common.notification_uppercase'), msgError.msgError,
                        [{
                            text: "OK",
                            style: "default",
                            onPress: hideBlockUI
                        }]
                    );
                });
        }
    };

    checkGiftVIP = (dataSaleOrder) => {
        const { cartRequest } = dataSaleOrder;
        const {
            cartRequest: { IsSenOTPConfirmCustomer, SaleOrderDetails, CheckNumberOfSlot },
        } = dataSaleOrder;
        const { userInfo: { storeID } } = this.props
        const isLoyalty = cartRequest.IsAllowParticipationLoyalty;
        // const hasAdjustPrice = !checkApplyCoupon(SaleOrderDetails)
        const hasAdjustPrice = helper.isNewPricingCampaign(SaleOrderDetails, storeID)
        if (CheckNumberOfSlot || hasAdjustPrice) {
            this.setState({ tempSaleOrders: dataSaleOrder, typeOTP: hasAdjustPrice ? "ADJUSTPRICEGIFTVOUCHER" : "SCREENPROTECTOR" }, () => {
                this.OTPSheetRef.current?.present()
            })
        } else
            if (isLoyalty) {
                this.onCheckLoyaltyPoint(dataSaleOrder);
            }
            else {
                this.getDataMemberPoint(dataSaleOrder);
            }
    };


    onCheckCancelEP = (dataSaleOrder) => {
        const { cartRequest } = dataSaleOrder;
        const { IsConfirmCancelEP } = cartRequest;
        if (IsConfirmCancelEP) {
            this.dataCancelEP = dataSaleOrder;
            this.getReasonCancelEP(cartRequest);
        }
        else {
            this.checkGiftVIP(dataSaleOrder);
        }
    };

    onUpdateSaleOrder = () => {
        if (this.onFocus) return Keyboard.dismiss();
        Keyboard.dismiss();
        const {
            gender,
            customerPhone,
            customerName,
            customerAddress,
            relationShipType,
            taxID,
            contactPhone,
            contactName,
            contactAddress,
            isCompany,
            customerIDCard,
            note,
            isHasSaleProgram,
            totalPrePaid,
            termLoan,
            isInsuranceFee,
            isInsuranceGood,
            goodInsuranceID,
            paymentMonthly,
            dataContract,
            contractID,
            candidateNo,
            saleUserInfo
        } = this.state;
        const { dataShoppingCart } = this.props;
        const dataCart = { ...dataShoppingCart }
        const {
            CustomerInfo,
            DeliveryTypeID,
            IsAutoCreateEP,
            SHAmount,
            ShippingCost,
            IsSOAnKhang
        } = dataCart;
        const {
            ContactPhone,
            ContactName,
            ContactGender,
            DeliveryAddress,
        } = CustomerInfo;
        const isVisibleContact = (DeliveryTypeID == 1);
        const isChangeContact = (isCompany && isVisibleContact);
        const isValidateCustomerInfo = this.checkValidateCustomerInfo();
        const isWarningPhone = !helper.IsNonEmptyString(customerPhone) && !IsSOAnKhang;
        const msgConfirm = isWarningPhone
            ? translate('editSaleOrder.warning_update_order_no_phone_number_customer')
            : "";
        if (isValidateCustomerInfo) {
            if (isHasSaleProgram) {
                const saleProgramInfo = getSaleProgramInfo(dataCart);
                const { IsCardPartner } = saleProgramInfo;
                const totalAmount = (SHAmount - ShippingCost);
                const newSaleProgramInfo = IsAutoCreateEP
                    ? {
                        ...saleProgramInfo,
                        "customerIDCard": customerIDCard,
                        'TotalPrePaid': totalPrePaid,
                        "TermLoan": termLoan,
                        "IsSelectedLifeInsurance": isInsuranceFee,
                        "IsSelectedGoodsInsurance": isInsuranceGood,
                        "GoodsInsuranceID": goodInsuranceID,
                        "PaymentAmountMonthly": paymentMonthly
                    }
                    : {
                        ...saleProgramInfo,
                        ...dataContract,
                        "customerIDCard": customerIDCard,
                        'TotalPrePaid': (IsCardPartner && !helper.IsNonEmptyString(contractID)) ? totalAmount : dataContract.TotalPrePaid,
                    };
                dataCart.SaleOrderDetails.forEach(saleOrder => {
                    saleOrder.SaleProgramInfo = newSaleProgramInfo;
                });
                dataCart.SaleOrderSaleProgramInfo = newSaleProgramInfo;
            }

            dataCart.SaleOrderDetails = dataCart.SaleOrderDetails.map(saleOrder =>
                saleOrder.IMEITemp && !helper.IsNonEmptyString(saleOrder.IMEI)
                    ? { ...saleOrder, IMEI: saleOrder.IMEITemp }
                    : { ...saleOrder }
            );

            dataCart.cus_CandidateNO = candidateNo;
            const dataSaleOrder = {
                "customerInfo": {
                    "customerName": customerName,
                    "customerAddress": customerAddress || "x",
                    "customerPhone": customerPhone,
                    "taxID": taxID,
                    "gender": gender,
                    "contactName": isChangeContact ? contactName : ContactName,
                    "contactPhone": isChangeContact ? contactPhone : ContactPhone,
                    "deliveryAddress": isChangeContact ? contactAddress : DeliveryAddress,
                    "contactGender": isChangeContact ? gender : ContactGender,
                    "customerIDCard": customerIDCard,
                    "ageID": "",
                    "birthday": ""
                },
                "relationShip": {
                    "relationShipType": relationShipType
                },
                "cartRequest": {
                    ...dataCart,
                    "Note": note,
                    "StaffUser": saleUserInfo?.UserName?.length > 0 ? saleUserInfo?.UserName : dataCart.StaffUser
                },
                "requestIDLoyalty": null,
                "customerIDLoyalty": null,
            };
            if (msgConfirm) {
                Alert.alert("", msgConfirm,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: "cancel",
                        },
                        {
                            text: translate('common.btn_continue'),
                            style: "default",
                            onPress: () => {
                                this.handleModifyCustomerrofile(dataSaleOrder);
                            }
                        }
                    ]
                );
            }
            else {
                this.handleModifyCustomerrofile(dataSaleOrder);
            }
        }
    };


    getDataLoyalty = (customerPhone, dataSaleOrder) => {
        const { isHasSaleProgram } = this.state;
        const { cartRequest: { IsAutoCreateEP, IsSenOTPConfirmCustomer } } = dataSaleOrder;
        if (isHasSaleProgram && IsSenOTPConfirmCustomer) {
            this.getDataInstallment(dataSaleOrder);
        }
        else {
            this.props.actionShoppingCart.checkCredentialExist(customerPhone, dataSaleOrder)
                .then(success => {
                    hideBlockUI();
                    this.props.navigation.navigate("OrderLoyalty");
                });
        }
    };

    onCheckLoyaltyPoint = (dataSaleOrder) => {
        const { isHasSaleProgram } = this.state;
        const {
            cartRequest: { TotalPointLoyalty, IsAutoCreateEP, cus_IsHasVoucherConcernAuthenticate, IsSenOTPConfirmCustomer },
            customerInfo: { customerPhone }
        } = dataSaleOrder;
        const isValidatePhone = helper.IsNonEmptyString(customerPhone);
        const isValidatePoint = (TotalPointLoyalty > 0);
        const isMoveToLoyalty = isValidatePoint && isValidatePhone && !cus_IsHasVoucherConcernAuthenticate;
        if (isHasSaleProgram && IsSenOTPConfirmCustomer) {
            this.getDataInstallment(dataSaleOrder);
        }
        else if (isMoveToLoyalty) {
            this.getDataLoyalty(customerPhone, dataSaleOrder);
        }
        else {
            this.addToSaleOrderCart(dataSaleOrder, true);
        }
    };

    addToSaleOrderCart = (data) => {
        showBlockUI();
        this.props.actionEditSaleOrder.createSaleOrderCart(data).then(orderInfo => {
            const { actionManagerSO, paramFilter } = this.props;
            actionManagerSO.getDataSearchSO(paramFilter);
            storageHelper.updateTopCustomerInfo(data.customerInfo);
            this.showAlertContent(orderInfo);
            this.props.actionShoppingCart.reset_map_customer_confirm_policy();
        }).catch(error => {
            const { errorType, msgError } = error;
            const newData = { ...data };
            newData.cartRequest.NotifyPre = msgError;
            if (errorType == SO_TYPE.LOCK_PRE) {
                Alert.alert(translate('common.notification_uppercase'), msgError, [
                    {
                        text: translate('common.btn_skip'),
                        onPress: hideBlockUI,
                    },
                    {
                        text: translate('common.btn_continue'),
                        onPress: () => this.addToSaleOrderCart(newData),
                    },
                ]);
            }
            else {
                Alert.alert(translate('common.notification_uppercase'), msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: "cancel",
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: "default",
                            onPress: () => this.addToSaleOrderCart(data)
                        }
                    ]
                );
            }
        });
    };

    showAlertContent = (orderInfo) => {
        let content = translate('editSaleOrder.create_order_successful');
        const { SaleOrders } = orderInfo;
        const { SaleOrderID, IsCheckBHRV, MesBHRV } = SaleOrders[0];
        if (helper.IsNonEmptyString(SaleOrderID)) {
            content += `\nMã SO: ${SaleOrderID}`;
        }
        if (IsCheckBHRV) {
            content += `\n${MesBHRV}`;
        }
        Alert.alert("", content,
            [
                {
                    text: "OK",
                    style: "default",
                    onPress: () => {
                        hideBlockUI();
                        this.props.navigation.navigate("OrderManagement");
                    }
                }
            ]
        );
    };

    updateSaleOrder = (data) => {
        showBlockUI();
        this.props.actionEditSaleOrder.updateSaleOrderCart(data).then(orderInfo => {
            hideBlockUI();
            const { actionManagerSO, paramFilter, navigation } = this.props;
            actionManagerSO.getDataSearchSO(paramFilter);
            storageHelper.updateTopCustomerInfo(data.customerInfo);
            this.showAlertEPContent(orderInfo);
            this.props.actionShoppingCart.reset_map_customer_confirm_policy();
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: () => this.updateSaleOrder(data)
                    }
                ]
            );
        });
    };

    showAlertEPContent = (orderInfo) => {
        let content = translate('editSaleOrder.update_order_successful');
        const { EPOSTransactionID, SaleOrderID } = orderInfo;
        if (helper.IsNonEmptyString(SaleOrderID)) {
            content += `\nMã SO: ${SaleOrderID}`;
        }
        if (helper.IsNonEmptyString(EPOSTransactionID)) {
            content += `\nMã EP: ${EPOSTransactionID}`;
        }
        Alert.alert("", content,
            [
                {
                    text: "OK",
                    style: "default",
                    onPress: () => {
                        hideBlockUI();
                        this.props.navigation.navigate("OrderManagement");
                    }
                }
            ]
        );
    };

    getDataMemberPoint = (dataSaleOrder) => {
        const { isHasSaleProgram } = this.state;
        const {
            cartRequest: { IsAutoCreateEP, IsSenOTPConfirmCustomer },
            customerInfo: { customerPhone }
        } = dataSaleOrder;
        const isValidatePhone = helper.IsNonEmptyString(customerPhone);
        if (isHasSaleProgram && IsSenOTPConfirmCustomer) {
            this.getDataInstallment(dataSaleOrder);
        }
        // else if (isValidatePhone) {
        //     showBlockUI();
        //     this.props.actionShoppingCart.getMemberPoint(dataSaleOrder).then(memberPointInfo => {
        //         const { decUsePoint } = memberPointInfo;
        //         if (decUsePoint > 0) {
        //             hideBlockUI();
        //             this.props.navigation.navigate("OrderMemberPoint");
        //         }
        //         else {
        //             this.addToSaleOrderCart(dataSaleOrder, false);
        //         }
        //     });
        // }
        else {
            this.addToSaleOrderCart(dataSaleOrder, true);
        }
    };

    getDataInstallment = (dataSaleOrder) => {
        showBlockUI();
        this.props.actionShoppingCart.getDataInstallment(dataSaleOrder)
            .then(success => {
                hideBlockUI();
                this.props.navigation.navigate("OrderInstallmentOTP");
            });
    };

    getDefaultCustomerInfo = () => {
        const { dataShoppingCart, route: { params } } = this.props;
        const {
            DisableApplyCustomerPhone: phoneValidate,
            CustomerInfo,
            RelationShipType,
            Note,
            DiscountCode,
            CustomerNote,
            cus_CandidateNO
        } = dataShoppingCart;
        const { profileSO } = params;
        let newCustomerInfo = {};
        if (helper.IsEmptyObject(params?.profileSO)) {
            newCustomerInfo = CustomerInfo;
        }
        else {
            const newCompanyPhone = profileSO[TYPE_PROFILE.COMPANY]?.companyPhone != CustomerInfo.CustomerPhone ? CustomerInfo.CustomerPhone : profileSO[TYPE_PROFILE.COMPANY]?.companyPhone;
            const newCustomerName = !!CustomerInfo.TaxID ? CustomerInfo.CustomerName : profileSO[TYPE_PROFILE.CUSTOMER]?.customerName;
            newCustomerInfo = {
                CustomerName: (!!profileSO[TYPE_PROFILE.COMPANY]?.taxNo ? profileSO[TYPE_PROFILE.COMPANY]?.companyName || "" : newCustomerName || "") || CustomerInfo.CustomerName,
                CustomerPhone: (!!profileSO[TYPE_PROFILE.COMPANY]?.taxNo ? newCompanyPhone || CustomerInfo.CustomerPhone : profileSO[TYPE_PROFILE.CUSTOMER]?.phoneNumber || "") || CustomerInfo.CustomerPhone,
                CustomerAddress: profileSO[TYPE_PROFILE.COMPANY]?.address || CustomerInfo.CustomerAddress,
                CustomerIDCard: profileSO[TYPE_PROFILE.CUSTOMER]?.cardCustomerId || CustomerInfo.CustomerIDCard,
                Gender: profileSO[TYPE_PROFILE.CUSTOMER]?.gender || CustomerInfo.Gender,
                TaxID: profileSO[TYPE_PROFILE.COMPANY]?.taxNo || CustomerInfo.TaxID,
                // ContactPhone: !!profileSO[TYPE_PROFILE.COMPANY]?.taxNo ? profileSO[TYPE_PROFILE.CUSTOMER]?.phoneNumber || CustomerInfo.ContactPhone : profileSO[TYPE_PROFILE.COMPANY]?.companyPhone || CustomerInfo.ContactPhone,
                ContactPhone: profileSO[TYPE_PROFILE.CUSTOMER]?.phoneNumber || CustomerInfo.ContactPhone,
                ContactName: !!profileSO[TYPE_PROFILE.COMPANY]?.taxNo ? profileSO[TYPE_PROFILE.CUSTOMER]?.customerName || CustomerInfo.ContactName : profileSO[TYPE_PROFILE.COMPANY]?.companyName || CustomerInfo.ContactName,
                CustomerAddress: profileSO[TYPE_PROFILE.COMPANY]?.address || CustomerInfo.CustomerAddress,
            };
        }

        const isLockPhone = !!phoneValidate;
        const {
            CustomerName,
            CustomerAddress,
            CustomerPhone,
            TaxID,
            Gender,
            ContactPhone,
            ContactName,
            ContactGender,
            DeliveryAddress,
            CustomerIDCard,
        } = newCustomerInfo;
        let defaultSaleProgram = {
            isHasSaleProgram: false,
            isLockCustomer: false,
            isLockTax: false,
            isLockName: false,
            isLockAddress: false,
            isAllowInvoice: true,
            totalPrePaid: 0,
            termLoan: 0,
            paymentMonthly: 0,
            isInsuranceFee: false,
            isInsuranceGood: false,
            goodInsuranceID: 0,
            //contract
            contactID: "",
            pgProcessUserID: "",
            pgProcessUserName: "",
            packageRates: ""
        };
        const saleProgramInfo = getSaleProgramInfo(dataShoppingCart);
        if (!helper.IsEmptyObject(saleProgramInfo)) {
            const {
                DisableTaxID,
                DisableCustomerAddress,
                DisableCustomerName,
                IsLockCustomer,
                IsAllowCompanyInvoice,
                TotalPrePaid,
                TermLoan,
                IsSelectedLifeInsurance,
                IsSelectedGoodsInsurance,
                GoodsInsuranceID,
                PaymentAmountMonthly,
                ContractID,
                PGProcessUserID,
                PGProcessUserName,
                packageRates
            } = saleProgramInfo;
            defaultSaleProgram = {
                isHasSaleProgram: true,
                isLockCustomer: IsLockCustomer,
                isLockTax: DisableTaxID,
                isLockName: DisableCustomerName,
                isLockAddress: DisableCustomerAddress,
                isAllowInvoice: IsAllowCompanyInvoice,
                totalPrePaid: TotalPrePaid || 0,
                termLoan: TermLoan || 0,
                isInsuranceFee: IsSelectedLifeInsurance,
                isInsuranceGood: IsSelectedGoodsInsurance,
                goodInsuranceID: GoodsInsuranceID,
                paymentMonthly: PaymentAmountMonthly || 0,
                //contract
                contactID: ContractID || "",
                pgProcessUserID: PGProcessUserID || "",
                pgProcessUserName: PGProcessUserName || "",
                packageRates: packageRates || ""
            };
        }
        const { discountCode } = this.getDiscountCode();
        this.setState({
            candidateNo: cus_CandidateNO,
            isLockPhone: isLockPhone,
            taxID: TaxID || "",
            customerPhone: CustomerPhone || "",
            customerName: CustomerName || "",
            customerAddress: CustomerAddress || "",
            contactPhone: ContactPhone || "",
            contactName: ContactName || "",
            contactAddress: DeliveryAddress || "",
            isCompany: !!TaxID,
            // gender: !TaxID ? Gender : ContactGender,
            gender: Gender,
            relationShipType: RelationShipType,
            note: Note || CustomerNote || "",
            discountCode: discountCode || "",
            // programInfo
            isHasSaleProgram: defaultSaleProgram.isHasSaleProgram,
            isLockCustomer: defaultSaleProgram.isLockCustomer,
            isLockTax: defaultSaleProgram.isLockTax,
            isLockName: defaultSaleProgram.isLockName,
            isLockAddress: defaultSaleProgram.isLockAddress,
            isAllowInvoice: defaultSaleProgram.isAllowInvoice,
            customerIDCard: CustomerIDCard || "",
            totalPrePaid: defaultSaleProgram.totalPrePaid,
            termLoan: defaultSaleProgram.termLoan,
            isInsuranceFee: defaultSaleProgram.isInsuranceFee,
            isInsuranceGood: defaultSaleProgram.isInsuranceGood,
            goodInsuranceID: defaultSaleProgram.goodInsuranceID,
            paymentMonthly: defaultSaleProgram.paymentMonthly,
            //contract
            contractID: defaultSaleProgram.contactID,
            dataContract: helper.IsEmptyObject(saleProgramInfo)
                ? {}
                : {
                    ContractID: defaultSaleProgram.contactID,
                    PGProcessUserID: defaultSaleProgram.pgProcessUserID,
                    PGProcessUserName: defaultSaleProgram.pgProcessUserName,
                    packageRates: defaultSaleProgram.packageRates,
                    TotalPrePaid: defaultSaleProgram.totalPrePaid,
                    TermLoan: defaultSaleProgram.termLoan,
                    PaymentAmountMonthly: defaultSaleProgram.paymentMonthly
                },
        }, () => {
            const newNumberPhone = this.state.isCompany ? this.state.contactPhone : this.state.customerPhone;
            this.handleAPIGetCustomerProfile(newNumberPhone, isFirstRender = true);
            helper.IsNonEmptyString(this.state.taxID) && this.getCompanyProfile(this.state.taxID, isFirstRender = true);

        });
    };

    getDiscountCode = () => {
        const { PRDLISTDISABLEDISCOUNTCODE, dataShoppingCart: { SaleOrderDetails, DiscountCode } } = this.props;
        let infoCoupon = {
            discountCode: DiscountCode,
            hasConfigCoupon: false
        };
        if (!!DiscountCode) {
            for (let index = 0; index < SaleOrderDetails.length; index++) {
                const { ProductID } = SaleOrderDetails[index];
                if (`,${PRDLISTDISABLEDISCOUNTCODE},`.includes(`,${ProductID.toString()},`)) {
                    infoCoupon = {
                        discountCode: "*********",
                        hasConfigCoupon: true
                    };
                    break;
                }
            }
        }
        return infoCoupon;
    };

    checkGiftVoucher = () => {
        const { customerPhone } = this.state;
        const {
            cartPromotion,
            dataShoppingCart: {
                SaleOrderID
            }
        } = this.props;
        const regExpPhone = new RegExp(/^[0]\d{9}$/);
        const isValidatePhone = regExpPhone.test(customerPhone);
        const giftVoucher = cartPromotion.find(ele => ele.invisibleByCustomerPhone);
        const isValidate = (!!giftVoucher && isValidatePhone);
        if (isValidate) {
            showBlockUI();
            this.props.actionShoppingCart.checkGiftVoucher({
                "customerPhone": customerPhone,
                "promotionGroups": cartPromotion,
                "saleOrderID": SaleOrderID
            }).then(isVisible => {
                hideBlockUI();
                this.setState({ isVisibleVoucher: isVisible });
            });
        }
        else {
            this.setState({ isVisibleVoucher: false });
        }
    };

    updateCartInfo = (dataCart) => {
        const {
            DisableApplyCustomerPhone: phoneValidate,
            CustomerInfo,
            IsInstallment,
            SaleOrderSaleProgramInfo
        } = dataCart;
        const {
            customerName,
            customerAddress,
            isCompany,
            taxID
        } = this.state;
        const isApplyCoupon = helper.hasProperty(CustomerInfo, "ApplyPhoneNumberByDiscountCode");
        const isApplyPhone = !!phoneValidate;
        const isLockPhone = isApplyPhone || isApplyCoupon;
        let defaultSaleProgram = {
            isHasSaleProgram: false,
            isLockCustomer: false,
            isLockTax: false,
            isLockName: false,
            isLockAddress: false,
            isAllowInvoice: true,
            totalPrePaid: 0,
            termLoan: 0,
            paymentMonthly: 0,
            isInsuranceFee: false,
            isInsuranceGood: false,
            goodInsuranceID: 0,
            customerName: customerName,
            customerAddress: customerAddress,
            taxID: taxID,
            isCompany: isCompany
        };
        if (IsInstallment) {
            const {
                DisableTaxID,
                DisableCustomerAddress,
                DisableCustomerName,
                IsLockCustomer,
                IsAllowCompanyInvoice,
                TotalPrePaid,
                TermLoan,
                IsSelectedLifeInsurance,
                IsSelectedGoodsInsurance,
                GoodsInsuranceID,
                PaymentAmountMonthly,
                CustomerName,
                CustomerAddress,
                TaxID
            } = SaleOrderSaleProgramInfo;
            defaultSaleProgram = {
                isHasSaleProgram: true,
                isLockCustomer: IsLockCustomer,
                isLockTax: DisableTaxID,
                isLockName: DisableCustomerName,
                isLockAddress: DisableCustomerAddress,
                isAllowInvoice: IsAllowCompanyInvoice,
                totalPrePaid: TotalPrePaid || 0,
                termLoan: TermLoan || 0,
                isInsuranceFee: IsSelectedLifeInsurance,
                isInsuranceGood: IsSelectedGoodsInsurance,
                goodInsuranceID: GoodsInsuranceID,
                paymentMonthly: PaymentAmountMonthly || 0,
                customerAddress: IsLockCustomer
                    ? CustomerAddress
                    : customerAddress,
                customerName: IsLockCustomer
                    ? CustomerName
                    : customerName,
                taxID: TaxID || "",
                isCompany: !!TaxID,
                numberPhone: this.state.customerPhone
            };
        }
        this.setState({
            isLockPhone: isLockPhone,
            customerName: defaultSaleProgram.customerName,
            customerAddress: defaultSaleProgram.customerAddress,
            taxID: defaultSaleProgram.taxID,
            isCompany: defaultSaleProgram.isCompany,
            // programInfo
            isHasSaleProgram: defaultSaleProgram.isHasSaleProgram,
            isLockCustomer: defaultSaleProgram.isLockCustomer,
            isLockTax: defaultSaleProgram.isLockTax,
            isLockName: defaultSaleProgram.isLockName,
            isLockAddress: defaultSaleProgram.isLockAddress,
            isAllowInvoice: defaultSaleProgram.isAllowInvoice,
            totalPrePaid: defaultSaleProgram.totalPrePaid,
            termLoan: defaultSaleProgram.termLoan,
            isInsuranceFee: defaultSaleProgram.isInsuranceFee,
            isInsuranceGood: defaultSaleProgram.isInsuranceGood,
            goodInsuranceID: defaultSaleProgram.goodInsuranceID,
            paymentMonthly: defaultSaleProgram.paymentMonthly,
        }, () => {
            this.handleAPIGetCustomerProfile(defaultSaleProgram.numberPhone);
            helper.IsNonEmptyString(this.state.taxID) && this.getCompanyProfile(this.state.taxID);
        });
    };

    getPaymentMonthly = () => {
        const {
            totalPrePaid,
            termLoan,
            isInsuranceFee,
            isInsuranceGood,
            goodInsuranceID,
        } = this.state;
        const { dataShoppingCart } = this.props;
        const saleProgramInfo = getSaleProgramInfo(dataShoppingCart);
        const {
            MinPrepaid,
            MaxPrepaid,
        } = saleProgramInfo;
        const isValidateMin = MinPrepaid <= totalPrePaid;
        const isValidateMax = totalPrePaid <= MaxPrepaid;
        const isHasPrePaid = isValidateMin && isValidateMax;
        const isHasTermLoan = (termLoan > 0);
        const isValidate = isHasPrePaid && isHasTermLoan;
        if (isValidate) {
            const cartRequest = helper.deepCopy(dataShoppingCart);
            const newSaleProgramInfo = {
                ...saleProgramInfo,
                'TotalPrePaid': totalPrePaid,
                "TermLoan": termLoan,
                "IsSelectedLifeInsurance": isInsuranceFee,
                "IsSelectedGoodsInsurance": isInsuranceGood,
                "GoodsInsuranceID": goodInsuranceID,
            };
            cartRequest.SaleOrderDetails.forEach(saleOrder => {
                saleOrder.SaleProgramInfo = newSaleProgramInfo;
            });
            showBlockUI();
            this.props.actionShoppingCart.getPaymentMonthly(cartRequest)
                .then(value => {
                    hideBlockUI();
                    this.setState({ paymentMonthly: value });
                }).catch(msgError => {
                    Alert.alert(translate('common.notification_uppercase'), msgError,
                        [
                            {
                                text: translate('common.btn_skip'),
                                style: "cancel",
                                onPress: hideBlockUI
                            },
                            {
                                text: translate('common.btn_notify_try_again'),
                                style: "default",
                                onPress: this.getPaymentMonthly
                            }
                        ]
                    );
                });
        }
    };

    onPressAddCart = (dataShoppingCart, isNonEmpty) => () => {
        const { dataShoppingCart } = this.props;
        const { IsSOAnKhang } = dataShoppingCart;
        const { customerPhone } = this.state;
        if (isNonEmpty) {
            this.props.actionEditSaleOrder.getDataAddOrderCart(dataShoppingCart);
        }
        if (IsSOAnKhang) {
            this.props.navigation.navigate('SearchAnKhang', { isEditMode: true });
        }
        else {
            this.props.navigation.navigate('SearchProduct');
        }
        this.props.actionEditSaleOrder.set_apply_phone(customerPhone);
    };

    onChangeInstallment = (value, isCreateSO) => {
        const { OldSaleOrderSaleProgramInfo } = this.props.dataShoppingCart;
        if (OldSaleOrderSaleProgramInfo?.PartnerInstallmentID == 1) {
            this.setState({ taxID: "" });
        }
        if (isCreateSO && !this.isAllowTypePayment) {
            const content = value
                ? translate('editSaleOrder.warning_update_payment')
                : translate('editSaleOrder.warning_update_pay_ment_1');
            Alert.alert("", content);
        }
        else {
            this.isAllowTypePayment = false;
            const { customerPhone } = this.state;
            if (value) {
                this.props.navigation.navigate("EditInstallment");
            }
            else {
                this.updateOrderSaleProgram();
            }
            this.props.actionEditSaleOrder.set_apply_phone(customerPhone);
        }
    };

    moveToInstallment = (isSendPartner) => () => {
        if (isSendPartner) {
            Alert.alert("", translate('editSaleOrder.profile_sent_partner'));
        }
        else {
            const { customerPhone } = this.state;
            this.props.navigation.navigate("EditInstallment");
            this.props.actionEditSaleOrder.set_apply_phone(customerPhone);
        }
    };

    updateOrderSaleProgram = async () => {
        const { newSaleOrderDetails, allPromises } = helper.handleThreePrice(this.props, 0);
        try {
            showBlockUI();
            if (allPromises.length > 0) {
                const result = await Promise.all(allPromises);
                newSaleOrderDetails.forEach(product => {
                    if (!helper.IsEmptyObject(product.PricePolicyApplyBO)) {
                        const policyBO = result.find(item => item[0]?.productID == product.ProductID);


                        if (policyBO) {
                            const newPricePolicyApplyBO = policyBO.find(item => item.PricePolicyTypeID == product.PricePolicyApplyBO?.PricePolicyTypeID);
                            if (newPricePolicyApplyBO) {
                                product.PricePolicyApplyBO = {
                                    PricePolicyProgramID: newPricePolicyApplyBO.pricePolicyProgramID,
                                    SalePricePolicyID: newPricePolicyApplyBO.SalePricePolicyID,
                                    SalePricePolicyName: newPricePolicyApplyBO.SalePricePolicyName,
                                    ProductID: policyBO[0].productID,
                                    lstSaleOrderPricePolicyApplyDetailBO: newPricePolicyApplyBO.DetailPolicies,
                                    PricePolicyTypeID: newPricePolicyApplyBO.PricePolicyTypeID
                                };
                            } else {
                                product.PricePolicyApplyBO = null; // Reset if no matching policy found
                            }
                        } else {
                            product.PricePolicyApplyBO = null; // Reset if no policyBO found
                        }
                    }
                });

                this.props.dataShoppingCart.SaleOrderDetails = newSaleOrderDetails;
            }
            const isContinue = await this.props.actionEditSaleOrder.updateSaleProgramOrder({
                "saleProgramID": 0,
                "cartRequest": this.props.dataShoppingCart,
                "interateRequest": null
            });
            hideBlockUI();
            if (isContinue) {
                this.props.navigation.navigate("AddCartKeepPromotion");
            }
        } catch (msgError) {
            Alert.alert(translate('common.notification_uppercase'), msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: this.updateOrderSaleProgram
                    }
                ]
            );
        }
    };

    getReasonCancelEP = (cartRequest) => {
        showBlockUI();
        this.props.actionEditSaleOrder.getReasonCancelEP(cartRequest).then(data => {
            hideBlockUI();
            this.setState({
                dataReason: data,
                isVisibleCancelEP: true
            });
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: () => this.getReasonCancelEP(cartRequest)
                    }
                ]
            );
        });
    };

    getDataCartDelivery = () => {
        const { dataShoppingCart } = this.props;
        const { isCompany } = this.state;
        const {
            DeliveryInfo,
            SaleOrderDetails,
            SaleProgramID,
            CreateDate,
            SaleOrderIDDelete,
            OrderTypeID
        } = dataShoppingCart;
        const { DeliveryTypeID } = DeliveryInfo;
        const stockProducts = SaleOrderDetails.map(ele => ({
            "InventoryStatusID": ele.InventoryStatusID,
            "ProductID": ele.ProductID,
            "Quantity": ele.Quantity,
            "SalePriceVAT": ele.SalePriceVAT,
            "IMEI": ele.IMEI,
            "stockQuantity": ele.Quantity,
            "GetStockType": 3
        }));
        const data = {
            stockProducts,
            deliveryInfo: DeliveryInfo,
            saleProgramID: SaleProgramID,
            createDate: CreateDate,
            saleOrderIDDelete: SaleOrderIDDelete,
            saleOrderTypeID: OrderTypeID,
            receiverPhone: ""
        };
        if (DeliveryTypeID == 1) {
            this.getInfoDeliveryAtStore(data);
        }
        else {
            this.getInfoDeliveryAtHome(data);
        }
    };

    getInfoDeliveryAtStore = ({
        deliveryInfo,
        stockProducts,
        saleProgramID,
        createDate,
        saleOrderIDDelete,
        saleOrderTypeID
    }) => {
        const { customerPhone } = this.state;
        const {
            StoreChangeOrders,
            DeliveryStoreName,
            DeliveryStoreID,
            DeliveryStoreAddress,
        } = deliveryInfo;
        let storeRequests = [];
        if (helper.IsNonEmptyArray(StoreChangeOrders)) {
            storeRequests = StoreChangeOrders.map(ele => ({
                "stockStoreID": ele.FromStoreID,
                "stockStoreName": ele.FromStoreName,
                "storeChangeQuantity": ele.TotalQuantity,
            }));
        }
        const storeInfo = {
            "storeID": DeliveryStoreID,
            "storeName": DeliveryStoreName,
            "storeAddress": DeliveryStoreAddress,
            "suggestTimes": [],
            "storeRequests": storeRequests
        };
        showBlockUI();
        this.props.actionEditSaleOrder.getStoreInfoSO({
            "storeID": deliveryInfo.DeliveryStoreID,
            "provinceID": deliveryInfo.DeliveryProvinceID,
            "districtID": deliveryInfo.DeliveryDistrictID,
            "wardID": deliveryInfo.DeliveryWardID,
            "deliveryAddress": deliveryInfo.deliveryAddress,
            "stockProducts": stockProducts,
            "saleProgramID": saleProgramID,
            "createDateSO": createDate,
            "saleOrderIDDelete": saleOrderIDDelete,
            "deliveryTypeID": deliveryInfo.DeliveryTypeID,
            "deliveryTime": deliveryInfo.DeliveryTime,
            "deliveryVehicles": deliveryInfo.DeliveryVehicles,
            "saleOrderTypeID": saleOrderTypeID
        }).then(suggestTimes => {
            hideBlockUI();
            storeInfo.suggestTimes = suggestTimes;
            this.props.actionEditSaleOrder.updateDataCartDelivery({
                storeInfo: storeInfo,
                stockProducts: stockProducts
            });
            this.props.navigation.navigate("CartDeliveryInfo");
            this.props.actionEditSaleOrder.set_apply_phone(customerPhone);
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: () => this.getInfoDeliveryAtStore({
                            deliveryInfo,
                            stockProducts,
                            saleProgramID,
                            createDate,
                            saleOrderIDDelete
                        })
                    }
                ]
            );
        });
    };

    getInfoDeliveryAtHome = ({
        deliveryInfo,
        stockProducts,
        saleProgramID,
        createDate,
        saleOrderIDDelete,
        saleOrderTypeID,
        receiverPhone
    }) => {
        const { customerPhone } = this.state;
        showBlockUI();
        this.props.actionEditSaleOrder.getStoreInfoShippingSO({
            "storeID": deliveryInfo.DeliveryStoreID,
            "distance": deliveryInfo.DeliveryDistance,
            "provinceID": deliveryInfo.DeliveryProvinceID,
            "districtID": deliveryInfo.DeliveryDistrictID,
            "wardID": deliveryInfo.DeliveryWardID,
            "deliveryAddress": deliveryInfo.deliveryAddress,
            "stockProducts": stockProducts,
            "saleProgramID": saleProgramID,
            "createDateSO": createDate,
            "saleOrderIDDelete": saleOrderIDDelete,
            "saleOrderTypeID": saleOrderTypeID,
            "deliveryTypeID": deliveryInfo.DeliveryTypeID,
            "deliveryTime": deliveryInfo.DeliveryTime,
            "deliveryVehicles": deliveryInfo.DeliveryVehicles,
            "isNotChangeDeliveryAddress": true
        }).then(({ storeInfo, storeDelivery }) => {
            hideBlockUI();
            this.props.actionEditSaleOrder.updateDataCartDelivery({
                storeInfo: storeInfo,
                stockProducts: stockProducts
            });
            this.props.navigation.navigate("CartDeliveryInfo", { storeDelivery, receiverPhone });
            this.props.actionEditSaleOrder.set_apply_phone(customerPhone);
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: () => this.getInfoDeliveryAtHome({
                            deliveryInfo,
                            stockProducts,
                            saleProgramID,
                            createDate,
                            saleOrderIDDelete,
                            saleOrderTypeID,
                            receiverPhone
                        })
                    }
                ]
            );
        });
    };

    onCheckInOutVoucher = (dataSaleOrder) => {
        const { cartRequest } = dataSaleOrder;
        const { IsIncome, TotalPaid } = cartRequest;
        const isCMVoucher = (TotalPaid > 0);
        const isInOut = IsIncome && isCMVoucher;
        if (isInOut) {
            this.dataInOutVoucher = dataSaleOrder;
            this.getInfoInOutVoucher(dataSaleOrder);
        }
        else {
            this.onCheckCancelEP(dataSaleOrder);
        }
    };

    getInfoInOutVoucher = (dataSaleOrder) => {
        showBlockUI();
        const { cartRequest } = dataSaleOrder;
        this.props.actionEditSaleOrder.getInfoInOutVoucher(cartRequest).then(data => {
            if (data.IsShowInOutVoucher) {
                hideBlockUI();
                this.setState({
                    isVisibleCMVoucher: true,
                    dataCMVoucher: data
                });
            }
            else {
                dataSaleOrder.cartRequest.cus_ShoppingCartCusInOutVoucher = data;
                this.onCheckCancelEP(dataSaleOrder);
            }
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: () => this.getInfoInOutVoucher(dataSaleOrder)
                    }
                ]
            );
        });
    };
    getPromoRequireInfors = ({ SaleOrderDetails }) => {
        let result = [];
        if (!helper.IsNonEmptyArray(SaleOrderDetails)) return [];

        SaleOrderDetails.forEach((mainProduct) => {
            if (mainProduct.PromoRequireInfors) {
                result = result.concat(mainProduct.PromoRequireInfors.map((ele, index) => ({
                    ...ele,
                    SaleOrderDetailID: mainProduct.SaleOrderDetailID,
                    ProductName: mainProduct.ProductName,
                    isDisplayName: index === 0,
                    isMainProduct: true
                })));
            }

            if (helper.IsNonEmptyArray(mainProduct.giftSaleOrders)) {
                mainProduct.giftSaleOrders.forEach(({ PromoRequireInfors, SaleOrderDetailID, ProductName }) => {
                    if (PromoRequireInfors) {
                        result = result.concat(PromoRequireInfors.map((ele, index) => ({
                            ...ele,
                            SaleOrderDetailID,
                            ProductName,
                            isDisplayName: index === 0,
                            isMainProduct: false
                        })));
                    }
                });
            }
        });

        return result;
    };

    calculateFeePlusMoneyBO = (feeList, SHChangeTranferAmountFee) => {
        if (helper.IsNonEmptyArray(feeList)) {
            let objFee = {
                BaseFeeMoneyAmount: 0,
                FeePlusMoneyAmount: 0,
                AdditionalFeeAmount: 0,
                FeeMinusMoneyAmount: 0,
                FeeMoney: 0,
                Rounding: 0
            };

            feeList.forEach(item => {
                if (!helper.IsEmptyObject(item.cus_ForwarderFeeBO)) {
                    objFee = {
                        ...objFee,
                        BaseFeeMoneyAmount: objFee.BaseFeeMoneyAmount + item.cus_ForwarderFeeBO.BaseFeeMoneyAmount,
                        FeePlusMoneyAmount: objFee.FeePlusMoneyAmount + item.cus_ForwarderFeeBO.FeePlusMoneyAmount,
                        AdditionalFeeAmount: objFee.AdditionalFeeAmount + item.cus_ForwarderFeeBO.AdditionalFeeAmount,
                        FeeMinusMoneyAmount: objFee.FeeMinusMoneyAmount + item.cus_ForwarderFeeBO.FeeMinusMoneyAmount,
                        FeeMoney: objFee.FeeMoney + item.cus_ForwarderFeeBO.FeeMoney,
                        Rounding: objFee.Rounding + item.Rounding,
                    };
                }
            });

            return {
                ...feeList[0].cus_ForwarderFeeBO,
                "SHChangeTranferAmountFee": SHChangeTranferAmountFee,
                ...objFee
            };
        }

        return {};
    };
    handleAddToCartWithAdjustPrice = () => {
        this.OTPSheetRef.current?.dismiss()
        const { tempSaleOrders } = this.state
        const { customerInfo = {}, cartRequest = {}, customerIDLoyalty = "" } = tempSaleOrders ?? {};
        const {
            IsAllowParticipationLoyalty,
            TotalPointLoyalty
        } = cartRequest;
        const isLoyalty = IsAllowParticipationLoyalty && !helper.IsNonEmptyString(customerIDLoyalty) && helper.IsNonEmptyString(customerInfo?.customerPhone) && TotalPointLoyalty > 0;
        if (isLoyalty) {
            this.getDataLoyalty(customerInfo?.customerPhone, tempSaleOrders);
        }
        else {
            this.getDataMemberPoint(tempSaleOrders);
        }
    }
    handleAPICheckScreensProtector = async (dataSaleOrder) => {
        try {
            const {
                userInfo: { storeID, languageID, moduleID }, saleScenarioTypeID } = this.props;
            const body = {
                "loginStoreId": storeID,
                "languageID": languageID,
                "moduleID": moduleID,
                "saleScenarioTypeID": saleScenarioTypeID,
                "customerPhone": this.state.customerPhone
            }
            showBlockUI();
            const isChecked = await actionShoppingCartCreator.checkNumberPhoneApplyVoucher(body)
            return isChecked;
        } catch (error) {
            Alert.alert("", error || "Đã có lỗi xảy ra. Vui lòng thử lại!",
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI

                    },
                    {
                        text: translate('shoppingCart.btn_retry_uppercase'),
                        style: "default",
                        onPress: () => this.handleModifyCustomerrofile(dataSaleOrder)
                    }
                ]
            )
            return null
        }

    };

}

const styles = StyleSheet.create({
    fabMedicine: {
        backgroundColor: '#981c3d',
        opacity: 0.7,
        width: 54,
        height: 54,
        borderRadius: 27,
        alignItems: 'center',
        justifyContent: 'center',
        marginLeft: 15,
        marginVertical: 10,

        shadowColor: '#981c3d',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    }
});

const mapStateToProps = function (state) {
    return {
        dataShoppingCart: state.editSaleOrderReducer.dataShoppingCart,
        cartPromotion: state.shoppingCartReducer.cartPromotion,
        applyDetailIDs: state.shoppingCartReducer.applyDetailIDs,
        voucherPromotion: state.shoppingCartReducer.voucherPromotion,
        stateCartPromotion: state.shoppingCartReducer.stateCartPromotion,
        userInfo: state.userReducer,
        paramFilter: state.managerSOReducer.paramFilter,
        cartChange: state.editSaleOrderReducer.cartChange,
        wowPoints: state.loyaltyReducer.wowPoints,
        PRDLISTDISABLEDISCOUNTCODE: state.appSettingReducer.PRDLISTDISABLEDISCOUNTCODE,
        customerConfirmPolicy: state.shoppingCartReducer.customerConfirmPolicy,
        numberPhoneCreateAtHome: state.detailReducer.numberPhoneCreateAtHome

    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        actionShoppingCart: bindActionCreators(actionShoppingCartCreator, dispatch),
        actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),
        actionPouch: bindActionCreators(actionPouchCreator, dispatch),
        actionEditSaleOrder: bindActionCreators(actionEditSaleOrderCreator, dispatch),
        actionManagerSO: bindActionCreators(actionManagerSOCreator, dispatch),
        actionPaymentOrder: bindActionCreators(actionPaymentOrderCreator, dispatch),
        loyaltyAction: bindActionCreators(loyaltyActionCreator, dispatch),
        actionDetail: bindActionCreators(actionDetailCreator, dispatch),

    };
};

export default connect(mapStateToProps, mapDispatchToProps)(EditSaleOrder);

const TotalAmount = ({ total }) => {
    return (
        <View style={{
            backgroundColor: COLORS.bgF7EED6,
            flexDirection: 'row',
            width: constants.width,
            justifyContent: 'space-between',
            paddingHorizontal: 10,
            height: 40,
            borderTopWidth: StyleSheet.hairlineWidth,
            borderTopColor: COLORS.bdFFFFFF,
        }}>
            <View style={{
                flexDirection: 'row',
                alignSelf: 'flex-end',
                alignItems: 'center',
                height: 40,
            }}>
                <MyText
                    style={{
                        color: COLORS.txtFF6A00,
                        fontWeight: 'bold'
                    }}
                    text={translate('editSaleOrder.total_price')}
                    children={
                        <MyText
                            style={{
                                color: COLORS.txt666666,
                                fontStyle: 'italic',
                                fontWeight: 'normal'
                            }}
                            text={translate('editSaleOrder.not_rounded')}
                        />
                    }
                />
            </View>

            <View style={{
                alignSelf: 'flex-end',
                justifyContent: 'center',
                height: 40,
            }}>
                <MyText
                    addSize={2}
                    style={{
                        color: COLORS.txtFF0000,
                        fontWeight: 'bold'
                    }}
                    text={helper.convertNum(total)}
                />
            </View>
        </View>
    );
};

const DepositAmount = ({ total, onShow }) => {
    const isShow = (total > 0);
    return (
        isShow &&
        <View style={{
            backgroundColor: COLORS.bgE4EBD5,
            flexDirection: 'row',
            width: constants.width,
            justifyContent: 'space-between',
            paddingHorizontal: 10,
            height: 40,
            borderTopWidth: StyleSheet.hairlineWidth,
            borderTopColor: COLORS.bdFFFFFF
        }}>
            <View style={{
                flexDirection: 'row',
                alignSelf: 'flex-end',
                alignItems: 'center',
                height: 40,
            }}>
                <MyText
                    style={{
                        color: COLORS.txtFC3158,
                        fontWeight: 'bold',
                        textDecorationLine: 'underline'
                    }}
                    onPress={onShow}
                    text={translate('editSaleOrder.total_customer_deposit')}
                />
            </View>

            <View style={{
                alignSelf: 'flex-end',
                justifyContent: 'center',
                height: 40,
            }}>
                <MyText
                    addSize={2}
                    style={{
                        color: COLORS.txtFF6600,
                        fontWeight: 'bold'
                    }}
                    text={helper.convertNum(total)}
                />

            </View>
        </View>
    );
};

const FeeAmount = ({ total, onShow }) => {
    const isShow = (total > 0);
    return (
        isShow &&
        <View style={{
            backgroundColor: COLORS.bgE4EBD5,
            flexDirection: 'row',
            width: constants.width,
            justifyContent: 'space-between',
            paddingHorizontal: 10,
            height: 40,
            borderTopWidth: StyleSheet.hairlineWidth,
            borderTopColor: COLORS.bdFFFFFF
        }}>
            <View style={{
                flexDirection: 'row',
                alignSelf: 'flex-end',
                alignItems: 'center',
                height: 40,
            }}>
                <MyText
                    style={{
                        color: COLORS.txtFC3158,
                        fontWeight: 'bold',
                        textDecorationLine: 'underline'
                    }}
                    onPress={onShow}
                    text={translate('editSaleOrder.additional_fee_2')}
                />
            </View>

            <View style={{
                alignSelf: 'flex-end',
                justifyContent: 'center',
                height: 40,
            }}>
                <MyText
                    addSize={2}
                    style={{
                        color: COLORS.txtFF6600,
                        fontWeight: 'bold'
                    }}
                    text={helper.convertNum(total)}
                />
            </View>
        </View>
    );
};

const PointLoyalty = ({ total, onShow }) => {
    const isShow = (total > 0);
    return (
        isShow &&
        <View style={{
            backgroundColor: COLORS.bgFAFAFA,
            flexDirection: 'row',
            width: constants.width,
            justifyContent: 'space-between',
            paddingHorizontal: 10,
            height: 40,
        }}>
            <View style={{
                flexDirection: 'row',
                alignSelf: 'flex-end',
                alignItems: 'center',
                height: 40,
            }}>
                <MyText
                    style={{
                        color: COLORS.txt2164F4,
                        fontWeight: 'bold'
                        // textDecorationLine: "underline"
                    }}
                    // onPress={onShow}

                    /* {"Tổng điểm tích lũy KHTT tạm tính"} */
                    text={translate('editSaleOrder.membership_point')}
                />
            </View>

            <View style={{
                alignSelf: 'flex-end',
                justifyContent: 'center',
                height: 40,
            }}>
                <MyText
                    addSize={2}
                    style={{
                        color: COLORS.txtFF00BF,
                        fontWeight: 'bold'
                    }}
                    text={helper.convertNum(total)}
                />
            </View>
        </View>
    );
};

const ButtonAdjust = ({
    onAdjustPrice,
    onCreatePrice,
    isHiddenAdjustPrice
}) => {
    return (
        <View style={{
            width: constants.width,
        }}>
            {/* {
                !isHiddenAdjustPrice &&
                <Button
                    text={translate('editSaleOrder.btn_adjust_price')}
                    onPress={onAdjustPrice}
                    styleContainer={{
                        flexDirection: 'row',
                        width: constants.width,
                        height: 40,
                        backgroundColor: COLORS.bg5B9A68,
                    }}
                    styleText={{
                        color: COLORS.txtFFFFBC,
                        fontSize: 16,
                        marginRight: 8
                    }}
                    iconRight={{
                        iconSet: "FontAwesome",
                        name: "chevron-right",
                        size: 14,
                        color: COLORS.icFFFFBC
                    }}
                />
            } */}
            <Button
                text={translate('editSaleOrder.btn_create_competitive_price')}
                onPress={onCreatePrice}
                styleContainer={{
                    flexDirection: 'row',
                    width: constants.width,
                    height: 40,
                    backgroundColor: COLORS.btn5482AB,
                    borderTopWidth: StyleSheet.hairlineWidth,
                    borderTopColor: COLORS.bdFFFFFF
                }}
                styleText={{
                    color: COLORS.txtFFFFBC,
                    fontSize: 16,
                    marginRight: 8
                }}
                iconRight={{
                    iconSet: "FontAwesome",
                    name: "chevron-right",
                    size: 14,
                    color: COLORS.icFFFFBC
                }}
            />
        </View>
    );
};

const checkApplyCoupon = (SaleOrderDetails) => {
    for (const { saleSaleOrders, AdjustPrice } of SaleOrderDetails) {
        if (AdjustPrice != 0) return false;
        for (const { AdjustPrice: saleAdjustPrice } of saleSaleOrders) {
            if (saleAdjustPrice != 0) return false;
        }
    }
    return true;
};

const getMsgRequirePromotion = (allPromotion, setKeyPromotionSelected) => {
    let isValidatePromotion = true;
    let msgRequirePromotion = "";
    let isWarningPromotion = false;
    allPromotion.forEach(groupPromotion => {
        const {
            promotionProducts,
            promotionGroupID,
            isRequired,
            promotionGroupName
        } = groupPromotion;
        const productSelected = promotionProducts.filter((product, index) => {
            const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
            const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
            return setKeyPromotionSelected.has(key);
        });
        if (productSelected.length == 0) {
            if (isRequired) {
                msgRequirePromotion += (
                    isValidatePromotion
                        ? `\t${promotionGroupName}`
                        : `\n\t${promotionGroupName}`
                );
                isValidatePromotion = false;
            }
            isWarningPromotion = true;
        }
    });
    return { isValidatePromotion, msgRequirePromotion, isWarningPromotion };
};

const checkRequireImeiSim = (SaleOrderDetails) => {
    let isRequire = false;
    SaleOrderDetails.forEach(mainProduct => {
        const {
            giftSaleOrders,
            saleSaleOrders,
            giftDeliverySaleOrders,
            saleDeliverySaleOrders
        } = mainProduct;
        giftSaleOrders.forEach(saleProduct => {
            const { IMEI, BrandIDOfSIM } = saleProduct;
            const isSIM = !!BrandIDOfSIM;
            if (isSIM && !IMEI) {
                isRequire = true;
            }
        });
        saleSaleOrders.forEach(saleProduct => {
            const { IMEI, BrandIDOfSIM } = saleProduct;
            const isSIM = !!BrandIDOfSIM;
            if (isSIM && !IMEI) {
                isRequire = true;
            }
        });
        giftDeliverySaleOrders.forEach(saleProduct => {
            const { IMEI, BrandIDOfSIM } = saleProduct;
            const isSIM = !!BrandIDOfSIM;
            if (isSIM && !IMEI) {
                isRequire = true;
            }
        });
        saleDeliverySaleOrders.forEach(saleProduct => {
            const { IMEI, BrandIDOfSIM } = saleProduct;
            const isSIM = !!BrandIDOfSIM;
            if (isSIM && !IMEI) {
                isRequire = true;
            }
        });
    });
    return isRequire;
};

const RadioRelationShip = ({ type, onChangeType, disabled }) => {
    return (
        <View pointerEvents={disabled ? 'none' : null} style={{
            width: constants.width,
            alignItems: "center",
        }}>
            <RadioRelation
                type={type}
                onChangeType={onChangeType}
            />
        </View>
    );
};

const InstallmentInfo = ({ value, programInfo, onSwitch, disabled, onChange, isSaleProgaramNotExist, outputStoreID }) => {
    const isHasProgram = value && !helper.IsEmptyObject(programInfo);
    return (
        <View style={{
            width: constants.width,
            alignItems: "center",
            backgroundColor: COLORS.bgFAFAFA
        }}>
            <View style={{
                justifyContent: 'center',
                backgroundColor: COLORS.bg6A9C84,
                width: constants.width,
                height: 40,
                paddingHorizontal: 10,
                borderTopWidth: StyleSheet.hairlineWidth,
                borderTopColor: COLORS.bdFFFFFF
            }}>
                <MyText
                    style={{
                        color: COLORS.txtFFFFFF,
                        fontWeight: 'bold'
                    }}
                    text={translate('editSaleOrder.payment_method')}
                />
            </View>
            <View style={{
                width: constants.width,
                alignItems: "center",
                paddingVertical: 8
            }}>
                <RadioInstallment
                    installment={value}
                    onSwitch={onSwitch}
                    disabled={disabled}
                />
                {
                    isHasProgram &&
                    <SaleProgram
                        info={programInfo}
                        onChange={onChange}
                        disabled={disabled}
                        isSaleProgaramNotExist={isSaleProgaramNotExist}
                        outputStoreID={outputStoreID}
                    />
                }
            </View>
        </View>
    );
};

const SaleProgram = ({ info, onChange, disabled, isSaleProgaramNotExist, outputStoreID }) => {
    const {
        SaleProgramID,
        SaleProgramName,
        Logo,
        OutputStoreID,
    } = info;
    return (
        <TouchableOpacity style={{
            width: constants.width - 40,
            paddingVertical: 10,
            paddingHorizontal: 20,
            borderWidth: 1,
            borderColor: COLORS.bdE4E4E4,
            borderRadius: 4,
            backgroundColor: COLORS.bgFFFFFF
        }}
            activeOpacity={0.8}
            onPress={onChange}
            disabled={disabled}
        >
            <View style={{
                justifyContent: 'center',
                alignItems: 'center',
                padding: 5,
                backgroundColor: COLORS.bgFFFFFF,
                flexWrap: "wrap",
                width: 150,
                height: 50,
                borderRadius: 2,
                shadowColor: COLORS.sd000000,
                shadowOffset: {
                    width: 0,
                    height: 1,
                },
                shadowOpacity: 0.25,
                shadowRadius: 3.84,
                elevation: 2,
                borderColor: COLORS.bd2FB47C,
                borderWidth: 2,
                marginBottom: 8,
            }}>
                {/* <ImageURI
                    uri={API_LOGO_PARTNER_INSTALLMENT + Logo}
                    style={{
                        width: 100,
                        height: 40,
                    }}
                    resizeMode={"contain"}
                /> */}
                {
                    !!Logo && <Image
                        source={{ uri: Logo }}
                        style={{
                            width: 100,
                            height: 40,
                        }}
                        resizeMode={"contain"}
                    />
                }

            </View>
            <View style={{
                padding: 10,
                backgroundColor: COLORS.bgFFFFFF,
                borderRadius: 4,
                shadowColor: COLORS.sd000000,
                shadowOffset: {
                    width: 0,
                    height: 1,
                },
                shadowOpacity: 0.25,
                shadowRadius: 3.84,

                elevation: 2
            }}>
                <MyText
                    style={{
                        width: '100%',
                        color: COLORS.txt333333,
                        fontStyle: 'italic'
                    }}
                    text={`${SaleProgramID} - ${SaleProgramName}`}
                />
                {isSaleProgaramNotExist &&
                    <MyText text={`${translate("editSaleOrder.program")} ${SaleProgramID} ${translate("editSaleOrder.exsit_store")} ${outputStoreID}`} style={{ color: COLORS.txtFF0000, fontSize: 13 }} />
                }
            </View>
        </TouchableOpacity>
    );
};

const getSaleProgramInfo = (dataShoppingCart) => {
    let saleProgramInfo = {};
    const { SaleOrderSaleProgramInfo } = dataShoppingCart;
    if (helper.isObject(SaleOrderSaleProgramInfo)) {
        saleProgramInfo = { ...SaleOrderSaleProgramInfo };
    }
    return saleProgramInfo;
};

const isExistVina = (SaleOrderDetails) => {
    let isExist = false;
    const isNonEmpty = helper.IsNonEmptyArray(SaleOrderDetails);
    if (isNonEmpty) {
        const item = SaleOrderDetails.find(({ ConnectType, BrandIDOfSIM }) => ConnectType == CONNECTED_TYPE_SIM.NOT_CONNECTED && BrandIDOfSIM === BRAND_ID_OF_SIM.MOBILE);
        if (item) {
            isExist = true;
        }
    }
    return isExist;
};
// check sản phẩm chính, bán kèm hoặc khuyến mãi có sim vina
const checkHadVinaSim = (SaleOrderDetails) => {
    let isRequire = false;
    if (helper.IsNonEmptyArray(SaleOrderDetails)) {
        SaleOrderDetails.forEach(mainProduct => {
            const { ConnectType, BrandIDOfSIM } = mainProduct;
            if (ConnectType == CONNECTED_TYPE_SIM.NOT_CONNECTED && BrandIDOfSIM == BRAND_ID_OF_SIM.VINA) return isRequire = true;
            const { giftSaleOrders, saleSaleOrders, giftDeliverySaleOrders, saleDeliverySaleOrders } = mainProduct;
            if (giftSaleOrders.find(({ ConnectType, BrandIDOfSIM }) => ConnectType == CONNECTED_TYPE_SIM.NOT_CONNECTED && BrandIDOfSIM == BRAND_ID_OF_SIM.VINA) !== undefined) return isRequire = true;
            if (saleSaleOrders.find(({ ConnectType, BrandIDOfSIM }) => ConnectType == CONNECTED_TYPE_SIM.NOT_CONNECTED && BrandIDOfSIM == BRAND_ID_OF_SIM.VINA) !== undefined) return isRequire = true;
            if (giftDeliverySaleOrders.find(({ ConnectType, BrandIDOfSIM }) => ConnectType == CONNECTED_TYPE_SIM.NOT_CONNECTED && BrandIDOfSIM == BRAND_ID_OF_SIM.VINA) !== undefined) return isRequire = true;
            if (saleDeliverySaleOrders.find(({ ConnectType, BrandIDOfSIM }) => ConnectType == CONNECTED_TYPE_SIM.NOT_CONNECTED && BrandIDOfSIM == BRAND_ID_OF_SIM.VINA) !== undefined) return isRequire = true;
        });
    }
    return isRequire;
};

const checkHadViettelSim = (SaleOrderDetails) => {
    // let isRequire = false;
    // if (helper.IsNonEmptyArray(SaleOrderDetails)) {
    //     SaleOrderDetails.forEach(mainProduct => {
    //         const { ConnectType, BrandIDOfSIM } = mainProduct;
    //         if (ConnectType == CONNECTED_TYPE_SIM.NOT_CONNECTED && BrandIDOfSIM == BRAND_ID_OF_SIM.VIETTEL) return isRequire = true;
    //         const { giftSaleOrders, saleSaleOrders, giftDeliverySaleOrders, saleDeliverySaleOrders } = mainProduct;
    //         if (giftSaleOrders.find(({ ConnectType, BrandIDOfSIM }) => ConnectType == CONNECTED_TYPE_SIM.NOT_CONNECTED && BrandIDOfSIM == BRAND_ID_OF_SIM.VIETTEL) !== undefined) return isRequire = true;
    //         if (saleSaleOrders.find(({ ConnectType, BrandIDOfSIM }) => ConnectType == CONNECTED_TYPE_SIM.NOT_CONNECTED && BrandIDOfSIM == BRAND_ID_OF_SIM.VIETTEL) !== undefined) return isRequire = true;
    //         if (giftDeliverySaleOrders.find(({ ConnectType, BrandIDOfSIM }) => ConnectType == CONNECTED_TYPE_SIM.NOT_CONNECTED && BrandIDOfSIM == BRAND_ID_OF_SIM.VIETTEL) !== undefined) return isRequire = true;
    //         if (saleDeliverySaleOrders.find(({ ConnectType, BrandIDOfSIM }) => ConnectType == CONNECTED_TYPE_SIM.NOT_CONNECTED && BrandIDOfSIM == BRAND_ID_OF_SIM.VIETTEL) !== undefined) return isRequire = true;
    //     });
    // }
    return false;
};


const getOutputType = (SaleOrderDetails) => {
    let outputType = [];
    const isNonEmpty = helper.IsNonEmptyArray(SaleOrderDetails);
    if (isNonEmpty) {
        SaleOrderDetails.forEach(mainProduct => {
            const {
                saleSaleOrders,
                saleDeliverySaleOrders,
                OutputTypeID
            } = mainProduct;
            outputType.push(OutputTypeID);
            saleSaleOrders.forEach(saleProduct => {
                const { OutputTypeID } = saleProduct;
                outputType.push(OutputTypeID);
            });
            saleDeliverySaleOrders.forEach(saleProduct => {
                const { OutputTypeID } = saleProduct;
                outputType.push(OutputTypeID);
            });
        });
    }
    return outputType;
};

const getAttachmentType = (data) => {
    let attachmentTypeList = "";
    const {
        cus_IsRequireAttachmentSMSPromotion,
        cus_IsPromotionType19Exist
    } = data;
    if (cus_IsRequireAttachmentSMSPromotion) {
        attachmentTypeList = attachmentTypeList.concat("5", ",");
    }
    if (cus_IsPromotionType19Exist) {
        attachmentTypeList = attachmentTypeList.concat("3", ",");
    }
    return attachmentTypeList;

};