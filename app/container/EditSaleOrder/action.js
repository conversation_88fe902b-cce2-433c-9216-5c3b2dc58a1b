import { <PERSON><PERSON> } from "react-native";
import { API_CONST } from '@constants';
import { helper } from "@common";
import {
    apiBase,
    METHOD,
    ERROR,
    EMPTY
} from '@config';
import { getCartPromotion } from "../ShoppingCart/action";
import { translate } from '@translate';

const {
    API_GET_EDIT_SALEORDER,
    API_ADD_TO_SHOPPING_CART,
    API_UPDATE_SALEORDER,
    API_ADD_TO_SALE_ORDER_CART,
    API_GET_DATA_CREATE_PRICE_SO,
    API_ADD_PRODUCT_SALE_ORDER,
    API_ADD_PROMOTION_SALE_ORDER,
    API_UPDATE_SALE_PROGRAM_ORDER,
    API_SEARCHUSER,
    API_GET_REASON_CANCEL_EP,
    API_UPDATE_DELIVERYINFO_ODER,
    API_GET_SUGGEST_TIMES_SO,
    API_GET_STORE_SHIPPING_SO,
    API_GET_STORE_TIMES_SO,
    API_GET_INFO_INOUTVOUCHER,
    API_GET_PROMOTION_PROFIT_SO,
    API_GET_TIMES_BY_DATE_SO,
    API_GET_IMAGE_SO,
    API_CHECK_PROMOTION_19ANDCOUPON,
    API_UPDATE_IMAGE_SALEORDER,
    API_SEEN_CUSTOMER_INFO,
    API_SEARCH_USER_POS
} = API_CONST;

const START_GET_SALEORDER_CART = "START_GET_SALEORDER_CART";
const STOP_GET_SALEORDER_CART = "STOP_GET_SALEORDER_CART";
const START_MODIFY_SALEORDER_CART = "START_MODIFY_SALEORDER_CART";
const STOP_MODIFY_SALEORDER_CART = "STOP_MODIFY_SALEORDER_CART";
const SET_DATA_ORDER_ADJUST = "SET_DATA_ORDER_ADJUST";
const RESET_SALE_ORDER_CART = "RESET_SALE_ORDER_CART";
const START_GET_PRODUCT_PROMOTION = "START_GET_PRODUCT_PROMOTION";
const STOP_GET_PRODUCT_PROMOTION = "STOP_GET_PRODUCT_PROMOTION";
const UPDATE_RESULT_PROMOTION_EDIT = "UPDATE_RESULT_PROMOTION_EDIT";
const START_GET_DATA_PRODUCT_ORDER = "START_GET_DATA_PRODUCT_ORDER";
const STOP_GET_DATA_PRODUCT_ORDER = "STOP_GET_DATA_PRODUCT_ORDER";
const SET_APPLY_PHONE = "SET_APPLY_PHONE";
const SET_DATA_CART_DELIVERY = "SET_DATA_CART_DELIVERY";
const START_GET_DATA_AT_HOME_SO = "START_GET_DATA_AT_HOME_SO";
const STOP_GET_DATA_AT_HOME_SO = "STOP_GET_DATA_AT_HOME_SO";

export const editSaleOrderAction = {
    START_GET_SALEORDER_CART,
    STOP_GET_SALEORDER_CART,
    START_MODIFY_SALEORDER_CART,
    STOP_MODIFY_SALEORDER_CART,
    SET_DATA_ORDER_ADJUST,
    RESET_SALE_ORDER_CART,
    START_GET_PRODUCT_PROMOTION,
    STOP_GET_PRODUCT_PROMOTION,
    UPDATE_RESULT_PROMOTION_EDIT,
    START_GET_DATA_PRODUCT_ORDER,
    STOP_GET_DATA_PRODUCT_ORDER,
    SET_APPLY_PHONE,
    SET_DATA_CART_DELIVERY,
    START_GET_DATA_AT_HOME_SO,
    STOP_GET_DATA_AT_HOME_SO
}

export const getSaleOrderInfo = function (saleOrderID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "SaleOrderID": saleOrderID,
            };
            dispatch(start_get_saleorder_cart());
            apiBase(API_GET_EDIT_SALEORDER, METHOD.POST, body).then((response) => {
                console.log("getSaleOrderInfo success", response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    dispatch(stop_get_saleorder_cart(object[0]));
                    resolve(object[0]);
                }
                else {
                    dispatch(stop_get_saleorder_cart());
                    reject({ msgError: translate('editSaleOrder.order_information_not_exist') });
                }
            }).catch(error => {
                console.log("getSaleOrderInfo error", error);
                reject(error);
            })
        })
    }
}

export const modifySaleOrderCart = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "cartRequest": data.cartRequest,
                "discountCode": data.discountCode,
                "promotionGroups": data.promotionGroups,
                "giftCode": "",
            };
            dispatch(start_modify_saleorder_cart());
            apiBase(API_ADD_TO_SHOPPING_CART, METHOD.POST, body).then((response) => {
                console.log("modifySaleOrderCart success", response);
                const { object } = response;
                if (!helper.IsEmptyObject(object)) {
                    dispatch(stop_modify_saleorder_cart(object));
                    resolve(object);
                }
                else {
                    dispatch(stop_modify_saleorder_cart({}));
                    resolve({});
                }
            }).catch(error => {
                console.log("modifySaleOrderCart error", error);
                reject(error);
            })
        })
    }
}

const modifyPromotionProfit = function (body) {
    return new Promise((resolve, reject) => {
        apiBase(API_ADD_TO_SHOPPING_CART, METHOD.POST, body).then((response) => {
            console.log("modifyPromotionProfit success", response);
            const { object } = response;
            if (!helper.IsEmptyObject(object)) {
                resolve(object);
            }
            else {
                reject({ msgError: "modifyPromotionProfit error" });
            }
        }).catch(error => {
            console.log("modifyPromotionProfit error", error);
            reject(error);
        })
    })
}

export const getMultiSalePromotion = function (data, isWarning) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            if (data.cartRequest.IsSOAnKhang) {
                dispatch(getCartPromotion(data.cartRequest));
                resolve(true);
            }
            else {
                let body = {
                    "loginStoreId": getState().userReducer.storeID,
                    "languageID": getState().userReducer.languageID,
                    "moduleID": getState().userReducer.moduleID,
                    "saleScenarioTypeID": getState().specialSaleProgramReducer.saleScenarioTypeID,
                    "promotionWithSaleOrderRequest": {
                        saleOrderTypeID: 1,
                        isGetPromotionForSaleProduct: false,
                        appliedPromotion: null,
                    },
                    "discountCode": data.discountCode,
                    "giftCode": data.giftCode,
                    "cartRequest": data.cartRequest,
                    "promotionGroups": data.promotionGroups,
                };
                const cartRequest = helper.deepCopy(data.cartRequest);
                apiBase(API_GET_PROMOTION_PROFIT_SO, METHOD.POST, body)
                    .then(async (response) => {
                        console.log("getMultiSalePromotion success", response);
                        const { object } = response;
                        if (!helper.IsEmptyObject(object)) {
                            body.cartRequest = object;
                            const shoppingCart = await modifyPromotionProfit(body);
                            dispatch(stop_modify_saleorder_cart(shoppingCart));
                            dispatch(getCartPromotion(shoppingCart));
                            resolve(true);
                        }
                        else {
                            dispatch(getCartPromotion(cartRequest));
                            resolve(true);
                        }
                        resolve(true);
                    }).catch(error => {
                        console.log("getMultiSalePromotion error", error);
                        if (isWarning) {
                            Alert.alert("", error.msgError, [{
                                text: "OK",
                                onPress: () => {
                                    dispatch(getCartPromotion(cartRequest));
                                    resolve(true);
                                }
                            }]);
                        }
                        else {
                            dispatch(getCartPromotion(cartRequest));
                            resolve(true);
                        }
                    })
            }
        })
    }
}

export const updateSaleOrderCart = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "customerInfo": data.customerInfo,
                "relationShip": data.relationShip,
                "cartRequest": data.cartRequest,
                "memberPoint": data.memberPoint,
                "otp": data.otp,
                "requestIDLoyalty": data.requestIDLoyalty,
                "customerIDLoyalty": data.customerIDLoyalty,
            };
            apiBase(API_UPDATE_SALEORDER, METHOD.POST, body).then((response) => {
                const { object } = response;
                console.log("updateSaleOrderCart success", response);
                if (!helper.IsEmptyObject(object)) {
                    resolve(object);
                }
                else {
                    resolve({});
                }
            }).catch(error => {
                console.log("updateSaleOrderCart error", error);
                reject(error.msgError);
            })
        })
    }
}

export const createSaleOrderCart = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "customerInfo": data.customerInfo,
                "relationShip": data.relationShip,
                "cartRequest": data.cartRequest,
                "memberPoint": data.memberPoint,
                "otp": data.otp,
                "requestIDLoyalty": data.requestIDLoyalty,
                "customerIDLoyalty": data.customerIDLoyalty,
            };
            apiBase(API_ADD_TO_SALE_ORDER_CART, METHOD.POST, body).then((response) => {
                console.log("createSaleOrderCart success", response);
                const { object } = response;
                if (helper.hasProperty(object, "SaleOrders")) {
                    resolve(object);
                } else {
                    reject({ msgError: translate("saleOrder.error_create_order") });
                }
            }).catch(error => {
                console.log("createSaleOrderCart error", error);
                reject(error);
            })
        })
    }
}

export const addProductToSaleOrder = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "mainProduct": data.mainProduct,
                "promotionGroups": data.promotionGroups,
                "saleOrder": data.saleOrder,
                "promotionResultList": data.promotionResultList,
                "continueGetPromotionNextDetail": data.currentContinue,
                "lastContinueGetPromotion": data.lastContinue
            };
            dispatch(start_get_data_product_order());
            apiBase(API_ADD_PRODUCT_SALE_ORDER, METHOD.POST, body).then((response) => {
                console.log("addProductToSaleOrder success", response);
                const { object } = response;
                if (!helper.IsEmptyObject(object)) {
                    const {
                        saleOrder,
                        changePromotionSODetail,
                        promotionResultList,
                        continueGetPromotionNextDetail,
                        lastContinueGetPromotion,
                    } = object;
                    const isContinue = !helper.IsEmptyObject(changePromotionSODetail);
                    dispatch(stop_get_data_product_order(saleOrder));
                    if (isContinue) {
                        dispatch(getProductPromotion(changePromotionSODetail));
                        dispatch(update_result_promotion_edit(
                            promotionResultList,
                            continueGetPromotionNextDetail,
                            lastContinueGetPromotion,
                        ))
                    }
                    resolve(isContinue);
                }
                else {
                    dispatch(stop_get_data_product_order(saleOrder));
                    resolve({});
                }
            }).catch(error => {
                console.log("addProductToSaleOrder error", error);
                reject(error.msgError);
            })
        })
    }
}

const handleExpandPromotion = (data, dataSalePromotion) => {
    const expDataPromotion = {};
    const expDataPromotionDelivery = {};
    if (helper.IsNonEmptyArray(data)) {
        data.forEach(ele => {
            const {
                productID,
                productName,
                giftPromotions,
                giftPromotionsByDelivery,
                relatePromotionSaleProductListGroupID: promotionGroupID,
                relatePromotionSaleProductSubCateID: subCategoryID,
                relatePromotionSaleProductInventoryStatusID: inventoryStatusID
            } = ele;
            const dataPromotion = helper.handelGiftPromotion(giftPromotions);
            const dataPromotionDelivery = helper.handelGiftPromotion(giftPromotionsByDelivery);
            const subIndex = dataSalePromotion.findIndex(ele => ele.subCategoryID == subCategoryID);
            const keyPromotion = `${promotionGroupID}${productID}${inventoryStatusID}0`;
            const isValidateSubIndex = (subIndex > -1);
            const isValidatePromotion = helper.IsNonEmptyArray(dataPromotion) && isValidateSubIndex;
            const isValidatePromotionDelivery = helper.IsNonEmptyArray(dataPromotionDelivery) && isValidateSubIndex;
            if (isValidatePromotion) {
                expDataPromotion[keyPromotion] = {
                    "data": dataPromotion,
                    "title": productName,
                    "subIndex": subIndex,
                    "productID": productID
                };
            }
            if (isValidatePromotionDelivery) {
                expDataPromotionDelivery[keyPromotion] = {
                    "data": dataPromotionDelivery,
                    "title": productName,
                    "subIndex": subIndex,
                    "productID": productID
                };
            }
        })
    }
    return { expDataPromotion, expDataPromotionDelivery };
}

const getProductPromotion = (data) => {
    return (dispatch, getState) => {
        dispatch(start_get_product_promotion());
        const {
            mainProduct,
            giftPromotion,
            salePromotion,
            deliveryGiftPromotions,
            deliverySalePromotions,
            cusResultSalePromotionProduct,
            // giftLostSalePromotions
        } = data;
        const dataPromotion = helper.handelGiftPromotion(giftPromotion);
        const dataSalePromotion = helper.handelSalePromotion(salePromotion);
        const dataPromotionDelivery = helper.handelGiftPromotion(deliveryGiftPromotions);
        const dataSalePromotionDelivery = helper.handelSalePromotion(deliverySalePromotions);
        // const dataPromotionLostSale = helper.handelGiftPromotion(giftLostSalePromotions);
        // Tạm chưa test lostsale bên eidt.
        const dataPromotionLostSale = [];
        const allPromotion = [...dataPromotion, ...dataPromotionDelivery, ...dataPromotionLostSale];
        const allSalePromotion = [...dataSalePromotion, ...dataSalePromotionDelivery];
        const { allPromotionID } = helper.getAllKeyPromotion(allPromotion, allSalePromotion);
        dataPromotion.forEach(groupPromotion => {
            const { contentExclude, newExcludePromotionIDs } = helper.getExcludeContent(
                groupPromotion,
                allPromotionID,
                dataPromotionDelivery,
                dataSalePromotionDelivery,
                dataPromotionLostSale,
            );
            groupPromotion.excludePromotionIDs = newExcludePromotionIDs;
            groupPromotion.contentExclude = contentExclude;
        });
        dataSalePromotion.forEach(salePromotion => {
            const { promotionGroups } = salePromotion;
            promotionGroups.forEach(groupPromotion => {
                const { contentExclude, newExcludePromotionIDs } = helper.getExcludeContent(
                    groupPromotion,
                    allPromotionID,
                    dataPromotionDelivery,
                    dataSalePromotionDelivery,
                    dataPromotionLostSale,
                );
                groupPromotion.excludePromotionIDs = newExcludePromotionIDs;
                groupPromotion.contentExclude = contentExclude;
            });
        });
        dataPromotionDelivery.forEach(groupPromotion => {
            const { contentExclude, newExcludePromotionIDs } = helper.getExcludeContent(
                groupPromotion,
                allPromotionID,
                dataPromotion,
                dataSalePromotion,
                "Khuyến mãi:",
                "Bán kèm:"
            );
            groupPromotion.excludePromotionIDs = newExcludePromotionIDs;
            groupPromotion.contentExclude = contentExclude;
        });
        dataSalePromotionDelivery.forEach(salePromotion => {
            const { promotionGroups } = salePromotion;
            promotionGroups.forEach(groupPromotion => {
                const { contentExclude, newExcludePromotionIDs } = helper.getExcludeContent(
                    groupPromotion,
                    allPromotionID,
                    dataPromotion,
                    dataSalePromotion,
                    "Khuyến mãi:",
                    "Bán kèm:"
                );
                groupPromotion.excludePromotionIDs = newExcludePromotionIDs;
                groupPromotion.contentExclude = contentExclude;
            });
        });
        dataPromotionLostSale.forEach(groupPromotion => {
            const { contentExclude, newExcludePromotionIDs } = helper.getExcludeContent(
                groupPromotion,
                allPromotionID,
                dataPromotion,
                dataSalePromotion,
                "LostSale:"
            );
            groupPromotion.excludePromotionIDs = newExcludePromotionIDs;
            groupPromotion.contentExclude = contentExclude;
        });
        const allExcludeDisabled = helper.getAllExcludeDisabled(
            [...dataPromotion, ...dataPromotionDelivery, ...dataPromotionLostSale],
            [...dataSalePromotion, ...dataSalePromotionDelivery]
        );
        const { expDataPromotion, expDataPromotionDelivery } = handleExpandPromotion(cusResultSalePromotionProduct, dataSalePromotion);
        dispatch(stop_get_product_promotion(
            mainProduct,
            dataPromotion,
            dataSalePromotion,
            dataPromotionDelivery,
            dataSalePromotionDelivery,
            allExcludeDisabled,
            expDataPromotion,
            expDataPromotionDelivery,
            dataPromotionLostSale
        ));
    }
}

export const addPromotionToSaleOrder = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "mainProduct": data.mainProduct,
                "promotionGroups": data.promotionGroups,
                "saleOrder": data.saleOrder,
                "promotionResultList": data.promotionResultList,
            };
            dispatch(start_modify_saleorder_cart());
            apiBase(API_ADD_PROMOTION_SALE_ORDER, METHOD.POST, body).then((response) => {
                console.log("addPromotionToSaleOrder success", response);
                const { object } = response;
                if (!helper.IsEmptyObject(object)) {
                    dispatch(stop_modify_saleorder_cart(object));
                    resolve(object);
                }
                else {
                    dispatch(stop_modify_saleorder_cart({}));
                    resolve({});
                }
            }).catch(error => {
                console.log("addPromotionToSaleOrder error", error);
                reject(error.msgError);
            })
        })
    }
}

export const deleteSaleOrderCart = function (data) {
    return (dispatch, getState) => {
        dispatch(stop_get_saleorder_cart(data));
        dispatch(stop_get_data_product_order(data));
    }
}

export const setDataSaleOrderCart = (data) => {
    return (dispatch, getState) => {
        dispatch(stop_get_saleorder_cart(data));
    }
}

export const getDataAdjustPrice = function (cartAdjustPrice) {
    return (dispatch, getState) => {
        dispatch(set_data_adjust_price_order(cartAdjustPrice));
        return Promise.resolve();
    }
}

export const getDataCreatePrice4SO = function (cartRequest) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "cartRequest": cartRequest,
                "discountCode": "",
                "giftCode": "",
                "promotionGroups": []
            };
            apiBase(API_GET_DATA_CREATE_PRICE_SO, METHOD.POST, body).then((response) => {
                console.log("getDataCreatePrice4SO success", response);
                const { object } = response;
                if (!helper.IsEmptyObject(object)) {
                    resolve(true);
                    dispatch(set_data_adjust_price_order(object));
                }
                else {
                    reject(translate('editSaleOrder.cannot_data_price'));
                }
            }).catch(error => {
                console.log("getDataCreatePrice4SO error", error);
                reject(error.msgError);
            })
        })
    }
}

export const getDataAddOrderCart = function (cartRequest) {
    return function (dispatch, getState) {
        dispatch(stop_get_data_product_order(cartRequest));
    }
}

export const updateSaleProgramOrder = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "cartRequest": data.cartRequest,
                "interateRequest": data.interateRequest,
                "saleProgramID": data.saleProgramID,
            };
            dispatch(start_get_data_product_order());
            apiBase(API_UPDATE_SALE_PROGRAM_ORDER, METHOD.POST, body).then((response) => {
                console.log("updateSaleProgramOrder success", response);
                const { object } = response;
                if (!helper.IsEmptyObject(object)) {
                    const {
                        saleOrder,
                        changePromotionSODetail,
                        promotionResultList,
                        continueGetPromotionNextDetail,
                        lastContinueGetPromotion,
                    } = object;
                    const isContinue = !helper.IsEmptyObject(changePromotionSODetail);
                    dispatch(stop_get_data_product_order(saleOrder));
                    if (isContinue) {
                        dispatch(getProductPromotion(changePromotionSODetail));
                        dispatch(update_result_promotion_edit(
                            promotionResultList,
                            continueGetPromotionNextDetail,
                            lastContinueGetPromotion,
                        ))
                    }
                    resolve(isContinue);
                }
                else {
                    dispatch(stop_get_data_product_order(saleOrder));
                    resolve({});
                }
            }).catch(error => {
                console.log("updateSaleProgramOrder error", error);
                reject(error.msgError);
            })
        })
    }
}

export const set_apply_phone = (phoneApply) => {
    return ({
        type: SET_APPLY_PHONE,
        phoneApply
    });
}

export const getUserInfo = function (userName) {
    return new Promise((resolve, reject) => {
        let body = {
            "keyword": userName,
            "username": userName,
        };
        apiBase(API_SEARCHUSER, METHOD.POST, body, { setTimeOut: 10000 }).then((response) => {
            console.log("getUserInfo success", response);
            const { object } = response;
            if (helper.IsNonEmptyArray(object)) {
                const { fullname, positionname, mobi } = object[0];
                resolve({ username: userName, fullname, positionname, mobi });
            }
            else {
                resolve({ username: userName, fullname: "", positionname: "", mobi: "" });
            }
        }).catch(error => {
            console.log("getUserInfo error", error);
            reject(error.msgError);
        })
    })
}

export const getReasonCancelEP = function (cartRequest) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "shoppingCartRequest": cartRequest,
            };
            apiBase(API_GET_REASON_CANCEL_EP, METHOD.POST, body).then((response) => {
                console.log("getReasonCancelEP success", response);
                const { object } = response;
                if (!helper.IsEmptyObject(object)) {
                    const { EPOSCancelReasonCacheBOList } = object;
                    if (helper.isArray(EPOSCancelReasonCacheBOList)) {
                        resolve(EPOSCancelReasonCacheBOList);
                    }
                    else {
                        resolve([]);
                    }
                }
                else {
                    resolve([]);
                }
            }).catch(error => {
                console.log("getReasonCancelEP error", error);
                reject(error.msgError);
            })
        })
    }
}

export const updateDeliveryInfo = function (cartRequest) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "shoppingCartRequest": cartRequest,
            };
            dispatch(start_get_data_product_order());
            apiBase(API_UPDATE_DELIVERYINFO_ODER, METHOD.POST, body).then((response) => {
                console.log("updateDeliveryInfo success", response);
                const { object } = response;
                if (!helper.IsEmptyObject(object)) {
                    const {
                        saleOrder,
                        changePromotionSODetail,
                        promotionResultList,
                        continueGetPromotionNextDetail,
                        lastContinueGetPromotion,
                    } = object;
                    const isContinue = !helper.IsEmptyObject(changePromotionSODetail);
                    dispatch(stop_get_data_product_order(saleOrder));
                    if (isContinue) {
                        dispatch(getProductPromotion(changePromotionSODetail));
                        dispatch(update_result_promotion_edit(
                            promotionResultList,
                            continueGetPromotionNextDetail,
                            lastContinueGetPromotion,
                        ))
                    }
                    resolve({ isContinue, saleOrder });
                }
                else {
                    dispatch(stop_get_data_product_order(saleOrder));
                    resolve({});
                }
            }).catch(error => {
                console.log("updateDeliveryInfo error", error);
                reject(error.msgError);
            })
        })
    }
}

export const updateDataCartDelivery = function (data) {
    return (dispatch, getState) => {
        const { storeInfo } = data;
        dispatch(set_data_cart_delivery(data));
        dispatch(stop_get_data_at_home_so([storeInfo]));
    }
}

export const getDataAtHomeSO = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "provinceID": data.provinceID,
                "districtID": data.districtID,
                "wardID": data.wardID,
                "deliveryAddress": data.deliveryAddress,
                "stockProducts": data.stockProducts,
                "saleProgramID": data.saleProgramID,
                "createDateSO": data.createDateSO,
                "saleOrderIDDelete": data.saleOrderIDDelete,
                "saleOrderTypeID": data.saleOrderTypeID,
                "getStockType": 3,
                "storeID": 0,
                "distance": 0,
                "pageIndex": 1,
                "pageSize": 3,
            };
            dispatch(start_get_data_at_home_so());
            apiBase(API_GET_STORE_SHIPPING_SO, METHOD.POST, body).then((response) => {
                console.log("getDataAtHome success", response);
                const { object, errorReason } = response;
                let storeDelivery = {}
                if (helper.IsNonEmptyArray(object)) {
                    for (let i = object.length - 1; i >= 0; i--) {
                        if (object[i].isExtra) {
                            storeDelivery = object[i]
                            object.splice(i, 1);
                            break
                        }
                    }
                    dispatch(stop_get_data_at_home_so(object));
                }
                else {
                    const content = errorReason || translate('editSaleOrder.no_find_store');
                    dispatch(stop_get_data_at_home_so([], EMPTY, content));
                }
                resolve({
                    storeDelivery: storeDelivery
                });
            }).catch(error => {
                console.log("getDataAtHome error", error);
                dispatch(stop_get_data_at_home_so([], !EMPTY, error.msgError, ERROR));
                resolve({
                    storeDelivery: {}
                });
            })
        })

    }
}

export const getStoreInfoShippingSO = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "storeID": data.storeID,
                "distance": data.distance,
                "provinceID": data.provinceID,
                "districtID": data.districtID,
                "wardID": data.wardID,
                "deliveryAddress": data.deliveryAddress,
                "stockProducts": data.stockProducts,
                "saleProgramID": data.saleProgramID,
                "createDateSO": data.createDateSO,
                "saleOrderIDDelete": data.saleOrderIDDelete,
                "saleOrderTypeID": data.saleOrderTypeID,
                "deliveryTypeID": data.deliveryTypeID,
                "deliveryTime": data.deliveryTime,
                "deliveryVehicles": data.deliveryVehicles || 1,
                "isNotChangeDeliveryAddress": data.isNotChangeDeliveryAddress,
                "getStockType": 3,
                "pageIndex": 1,
                "pageSize": 3,
            };
            apiBase(API_GET_SUGGEST_TIMES_SO, METHOD.POST, body, { setTimeOut: 30000 }).then((response) => {
                console.log("getStoreInfoShippingSO success", response);
                const { object } = response;
                let storeDelivery = {}
                for (let i = object?.length - 1; i >= 0; i--) {
                    if (object?.[i]?.isExtra) {
                        storeDelivery = object[i]
                        object?.splice(i, 1);
                        break
                    }
                }
                if (helper.IsNonEmptyArray(object)) {
                    const { suggestTimes, storeID, messageError } = object[0];
                    const isEmptyTime = helper.IsEmptyObject(suggestTimes);
                    if (messageError) {
                        reject(messageError);
                    }
                    else if (isEmptyTime) {
                        reject(translate('editSaleOrder.expired_time'));
                    }
                    else if (storeID != data.storeID) {
                        reject(translate('editSaleOrder.no_delivery_information_found'));
                    }
                    else {
                        resolve({
                            storeInfo: object[0],
                            storeDelivery: storeDelivery
                        });
                    }
                }
                else {
                    reject(translate('editSaleOrder.no_delivery_information_found'));
                }
            }).catch(error => {
                console.log("getStoreInfoShippingSO error", error);
                reject(error.msgError);
            })
        })
    }
}

export const getSuggestTimeByDateSO = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "stockProducts": data.stockProducts,
                "storeID": data.storeID,
                "distance": data.distance,
                "provinceID": data.provinceID,
                "districtID": data.districtID,
                "wardID": data.wardID,
                "deliveryAddress": data.deliveryAddress,
                "saleProgramID": data.saleProgramID,
                "createDateSO": data.createDateSO,
                "saleOrderIDDelete": data.saleOrderIDDelete,
                "saleOrderTypeID": data.saleOrderTypeID,
                "deliveryTypeID": data.deliveryTypeID,
                "deliveryVehicles": data.deliveryVehicles,
                "dateFrom": data.deliveryTime,
                "dateTo": null,
                "getStockType": 3,
                "pageIndex": 1,
                "pageSize": 3,
            };
            apiBase(API_GET_TIMES_BY_DATE_SO, METHOD.POST, body, { setTimeOut: 10000 }).then((response) => {
                console.log("getSuggestTimeByDateSO success", response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    const { suggestTimes } = object[0];
                    if (helper.IsNonEmptyArray(suggestTimes)) {
                        resolve(suggestTimes);
                    }
                    else {
                        reject(translate("editSaleOrder.no_delivery_information"));
                    }
                }
                else {
                    reject(translate("editSaleOrder.no_delivery_information"));
                }
            }).catch(error => {
                console.log("getSuggestTimeByDateSO error", error);
                reject(error.msgError);
            })
        })
    }
}

export const getStoreInfoSO = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "storeID": data.storeID,
                "provinceID": data.provinceID,
                "districtID": data.districtID,
                "wardID": data.wardID,
                "deliveryAddress": data.deliveryAddress,
                "stockProducts": data.stockProducts,
                "saleProgramID": data.saleProgramID,
                "createDateSO": data.createDateSO,
                "saleOrderIDDelete": data.saleOrderIDDelete,
                "deliveryTypeID": data.deliveryTypeID,
                "deliveryTime": data.deliveryTime,
                "deliveryVehicles": data.deliveryVehicles,
                "saleOrderTypeID": data.saleOrderTypeID,
                "getStockType": 1,
                "distance": 0,
                "pageIndex": 1,
                "pageSize": 3,
            };
            apiBase(API_GET_STORE_TIMES_SO, METHOD.POST, body, { setTimeOut: 30000 }).then((response) => {
                console.log("getStoreInfoSO success", response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    resolve(object);
                }
                else {
                    reject(translate('editSaleOrder.no_delivery_information'));
                }
            }).catch(error => {
                console.log("getStoreInfoSO error", error);
                reject(error.msgError);
            })
        })
    }
}

export const getInfoInOutVoucher = function (cartRequest) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "shoppingCartRequest": cartRequest,
            };
            apiBase(API_GET_INFO_INOUTVOUCHER, METHOD.POST, body).then((response) => {
                console.log("getInfoInOutVoucher success", response);
                const { object } = response;
                if (!helper.IsEmptyObject(object)) {
                    resolve(object);
                }
                else {
                    resolve({});
                }
            }).catch(error => {
                console.log("getInfoInOutVoucher error", error);
                reject(error.msgError);
            })
        })
    }
}

export const getImageSaleOrder = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "saleOrderID": data.saleOrderID,
                "saleOrderDetailID": data.saleOrderDetailID,
                "attachmentTypeList": data.attachmentTypeList
            };
            apiBase(API_GET_IMAGE_SO, METHOD.POST, body).then((response) => {
                const { object } = response;
                console.log("getImageSO success", response);
                if (!helper.IsEmptyArray(object)) {
                    resolve(object);
                }
                else {
                    resolve([]);
                }
            }).catch(error => {
                console.log("getImageSO error", error);
                reject(error.msgError);
            })
        })
    }
}

export const checkPromotion19AndCoupon = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "saleOrderID": data,
            };
            apiBase(API_CHECK_PROMOTION_19ANDCOUPON, METHOD.POST, body).then((response) => {
                console.log("checkPromotion19AndCoupon sucess", response);
                const { object } = response;
                resolve(object);
            }).catch(error => {
                console.log("checkPromotion19AndCoupon error", error);
                reject(error);
            })
        })
    }
}

export const updateImageSaleOrder = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                ...data
            };
            apiBase(API_UPDATE_IMAGE_SALEORDER, METHOD.POST, body).then((response) => {
                console.log("updateImageSaleOrder sucess", response);
                const { object } = response;
                resolve(object);
            }).catch(error => {
                console.log("updateImageSaleOrder error", error);
                reject(error);
            })
        })
    }
}

export const logSeenCustomerInfo = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "sourceTypeID": "1",
                "entityTypeID": 1,
                "sourceCode": data
            };
            apiBase(API_SEEN_CUSTOMER_INFO, METHOD.POST, body).then((response) => {
                console.log("logSeenCustomerInfo sucess", response);
                resolve(response);
            }).catch(error => {
                console.log("logSeenCustomerInfo error", error);
                reject(error);
            })
        })
    }
}

export const searchUserPos = function (keyWord) {
    return new Promise((resolve, reject) => {
        const body = {
            "UserName": keyWord,
        };
        apiBase(API_SEARCH_USER_POS, METHOD.POST, body).then((response) => {
            console.log("searchUserPos success", response);
            const { object } = response;
            if (helper.IsNonEmptyArray(object)) {
                resolve(object);
            }
            else {
                reject("Không tìm thấy thông tin nhân viên.");
            }
        }).catch(error => {
            console.log("searchUserPos error", error);
            reject(error.msgError);
        })
    })
}

const start_get_saleorder_cart = () => {
    return ({
        type: START_GET_SALEORDER_CART
    });
}

const stop_get_saleorder_cart = (dataShoppingCart = {}) => {
    return ({
        type: STOP_GET_SALEORDER_CART,
        dataShoppingCart,
    });
}

const start_modify_saleorder_cart = () => {
    return ({
        type: START_MODIFY_SALEORDER_CART
    });
}

const stop_modify_saleorder_cart = (dataShoppingCart = {}) => {
    return ({
        type: STOP_MODIFY_SALEORDER_CART,
        dataShoppingCart,
    });
}

const set_data_adjust_price_order = (dataCartAdjust) => {
    return ({
        type: SET_DATA_ORDER_ADJUST,
        dataCartAdjust
    });
}

const start_get_product_promotion = () => {
    return ({
        type: START_GET_PRODUCT_PROMOTION
    });
}

const stop_get_product_promotion = (
    product,
    promotion,
    salePromotion,
    promotionDelivery,
    salePromotionDelivery,
    allExcludeDisabled,
    expDataPromotion,
    expDataPromotionDelivery,
    dataPromotionLostSale
) => {
    return ({
        type: STOP_GET_PRODUCT_PROMOTION,
        product,
        promotion,
        salePromotion,
        promotionDelivery,
        salePromotionDelivery,
        allExcludeDisabled,
        expDataPromotion,
        expDataPromotionDelivery,
        dataPromotionLostSale
    });
}

const update_result_promotion_edit = (
    promotionResultList,
    currentContinue,
    lastContinue
) => {
    return ({
        type: UPDATE_RESULT_PROMOTION_EDIT,
        promotionResultList,
        currentContinue,
        lastContinue
    });
}

const start_get_data_product_order = () => {
    return ({
        type: START_GET_DATA_PRODUCT_ORDER
    });
}

const stop_get_data_product_order = (dataCartProduct) => {
    return ({
        type: STOP_GET_DATA_PRODUCT_ORDER,
        dataCartProduct
    });
}

const set_data_cart_delivery = (dataCartDelivery) => {
    return ({
        type: SET_DATA_CART_DELIVERY,
        dataCartDelivery
    });
}

const start_get_data_at_home_so = () => {
    return ({
        type: START_GET_DATA_AT_HOME_SO
    });
}

const stop_get_data_at_home_so = (
    storeAtHome,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_DATA_AT_HOME_SO,
        storeAtHome,
        isEmpty,
        description,
        isError,
    });
}