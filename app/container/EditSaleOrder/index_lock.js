import React, { Component, useRef, useState } from 'react';
import {
    View,
    FlatList,
    StyleSheet,
    BackHandler,
    TouchableOpacity,
    Image,
    Alert
} from 'react-native';
import {
    TitleInput,
    MyText,
    ImageURI,
    Picker,
    Icon,
    Button,
    hideBlockUI,
    showBlock<PERSON>
} from "@components";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import SafeAreaView from 'react-native-safe-area-view';
import { connect, useDispatch } from 'react-redux';
import { constants, API_CONST } from "@constants";
import { helper } from "@common";
import * as actionEditSaleOrderCreator from "./action";

import LockProductInfo from "./component/LockProductInfo";
import LockCoupon from "./component/LockCoupon";
import LockCartPromotion from "./component/LockCartPromotion";
import RadioGender from "./component/Radio/RadioGender";
import RadioRelation from "./component/Radio/RadioRelation";
import RadioInstallment from "./component/Radio/RadioInstallment";
import ModalDeposit from "./component/Modal/ModalDeposit";
import ModalLoyalty from "./component/Modal/ModalLoyalty";
import ModalFee from "./component/Modal/ModalFee";
import TitleInputMoney from "./component/TitleInputMoney";
import InsuranceInfo from "./component/InsuranceInfo";
import LockDeliveryInfo from "./component/LockDeliveryInfo";
import { translate } from '@translate';
import { COLORS } from "@styles";
import ModalProfitPromotion from "./component/Modal/ModalProfitPromotion";
import { PrescriptionScreen } from '../AnKhangPharmacy/screens';
import LockPrescriptionImages from './component/LockPrescriptionImages';
import { logSeenCustomerInfo } from './action';
import { BlockUI } from '../Detail';
import { PackagePriceSheet } from '../Detail/Sheets';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { SearchUserSheet } from './sheets';
import SearchUserInner from './component/SearchUserInner';
import { bindActionCreators } from 'redux';

const { PARTNER_ID } = constants
const {
    API_LOGO_PARTNER_INSTALLMENT
} = API_CONST;

class LockSaleOrder extends Component {

    constructor(props) {
        super(props);
        this.state = {
            discountCode: "",

            gender: 1,
            customerPhone: "",
            customerName: "",
            customerAddress: "",
            taxID: "",
            contactPhone: "",
            contactName: "",
            contactAddress: "",

            customerIDCard: "",
            totalPrePaid: "",
            termLoan: 0,
            saleProgramInfo: {},
            isInsuranceFee: false,
            isInsuranceGood: false,
            goodInsuranceID: 0,
            paymentMonthly: 0,

            relationShipType: null,
            isCompany: false,

            isVisibleDeposit: false,
            isVisibleLoyalty: false,

            isRequirePhone: false,
            isVisibleVoucher: false,

            isHasSaleProgram: false,
            isLockCustomer: false,
            isAllowInvoice: true,
            isLockPhone: false,
            isLockName: false,
            isLockAddress: false,
            isLockTax: false,
            isVisibleProfit: false,
            isShowDrug: false,
            isShowCustomerID: false,
            dataPackagePrice: [],
            blockUI: false,
            saleUserInfo: {},


        };
        this.setKeyPromotion = new Set();
        this.promotionGroups = [];
        this.packagePriceSheetRef = React.createRef(null);
        this.SearchUserSheetRef = React.createRef(null);


    }

    componentDidMount() {
        //addEventListener "addEventListener"
        BackHandler.addEventListener('addEventListener', this.onBackButtonPressed);
    }

    onBackButtonPressed = () => {
        return true;
    }

    componentWillUnmount() {
        // this.backHandler.remove();
        BackHandler.removeEventListener('hardwareBackPress', this.onBackButtonPressed);
    }

    updateSaleOrder = () => {
        const { dataShoppingCart } = this.props;
        const { saleUserInfo } = this.state
        const customerInfo = {
            ...dataShoppingCart.CustomerInfo,
            "customerName": dataShoppingCart.CustomerInfo.CustomerName,
            "customerAddress": dataShoppingCart.CustomerInfo.CustomerAddress || "x",
            "customerPhone": dataShoppingCart.CustomerInfo.CustomerPhone,
            "taxID": dataShoppingCart.CustomerInfo.TaxID,
            "gender": dataShoppingCart.CustomerInfo.Gender,
            "contactName": dataShoppingCart.CustomerInfo.ContactName,
            "contactPhone": dataShoppingCart.CustomerInfo.ContactPhone,
            "deliveryAddress": dataShoppingCart.CustomerInfo.DeliveryAddress,
            "contactGender": dataShoppingCart.CustomerInfo.ContactGender,
            "customerIDCard": dataShoppingCart.CustomerInfo.CustomerIDCard,
            "ageID": "",
            "birthday": ""
        }


        const dataSaleOrder = {
            "customerInfo": customerInfo,
            "relationShip": {
                "relationShipType": dataShoppingCart.RelationShipType
            },
            "cartRequest": {
                ...dataShoppingCart,
                CustomerInfo: customerInfo,
                "StaffUser": saleUserInfo?.UserName?.length > 0 ? saleUserInfo?.UserName : dataShoppingCart.StaffUser
            },
            "requestIDLoyalty": null,
            "customerIDLoyalty": null,
        };
        showBlockUI();
        this.props.actionEditSaleOrder.updateSaleOrderCart(dataSaleOrder).then(orderInfo => {

            hideBlockUI();
            this.props.navigation.goBack();

        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: this.updateSaleOrder
                    }
                ]
            );
        });
    }

    render() {
        const {
            dataShoppingCart: {
                SaleOrderDetails,
                SHCouponDiscountAmount,
                SHAmount,
                TotalPointLoyalty,
                SHChangeTranferAmountFee,
                SHForwarderFeeAmount,
                TotalAdvance,
                ShippingCost,
                giftShoppingCartSaleOrders,
                CustomerInfo,
                RelationShipType,
                DiscountCode,
                IsInstallment,
                SaleOrderSaleProgramInfo,
                IsAutoCreateEP,
                DeliveryInfo,
                Note,
                ErrorMessageOutput,
                cus_PromDiscountExtraMaster,
                IsSOScreenSticker,
                IsSOAnKhang,
                cus_SaleOrderDoSageBOList,
                saleShoppingCartSaleOrders,
                SaleOrderID,
                CustomerNote,
                cus_CandidateNO,
                EditFieldNames = {},
                StaffFullName,
                StaffUser,
            },
            PRDLISTDISABLEDISCOUNTCODE,
        } = this.props;
        const { StaffUser: StaffUserFromEditField = false } = EditFieldNames
        const newDiscountCode = getDiscountCode(PRDLISTDISABLEDISCOUNTCODE, SaleOrderDetails, DiscountCode);
        const saleCartSaleOrders = saleShoppingCartSaleOrders ?? []
        const {
            isVisibleDeposit,
            isVisibleLoyalty,
            isVisibleFee,
            isVisibleProfit,
            isShowDrug,
            saleUserInfo
        } = this.state;
        const isShowDrugLock = IsSOAnKhang && cus_SaleOrderDoSageBOList?.length > 0;
        const titleUser = !!saleUserInfo.UserName ? `${saleUserInfo.UserName} - ${saleUserInfo.FullName}` : `${StaffUser} - ${StaffFullName}` || ""

        return (
            <View style={{ flex: 1 }}>
                <BottomSheetModalProvider>
                    <KeyboardAwareScrollView
                        style={{
                            flex: 1
                        }}
                        enableResetScrollToCoords={false}
                        keyboardShouldPersistTaps="always"
                        bounces={false}
                        overScrollMode="always"
                        showsHorizontalScrollIndicator={false}
                        showsVerticalScrollIndicator={false}
                        extraScrollHeight={60}
                    >
                        <SafeAreaView
                            style={{
                                flex: 1,
                                backgroundColor: COLORS.bgFAFAFA
                            }}
                        >
                            <FlatList
                                data={SaleOrderDetails}
                                renderItem={({ item, index }) => (<LockProductInfo
                                    onChangeSheet={() => {
                                        if (!helper.IsEmptyObject(item.PricePolicyApplyBO)) {
                                            const dataPackagePrice = [
                                                {
                                                    ...item.PricePolicyApplyBO,
                                                    DetailPolicies: item.PricePolicyApplyBO.lstSaleOrderPricePolicyApplyDetailBO
                                                }
                                            ]
                                            this.setState({ dataPackagePrice: dataPackagePrice }, () => {
                                                this.packagePriceSheetRef.current?.present();
                                            })
                                        }
                                        else {
                                            Alert.alert(translate("common.notification"), "Không có thông tin dịch vụ.", [
                                                {
                                                    text: 'OK',
                                                    style: 'cancel'
                                                }
                                            ]);
                                        }
                                    }}
                                    mainProduct={item}
                                    couponDiscount={SHCouponDiscountAmount}
                                    isSticker={IsSOScreenSticker}
                                />)}
                                keyExtractor={(item, index) => item.SaleOrderDetailID}
                                keyboardShouldPersistTaps={"always"}
                                ListHeaderComponent={
                                    <View style={{
                                        width: constants.width
                                    }}>
                                        <InstallmentInfo
                                            programInfo={SaleOrderSaleProgramInfo}
                                            installment={IsInstallment}

                                        />
                                        <View style={{
                                            justifyContent: 'center',
                                            backgroundColor: COLORS.bg6A9C84,
                                            width: constants.width,
                                            height: 40,
                                            paddingHorizontal: 10,
                                        }}>
                                            <MyText
                                                style={{
                                                    color: COLORS.txtFFFFFF,
                                                    fontWeight: 'bold'
                                                }}
                                                text={translate('editSaleOrder.products_list')}
                                            />
                                        </View>
                                    </View>
                                }
                                ListFooterComponent={
                                    <View style={{
                                        width: constants.width
                                    }}>
                                        <LockCoupon
                                            discountCode={newDiscountCode}
                                            couponDiscount={SHCouponDiscountAmount}
                                        />
                                        <LockCartPromotion
                                            giftSaleOrders={giftShoppingCartSaleOrders}
                                            saleSaleOrders={saleCartSaleOrders}

                                        />
                                        <FeeAmount
                                            total={ShippingCost}
                                            onShow={() => {
                                                this.setState({ isVisibleFee: true })
                                            }}
                                        />
                                        <TotalAmount
                                            total={SHAmount}
                                        />
                                        <DepositAmount
                                            total={TotalAdvance}
                                            onShow={() => {
                                                this.setState({ isVisibleDeposit: true })
                                            }}
                                        />
                                        <PointLoyalty
                                            total={TotalPointLoyalty}
                                            onShow={() => {
                                                this.setState({ isVisibleLoyalty: true })
                                            }}
                                        />
                                        <LockDeliveryInfo
                                            deliveryInfo={DeliveryInfo}
                                            note={Note || CustomerNote}
                                        />
                                        <CustomerCartInfo
                                            info={CustomerInfo}
                                            saleOrderID={SaleOrderID}
                                            onSetShowCustomerID={(bool) => { this.setState({ isShowCustomerID: bool }) }}
                                            candidateNo={cus_CandidateNO}
                                            StaffUser={StaffUserFromEditField}
                                            searchUserSheetRef={this.SearchUserSheetRef}
                                            saleUserInfo={saleUserInfo}
                                            titleUser={titleUser}
                                        />
                                        {
                                            IsInstallment &&
                                            <ContractInfo
                                                SHAmount={SHAmount}
                                                saleProgramInfo={SaleOrderSaleProgramInfo}
                                                isShowCustomerID={this.state.isShowCustomerID}
                                            />
                                        }
                                        {
                                            IsSOAnKhang &&
                                            <LockPrescriptionImages />
                                        }
                                        <RadioRelationShip
                                            type={RelationShipType}
                                            onChangeType={() => { }}
                                        />

                                        {
                                            StaffUserFromEditField ?
                                                <View style={{
                                                    width: constants.width,
                                                    alignItems: "center",
                                                    justifyContent: "center",
                                                    flexDirection: "row",
                                                    paddingVertical: 10
                                                }}>
                                                    <Button
                                                        text={translate('editSaleOrder.btn_update_uppercase')}
                                                        styleContainer={{
                                                            borderRadius: 4,
                                                            backgroundColor: COLORS.btn288AD6,
                                                            height: 44,
                                                            width: 140,
                                                            opacity: 1,
                                                        }}
                                                        activeOpacity={1}
                                                        styleText={{
                                                            color: COLORS.txtFFFFFF,
                                                            fontSize: 14,
                                                            fontWeight: "bold"
                                                        }}
                                                        onPress={this.updateSaleOrder}
                                                    />
                                                </View>
                                                : <MyText
                                                    style={{
                                                        color: COLORS.txtFF0000,
                                                        fontWeight: 'bold'
                                                    }}
                                                    text={ErrorMessageOutput}
                                                />
                                        }
                                    </View>
                                }
                            />
                            <ModalDeposit
                                isVisible={isVisibleDeposit}
                                hideModal={() => {
                                    this.setState({ isVisibleDeposit: false })
                                }}
                                saleOrderDetails={SaleOrderDetails}
                            />
                            <ModalFee
                                isVisible={isVisibleFee}
                                hideModal={() => {
                                    this.setState({ isVisibleFee: false })
                                }}
                                SHChangeTranferAmountFee={SHChangeTranferAmountFee}
                                SHForwarderFeeAmount={SHForwarderFeeAmount}
                                ShippingCost={ShippingCost}
                            />
                            <ModalLoyalty
                                isVisible={isVisibleLoyalty}
                                hideModal={() => {
                                    this.setState({ isVisibleLoyalty: false })
                                }}
                                saleOrderDetails={SaleOrderDetails}
                            />
                            <ModalProfitPromotion
                                isVisible={isVisibleProfit}
                                hideModal={() => {
                                    this.setState({ isVisibleProfit: false })
                                }}
                                data={cus_PromDiscountExtraMaster}
                            />

                            <BlockUI
                                visible={this.state.blockUI}
                                onChangeVisible={(value) => {
                                    this.setState({
                                        blockUI: value
                                    });
                                    this.packagePriceSheetRef.current.dismiss();
                                }}
                            />
                        </SafeAreaView>
                    </KeyboardAwareScrollView>
                    {
                        isShowDrugLock &&
                        <View>
                            <PrescriptionScreen
                                saleOrderDetail={cus_SaleOrderDoSageBOList}
                                isVisible={isShowDrug}
                                hideModal={() => {
                                    this.setState({ isShowDrug: false })
                                }}
                                disabled={true}
                                handleGetDataDrug={(dataDrug) => {
                                    // this.handleGetDataDrug(dataDrug)
                                }}
                            />
                            <TouchableOpacity
                                onPress={() => {
                                    this.setState({ isShowDrug: true })
                                }}
                                style={{
                                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                                    position: "absolute",
                                    bottom: 30,
                                    right: 10,
                                    width: 40,
                                    height: 40,
                                    borderRadius: 20,
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    shadowColor: "#000",
                                    shadowOffset: {
                                        width: 0,
                                        height: 2,
                                    },
                                    shadowOpacity: 0.25,
                                    shadowRadius: 3.84,
                                    elevation: 5,
                                }}>
                                <Icon
                                    name={"pill"}
                                    iconSet={"MaterialCommunityIcons"}
                                    size={22}
                                    color={"#40B93C"}
                                />
                            </TouchableOpacity>
                        </View>
                    }
                    {
                        <PackagePriceSheet
                            ref={this.packagePriceSheetRef}
                            snapPoints={['99.99999%']}
                            product={this.state.dataPackagePrice}
                            listPackageService={this.state.dataPackagePrice}
                            onGoNext={(data) => { }}
                            getCurrentIndex={() => {
                                console.log('');
                            }}
                            disabled={false}
                            topInset={200}
                            onCloseSheet={() => {
                                this.setState({ blockUI: false });
                            }}
                        />
                    }
                    <SearchUserSheet
                        bottomSheetRef={this.SearchUserSheetRef}
                        onChangeStatusSheet={() => { }}
                        staffInfo={{ name: titleUser }}
                    >
                        <SearchUserInner
                            handleSelectUser={(user) => {
                                this.setState({ saleUserInfo: user })
                                this.SearchUserSheetRef.current?.close()
                            }}
                        />
                    </SearchUserSheet>
                </BottomSheetModalProvider>
            </View>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        dataShoppingCart: state.editSaleOrderReducer.dataShoppingCart,
        PRDLISTDISABLEDISCOUNTCODE: state.appSettingReducer.PRDLISTDISABLEDISCOUNTCODE
    }
}
const mapDispatchToProps = function (dispatch) {
    return {
        actionEditSaleOrder: bindActionCreators(actionEditSaleOrderCreator, dispatch),
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(LockSaleOrder);

const TotalAmount = ({ total }) => {
    return (
        <View style={{
            backgroundColor: COLORS.bgF7EED6,
            flexDirection: 'row',
            width: constants.width,
            justifyContent: 'space-between',
            paddingHorizontal: 10,
            height: 40,
            borderTopWidth: StyleSheet.hairlineWidth,
            borderTopColor: COLORS.bdFFFFFF,
        }}>
            <View style={{
                flexDirection: 'row',
                alignSelf: 'flex-end',
                alignItems: 'center',
                height: 40,
            }}>
                <MyText
                    style={{
                        color: COLORS.txtFF6A00,
                        fontWeight: 'bold'
                    }}
                    text={translate('editSaleOrder.total_price')}
                    children={
                        <MyText
                            style={{
                                color: COLORS.txt666666,
                                fontStyle: 'italic',
                                fontWeight: 'normal'
                            }}
                            text={translate('editSaleOrder.not_rounded')}
                        />
                    }
                />
            </View>

            <View style={{
                alignSelf: 'flex-end',
                justifyContent: 'center',
                height: 40,
            }}>
                <MyText
                    style={{
                        color: COLORS.txtFF0000,
                        fontWeight: 'bold'
                    }}
                    text={helper.convertNum(total)}
                    addSize={2}
                />
            </View>
        </View>
    );
}

const DepositAmount = ({ total, onShow }) => {
    const isShow = (total > 0);
    return (
        isShow &&
        <View style={{
            backgroundColor: COLORS.bgE4EBD5,
            flexDirection: 'row',
            width: constants.width,
            justifyContent: 'space-between',
            paddingHorizontal: 10,
            height: 40,
            borderTopWidth: StyleSheet.hairlineWidth,
            borderTopColor: COLORS.bdFFFFFF
        }}>
            <View style={{
                flexDirection: 'row',
                alignSelf: 'flex-end',
                alignItems: 'center',
                height: 40,
            }}>
                <MyText
                    style={{
                        color: COLORS.txtFC3158,
                        fontWeight: 'bold',
                        textDecorationLine: 'underline'
                    }}
                    onPress={onShow}
                    text={translate('editSaleOrder.total_customer_deposit')}
                />
            </View>

            <View style={{
                alignSelf: 'flex-end',
                justifyContent: 'center',
                height: 40,
            }}>
                <MyText
                    addSize={2}
                    style={{
                        color: COLORS.txtFF6600,
                        fontWeight: 'bold'
                    }}
                    text={helper.convertNum(total)}
                />
            </View>
        </View>
    );
}

const FeeAmount = ({ total, onShow }) => {
    const isShow = (total > 0);
    return (
        isShow &&
        <View style={{
            backgroundColor: COLORS.bgE4EBD5,
            flexDirection: 'row',
            width: constants.width,
            justifyContent: 'space-between',
            paddingHorizontal: 10,
            height: 40,
            borderTopWidth: StyleSheet.hairlineWidth,
            borderTopColor: COLORS.bdFFFFFF
        }}>
            <View style={{
                flexDirection: 'row',
                alignSelf: 'flex-end',
                alignItems: 'center',
                height: 40,
            }}>
                <MyText
                    style={{
                        color: COLORS.txtFC3158,
                        fontWeight: 'bold',
                        textDecorationLine: 'underline'
                    }}
                    onPress={onShow}
                    text={translate('editSaleOrder.additional_fee_2')}
                />
            </View>

            <View style={{
                alignSelf: 'flex-end',
                justifyContent: 'center',
                height: 40,
            }}>
                <MyText
                    addSize={2}
                    style={{
                        color: COLORS.txtFF6600,
                        fontWeight: 'bold'
                    }}
                    text={helper.convertNum(total)}
                />
            </View>
        </View>
    );
}

const PointLoyalty = ({ total, onShow }) => {
    const isShow = (total > 0);
    return (
        isShow &&
        <View style={{
            backgroundColor: COLORS.bgFFFFFF,
            flexDirection: 'row',
            width: constants.width,
            justifyContent: 'space-between',
            paddingHorizontal: 10,
            height: 40,
        }}>
            <View style={{
                flexDirection: 'row',
                alignSelf: 'flex-end',
                alignItems: 'center',
                height: 40,
            }}>
                <MyText
                    style={{
                        color: COLORS.txt2164F4,
                        fontWeight: 'bold',
                        textDecorationLine: 'underline'
                    }}
                    onPress={onShow}
                    text={translate('editSaleOrder.provisional_total_membership_score')}
                />
            </View>

            <View style={{
                alignSelf: 'flex-end',
                justifyContent: 'center',
                height: 40,
            }}>
                <MyText
                    addSize={2}
                    style={{
                        color: COLORS.txtFF00BF,
                        fontWeight: 'bold'
                    }}
                    text={helper.convertNum(total, false)}
                />
            </View>
        </View>
    );
}

const RadioRelationShip = ({ type, onChangeType }) => {
    return (
        <View style={{
            width: constants.width,
            alignItems: "center",
        }}>
            <RadioRelation
                type={type}
                onChangeType={onChangeType}
            />
        </View>
    )
}

const convertString2Number = (stringValue) => {
    return stringValue ? parseFloat(stringValue) : 0
}

const CustomerCartInfo = ({ info, saleOrderID, onSetShowCustomerID, candidateNo, StaffUser, searchUserSheetRef, saleUserInfo, titleUser }) => {
    const {
        CustomerName,
        CustomerAddress,
        CustomerPhone,
        TaxID,
        Gender,
        ContactPhone,
        ContactName,
        ContactGender,
        DeliveryAddress,
    } = info;
    const dispatch = useDispatch();


    const [isShowCustomer, setIsShowCustomer] = useState(false)
    const isSeenLog = useRef(false)
    const isCompany = !!TaxID;
    const onShowCustomerInfo = () => {
        if (!isSeenLog.current && !isShowCustomer) {
            showBlockUI();
            dispatch(logSeenCustomerInfo(saleOrderID)).then(success => {
                isSeenLog.current = true;
                hideBlockUI();
                setIsShowCustomer(true)
                onSetShowCustomerID(true)
            }).catch(error => {
                Alert.alert("Không thể xem thông tin", error.msgError, [{
                    text: "OK",
                    onPress: hideBlockUI
                }]);
            });
        }
        else {
            setIsShowCustomer(!isShowCustomer)
            onSetShowCustomerID(!isShowCustomer)

        }
    }
    return (
        <View style={{
            width: constants.width,
            alignItems: "center",
        }}>
            {StaffUser && <View
                style={{
                    alignItems: 'center',
                    backgroundColor: COLORS.btn5482AB,
                    height: 40,
                    paddingHorizontal: 10,
                    marginBottom: 8
                }}>
                <Button
                    text={"NVBH: " + titleUser}
                    onPress={() => { searchUserSheetRef.current?.present() }}
                    styleContainer={{
                        flexDirection: 'row',
                        width: constants.width,
                        height: 40,
                        justifyContent: 'space-between',
                        width: constants.width - 10
                    }}
                    styleText={{
                        color: COLORS.txtFFFFFF,
                        fontSize: 14,
                        marginRight: 8,
                        fontWeight: 'bold'
                    }}
                    iconRight={{
                        iconSet: 'Feather',
                        name: "edit",
                        size: 20,
                        color: COLORS.icFFFFBC
                    }}
                />
            </View>}
            <View style={{
                justifyContent: 'center',
                backgroundColor: COLORS.bg6A9C84,
                width: constants.width,
                height: 40,
                paddingHorizontal: 10,
                marginBottom: 10
            }}>

                <Button
                    text={translate('shoppingCart.customer_info')}
                    onPress={onShowCustomerInfo}
                    styleContainer={{
                        flexDirection: 'row',
                        width: constants.width,
                        height: 40,
                        justifyContent: 'space-between',
                        width: constants.width - 10
                    }}
                    styleText={{
                        color: COLORS.txtFFFFFF,
                        fontSize: 16,
                        marginRight: 8,
                        fontWeight: 'bold'
                    }}
                    iconRight={{
                        iconSet: 'Ionicons',
                        name: isShowCustomer ? 'eye-outline' : 'eye-off-outline',
                        size: 20,
                        color: COLORS.icFFFFBC
                    }}
                />
            </View>
            {
                isShowCustomer ? (
                    isCompany
                        ? <View style={{
                            width: constants.width - 20,
                            marginTop: 10,
                        }}>
                            <TitleInput
                                title={translate('editSaleOrder.text_input_tax_id')}
                                isRequired={true}
                                styleInput={{
                                    borderWidth: 1,
                                    borderRadius: 4,
                                    borderColor: COLORS.bdCCCCCC,
                                    marginBottom: 5,
                                    paddingHorizontal: 10,
                                    paddingVertical: 8,
                                    backgroundColor: COLORS.bgFFFFFF,
                                }}
                                value={TaxID}
                                editable={false}
                                width={constants.width - 20}
                                height={40}
                            />

                            <TitleInput
                                title={translate('editSaleOrder.text_input_customer_company_name')}
                                isRequired={true}
                                styleInput={{
                                    borderWidth: 1,
                                    borderRadius: 4,
                                    borderColor: COLORS.bdCCCCCC,
                                    marginBottom: 5,
                                    paddingHorizontal: 10,
                                    justifyContent: 'center',
                                    paddingVertical: 8,
                                    backgroundColor: COLORS.bgFFFFFF,
                                }}
                                value={CustomerName}
                                editable={false}
                                key={"companyName"}
                                width={constants.width - 20}
                                height={40}
                            />

                            <TitleInput
                                title={translate('editSaleOrder.text_input_customer_company_address')}
                                isRequired={true}
                                styleInput={{
                                    borderWidth: 1,
                                    borderRadius: 4,
                                    borderColor: COLORS.bdCCCCCC,
                                    marginBottom: 5,
                                    paddingHorizontal: 10,
                                    justifyContent: 'center',
                                    paddingVertical: 8,
                                    backgroundColor: COLORS.bgFFFFFF,
                                }}
                                value={CustomerAddress}
                                editable={false}
                                key={"companyAddress"}
                                width={constants.width - 20}
                                height={40}
                            />

                            <TitleInput
                                title={translate('editSaleOrder.text_input_customer_company_phone')}
                                isRequired={true}
                                styleInput={{
                                    borderWidth: 1,
                                    borderRadius: 4,
                                    borderColor: COLORS.bdCCCCCC,
                                    marginBottom: 5,
                                    paddingHorizontal: 10,
                                    paddingVertical: 8,
                                    backgroundColor: COLORS.bgFFFFFF,
                                }}
                                value={CustomerPhone}
                                editable={false}
                                key={"companyPhone"}
                                width={constants.width - 20}
                                height={40}
                            />

                            <View style={{
                                width: constants.width - 20,
                                flexDirection: "row",
                                marginBottom: 4,
                                justifyContent: "space-between"
                            }}>
                                <RadioGender
                                    gender={ContactGender}
                                    onSwitchGender={() => { }}
                                />
                            </View>

                            <TitleInput
                                title={translate('editSaleOrder.text_input_contact_phone')}
                                styleInput={{
                                    borderWidth: 1,
                                    borderRadius: 4,
                                    borderColor: COLORS.bdCCCCCC,
                                    marginBottom: 5,
                                    paddingHorizontal: 10,
                                    backgroundColor: COLORS.bgFFFFFF,
                                    paddingVertical: 8,
                                }}
                                editable={false}
                                value={ContactPhone}
                                key={"contactPhone"}
                                width={constants.width - 20}
                                height={40}
                            />

                            <TitleInput
                                title={translate('editSaleOrder.text_input_contact_name')}
                                styleInput={{
                                    borderWidth: 1,
                                    borderRadius: 4,
                                    borderColor: COLORS.bdCCCCCC,
                                    marginBottom: 5,
                                    paddingHorizontal: 10,
                                    backgroundColor: COLORS.bgFFFFFF,
                                    paddingVertical: 8
                                }}
                                value={ContactName}
                                editable={false}
                                key={"contactName"}
                                width={constants.width - 20}
                                height={40}
                            />

                            <TitleInput
                                title={translate('editSaleOrder.text_input_contact_address')}
                                styleInput={{
                                    borderWidth: 1,
                                    borderRadius: 4,
                                    borderColor: COLORS.bdCCCCCC,
                                    marginBottom: 5,
                                    paddingHorizontal: 10,
                                    backgroundColor: COLORS.bgFFFFFF,
                                    justifyContent: 'center',
                                    paddingVertical: 8
                                }}
                                value={DeliveryAddress}
                                editable={false}
                                key={"contactAddress"}
                                width={constants.width - 20}
                                height={40}
                            />
                        </View>
                        : <View style={{
                            width: constants.width - 20,
                            marginTop: 10,
                        }}>
                            <View style={{
                                width: constants.width - 20,
                                flexDirection: "row",
                                marginBottom: 4,
                                justifyContent: "space-between"
                            }}>
                                <RadioGender
                                    gender={Gender}
                                    onSwitchGender={() => { }}
                                />
                            </View>

                            <TitleInput
                                title={translate('editSaleOrder.text_input_phone')}
                                isRequired={true}
                                styleInput={{
                                    borderWidth: 1,
                                    borderRadius: 4,
                                    borderColor: COLORS.bdCCCCCC,
                                    marginBottom: 5,
                                    paddingHorizontal: 10,
                                    paddingVertical: 8,
                                    backgroundColor: COLORS.bgFFFFFF,
                                }}
                                value={CustomerPhone}
                                editable={false}
                                key={"customerPhone"}
                                width={constants.width - 20}
                                height={40}
                            />

                            <TitleInput
                                title={translate('editSaleOrder.text_input_name_2')}
                                isRequired={true}
                                styleInput={{
                                    borderWidth: 1,
                                    borderRadius: 4,
                                    borderColor: COLORS.bdCCCCCC,
                                    marginBottom: 5,
                                    paddingHorizontal: 10,
                                    paddingVertical: 8,
                                    backgroundColor: COLORS.bgFFFFFF,
                                }}
                                value={CustomerName}
                                editable={false}
                                key={"customerName"}
                                width={constants.width - 20}
                                height={40}
                            />

                            <TitleInput
                                title={translate('editSaleOrder.text_input_address_2')}
                                styleInput={{
                                    borderWidth: 1,
                                    borderRadius: 4,
                                    borderColor: COLORS.bdCCCCCC,
                                    marginBottom: 5,
                                    paddingHorizontal: 10,
                                    justifyContent: 'center',
                                    paddingVertical: 8,
                                    backgroundColor: COLORS.bgFFFFFF,
                                }}
                                value={CustomerAddress}
                                editable={false}
                                key={"customerAddress"}
                                width={constants.width - 20}
                                height={40}
                            />
                            {helper.IsNonEmptyString(candidateNo) && <TitleInput
                                title={"Số báo danh:"}
                                styleInput={{
                                    borderWidth: 1,
                                    borderRadius: 4,
                                    borderColor: COLORS.bdCCCCCC,
                                    marginBottom: 5,
                                    paddingHorizontal: 10,
                                    justifyContent: 'center',
                                    paddingVertical: 8,
                                    backgroundColor: COLORS.bgFFFFFF,
                                }}
                                value={candidateNo}
                                editable={false}
                                key={"candidateNo"}
                                width={constants.width - 20}
                                height={40}
                            />}
                        </View>
                ) :
                    <TitleInput
                        title={isCompany ? translate('editSaleOrder.text_input_contact_name') : translate('editSaleOrder.text_input_name_2')}
                        styleInput={{
                            borderWidth: 1,
                            borderRadius: 4,
                            borderColor: COLORS.bdCCCCCC,
                            marginBottom: 5,
                            paddingHorizontal: 10,
                            backgroundColor: COLORS.bgFFFFFF,
                            paddingVertical: 8
                        }}
                        value={isCompany ? ContactName : CustomerName}
                        editable={false}
                        key={"contactName"}
                        width={constants.width - 20}
                        height={40}
                    />
            }
        </View>
    );
}

const InstallmentInfo = ({ installment, programInfo }) => {
    const isHasProgram = installment && !helper.IsEmptyObject(programInfo);
    return (
        <View style={{
            width: constants.width,
            alignItems: "center",
            backgroundColor: COLORS.bgFFFFFF
        }}>
            <View style={{
                justifyContent: 'center',
                backgroundColor: COLORS.bg6A9C84,
                width: constants.width,
                height: 40,
                paddingHorizontal: 10,
                borderTopWidth: StyleSheet.hairlineWidth,
                borderTopColor: COLORS.bdFFFFFF
            }}>
                <MyText
                    style={{
                        color: COLORS.txtFFFFFF,
                        fontWeight: 'bold'
                    }}
                    text={translate('editSaleOrder.payment_method')}
                />
            </View>
            <View style={{
                width: constants.width,
                alignItems: "center",
                paddingVertical: 8
            }}>
                <RadioInstallment
                    installment={installment}
                    onSwitch={(value) => { }}
                    disabled={true}
                />
                {
                    isHasProgram &&
                    <SaleProgram
                        info={programInfo}
                    />
                }
            </View>
        </View>
    );
}

const SaleProgram = ({ info }) => {
    const {
        SaleProgramID,
        SaleProgramName,
        Logo
    } = info;
    return (
        <View
            style={{
                width: constants.width - 40,
                paddingVertical: 10,
                paddingHorizontal: 20,
                borderWidth: 1,
                borderColor: COLORS.bdE4E4E4,
                borderRadius: 4,
            }}
        >
            <View style={{
                justifyContent: 'center',
                alignItems: 'center',
                padding: 5,
                backgroundColor: COLORS.bgFFFFFF,
                flexWrap: "wrap",
                width: 150,
                height: 50,
                borderRadius: 2,
                shadowColor: COLORS.sd000000,
                shadowOffset: {
                    width: 0,
                    height: 1,
                },
                shadowOpacity: 0.25,
                shadowRadius: 3.84,
                elevation: 2,
                borderColor: COLORS.bd2FB47C,
                borderWidth: 2,
                marginBottom: 8,
            }}>
                {/* <ImageURI
                    uri={API_LOGO_PARTNER_INSTALLMENT + Logo}
                    style={{
                        width: 100,
                        height: 40,
                    }}
                    resizeMode={"contain"}
                /> */}
                <Image
                    source={{ uri: API_LOGO_PARTNER_INSTALLMENT + Logo }}
                    style={{
                        width: 100,
                        height: 40,
                    }}
                    resizeMode={"contain"}
                />
            </View>
            <View style={{
                padding: 10,
                backgroundColor: COLORS.bgFFFFFF,
                borderRadius: 4,
                shadowColor: COLORS.sd000000,
                shadowOffset: {
                    width: 0,
                    height: 1,
                },
                shadowOpacity: 0.25,
                shadowRadius: 3.84,

                elevation: 2
            }}>
                <MyText
                    style={{
                        width: '100%',
                        color: COLORS.txt333333,
                        fontStyle: 'italic'
                    }}
                    text={`${SaleProgramID} - ${SaleProgramName}`}
                />
            </View>
        </View>
    );
}

const ContractInfo = ({ SHAmount, saleProgramInfo, isShowCustomerID }) => {
    const {
        customerIDCard,
        SaleProgramName,
        SaleProgramID,
        RecordsProcessingFee,
        TotalPrePaid,
        TermLoanList,
        TermLoan,
        PaymentAmountMonthly,
        InsuranceFees,
        MasterGoodsInsurance,
        IsSelectedLifeInsurance,
        IsSelectedGoodsInsurance,
        GoodsInsuranceID,
        PartnerInstallmentID
    } = saleProgramInfo;
    const percent = (parseFloat(TotalPrePaid) / SHAmount) * 100;
    const isNewFollowPartner = helper.isNewFollowPartner(PartnerInstallmentID)

    return (
        <View style={{
            width: constants.width,
            alignItems: "center",
            backgroundColor: COLORS.bgFDF9E5,
            paddingVertical: 8
        }}>
            {isShowCustomerID && <TitleInput
                title={translate('editSaleOrder.text_input_id_card_number')}
                styleInput={{
                    borderWidth: 1,
                    borderRadius: 4,
                    borderColor: COLORS.bdCCCCCC,
                    marginBottom: 5,
                    paddingHorizontal: 10,
                    paddingVertical: 8,
                    backgroundColor: COLORS.bgFFFFFF
                }}
                value={customerIDCard}
                editable={false}
                width={constants.width - 20}
                height={40}
                key={"customerIDCard"}
                placeholder={""}
            />}

            <MyText
                style={{
                    width: constants.width - 20,
                    marginBottom: 2,
                    fontWeight: 'bold',
                    color: COLORS.txt288AD6
                }}
                text={`${SaleProgramID} - ${SaleProgramName}`}
            />
            {
                !isNewFollowPartner && <>
                    <TitleInputMoney
                        title={translate('editSaleOrder.text_input_cost_contract')}
                        style={{
                            borderWidth: 1,
                            borderRadius: 4,
                            borderColor: COLORS.bdCCCCCC,
                            backgroundColor: COLORS.bgFFFFFF,
                            color: COLORS.txt333333,
                            height: 40,
                            marginBottom: 5,
                            paddingHorizontal: 10,
                            width: constants.width - 20,
                            backgroundColor: COLORS.bgFFFFFF,
                        }}
                        value={RecordsProcessingFee}
                        editable={false}
                        key={"processingFee"}
                        placeholder={"0"}
                    />
                    <TitleInputMoney
                        title={`${translate('editSaleOrder.lf_deposit')}`}
                        percent={percent}
                        style={{
                            borderWidth: 1,
                            borderRadius: 4,
                            borderColor: COLORS.bdCCCCCC,
                            backgroundColor: COLORS.bgFFFFFF,
                            color: COLORS.txt333333,
                            height: 40,
                            marginBottom: 5,
                            paddingHorizontal: 10,
                            width: constants.width - 20,
                            backgroundColor: COLORS.bgFFFFFF,
                        }}
                        value={TotalPrePaid}
                        editable={false}
                        key={"totalPrePaid"}
                        placeholder={"0"}
                    />
                    {
                        helper.isArray(TermLoanList) &&
                        <Picker
                            label={"TermLoanName"}
                            value={"TermLoanNumber"}
                            defaultLabel={"Kỳ hạn vay"}
                            valueSelected={TermLoan}
                            data={TermLoanList}
                            onChange={(item) => {
                            }}
                            title={translate('editSaleOrder.picker_loan_term')}
                            disabled={true}
                        />
                    }
                    <InsuranceInfo
                        dataFee={InsuranceFees}
                        dataMasterGood={MasterGoodsInsurance}
                        isCheckFee={IsSelectedLifeInsurance}
                        isCheckGood={IsSelectedGoodsInsurance}
                        goodId={GoodsInsuranceID}
                        onPressFee={() => {
                        }}
                        onPressGood={(goodID) => {
                        }}
                        onPressInsurance={(value) => {
                        }}
                    />
                    <TitleInputMoney
                        title={translate('editSaleOrder.text_input_payment_per_month')}
                        style={{
                            borderWidth: 1,
                            borderRadius: 4,
                            borderColor: COLORS.bdCCCCCC,
                            backgroundColor: COLORS.bgFFFFFF,
                            color: COLORS.txt333333,
                            height: 40,
                            marginBottom: 5,
                            paddingHorizontal: 10,
                            width: constants.width - 20,
                            backgroundColor: COLORS.bgFFFFFF,
                        }}
                        value={PaymentAmountMonthly}
                        editable={false}
                        key={"amountMonthly"}
                        placeholder={""}
                    /></>
            }

        </View>
    );
}


const getDiscountCode = (PRDLISTDISABLEDISCOUNTCODE, SaleOrderDetails, DiscountCode) => {
    let discountCode = DiscountCode
    if (!!DiscountCode) {
        for (let index = 0; index < SaleOrderDetails.length; index++) {
            const { ProductID } = SaleOrderDetails[index];
            if (`,${PRDLISTDISABLEDISCOUNTCODE},`.includes(`,${ProductID.toString()},`)) {
                discountCode = "*********"
                break
            }
        }
    }
    return discountCode
}
