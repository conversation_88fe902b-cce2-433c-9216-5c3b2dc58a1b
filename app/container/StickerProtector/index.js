import React, { useEffect, useRef, useState } from 'react';
import { View, Text, Animated, SafeAreaView, Dimensions } from 'react-native';
import { useDispatch, useSelector, batch } from 'react-redux';
import { constants } from '@constants';
import { translate } from '@translate';
import { COLORS } from '@styles';
import { MyText } from '@components';
import {
    IndicatorViewPager,
    PagerTitleIndicator
} from 'react-native-best-viewpager';
import ScreenSticker from './tabs/ScreenSticker';
import WarrantScreenSticker from './tabs/WarrantScreenSticker';
import { getSOInputImeiList } from './action';

const StickerProtector = () => {
    const { initScreen } = useSelector(state => state.saleOrderCartReducer);
    const viewPager = useRef();
    const PAGE_INDEX = {
        stickerList: 0,
        warranty: 1
    }
    const titles = [
        translate('screenSticker.paste_screen_sticker'),
        translate('screenSticker.warrant_screen_sticker')
    ];
    const dispatch = useDispatch();

    useEffect(() => {
        const withUser = true;
        batch(() => {
            dispatch(getSOInputImeiList());
            dispatch(getSOInputImeiList(withUser));
        });
    }, []);

    useEffect(() => {
        if (initScreen === "StickerProtector") {
            // TODO: Technical debt => setPage(PAGE_INDEX.warranty)
            viewPager.current.setPage(PAGE_INDEX.stickerList);
        }
    }, [initScreen]);

    return (
        <SafeAreaView
            style={{
                flex: 1
            }}>
            <IndicatorViewPager
                style={{ flex: 1, flexDirection: 'column-reverse' }}
                indicator={renderPagerTitleIndicator(titles)}
                ref={ref => { viewPager.current = ref }}
                keyboardShouldPersistTaps={'always'}>
                <View key="ScreenSticker" style={{
                    flex: 1
                }}>
                    <ScreenSticker />
                </View>
                <View key="WarrantStickerProtector" style={{
                    flex: 1
                }}>
                    <WarrantScreenSticker />
                </View>
            </IndicatorViewPager>
        </SafeAreaView>
    );
};

export const renderPagerTitleIndicator = (titles) => {
    const itemWidth = {
        width: Dimensions.get('window').width / titles.length - 30
    };
    return (
        <PagerTitleIndicator
            initialPage={0}
            style={{ backgroundColor: COLORS.bg00A98F }}
            titles={titles}
            trackScroll={true}
            selectedBorderStyle={{
                backgroundColor: COLORS.bgFFF000,
                height: 2,
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0
            }}
            renderTitle={(index, title, isSelected) => {
                return (
                    <View style={itemWidth}>
                        <MyText
                            style={{
                                color: isSelected
                                    ? COLORS.txtFFF000
                                    : COLORS.txtFFFFFF,
                                fontWeight: 'bold',
                                textAlign: 'center'
                            }}
                            text={title}
                        // addSize={isSelected ? 1 : 0}
                        />
                    </View>
                );
            }}
        />
    );
};

export default StickerProtector;
