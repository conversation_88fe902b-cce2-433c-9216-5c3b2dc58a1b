import React from 'react';
import {
    View,
    TextInput,
    Keyboard,
} from 'react-native';
import { Button, MyText } from "@components";
import { constants } from "@constants";
import { translate } from '@translate';
import { COLORS } from "@styles";

const OtpCode = ({
    expireTime,
    code,
    onChange,
    onCreate,
    onVerify
}) => {
    const isCounting = (expireTime > 0);
    const countValue = isCounting ? `(${expireTime})` : "";
    const createOTP = (type) => () => {
        onCreate(type);
    }
    return (
        <View style={{
            width: constants.width,
            alignItems: "center",
            paddingVertical: 16
        }}>
            <View style={{
                width: constants.width - 20,
                flexDirection: 'row',
                marginBottom: 15,
                justifyContent: 'space-around',
                opacity: isCounting ? 0.5 : 1
            }}>
                <Button
                    text={`${translate('editSaleOrder.btn_receive_OTP_call')} ${countValue}`}
                    styleContainer={{
                        width: 165,
                        height: 50,
                        backgroundColor: COLORS.btn288AD6,
                        borderRadius: 4,
                        opacity: 0
                    }}
                    styleText={{
                        color: COLORS.txtFFFFFF,
                        fontSize: 14
                    }}
                    onPress={createOTP("CALLCENTER")}
                    // disabled={isCounting}
                    disabled={true}
                />

                <Button
                    text={`${translate('editSaleOrder.btn_receive_OTP_message')} ${countValue}`}
                    styleContainer={{
                        width: 165,
                        height: 50,
                        backgroundColor: COLORS.btn288AD6,
                        borderRadius: 4,
                    }}
                    styleText={{
                        color: COLORS.txtFFFFFF,
                        fontSize: 14
                    }}
                    onPress={createOTP("SMS")}
                    disabled={isCounting}
                />
            </View>

            <View style={{
                width: constants.width - 20,
                flexDirection: "row",
                paddingHorizontal: 5,
                marginBottom: 8,
                opacity: isCounting ? 1 : 0.5
            }}>
                <TextInput
                    style={{
                        height: 40,
                        width: constants.width - 110,
                        borderWidth: 1,
                        borderRadius: 4,
                        borderColor: COLORS.bdCCCCCC,
                        paddingHorizontal: 10,
                        backgroundColor: COLORS.bgFFFFFF,
                        color: COLORS.txt333333
                    }}
                    placeholder={translate('editSaleOrder.placeholder_OTP')}
                    keyboardType="numeric"
                    returnKeyType={"done"}
                    editable={isCounting}
                    value={code}
                    onChangeText={onChange}
                    blurOnSubmit={true}
                    onSubmitEditing={Keyboard.dismiss}
                />
                <Button
                    text={translate('common.btn_confirm')}
                    styleContainer={{
                        width: 75,
                        height: 40,
                        backgroundColor: COLORS.btn288AD6,
                        borderRadius: 4,
                        marginLeft: 5,
                    }}
                    styleText={{
                        color: COLORS.txtFFFFFF,
                        fontSize: 14
                    }}
                    onPress={onVerify}
                    disabled={!isCounting}
                />
            </View>

            <MyText style={{
                color: COLORS.txt333333,
                fontStyle: "italic",
                textAlign: "center",
                width: constants.width - 20,
            }}
                text={translate('editSaleOrder.OTP_then_confirm')}
            />
        </View>
    );
}

export default OtpCode;