import { API_CONST } from "@constants";
import { helper, dateHelper } from "@common";
import { apiBase, METHOD, ERROR, EMPTY, SUCCESS } from "@config";
import { translate } from "@translate";

const {
    API_GET_SERVICE_LIST,
    API_VALIDATE_DATA_SERVICE_REQUEST,
    API_GET_CREATE_SERVICE_REQUEST,
    API_UPLOAD_AIRTIME_ATTACH_MENT,
    API_GET_PROCESS_SERVICE_REQUEST,
    API_GET_TRANSACTION_DETAIL,
    API_GET_PRODUCT_LIST_PACK_OF_DATA,
    API_GET_PROCESSOUT_VOUCHER,
    API_PROCESS_SERVICE_REQUEST,
    API_QUERY_SERVICE_REQUEST,
    API_GET_PRICE_AND_FEE_SERVICE,
    API_CREATE_TICKET_SERVICE,
    API_PREPARSE_CREATE_REQUEST,
    API_GET_PROVINCE,
    API_GET_DISTRICT,
    API_GET_WARD,
} = API_CONST;

const START_GET_SERVICE_LIST = "START_GET_SERVICE_LIST";
const STOP_GET_SERVICE_LIST = "STOP_GET_SERVICE_LIST";
const UPDATE_HEADER_AIRTIME = 'UPDATE_HEADER_AIRTIME';
const START_VALIDATE_DATA_SERVICE_REQUEST = 'START_VALIDATE_DATA_SERVICE_REQUEST';
const STOP_VALIDATE_DATA_SERVICE_REQUEST = 'STOP_VALIDATE_DATA_SERVICE_REQUEST';
const START_GET_DATA_CREATE_SERVICE_REQUEST = 'START_GET_DATA_CREATE_SERVICE_REQUEST';
const STOP_GET_DATA_CREATE_SERVICE_REQUEST = 'STOP_GET_DATA_CREATE_SERVICE_REQUEST';
const START_ADD_TO_SALE_ORDER_CART = "START_ADD_TO_SALE_ORDER_CART";
const STOP_ADD_TO_SALE_ORDER_CART = "STOP_ADD_TO_SALE_ORDER_CART";
const START_GET_SEND_OTP_PROCESS = "START_GET_SEND_OTP_PROCESS";
const STOP_GET_SEND_OTP_PROCESS = "STOP_GET_SEND_OTP_PROCESS";
const UPDATE_CUSTOMER_AIRTIME_SERCICE = 'UPDATE_CUSTOMER_AIRTIME_SERCICE';
const CLEAR_DATA_CUSTOMER = 'CLEAR_DATA_CUSTOMER';
const SET_QR_INFO = "SET_QR_INFO";
const START_QUERY_CUSTOMER_BANK_ACCOUNT = 'START_QUERY_CUSTOMER_BANK_ACCOUNT';
const STOP_QUERY_CUSTOMER_BANK_ACCOUNT = 'STOP_QUERY_CUSTOMER_BANK_ACCOUNT';
const START_GET_FEE_CASHIN = 'START_GET_FEE_CASHIN';
const STOP_GET_FEE_CASHIN = 'STOP_GET_FEE_CASHIN';
const START_GET_BANK_LIST = 'START_GET_BANK_LIST';
const STOP_GET_BANK_LIST = 'STOP_GET_BANK_LIST';
const STOP_INSERT_AND_CREATE_TICKET = 'STOP_INSERT_AND_CREATE_TICKET';
const UPDATE_DATA_TICKET = 'UPDATE_DATA_TICKET';
const START_PREPARE_DATA_BEFORE_CREATE_ORDER = 'START_PREPARE_DATA_BEFORE_CREATE_ORDER';
const STOP_PREPARE_DATA_BEFORE_CREATE_ORDER = 'STOP_PREPARE_DATA_BEFORE_CREATE_ORDER';
const CLEAR_DATA_PREAPRE_CREATE_ORDER = 'CLEAR_DATA_PREAPRE_CREATE_ORDER';
const START_GET_PROVINCE = "START_GET_PROVINCE";
const STOP_GET_PROVINCE = "STOP_GET_PROVINCE";
const START_GET_DISTRICT = "START_GET_DISTRICT";
const STOP_GET_DISTRICT = "STOP_GET_DISTRICT";
const CLEAR_DATA_FEE_CASHIN = "CLEAR_DATA_FEE_CASHIN";
const UPDATE_CUSTOMER_PHONE_NUMBER = "UPDATE_CUSTOMER_PHONE_NUMBER";
const START_GET_DATA_QUERY_STATUS = "START_GET_DATA_QUERY_STATUS";
const STOP_GET_DATA_QUERY_STATUS = "STOP_GET_DATA_QUERY_STATUS";

export const actionBankAirtimeService = {
    START_GET_SERVICE_LIST,
    STOP_GET_SERVICE_LIST,
    UPDATE_HEADER_AIRTIME,
    START_VALIDATE_DATA_SERVICE_REQUEST,
    STOP_VALIDATE_DATA_SERVICE_REQUEST,
    START_GET_DATA_CREATE_SERVICE_REQUEST,
    STOP_GET_DATA_CREATE_SERVICE_REQUEST,
    START_ADD_TO_SALE_ORDER_CART,
    STOP_ADD_TO_SALE_ORDER_CART,
    START_GET_SEND_OTP_PROCESS,
    STOP_GET_SEND_OTP_PROCESS,
    UPDATE_CUSTOMER_AIRTIME_SERCICE,
    CLEAR_DATA_CUSTOMER,
    SET_QR_INFO,
    START_QUERY_CUSTOMER_BANK_ACCOUNT,
    STOP_QUERY_CUSTOMER_BANK_ACCOUNT,
    START_GET_FEE_CASHIN,
    STOP_GET_FEE_CASHIN,
    START_GET_BANK_LIST,
    STOP_GET_BANK_LIST,
    STOP_INSERT_AND_CREATE_TICKET,
    UPDATE_DATA_TICKET,
    START_PREPARE_DATA_BEFORE_CREATE_ORDER,
    STOP_PREPARE_DATA_BEFORE_CREATE_ORDER,
    CLEAR_DATA_PREAPRE_CREATE_ORDER,
    START_GET_PROVINCE,
    STOP_GET_PROVINCE,
    START_GET_DISTRICT,
    STOP_GET_DISTRICT,
    CLEAR_DATA_FEE_CASHIN,
    UPDATE_CUSTOMER_PHONE_NUMBER,
    START_GET_DATA_QUERY_STATUS,
    STOP_GET_DATA_QUERY_STATUS
};


export const getServiceList = function (data) {
    return (dispatch, getState) => {
        const body = {
            loginStoreId: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            moduleID: getState().userReducer.moduleID,
            catalogID: data.catalogID,
            serviceGroupID: data.serviceGroupID,
        }
        if (data.catalogID == 3 && data.serviceGroupID == 30) {
            body.isLoadSearchBy = false;
            body.isSearchByEdit = false;
        }
        dispatch(start_get_service_list())
        apiBase(API_GET_SERVICE_LIST, METHOD.POST, body)
            .then((response) => {
                console.log("getServiceList success", response);
                if (helper.IsNonEmptyArray(response.object)) {
                    dispatch(stop_get_service_list(response.object, false, '', false));
                } else {
                    dispatch(stop_get_service_list([], true, 'Không lấy được dữ liệu', false));
                }
            }).catch(error => {
                dispatch(stop_get_service_list([], false, error.msgError, true))
                console.log("getServiceList error", error);
            })
    }
};

export const validateDataServiceRequest = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "catalogID": data.catalogID,
                "serviceGroupID": data.serviceGroupID,
                "airtimeTransactionTypeID": data.airtimeTransactionTypeID,
                "customerName": data.customerName,
                "customerPhone": data.customerPhone,
                "customerIDCard": data.customerIDCard,
                "phoneNumber": data.phoneNumber,
                "amount": data.amount,
                "ExtraData": data.ExtraData
            };
            dispatch(start_validate_data_service_request());
            apiBase(API_VALIDATE_DATA_SERVICE_REQUEST, METHOD.POST, body)
                .then((response) => {
                    console.log("validateDataServiceRequest success", response);
                    const { object } = response;
                    if (!helper.IsEmptyObject(object)) {
                        dispatch(stop_validate_data_service_request(object));
                        resolve(object);
                    } else {
                        dispatch(stop_validate_data_service_request({}));
                        resolve("Lỗi lấy thông tin dữ liệu");
                    }
                })
                .catch((error) => {
                    dispatch(stop_validate_data_service_request(!SUCCESS, {}));
                    const { msgError } = error;
                    console.log("validateDataServiceRequest error", error);
                    reject(msgError);
                });
        });
    };
};

export const getIssueplace = function (
    identificationType,
    nationalityID,
    cardID
) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                nationalityID: nationalityID,
                identificationType: identificationType,
                cardID: cardID
            };
            console.log(body);
            apiBase(API_CONST.API_GET_SIM_ISSUE_PLACE, METHOD.POST, body)
                .then((response) => {
                    if (
                        helper.hasProperty(response, 'object') &&
                        helper.isArray(response.object) &&
                        response.object.length > 0
                    ) {
                        resolve(response.object);
                    } else {
                        reject({
                            msgError: translate('activeSimManager.error_7')
                        });
                    }
                    console.log('response getIssueplace: ', response);
                })
                .catch((error) => {
                    reject(error);
                    console.log('error getIssueplace: ', error);
                });
        });
    };
};

export const getDataCreateServiceRequest = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "catalogID": data.catalogID,
                "serviceGroupID": data.serviceGroupID,
                "airtimeTransactionTypeID": data.airtimeTransactionTypeID,
                "airTimeTransactionBO": data.airTimeTransactionBO,
                "ExtraData": data.ExtraData,
                "inputPrice": data.inputPrice,
                "salePrice": data.salePrice,
                "partnerData": data.partnerData,
            };
            dispatch(start_get_data_create_service_request());
            apiBase(API_GET_CREATE_SERVICE_REQUEST, METHOD.POST, body)
                .then((response) => {
                    console.log("getDataCreateServiceRequest success", response);
                    const { object } = response;
                    if (!helper.IsEmptyObject(object)) {
                        dispatch(stop_get_data_create_service_request(object));
                        resolve(response);
                    } else {
                        dispatch(stop_get_data_create_service_request({}));
                        resolve("Lỗi lấy thông tin dữ liệu");
                    }
                })
                .catch((error) => {
                    dispatch(stop_get_data_create_service_request(!SUCCESS, {}));
                    const { msgError } = error;
                    console.log("getDataCreateServiceRequest error", error);
                    reject(msgError);
                });
        });
    };
};

export const addToSaleOrderCart = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "catalogID": data.catalogID,
                "serviceGroupID": data.serviceGroupID,
                "airtimeTransactionTypeID": data.airtimeTransactionTypeID,
                "airTimeTransactionBO": data.airTimeTransactionBO,
                "ExtraData": data.ExtraData,
                "ServiceVoucherID": data.ServiceVoucherID,
                "inputPrice": data.inputPrice,
                "salePrice": data.salePrice,
                "partnerData": data.partnerData,
                "creAirtimeReq": data.creAirtimeReq
            };
            dispatch(start_add_to_sale_order_cart());
            apiBase(API_GET_CREATE_SERVICE_REQUEST, METHOD.POST, body)
                .then((response) => {
                    console.log("addToSaleOrderCart success", response);
                    const { object } = response;
                    if (!helper.IsEmptyObject(object)) {
                        dispatch(stop_add_to_sale_order_cart(object));
                        resolve(response);
                    } else {
                        dispatch(stop_add_to_sale_order_cart({}));
                        reject({ msgError: translate("saleOrder.error_create_order") });
                    }
                })
                .catch((error) => {
                    dispatch(stop_add_to_sale_order_cart(!SUCCESS, {}));
                    console.log("addToSaleOrderCart error", error);
                    reject(error);
                });
        });
    };
};


export const getSendOTPProcessServicePrequest = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "catalogID": data.catalogID,
                "serviceGroupID": data.serviceGroupID,
                "serviceVoucherID": data.serviceVoucherID,
                "isRequestOTP": data.isRequestOTP
            };
            dispatch(start_get_send_otp_processs());
            apiBase(API_GET_PROCESS_SERVICE_REQUEST, METHOD.POST, body)
                .then((response) => {
                    console.log("getSendOTPProcessServicePrequest success", response);
                    const { object } = response;
                    if (!helper.IsEmptyObject(object)) {
                        dispatch(stop_get_send_otp_processs(object));
                        resolve(response);
                    } else {
                        dispatch(stop_get_send_otp_processs({}));
                        reject({ msgError: "Không tìm thấy thông tin gửi lại OTP" });
                    }
                })
                .catch((error) => {
                    dispatch(stop_get_send_otp_processs({}));
                    const { msgError } = error;
                    console.log("getSendOTPProcessServicePrequest error", error);
                    reject(msgError);
                });
        });
    };
};

export const getGetTransactionDetail = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "catalogID": data.catalogID,
                "serviceGroupID": data.serviceGroupID,
                "serviceVoucherID": data.serviceVoucherID,
            };
            dispatch(start_get_send_otp_processs());
            apiBase(API_GET_TRANSACTION_DETAIL, METHOD.POST, body)
                .then((response) => {
                    console.log("getGetTransactionDetail success", response);
                    const { object } = response;
                    if (!helper.IsEmptyObject(object)) {
                        dispatch(stop_get_send_otp_processs(object));
                        resolve(response);
                    } else {
                        dispatch(stop_get_send_otp_processs({}));
                        reject({ msgError: "Không tìm thấy thông tin gửi lại OTP" });
                    }
                })
                .catch((error) => {
                    dispatch(stop_get_send_otp_processs(!SUCCESS, {}));
                    const { msgError } = error;
                    console.log("getGetTransactionDetail error", error);
                    reject(msgError);
                });
        });
    };
};



export const getConfirmOTPProcessServicePrequest = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "catalogID": data.catalogID,
                "serviceGroupID": data.serviceGroupID,
                "serviceVoucherID": data.serviceVoucherID,
                "otp": data.otp
            };
            apiBase(API_GET_PROCESS_SERVICE_REQUEST, METHOD.POST, body)
                .then((response) => {
                    console.log("getPriceService success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("getPriceService error", error);
                    reject(error.msgError);
                });
        });
    };
};

export const getAmountLimitAirtimeService = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "catalogID": data.catalogID,
                "serviceGroupID": data.serviceGroupID,
                "airtimeTransactionTypeID": data.airtimeTransactionTypeID,
                "isLoadProductInfo": 0
            };
            console.log(body);
            apiBase(API_GET_PRODUCT_LIST_PACK_OF_DATA, METHOD.POST, body)
                .then((response) => {
                    console.log('getAmountLimitAirtimeService success', response);
                    if (!helper.IsEmptyObject(response.object)) {
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    reject(error);
                    console.log('getAmountLimitAirtimeService error', error);
                });
        });
    };
};

export const getProcessoutVoucher = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "ServiceVoucherID": data.ServiceVoucherID
            };
            apiBase(API_GET_PROCESSOUT_VOUCHER, METHOD.POST, body)
                .then((response) => {
                    console.log("getProcessoutVoucher success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("getProcessoutVoucher error", error);
                    reject(error);
                });
        });
    };
};

export const getDataQRPayment = function (catalogID, serviceGroupID, serviceVoucherID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "catalogID": catalogID,
                "serviceGroupID": serviceGroupID,
                "serviceVoucherID": serviceVoucherID
            };
            apiBase(API_PROCESS_SERVICE_REQUEST, METHOD.POST, body).then((response) => {
                console.log("getDataQRPayment success", response);
                const { object } = response;
                if (!helper.IsEmptyObject(object)) {
                    dispatch(set_qr_info(object));
                    resolve(response.object);
                }
                else {
                    reject(translate('saleOrderPayment.cannot_information_QR'));
                }
            }).catch(error => {
                console.log("getDataQRPayment error", error);
                reject(error.msgError);
            })
        })
    }
}

export const getQuerysTatusServiceRequest = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                airtimeTransactionID: data.airtimeTransactionID,
                airtimeTransactionRFID: data.airtimeTransactionRFID
            };
            dispatch(start_get_data_query_status())
            apiBase(API_QUERY_SERVICE_REQUEST, METHOD.POST, body)
                .then((response) => {
                    console.log("getQuerysTatusServiceRequest success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        dispatch(stop_get_data_query_status(response.object, false, '', false));
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("getQuerysTatusServiceRequest error", error);
                    dispatch(stop_get_data_query_status({}, false, error.msgError, true))
                    reject(error.msgError);
                });
        });
    };
};

export const getBankAccountList = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "catalogID": data.catalogID,
                "serviceGroupID": data.serviceGroupID,
            };
            dispatch(start_get_bank_list())
            apiBase(API_GET_SERVICE_LIST, METHOD.POST, body).then((response) => {
                console.log("getBankAccountList success", response);
                const lstBank = response?.object?.[0]?.ListBank;
                console.log(lstBank, '99999')
                if (helper.IsNonEmptyArray(lstBank)) {
                    dispatch(stop_get_bank_list(lstBank, false, '', false));
                    resolve(lstBank);
                } else {
                    reject("Không lấy được danh sách Ngân hàng")
                }
            }).catch(error => {
                console.log("getBankAccountList error", error);
                reject(error.msgError)
            })
        });
    }
}

export const queryCustomerBankAcc = function (data) {
    return (dispatch, getState) => {
        const body = {
            loginStoreId: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            moduleID: getState().userReducer.moduleID,
            "catalogID": data.catalogID,
            "serviceGroupID": data.serviceGroupID,
            "airtimeTransactionTypeID": data.airtimeTransactionTypeID,
            "extraData": data.extraData
        }
        dispatch(start_query_customer_banks_account())
        apiBase(API_VALIDATE_DATA_SERVICE_REQUEST, METHOD.POST, body)
            .then((response) => {
                console.log("queryCustomerBankAcc success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    dispatch(stop_query_customer_banks_account(response.object, false, '', false));
                } else {
                    dispatch(stop_query_customer_banks_account({}, true, 'Không lấy được dữ liệu', false));
                }
            }).catch(error => {
                dispatch(stop_query_customer_banks_account({}, false, error.msgError, true))
                console.log("queryCustomerBankAcc error", error);
            })
    }
}

export const getPriceAndFeeService = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "catalogID": data.catalogID,
                "serviceGroupID": data.serviceGroupID,
                "airtimeTransactionTypeID": data.airtimeTransactionTypeID,
                "productID": data.productID,
                "amount": data.amount,
                "productID": data.productID,
                "extraData": data.extraData
            };
            dispatch(start_get_fee_cashin())
            apiBase(API_GET_PRICE_AND_FEE_SERVICE, METHOD.POST, body)
                .then((response) => {
                    console.log("getPriceAndFeeService success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        dispatch(stop_get_fee_cashin(response.object, false, '', false));
                        resolve(response?.object);
                    }
                    else {
                        reject("Không lấy được dữ liệu");
                    }
                })
                .catch((error) => {
                    reject(error);
                    console.log('getPriceAndFeeService error', error);
                });
        });
    };
};

export const createTicletServiceRequest = function (AirtimeTransactionID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                airtimeTransactionID: AirtimeTransactionID,
                ticketType: 3
            };
            apiBase(API_CREATE_TICKET_SERVICE, METHOD.POST, body).then((response) => {
                console.log("createTiclet success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    dispatch(stop_insert_and_createticket(response.object, false, '', false));
                    resolve(response.object);
                }
                else {
                    resolve("Lỗi lấy thông tin dữ liệu");
                }
            }).catch(error => {
                console.log("createTiclet error", error);
                reject(error.msgError)
            })
        });
    }
}

export const prepareDataCreateOrder = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "serviceVoucherID": data.serviceVoucherID
            };
            dispatch(start_prepare_data_before_creae_order());
            apiBase(API_PREPARSE_CREATE_REQUEST, METHOD.POST, body)
                .then((response) => {
                    const { object } = response;
                    if (!helper.IsEmptyObject(object)) {
                        console.log("prepareDataCreateOrder success", response);
                        dispatch(stop_prepare_data_before_creae_order(object));
                        resolve(response);
                    } else {
                        dispatch(stop_prepare_data_before_creae_order({}));
                        reject({ msgError: 'Lỗi chuẩn bị dữ liệu trước khi tạo đơn' });
                    }
                })
                .catch((error) => {
                    dispatch(stop_prepare_data_before_creae_order(!SUCCESS, {}));
                    console.log("prepareDataCreateOrder error", error);
                    reject(error);
                });
        });
    };
};

export const getDataProvince = function () {
    return function (dispatch, getState) {
        let body = {
            "loginStoreId": getState().userReducer.storeID,
            "languageID": getState().userReducer.languageID,
            "moduleID": getState().userReducer.moduleID,
            "keyWord": "",
            "top": 64
        };
        dispatch(start_get_province());
        apiBase(API_GET_PROVINCE, METHOD.POST, body).then((response) => {
            console.log("getDataProvince success", response);
            const { object } = response;
            if (helper.isArray(object) && object.length > 0) {
                dispatch(stop_get_province(object));
            }
            else {
                dispatch(stop_get_province([], !EMPTY, translate("provincemain.mess_error"), ERROR));
            }
        }).catch(error => {
            console.log("getDataProvince error", error);
            dispatch(stop_get_province([], !EMPTY, error.msgError, ERROR));
        })
    }
}

export const getDataDistrict = function (provinceID) {
    return function (dispatch, getState) {
        let body = {
            "loginStoreId": getState().userReducer.storeID,
            "languageID": getState().userReducer.languageID,
            "moduleID": getState().userReducer.moduleID,
            "keyWord": "",
            "provinceID": provinceID,
            "top": 64
        };
        dispatch(start_get_district());
        apiBase(API_GET_DISTRICT, METHOD.POST, body).then((response) => {
            console.log("getDataDistrict success", response);
            const { object } = response;
            if (helper.isArray(object) && object.length > 0) {
                dispatch(stop_get_district(object));
            }
            else {
                dispatch(stop_get_district([], !EMPTY, translate("provincemain.mess_error1"), ERROR));
            }
        }).catch(error => {
            console.log("getDataDistrict error", error);
            dispatch(stop_get_district([], !EMPTY, error.msgError, ERROR));
        })
    }
}

export const getDistrict = function (provinceID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                "keyWord": "",
                "provinceID": provinceID,
                "top": 64
            };
            apiBase(API_GET_DISTRICT, METHOD.POST, body).then((response) => {
                console.log("getDistrict success", response);
                const { object } = response;
                if (helper.isArray(object) && object.length > 0) {
                    resolve(object)
                }
                else {
                    reject(translate("provincemain.mess_error1"));
                }
            }).catch(error => {
                console.log("getDistrict error", error);
                reject(error.msgError);
            })
        })
    };
}

export const getWard = function (provinceID, districtID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                "provinceID": provinceID,
                "districtID": districtID,
                "top": 64
            };
            apiBase(API_GET_WARD, METHOD.POST, body).then((response) => {
                console.log("getWard success", response);
                const { object } = response;
                if (helper.isArray(object) && object.length > 0) {
                    resolve(object)
                }
                else {
                    reject(translate("provincemain.mess_error2"));
                }
            }).catch(error => {
                console.log("getWard error", error);
                reject(error.msgError);
            })
        })
    };
}

export const getAllLocation = function (provinceID, districtID) {
    return new Promise((resolve, reject) => {
        console.log("getAllLocation");
        const allPromise = [
            getDistrict(provinceID),
            getWard(provinceID, districtID)
        ];
        Promise.all(allPromise).then(result => {
            console.log("getAllLocation success", result);
            resolve({
                dataDistrict: result[0],
                dataWard: result[1]
            })
        }).catch(msgError => {
            console.log("getAllLocation error", msgError);
            reject(msgError);
        })
    })
}

export const getDataBankQRCodeScan = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "catalogID": data.catalogID,
                "serviceGroupID": data.serviceGroupID,
                "airtimeTransactionTypeID": data.airtimeTransactionTypeID,
                "extraData": data.extraData,
            };
            dispatch(start_query_customer_banks_account())
            apiBase(API_VALIDATE_DATA_SERVICE_REQUEST, METHOD.POST, body)
                .then((response) => {
                    console.log("getDataBankQRCodeScan success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        dispatch(stop_query_customer_banks_account(response.object, false, '', false));
                    } else {
                        dispatch(stop_query_customer_banks_account({}, true, 'Không lấy được dữ liệu', false));
                    }
                }).catch(error => {
                    dispatch(stop_query_customer_banks_account({}, false, error.msgError, true))
                    console.log("getDataBankQRCodeScan error", error);
                })
        });
    }
}

const set_qr_info = (qrInfo) => {
    return ({
        type: SET_QR_INFO,
        qrInfo
    });
}
const update_customer_airtime_service = (
    data
) => ({
    type: UPDATE_CUSTOMER_AIRTIME_SERCICE,
    data
});

export const updateCustomerAirtimeService = (data) => {
    return function (dispatch, getState) {
        dispatch(update_customer_airtime_service(data));
    }
}

export const start_get_service_list = () => {
    return {
        type: START_GET_SERVICE_LIST,
    };
};

export const stop_get_service_list = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_SERVICE_LIST,
    data,
    isEmpty,
    description,
    isError
});

const update_header_airtime = (
    data
) => ({
    type: UPDATE_HEADER_AIRTIME,
    data
});

export const updateHeaderAirtime = (data) => {
    return function (dispatch, getState) {
        dispatch(update_header_airtime(data));
    }
}

const start_validate_data_service_request = () => ({
    type: START_VALIDATE_DATA_SERVICE_REQUEST
});

const stop_validate_data_service_request = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_VALIDATE_DATA_SERVICE_REQUEST,
    data,
    isEmpty,
    description,
    isError
});

const start_get_data_create_service_request = () => ({
    type: START_GET_DATA_CREATE_SERVICE_REQUEST
});

const stop_get_data_create_service_request = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_DATA_CREATE_SERVICE_REQUEST,
    data,
    isEmpty,
    description,
    isError
});

export const start_add_to_sale_order_cart = () => {
    return {
        type: START_ADD_TO_SALE_ORDER_CART,
    };
};

export const stop_add_to_sale_order_cart = (dataSaleOrderCart = {}) => {
    return {
        type: STOP_ADD_TO_SALE_ORDER_CART,
        dataSaleOrderCart,
    };
};

const start_get_send_otp_processs = () => ({
    type: START_GET_SEND_OTP_PROCESS
});
const stop_get_send_otp_processs = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_SEND_OTP_PROCESS,
    data,
    isEmpty,
    description,
    isError
});


export const clear_data_customer = () => ({
    type: CLEAR_DATA_CUSTOMER
});

export const updateItemCollectionManagermant = (data) => {
    return function (dispatch, getState) {
        dispatch(set_qr_info(data));
    }
}

const start_query_customer_banks_account = () => ({
    type: START_QUERY_CUSTOMER_BANK_ACCOUNT
});
const stop_query_customer_banks_account = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_QUERY_CUSTOMER_BANK_ACCOUNT,
    data,
    isEmpty,
    description,
    isError
});

const start_get_fee_cashin = () => ({
    type: START_GET_FEE_CASHIN
});
const stop_get_fee_cashin = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_FEE_CASHIN,
    data,
    isEmpty,
    description,
    isError
});

const start_get_bank_list = () => ({
    type: START_GET_BANK_LIST
});

const stop_get_bank_list = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_BANK_LIST,
    data,
    isEmpty,
    description,
    isError
});

const stop_insert_and_createticket = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_INSERT_AND_CREATE_TICKET,
    data,
    isEmpty,
    description,
    isError
});

const updateDataTicket = (
    data
) => ({
    type: UPDATE_DATA_TICKET,
    data
});

export const customDataTicket = (data) => {
    return function (dispatch, getState) {
        dispatch(updateDataTicket(data));
    }
}

export const start_prepare_data_before_creae_order = () => {
    return {
        type: START_PREPARE_DATA_BEFORE_CREATE_ORDER
    };
};

const stop_prepare_data_before_creae_order = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_PREPARE_DATA_BEFORE_CREATE_ORDER,
    data,
    data,
    isEmpty,
    description,
    isError
});

export const clear_data_prepare_create_order = () => ({
    type: CLEAR_DATA_PREAPRE_CREATE_ORDER
});

const start_get_province = () => {
    return ({
        type: START_GET_PROVINCE
    });
}

const stop_get_province = (
    dataProvince,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_PROVINCE,
        dataProvince,
        isEmpty,
        description,
        isError,
    });
}

const start_get_district = () => {
    return ({
        type: START_GET_DISTRICT
    });
}

const stop_get_district = (
    dataDistrict,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_DISTRICT,
        dataDistrict,
        isEmpty,
        description,
        isError,
    });
}

export const clear_data_Fee_Cashin = () => ({
    type: CLEAR_DATA_FEE_CASHIN
});
const update_customer_phone_number = (
    data
) => ({
    type: UPDATE_CUSTOMER_PHONE_NUMBER,
    data
});

export const updateCustomerPhone = (data) => {
    return function (dispatch, getState) {
        dispatch(update_customer_phone_number(data));
    }
}

const start_get_data_query_status = () => ({
    type: START_GET_DATA_QUERY_STATUS
});
const stop_get_data_query_status = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_DATA_QUERY_STATUS,
    data,
    isEmpty,
    description,
    isError
});

