import {
  Alert,
  Animated,
  TouchableOpacity,
  View,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
} from 'react-native';
import React, { useState } from 'react';
import KModal from 'react-native-modal';
import { Icon, MyText } from '@components';
import { constants } from '@constants';
import { COLORS } from '../../../../styles';
import * as actionBankAirtimeServiceCreator from '../../action';
import InputAmount from '../../component/Input/InputCheckLimit/InputAmount';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import LinearGradient from 'react-native-linear-gradient';
import { hideBlockUI, showBlockUI } from '@components';
import { helper } from "@common";

const LimitModal = ({
  isVisible,
  onClose,
  actionBankAirtimeService,
  dataUpdateCustomer,
  navigation,
  itemCatalog,
  updateHeaderAirtime
}) => {
  const [limitAmount, setLimitAmount] = useState('');
  const [isActiveAmount, setisActiveAmount] = useState(true)
  const animatedOpacity = new Animated.Value(0);
  Animated.timing(animatedOpacity, { toValue: 1, duration: 1000 }).start();
  const { AirtimeServiceGroupID } = itemCatalog ?? '';
  const min = updateHeaderAirtime?.MinAmount;
  const max = updateHeaderAirtime?.MaxAmount;

  const handleCheckBankAmount = () => {
    const { ServiceCategoryID, AirtimeServiceGroupID, ServiceGroupTransactionLimit } = itemCatalog ?? '';
    const { AirTimeTransactionTypeID } = updateHeaderAirtime ?? '';
    if (limitAmount === '') {
      Alert.alert('', 'Vui lòng nhập số tiền bạn muốn thực hiện giao dịch!');
      return false;
    }
    if (isActiveAmount) {
      Alert.alert("", `Vui lòng nhập số tiền trong khoảng ${helper.formatMoney(min)} -  ${helper.formatMoney(max)}`)
      return false;
    }
    else {
      showBlockUI()
      const data = {
        "catalogID": ServiceCategoryID,
        "serviceGroupID": AirtimeServiceGroupID,
        "airtimeTransactionTypeID": AirTimeTransactionTypeID,
        "amount": limitAmount,
        "ExtraData": {
          "ServiceGroupTransactionLimit": ServiceGroupTransactionLimit
        }
      };
      actionBankAirtimeService.validateDataServiceRequest(data).then((reponse) => {
        hideBlockUI();
        onClose();
        const { Amount } = reponse
        navigation?.navigate('StepOne');
        actionBankAirtimeService.updateCustomerAirtimeService({
          ...dataUpdateCustomer,
          depositAmount: Amount,
          rechargeFee: ""
        });
        actionBankAirtimeService.clear_data_Fee_Cashin();
      })
        .catch((msgError) => {
          Alert.alert("", msgError, [
            {
              text: "OK",
              onPress: hideBlockUI,
            },
          ]);
        });
    }
  };

  return (
    <KModal
      isVisible={isVisible}
      style={{
        margin: 0,
        justifyContent: 'center',
        alignItems: 'center',
      }}
      deviceWidth={constants.width}
      deviceHeight={constants.height}
      backdropTransitionOutTiming={0}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      useNativeDriver={true}
      hideModalContentWhileAnimating={true}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{
          width: '90%',
          borderRadius: 12,
          overflow: 'hidden',
          elevation: 5,
        }}
      >
        <View
          style={{
            borderRadius: 12,
            overflow: 'hidden',
            backgroundColor: COLORS.bgFFFFFF,
          }}
        >
          <LinearGradient
            colors={['#00AAFF', '#00BBFF']}
            style={{
              width: '100%',
              flexDirection: 'row',
              alignItems: 'center',
              paddingHorizontal: 10,
              paddingVertical: 15,
              justifyContent: 'space-between',
              borderTopLeftRadius: 12,
              borderTopRightRadius: 12,
            }}
          >
            <MyText text="KIỂM TRA HẠN MỨC" style={{
              color: COLORS.bgFFFFFF,
              fontWeight: 'bold',
              fontSize: 16,
              textAlign: 'center',
            }} />
            <TouchableOpacity onPress={onClose} style={{ padding: 5 }}>
              <Icon
                iconSet="Ionicons"
                name="close-circle-outline"
                color={COLORS.bgFFFFFF}
                size={25}
                style={{ alignSelf: 'flex-end' }}
              />
            </TouchableOpacity>
          </LinearGradient>
          <View
            style={{
              paddingVertical: 20,
              paddingHorizontal: 15,
              alignItems: 'center'
            }}
          >
            <InputAmount
              title={AirtimeServiceGroupID == 35 ? "Nhập số tiền khách muốn rút:" : "Nhập số tiền khách muốn nạp:"}
              value={limitAmount}
              onChange={(text) => {
                if (text >= min && text <= max) {
                  setisActiveAmount(false)
                } else {
                  setisActiveAmount(true)
                }
                setLimitAmount(text);
              }}
              disabled={false}
              isRequired={true}
              onCheckMount={() => {
                Keyboard.dismiss();
                handleCheckBankAmount();
              }}
            />
            <MyText
              text={`(Số tiền nạp nhập trong khoảng ${helper.formatMoney(min)} - ${helper.formatMoney(max)})`}
              style={{ color: COLORS.txtFF0000, fontSize: 11, fontStyle: 'italic', marginTop: 10 }}
            />
          </View>
        </View>
      </KeyboardAvoidingView>
    </KModal>
  );
};

const mapStateToProps = (state) => ({
  dataUpdateCustomer: state.bankAirtimeServiceReducer.dataUpdateCustomer,
  itemCatalog: state.collectionReducer.itemCatalog,
  updateHeaderAirtime: state.bankAirtimeServiceReducer.updateHeaderAirtime
});

const mapDispatchToProps = (dispatch) => ({
  actionBankAirtimeService: bindActionCreators(
    actionBankAirtimeServiceCreator,
    dispatch
  ),
});

export default connect(mapStateToProps, mapDispatchToProps)(LimitModal);
