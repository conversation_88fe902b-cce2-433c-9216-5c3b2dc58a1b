import { Alert, Keyboard, TouchableOpacity, View } from "react-native";
import React, { Component } from "react";
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import SafeAreaView from "react-native-safe-area-view";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { date<PERSON><PERSON><PERSON>, helper } from "@common";
import {
    MyText,
    TitleInput,
    showBlockUI,
    hideBlockUI
} from '@components';
import * as actionCollectionCreator from "../../../CollectionTransfer/action";
import { COLORS } from '@styles';
import { constants } from '@constants';
import { translate } from '@translate';
import ImageProcess from "../../component/SignImage/ImageProcess";
import SignImage from "../../component/SignImage/SignImage";
import { API_CONST } from '@constants';
import * as actionGetSimCreator from '../../../ActiveSimManager/action';
import RNFetchBlob from 'rn-fetch-blob';
import * as actionBankAirtimeServiceCreator from "../../action";
import CheckBox from "../../component/Checkbox/CheckBox";
import { BackHeader } from "@header";

class StepThree extends Component {
    constructor(props) {
        super(props);
        this.state = {
            customerPortrait: [],
            linkSignImage: "",
            isVisibleSign: false,
            signature: '',
            signBase64: '',
            isCheck: false,
            scrollEnabled: true,
            uriImages: []
        };
    }

    componentDidMount() {
        const {
            dataUpdateCustomer
        } = this.props;
        const {
            customerIDCard,
            customerName,
            rechargerPhoneNumber,
            receiptCode,
            depositAmount,
            signature,
            linkSignImage,
            isCheck
        } = dataUpdateCustomer;
        this.setState({
            customerIDCard: customerIDCard,
            receiptCode: receiptCode,
            customerName: customerName,
            rechargerPhoneNumber: rechargerPhoneNumber,
            depositAmount: depositAmount || '',
            signature: signature,
            linkSignImage: linkSignImage,
            isCheck: isCheck
        })
    }

    componentWillUnmount() {
    }


    closeSignature = () => {
        this.setState({ isVisibleSign: false });
    };

    takeSignature = (signature) => {
        showBlockUI()
        this.setState({
            isVisibleSign: false,
        });
        let timestamp = Date.now()
        const fs = RNFetchBlob.fs
        const filePathSignature = `${fs.dirs.DocumentDir}/signature/${timestamp}.png`
        fs.writeFile(filePathSignature, signature.split(",")[1], 'base64').then((response) => {
            const newURIImages = [...this.state.uriImages];
            const uri_path = Platform.OS == 'ios'
                ? filePathSignature
                : `file://${filePathSignature}`;
            const bodyFromData = new FormData();
            bodyFromData.append('file', {
                uri: uri_path,
                type: 'image/png',
                name: `takeSignature${dateHelper.getTimestamp()}`
            }); actionGetSimCreator.getImageCDN(bodyFromData)
                .then((res) => {
                    hideBlockUI()
                    const remoteSignatureURI =
                        API_CONST.API_GET_IMAGE_CDN + res[0];
                    this.setState({ linkSignImage: remoteSignatureURI })
                    this.setState({ signature: remoteSignatureURI });
                    this.props.actionBankAirtimeService.updateCustomerAirtimeService({
                        ...this.props.dataUpdateCustomer,
                        linkSignImage: remoteSignatureURI
                    })
                    console.log(remoteSignatureURI, "remoteSignatureURI")
                    hideBlockUI();
                })
                .catch((error) => {
                    Alert.alert(
                        translate('common.notification_uppercase'),
                        error.msgError,
                        [
                            {
                                text: translate('common.btn_skip'),
                                onPress: hideBlockUI,
                                style: 'cancel'
                            },
                            {
                                text: translate(
                                    'common.btn_notify_try_again'
                                ),
                                onPress: () => {
                                    this.takeSignature(signature);
                                },
                                style: 'default'
                            }
                        ],
                        { cancelable: false }
                    );
                });
        }).catch((error) => {
            hideBlockUI()
            console.log("🚀 ~ SimProcess ~ fs.createFile ~ error:", error)
        });

    };

    handleCreateOrder = () => {
        this.props.navigation.navigate("StepThree")
    }

    handleContinue = () => {
        const {
            itemCatalog,
            actionBankAirtimeService,
            updateHeaderAirtime,
            dataUpdateCustomer,
            dataFeeCashin
        } = this.props;
        const {
            ServiceCategoryID,
            AirtimeServiceGroupID,
            ServiceGroupTransactionLimit
        } = itemCatalog ?? {};
        const {
            AirTimeTransactionTypeID
        } = updateHeaderAirtime ?? {};
        const {
            depositAmount,
            customerName,
            receiptCode,
            rechargerPhoneNumber,
            cus_FilePathFrontOfIDCard,
            cus_FilePathBackOfIDCard,
            customerCardType,
            customerIDCard,
            customerPortrait,
            linkSignImage,
            idCardIssueDate,
            client_IDCardIssuePlaceID,
            streetAddress,
            wardName,
            districtName,
            provinceName,
        } = dataUpdateCustomer;
        const {
            isCheck,
        } = this.state;
        const portrait = customerPortrait?.[0]?.UrlFile;
        const promotion = dataFeeCashin?.PromotionInfo ?? '';
        const customerResidencePlace = `${streetAddress}, ${wardName}, ${districtName}, ${provinceName}`;
        if (!isCheck) {
            Alert.alert("", "Ban vui lòng đồng ý với chính sách dữ liệu cá nhân của TGDD")
            return false;
        }
        if (linkSignImage == '' || linkSignImage == undefined) {
            Alert.alert("", "Chữ ký khách hàng không được để trống")
            return false;
        } else {
            const data = {
                "catalogID": ServiceCategoryID,
                "serviceGroupID": AirtimeServiceGroupID,
                "airtimeTransactionTypeID": AirTimeTransactionTypeID,
                "airTimeTransactionBO": {
                    "productid": '4644263000011',
                    "amount": depositAmount,
                    "fee": 0,
                    "phoneNumber": receiptCode,
                    "inputPrice": depositAmount,
                    "salePrice": depositAmount,
                    "customerName": customerName,
                    "customerPhone": rechargerPhoneNumber,
                    "cus_AirTimeTransactionAttachBOLst": [
                        {
                            // "FilePath": "https://cdn.tgdd.vn/erp/MWGLogos/erablue_logo_20221012.jpg",
                            "FilePath": cus_FilePathFrontOfIDCard,
                            "cus_ShortName": "CMT"
                        },
                        {
                            // "FilePath": "https://cdn.tgdd.vn/erp/MWGLogos/erablue_logo_20221012.jpg",
                            "FilePath": cus_FilePathBackOfIDCard,
                            "cus_ShortName": "CMS"
                        },
                        {
                            // "FilePath": "https://cdn.tgdd.vn/erp/MWGLogos/erablue_logo_20221012.jpg",
                            "FilePath": portrait,
                            "cus_ShortName": "ACD"
                        },
                        {
                            // "FilePath": "https://cdn.tgdd.vn/erp/MWGLogos/erablue_logo_20221012.jpg",
                            "FilePath": linkSignImage,
                            "cus_ShortName": "SIGN"
                        }
                    ],
                    "PromotionPlanCustomer": promotion,
                    "ExtraData": {
                        "PromotionListGroupID": promotion?.PromotionListGroupID,
                    }
                },
                "ExtraData": {
                    "customerCardType": customerCardType,
                    "customerIDCard": customerIDCard,
                    "ServiceGroupTransactionLimit": ServiceGroupTransactionLimit,
                    "customerCardIssueDate": idCardIssueDate,
                    "customerCardIssuePlace": client_IDCardIssuePlaceID,
                    "customerCardResidencePlace": customerResidencePlace,
                },
            }
            showBlockUI();
            actionBankAirtimeService.getDataCreateServiceRequest(data).then((reponse) => {
                const {
                    cus_AirtimeTransactionSVMapBO
                } = reponse?.object ?? {};
                const {
                    ServiceVoucherID
                } = cus_AirtimeTransactionSVMapBO ?? '';
                this.createQR(ServiceCategoryID, AirtimeServiceGroupID, ServiceVoucherID)
            })
                .catch((msgError) => {
                    hideBlockUI();
                    Alert.alert("", msgError, [
                        {
                            text: "OK",
                            onPress: () => {
                                hideBlockUI();
                            },
                        },
                    ]);
                });

        }
    }

    createQR = (ServiceCategoryID, AirtimeServiceGroupID, ServiceVoucherID) => {
        this.props.actionBankAirtimeService.getDataQRPayment(ServiceCategoryID, AirtimeServiceGroupID, ServiceVoucherID).then((qrInfo) => {
            hideBlockUI();
            this.props.navigation.navigate("QrCodePay");
        }).catch(msgError => {
            Alert.alert("", msgError, [{
                text: "OK",
                onPress: hideBlockUI()
            }]);
        })
    }

    goBack = () => {
        const {
            dataUpdateCustomer,
            actionBankAirtimeService,
            navigation
        } = this.props;
        const { signature, linkSignImage, isCheck } = this.state;
        actionBankAirtimeService.updateCustomerAirtimeService({
            ...dataUpdateCustomer,
            signature,
            linkSignImage,
            isCheck
        })
        navigation.goBack();
    }

    getTitleAirtimeList = () => {
        const { AirTimeTransactionTypeName } = this.props.updateHeaderAirtime ?? "";
        const conertTransactionTypeName = AirTimeTransactionTypeName?.toUpperCase();
        return conertTransactionTypeName;
    };

    render() {
        const {
            linkSignImage,
            isCheck,
            customerIDCard,
            customerName,
            rechargerPhoneNumber,
            depositAmount,
        } = this.state;
        const {
            dataUpdateCustomer
        } = this.props;
        return (
            <View
                style={{
                    flex: 1,
                    backgroundColor: "white",
                }}
            >
                <BackHeader
                    onGoBack={this.goBack}
                    title={this.getTitleAirtimeList()}
                />
                <KeyboardAwareScrollView
                    style={{
                        flex: 1,
                    }}
                    enableResetScrollToCoords={false}
                    keyboardShouldPersistTaps="always"
                    bounces={false}
                    overScrollMode="always"
                    showsHorizontalScrollIndicator={false}
                    showsVerticalScrollIndicator={false}
                    extraScrollHeight={60}
                    scrollEnabled={this.state.scrollEnabled}
                >
                    <SafeAreaView
                        style={{
                            flex: 1,
                            marginTop: 10,
                            marginLeft: 10
                        }}
                    >
                        <View style={{
                        }}>
                            <TitleInput
                                title={"Số CMND/CCCD: "}
                                styleInput={{
                                    borderWidth: 1,
                                    borderRadius: 4,
                                    borderColor: COLORS.bdCCCCCC,
                                    marginBottom: 10,
                                    paddingHorizontal: 10,
                                    backgroundColor: COLORS.bgE0E0E0,
                                    paddingVertical: 8
                                }}
                                placeholder={translate("collection.placeholder_ID_card_number")}
                                value={customerIDCard}
                                onChangeText={(text) => {
                                    let validate = customerCardType == 1 ? new RegExp(/^\d{0,9}$/) : new RegExp(/^\d{0,12}$/);
                                    if (validate.test(text) || text == "") {
                                        this.setState({ customerIDCard: text })
                                    }
                                }}
                                keyboardType="numeric"
                                returnKeyType="done"
                                onSubmitEditing={Keyboard.dismiss}
                                width={constants.width - 20}
                                height={40}
                                clearText={() => {
                                    this.setState({ customerIDCard: '' });
                                }}
                                key="customerIDCard"
                                isRequired={true}
                                editable={false}
                            />

                            <TitleInput
                                title={'Họ và tên người nhận: '}
                                styleInput={{
                                    borderWidth: 1,
                                    borderRadius: 4,
                                    borderColor: COLORS.bdCCCCCC,
                                    marginBottom: 10,
                                    paddingHorizontal: 10,
                                    backgroundColor: COLORS.bgE0E0E0,
                                    paddingVertical: 8,
                                }}
                                placeholder={"Nhập họ và tên người nhận"}
                                value={customerName}
                                onChangeText={(text) => {
                                    if (helper.isValidateCharVN(text)) {
                                        this.setState({ customerName: text });
                                    }
                                }}
                                keyboardType={"default"}
                                returnKeyType={"done"}
                                blurOnSubmit
                                width={constants.width - 20}
                                height={40}
                                clearText={() => {
                                    this.setState({ customerName: '' });
                                }}
                                key="customerName"
                                isRequired={true}
                                editable={false}
                            />

                            <TitleInput
                                title={"Số điện thoại người nhận: "}
                                styleInput={{
                                    borderWidth: 1,
                                    borderRadius: 4,
                                    borderColor: COLORS.bdCCCCCC,
                                    marginBottom: 5,
                                    paddingHorizontal: 10,
                                    backgroundColor: COLORS.bgE0E0E0,
                                    paddingVertical: 8
                                }}
                                placeholder={"Nhập số điện thoại người nhận"}
                                value={rechargerPhoneNumber}
                                onChangeText={(text) => {
                                    const regExpPhone = new RegExp(/^[0]\d{0,9}$/);
                                    const isValidate = regExpPhone.test(text) || (text == "");
                                    if (isValidate) {
                                        this.setState({ rechargerPhoneNumber: text });
                                    }
                                }}
                                keyboardType="numeric"
                                returnKeyType="done"
                                blurOnSubmit
                                width={constants.width - 20}
                                height={40}
                                clearText={() => {
                                    this.setState({ rechargerPhoneNumber: '' });
                                }}
                                key="rechargerPhoneNumber"
                                isRequired={true}
                                editable={false}
                            />
                            <TitleInput
                                title={"Số tiền rút: "}
                                placeholder={translate("collection.enter_amount_want_to_deposit")}
                                styleInput={{
                                    borderWidth: 1,
                                    borderRadius: 4,
                                    borderColor: COLORS.bdCCCCCC,
                                    marginBottom: 5,
                                    paddingHorizontal: 10,
                                    backgroundColor: COLORS.bgE0E0E0,
                                    paddingVertical: 8
                                }}
                                value={helper.formatMoney(depositAmount)}
                                onChangeText={(text) => { }}
                                keyboardType="numeric"
                                returnKeyType="done"
                                blurOnSubmit
                                width={constants.width - 20}
                                height={40}
                                clearText={() => {
                                    this.setState({ rechargerPhoneNumber: '' });
                                }}
                                key="rechargerPhoneNumber"
                                isRequired={true}
                                editable={false}
                            />
                        </View>
                        <MyText
                            text={translate("collection.customer_signature")}
                            addSize={-1.5}
                            style={{
                                color: COLORS.txt333333,
                                fontWeight: "bold",
                                fontStyle: "italic",
                                marginBottom: 10
                            }}>
                            {
                                <MyText
                                    text={"*"}
                                    addSize={-1.5}
                                    style={{
                                        color: COLORS.txtFF0000
                                    }}
                                />
                            }
                        </MyText>
                        <CheckBox
                            onPress={() => this.setState({ isCheck: !isCheck })}
                            isCheck={isCheck}
                            title={"Khách hàng đồng ý với "}
                            link={`${"https://www.dienmayxanh.com/chinh-sach-xu-ly-du-lieu-ca-nhan"}`}
                            nameLink={"chính sách dữ liệu cá nhân "}
                            titleLast={"của TGDD"}
                        />
                        <View style={{
                            width: '100%',
                            height: linkSignImage ? 200 : 400,
                            justifyContent: 'center'
                        }}>
                            {
                                linkSignImage ?
                                    <View
                                        style={{
                                            width: '100%',
                                            height: 220,
                                            justifyContent: 'center',
                                            alignItems: 'center'
                                        }}
                                    >
                                        <View style={{
                                            flex: 1,
                                            borderWidth: 1,
                                            margin: 10,
                                            width: 350,
                                            height: 230,
                                            borderColor: COLORS.bg19A796,
                                            paddingBottom: 15,
                                            paddingHorizontal: 10,
                                            borderRadius: 10,
                                            justifyContent: "center",
                                            alignItems: "center",
                                            marginTop: 10
                                        }}>
                                            <ImageProcess
                                                onCamera={() => {
                                                    this.setState({
                                                        isVisibleSign: true
                                                    });
                                                }}
                                                urlImageLocal={this.state.signature}
                                                urlImageRemote={this.state.signature}
                                                deleteImage={() => {
                                                    this.setState({ signature: '' })
                                                    this.setState({ linkSignImage: '' })
                                                    this.props.actionBankAirtimeService.updateCustomerAirtimeService({
                                                        ...dataUpdateCustomer,
                                                        linkSignImage: ''
                                                    })
                                                }}
                                                isSignature={true}
                                            />
                                        </View>
                                    </View>
                                    :
                                    <SignImage
                                        onBegin={() => {
                                            this.setState({
                                                scrollEnabled: false
                                            })
                                        }}
                                        onEnd={() => {
                                            this.setState({
                                                scrollEnabled: true
                                            })
                                        }}
                                        isVisibleSign={() => this.state.isVisibleSign}
                                        takeSignature={this.takeSignature}
                                        closeSignature={() => { this.setState({ isVisibleSign: false }) }}
                                    />
                            }
                        </View>
                        <View
                            style={{
                                width: constants.width - 25,
                                height: 50,
                                flexDirection: "row",
                                marginTop: 20,
                            }}
                        >
                            <TouchableOpacity
                                onPress={this.goBack}
                                style={{
                                    width: 120,
                                    height: 50,
                                    borderRadius: 18,
                                    borderWidth: 2,
                                    borderColor: COLORS.bg00A98F,
                                    alignItems: "center",
                                    justifyContent: "center",
                                    backgroundColor: "white",
                                }}
                            >
                                <MyText
                                    text={"QUAY LẠI"}
                                    style={{
                                        fontWeight: "bold",
                                        color: COLORS.bg00A98F,
                                    }}
                                />
                            </TouchableOpacity>
                            <View
                                style={{
                                    width: 10,
                                }}
                            />
                            <TouchableOpacity
                                onPress={() => this.handleContinue()}
                                style={{
                                    backgroundColor: "pink",
                                    flex: 1,
                                    height: 50,
                                    borderRadius: 18,
                                    alignItems: "center",
                                    justifyContent: "center",
                                    backgroundColor: COLORS.bgF49B0C,
                                }}
                            >
                                <MyText
                                    text={"TẠO GIAO DỊCH"}
                                    style={{
                                        fontWeight: "bold",
                                        color: COLORS.bgFFFFFF,
                                    }}
                                />
                            </TouchableOpacity>
                        </View>
                    </SafeAreaView>
                </KeyboardAwareScrollView>
            </View>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        itemCatalog: state.collectionReducer.itemCatalog,
        dataValidateServiceRequest: state.bankAirtimeServiceReducer.dataValidateServiceRequest,
        dataCreateServiceRequest: state.bankAirtimeServiceReducer.dataCreateServiceRequest,
        dataUpdateCustomer: state.bankAirtimeServiceReducer.dataUpdateCustomer,
        updateHeaderAirtime: state.bankAirtimeServiceReducer.updateHeaderAirtime,
        dataFeeCashin: state.bankAirtimeServiceReducer.dataFeeCashin,
    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        actionCollection: bindActionCreators(actionCollectionCreator, dispatch),
        actionGetSim: bindActionCreators(actionGetSimCreator, dispatch),
        actionBankAirtimeService: bindActionCreators(actionBankAirtimeServiceCreator, dispatch),
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(StepThree);
