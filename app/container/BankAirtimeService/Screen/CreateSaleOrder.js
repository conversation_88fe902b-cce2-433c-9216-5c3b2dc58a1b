import {
    <PERSON>,
    <PERSON><PERSON>,
    BackHandler
} from 'react-native';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import SafeAreaView from 'react-native-safe-area-view';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { constants, } from '@constants';
import { COLORS } from '@styles';
import {
    MyText,
    showBlockUI,
    hideBlockUI,
    Button,
    Icon,
} from '@components';
import * as actionCollectionCreator from "../../CollectionTransfer/action";
import * as actionPaymentOrderCreator from "../../SaleOrderPayment/action";
import ButtonCollection from '../component/Button/ButtonCollection';
import * as actionBankAirtimeServiceCreator from "../action";
import { helper } from '@common';
import { translate } from '@translate';
import { BackHeader } from '@header';


class CreateSaleOrder extends Component {
    constructor(props) {
        super(props);
        this.state = {
            replyTicket: {},
            checkTicket: false,
            ticketId: "",
            Airtime: "",
            statusTicket: {},
            isReplyTicketDisabled: true,
            isCheckResultDisabled: false,

        };
    }

    componentDidMount(checkTicket, dataTicket) {
        this.handleCreateSticket();
        BackHandler.addEventListener('addEventListener', this.onBackButtonPressed);
    }

    onBackButtonPressed = () => {
        return true;
    }

    componentWillUnmount() {
        // this.backHandler.remove();
        BackHandler.removeEventListener('hardwareBackPress', this.onBackButtonPressed);
    }

    componentDidUpdate(prevProps) {

    }

    handleCreateSticket = () => {
        const {
            actionBankAirtimeService,
            dataCreateServiceRequest
        } = this.props;
        const { cus_AirtimeTransactionSVMapBO } = dataCreateServiceRequest ?? {};
        const { AirtimeTransactionID } = cus_AirtimeTransactionSVMapBO ?? {}
        actionBankAirtimeService.createTicletServiceRequest(AirtimeTransactionID).then((reponseDataTicket) => {
            hideBlockUI();
            this.setState({
                isReplyTicketDisabled: true,
                isCheckResultDisabled: false,
            });
        }).catch(msgError => {
            this.setState({
                isReplyTicketDisabled: false,
                isCheckResultDisabled: true,
            });
            Alert.alert("", msgError, [
                {
                    text: "OK",
                    onPress: hideBlockUI,
                },
            ])
        });
    }

    handleQueryStatus = () => {
        showBlockUI();
        const {
            actionCollection,
            dataInserAndCreateTicket: {
                TICKETID,
                AIRTIMETRANSACTIONID
            }
        } = this.props;
        const data = {
            TICKETID: TICKETID,
            AIRTIMETRANSACTIONID: AIRTIMETRANSACTIONID,
            TicketType: 3
        }
        actionCollection.checksSatusTicketService(data).then((reponseStatus) => {
            hideBlockUI();
            this.setState({ statusTicket: reponseStatus });
        }).catch(msgError => {
            Alert.alert("", msgError, [
                {
                    text: translate("collection.btn_accept"),
                    onPress: hideBlockUI,
                },
                {

                    text: translate("collection.retry"),
                    onPress: () => { this.handleQueryStatus() }
                }
            ])
        });
    }

    handleAddToSaleOrderCart = () => {
        const {
            itemCatalog,
            actionBankAirtimeService,
            updateHeaderAirtime,
            dataUpdateCustomer,
            navigation,
            dataCreateServiceRequest,
            dataFeeCashin,
            dataPrepareCreateOrder
        } = this.props;
        const {
            ServiceCategoryID,
            AirtimeServiceGroupID,
            ServiceGroupTransactionLimit
        } = itemCatalog ?? {};
        const {
            AirTimeTransactionTypeID
        } = updateHeaderAirtime ?? {};
        const {
            cus_AirtimeTransactionSVMapBO
        } = dataCreateServiceRequest ?? {};
        const {
            creAirtimeReq
        } = dataPrepareCreateOrder ?? {}
        const AirtimeTransactionID = cus_AirtimeTransactionSVMapBO?.AirtimeTransactionID;
        const ServiceVoucherID = cus_AirtimeTransactionSVMapBO?.ServiceVoucherID;
        const {
            depositAmount,
            customerName,
            rechargerPhoneNumber,
            customerCardType,
            customerIDCard,
            rechargeFee,
            bankCode,
            numberBankAccount,
            bankData,
            recipientName,
            idCardIssueDate,
            client_IDCardIssuePlaceID,
            streetAddress,
            wardName,
            districtName,
            provinceName
        } = dataUpdateCustomer;
        const reponsTran = dataFeeCashin?.PriceInfo?.ExtraData?.refNo ?? '';
        const inputPrice = dataFeeCashin?.PriceInfo?.InputPrice ?? '';
        const salePrice = dataFeeCashin?.PriceInfo?.SalePrice ?? '';
        const partnerData = dataFeeCashin?.PriceInfo?.PartnerData ?? '';
        const promotion = dataFeeCashin?.PromotionInfo ?? '';
        const customerResidencePlace = `${streetAddress}, ${wardName}, ${districtName}, ${provinceName}`;
        const data = {
            "catalogID": ServiceCategoryID,
            "serviceGroupID": AirtimeServiceGroupID,
            "airtimeTransactionTypeID": AirTimeTransactionTypeID,
            "airTimeTransactionBO": {
                "productid": '*************',
                "amount": depositAmount,
                "fee": rechargeFee,
                "phoneNumber": numberBankAccount,
                "inputPrice": inputPrice,
                "salePrice": salePrice,
                "customerName": customerName,
                "customerPhone": rechargerPhoneNumber,
                "airtimeTransactionID": AirtimeTransactionID,
                "responseTransactionID": reponsTran,
                "PromotionPlanCustomer": promotion,
                "ExtraData": {
                    "PromotionListGroupID": promotion?.PromotionListGroupID,
                }
            },
            "ServiceVoucherID": ServiceVoucherID,
            "ExtraData": {
                "customerCardType": customerCardType,
                "customerIDCard": customerIDCard,
                "bankCode": bankCode,
                "ServiceGroupTransactionLimit": ServiceGroupTransactionLimit,
                "bankData": bankData,
                "accountName": recipientName,
                "customerCardIssueDate": idCardIssueDate,
                "customerCardIssuePlace": client_IDCardIssuePlaceID,
                "customerCardResidencePlace": customerResidencePlace,
            },
            "inputPrice": inputPrice,
            "salePrice": salePrice,
            "partnerData": partnerData,
            "creAirtimeReq": creAirtimeReq,

        }
        showBlockUI();
        actionBankAirtimeService.addToSaleOrderCart(data).then((reponse) => {
            hideBlockUI();
            this.goToPaymentSO(reponse);
        })
            .catch((msgError) => {
                Alert.alert("", msgError?.msgError, [
                    {
                        text: "OK",
                        onPress: () => {
                            hideBlockUI();
                        },
                    },
                ]);
            });

    }

    goToPaymentSO = () => {
        const {
            dataSaleOrderCart
        } = this.props;
        const {
            actionPaymentOrder,
            navigation,
            actionBankAirtimeService
        } = this.props;
        const SaleOrders = dataSaleOrderCart.SaleOrders[0];
        const {
            SaleOrderID
        } = SaleOrders;
        actionPaymentOrder.setDataSO({
            SaleOrderID: SaleOrderID,
            SaleOrderTypeID: 1000
        }).then(success => {
            hideBlockUI();
            navigation.navigate('SaleOrderPayment');
            actionPaymentOrder.getSaleOrderPayment(SaleOrderID);
            actionPaymentOrder.getReportPrinterSocket(1000);
            actionPaymentOrder.getDataQRTransaction(SaleOrderID);
            actionPaymentOrder.getDataSCTransaction(SaleOrderID);
            actionBankAirtimeService.clear_data_customer();
        })
    }

    getTitleAirtimeList = () => {
        const { AirTimeTransactionTypeName } = this.props.updateHeaderAirtime ?? "";
        const conertTransactionTypeName = AirTimeTransactionTypeName?.toUpperCase();
        return conertTransactionTypeName;
    };

    goBack = () => {
        const {
            actionBankAirtimeService,
            dataCreateServiceRequest,
            navigation
        } = this.props;
        const { cus_AirtimeTransactionSVMapBO } = dataCreateServiceRequest ?? {};
        const { ServiceVoucherID } = cus_AirtimeTransactionSVMapBO ?? {}
        actionBankAirtimeService.clear_data_Fee_Cashin();
        navigation.navigate("HistorySell", {
            "SaleOrderID": ServiceVoucherID
        });
    }

    render() {
        const {
            dataInserAndCreateTicket: {
                AIRTIMETRANSACTIONID,
                LISTUSERAPPROVE,
                OUTPUTRECEIPTID
            },
            dataCreateServiceRequest
        } = this.props;
        const {
            cus_AirtimeTransactionSVMapBO
        } = dataCreateServiceRequest ?? {};
        const {
            ServiceVoucherID
        } = cus_AirtimeTransactionSVMapBO ?? ''
        const {
            replyTicket,
            statusTicket,
            isReplyTicketDisabled,
            isCheckResultDisabled
        } = this.state;
        const getTicketStatus = statusTicket?.STATUSID;
        const getStatusMess = statusTicket?.STATUSMESS;
        return (
            <View style={{
                flex: 1,
                backgroundColor: 'white'
            }}>
                <BackHeader
                    onGoBack={this.goBack}
                    title={this.getTitleAirtimeList()}
                />
                <KeyboardAwareScrollView
                    style={{
                        flex: 1,
                    }}
                    enableResetScrollToCoords={false}
                    keyboardShouldPersistTaps="always"
                    bounces={false}
                    overScrollMode="always"
                    showsHorizontalScrollIndicator={false}
                    showsVerticalScrollIndicator={false}
                    extraScrollHeight={60}
                >
                    <SafeAreaView
                        style={{
                            flex: 1,
                            alignItems: 'center'
                        }}>
                        <View style={{
                            padding: 10,
                            borderColor: COLORS.bgE0E0E0,
                        }}>
                            <View style={{
                                backgroundColor: COLORS.bgFFFFFF,
                                borderRadius: 7,
                                padding: 10,
                                shadowColor: COLORS.bg7F7F7F,
                                shadowOffset: {
                                    width: 0,
                                    height: 0,
                                },
                                shadowOpacity: 0.5,
                                shadowRadius: 1,
                                elevation: 5,
                            }}>
                                <MyText
                                    text={translate("collection.recharge_request")}
                                    addSize={-1.5}
                                    style={{
                                        color: COLORS.txt333333,
                                        marginBottom: 10,
                                        fontSize: 15,
                                        marginTop: 10,
                                        marginLeft: 5
                                    }} >
                                    {
                                        <MyText
                                            text={`[${ServiceVoucherID}]`}
                                            addSize={-1.5}
                                            style={{
                                                color: COLORS.txtD0021B,
                                                fontSize: 15
                                            }}
                                        >
                                            {
                                                <MyText
                                                    text={`${translate("collection.send_to_App_XWork")} ${LISTUSERAPPROVE} ${translate("collection.wait_for_management_to_approve")}`}
                                                    addSize={-1.5}
                                                    style={{
                                                        color: COLORS.txt333333,
                                                        fontSize: 15
                                                    }}
                                                />
                                            }
                                        </MyText>
                                    }
                                </MyText>
                                <View style={{
                                    flexDirection: 'row'
                                }}>
                                    <Icon
                                        iconSet={'MaterialIcons'}
                                        name={'info-outline'}
                                        color={COLORS.bgFF0000}
                                        size={18}
                                    />
                                    <MyText
                                        text={translate("collection.ticket_is_valid_for_10_minutes")}
                                        addSize={-1.5}
                                        style={{
                                            color: COLORS.bgFF0000,
                                            fontSize: 15,
                                            marginLeft: 5,
                                            fontStyle: 'italic'
                                        }}
                                    />
                                </View>
                            </View>
                            <View style={{
                                flexDirection: 'row',
                                backgroundColor: COLORS.bgFFFFFF,
                                borderRadius: 7,
                                padding: 10,
                                alignItems: 'center',
                                shadowColor: COLORS.bg7F7F7F,
                                shadowOffset: {
                                    width: 0,
                                    height: 0,
                                },
                                shadowOpacity: 0.5,
                                shadowRadius: 1,
                                elevation: 5,
                                marginTop: 10
                            }}>
                                <MyText
                                    text={translate("collection.ticket_status")}
                                    addSize={-1.5}
                                    style={{
                                        color: COLORS.txt333333,
                                        marginBottom: 10,
                                        fontSize: 15,
                                        marginTop: 10,
                                        marginLeft: 5,
                                        flex: 1,
                                        fontWeight: 'bold',
                                        width: 40
                                    }} />
                                <MyText
                                    text={getTicketStatus != null ? getStatusMess : translate("collection.sent_to_supermarket_manager")}
                                    addSize={-1.5}
                                    style={{
                                        color: (getTicketStatus != "APPROVE" && getTicketStatus != null) ? COLORS.bgFF0000 : COLORS.bg00AAFF,
                                        fontSize: 15,
                                        fontWeight: 'bold',
                                        width: 200
                                    }}
                                />
                            </View>
                            {

                                getTicketStatus == "APPROVE" ?
                                    <View
                                        style={{
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            marginBottom: 10,
                                            marginTop: 20,
                                            marginRight: 10
                                        }}>
                                        <Button
                                            onPress={() => this.handleAddToSaleOrderCart()}
                                            text={translate("collection.create_order")}
                                            disabled={false}
                                            styleContainer={{
                                                borderRadius: 7,
                                                backgroundColor: COLORS.txtFF8900,
                                                marginLeft: 10,
                                                height: 40,
                                                width: constants.getSize(130),
                                                opacity: getTicketStatus == "APPROVE" ? 1 : 0.5
                                            }}
                                            styleText={{
                                                color: COLORS.txtFFFFFF,
                                                fontSize: 14,
                                                fontWeight: 'bold'
                                            }}
                                        />
                                    </View>
                                    :
                                    <View style={{
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        flexDirection: 'row',
                                        marginTop: 20
                                    }}>
                                        <ButtonCollection
                                            onPress={() => this.handleCreateSticket()}
                                            disabled={isReplyTicketDisabled ? true : false}
                                            title={translate("collection.reply_ticket")}
                                            iconSet={"Ionicons"}
                                            nameIcon={"reload"}
                                            style={{
                                                backgroundColor: COLORS.bg00A98F
                                            }}
                                            opacity={isReplyTicketDisabled ? 0.5 : 1}
                                        />
                                        <View style={{ flex: 1 }} />
                                        <ButtonCollection
                                            onPress={() => this.handleQueryStatus()}
                                            disabled={isCheckResultDisabled}
                                            title={translate("collection.check_the_result")}
                                            iconSet={"Ionicons"}
                                            nameIcon={"search"}
                                            style={{
                                                backgroundColor: COLORS.bg1E88E5,
                                            }}
                                            opacity={isCheckResultDisabled ? 0.5 : 1}
                                        />
                                    </View>

                            }
                        </View>
                    </SafeAreaView>

                </KeyboardAwareScrollView>
            </View>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        dataInserAndCreateTicket: state.bankAirtimeServiceReducer.dataInserAndCreateTicket,
        saleOrderCart: state.collectionReducer.saleOrderCart,
        dataPayBill: state.collectionReducer.dataPayBill,
        dataSaleOrderCart: state.collectionReducer.dataSaleOrderCart,
        userInfo: state.userReducer,
        dataCreateServiceRequest: state.bankAirtimeServiceReducer.dataCreateServiceRequest,
        itemCatalog: state.collectionReducer.itemCatalog,
        dataUpdateCustomer: state.bankAirtimeServiceReducer.dataUpdateCustomer,
        updateHeaderAirtime: state.bankAirtimeServiceReducer.updateHeaderAirtime,
        dataFeeCashin: state.bankAirtimeServiceReducer.dataFeeCashin,
        dataPrepareCreateOrder: state.bankAirtimeServiceReducer.dataPrepareCreateOrder
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionCollection: bindActionCreators(actionCollectionCreator, dispatch),
        actionPaymentOrder: bindActionCreators(actionPaymentOrderCreator, dispatch),
        actionBankAirtimeService: bindActionCreators(actionBankAirtimeServiceCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(CreateSaleOrder);