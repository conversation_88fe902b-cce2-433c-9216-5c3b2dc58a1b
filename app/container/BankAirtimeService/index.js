import {
  SafeAreaView,
  StyleSheet,
  View,
  FlatList,
  Animated,
  Alert,
} from "react-native";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { COLORS } from "@styles";
import { TouchableOpacity } from "react-native";
import {
  BaseLoading,
  Icon,
  MyText,
} from "@components";
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import * as actionBankAirtimeServiceCreator from "./action";
import { useFocusEffect } from '@react-navigation/native';
import FastImage from 'react-native-fast-image';
import LimitModal from "./component/Modal/LimitModal";
import { helper } from "@common"
import { translate } from "@translate";
import FeeScheduleModal from "./component/Modal/FeeScheduleModal";

const BankAirtimeService = ({
  actionBankAirtimeService,
  dataServiceList,
  stateServiceList,
  itemCatalog,
  navigation
}) => {
  const [updateItem, setUpdateItem] = useState({});
  const scaleValue = useRef(new Animated.Value(1)).current;
  const opacityValue = useRef(new Animated.Value(1)).current;
  const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog ?? {};
  const { isFetching, isError, isEmpty, description } = stateServiceList ?? "";
  const [isVisibleModal, setIsVisibleModal] = useState(false);
  const [isVisibleModalCollection, setIsVisibleModalCollection] = useState(false);

  const animatePressIn = () => {
    Animated.parallel([
      Animated.spring(scaleValue, {
        toValue: 0.95,
        friction: 3,
        useNativeDriver: true,
      }),
      Animated.timing(opacityValue, {
        toValue: 0.8,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const animatePressOut = () => {
    Animated.parallel([
      Animated.spring(scaleValue, {
        toValue: 1,
        friction: 3,
        useNativeDriver: true,
      }),
      Animated.timing(opacityValue, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  };

  useFocusEffect(
    useCallback(() => {
      const data = {
        catalogID: ServiceCategoryID,
        serviceGroupID: AirtimeServiceGroupID,
      };
      actionBankAirtimeService.getServiceList(data);
    }, [actionBankAirtimeService])
  );

  const handleItemPress = useCallback(
    (item) => {
      switch (AirtimeServiceGroupID) {
        case 35: {
          actionBankAirtimeService.updateHeaderAirtime(item);
          setIsVisibleModalCollection(true)
          break;
        }
        case 37: {
          actionBankAirtimeService.updateHeaderAirtime(item);
          actionBankAirtimeService.clear_data_prepare_create_order()
          setIsVisibleModalCollection(true)
          break;
        }
        default:
          break;
      }
    },
    [
      actionBankAirtimeService,
      ServiceCategoryID,
      AirtimeServiceGroupID,
      navigation,
    ]
  );

  const handleInfoPress = useCallback((item) => {
    if (item?.PriceList?.length > 0) {
      setIsVisibleModal(true);
    } else {
      Alert.alert(
        translate("common.notification_uppercase"),
        "Không tìm thấy biểu phí!",
        [
          {
            text: translate("collection.btn_accept"),
            onPress: () => { },
          },
        ]
      );
    }
    setUpdateItem(item);
  }, []);

  const preGetServiceList = (ServiceCategoryID, AirtimeServiceGroupID) => {
    const data = {
      catalogID: ServiceCategoryID,
      serviceGroupID: AirtimeServiceGroupID,
    };
    actionBankAirtimeService.getServiceList(data);
  };

  const renderItem = ({ item, index }) => {
    return (
      <View
        style={{
          padding: 5,
        }}
      >
        <View
          style={{
            flexDirection: "row",
            backgroundColor: COLORS.bgFFFFFF,
            borderRadius: 15,
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 5 },
            shadowOpacity: 0.15,
            shadowRadius: 10,
            elevation: 5,
            padding: 10,
            alignItems: "center",
          }}
        >
          <View
            style={{
              flexDirection: "row",
            }}
          >
            <TouchableOpacity
              onPressIn={animatePressIn}
              onPressOut={() => {
                animatePressOut();
                handleItemPress(item);
              }}
              style={{
                flexDirection: "row",
                alignItems: "center",
                flex: 1,
                height: 90,
                borderRadius: 10,
                backgroundColor: COLORS.bgFFFFFF,
                flexDirection: "row",
                shadowColor: COLORS.bg8E8E93,
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.3,
                shadowRadius: 6,
                elevation: 6,
              }}
            >
              <FastImage
                style={{ width: 90, height: 50, marginLeft: 5 }}
                source={{
                  uri: item.Logo,
                  priority: FastImage.priority.normal,
                }}
                resizeMode={FastImage.resizeMode.contain}
              />
              <MyText
                style={{
                  color: COLORS.txt000000,
                  fontSize: 12,
                  fontWeight: "bold",
                  textAlign: "center",
                  flex: 1,
                  paddingHorizontal: 5,
                  textAlign: "left",
                }}
                text={item.AirTimeTransactionTypeName}
              />
            </TouchableOpacity>
            <View
              style={{
                width: "25%",
                height: 90,
                justifyContent: "center",
                alignItems: "center",
                backgroundColor: COLORS.txt0099E5,
                borderRadius: 10,
                marginLeft: 5,
              }}
            >
              <TouchableOpacity
                onPress={() => handleInfoPress(item)}
                style={{
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Icon
                  iconSet={"Ionicons"}
                  name={"information-circle-outline"}
                  color={COLORS.bgFFFFFF}
                  size={35}
                />
                <MyText
                  style={{
                    color: COLORS.bgFFFFFF,
                    width: 70,
                    textAlign: "center",
                  }}
                  text={"Biểu phí"}
                />
              </TouchableOpacity>
            </View>
          </View>
        </View>
        {isVisibleModalCollection && (
          <LimitModal
            isVisible={isVisibleModalCollection}
            data={updateItem}
            onClose={() => setIsVisibleModalCollection(false)}
            navigation={navigation}
          />
        )}
        {
          isVisibleModal && (
            <FeeScheduleModal
              selectedPartnerInfo={updateItem}
              listPartner={dataServiceList?.map(
                (partner) => partner.PriceList
              )}
              isVisible={isVisibleModal}
              hideModal={() => {
                setIsVisibleModal(false);
              }}
            />
          )
        }
      </View>
    );
  };

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: COLORS.bgFFFFFF,
      }}
    >
      <KeyboardAwareScrollView
        style={{
          flex: 1,
        }}
        enableResetScrollToCoords={false}
        keyboardShouldPersistTaps="always"
        bounces={false}
        overScrollMode="always"
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        extraScrollHeight={60}
      >
        <SafeAreaView
          style={{
            flex: 1,
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <BaseLoading
            isLoading={isFetching}
            isError={isError}
            isEmpty={isEmpty}
            textLoadingError={description}
            onPressTryAgains={() => {
              preGetServiceList(ServiceCategoryID, AirtimeServiceGroupID);
            }}
            content={
              <View>
                <FlatList
                  style={{
                    backgroundColor: COLORS.bgFFFFFF,
                    padding: 5,
                  }}
                  data={dataServiceList}
                  keyExtractor={(item, index) => `${index}`}
                  renderItem={(item) => renderItem(item)}
                />
              </View>
            }
          />
        </SafeAreaView>
      </KeyboardAwareScrollView>
    </View>
  );
};


const mapStateToProps = function (state) {
  return {
    dataServiceList: state.bankAirtimeServiceReducer.dataServiceList,
    stateServiceList: state.bankAirtimeServiceReducer.stateServiceList,
    itemCatalog: state.collectionReducer.itemCatalog,
  };
};

const mapDispatchToProps = function (dispatch) {
  return {
    actionBankAirtimeService: bindActionCreators(
      actionBankAirtimeServiceCreator,
      dispatch
    ),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(BankAirtimeService);

const styles = StyleSheet.create({});
