import React from 'react';
import {
    View,
    TextInput,
} from 'react-native';
import {
    Button,
    MyText
} from "@components";
import { constants } from '@constants';
import { translate } from '@translate';
import { COLORS } from "@styles";

const OtpCode = ({
    expireTime,
    code,
    onChange,
    onCreate,
    onVerify,
    customerPhone
}) => {
    const isCounting = (expireTime > 0);
    const countValue = isCounting ? `(${expireTime})` : "";
    const createOTP = (type) => () => {
        onCreate(type);
    }
    return (
        <View style={{
            minHeight: 40,
            width: constants.width,
            backgroundColor: COLORS.bgF0F0F0,
            justifyContent: "center",
            alignItems: "center",
            paddingVertical: 10,
        }}>
            <View style={{
                width: constants.width - 20,
                flexDirection: 'row',
                // marginBottom: 15,
                justifyContent: 'space-around',
                opacity: isCounting ? 0.5 : 1
            }}>
                <Button
                    text={`${translate('saleOrderPayment.btn_receive_OTP_call')} ${countValue}`}
                    styleContainer={{
                        width: 165,
                        height: 38,
                        backgroundColor: COLORS.btn288AD6,
                        borderRadius: 4,
                        opacity: 0
                    }}
                    styleText={{
                        color: COLORS.txtFFFFFF,
                        fontSize: 14,
                        fontWeight: "bold"
                    }}
                    onPress={createOTP("CALLCENTER")}
                    // disabled={isCounting}
                    disabled={true}
                />

                <Button
                    text={`${translate('saleOrderPayment.btn_receive_OTP_message')} ${countValue}`}
                    styleContainer={{
                        width: 165,
                        height: 38,
                        backgroundColor: COLORS.btn288AD6,
                        borderRadius: 4,
                    }}
                    styleText={{
                        color: COLORS.txtFFFFFF,
                        fontSize: 14,
                        fontWeight: "bold"
                    }}
                    onPress={createOTP("SMS")}
                    disabled={isCounting}
                />
            </View>

            <MyText
                text={translate('saleOrderPayment.OTP_will_be_sent_to')}
                addSize={-1.5}
                style={{
                    width: constants.width - 20,
                    marginVertical: 8,
                    textAlign: 'center',
                    color: COLORS.txt333333,
                    fontStyle: 'italic'
                }}>
                <MyText
                    text={customerPhone}
                    style={{
                        fontWeight: 'bold',
                        fontStyle: 'normal'
                    }} />
                <MyText
                    text={')'}
                    addSize={-1.5}
                    style={{
                        width: constants.width - 20,
                        marginVertical: 8,
                        textAlign: 'center',
                        color: COLORS.txt333333,
                        fontStyle: 'italic'
                    }} />
            </MyText>

            <View style={{
                width: constants.width - 20,
                flexDirection: "row",
                justifyContent: 'space-around',
                opacity: isCounting ? 1 : 0.5
            }}>
                <TextInput
                    style={{
                        height: 38,
                        width: constants.width - 140,
                        borderWidth: 1,
                        borderRadius: 2,
                        borderColor: COLORS.bdCCCCCC,
                        paddingHorizontal: 10,
                        backgroundColor: COLORS.bgFFFFFF,
                        color: COLORS.txt333333,
                        borderRadius: 4,
                    }}
                    placeholder={translate('saleOrderPayment.placeholder_enter_OTP')}
                    value={code}
                    onChangeText={onChange}
                    keyboardType="numeric"
                    returnKeyType={"done"}
                    editable={isCounting}
                />
                <Button
                    text={translate('saleOrderPayment.btn_verify')}
                    styleContainer={{
                        width: 100,
                        height: 38,
                        backgroundColor: COLORS.btn288AD6,
                        borderRadius: 4,
                        marginLeft: 5,
                    }}
                    styleText={{
                        color: COLORS.txtFFFFFF,
                        fontSize: 14,
                        fontWeight: "bold"
                    }}
                    onPress={onVerify}
                    disabled={!isCounting}
                />
            </View>
        </View>
    );
}

export default OtpCode;