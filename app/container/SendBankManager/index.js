import React, { Component } from 'react';
import {
    TouchableOpacity,
    View,
    SafeAreaView,
    StyleSheet,
    Alert,
    FlatList
} from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { COLORS } from '@styles';
import {
    MyText,
    Icon,
    showBlockUI,
    hide<PERSON>lockUI,
    BaseLoading
} from '@components';
import { constants } from '@constants';
import * as sendBankActionCreator from './action';

class SendBankManager extends Component {
    constructor(props) {
        super(props);
        const initData = [
            {
                iconSet: 'MaterialIcons',
                iconName: 'post-add',
                color: COLORS.ic147EFB,
                title: 'Tạo phiếu nộp tiền',
                name: 'CreateDepositReceipt'
            },
            {
                iconSet: 'MaterialCommunityIcons',
                iconName: 'newspaper-variant-outline',
                color: COLORS.ic147EFB,
                title: '<PERSON>u<PERSON><PERSON> lý phiếu nộp tiền',
                name: 'DepositReceipt'
            },
            {
                iconSet: 'MaterialCommunityIcons',
                iconName: 'file-check-outline',
                color: COLORS.ic147EFB,
                title: '<PERSON>u<PERSON>n lý yêu cầu duyệt chi tiền',
                name: 'SendBankStask'
            }
        ];
        this.state = {
            inventoryMenu: initData
        };
    }

    componentDidMount() {}

    onPressCheck = (item) => {
        const { navigation } = this.props;
        const sendBankAction = this.props.sendBankAction;
        if (item.name === 'DepositReceipt') {
            showBlockUI();
            sendBankAction
                .checkPremissionSendBank('ACC_SENDBANK_MPOS_MANAGEMENT')
                .then((res) => {
                    hideBlockUI();
                    navigation.navigate('DepositReceipt');
                })
                .catch((err) => {
                    Alert.alert('Thông báo', err.msgError, [
                        {
                            text: 'OK',
                            onPress: () => hideBlockUI()
                        }
                    ]);
                });
        } else if (item.name === 'SendBankStask') {
            showBlockUI();
            sendBankAction
                .checkPremissionSendBank(
                    'ACC_SENDBANK_MPOS_MANAGEMENT_APPROVEBANK'
                )
                .then((res) => {
                    hideBlockUI();
                    navigation.navigate('SendBankStask');
                })
                .catch((err) => {
                    Alert.alert('Thông báo', err.msgError, [
                        {
                            text: 'OK',
                            onPress: () => hideBlockUI()
                        }
                    ]);
                });
        } else if (item.name === 'CreateDepositReceipt') {
            showBlockUI();
            sendBankAction
                .checkPremissionSendBank('ACC_SENDBANK_MPOS_CREATE')
                .then((res) => {
                    hideBlockUI();
                    navigation.navigate('CreateDepositReceipt');
                })
                .catch((err) => {
                    Alert.alert('Thông báo', err.msgError, [
                        {
                            text: 'OK',
                            onPress: () => hideBlockUI()
                        }
                    ]);
                });
        } else {
            Alert.alert(
                'Thông báo',
                'Chức năng này chưa được cập nhật, vui lòng quay lại sau!',
                [
                    {
                        text: 'OK',
                        onPress: () => console.log('OK Pressed')
                    }
                ]
            );
        }
    };

    renderItem = (item) => {
        const navigation = this.props.navigation;
        return (
            <TouchableOpacity
                style={{
                    width: constants.width - 40,
                    alignContent: 'center',
                    margin: 8,
                    borderColor: COLORS.bd288AD6,
                    borderWidth: StyleSheet.hairlineWidth,
                    borderRadius: 4,
                    paddingVertical: 8,
                    paddingHorizontal: 10,
                    alignItems: 'center',
                    flexDirection: 'row',
                    backgroundColor: COLORS.bgFFFFFF
                }}
                onPress={() => {
                    this.onPressCheck(item);
                }}
                activeOpacity={0.8}>
                <Icon
                    size={26}
                    color={item.color}
                    name={`${item.iconName}`}
                    iconSet={`${item.iconSet}`}
                />
                <MyText
                    style={{
                        fontSize: 16,
                        marginLeft: 10
                    }}
                    text={item.title}
                />
            </TouchableOpacity>
        );
    };
    render() {
        const { inventoryMenu } = this.state;
        return (
            <SafeAreaView style={{ flex: 1 }}>
                <View
                    style={{
                        flex: 1,
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexWrap: 'wrap',
                        margin: constants.getSize(10)
                    }}>
                    <FlatList
                        contentContainerStyle={{
                            paddingVertical: 10
                        }}
                        data={inventoryMenu}
                        keyExtractor={(item, index) => index.toString()}
                        renderItem={({ item }) => this.renderItem(item)}
                        scrollEnabled={false}
                    />
                </View>
            </SafeAreaView>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        userInfo: state.userReducer
    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        sendBankAction: bindActionCreators(sendBankActionCreator, dispatch)
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(SendBankManager);
