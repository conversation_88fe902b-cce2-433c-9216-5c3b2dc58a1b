import React, { useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import KModal from 'react-native-modal';
import { constants } from '@constants';
import { Icon, MyText } from '@components';
import { COLORS } from '@styles';
import ImageViewer from 'react-native-image-zoom-viewer';

function ModalImage({ isVisible, hideModal, listImage }) {
    return (
        <KModal
            isVisible={isVisible}
            style={{
                margin: 0,
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: 5,
                paddingHorizontal: 0
            }}
            deviceWidth={constants.width}
            deviceHeight={constants.height}
            backdropTransitionOutTiming={0}
            animationIn="slideInUp"
            animationOut="slideOutDown"
            useNativeDriver={true}
            hideModalContentWhileAnimating={true}>
            <View
                style={{
                    flex: 1,
                    backgroundColor: COLORS.bg000000,
                    paddingTop: constants.heightTopSafe
                }}>
                <View
                    style={{
                        width: constants.width,
                        height: constants.height,
                        borderRadius: 10,
                        overflow: 'hidden',
                        marginBottom: 10
                    }}>
                    <TouchableOpacity
                        style={{
                            position: 'absolute',
                            right: 5,
                            top: constants.heightTopSafe + 5,
                            zIndex: 100
                        }}
                        onPress={hideModal}>
                        <Icon
                            iconSet="Ionicons"
                            name="close"
                            color={COLORS.bgFFFFFF}
                            size={32}
                        />
                    </TouchableOpacity>
                    <ImageViewer
                        renderHeader={() => (
                            <TouchableOpacity
                                style={{
                                    position: 'absolute',
                                    right: 5,
                                    top: constants.heightTopSafe + 5,
                                    zIndex: 100
                                }}
                                onPress={hideModal}>
                                <Icon
                                    iconSet="Ionicons"
                                    name="close"
                                    color={COLORS.bgFFFFFF}
                                    size={32}
                                />
                            </TouchableOpacity>
                        )}
                        imageUrls={listImage.map((item) => ({
                            url: `data:image/png;base64,${item?.file}`
                        }))}
                        enableSwipeDown
                        onCancel={hideModal}
                    />
                </View>
            </View>
        </KModal>
    );
}
const styles = StyleSheet.create({
    header: {
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: COLORS.bg6CAF52,
        height: 44,
        overflow: 'hidden',
        borderRadius: 5
    },
    btn: {
        padding: 10,
        backgroundColor: COLORS.btn2C8BD7,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 5,
        width: 100
    },
    txtBtn: {
        fontWeight: 'bold',
        color: COLORS.txtFFFFFF
    },
    content: {
        borderWidth: 1,
        width: '90%',
        marginVertical: 10,
        height: 300,
        borderColor: COLORS.bdDDDDDD,
        padding: 10
    },
    fieldSet: {
        flexDirection: 'row',
        marginVertical: 5,
        marginHorizontal: 10,
        justifyContent: 'space-between'
    },
    txCalendar: {
        color: COLORS.txt0088F2
    }
});
export default ModalImage;
