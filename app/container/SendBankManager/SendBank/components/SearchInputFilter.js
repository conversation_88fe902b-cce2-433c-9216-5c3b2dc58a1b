import React, { useState } from 'react';
import { View, TouchableOpacity, TextInput, Keyboard } from 'react-native';
import { Icon } from '@components';
import { COLORS } from '@styles';
import { constants } from '@constants';

const SearchInputFilter = ({
    onSubmit,
    inputText,
    onChangeText,
    onClearText,
    placeholder,
    onPressFilter,
    style
}) => {
    return (
        <View
            style={{
                flexDirection: 'row',
                marginHorizontal: constants.getSize(8),
                marginTop: 5,
                marginVertical: 10
            }}>
            <View
                style={[
                    {
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        width: constants.width - 60,
                        borderRadius: constants.getSize(20),
                        paddingHorizontal: constants.getSize(15),
                        height: constants.getSize(40),
                        backgroundColor: COLORS.bgFFFFFF,
                        borderColor: COLORS.bdB9B9B9,
                        justifyContent: 'center',
                        alignItems: 'center'
                    },
                    style
                ]}>
                <TextInput
                    placeholder={placeholder}
                    style={{
                        width: inputText != '' ? '80%' : '90%',
                        alignItems: 'center',
                        justifyContent: 'center'
                    }}
                    value={inputText}
                    onChangeText={onChangeText}
                    onFocus={() => {}}
                    onSubmitEditing={() => {
                        Keyboard.dismiss();
                        onSubmit(inputText);
                    }}
                    returnKeyType="done"
                />
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    {inputText == '' ? null : (
                        <TouchableOpacity
                            style={{
                                backgroundColor: COLORS.bdB9B9B9,
                                alignItems: 'center',
                                justifyContent: 'center',
                                marginHorizontal: 5,
                                width: constants.getSize(18),
                                height: constants.getSize(18),
                                borderRadius: 10
                            }}
                            activeOpacity={0.7}
                            onPress={onClearText}>
                            <Icon
                                iconSet="Ionicons"
                                name="md-close"
                                style={{ color: COLORS.icFFFFFF, fontSize: 15 }}
                            />
                        </TouchableOpacity>
                    )}
                    <TouchableOpacity
                        style={{
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: constants.getSize(32),
                            height: constants.getSize(32),
                            borderRadius: 20,
                            marginLeft: 5
                        }}
                        onPress={() => {
                            Keyboard.dismiss();
                            onSubmit(inputText);
                        }}>
                        <Icon
                            iconSet="Ionicons"
                            name="ios-search"
                            style={{ fontSize: 24, color: COLORS.ic0088F2 }}
                        />
                    </TouchableOpacity>
                </View>
            </View>
            <TouchableOpacity
                style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: constants.getSize(40),
                    height: constants.getSize(40),
                    borderRadius: 20,
                    marginLeft: 5
                }}
                onPress={onPressFilter}>
                <Icon
                    iconSet="Ionicons"
                    name="funnel-sharp"
                    style={{ fontSize: 24, color: COLORS.ic0088F2 }}
                />
            </TouchableOpacity>
        </View>
    );
};

export default SearchInputFilter;
