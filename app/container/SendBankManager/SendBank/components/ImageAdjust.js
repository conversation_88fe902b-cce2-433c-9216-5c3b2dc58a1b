import React from 'react';
import { Icon, MyText } from '../../../components';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { COLORS } from '@styles';

const ImageAdjust = ({
    onCamera,
    onDelete,
    onUpload,
    urlLocal,
    urlRemote,
    title,
    isRequire
}) => {
    const uriImage = urlRemote ? urlRemote : urlLocal;
    const isNonImage = !uriImage;
    return (
        <View
            style={{
                // flex: 1
            }}>
            <MyText
                text={title}
                addSize={-1.5}
                style={{
                    color: COLORS.txt333333,
                    textAlign: 'center'
                }}>
                {isRequire && (
                    <MyText
                        text={'*'}
                        style={{ color: COLORS.txtFF0000 }}></MyText>
                )}
            </MyText>
            {isNonImage ? (
                <TouchableOpacity
                    style={{
                        height: 120,
                        justifyContent: 'center',
                        alignItems: 'center',
                        backgroundColor: COLORS.btnF5F5F5,
                        marginHorizontal: 1
                    }}
                    onPress={onCamera}>
                    <Icon
                        iconSet={'Ionicons'}
                        name={'ios-camera'}
                        color={COLORS.icFFB23F}
                        size={60}
                    />
                </TouchableOpacity>
            ) : (
                <View
                    style={{
                        flex: 1,
                        justifyContent: 'flex-start',
                        alignItems: 'center',
                        marginHorizontal: 1
                    }}>
                    <View
                        style={{
                            width: 100,
                            height: 120
                        }}>
                       
                    </View>
                </View>
            )}
        </View>
    );
};
const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignSelf: 'center',
        paddingVertical: 5
    },
    txtext: {
        flex: 0.5,
        justifyContent: 'center',
        fontWeight: '500'
    }
});

export default ImageAdjust;
