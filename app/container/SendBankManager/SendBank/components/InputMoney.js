import React from 'react';
import { View } from 'react-native';
import { NumberInput } from '@components';
import { constants } from '@constants';
import { COLORS } from '@styles';

const InputMoney = ({
    name,
    value,
    colorValue = COLORS.txt333333,
    onChange,
    onFocus,
    onBlur,
    autoFocus,
    isActiveNumbericIOS
}) => {
    return (
        <View
            style={{
                // flex: 1,
                flexDirection: 'row'
            }}>
            <View
                style={{
                    width: '100%',
                    minHeight: 40,
                    justifyContent: 'center',
                    width: 'auto',
                    borderWidth: 0.5,
                    borderRadius: 8,
                    backgroundColor: COLORS.bgFFFFFF
                }}>
                <NumberInput
                    style={{
                        height: 40,
                        width: constants.width - 60,
                        paddingHorizontal: 9,
                        color: colorValue
                    }}
                    placeholder="Vui lòng nhập số tiền"
                    value={value}
                    onChangeText={onChange}
                    onFocus={onFocus}
                    onBlur={onBlur}
                    blurOnSubmit
                    autoFocus={autoFocus}
                    isActiveNumbericIOS={isActiveNumbericIOS}
                />
            </View>
        </View>
    );
};

export default InputMoney;
