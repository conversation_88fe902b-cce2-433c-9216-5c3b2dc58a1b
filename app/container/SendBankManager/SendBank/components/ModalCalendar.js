import React, { useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import moment from 'moment';
import { Calendar, LocaleConfig } from 'react-native-calendars';
import KModal from 'react-native-modal';
import { MyText } from '@components';
import { constants } from '@constants';
import { translate } from '@translate';
import { COLORS } from '@styles';

function ModalCalendar({
    isVisible,
    hideModal,
    startDate,
    endDate,
    setDate,
    onConfirmDate
}) {
    const [state, setState] = React.useState({
        isStartDatePicked: false,
        isEndDatePicked: false,
        startDate: startDate,
        endDate: endDate
    });
    const [markedDates, setMarkedDates] = React.useState({});
    const [days, setDays] = React.useState(new Date(moment().year()));
    LocaleConfig.locales['vi'] = {
        monthNames: [
            'Tháng 1',
            'Tháng 2',
            'Tháng 3',
            'Tháng 4',
            'Tháng 5',
            'Tháng 6',
            'Tháng 7',
            'Tháng  8',
            'Tháng 9',
            'Tháng 10',
            'Tháng 11',
            'Tháng 12'
        ],
        monthNamesShort: [
            'Tháng 1',
            'Tháng 2',
            'Tháng 3',
            'Tháng 4',
            'Tháng 5',
            'Tháng 6',
            'Tháng 7',
            'Tháng  8',
            'Tháng 9',
            'Tháng 10',
            'Tháng 11',
            'Tháng 12'
        ],
        dayNames: [
            'Chủ Nhật',
            'Thứ Hai',
            'Thứ Ba',
            'Thứ Tư',
            'Thứ Năm',
            'Thứ Sáu',
            'Thứ Bảy'
        ],
        dayNamesShort: ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'],
        today: "Aujourd'hui"
    };
    LocaleConfig.defaultLocale = 'vi';

    const getDaysInMonth = (month, year) => {
        return new Date(year, month, 0).getDate(); // Tính số ngày trong tháng
    };

    const onDayPress = (date) => {
        getDaysInMonth(date.month, date.year);
        let daysInMonth = getDaysInMonth(date.month, date.year);
        setDays(daysInMonth);
        if (state.isStartDatePicked == false) {
            setState({
                ...state,
                isStartDatePicked: true,
                isEndDatePicked: false,
                startDate: date.timestamp,
                endDate: date.timestamp
            });
            setMarkedDates({
                [date.dateString]: {
                    startingDay: true,
                    selected: true,
                    color: COLORS.txt50CEBB,
                    marked: true,
                    selectedColor: COLORS.txt50CEBB
                }
            });
        } else {
            setState({
                ...state,
                isStartDatePicked: false,
                isEndDatePicked: true,
                endDate: date.timestamp
            });
            let updateMarkedDates = { ...markedDates };
            let startDate = moment(new Date(state.startDate));
            let endDate = moment(new Date(date.dateString));
            let range = endDate.diff(new Date(startDate), 'days');
            if (range > 0) {
                for (let i = 1; i <= range; i++) {
                    let tempDate = startDate.add(1, 'day');
                    tempDate = moment(new Date(tempDate)).format('YYYY-MM-DD');
                    if (i < range) {
                        updateMarkedDates[tempDate] = {
                            selected: true,
                            color: COLORS.txt70D7C7,
                            textColor: COLORS.txtFFFFFF
                        };
                    } else {
                        updateMarkedDates[tempDate] = {
                            endingDay: true,
                            selected: true,
                            dotColor: 'red',
                            activeOpacity: 0,
                            color: COLORS.txt50CEBB,
                            marked: true,
                            selectedColor: COLORS.txt50CEBB
                        };
                    }
                }
                setMarkedDates(updateMarkedDates);
            } else {
                setState({
                    ...state,
                    isStartDatePicked: true,
                    isEndDatePicked: false,
                    startDate: date.timestamp,
                    endDate: date.timestamp
                });
                setMarkedDates({
                    [date.dateString]: {
                        startingDay: true,
                        selected: true,
                        color: COLORS.txt50CEBB,
                        marked: true,
                        selectedColor: COLORS.txt50CEBB
                    }
                });
            }
        }
    };
    useEffect(() => {
        setState({
            ...state,
            isStartDatePicked: false,
            isEndDatePicked: false
        });
        setMarkedDates({
            [moment(new Date(state.startDate)).format('YYYY-MM-DD')]: {
                startingDay: true,
                selected: true,
                color: COLORS.txt50CEBB,
                marked: true,
                selectedColor: COLORS.txt50CEBB
            }
        });
        let updateMarkedDates = { ...markedDates };
        let startDate = moment(new Date(state.startDate));
        let endDate = moment(new Date(state.endDate));

        let range = endDate.diff(new Date(startDate), 'days');
        if (range > 0) {
            for (let i = 0; i <= range; i++) {
                let tempDate = startDate.add(1, 'day');
                tempDate = moment(new Date(tempDate)).format('YYYY-MM-DD');
                if (i == 0) {
                    tempDate = startDate.add(-1, 'day');
                    updateMarkedDates[
                        moment(new Date(tempDate)).format('YYYY-MM-DD')
                    ] = {
                        startingDay: true,
                        selected: true,
                        color: COLORS.txt50CEBB,
                        marked: true,
                        selectedColor: COLORS.txt50CEBB
                    };
                } else if (i < range) {
                    updateMarkedDates[tempDate] = {
                        selected: true,
                        color: COLORS.txt70D7C7,
                        textColor: COLORS.txtFFFFFF
                    };
                } else {
                    updateMarkedDates[tempDate] = {
                        endingDay: true,
                        selected: true,
                        dotColor: 'red',
                        activeOpacity: 0,
                        color: COLORS.txt50CEBB,
                        marked: true,
                        selectedColor: COLORS.txt50CEBB
                    };
                }
            }
            setMarkedDates(updateMarkedDates);
        }
    }, [startDate]);

    const handleClose = () => {
        const start = moment(state.startDate);
        const end = moment(state.endDate);
        const difference = end.diff(start, 'days');
        if (difference >= days) {
            Alert.alert(
                'Thông báo',
                'Ngày bắt đầu và ngày kết thúc chỉ được cách nhau tối đa 30 ngày!',
                [
                    {
                        text: 'OK',
                        onPress: () => {}
                    }
                ]
            );
        } else {
            hideModal();
            setDate({
                startDate: start,
                endDate: end
            });
            onConfirmDate;
        }
    };

    return (
        <KModal
            isVisible={isVisible}
            style={{
                margin: 0,
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: 5,
                paddingHorizontal: 0
            }}
            deviceWidth={constants.width}
            deviceHeight={constants.height}
            backdropTransitionOutTiming={0}
            animationIn="slideInUp"
            animationOut="slideOutDown"
            useNativeDriver={true}
            hideModalContentWhileAnimating={true}>
            <View style={{ backgroundColor: COLORS.bgFFFFFF, width: '100%' }}>
                <Calendar
                    current={new Date()}
                    markedDates={markedDates}
                    onDayPress={onDayPress}
                    headerStyle={styles.txCalendar}
                />
                <View
                    style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        paddingHorizontal: 20,
                        marginVertical: 10,
                        justifyContent: 'center'
                    }}>
                    <TouchableOpacity
                        style={styles.btn}
                        activeOpacity={0.7}
                        onPress={() => {
                            handleClose();
                        }}>
                        <MyText
                            text={translate('common.btn_close')}
                            style={styles.txtBtn}
                        />
                    </TouchableOpacity>
                </View>
            </View>
        </KModal>
    );
}
const styles = StyleSheet.create({
    header: {
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: COLORS.bg6CAF52,
        height: 44,
        overflow: 'hidden',
        borderRadius: 5
    },
    btn: {
        padding: 10,
        backgroundColor: COLORS.btn2C8BD7,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 5,
        width: 100
    },
    txtBtn: {
        fontWeight: 'bold',
        color: COLORS.txtFFFFFF
    },
    content: {
        borderWidth: 1,
        width: '90%',
        marginVertical: 10,
        height: 300,
        borderColor: COLORS.bdDDDDDD,
        padding: 10
    },
    fieldSet: {
        flexDirection: 'row',
        marginVertical: 5,
        marginHorizontal: 10,
        justifyContent: 'space-between'
    },
    txCalendar: {
        color: COLORS.txt0088F2
    }
});
export default ModalCalendar;
