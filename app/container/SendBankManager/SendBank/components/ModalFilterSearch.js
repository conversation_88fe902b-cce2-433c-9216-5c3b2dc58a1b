import React, { useState, useEffect, useRef } from 'react';
import {
    View,
    TouchableOpacity,
    StyleSheet,
    Keyboard,
    Pressable
} from 'react-native';
import { COLORS } from '@styles';
import KModal from 'react-native-modal';
import { constants } from '@constants';
import { MyText, Icon, TitleInput, Picker } from '@components';
import { connect } from 'react-redux';
import { translate } from '@translate';
import moment from 'moment';
import ModalCalendar from './ModalCalendar';

const ModalFilterSearch = ({
    title,
    isVisible,
    hideModal,
    onPressSearch,
    dataApprovalStatus
}) => {
    const [modalVisible, setModalVisible] = useState(isVisible);
    const [userTextVoucher, setTextVoucher] = useState('');
    const [fromDate, setFromDate] = useState(new Date());
    const [toDate, setToDate] = useState(new Date());
    const [status, setStatus] = useState('');
    const [dataStatus, setDataStatus] = useState(dataApprovalStatus);
    const [statusCode, setStatusCode] = useState('');
    const [isShowCalendar, setIsShowCalendar] = useState(false);

    const handleOpenCalendar = () => {
        setIsShowCalendar(true);
    };

    const handleSearchFilter = () => {
        Keyboard.dismiss();
        hideModal();
        const data = { userTextVoucher, fromDate, toDate, statusCode };
        onPressSearch(data);
    };

    return (
        <KModal
            style={styles.vwKmodal}
            animationIn="slideInUp"
            animationOut="slideOutDown"
            useNativeDriver={true}
            deviceWidth={constants.width}
            deviceHeight={constants.height}
            backdropTransitionOutTiming={0}
            hideModalContentWhileAnimating={true}
            visible={modalVisible}
            onRequestClose={hideModal}>
            <View style={styles.vwSearchItem}>
                <View style={styles.btnClose}>
                    <View style={styles.txHeader}>
                        <MyText
                            style={{
                                color: COLORS.txt147EFB
                            }}
                            text={title}
                        />
                    </View>
                    <Pressable style={styles.btnHeader} onPress={hideModal}>
                        <Icon
                            iconSet="MaterialCommunityIcons"
                            size={24}
                            color={COLORS.bg147EFB}
                            name={'close'}
                        />
                    </Pressable>
                </View>
                <View style={styles.vwInput}>
                    <TitleInput
                        title={
                            translate('sendbank.text_input_voucher_code') + ':'
                        }
                        styleInput={[styles.txInput]}
                        placeholder={'Vui lòng nhập mã yêu cầu duyệt'}
                        onChangeText={setTextVoucher}
                        value={userTextVoucher}
                        blurOnSubmit={true}
                        width={constants.width - 20}
                        height={40}
                        clearText={() => {
                            setTextVoucher('');
                        }}
                        editable
                        keyboardType="number"
                    />
                </View>
                <View style={styles.vwSearch}>
                    <TouchableOpacity
                        style={styles.btModalCalendar}
                        onPress={() => handleOpenCalendar()}>
                        <MyText
                            style={{
                                width: '80%',
                                marginLeft: 7,
                                paddingHorizontal: 5
                            }}
                            text={`${moment(fromDate).format(
                                'DD/MM/YYYY'
                            )} - ${moment(toDate).format('DD/MM/YYYY')} `}
                        />
                        <Icon
                            iconSet="MaterialCommunityIcons"
                            name="calendar-range"
                            style={{
                                fontSize: 24,
                                marginRight: 10,
                                color: COLORS.ic2C8BD7
                            }}
                        />
                    </TouchableOpacity>
                </View>
                <View style={styles.vwSearch}>
                    <Picker
                        style={styles.vwPickerSearch}
                        label="approveReqStatusName"
                        value="approveReqStatusName"
                        defaultLabel={translate('sendbank.select_status')}
                        valueSelected={status}
                        data={dataStatus}
                        onChange={(item) => {
                            setStatus(item.approveReqStatusName);
                            setStatusCode(item.approveReqStatus);
                        }}
                    />
                </View>
                <TouchableOpacity
                    style={styles.btSearch}
                    onPress={() => handleSearchFilter()}>
                    <MyText style={styles.txButtonSearch} text={'Tìm kiếm'} />
                </TouchableOpacity>
            </View>
            <ModalCalendar
                isVisible={isShowCalendar}
                hideModal={() => setIsShowCalendar(false)}
                startDate={fromDate}
                endDate={toDate}
                setDate={(day) => {
                    setFromDate(day.startDate);
                    setToDate(day.endDate);
                }}
            />
        </KModal>
    );
};

const mapStateToProps = function (state) {
    return {
        dataSearch: state.sendBankReducer.dataSearch,
        dataApprovalStatus: state.sendBankReducer.dataApprovalStatus
    };
};

const mapDispatchToProps = function (dispacth) {
    return {};
};

const styles = StyleSheet.create({
    vwKmodal: {
        // width: '100%',
        // height: '100%',
        margin: 0,
        padding: 0,
        borderRadius: 8,
        paddingHorizontal: 0,
        justifyContent: 'flex-end',
        backgroundColor: COLORS.bg0000005
    },
    vwSearchItem: {
        width: 'auto',
        height: '50%',
        backgroundColor: COLORS.bgF0F0F0,
        borderRadius: 12
    },
    vwSearch: {
        flexDirection: 'row',
        backgroundColor: COLORS.bgFFFFFF,
        width: 'auto',
        height: 40,
        borderRadius: 8,
        marginVertical: 8,
        marginHorizontal: 10
    },
    btSearch: {
        flex: 0.95,
        marginLeft: 17,
        justifyContent: 'center'
    },
    txCancel: {
        color: COLORS.txt7affcc,
        fontWeight: '700'
        // marginRight:
    },
    lnModal: {
        marginTop: 6,
        backgroundColor: COLORS.bgA7A7A7,
        width: constants.width / 7,
        height: 6,
        borderRadius: 10,
        justifyContent: 'center',
        alignSelf: 'center'
    },
    vwSearchModal: {
        marginVertical: 10,
        marginHorizontal: 20,
        paddingHorizontal: 10,
        backgroundColor: COLORS.bgFFFFFF,
        borderRadius: 16,
        height: 40,
        width: 'auto',
        borderWidth: 1,
        borderColor: COLORS.bgA7A7A7
    },
    ltStore: {
        width: '90%',
        alignSelf: 'center',
        flexDirection: 'row',
        paddingVertical: 10,
        borderBottomWidth: 1,
        borderColor: COLORS.bgA7A7A7,
        alignItems: 'center'
    },
    txItem: {
        marginLeft: 8
    },
    icStore: {
        width: constants.getSize(30),
        height: '100%',
        alignItems: 'center',
        alignContent: 'center',
        justifyContent: 'center',
        paddingLeft: constants.getSize(4)
    },
    vwSearch: {
        width: 'auto',
        height: 40,
        marginVertical: 8,
        marginHorizontal: 10
    },
    btModalCalendar: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: COLORS.bgFFFFFF,
        borderRadius: 5,
        paddingHorizontal: 5,
        height: 44
    },
    vwPickerSearch: {
        flexDirection: 'row',
        height: 38,
        width: constants.width - 20,
        backgroundColor: COLORS.btnFFFFFF,
        paddingHorizontal: 10,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center'
    },
    vwInput: {
        marginTop: 10,
        height: 'auto',
        alignItems: 'center'
    },
    txInput: {
        borderRadius: 8,
        borderColor: COLORS.bdCCCCCC,
        marginBottom: 5,
        paddingHorizontal: 10,
        paddingVertical: 8,
        backgroundColor: COLORS.bgFFFFFF
    },
    vwListItem: {
        flex: 1
    },
    vwItem: {
        backgroundColor: COLORS.bgFFFFFF,
        padding: 8,
        borderRadius: 8,
        marginVertical: 8,
        marginHorizontal: 10
    },
    btSearch: {
        width: constants.width / 3,
        height: 36,
        borderRadius: 18,
        backgroundColor: COLORS.bg288AD6,
        justifyContent: 'center',
        alignItems: 'center',
        alignSelf: 'center',
        marginVertical: 10
    },
    txButtonSearch: { color: COLORS.txtFFFFFF, fontWeight: '800' },
    txText: {
        flex: 0.5,
        fontWeight: '500'
    },
    btUserBrowser: {
        flex: 0.5,
        backgroundColor: COLORS.ic288AD6,
        borderRadius: 10,
        padding: 8
    },
    vwButtonAction: {
        flexDirection: 'row',
        backgroundColor: COLORS.bgFFFFFF,
        width: 'auto',
        borderRadius: 8,
        marginVertical: 6
    },
    txButtonAction: {
        flex: 0.5,
        borderRadius: 10,
        alignItems: 'center',
        flexDirection: 'row'
    },
    txSeeMore: {
        fontSize: 13,
        fontWeight: '600',
        color: COLORS.txt0088F2,
        marginRight: 8
    },
    btnActionFile: {
        borderRadius: 10,
        alignItems: 'center',
        flexDirection: 'row'
    },
    txFile: {
        fontSize: 12,
        fontWeight: '600',
        color: COLORS.txt0088F2,
        marginLeft: 3
    },
    btnClose: {
        width: constants.width - 10,
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginVertical: 6
    },
    txHeader: {
        alignSelf: 'center',
        marginBottom: 10,
        justifyContent: 'center',
        alignItems: 'center'
    },
    btnHeader: {
        justifyContent: 'flex-end',
        alignItems: 'flex-end'
    }
});

export default connect(mapStateToProps, mapDispatchToProps)(ModalFilterSearch);
