import React, { useState } from 'react';
import { View, TouchableOpacity, StyleSheet, Pressable } from 'react-native';
import { Icon, MyText } from '@components';
import { constants } from '@constants';
import { helper } from '@common';
import { translate } from '@translate';
import { TextField } from './TextField';
import { COLORS } from '@styles';

function ItemSendbank({ item }) {
    const statusMapping = {
        NEW: 'Khởi tạo',
        NEEDHANDLE: 'Chờ xử lý',
        APPROVED: 'Đã duyệt',
        CANCEL: 'Đã huỷ'
    };
    return (
        <View style={styles.vwItem}>
            <Pressable>
                <Icon
                    iconSet="Ionicons"
                    size={20}
                    color={COLORS.bg147EFB}
                    name={'ellipsis-vertical-sharp'}
                />
            </Pressable>
            <TextField
                label={translate('sendbank.requires_approval_code')}
                value={item.approveRequestId}
            />
            <TextField
                label={translate('sendbank.supermarket_requires_approval')}
                value={item.storeName}
            />
            <TextField
                label={translate('sendbank.date_creation_approval_request')}
                value={new Date(item.createdDate).toLocaleDateString()}
            />
            <View style={styles.vwButtonAction}>
                <View style={{ flex: 0.5, justifyContent: 'center' }}>
                    <MyText
                        style={{
                            fontWeight: '500'
                        }}
                        text={translate('sendbank.staff_approved') + ':'}
                    />
                </View>
                <TouchableOpacity
                    style={styles.txButtonAction}
                    onPress={() => {
                        navigation.navigate('EditSendBank', item);
                    }}>
                    <MyText
                        style={{
                            flex: 0.8,
                            fontSize: 15,
                            fontWeight: '600',
                            color: COLORS.txt008848
                        }}
                        text={item.approveUserName}
                    />
                    <View
                        style={{
                            flex: 0.2,
                            justifyContent: 'center',
                            alignItems: 'center'
                        }}>
                        <Icon
                            iconSet="Feather"
                            name={'edit'}
                            size={18}
                            color={COLORS.txt008848}
                        />
                    </View>
                </TouchableOpacity>
            </View>
            <View
                style={{
                    width: 'auto',
                    height: 1,
                    backgroundColor: COLORS.bgDDDDDD
                }}
            />
            <View style={styles.vwButtonAction}>
                <View style={{ flex: 0.5, justifyContent: 'center' }}>
                    <MyText
                        style={{
                            fontWeight: '500'
                        }}
                        text={translate('sendbank.voucher_code') + ':'}
                    />
                </View>
                <TouchableOpacity
                    style={styles.txButtonAction}
                    onPress={() => {
                        sendBankAction.getpaymentdetail(item.approveRequestId);
                        navigation.navigate('DetailSendBank', item);
                    }}>
                    <MyText style={styles.txSeeMore} text={'Xem thêm'} />
                    <Icon
                        iconSet="Feather"
                        name={'eye'}
                        size={14}
                        color={COLORS.bg57a7ff}
                    />
                </TouchableOpacity>
            </View>
            <TextField
                label={translate('sendbank.bank_pays_money')}
                value={item.bankName}
            />
            <TextField
                label={translate('sendbank.total_amount')}
                value={helper.convertNum(item.totalMoney)}
            />
            <View
                style={{
                    width: 'auto',
                    height: 1,
                    backgroundColor: COLORS.bgDDDDDD
                }}
            />
            <TextField
                label={translate('sendbank.payment_date')}
                value={new Date(item.realSendDate).toLocaleDateString()}
            />
            <TextField
                label={translate('sendbank.staff_created')}
                value={item.createdUserName}
            />
            <TextField
                label={translate('sendbank.processing_status')}
                value={statusMapping[item.status]}
            />
            <TextField
                label={translate('sendbank.processing_times')}
                value={new Date(item.approveRQDate).toLocaleDateString()}
            />
            <View style={styles.vwButtonAction}>
                <View style={{ flex: 0.5, justifyContent: 'center' }}>
                    <MyText
                        style={{
                            fontWeight: '500'
                        }}
                        text={translate('sendbank.list_attachments') + ':'}
                    />
                </View>
                <TouchableOpacity
                    style={styles.txButtonAction}
                    onPress={() => {
                        handledAttach(item.approveRequestId);
                    }}>
                    <MyText style={styles.txSeeMore} text={'Xem thêm'} />
                    <Icon
                        iconSet="Feather"
                        size={14}
                        color={COLORS.bg57a7ff}
                        name={'eye'}
                    />
                </TouchableOpacity>
            </View>
            <View
                style={{
                    width: '100%',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                }}>
                <Pressable
                    style={styles.btnActionFile}
                    onPress={() => {
                        setIsShowApprove(true);
                        // handledAttach(item.approveRequestId);
                    }}>
                    <Icon
                        iconSet="MaterialCommunityIcons"
                        size={16}
                        color={COLORS.bg57a7ff}
                        name={'account-edit-outline'}
                    />
                    <MyText style={styles.txFile} text={'Yêu cầu điều chỉnh'} />
                </Pressable>
                <Pressable
                    style={styles.btnActionFile}
                    onPress={() => {
                        // handledAttach(item.approveRequestId);
                    }}>
                    <Icon
                        iconSet="MaterialCommunityIcons"
                        size={16}
                        color={COLORS.ic00C300}
                        name={'account-check'}
                    />
                    <MyText
                        style={[styles.txFile, { color: COLORS.txt05CC47 }]}
                        text={'Duyệt yêu cầu'}
                    />
                </Pressable>
                <Pressable
                    style={styles.btnActionFile}
                    onPress={() => {
                        // handledAttach(item.approveRequestId);
                    }}>
                    <Icon
                        iconSet="MaterialCommunityIcons"
                        size={16}
                        color={COLORS.bgFF0000}
                        name={'delete'}
                    />
                    <MyText
                        style={[styles.txFile, { color: COLORS.txtFC3158 }]}
                        text={'Huỷ yêu cầu duyệt'}
                    />
                </Pressable>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    vwInput: {
        marginTop: 10,
        height: 'auto',
        alignItems: 'center'
    },
    txInput: {
        borderRadius: 8,
        borderColor: COLORS.bdCCCCCC,
        marginBottom: 5,
        paddingHorizontal: 10,
        paddingVertical: 8,
        backgroundColor: COLORS.bgFFFFFF
    },
    vwListItem: {
        flex: 1
    },
    vwItem: {
        backgroundColor: COLORS.bgFFFFFF,
        padding: 8,
        borderRadius: 8,
        marginVertical: 8,
        marginHorizontal: 10
    },
    btSearch: {
        width: constants.width / 3,
        height: 36,
        borderRadius: 18,
        backgroundColor: COLORS.bg288AD6,
        justifyContent: 'center',
        alignItems: 'center',
        alignSelf: 'center',
        marginVertical: 10
    },
    txButtonSearch: { color: COLORS.txtFFFFFF, fontWeight: '800' },
    txText: {
        flex: 0.5,
        fontWeight: '500'
    },
    btUserBrowser: {
        flex: 0.5,
        backgroundColor: COLORS.ic288AD6,
        borderRadius: 10,
        padding: 8
    },
    vwButtonAction: {
        flexDirection: 'row',
        backgroundColor: COLORS.bgFFFFFF,
        width: 'auto',
        borderRadius: 8,
        marginVertical: 8
    },
    txButtonAction: {
        flex: 0.5,
        borderRadius: 10,
        alignItems: 'center',
        flexDirection: 'row'
    },
    txSeeMore: {
        fontSize: 13,
        fontWeight: '600',
        color: COLORS.txt0088F2,
        marginRight: 8
    },
    btnActionFile: {
        borderRadius: 10,
        alignItems: 'center',
        flexDirection: 'row'
    },
    txFile: {
        fontSize: 12,
        fontWeight: '600',
        color: COLORS.txt0088F2,
        marginLeft: 3
    }
});

export default ItemSendbank;
