import React, { useState } from 'react';
import {
    FlatList,
    StyleSheet,
    TouchableOpacity,
    View,
} from 'react-native';
import { BaseLoading, MyText, Icon, SearchInput } from '@components';
import KModal from 'react-native-modal';
import { COLORS } from '@styles';
import { connect } from 'react-redux';
import { constants } from '@constants';
import { bindActionCreators } from 'redux';
import * as sendBankActionCreator from '../action';

const ModalSearchStore = ({
    title,
    onStoreChange,
    sendBankAction,
    dataSearch,
    stateSearchStore
}) => {
    const [modalVisible, setModalVisible] = useState(false);
    const [keyword, setKeyword] = useState('');
    
    //handleSearch
    const handleSearch = (text) => {
        sendBankAction.getStoreList(text);
        setKeyword(text);
    };

    return (
        <View>
            <TouchableOpacity
                style={styles.vwSearch}
                onPress={() => {
                    setModalVisible(!modalVisible);
                }}>
                <View style={styles.btSearch}>
                    <MyText
                        style={{
                            color: COLORS.txt147EFB
                        }}
                        text={title}
                    />
                </View>
                <View style={styles.icSearch}>
                    <Icon
                        iconSet={'Ionicons'}
                        name={'caret-down-sharp'}
                        color={COLORS.ic147EFB}
                        size={14}
                    />
                </View>
            </TouchableOpacity>
            <KModal
                style={styles.vwKmodal}
                animationIn="slideInUp"
                animationOut="slideOutDown"
                useNativeDriver={true}
                hideModalContentWhileAnimating={true}
                backdropTransitionOutTiming={0}
                visible={modalVisible}
                onRequestClose={() => setModalVisible(modalVisible)}>
                <View style={styles.vwSearchItem}>
                    <View style={styles.lnModal}></View>
                    <View style={styles.btHeaderBack}>
                        <MyText
                            text={'Tìm kiếm siêu thị'}
                            style={styles.txHeader}
                        />
                        <TouchableOpacity
                            style={{ padding: 10 }}
                            activeOpacity={1}
                            onPress={() => setModalVisible(!modalVisible)}>
                            <Icon
                                style={styles.icHeader}
                                iconSet={'Ionicons'}
                                name={'close'}
                                color={COLORS.ic147EFB}
                                size={24}
                            />
                            {/* <MyText style={styles.txCancel} text={'B'} /> */}
                        </TouchableOpacity>
                    </View>
                    <SearchInput
                        style={styles.vwSearchModal}
                        width={constants.width - constants.getSize(20)}
                        height={40}
                        placeholder={title}
                        value={keyword}
                        onChangeText={(text) => {
                            handleSearch(text);
                        }}
                        rightComponent={[
                            { source: { uri: 'ic_search' }, styles }
                        ]}
                        returnKeyType="search"
                    />
                    <BaseLoading
                        isLoading={stateSearchStore.isFetching}
                        isError={stateSearchStore.isError}
                        isEmpty={stateSearchStore.isEmpty}
                        textLoadingError={stateSearchStore.description}
                        onPressTryAgains={() => handleSearch(keyword)}
                        content={
                            <FlatList
                                data={dataSearch}
                                keyExtractor={(index) => index}
                                renderItem={({ item }) => (
                                    <TouchableOpacity
                                        style={styles.ltStore}
                                        onPress={() => {
                                            const storeName =
                                                item.storeID +
                                                '-' +
                                                item.storeName;
                                            onStoreChange(item);
                                            setModalVisible(!modalVisible);
                                        }}>
                                        <Icon
                                            style={styles.icStore}
                                            size={constants.getSize(18)}
                                            iconSet={'FontAwesome'}
                                            name={'store-alt'}
                                            color={COLORS.ic2FB47C}
                                        />
                                        <MyText
                                            style={styles.txItem}
                                            text={
                                                item.storeID +
                                                ' - ' +
                                                item.storeName
                                            }
                                        />
                                    </TouchableOpacity>
                                )}
                                keyboardShouldPersistTaps={'handled'}
                                showsVerticalScrollIndicator={false}
                                removeClippedSubviews={true}
                            />
                        }
                    />
                </View>
            </KModal>
        </View>
    );
};

const mapStateToProps = function (state) {
    return {
        dataSearch: state.sendBankReducer.dataSearch,
        stateSearchStore: state.sendBankReducer.stateSearchStore
    };
};

const mapDispatchToProps = function (dispacth) {
    return {
        sendBankAction: bindActionCreators(sendBankActionCreator, dispacth)
    };
};

const styles = StyleSheet.create({
    vwKmodal: {
        margin: 0,
        padding: 0,
        borderRadius: 8,
        paddingHorizontal: 0,
        justifyContent: 'flex-end',
        backgroundColor: COLORS.bg0000005
    },
    vwSearchItem: {
        width: 'auto',
        height: '85%',
        backgroundColor: COLORS.bgFFFFFF,
        borderRadius: 12
    },
    vwSearch: {
        flexDirection: 'row',
        backgroundColor: COLORS.bgFFFFFF,
        width: 'auto',
        height: 40,
        borderRadius: 8,
        marginVertical: 8,
        marginHorizontal: 10
    },
    btSearch: {
        flex: 0.95,
        marginLeft: 17,
        justifyContent: 'center'
    },
    icSearch: {
        flex: 0.05,
        justifyContent: 'center',
        alignItems: 'flex-end',
        marginRight: 22
    },
    btHeaderBack: {
        width: 'auto',
        height: 40,
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row'
    },
    txHeader: {
        textAlign: 'center',
        textAlignVertical: 'center',
        color: COLORS.txt147EFB,
        fontSize: 16,
        fontWeight: 'bold',
        marginLeft: 28,
        width: '80%'
    },
    icHeader: {
        marginTop: -20,
        marginRight: -16
    },
    txCancel: {
        color: COLORS.txt7affcc,
        fontWeight: '700'
        // marginRight:
    },
    lnModal: {
        marginTop: 6,
        backgroundColor: COLORS.bgA7A7A7,
        width: constants.width / 7,
        height: 6,
        borderRadius: 10,
        justifyContent: 'center',
        alignSelf: 'center'
    },
    vwSearchModal: {
        marginVertical: 10,
        marginHorizontal: 20,
        paddingHorizontal: 10,
        backgroundColor: COLORS.bgFFFFFF,
        borderRadius: 16,
        height: 40,
        width: 'auto',
        borderWidth: 1,
        borderColor: COLORS.bgA7A7A7
    },
    ltStore: {
        width: '90%',
        alignSelf: 'center',
        flexDirection: 'row',
        paddingVertical: 10,
        borderBottomWidth: 1,
        borderColor: COLORS.bgA7A7A7,
        alignItems: 'center'
    },
    txItem: {
        marginLeft: 8
    },
    icStore: {
        width: constants.getSize(30),
        height: '100%',
        alignItems: 'center',
        alignContent: 'center',
        justifyContent: 'center',
        paddingLeft: constants.getSize(4)
    }
});

export default connect(mapStateToProps, mapDispatchToProps)(ModalSearchStore);
