import React, { useEffect, useState } from 'react';
import { Alert, StyleSheet, TouchableOpacity, View } from 'react-native';
import { MyText, Icon, Picker } from '@components';
import KModal from 'react-native-modal';
import { COLORS } from '@styles';
import { constants } from '@constants';
import { translate } from '@translate';
import InputMoney  from './InputMoney';

function ModalApproveReason({
    title,
    isVisible,
    hideModal,
    onSubmit,
    onCancel,
    totalId,
    listBank
}) {
    const [modalVisible, setModalVisible] = useState(isVisible);
    const [bankName, setBankName] = useState('');
    const [bankId, setBankId] = useState('');
    const [bankAccount, setBankAccount] = useState('');
    const [totalMoney, setTotalMoney] = useState('');

    const handleSubmit = () => {
        if (bankName === '' && totalMoney.toString() === '') {
            return Alert.alert(
                translate('common.notification'),
                translate('sendbank.please_select_information')
            );
        } else if (totalMoney.toString() === '') {
            return Alert.alert(
                translate('common.notification'),
                translate('sendbank.please_enter_total_money')
            );
        } else if (bankName === '') {
            return Alert.alert(
                translate('common.notification'),
                translate('sendbank.please_select_bank')
            );
        } else if (totalMoney.toString() !== totalId.toString()) {
            return Alert.alert(
                translate('common.notification'),
                translate('sendbank.total_money_not_match')
            );
        } else {
            onSubmit(bankId, bankAccount, totalMoney);
        }
    };

    return (
        <View style={styles.modal}>
            <KModal
                style={styles.vwKmodal}
                animationIn="slideInUp"
                animationOut="slideOutDown"
                useNativeDriver={true}
                deviceWidth={constants.width}
                deviceHeight={constants.height}
                backdropTransitionOutTiming={0}
                hideModalContentWhileAnimating={true}
                visible={modalVisible}
                onRequestClose={hideModal}>
                <View style={styles.vwTitle}>
                    <TouchableOpacity
                        style={styles.btnClose}
                        onPress={hideModal}>
                        <Icon
                            iconSet="MaterialCommunityIcons"
                            size={20}
                            color={COLORS.bg147EFB}
                            name={'close'}
                        />
                    </TouchableOpacity>
                    <MyText style={styles.txTitle} text={title} />
                    <View>
                        <MyText
                            text={'Chọn ngân hàng nộp tiền:'}
                            style={{
                                fontSize: 12,
                                fontWeight: 'bold'
                            }}
                        />
                        <Picker
                            style={styles.vwPickerSearch}
                            label="bankName"
                            value="bankName"
                            defaultLabel={translate('sendbank.select_bank')}
                            valueSelected={bankName}
                            data={listBank}
                            onChange={(item) => {
                                setBankName(item.bankName);
                                setBankAccount(item.bankAccount);
                                setBankId(item.bankId);
                            }}
                        />
                    </View>
                    <InputMoney
                        title={translate('sendbank.total_amount_submit') + ':'}
                        value={totalMoney}
                        onChange={(value) => {
                            setTotalMoney(value);
                        }}
                        isActiveNumbericIOS={true}
                    />

                    <View style={styles.vwBtnAction}>
                        <TouchableOpacity
                            style={[
                                styles.btAction,
                                { backgroundColor: COLORS.bgE9EFF3 }
                            ]}
                            onPress={onCancel}>
                            <MyText
                                style={styles.txAction}
                                text={translate('sendbank.btn_back')}
                            />
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={[
                                styles.btAction,
                                { backgroundColor: COLORS.bg00AAFF }
                            ]}
                            onPress={() => handleSubmit()}>
                            <MyText
                                style={[
                                    styles.txAction,
                                    { color: COLORS.txtFFFFFF }
                                ]}
                                text={translate('sendbank.btn_submit')}
                            />
                        </TouchableOpacity>
                    </View>
                </View>
            </KModal>
        </View>
    );
}

const styles = StyleSheet.create({
    modal: {
        width: '100%',
        height: '100%',
        backgroundColor: COLORS.bg0000003,
        position: 'absolute',
        top: 0,
        left: 0
    },
    vwKmodal: {
        // justifyContent: 'center',
        top: 0,
        left: 0
    },
    btnClose: {
        padding: 8,
        height: 40,
        alignSelf: 'flex-end'
    },
    vwTitle: {
        width: '100%',
        height: 'auto',
        backgroundColor: COLORS.bgFFFFFF,
        borderRadius: 12,
        alignItems: 'center'
    },
    txTitle: {
        textAlign: 'center',
        fontSize: 15,
        color: COLORS.bg147EFB,
        fontWeight: 'bold',
        marginBottom: 10,
        marginTop: -10
    },
    txInput: {
        borderRadius: 8,
        borderColor: COLORS.bd7A7A7A,
        marginBottom: 5,
        paddingHorizontal: 10,
        paddingVertical: 8,
        backgroundColor: COLORS.bgFFFFFF,
        borderWidth: 0.5
    },
    vwBtnAction: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 10,
        padding: 20
    },
    btAction: {
        width: 160,
        height: 40,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 8,
        marginHorizontal: 10
    },
    txAction: {
        fontSize: 12,
        fontWeight: 'bold'
    },
    vwPickerSearch: {
        flexDirection: 'row',
        height: 38,
        width: constants.width - 60,
        backgroundColor: COLORS.btnFFFFFF,
        paddingHorizontal: 10,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center'
    }
});

export default ModalApproveReason;
