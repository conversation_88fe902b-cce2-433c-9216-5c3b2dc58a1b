import React from 'react';
import { MyText } from '@components';
import { StyleSheet, View } from 'react-native';

const TextField = ({ label, value, styleValue, styleView }) => {
    return (
        <View style={[styles.container, styleView]}>
            <MyText style={styles.txtext} text={label + ':'} />
            <MyText style={[styles.txtext, styleValue]} text={value} />
        </View>
    );
};
const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        paddingVertical: 5
    },
    txtext: {
        width: '50%',
        fontWeight: '500'
    }
});

export default TextField;
