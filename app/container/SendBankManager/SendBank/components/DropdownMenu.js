import { Icon, MyText } from '@components';
import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { Menu, MenuItem } from 'react-native-material-menu';
import { COLORS } from '@styles';

const DropdownMenu = ({ menu = [], onPressItem }) => {
    const [visible, setVisible] = React.useState(false);

    return (
        <Menu
            visible={visible}
            animationDuration={visible ? 300 : 30}
            anchor={
                <TouchableOpacity
                    onPress={() => setVisible((currentState) => !currentState)}>
                    <Icon
                        iconSet="Ionicons"
                        name="ellipsis-vertical"
                        size={26}
                        color={COLORS.btn008848}
                    />
                </TouchableOpacity>
            }
            onRequestClose={() => setVisible(false)}>
            {menu.length > 0 &&
                menu.map((item) => {
                    const { title, isCode, iconSet, iconName } = item;
                    return (
                        <MenuItem
                            key={isCode}
                            textStyle={{ color: '#141414' }}
                            onPress={() => {
                                setVisible(false);
                                onPressItem(title, isCode);
                            }}>
                            <View
                                style={{
                                    alignItems: 'center',
                                    flexDirection: 'row',
                                    paddingLeft: 8,
                                    paddingTop: 8
                                }}>
                                <Icon
                                    iconSet={iconSet}
                                    name={iconName}
                                    size={24}
                                    style={{ marginRight: 4 }}
                                    color={item.color}
                                />
                                <MyText
                                    style={{ marginLeft: 4 }}
                                    text={title}
                                />
                            </View>
                        </MenuItem>
                    );
                })}
        </Menu>
    );
};

export default DropdownMenu;
