import React, { useEffect, useState } from 'react';
import { FlatList, SafeAreaView, StyleSheet, View } from 'react-native';
import { helper, dateHelper } from '@common';
import { translate } from '@translate';
import { COLORS } from '@styles';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import TextField from './components/TextField';
import { constants } from '@constants';
import * as sendBankActionCreator from '../action';

const DetailSendBank = ({ navigation, dataPaymentDetails, route }) => {
    const dataItem = route.params;

    const dataDetail = dataPaymentDetails || [];

    const renderItem = (item, index) => {
        return (
            <View style={styles.header}>
                {/* ============== UI mới ============== */}
                <TextField
                    label={translate('sendbank.payment_voucher_code')}
                    value={item.sendBankId}
                />
                <TextField
                    label={translate(
                        'sendbank.date_of_creation_of_payment_slip'
                    )}
                    value={`${dateHelper.formatDateFULL(
                        new Date(item.createdDate)
                    )}`}
                />
                <TextField
                    label={translate('sendbank.cashier_staff')}
                    value={`${item.sendUser} - ${item.sendUserName}`}
                />
                <TextField
                    label={translate('sendbank.amount_paid')}
                    value={helper.convertNum(item.sendMoney)}
                />
                <TextField
                    label={translate('sendbank.voucher_code')}
                    value={item.inOutVoucherId}
                />
                <TextField
                    label={translate('sendbank.voucher_creation_date')}
                    value={
                        item.inputTime == null
                            ? ''
                            : `${dateHelper.formatDateFULL(
                                  new Date(item.inputTime)
                              )}`
                    }
                />
            </View>
        );
    };

    return (
        <SafeAreaView style={styles.container}>
            <FlatList
                style={styles.container}
                data={dataDetail}
                keyExtractor={(index) => index}
                renderItem={({ item, index }) => renderItem(item)}
            />
        </SafeAreaView>
    );
};

const mapStateToProps = function (state) {
    return {
        dataPaymentDetails: state.sendBankReducer.dataPaymentDetails
    };
};

const mapDispatchToProps = function (dispacth) {
    return {
        sendBankAction: bindActionCreators(sendBankActionCreator, dispacth)
    };
};

const styles = StyleSheet.create({
    container: {
        flex: 1
    },
    header: {
        width: 'auto',
        height: 'auto',
        borderWidth: 0.2,
        borderRadius: 12,
        margin: 5,
        backgroundColor: COLORS.bgFFFFFF,
        paddingHorizontal: 15,
        paddingVertical: 8
    },
    title: {
        fontSize: constants.getSize(16),
        fontWeight: '700',
        color: COLORS.txt0099E5
    },
    vwtTextField: {
        padding: 5,
        backgroundColor: 'pink'
    }
});

export default connect(mapStateToProps, mapDispatchToProps)(DetailSendBank);
