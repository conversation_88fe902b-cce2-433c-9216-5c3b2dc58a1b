import React, { useCallback, useEffect, useState } from 'react';
import {
    SafeAreaView,
    StyleSheet,
    TouchableOpacity,
    View,
    KeyboardAvoidingView,
    Alert,
    Keyboard,
    TextInput
} from 'react-native';
import { dateHelper, helper } from '@common';
import { translate } from '@translate';
import { MyText, Loader, hideBlockUI, showBlockUI } from '@components';
import { COLORS } from '@styles';
import { connect } from 'react-redux';
import { constants } from '@constants';
import { bindActionCreators } from 'redux';
import * as sendBankActionCreator from '../action';
import TextField from './components/TextField';
import { useFocusEffect } from '@react-navigation/native';

const EditSendBank = ({
    navigation,
    sendBankAction,
    dataSearchUser,
    stateApproveUser,
    route
}) => {
    const [userName, setUserName] = useState('');
    const [data, setData] = useState(route.params);
    const [keyword, setKeyword] = useState('');
    const [isError, setIsError] = useState(null);
    const [approvalStatus, setApprovalStatus] = useState('');
    const dataSearch = dataSearchUser[0];
    const statusMapping = {
        NEW: 'Khởi tạo',
        NEEDHANDLE: 'Chờ xử lý',
        APPROVED: 'Đã duyệt',
        CANCEL: 'Đã huỷ'
    };

    useFocusEffect(
        useCallback(() => {
            sendBankAction.resetSearchUser();
        }, [sendBankAction])
    );

    useEffect(() => {
        if (dataSearch) {
            setUserName(dataSearch?.fullname);
            setIsError(false);
        } else if (dataSearchUser.length === 0) {
            setIsError(true);
            setUserName('');
        }
        if (statusMapping[data.status]) {
            setApprovalStatus(statusMapping[data.status]);
        }
    }, [dataSearchUser]);

    onPressSearchUser = async (keyword) => {
        showBlockUI();
        await sendBankAction
            .searchUser(keyword)
            .then((response) => {
                hideBlockUI();
            })
            .catch((error) => {
                hideBlockUI();
                Alert.alert('Thông báo', error.msgError, [
                    {
                        text: 'OK',
                        onPress: () => {
                            hideBlockUI();
                        }
                    }
                ]);
            });
    };

    onPressSubmit = async () => {
        let userId = dataSearch?.username;
        Keyboard.dismiss();
        try {
            if (
                keyword.toString() !== data.approveUserId.toString() &&
                userName !== ''
            ) {
                showBlockUI();
                await sendBankAction
                    .getChangeApproveUserSendbank(data, userId, userName)
                    .then((res) => {
                        Alert.alert('Thông báo', res, [
                            {
                                text: 'OK',
                                onPress: () => {
                                    hideBlockUI();
                                    sendBankAction.resetSearchUser();
                                    sendBankAction.getListSendBank();
                                    navigation.navigate('SendBankStask');
                                }
                            }
                        ]);
                    });
            } else if (userName == '') {
                hideBlockUI();
                Alert.alert('Thông báo', 'Vui lòng nhập nhân viên thay đổi', [
                    {
                        text: 'OK',
                        onPress: () => {
                            hideBlockUI();
                        }
                    }
                ]);
            } else {
                Alert.alert(
                    'Thông báo',
                    'Nhân viên duyệt mới phải khác với nhân viên duyệt trên',
                    [
                        {
                            text: 'OK',
                            onPress: () => {
                                hideBlockUI();
                            }
                        }
                    ]
                );
            }
        } catch (error) {
            Alert.alert('Thông báo', error.msgError, [
                {
                    text: 'OK',
                    onPress: () => {
                        sendBankAction.resetSearchUser();
                        hideBlockUI();
                        setKeyword('');
                        // navigation.navigate('SendBankStask');
                    }
                }
            ]);
        }
    };

    return (
        <SafeAreaView style={styles.container}>
            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                keyboardVerticalOffset={Platform.OS === 'ios' ? 96 : 0}
                style={styles.container}
                contentContainerStyle={{ flex: 1 }}>
                <View style={styles.header}>
                    <TextField
                        label={translate('sendbank.staff_approved')}
                        value={data.approveUserName}
                        styleValue={{ color: COLORS.txt778899 }}
                    />
                    <MyText
                        style={styles.txtHeader}
                        text={translate(
                            'sendbank.change_employee_approval_information'
                        )}
                    />
                    <View style={styles.vwInput}>
                        <View>
                            <TextInput
                                style={styles.txInput}
                                width={constants.width / 2 - 60}
                                height={constants.getSize(36)}
                                onChangeText={(text) => setKeyword(text)}
                                returnKeyType="done"
                                blurOnSubmit
                                value={keyword}
                                placeholder={translate(
                                    'sendbank.employee_code_changed'
                                )}
                                clearText={() => {
                                    setKeyword('');
                                }}
                                keyboardType="number"
                                maxLength={12}
                                onSubmitEditing={() => {
                                    onPressSearchUser(keyword);
                                }}
                            />
                        </View>
                        <View
                            style={[
                                styles.inSearch,
                                {
                                    width: constants.width / 2,
                                    height: constants.getSize(36)
                                }
                            ]}>
                            <MyText style={styles.txtSearch} text={userName} />
                        </View>
                    </View>
                    <View
                        style={{
                            flexDirection: 'row',
                            alignSelf: 'center',
                            marginTop: 10
                        }}>
                        <TouchableOpacity
                            style={styles.btn_action}
                            onPress={() => this.onPressSubmit()}>
                            <MyText
                                style={styles.txButtonSearch}
                                text={translate('sendbank.btn_submit')}
                            />
                        </TouchableOpacity>
                    </View>
                </View>
            </KeyboardAvoidingView>
            <Loader isLoading={stateApproveUser.isFetching} />
        </SafeAreaView>
    );
};

const mapStateToProps = function (state) {
    return {
        dataSearchUser: state.sendBankReducer.dataSearchUser,
        stateApproveUser: state.sendBankReducer.stateApproveUser
    };
};

const mapDispatchToProps = function (dispacth) {
    1;
    return {
        sendBankAction: bindActionCreators(sendBankActionCreator, dispacth)
    };
};

const styles = StyleSheet.create({
    container: {
        flex: 1
    },
    header: {
        width: 'auto',
        height: 'auto',
        borderWidth: 0.2,
        borderRadius: 12,
        margin: 5,
        backgroundColor: COLORS.bgFFFFFF,
        paddingHorizontal: 15,
        paddingVertical: 8
    },
    txtHeader: {
        fontSize: 13,
        fontWeight: '500',
        color: COLORS.txt000000
    },
    vwInput: {
        marginTop: 6,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between'
    },
    txInput: {
        alignItems: 'flex-start',
        padding: 10,
        borderRadius: constants.getSize(8),
        borderWidth: 0.5,
        borderColor: COLORS.bdCCCCCC,
        backgroundColor: COLORS.bgFFFFFF
    },
    inSearch: {
        justifyContent: 'center',
        backgroundColor: COLORS.bgFFFFFF,
        borderRadius: constants.getSize(8),
        borderWidth: 0.2,
        borderColor: COLORS.bd333333
    },
    txtSearch: {
        marginLeft: 10,
        fontSize: 14,
        fontWeight: '600',
        color: COLORS.txt008848
    },
    btn_action: {
        width: constants.width / 3,
        padding: 10,
        height: 36,
        borderRadius: 18,
        backgroundColor: COLORS.bg147EFB,
        justifyContent: 'center',
        alignItems: 'center',
        alignSelf: 'center',
        margin: 8
    },
    txMonney: {
        color: COLORS.txtFE7013
    },
    txButtonSearch: {
        color: COLORS.txtFFFFFF,
        fontWeight: '700'
    },
    txtError: {
        textAlign: 'right',
        fontSize: 12,
        color: COLORS.txtFF0000
    }
});

export default connect(mapStateToProps, mapDispatchToProps)(EditSendBank);
