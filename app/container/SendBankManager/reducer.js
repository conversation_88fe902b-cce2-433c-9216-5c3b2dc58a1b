import { sendBankState } from './state';
import { sendBankAction } from './action';
const sendBankReducer = (state = sendBankState, action) => {
    switch (action.type) {
        case sendBankAction.START_SEARCH_LIST_SENDBANK:
            return {
                ...state,
                dataSearchList: [],
                stateSearchList: {
                    isFetching: true,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.STOP_SEARCH_LIST_SENDBANK:
            return {
                ...state,
                dataSearchList: action.dataSearchList,
                stateSearchList: {
                    isFetching: false,
                    isEmpty: action.isEmpty,
                    description: action.description,
                    isError: action.isError
                }
            };
        case sendBankAction.RESET_SEARCH_LIST_SENDBANK:
            return {
                ...state,
                dataSearchList: [],
                stateSearchList: {
                    isFetching: false,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.START_CHANGE_STORE:
            return {
                ...state,
                dataSearch: [],
                stateSearchStore: {
                    isFetching: true,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.STOP_CHANGE_STORE:
            return {
                ...state,
                dataSearch: action.dataSearch,
                stateSearchStore: {
                    isFetching: false,
                    isEmpty: action.isEmpty,
                    description: action.description,
                    isError: action.isError
                }
            };
        case sendBankAction.RESET_CHANGE_STORE:
            return {
                ...state,
                dataSearch: [],
                stateSearchStore: {
                    isFetching: false,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.START_SEARCH_USER:
            return {
                ...state,
                dataSearchUser: [],
                stateSearchUser: {
                    isFetching: true,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.STOP_SEARCH_USER:
            return {
                ...state,
                dataSearchUser: action.dataSearchUser,
                stateSearchUser: {
                    isFetching: false,
                    isEmpty: action.isEmpty,
                    description: action.description,
                    isError: action.isError
                }
            };
        case sendBankAction.RESET_SEARCH_USER:
            return {
                ...state,
                dataSearchUser: [],
                stateSearchStore: {
                    isFetching: false,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.START_CHANGE_APPROVE_USER:
            return {
                ...state,
                dataApproveUser: [],
                stateApproveUser: {
                    isFetching: true,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.STOP_CHANGE_APPROVE_USER:
            return {
                ...state,
                dataApproveUser: action.dataApproveUser,
                stateApproveUser: {
                    isFetching: false,
                    isEmpty: action.isEmpty,
                    description: action.description,
                    isError: action.isError
                }
            };
        case sendBankAction.START_GET_CANCEL_REASON:
            return {
                ...state,
                dataCancelReason: [],
                stateCancelReason: {
                    isFetching: true,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.STOP_GET_CANCEL_REASON:
            return {
                ...state,
                dataCancelReason: action.dataCancelReason,
                stateCancelReason: {
                    isFetching: false,
                    isEmpty: action.isEmpty,
                    description: action.description,
                    isError: action.isError
                }
            };
        case sendBankAction.START_GET_APPROVAL_STARTS:
            return {
                ...state,
                dataApprovalStatus: [],
                stateCancelReason: {
                    isFetching: true,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.STOP_GET_APPROVAL_STARTS:
            return {
                ...state,
                dataApprovalStatus: action.dataApprovalStatus,
                stateCancelReason: {
                    isFetching: false,
                    isEmpty: action.isEmpty,
                    description: action.description,
                    isError: action.isError
                }
            };
        case sendBankAction.START_GET_PAYMENT_DETAILS:
            return {
                ...state,
                dataPaymentDetails: [],
                statePaymentDetails: {
                    isFetching: true,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.STOP_GET_PAYMENT_DETAILS:
            return {
                ...state,
                dataPaymentDetails: action.dataPaymentDetails,
                statePaymentDetails: {
                    isFetching: false,
                    isEmpty: action.isEmpty,
                    description: action.description,
                    isError: action.isError
                }
            };
        case sendBankAction.START_GET_BANKS: {
            return {
                ...state,
                dataBankList: [],
                stateBanks: {
                    isFetching: true,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        }
        case sendBankAction.STOP_GET_BANKS: {
            return {
                ...state,
                dataBankList: action.dataBankList,
                stateBanks: {
                    isFetching: false,
                    isEmpty: action.isEmpty,
                    description: action.description,
                    isError: action.isError
                }
            };
        }
        // ========== deposit =========
        case sendBankAction.START_SEARCH_LIST_DEPOSITRECEIPT:
            return {
                ...state,
                dataSearchListDeposit: [],
                stateSearchListDeposit: {
                    isFetching: true,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.STOP_SEARCH_LIST_DEPOSITRECEIPT:
            return {
                ...state,
                dataSearchListDeposit: action.dataSearchListDeposit,
                stateSearchListDeposit: {
                    isFetching: false,
                    isEmpty: action.isEmpty,
                    description: action.description,
                    isError: action.isError
                }
            };
        case sendBankAction.START_GET_BANK_CREARTE_FORM:
            return {
                ...state,
                dataBankCrearteForm: [],
                stateBankCrearteForm: {
                    isFetching: true,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.STOP_GET_BANK_CREARTE_FORM:
            return {
                ...state,
                dataBankCrearteForm: action.dataBankCrearteForm,
                stateBankCrearteForm: {
                    isFetching: false,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.START_GET_BANK_SEARCH_FORM:
            return {
                ...state,
                dataBankSearchForm: [],
                stateBankSearchForm: {
                    isFetching: true,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.STOP_GET_BANK_SEARCH_FORM:
            return {
                ...state,
                dataBankSearchForm: action.payload,
                stateBankSearchForm: {
                    isFetching: false,
                    isEmpty: action.payload.length === 0,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.START_DAILY_FUND:
            return {
                ...state,
                dataDailyFund: [],
                stateDailyFund: {
                    isFetching: true,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.STOP_DAILY_FUND:
            return {
                ...state,
                dataDailyFund: action.dataDailyFund,
                stateDailyFund: {
                    isFetching: false,
                    isEmpty: action.isEmpty,
                    description: action.description,
                    isError: action.isError
                }
            };
        case sendBankAction.START_CREATE_SEND_BANK: {
            return {
                ...state,
                dataCreateSendBank: {},
                stateCreateSendBank: {
                    isFetching: true,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        }
        case sendBankAction.STOP_CREATE_SEND_BANK: {
            return {
                ...state,
                dataCreateSendBank: action.dataCreateSendBank,
                stateCreateSendBank: {
                    isFetching: false,
                    isEmpty: false,
                    description: action.description,
                    isError: action.isError
                }
            };
        }
        case sendBankAction.START_CREATE_SEND_BANK_APPROVE: {
            return {
                ...state,
                dataSendBankApprove: {},
                stateCreateSendBankApprove: {
                    isFetching: true,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        }
        case sendBankAction.STOP_CREATE_SEND_BANK_APPROVE: {
            return {
                ...state,
                dataSendBankApprove: action.dataSendBankApprove,
                stateCreateSendBankApprove: {
                    isFetching: false,
                    isEmpty: false,
                    description: action.description,
                    isError: action.isError
                }
            };
        }
        case sendBankAction.START_GET_DETAIL_SEND_BANK: {
            return {
                ...state,
                dataDetailSendBank: {},
                stateDetailSendBank: {
                    isFetching: true,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        }
        case sendBankAction.STOP_GET_DETAIL_SEND_BANK:
            return {
                ...state,
                dataDetailSendBank: action.dataDetailSendBank,
                stateDetailSendBank: {
                    isFetching: false,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        case sendBankAction.START_GET_REPORT_PRINTER:
            return {
                ...state,
                statePrinter: {
                    isFetching: true,
                    isError: false,
                    description: '',
                    isEmpty: false
                }
            };
        case sendBankAction.STOP_GET_REPORT_PRINTER:
            return {
                ...state,
                printerRetail: action.printerRetail,
                printerVAT: action.printerVAT,
                printerCommon: action.printerCommon,
                statePrinter: {
                    isFetching: false,
                    isEmpty: false,
                    description: '',
                    isError: false
                }
            };
        default:
            return state;
    }
};

export { sendBankReducer };