import React, { Component } from "react";
import {
  StyleSheet,
  View,
  TextInput,
  TouchableOpacity,
  FlatList,
  SafeAreaView,
  Alert,
} from "react-native";
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import {
  helper,
  storageHelper,
  convertHtml2Image,
  convertToBase64,
  printSocket,
} from "@common";
import { translate } from "@translate";
import { constants } from "@constants";
import {
  MyText,
  Icon,
  BaseLoading,
  showBlockUI,
  hideBlockUI,
  Button,
  ViewHTML,
} from "@components";
import PrintReport from "../../SaleOrderPayment/component/PrintReport";
import Report from "../../SaleOrderPayment/component/PrintReport/Report";
import * as actionSaleOrderCreator from "../../SaleOrderPayment/action";
import * as actionManagerSOCreator from "../../SaleOrderManager/action";
import * as actionCollectionManagerCreator from "../../CollectionTransferManager/action";
import PaymentTransferSheet from "../../SaleOrderPayment/Sheet/PaymentTransferSheet";
import PaymentBankTransfer from "../../AnKhangNew/components/PaymentBankTransfer";
import { COLORS } from "@styles";
import { BottomSheetModalProvider } from "@gorhom/bottom-sheet";
import { AlertMessage } from "../../AnKhangNew/components";
const { H_BILL, H_VOUCHER, H_BANK, H_KEY, PRINT_CONFIG } = constants;

class PrintCoupon extends Component {
  constructor(props) {
    super(props);
    this.state = {
      reportRetail: {},
      reportVAT: {},
      reportCommon: {},
      reportInfo: [],
      isVisible: false,
      base64PDF: "",
      totalRemain: 0,
      isShow: false,
      dataBankInfo: [],
      bankSelected: {},
      statusTransferPaymentSuccess: {
        type: "INIT",
        message: "GIAO DỊCH ĐANG ĐỢI XỬ LÝ...",
      },
      transactionIcon: "",
      transactionColor: "",
      isVisibleCollectionTransaction: false,
      contentCollectionTransaction: "",
    };
    this.dataPrint = {};
    this.paymentTransferSheetRef = React.createRef(null);
    this.intervalPaymentId = React.createRef(-1);
  }

  componentDidUpdate(preProps, preState) {
    if (preProps.defaultReport !== this.props.defaultReport) {
      const { itemSelectedReport } = this.props;
      this.setState(
        {
          reportRetail: itemSelectedReport.retail,
          reportVAT: itemSelectedReport.vat,
          reportCommon: itemSelectedReport.common,
        },
        this.triggerCheckPrintContent
      );
    }

    if (preProps.reportContent !== this.props.reportContent) {
      const { reportContent } = this.props;
      if (helper.IsNonEmptyArray(reportContent)) {
        const reportInfo = reportContent.map((ele) => ({
          ReportContent: ele.REPORTCONTENT,
          NumberOfCopy: "1",
          ReportName: ele.REPORTNAME,
        }));
        this.setState({ reportInfo }, this.triggerCheckPrintContent);
      }
    }
  }

  triggerCheckPrintContent = () => {
    const { reportRetail, reportVAT, reportCommon, reportInfo } = this.state;
    const isRetail = !helper.IsEmptyObject(reportRetail);
    const isVat = !helper.IsEmptyObject(reportVAT);
    const isCommon = !helper.IsEmptyObject(reportCommon);
    const isReport = isRetail || isVat || isCommon;
    const reportContents = reportInfo.filter((ele) =>
      helper.IsNonEmptyString(ele.NumberOfCopy)
    );
    if (isReport && helper.IsNonEmptyArray(reportContents)) {
      this.onCheckPrintContent();
    }
  };

  onCheckPrintContent = () => {
    const { reportInfo } = this.state;
    const { dataSO } = this.props;
    const reportContents = reportInfo.filter((ele) =>
      helper.IsNonEmptyString(ele.NumberOfCopy)
    );
    const data = {
      saleOrderID: dataSO.SaleOrderID,
      reportContents: reportContents,
    };
    const isReceipt = reportContents.some(
      (ele) =>
        ele?.ReportContent == "OutputReceiptContent" ||
        ele?.ReportContent == "KeySoftwareContent"
    );
    data.isFitContent = true;
    data.isGetContentHTML = true;
    if (isReceipt) {
      this.getReceiptContentHtml(data);
    } else {
      this.getContentHtml(data);
    }
    this.dataPrint = data;
  };

  getReportPrinter = () => {
    const {
      dataSO: { SaleOrderTypeID },
      actionSaleOrder,
    } = this.props;
    actionSaleOrder.getReportPrinterSocket(SaleOrderTypeID);
  };

  handleGetSaleOrder = () => {
    showBlockUI();
    const {
      dataSO: { SaleOrderID },
      actionSaleOrder,
    } = this.props;
    actionSaleOrder
      .getSaleOrderPayment(SaleOrderID)
      .then((reponse) => {
        hideBlockUI();
        const { TotalRemain } = reponse;
        this.setState({ totalRemain: TotalRemain, isShow: true });
      })
      .catch(({ msgError }) => {
        Alert.alert(translate("common.notification_uppercase"), msgError, [
          {
            text: translate("common.btn_skip"),
            style: "cancel",
            onPress: hideBlockUI,
          },
          {
            text: translate("common.btn_notify_try_again"),
            style: "default",
            onPress: this.handleGetSaleOrder,
          },
        ]);
      });
  };

  handleBankTransfer = (amount) => {
    showBlockUI();
    const {
      dataSO: { SaleOrderID },
      actionSaleOrder,
    } = this.props;
    actionSaleOrder
      .getBankInfo({
        saleOrderID: SaleOrderID,
        paymentAmount: amount,
      })
      .then((result) => {
        hideBlockUI();
        this.setState(
          {
            dataBankInfo: result,
            bankSelected: result[0],
            statusTransferPaymentSuccess: {
              type: "INIT",
              message: "GIAO DỊCH ĐANG ĐỢI XỬ LÝ...",
            },
          },
          () => {
            this.paymentTransferSheetRef.current?.present();
            if (this.state.bankSelected?.QRCodeData?.length > 0) {
              this.handleIntervalPayment();
            }
          }
        );
      })
      .catch((error) => {
        Alert.alert(
          translate("common.notification_uppercase"),
          error.msgError,
          [
            {
              text: translate("saleExpress.retry"),
              style: "cancel",
              onPress: () => this.handleBankTransfer(amount),
            },
            {
              text: translate("common.btn_skip"),
              style: "cancel",
              onPress: () => hideBlockUI(),
            },
          ]
        );
      });
  };

  handleChangeBank = (bank) => {
    clearInterval(this.intervalPaymentId.current);
    this.setState({ bankSelected: bank }, () => {
      this.handleIntervalPayment();
    });
  };
  handleIntervalPayment = () => {
    if (!helper.configIntervalPayment()) return;
    this.intervalPaymentId.current = setInterval(
      this.intervalPaymentFunction,
      5000
    );
    setTimeout(() => {
      clearInterval(this.intervalPaymentId.current);
    }, 3000000);
  };
  intervalPaymentFunction = async () => {
    const {
      dataSO: { SaleOrderID },
    } = this.props;
    const {
      bankSelected: { CreatedDate },
    } = this.state;

    actionSaleOrderCreator
      .getTransactionTransfer(SaleOrderID, CreatedDate)
      .then(() => {
        this.setState({
          statusTransferPaymentSuccess: {
            type: "SUCCESS",
            message: "GIAO DỊCH ĐÃ THỰC HIỆN THÀNH CÔNG",
          },
        });
        clearInterval(this.intervalPaymentId.current);
      })
      .catch((msgError) => {
        this.setState({
          statusTransferPaymentSuccess: {
            type: "ERROR",
            message: msgError,
          },
        });
        clearInterval(this.intervalPaymentId.current);
      });
  };

  render() {
    const {
      reportRetail,
      reportVAT,
      reportCommon,
      reportInfo,
      isVisible,
      base64PDF,
      transactionIcon,
      transactionColor,
      isVisibleCollectionTransaction,
      contentCollectionTransaction,
    } = this.state;
    const {
      printerRetail,
      printerVAT,
      printerCommon,
      statePrinter,
      stateReportContent,
      dataSO: { SaleOrderID },
      route,
      dataQueryStatus,
    } = this.props;
    const eleBank = reportInfo.find(
      (ele) => ele.ReportContent == "BankAccountContent"
    );
    const saleOrderID = dataQueryStatus?.SaleOrderID ?? "";

    return (
      <SafeAreaView
        style={{
          flex: 1,
          backgroundColor: COLORS.bgFAFAFA,
        }}
      >
        <BottomSheetModalProvider>
          <BaseLoading
            isLoading={stateReportContent.isFetching}
            isEmpty={stateReportContent.isEmpty}
            textLoadingError={stateReportContent.description}
            isError={stateReportContent.isError}
            onPressTryAgains={() => { }}
            content={
              <View
                style={{
                  width: constants.width,
                }}
              >
                <FlatList
                  data={reportInfo}
                  renderItem={({ item, index }) => (
                    <TypeContent
                      info={item}
                      onchangeNumber={(info) => {
                        reportInfo[index] = info;
                        this.setState({ reportInfo });
                      }}
                    />
                  )}
                  keyExtractor={(item, index) => `${index}`}
                  ListFooterComponent={
                    <>
                      {!helper.IsEmptyObject(eleBank) && (
                        <>
                          {this.state.isShow ? (
                            <PaymentBankTransfer
                              saleOrderID={SaleOrderID}
                              payableAmount={this.state.totalRemain}
                              colorPrimary={COLORS.bdFF8900}
                              colorSecondary={COLORS.txtEA1D5D}
                              colorTertiary={COLORS.txtEA1D5D}
                              defaultShow
                              isShowFunctionReloadSO={false}
                              handleAPIBankTransfer={this.handleBankTransfer}
                            />
                          ) : (
                            <View
                              style={{
                                flexDirection: "row",
                                alignItems: "center",
                                marginLeft: 10,
                                paddingBottom: 10,
                              }}
                            >
                              <TouchableOpacity
                                style={{
                                  flexDirection: "row",
                                  alignItems: "center",
                                  justifyContent: "center",
                                }}
                                onPress={this.handleGetSaleOrder}
                              >
                                <MyText
                                  text={translate(
                                    "saleOrderManager.view_transfer_information"
                                  )}
                                  style={{
                                    color: COLORS.txtEA1D5D,
                                    fontWeight: "bold",
                                  }}
                                />
                                <Icon
                                  iconSet="Ionicons"
                                  name={"chevron-down"}
                                  size={22}
                                  color={COLORS.txtEA1D5D}
                                />
                              </TouchableOpacity>
                            </View>
                          )}
                        </>
                      )}
                      <PrintReport
                        title={translate("saleOrderPayment.choose_printer")}
                        statePrinter={statePrinter}
                        onTryAgains={this.getReportPrinter}
                        dataRetail={printerRetail}
                        dataVAT={printerVAT}
                        dataCommon={printerCommon}
                        renderItemRetail={({ item, index }) => (
                          <Report
                            key={`ReportRetail`}
                            info={item}
                            report={reportRetail}
                            onCheck={() => {
                              storageHelper.setDefaultPrinter(
                                item.STOREPRINTERID,
                                0
                              );
                              this.setState({ reportRetail: item });
                            }}
                          />
                        )}
                        renderItemVAT={({ item, index }) => (
                          <Report
                            key={`ReportVAT`}
                            info={item}
                            report={reportVAT}
                            onCheck={() => {
                              storageHelper.setDefaultPrinter(
                                item.STOREPRINTERID,
                                1
                              );
                              this.setState({ reportVAT: item });
                            }}
                          />
                        )}
                        renderItemCommon={({ item, index }) => (
                          <Report
                            key={`ReportCommon`}
                            info={item}
                            report={reportCommon}
                            onCheck={() => {
                              storageHelper.setDefaultPrinter(
                                item.STOREPRINTERID,
                                2
                              );
                              this.setState({ reportCommon: item });
                            }}
                          />
                        )}
                      />
                      <ButtonCreate
                        onPress={this.triggerCheckPrintContent}
                        disabled={stateReportContent.isFetching}
                      />
                    </>
                  }
                />
              </View>
            }
          />

          <ViewHTML
            isVisible={isVisible}
            source={base64PDF}
            hideModal={() => {
              this.setState({ isVisible: false });
            }}
            title={"Thông Tin Chuyển Khoản"}
          />
          <PaymentTransferSheet
            paymentTransferSheetRef={this.paymentTransferSheetRef}
            bankList={this.state.dataBankInfo}
            bankSelected={this.state.bankSelected}
            saleOrderID={SaleOrderID}
            onChangeBank={this.handleChangeBank}
            handleIntervalPayment={this.handleIntervalPayment}
            onChangeStatusSheet={(position) => {
              if (position == -1) {
                clearInterval(this.intervalPaymentId.current);
              }
            }}
            statusTransferPaymentSuccess={
              this.state.statusTransferPaymentSuccess
            }
          />
          <AlertMessage
            title="Chi tiết giao dịch"
            visible={this.state.isVisibleCollectionTransaction}
            onClose={() =>
              this.setState({
                isVisibleCollectionTransaction: false,
                contentCollectionTransaction: "",
              })
            }
            message={""}
            isMaxHeight={true}
            buttons={[
              {
                key: 0,
                text: "Về quản lý giao dịch",
                outline: true,
                onPress: () => {
                  const data = {
                    keyword: saleOrderID,
                    fromDate: new Date(),
                    toDate: new Date(),
                    isDelete: false,
                    isCreate: true,
                  };
                  this.props.actionCollectionManager.getSearchCollectionManager(
                    data
                  );
                  this.props.navigation.navigate("HistorySell", {
                    SaleOrderID: saleOrderID,
                  });
                  this.setState({
                    isVisibleCollectionTransaction: false,
                    contentCollectionTransaction: "",
                  });
                },
              },
              {
                key: 1,
                text: "Tiếp tục thanh toán",
                outline: false,
                onPress: () => {
                  this.props.navigation.navigate("PayBillAirtimeService");
                  this.setState({
                    isVisibleCollectionTransaction: false,
                    contentCollectionTransaction: "",
                  });
                },
              },
              {
                key: 2,
                text: "Quét mã QrCode",
                outline: true,
                onPress: () => {
                  const item = {
                    ServiceCatalogID: 4,
                    isVisibleScanQrCode: true
                  };
                  this.setState(
                    {
                      isVisibleCollectionTransaction: false,
                      contentCollectionTransaction: ""
                    },
                    () => {
                      setTimeout(() => {
                        this.props.navigation.navigate("CatalogCollection", {
                          item
                        });
                      }, 300); 
                    }
                  );
                }
                
              },
            ]}
            style={{ flexDirection: "column" }}
          >
            <View
              style={{
                alignItems: "center",
                flex: 1,
                flexDirection: "row",
                justifyContent: "space-between",
                marginVertical: 4,
              }}
            >
              <View
                style={{
                  flex: 1,
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Icon
                  name={transactionIcon}
                  iconSet="MaterialCommunityIcons"
                  color={transactionColor}
                  size={55}
                />
                <MyText
                  style={{ fontSize: 18, fontWeight: "700", paddingTop: 10 }}
                  text={this.state.contentCollectionTransaction}
                />
              </View>
            </View>
          </AlertMessage>
        </BottomSheetModalProvider>
      </SafeAreaView>
    );
  }

  onCheckPrintContent = () => {
    const { reportRetail, reportVAT, reportCommon, reportInfo } = this.state;
    const { dataSO } = this.props;
    const isRetail = !helper.IsEmptyObject(reportRetail);
    const isVat = !helper.IsEmptyObject(reportVAT);
    const isCommon = !helper.IsEmptyObject(reportCommon);
    const isReport = isRetail || isVat || isCommon;
    const reportContents = reportInfo.filter((ele) =>
      helper.IsNonEmptyString(ele.NumberOfCopy)
    );
    if (!isReport) {
      Alert.alert("", translate("saleOrderPayment.please_choose_printer"));
    } else if (!helper.IsNonEmptyArray(reportContents)) {
      Alert.alert("", translate("saleOrderManager.please_choose_printer"));
    } else {
      const data = {
        saleOrderID: dataSO.SaleOrderID,
        reportContents: reportContents,
      };
      const isReceipt = reportContents.some(
        (ele) =>
          ele?.ReportContent == "OutputReceiptContent" ||
          ele?.ReportContent == "KeySoftwareContent"
      );
      data.isFitContent = true;
      data.isGetContentHTML = true;
      if (isReceipt) {
        this.getReceiptContentHtml(data);
      } else {
        this.getContentHtml(data);
      }
      this.dataPrint = data;
    }
  };

  getContentBase64PDF = (info) => {
    showBlockUI();
    this.props.actionSaleOrder
      .getReportContentBase64(info)
      .then((data) => {
        this.onPrintBillPDF(data, info.reportContents);
      })
      .catch((msgError) => {
        Alert.alert(translate("common.notification_uppercase"), msgError, [
          {
            text: translate("saleOrderPayment.btn_skip_uppercase"),
            style: "default",
            onPress: hideBlockUI,
          },
          {
            text: translate("saleOrderPayment.btn_retry_uppercase"),
            style: "default",
            onPress: () => this.getContentBase64PDF(info),
          },
        ]);
      });
  };

  onPrintBillPDF = (data, reportContents) => {
    const requestAPI = this.getPrintPDFRequestAPI(data, reportContents);
    if (helper.IsNonEmptyArray(requestAPI)) {
      this.printAllRequest(requestAPI);
    } else {
      hideBlockUI();
    }
  };

  getPrintPDFRequestAPI = (data, reportContents) => {
    const {
      dataSO: { SaleOrderID },
    } = this.props;
    const requestAPI = [];
    const {
      EBillContent,
      eBillContentPrinterTypeID,

      GiftVCIssueContentPrint,
      giftVCIssueContentPrinterTypeID,

      EBillContentIncome,
      eBillContentIncomePrinterTypeID,

      OutTransContent,
      outTransContentPrinterTypeID,

      BankAccountContent,
      bankAccountContentPrinterTypeID,

      PrepareProductsForSaleContent,
      prepareProductsForSaleContentPrinterTypeID,

      DosageContent,
      dosageContentPrinterTypeID,

      infoBatchNOContent,
      infoBatchNOContentPrinterTypeID,

      OutputReceiptContent,
      outputReceiptContentPrinterTypeID,

      AirtimeTransferContent,
      airtimeTransferContentPrinterTypeID,
    } = data;
    reportContents.forEach((ele) => {
      const { ReportContent, NumberOfCopy } = ele;
      switch (ReportContent) {
        case "EBillContent":
          if (helper.IsNonEmptyString(EBillContent)) {
            const report = this.getReport(eBillContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
              for (let i = 0; i < NumberOfCopy; i++) {
                const printService = this.getPrintServicePDF(
                  report,
                  EBillContent
                );
                requestAPI.push(printService);
              }
            }
          }
          break;
        case "EBillContentIncome":
          if (helper.IsNonEmptyString(EBillContentIncome)) {
            const report = this.getReport(eBillContentIncomePrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
              for (let i = 0; i < NumberOfCopy; i++) {
                const printService = this.getPrintServicePDF(
                  report,
                  EBillContentIncome
                );
                requestAPI.push(printService);
              }
            }
          }
          break;
        case "GiftVCIssueContentPrint":
          if (helper.IsNonEmptyString(GiftVCIssueContentPrint)) {
            const report = this.getReport(giftVCIssueContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
              for (let i = 0; i < NumberOfCopy; i++) {
                const printService = this.getPrintServicePDF(
                  report,
                  GiftVCIssueContentPrint
                );
                requestAPI.push(printService);
              }
              actionSaleOrderCreator.insertLogPrintGift(SaleOrderID);
            }
          }
          break;
        case "KeySoftwareContent":
          if (helper.IsNonEmptyString(OutTransContent)) {
            const report = this.getReport(outTransContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
              const printService = this.getPrintServicePDF(
                report,
                OutTransContent
              );
              requestAPI.push(printService);
            }
          }
          break;
        case "BankAccountContent":
          if (helper.IsNonEmptyString(BankAccountContent)) {
            const report = this.getReport(bankAccountContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
              for (let i = 0; i < NumberOfCopy; i++) {
                const printService = this.getPrintServicePDF(
                  report,
                  BankAccountContent
                );
                requestAPI.push(printService);
              }
            }
          }
          break;
        case "PrepareProductsForSaleContent":
          if (helper.IsNonEmptyString(PrepareProductsForSaleContent)) {
            const report = this.getReport(
              prepareProductsForSaleContentPrinterTypeID
            );
            if (!helper.IsEmptyObject(report)) {
              for (let i = 0; i < NumberOfCopy; i++) {
                const printService = this.getPrintServicePDF(
                  report,
                  PrepareProductsForSaleContent
                );
                requestAPI.push(printService);
              }
            }
          }
          break;
        case "DosageContent":
          if (helper.IsNonEmptyString(DosageContent)) {
            const report = this.getReport(dosageContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
              for (let i = 0; i < NumberOfCopy; i++) {
                const printService = this.getPrintServicePDF(
                  report,
                  DosageContent
                );
                requestAPI.push(printService);
              }
            }
          }
          break;
        case "InfoBatchNOContent":
          if (helper.IsNonEmptyString(infoBatchNOContent)) {
            const report = this.getReport(infoBatchNOContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
              for (let i = 0; i < NumberOfCopy; i++) {
                const printService = this.getPrintServicePDF(
                  report,
                  infoBatchNOContent
                );
                requestAPI.push(printService);
              }
            }
          }
          break;
        case "OutputReceiptContent":
          if (helper.IsNonEmptyString(OutputReceiptContent)) {
            const report = this.getReport(outputReceiptContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
              for (let i = 0; i < NumberOfCopy; i++) {
                const printService = this.getPrintServicePDF(
                  report,
                  OutputReceiptContent
                );
                requestAPI.push(printService);
              }
            }
          }
          break;
        case "AirtimeTransferContent":
          if (helper.IsNonEmptyString(AirtimeTransferContent)) {
            const report = this.getReport(airtimeTransferContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
              for (let i = 0; i < NumberOfCopy; i++) {
                const printService = this.getPrintServicePDF(
                  report,
                  AirtimeTransferContent
                );
                requestAPI.push(printService);
              }
            }
          }
          break;
        default:
          console.log(ele);
          break;
      }
    });
    return requestAPI;
  };

  getPrintServicePDF = (report, content) => {
    let printerConfig = {
      strPrinterName: report.PRINTERNAME,
      strPaperSize: report.PAPERSIZE,
      paperwidth: report.PAPERWIDTH,
      parperheight: report.PARPERHEIGHT,
      intCopyCount: 1,
      bolIsDuplex: false,
      bolShrinkToMargin: false,
      strBase64: content,
    };
    if (report.REPORTID == 2820) {
      printerConfig.strPaperSize = "A4 210 x 297 mm";
    }
    let formBody = [];
    for (const property in printerConfig) {
      const encodedKey = encodeURIComponent(property);
      const encodedValue = encodeURIComponent(printerConfig[property]);
      formBody.push(encodedKey + "=" + encodedValue);
    }
    formBody = formBody.join("&");
    return new Promise((resolve, reject) => {
      actionSaleOrderCreator
        .printBillVoucher(formBody)
        .then((result) => {
          resolve(result);
        })
        .catch((msgError) => {
          reject(msgError);
        });
    });
  };

  printAllRequest = (allPromise) => {
    Promise.all(allPromise)
      .then((result) => {
        console.log("PRINT RSULT", result);
        Alert.alert("", translate("saleOrderPayment.print_successfully"), [
          {
            text: "OK",
            style: "default",
            onPress: hideBlockUI,
          },
        ]);
      })
      .catch((msgError) => {
        Alert.alert(translate("common.notification"), msgError, [
          {
            text: "OK",
            style: "default",
            onPress: hideBlockUI,
          },
        ]);
      });
  };

  getReport = (type) => {
    const { reportRetail, reportVAT, reportCommon } = this.state;
    switch (type) {
      case "InvoiceRetailPrinter":
        return reportRetail;
      case "VATContentPrint":
        return reportVAT;
      default:
        // CommonPrinter
        return reportCommon;
    }
  };

  getContentBase64View = () => {
    const { base64PDF } = this.state;
    if (helper.IsNonEmptyString(base64PDF)) {
      this.setState({ isVisible: true });
    } else {
      showBlockUI();
      const { actionManagerSO, dataSO } = this.props;
      actionManagerSO
        .getContentBase64View({
          reportContent: "BankAccountContent",
          saleOrderID: dataSO.SaleOrderID,
        })
        .then((base64) => {
          this.setState({
            isVisible: true,
            base64PDF: base64,
          });
          hideBlockUI();
        })
        .catch((msgError) => {
          Alert.alert(translate("common.notification"), msgError, [
            {
              text: "OK",
              onPress: hideBlockUI,
            },
          ]);
        });
    }
  };

  /*  */
  printAllRequestFW = async (allPromise) => {
    try {
      for (const body of allPromise) {
        await actionSaleOrderCreator.printBillVoucherBit(body);
        await helper.sleep(1000);
      }
      Alert.alert("", translate("saleOrderPayment.print_successfully"), [
        {
          text: "OK",
          style: "default",
          onPress: hideBlockUI,
        },
      ]);
    } catch (msgError) {
      Alert.alert(translate("common.notification"), msgError, [
        {
          text: "OK",
          style: "default",
          onPress: hideBlockUI,
        },
      ]);
    }
  };

  getTransactionStatusConfig = (status) => {
    const defaultDescription =
      "Giao dịch đang được xử lý. Theo dõi kết quả ở mục 'Quản lý GD ngành hàng multicat'. Vẫn thu đủ tiền KH và không thu lại giao dịch mới trước khi GD này có kết quả cuối";
    const config = {
      PENDING: {
        icon: "sync",
        color: "green",
        message:
          this.props.dataQueryStatus?.cus_AirtimeStatusCacheBO?.Description ||
          defaultDescription,
      },
      SUCCESS: {
        icon: "check-circle-outline",
        color: "#83B4FF",
        message:
          this.props.dataQueryStatus?.cus_AirtimeStatusCacheBO?.Description ||
          defaultDescription,
      },
      FAIL: {
        icon: "close-circle-outline",
        color: "#F95454",
        message:
          this.props.dataQueryStatus?.cus_AirtimeStatusCacheBO?.Description ||
          defaultDescription,
      },
    };

    return config[status] || config.PENDING;
  };

  printAllRequestSocket = async (allPromise) => {
    const { dataQueryStatus } = this.props;
    const transactionStatus =
      dataQueryStatus?.cus_AirtimeStatusCacheBO?.Status ?? null;
    const { icon, color, message } =
      this.getTransactionStatusConfig(transactionStatus);
    try {
      for (const { data, ip, delay } of allPromise) {
        await printSocket(data, ip);
        if (delay > 0) {
          await helper.sleep(delay);
        }
      }
      Alert.alert("", translate("saleOrderPayment.print_successfully"), [
        {
          text: "OK",
          style: "default",
          onPress: () => {
            hideBlockUI();
            this.setState({
              isVisibleCollectionTransaction: true,
              contentCollectionTransaction: message,
              transactionIcon: icon,
              transactionColor: color,
            });
          },
        },
      ]);
    } catch (msgError) {
      Alert.alert(translate("common.notification"), msgError, [
        {
          text: "OK",
          style: "default",
          onPress: () => {
            hideBlockUI(),
              this.setState({
                isVisibleCollectionTransaction: true,
                contentCollectionTransaction: message,
                transactionIcon: icon,
                transactionColor: color,
              })
          },
        },
      ]);
    }
  };

  getContentHtml = (info) => {
    showBlockUI();
    this.props.actionSaleOrder
      .getReportContentBase64(info)
      .then((data) => {
        this.onConvertHTML(data, info.reportContents);
      })
      .catch((msgError) => {
        Alert.alert(translate("common.notification_uppercase"), msgError, [
          {
            text: translate("saleOrderPayment.btn_skip_uppercase"),
            style: "default",
            onPress: hideBlockUI,
          },
          {
            text: translate("saleOrderPayment.btn_retry_uppercase"),
            style: "default",
            onPress: () => this.getContentHtml(info),
          },
        ]);
      });
  };

  getReceiptContentHtml = (info) => {
    showBlockUI();
    this.props.actionSaleOrder
      .getReportContentBase64New(info)
      .then((data) => {
        this.onConvertHTML(data, info.reportContents);
      })
      .catch((msgError) => {
        Alert.alert(translate("common.notification_uppercase"), msgError, [
          {
            text: translate("saleOrderPayment.btn_skip_uppercase"),
            style: "default",
            onPress: hideBlockUI,
          },
          {
            text: translate("saleOrderPayment.btn_retry_uppercase"),
            style: "default",
            onPress: () => this.getReceiptContentHtml(info),
          },
        ]);
      });
  };

  requestConvertHtml = async (data, reportContents) => {
    try {
      const requestConvert = [];
      const {
        EBillContentHTML,
        GiftVCIssueContentPrintHTML,
        EBillContentIncomeHTML,
        OutTransContentHTML,
        BankAccountContentHTML,
        PrepareProductsForSaleContentHTML,
        DosageContentHTML,
        InfoBatchNOContentHTML,
        OutputReceiptContentHTML,
        AirtimeTransferContentHTML,
      } = data;
      for (const ele of reportContents) {
        const { ReportContent } = ele;
        switch (ReportContent) {
          case "EBillContent":
            if (helper.IsNonEmptyString(EBillContentHTML)) {
              const eBillContent = await convertHtml2Image(
                EBillContentHTML,
                H_BILL
              );
              requestConvert.push([eBillContent]);
            }
            break;
          case "EBillContentIncome":
            if (helper.IsNonEmptyString(EBillContentIncomeHTML)) {
              const eBillContentIncome = await convertHtml2Image(
                EBillContentIncomeHTML,
                H_BILL
              );
              requestConvert.push([eBillContentIncome]);
            }
            break;
          case "GiftVCIssueContentPrint":
            if (helper.IsNonEmptyString(GiftVCIssueContentPrintHTML)) {
              const giftData =
                GiftVCIssueContentPrintHTML.split(`<br /><br />`);
              const giftVCIssueContentPrint = [];
              for (const giftHtml of giftData) {
                const giftContent = await convertHtml2Image(
                  giftHtml,
                  H_VOUCHER
                );
                giftVCIssueContentPrint.push(giftContent);
              }
              requestConvert.push(giftVCIssueContentPrint);
            }
            break;
          case "KeySoftwareContent":
            if (helper.IsNonEmptyString(OutTransContentHTML)) {
              const keySoftwareContent = await convertHtml2Image(
                OutTransContentHTML,
                H_KEY
              );
              requestConvert.push([keySoftwareContent]);
            }
            break;
          case "BankAccountContent":
            if (helper.IsNonEmptyString(BankAccountContentHTML)) {
              const bankAccountContent = await convertHtml2Image(
                BankAccountContentHTML,
                H_BANK
              );
              requestConvert.push([bankAccountContent]);
            }
            break;
          case "PrepareProductsForSaleContent":
            if (helper.IsNonEmptyString(PrepareProductsForSaleContentHTML)) {
              const prepareContent = await convertHtml2Image(
                PrepareProductsForSaleContentHTML,
                H_BILL
              );
              requestConvert.push([prepareContent]);
            }
            break;
          case "DosageContent":
            if (helper.IsNonEmptyString(DosageContentHTML)) {
              const dosageContent = await convertHtml2Image(
                DosageContentHTML,
                H_VOUCHER
              );
              requestConvert.push([dosageContent]);
            }
            break;
          case "InfoBatchNOContent":
            if (helper.IsNonEmptyString(InfoBatchNOContentHTML)) {
              const batchContent = await convertHtml2Image(
                InfoBatchNOContentHTML,
                H_VOUCHER
              );
              requestConvert.push([batchContent]);
            }
            break;
          case "OutputReceiptContent":
            if (helper.IsNonEmptyString(OutputReceiptContentHTML)) {
              const receiptContent = await convertHtml2Image(
                OutputReceiptContentHTML,
                H_KEY
              );
              requestConvert.push([receiptContent]);
            }
            break;
          case "AirtimeTransferContent":
            if (helper.IsNonEmptyString(AirtimeTransferContentHTML)) {
              const airtimeTransferContent = await convertHtml2Image(
                AirtimeTransferContentHTML,
                H_BILL
              );
              requestConvert.push([airtimeTransferContent]);
            }
            break;
          default:
            console.log(ele);
            break;
        }
      }
      return requestConvert;
    } catch (error) {
      console.log("convertHtml2Image", error);
      Alert.alert(
        "",
        'Quá trình in lỗi. Vui lòng vào chức năng "In lại" để thao tác tiếp.',
        [
          {
            text: "OK",
            style: "default",
            onPress: hideBlockUI,
          },
        ]
      );
    }
  };

  onConvertHTML = async (data, reportContents) => {
    const dataBit = await this.requestConvertHtml(data, reportContents);
    if (helper.IsNonEmptyArray(dataBit)) {
      this.onPrintBillHTML(data, reportContents, dataBit);
    } else {
      hideBlockUI();
    }
  };

  getPrintServiceHTML = (report, info, type) => {
    const {
      userInfo: { userName },
    } = this.props;
    let body = {
      Printer: report.PRINTERSHORTNAME,
      Value: info,
      Type: type,
      User: userName,
      Status: "ReprintPayment",
    };
    return body;
  };

  getPrintServiceSocket = (report, info) => {
    let body = {
      ip: report.IPPRINTER,
      delay: report.DELAY,
      data: info,
    };
    return body;
  };

  getPrintHTMLRequestAPI = (data, reportContents, dataBit) => {
    const {
      dataSO: { SaleOrderID },
    } = this.props;
    const { reportRetail } = this.state;
    const requestAPI = [];
    const {
      eBillContentPrinterTypeID,
      giftVCIssueContentPrinterTypeID,
      eBillContentIncomePrinterTypeID,
      outTransContentPrinterTypeID,
      bankAccountContentPrinterTypeID,
      GiftVCIssueContentPrintHTML,
      dosageContentPrinterTypeID,
      infoBatchNOContentPrinterTypeID,
      outputReceiptContentPrinterTypeID,
      airtimeTransferContentPrinterTypeID,
    } = data;
    reportContents.forEach((ele, index) => {
      const { ReportContent, NumberOfCopy } = ele;
      const dataConvert = dataBit[index];
      let report = {};
      switch (ReportContent) {
        case "EBillContent":
          report = this.getReport(eBillContentPrinterTypeID);
          break;
        case "EBillContentIncome":
          report = this.getReport(eBillContentIncomePrinterTypeID);
          break;
        case "GiftVCIssueContentPrint":
          report = this.getReport(giftVCIssueContentPrinterTypeID);
          break;
        case "KeySoftwareContent":
          report = this.getReport(outTransContentPrinterTypeID);
          break;
        case "BankAccountContent":
          report = this.getReport(bankAccountContentPrinterTypeID);
          break;
        case "DosageContent":
          report = this.getReport(dosageContentPrinterTypeID);
          break;
        case "InfoBatchNOContent":
          report = this.getReport(infoBatchNOContentPrinterTypeID);
          break;
        case "OutputReceiptContent":
          report = this.getReport(outputReceiptContentPrinterTypeID);
          break;
        case "AirtimeTransferContent":
          report = this.getReport(airtimeTransferContentPrinterTypeID);
          break;
        default:
          report = reportRetail;
          break;
      }
      if (helper.IsNonEmptyArray(dataConvert)) {
        for (let i = 0; i < NumberOfCopy; i++) {
          dataConvert.forEach((info) => {
            if (!report.IPPRINTER) {
              report.IPPRINTER = "*************";
              report.DELAY = 500;
            }
            const printService = this.getPrintServiceSocket(
              report,
              info,
              ReportContent
            );
            requestAPI.push(printService);
          });
        }
        const isPMH = ReportContent == "GiftVCIssueContentPrint";
        if (isPMH && helper.IsNonEmptyString(GiftVCIssueContentPrintHTML)) {
          actionSaleOrderCreator.insertLogPrintGift(SaleOrderID);
        }
      }
    });
    return requestAPI;
  };

  onPrintBillHTML = (data, reportContents, dataBit) => {
    const requestAPI = this.getPrintHTMLRequestAPI(
      data,
      reportContents,
      dataBit
    );
    if (helper.IsNonEmptyArray(requestAPI)) {
      this.printAllRequestSocket(requestAPI);
    } else {
      hideBlockUI();
    }
  };

  /*  */
  printAllRequestBHX = async (allPromise) => {
    try {
      for (const body of allPromise) {
        await actionSaleOrderCreator.printBillVoucherBHX(body);
        await helper.sleep(1000);
      }
      Alert.alert("", translate("saleOrderPayment.print_successfully"), [
        {
          text: "OK",
          style: "default",
          onPress: hideBlockUI,
        },
      ]);
    } catch (msgError) {
      Alert.alert(translate("common.notification"), msgError, [
        {
          text: "OK",
          style: "default",
          onPress: hideBlockUI,
        },
      ]);
    }
  };

  getContentHtmlBHX = (info) => {
    showBlockUI();
    this.props.actionSaleOrder
      .getReportContentBase64(info)
      .then((data) => {
        this.onConvertHTMLBHX(data, info.reportContents);
      })
      .catch((msgError) => {
        Alert.alert(translate("common.notification_uppercase"), msgError, [
          {
            text: translate("saleOrderPayment.btn_skip_uppercase"),
            style: "default",
            onPress: hideBlockUI,
          },
          {
            text: translate("saleOrderPayment.btn_retry_uppercase"),
            style: "default",
            onPress: () => this.getContentHtmlBHX(info),
          },
        ]);
      });
  };

  requestConvertHtmlBHX = (data, reportContents) => {
    try {
      const requestConvert = [];
      const {
        EBillContentHTML,
        GiftVCIssueContentPrintHTML,
        EBillContentIncomeHTML,
        OutTransContentHTML,
        BankAccountContentHTML,
        PrepareProductsForSaleContentHTML,
        DosageContentHTML,
        InfoBatchNOContentHTML,
        OutputReceiptContentHTML,
        AirtimeTransferContentHTML,
      } = data;
      for (const ele of reportContents) {
        const { ReportContent } = ele;
        switch (ReportContent) {
          case "EBillContent":
            if (helper.IsNonEmptyString(EBillContentHTML)) {
              const eBillContent = convertToBase64(EBillContentHTML);
              requestConvert.push(eBillContent);
            }
            break;
          case "EBillContentIncome":
            if (helper.IsNonEmptyString(EBillContentIncomeHTML)) {
              const eBillContentIncome = convertToBase64(
                EBillContentIncomeHTML
              );
              requestConvert.push(eBillContentIncome);
            }
            break;
          case "GiftVCIssueContentPrint":
            if (helper.IsNonEmptyString(GiftVCIssueContentPrintHTML)) {
              const giftContent = convertToBase64(GiftVCIssueContentPrintHTML);
              requestConvert.push(giftContent);
            }
            break;
          case "KeySoftwareContent":
            if (helper.IsNonEmptyString(OutTransContentHTML)) {
              const keySoftwareContent = convertToBase64(OutTransContentHTML);
              requestConvert.push(keySoftwareContent);
            }
            break;
          case "BankAccountContent":
            if (helper.IsNonEmptyString(BankAccountContentHTML)) {
              const bankAccountContent = convertToBase64(
                BankAccountContentHTML
              );
              requestConvert.push(bankAccountContent);
            }
            break;
          case "PrepareProductsForSaleContent":
            if (helper.IsNonEmptyString(PrepareProductsForSaleContentHTML)) {
              const prepareContent = convertToBase64(
                PrepareProductsForSaleContentHTML
              );
              requestConvert.push(prepareContent);
            }
            break;
          case "DosageContent":
            if (helper.IsNonEmptyString(DosageContentHTML)) {
              const dosageContent = convertToBase64(DosageContentHTML);
              requestConvert.push(dosageContent);
            }
            break;
          case "InfoBatchNOContent":
            if (helper.IsNonEmptyString(InfoBatchNOContentHTML)) {
              const batchContent = convertToBase64(InfoBatchNOContentHTML);
              requestConvert.push(batchContent);
            }
            break;
          case "OutputReceiptContent":
            if (helper.IsNonEmptyString(OutputReceiptContentHTML)) {
              const receiptContent = convertToBase64(OutputReceiptContentHTML);
              requestConvert.push(receiptContent);
            }
            break;
          case "AirtimeTransferContent":
            if (helper.IsNonEmptyString(AirtimeTransferContentHTML)) {
              const airtimeTransferContent = convertToBase64(
                AirtimeTransferContentHTML
              );
              requestConvert.push(airtimeTransferContent);
            }
            break;
          default:
            console.log(ele);
            break;
        }
      }
      console.log("convertHtml2Image", requestConvert);
      return requestConvert;
    } catch (error) {
      console.log("convertHtml2Image", error);
      Alert.alert(
        "",
        'Quá trình in lỗi. Vui lòng vào chức năng "In lại" để thao tác tiếp.',
        [
          {
            text: "OK",
            style: "default",
            onPress: hideBlockUI,
          },
        ]
      );
    }
  };

  onConvertHTMLBHX = (data, reportContents) => {
    const dataContent = this.requestConvertHtmlBHX(data, reportContents);
    if (helper.IsNonEmptyArray(dataContent)) {
      this.onPrintBillHTMLBHX(data, reportContents, dataContent);
    } else {
      hideBlockUI();
    }
  };

  getPrintServiceHTMLBHX = (report, info, type) => {
    let body = {
      printerName: report.PRINTERNAME,
      docData: info,
    };
    return body;
  };

  getPrintHTMLRequestAPIBHX = (data, reportContents, dataBit) => {
    const { reportRetail } = this.state;
    const requestAPI = [];
    const {
      eBillContentPrinterTypeID,
      eBillContentIncomePrinterTypeID,
      giftVCIssueContentPrinterTypeID,
      outTransContentPrinterTypeID,
      bankAccountContentPrinterTypeID,
      dosageContentPrinterTypeID,
      infoBatchNOContentPrinterTypeID,
      outputReceiptContentPrinterTypeID,
      airtimeTransferContentPrinterTypeID,
    } = data;
    reportContents.forEach((ele, index) => {
      const { ReportContent } = ele;
      const dataConvert = dataBit[index];
      let report = {};
      switch (ReportContent) {
        case "EBillContent":
          report = this.getReport(eBillContentPrinterTypeID);
          break;
        case "EBillContentIncome":
          report = this.getReport(eBillContentIncomePrinterTypeID);
          break;
        case "GiftVCIssueContentPrint":
          report = this.getReport(giftVCIssueContentPrinterTypeID);
          break;
        case "KeySoftwareContent":
          report = this.getReport(outTransContentPrinterTypeID);
          break;
        case "BankAccountContent":
          report = this.getReport(bankAccountContentPrinterTypeID);
          break;
        case "DosageContent":
          report = this.getReport(dosageContentPrinterTypeID);
          break;
        case "InfoBatchNOContent":
          report = this.getReport(infoBatchNOContentPrinterTypeID);
          break;
        case "OutputReceiptContent":
          report = this.getReport(outputReceiptContentPrinterTypeID);
          break;
        case "AirtimeTransferContent":
          report = this.getReport(airtimeTransferContentPrinterTypeID);
          break;
        default:
          report = reportRetail;
          break;
      }
      if (helper.IsNonEmptyString(dataConvert)) {
        const printService = this.getPrintServiceHTMLBHX(
          report,
          dataConvert,
          ReportContent
        );
        requestAPI.push(printService);
      }
    });
    return requestAPI;
  };

  onPrintBillHTMLBHX = (data, reportContents, dataBit) => {
    const requestAPI = this.getPrintHTMLRequestAPIBHX(
      data,
      reportContents,
      dataBit
    );
    if (helper.IsNonEmptyArray(requestAPI)) {
      this.printAllRequestBHX(requestAPI);
    } else {
      hideBlockUI();
    }
  };

  switchPrintToPDF = () => {
    this.dataPrint.isFitContent = false;
    this.dataPrint.isGetContentHTML = false;
    this.getContentBase64PDF(this.dataPrint);
  };
}

const mapStateToProps = (state) => ({
  dataSO: state.saleOrderPaymentReducer.dataSO,
  printerRetail: state.saleOrderPaymentReducer.printerRetail,
  printerVAT: state.saleOrderPaymentReducer.printerVAT,
  printerCommon: state.saleOrderPaymentReducer.printerCommon,
  defaultReport: state.saleOrderPaymentReducer.defaultReport,
  itemSelectedReport: state.collectInstallmentReducer.itemSelectedReport,
  statePrinter: state.saleOrderPaymentReducer.statePrinter,
  reportContent: state.managerSOReducer.reportContent,
  stateReportContent: state.managerSOReducer.stateReportContent,
  userInfo: state.userReducer,
  dataQueryStatus: state.collectionManagerReducer.dataQueryStatus,
});

const mapDispatchToProps = (dispatch) => ({
  actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),
  actionManagerSO: bindActionCreators(actionManagerSOCreator, dispatch),
  actionCollectionManager: bindActionCreators(
    actionCollectionManagerCreator,
    dispatch
  ),
});

export default connect(mapStateToProps, mapDispatchToProps)(PrintCoupon);

const TypeContent = ({ info, onchangeNumber }) => {
  const { ReportName, NumberOfCopy } = info;
  return (
    <View
      style={{
        width: constants.width,
        padding: 10,
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <MyText
        text={ReportName}
        style={{
          color: COLORS.txt555555,
          width: constants.width - 150,
          fontWeight: "bold",
          paddingRight: 4,
        }}
      />
      <View
        style={{
          width: 130,
          justifyContent: "center",
          alignItems: "center",
          flexDirection: "row",
        }}
      >
        <MyText
          text={translate("saleOrderManager.number_paper")}
          style={{
            color: COLORS.txt333333,
            width: 46,
          }}
        />
        <View
          style={{
            width: 84,
            borderWidth: StyleSheet.hairlineWidth,
            borderColor: COLORS.bd848A8C,
            borderRadius: 4,
            height: 40,
            flexDirection: "row",
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: COLORS.bgFAFAFA,
            overflow: "hidden",
          }}
        >
          <TextInput
            value={`${NumberOfCopy}`}
            style={{
              height: 38,
              width: 48,
              textAlign: "center",
              paddingHorizontal: 8,
            }}
            placeholder={"0"}
            onChangeText={(text) => {
              const regExpNum = new RegExp(/^[1-9]{1,2}$/);
              const isValidate = regExpNum.test(text) || text == "";
              if (isValidate) {
                info.NumberOfCopy = text;
                onchangeNumber(info);
              }
            }}
            keyboardType={"numeric"}
            returnKeyType={"done"}
          />
          <TouchableOpacity
            style={{
              width: 36,
              height: 38,
              justifyContent: "center",
              alignItems: "center",
              backgroundColor: COLORS.bgF5F5F5,
            }}
            onPress={() => {
              info.NumberOfCopy = "";
              onchangeNumber(info);
            }}
          >
            <Icon
              iconSet={"Ionicons"}
              name={"close"}
              color={COLORS.ic848A8C}
              size={24}
            />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const ButtonCreate = ({ onPress, disabled }) => {
  return (
    <View
      style={{
        width: constants.width,
        alignItems: "center",
        justifyContent: "center",
        flexDirection: "row",
        paddingVertical: 10,
        opacity: disabled ? 0.5 : 1,
      }}
    >
      <Button
        text={translate("saleOrderManager.continue_printer")}
        styleContainer={{
          borderRadius: 4,
          backgroundColor: COLORS.bdFF8900,
          height: 44,
          width: 140,
          marginLeft: 10,
        }}
        styleText={{
          color: COLORS.txtFFFFFF,
          fontSize: 14,
          fontWeight: "bold",
        }}
        onPress={onPress}
        disabled={disabled}
      />
    </View>
  );
};
