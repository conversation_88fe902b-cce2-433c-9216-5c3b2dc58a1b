import React from 'react';
import { MyText } from '@components';
import { StyleSheet, View } from 'react-native';

const TextField = ({ label, value, styleValue, styleView }) => {
    return (
        <View style={[styles.container, styleView]}>
            <MyText
                style={[styles.txtext, { fontWeight: '300' }]}
                text={label + ':'}
            />
            <MyText style={[styles.txtext, styleValue]} text={value} />
        </View>
    );
};
const styles = StyleSheet.create({
    container: {
        flexDirection: 'row'
    },
    txtext: {
        width: '50%',
        fontWeight: '500'
    }
});

export default TextField;
