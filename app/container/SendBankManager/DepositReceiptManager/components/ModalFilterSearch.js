import React, { useState } from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { COLORS } from '@styles';
import KModal from 'react-native-modal';
import { constants } from '@constants';
import {
    MyText,
    Icon,
    Picker,
    <PERSON>Button,
    <PERSON>erSearch,
    Button
} from '@components';
import moment from 'moment';
import ModalCalendar from './ModalCalendar';

const ModalFilterSearch = ({
    isVisible,
    hideModal,
    dataListBank,
    onPressSearch
}) => {
    const dataList = dataListBank;
    const [modalVisible, setModalVisible] = useState(isVisible);
    const [fromDate, setFromDate] = useState(new Date());
    const [toDate, setToDate] = useState(new Date());
    const [isCalendarVisible, setIsCalendarVisible] = useState(false);
    const [selectedBank, setSelectedBank] = useState({});
    const [selectedSendBankType, setSelectedSendBankType] = useState({});
    const [isVoucher, setIsVoucher] = useState('');

    const listSendBankType = [
        {
            type: 1,
            name: 'Nộp tiền ngân hàng'
        },
        {
            type: 3,
            name: 'Nộp tiền nội bộ'
        }
    ];
    const [listStatus, setListStatus] = useState([
        { title: 'Chưa tạo', selected: false, codeCM: 0 },
        { title: 'Đã tạo', selected: false, codeCM: 1 }
    ]);

    const selectStatus = (index) => {
        const newListStatus = listStatus.map((item) => ({
            ...item,
            selected: false
        }));
        newListStatus[index].selected = true;
        setListStatus(newListStatus);
        setIsVoucher(index);
    };

    const selectDate = (date) => {
        setFromDate(new Date(date.startDate));
        setToDate(new Date(date.endDate));
    };

    const onPressSearcFilter = () => {
        let bankId = [selectedBank.bankId ? selectedBank.bankId : ''];
        let sendBankType = selectedSendBankType.type
            ? selectedSendBankType.type
            : '';
        const data = {
            fromDate,
            toDate,
            bankId,
            isVoucher,
            sendBankType
        };
        onPressSearch(data);
    };

    return (
        <KModal
            style={styles.vwKmodal}
            animationIn="slideInUp"
            animationOut="slideOutDown"
            useNativeDriver={true}
            deviceWidth={constants.width}
            deviceHeight={constants.height}
            backdropTransitionOutTiming={0}
            hideModalContentWhileAnimating={true}
            visible={modalVisible}
            onRequestClose={hideModal}
            onBackdropPress={hideModal}>
            <View style={styles.vwSearchItem}>
                <View style={styles.filterItem}>
                    <MyText text={'Ngày tạo: '} style={styles.header} />
                    <TouchableOpacity
                        style={styles.dateWrapper}
                        onPress={() => {
                            setIsCalendarVisible(true);
                        }}>
                        <MyText
                            text={`${moment(fromDate).format(
                                'DD/MM/YYYY'
                            )} - ${moment(toDate).format('DD/MM/YYYY')}`}
                        />
                        <Icon
                            iconSet="Feather"
                            name="calendar"
                            style={{
                                fontSize: 30,
                                color: COLORS.ic2C8BD7
                            }}
                        />
                    </TouchableOpacity>
                </View>

                <View style={styles.filterItem}>
                    <MyText
                        text={'Trạng thái phiếu chi: '}
                        style={styles.header}
                    />
                    <RadioButton
                        style={{
                            flex: 1,
                            flexDirection: 'row',
                            justifyContent: 'space-around'
                        }}
                        dataItems={listStatus}
                        selectItem={selectStatus}
                        mainComponent={(item) => {
                            return (
                                <MyText
                                    text={item.title}
                                    style={{
                                        color: item.selected
                                            ? COLORS.txtFF8900
                                            : COLORS.txt333333,
                                        marginLeft: 2,
                                        fontSize: 15
                                    }}
                                />
                            );
                        }}
                    />
                </View>

                <View style={styles.filterItem}>
                    <MyText text={'Loại nộp tiền: '} style={styles.header} />
                    <Picker
                        label="name"
                        value="type"
                        data={listSendBankType}
                        valueSelected={selectedSendBankType.type}
                        onChange={setSelectedSendBankType}
                        defaultLabel={'Chọn loại nộp tiền'}
                        style={styles.picker}
                    />
                </View>

                {selectedSendBankType.type === 1 && (
                    <View style={styles.filterItem}>
                        <MyText text={'Ngân hàng: '} style={styles.header} />
                        <PickerSearch
                            label="bankName"
                            value="bankId"
                            data={dataList}
                            valueSelected={selectedBank.bankId}
                            onChange={setSelectedBank}
                            defaultLabel={'Chọn ngân hàng'}
                            style={styles.picker}
                        />
                    </View>
                )}
                <View
                    style={{
                        flex: 1,
                        alignItems: 'flex-end',
                        backgroundColor: COLORS.bgF0F0F0,
                        marginTop: 10,
                        flexDirection: 'row',
                        justifyContent: 'space-around'
                    }}>
                    <Button
                        styleContainer={styles.btnFilter}
                        styleText={styles.btnFilterText}
                        onPress={onPressSearcFilter}
                        text={'Xác nhận'}
                    />
                </View>
            </View>
            <ModalCalendar
                isVisible={isCalendarVisible}
                startDate={fromDate}
                endDate={toDate}
                hideModal={() => {
                    setIsCalendarVisible(false);
                }}
                setDate={selectDate}
            />
        </KModal>
    );
};

export default ModalFilterSearch;

const styles = StyleSheet.create({
    vwKmodal: {
        margin: 0,
        padding: 0,
        borderRadius: 8,
        paddingHorizontal: 0,
        justifyContent: 'flex-end',
        backgroundColor: COLORS.bg0000005
    },
    txHeader: {
        alignSelf: 'center',
        marginBottom: 10,
        justifyContent: 'center',
        alignItems: 'center'
    },
    vwSearchItem: {
        width: 'auto',
        height: '40%',
        backgroundColor: COLORS.bgF0F0F0,
        borderTopStartRadius: 16,
        borderTopEndRadius: 16,
        paddingHorizontal: 15,
        paddingVertical: 10
    },
    filterItem: {
        width: '100%',
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 5,
        gap: 5
    },
    header: {
        fontWeight: 'bold',
        marginVertical: 10
    },
    dateWrapper: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        height: 45,
        paddingHorizontal: 10,
        borderRadius: 5,
        borderWidth: 1,
        borderColor: COLORS.bdDDDDDD,
        backgroundColor: COLORS.bgFFFFFF
    },
    picker: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        height: 45,
        borderRadius: 5,
        borderWidth: 1,
        borderColor: COLORS.bdE4E4E4,
        backgroundColor: COLORS.btnFFFFFF
    },
    btnFilter: {
        width: constants.width - 32,
        height: 46,
        backgroundColor: COLORS.bg2C8BD7,
        borderRadius: 8,
        padding: 10,
        marginHorizontal: 10,
        alignItems: 'center',
        marginBottom: 16
    },
    btnClose: {
        width: 120,
        height: 40,
        backgroundColor: COLORS.bg2C8BD7,
        borderRadius: 8,
        padding: 10,
        marginHorizontal: 10,
        alignItems: 'center',
        marginBottom: 10
    },
    btnFilterText: {
        color: COLORS.txtFFFFFF,
        fontSize: 16,
        fontWeight: 'bold'
    }
});
