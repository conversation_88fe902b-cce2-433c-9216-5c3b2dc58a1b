import React, { useEffect, useState } from 'react';
import {
    SafeAreaView,
    StyleSheet,
    View,
    FlatList,
    Keyboard,
    Alert
} from 'react-native';
import { COLORS } from '@styles';
import { constants } from '@constants';
import {
    ModalFilterSearch,
    ReceiptItem,
    SearchInputFilter
} from './components';
import { FloatingAction } from 'react-native-floating-action';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as sendBankActionCreator from '../action';
import { helper } from '@common';
import { BaseLoading, hideBlockUI, Loader, showBlockUI } from '@components';

const DepositReceipt = ({
    navigation,
    sendBankAction,
    dataSearchListDeposit,
    stateSearchListDeposit,
    dataBankSearchForm
}) => {
    const [userText, setUserText] = useState('');
    const [isShowFilter, setIsShowFilter] = useState(false);
    const [dataList, setDataList] = useState([]);
    const [data, setData] = useState([]);
    const [isCheckList, setIsCheckList] = useState(false);
    const [showInfo, setShowInfo] = useState(false);

    const onCheckList = (isCheck, index, item) => {
        setDataList((prevDataList) => {
            if (isCheck) {
                return [...prevDataList, item]; // Thêm item nếu isCheck = true
            } else {
                return prevDataList.filter((i) => i !== item); // Xóa item nếu isCheck = false
            }
        });
    };

    const menuItem = [
        {
            text: 'Tạo yêu cầu duyệt',
            icon: require('../../../../assets/ic_creater_ticket.png'),
            name: '1',
            tintColor: COLORS.bg2FB47C
        },
        {
            text: 'Xoá phiếu',
            icon: require('../../../../assets/ic_delete.png'),
            name: '2',
            tintColor: COLORS.bgEA1D5D
        },
        {
            text: 'In phiếu',
            icon: require('../../../../assets/ic_priter.png'),
            name: '3',
            tintColor: COLORS.bg288AD6
        }
    ];

    const onOpenReceipt = (data) => {
        // Xem/ Chỉnh sửa phiếu
        showBlockUI();
        sendBankAction
            .getDetailSendBank(data)
            .then((res) => {
                hideBlockUI();
                const param = {
                    dataListSendBank: res,
                    dataTicket: data
                };
                navigation.navigate('DepositReceiptDetail', param);
            })
            .catch((error) => {
                hideBlockUI();
                Alert.alert('Thông báo', error, [
                    {
                        text: 'Đóng',
                        // onPress: () => ,
                        style: 'default'
                    },
                    {
                        text: 'Thử lại',
                        onPress: () => onOpenReceipt(data),
                        style: 'default'
                    }
                ]);
            });
    };

    // const onCreateReceipt = () => {
    //     navigation.navigate('CreateDepositReceipt');
    // };

    const onCreateRequest = () => {
        if (dataList.length == 0) {
            Alert.alert(
                'Thông báo',
                'Vui lòng chọn phiếu nộp tiền cần duyệt',
                [{ text: 'Đóng', style: 'default' }],
                { cancelable: false }
            );
            return;
        } else {
            // navigation.navigate('CreateDepositRequest', dataList);
            const body = {
                lstSendBank: dataList
            };
            sendBankAction
                .validatePopupSendbank(body)
                .then((res) => {
                    navigation.navigate('CreateDepositRequest', dataList);
                })
                .catch((err) => {
                    Alert.alert(
                        'Thông báo',
                        err,
                        [
                            {
                                text: 'Đóng',
                                style: 'default'
                            }
                        ],
                        { cancelable: false }
                    );
                });
        }
    };

    const actionDelete = async (items) => {
        const body = {
            lstSendBank: items
        };
        await sendBankAction
            .onDeleteSendBank(body)
            .then((response) => {
                setData(data.filter((item) => !items.includes(item)));
                Alert.alert(
                    'Thông báo',
                    'Xoá phiếu nộp tiền thành công',
                    [{ text: 'Đóng', style: 'default' }],
                    { cancelable: false }
                );
            })
            .catch((error) => {
                Alert.alert(
                    'Thông báo',
                    error,
                    [{ text: 'Đóng', style: 'default' }],
                    { cancelable: false }
                );
            });
    };

    const onDeleteAllReceipt = () => {
        if (dataList.length === 0) {
            Alert.alert(
                'Thông báo',
                'Vui lòng chọn phiếu nộp tiền cần xoá',
                [{ text: 'Đóng', style: 'default' }],
                { cancelable: false }
            );
            return;
        }
        Alert.alert(
            'Thông báo',
            'Bạn có chắc muốn xoá phiếu nộp tiền đã chọn?',
            [
                { text: 'Đóng', style: 'default' },
                {
                    text: 'Xoá',
                    style: 'destructive',
                    onPress: async () => {
                        await actionDelete(dataList);
                        setDataList([]);
                        setIsCheckList(false);
                    }
                }
            ]
        );
    };

    const handleSearchFilter = (data) => {
        Keyboard.dismiss();
        setTimeout(() => {
            setIsShowFilter(false);
        }, 100);
        sendBankAction.getListDepositReceipt(data);
    };

    const onCheckUserPersonal = (showId, item) => {
        if (showId == 1) {
            Alert.alert(
                'Thông báo',
                'Bạn có muốn hiển thị dữ liệu cá nhân: Số CCCD, Ngày cấp, Nơi cấp của bạn trên giấy nộp tiền ngân hàng hay không?',
                [
                    {
                        text: 'Không',
                        style: 'default',
                        onPress: () => {
                            onPrintDeposit(showId, item);
                        }
                    },
                    {
                        text: 'Đồng ý',
                        style: 'default',
                        onPress: () => {
                            setShowInfo(true);
                            onPrintDeposit(showId, item);
                        }
                    }
                ],
                { cancelable: false }
            );
        } else onPrintDeposit(showId, item);
    };

    const checkPrintDeposit = (item) => {
        let body = {
            lstSendBank: [item]
        };
        sendBankAction
            .checkPrinterSendbank(body)
            .then((res) => {
                onCheckUserPersonal(res, [item]);
            })
            .catch((err) => {
                Alert.alert('Thông báo', err, [
                    {
                        text: 'Ok',
                        style: 'default'
                    }
                ]);
            });
    };

    const checkPrintDeposits = () => {
        if (dataList.length == 0) {
            Alert.alert(
                'Thông báo',
                'Vui lòng chọn phiếu nộp tiền cần in',
                [{ text: 'Đóng', style: 'default' }],
                { cancelable: false }
            );
            return;
        } else {
            let body = {
                lstSendBank: dataList
            };
            sendBankAction
                .checkPrinterSendbank(body)
                .then((res) => {
                    onCheckUserPersonal(res, dataList);
                })
                .catch((err) => {
                    Alert.alert('Thông báo', err, [
                        {
                            text: 'Ok',
                            style: 'default'
                        }
                    ]);
                });
        }
    };

    const onPrintDeposit = (showId, item) => {
        showBlockUI();
        let data = {
            showInfoUser: showInfo,
            sendBankTypeId: showId,
            lstSendBank: item
        };
        sendBankAction
            .printSendbank(data)
            .then((res) => {
                if (res.object !== null) {
                    hideBlockUI();
                    const params = {
                        object: res.object
                    };
                    navigation.navigate('PrintDeposit', params);
                }
                else {
                    hideBlockUI();
                    Alert.alert('Thông báo', 'Không có dữ liệu để in', [
                        {
                            text: 'Đóng',
                            style: 'default'
                        }
                    ]);
                }
            })
            .catch((error) => {
                hideBlockUI();
                Alert.alert('Thông báo', error, [
                    {
                        text: 'Đóng',
                        style: 'default'
                    },
                    {
                        text: 'Thử lại',
                        onPress: () => onPrintDeposit(item),
                        style: 'default'
                    }
                ]);
            });
    };

    const handleSearchList = () => {
        let body = {
            showInfoUser: true,
            sendUser: userText,
            isVoucher: -1
        };
        sendBankAction.getListDepositReceipt(body);
    };

    const handelPressItem = (name, text) => {
        switch (name) {
            // case '0':
            //     onCreateReceipt();
            //     break;
            case '1':
                onCreateRequest();
                break;
            case '2':
                onDeleteAllReceipt();
                break;
            case '3':
                checkPrintDeposits();
                break;
            default:
                break;
        }
    };

    const didMount = () => {
        let data = {
            isVoucher: -1
        };
        sendBankAction.getListDepositReceipt(data);
        sendBankAction.getBankSearchForm();
    };

    useEffect(didMount, []);

    useEffect(() => {
        setData(dataSearchListDeposit);
    }, [dataSearchListDeposit]);

    const renderItem = ({ item, index }) => {
        return (
            <ReceiptItem
                index={index}
                item={item}
                isCheckItem={isCheckList}
                openReceipt={(res) => onOpenReceipt(res)}
                onRequest={() => onCreateRequest(item)}
                onCheckList={onCheckList}
                // onPressDelete={() => onDeleteReceipt(item)}
                onPressPrint={() => checkPrintDeposit(item)}
            />
        );
    };

    return (
        <SafeAreaView style={styles.container}>
            <View
                style={{
                    flex: 1,
                    position: 'relative'
                }}>
                <SearchInputFilter
                    inputText={userText}
                    onChangeText={setUserText}
                    onClearText={() => {
                        setUserText('');
                    }}
                    placeholder={'Vui lòng nhập mã số nhân viên thu ngân'}
                    onPressFilter={() => setIsShowFilter(true)}
                    onSubmit={() => handleSearchList()}
                    key={'SearchInputFilter'}
                />
                <BaseLoading
                    style={{ flex: 1 }}
                    isLoading={stateSearchListDeposit.isFetching}
                    isError={stateSearchListDeposit.isError}
                    isEmpty={stateSearchListDeposit.isEmpty}
                    textLoadingError={stateSearchListDeposit.description}
                    onPressTryAgains={() => {
                        sendBankAction.getListDepositReceipt();
                    }}
                    content={
                        <FlatList
                            data={data}
                            renderItem={renderItem}
                            contentContainerStyle={styles.flatList}
                            scrollToOffset={{ animated: true, offset: 10 }}
                            keyExtractor={(item, index) => index.toString()}
                        />
                    }
                />
                {!!isShowFilter && (
                    <ModalFilterSearch
                        isVisible={isShowFilter}
                        hideModal={() => {
                            setIsShowFilter(false);
                        }}
                        dataListBank={dataBankSearchForm}
                        onPressSearch={(data) => {
                            handleSearchFilter(data);
                        }}
                    />
                )}
            </View>
            <FloatingAction
                actions={menuItem}
                color={COLORS.btn597B66}
                distanceToEdge={{
                    vertical: constants.heightBottomSafe + 10,
                    horizontal: 10
                }}
                overlayColor={COLORS.bg0000005}
                dismissKeyboardOnPress={true}
                iconWidth={18}
                iconHeight={18}
                buttonSize={42}
                animated={false}
                onPressItem={(name, text) => {
                    handelPressItem(name, text);
                }}
                key={'DetailAction'}
            />
        </SafeAreaView>
    );
};

const mapStateToProps = function (state) {
    return {
        userInfo: state.userReducer,
        dataSearchListDeposit: state.sendBankReducer.dataSearchListDeposit,
        stateSearchListDeposit: state.sendBankReducer.stateSearchListDeposit,
        dataBankSearchForm: state.sendBankReducer.dataBankSearchForm
    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        sendBankAction: bindActionCreators(sendBankActionCreator, dispatch)
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(DepositReceipt);

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: COLORS.bgF5F5F5
    },
    filter: {
        padding: 10,
        gap: 10
    },
    filterItem: {
        width: '100%',
        flexDirection: 'row',
        alignItems: 'center',
        gap: 5
    },
    header: {
        fontWeight: 'bold',
        marginVertical: 10
    },
    dateWrapper: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        height: 45,
        paddingHorizontal: 10,
        borderRadius: 5,
        borderWidth: 1,
        borderColor: COLORS.bdDDDDDD,
        backgroundColor: COLORS.bgFFFFFF
    },
    textInput: {
        flex: 1,
        borderWidth: 1,
        borderRadius: 4,
        borderColor: COLORS.bdCCCCCC,
        marginVertical: 5,
        paddingHorizontal: 10,
        backgroundColor: COLORS.bgFFFFFF,
        justifyContent: 'center',
        paddingVertical: 8
    },
    picker: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        height: 45,
        borderRadius: 5,
        borderWidth: 1,
        borderColor: COLORS.bdE4E4E4,
        backgroundColor: COLORS.btnFFFFFF
    },
    buttonWrapper: {
        justifyContent: 'center',
        flexDirection: 'row',
        flexWrap: 'wrap',
        margin: 5,
        gap: 10
    },
    button: {
        borderRadius: 7,
        paddingVertical: 10,
        paddingHorizontal: 15,
        backgroundColor: COLORS.btn2C8BD7
    },
    buttonText: {
        fontSize: 14,
        fontWeight: 'bold',
        color: COLORS.txtFFFFFF
    },
    flatList: {
        flexGrow: 1,
        gap: 10,
        paddingBottom: constants.height / 10 - 40
    }
});
