import React, { useEffect, useState } from 'react';
import { StyleSheet, TextInput, TouchableOpacity, View } from 'react-native';
import { MyText, Icon } from '@components';
import { COLORS } from '@styles';

const CurrencyTable = ({ data, lstDailyFund }) => {
    const [dataFunds, setDataFunds] = useState();
    const [totalSumMoney, setTotalSumMoney] = useState(data?.sumTotalMoney);
    const handleInputChange = (value, index) => {
        let updatedFunds = [...dataFunds];
        // Nếu giá trị nhập vào undefined hoặc rỗng thì đặt về "0"
        if (!value || value.trim() === '') {
            value = '0';
        }
        // Chỉ giữ lại số (loại bỏ ký tự không phải số)
        let newValue = value.replace(/\D/g, '');
        // Nếu kết quả rỗng sau khi loại bỏ ký tự không hợp lệ, đặt lại thành "0"
        if (newValue === '') {
            newValue = 0;
        } else {
            // Loại bỏ số 0 ở đầu nhưng giữ ít nhất một số
            newValue = newValue.replace(/^0+/, '') || 0;
        }
        // Chuyển thành số nguyên
        let numberSheets = parseInt(newValue, 10) || 0;
        // Cập nhật dữ liệu
        updatedFunds[index].numberSheets = numberSheets;
        updatedFunds[index].totalMoney =
            updatedFunds[index].denominations * numberSheets;
        setDataFunds(updatedFunds);
    };

    // Trừ số lượng
    const onPressTotalApart = (index) => {
        let updatedFunds = [...dataFunds];
        if (updatedFunds[index].numberSheets > 0) {
            updatedFunds[index].numberSheets -= 1;
            updatedFunds[index].totalMoney =
                updatedFunds[index].denominations *
                updatedFunds[index].numberSheets;
        }
        setDataFunds(updatedFunds);
    };

    // Cộng số lượng
    const onPressTotalAdd = (index) => {
        let updatedFunds = [...dataFunds];
        updatedFunds[index].numberSheets += 1;
        updatedFunds[index].totalMoney =
            updatedFunds[index].denominations *
            updatedFunds[index].numberSheets;
        setDataFunds(updatedFunds);
    };

    const CoverCurrencyUnitName = {
        1: 'VNĐ',
        2: 'USD'
    };

    useEffect(() => {
        setTotalSumMoney(
            dataFunds?.reduce((sum, item) => sum + item.totalMoney, 0)
        );
    }, [dataFunds]);

    useEffect(() => {
        lstDailyFund(dataFunds, totalSumMoney);
    }, [dataFunds, totalSumMoney]);

    useEffect(() => {
        if (data?.lstDailyFund) {
            setDataFunds(data?.lstDailyFund);
            return;
        }
        if (data.lstSendBank) {
            setDataFunds(data.lstSendBank);
            return;
        }
    }, [data]);

    return (
        <View
            style={styles.container}
            showsVerticalScrollIndicator={false}
            automaticallyAdjustKeyboardInsets>
            <MyText text={'Bảng thống kê số tiền nộp'} style={styles.title} />
            <View style={styles.headerRow}>
                <MyText text={'Mệnh giá'} style={styles.headerText} />
                <MyText text={'Số tờ'} style={styles.headerText} />
                <MyText text={'Tổng tiền'} style={styles.headerText} />
            </View>
            {dataFunds?.map((item, index) => (
                <View key={item.dailyFundId} style={styles.row}>
                    <MyText text={item.denominations.toLocaleString('vi-VN')} style={styles.text} />
                    <View style={styles.vwInput}>
                        <TouchableOpacity
                            style={[
                                styles.btnInput,
                                {
                                    backgroundColor:
                                        item.numberSheets === 0
                                            ? COLORS.btnDDDDDD
                                            : COLORS.btnFF0000
                                }
                            ]}
                            onPress={() => onPressTotalApart(index)}
                            // disabled={}
                        >
                            <Icon
                                name="remove"
                                type="Ionicons"
                                size={14}
                                color={COLORS.icFFFFFF}
                            />
                        </TouchableOpacity>
                        <TextInput
                            style={styles.input}
                            value={item.numberSheets.toString()}
                            onChangeText={(value) =>
                                handleInputChange(value, index)
                            }
                            maxLength={5}
                            clearText={() => handleInputChange('')}
                            keyboardType="numeric"
                            returnKeyType={'done'}
                        />
                        <TouchableOpacity
                            style={[
                                styles.btnInput,
                                {
                                    backgroundColor: COLORS.btn008848
                                }
                            ]}
                            onPress={() => onPressTotalAdd(index, item)}>
                            <Icon
                                name="add"
                                type="Ionicons"
                                size={14}
                                color={COLORS.icFFFFFF}
                            />
                        </TouchableOpacity>
                    </View>
                    <MyText
                        text={`${item.totalMoney.toLocaleString('vi-VN')} ${
                            item.currencyUnitName
                        }`}
                        style={styles.text}
                    />
                </View>
            ))}
            <View style={styles.footer}>
                <MyText text={'Tổng số tiền nộp'} style={styles.footerText} />
                <MyText
                    text={`${
                        totalSumMoney
                            ? totalSumMoney.toLocaleString('vi-VN')
                            : '0'
                    } ${CoverCurrencyUnitName[data?.currencyUnitId]}`}
                    style={styles.footerAmount}
                />
            </View>
        </View>
    );
};

export default CurrencyTable;

const styles = StyleSheet.create({
    container: {
        flexGrow: 1
    },
    title: {
        marginTop: 8,
        fontSize: 16,
        fontWeight: '700',
        marginBottom: 12
    },
    headerRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 6
    },
    headerText: {
        flex: 1,
        textAlign: 'center',
        fontWeight: 'bold'
    },
    row: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginVertical: 4
    },
    text: {
        flex: 1,
        textAlign: 'center',
        fontSize: 16
    },
    vwInput: {
        flex: 1,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center'
    },
    input: {
        minWidth: 50,
        height: 40,
        borderWidth: 1,
        borderRadius: 4,
        borderColor: COLORS.bd778899,
        paddingHorizontal: 10,
        justifyContent: 'center',
        textAlign: 'center'
    },
    footer: {
        flexWrap: 'wrap',
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginVertical: 16
    },
    footerText: {
        fontWeight: '700',
        fontSize: 16
    },
    footerAmount: {
        fontSize: 16,
        color: COLORS.txtEB3B3B
    },
    btnInput: {
        height: 28,
        width: 28,
        marginHorizontal: 4,
        borderRadius: 8,
        borderColor: COLORS.primary,
        justifyContent: 'center',
        alignItems: 'center'
    }
});
