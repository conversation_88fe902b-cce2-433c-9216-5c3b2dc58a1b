import React, { useEffect, useState } from 'react';
import {
    StyleSheet,
    SafeAreaView,
    Alert,
    TouchableOpacity
} from 'react-native';
import { connect } from 'react-redux';
import { translate } from '@translate';
import {
    MyText,
    showBlockUI,
    hideBlockUI,
} from '@components';

import { COLORS } from '@styles';
import PrintReport from '../../../SaleOrderManager/component/PrintReport/index';
import Report from '../../../SaleOrderManager/component/PrintReport/Report';
import * as sendBankActionCreator from '../../action';
import * as actionSaleOrderCreator from '../../../SaleOrderPayment/action';
import { bindActionCreators } from 'redux';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { helper } from '@common';
import { constants } from '@constants';

const PrintDeposit = ({
    navigation,
    sendBankAction,
    printerCommon,
    actionSaleOrder,
    statePrinter,
    route
}) => {
    const [reportCommon, setReportCommon] = useState({});
    const [sample, setSample] = useState();

    useEffect(() => {
        sendBankAction.getReportPrinter('8');
        setSample(route.params.object);
    }, []);

    const getReportPrinter = () => {
        const { storeID, brandID } = userInfo;
        const isAnKhang = brandID == 8;
        if (helper.checkConfigStorePrint(storeID) && !isAnKhang) {
            actionSaleOrder.getReportPrinterSocket('8');
        } else {
            actionSaleOrder.getReportPrinter('8');
        }
    };

    const onCheckContent = () => {
        if (helper.IsEmptyObject(reportCommon)) {
            Alert.alert('', translate('f88.please_select_printer'));
        } else {
            getPrintContent();
        }
    };

    const getPrintContent = async () => {
        try {
            showBlockUI();
            onPrintBill(sample);
        } catch (msgError) {
            Alert.alert('', msgError, [
                {
                    text: 'OK',
                    style: 'default',
                    onPress: hideBlockUI
                }
            ]);
        }
    };

    const onPrintBill = (data) => {
        const requestAPI = getPrintRequestAPI(data);
        if (helper.IsNonEmptyArray(requestAPI)) {
            printAllRequest(requestAPI);
        } else {
            hideBlockUI();
        }
    };

    const printAllRequest = (allPromise) => {
        Promise.all(allPromise)
            .then((result) => {
                console.log('PRINT RSULT', result);
                Alert.alert('', translate('saleOrderManager.print_success'), [
                    {
                        text: 'OK',
                        style: 'default',
                        onPress: () => {
                            navigation.goBack();
                            hideBlockUI();
                        }
                    }
                ]);
            })
            .catch((msgError) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: 'OK',
                            style: 'default',
                            onPress: hideBlockUI
                        }
                    ]
                );
            });
    };

    const getPrintRequestAPI = (content) => {
        const requestAPI = [];
        const report = reportCommon;
        if (!helper.IsEmptyObject(report)) {
            for (let i = 0; i < 1; i++) {
                const printService = getPrintService(report, content);
                requestAPI.push(printService);
            }
        }
        return requestAPI;
    };

    const getPrintService = (report, content) => {
        let printerConfig = {
            strPrinterName: report.PRINTERNAME,
            strPaperSize: report.PAPERSIZE,
            paperwidth: report.PAPERWIDTH,
            parperheight: report.PARPERHEIGHT,
            intCopyCount: 1,
            bolIsDuplex: false,
            bolShrinkToMargin: false,
            strBase64: content
        };
        let formBody = [];
        for (const property in printerConfig) {
            const encodedKey = encodeURIComponent(property);
            const encodedValue = encodeURIComponent(printerConfig[property]);
            formBody.push(encodedKey + '=' + encodedValue);
        }
        formBody = formBody.join('&');
        return new Promise((resolve, reject) => {
            actionSaleOrderCreator
                .printBillVoucher(formBody)
                .then((result) => {
                    resolve(result);
                })
                .catch((msgError) => {
                    reject(msgError);
                });
        });
    };

    return (
        <SafeAreaView style={styles.container}>
            <KeyboardAwareScrollView
                style={{
                    flex: 1,
                    alignContent: 'center'
                }}
                enableResetScrollToCoords={false}
                keyboardShouldPersistTaps={'always'}
                bounces={false}
                overScrollMode="always"
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                extraScrollHeight={60}>
                <PrintReport
                    title={translate('saleOrderPayment.choose_printer')}
                    statePrinter={statePrinter}
                    onTryAgains={() => getReportPrinter()}
                    dataCommon={printerCommon}
                    renderItemCommon={({ item, index }) => (
                        <Report
                            key={`ReportCommon`}
                            info={item}
                            report={reportCommon}
                            onCheck={() => {
                                setReportCommon(item);
                            }}
                        />
                    )}
                />
                <TouchableOpacity
                    style={{
                        justifyContent: 'center',
                        alignSelf: 'center',
                        backgroundColor: COLORS.bg288AD6,
                        width: (constants.width - 60) / 2,
                        height: 42,
                        borderRadius: 6
                    }}
                    onPress={() => {
                        onCheckContent();
                    }}>
                    <MyText
                        text={'In giấy nộp tiền'}
                        style={{
                            color: COLORS.txtFFFFFF,
                            fontSize: 16,
                            fontWeight: '600',
                            padding: 6,
                            textAlign: 'center'
                        }}
                    />
                </TouchableOpacity>
            </KeyboardAwareScrollView>
        </SafeAreaView>
    );
};

const mapStateToProps = (state) => ({
    userInfo: state.userReducer,
    dataSearchUser: state.sendBankReducer.dataSearchUser,
    printerRetail: state.sendBankReducer.printerRetail,
    printerVAT: state.sendBankReducer.printerVAT,
    printerCommon: state.sendBankReducer.printerCommon,
    statePrinter: state.sendBankReducer.statePrinter
});

const mapDispatchToProps = (dispatch) => ({
    sendBankAction: bindActionCreators(sendBankActionCreator, dispatch),
    actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch)
});

export default connect(mapStateToProps, mapDispatchToProps)(PrintDeposit);

const styles = StyleSheet.create({
    container: {
        flex: 1
    }
});
