export const sendBankState = {
    dataSearchList: [],
    dataSearch: [],
    dataSearchUser: [],
    dataApproveUser: [],
    dataCancelReason: [],
    dataApprovalStatus: [],
    stateSearchList: {
        isFetching: false,
        isEmpty: false,
        description: '',
        isError: false
    },
    stateSearchStore: {
        isFetching: false,
        isEmpty: false,
        description: '',
        isError: false
    },
    stateSearchUser: {
        isFetching: false,
        isEmpty: false,
        description: '',
        isError: false
    },
    stateApproveUser: {
        isFetching: false,
        isEmpty: false,
        description: '',
        isError: false
    },
    stateCancelReason: {
        isFetching: false,
        isEmpty: false,
        description: '',
        isError: false
    },
    stateApprovalStatus: {
        isFetching: false,
        isEmpty: false,
        description: '',
        isError: false
    },
    // ======= deposit ==========
    dataSearchListDeposit: [],
    stateSearchListDeposit: {
        isFetching: false,
        isEmpty: false,
        description: '',
        isError: false
    },
    dataBankCrearteForm: [],
    stateBankCrearteForm: {
        isFetching: false,
        isEmpty: false,
        description: '',
        isError: false
    },
    dataBankSearchForm: [],
    stateBankSearchForm: {
        isFetching: false,
        isEmpty: false,
        description: '',
        isError: false
    },
    dataDailyFund: {},
    stateDailyFund: {
        isFetching: false,
        isEmpty: false,
        description: '',
        isError: false
    },
    dataCreateSendBank: {},
    stateCreateSendBank: {
        isFetching: false,
        isEmpty: false,
        description: '',
        isError: false
    },
    dataDetailSendBank: {},
    stateDetailSendBank: {
        isFetching: false,
        isEmpty: false,
        description: '',
        isError: false
    },
    printerRetail: [],
    printerVAT: [],
    printerCommon: [],
    statePrinter: {
        isFetching: false,
        description: '',
        isError: false,
        isEmpty: false
    }
};
