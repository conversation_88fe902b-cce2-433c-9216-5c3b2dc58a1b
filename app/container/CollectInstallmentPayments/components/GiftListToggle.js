import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { Icon, MyText } from '@components';

const GiftListToggle = ({
    isGiftListVisible,
    onToggleGiftList,
    onScanPress,
    giftCount = 0
}) => {
    return (
        <TouchableOpacity
            onPress={onToggleGiftList}
            activeOpacity={0.85}
            style={{
                paddingVertical: 12,
                paddingHorizontal: 16,
                backgroundColor: '#FFFFFF',
                borderRadius: 12,
                marginTop: 12,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 3 },
                shadowOpacity: 0.08,
                shadowRadius: 6,
                elevation: 4,
                borderWidth: 1,
                borderColor: '#E0E0E0'
            }}
        >
            <MyText
                text={"Vui lòng quét mã trên app QTV nếu khách hàng muốn nhận khuyến mãi"}
                addSize={-1}
                style={{
                    color: '#DC2525',
                    fontSize: 14,
                    fontWeight: '600',
                    marginBottom: 8,
                    textAlign: 'center'
                }}
            />

            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <TouchableOpacity
                    onPress={onScanPress}
                    style={{
                        width: 60,
                        height: 60,
                        borderRadius: 30,
                        backgroundColor: '#00C853',
                        justifyContent: 'center',
                        alignItems: 'center',
                        marginRight: 12,
                        shadowColor: '#000',
                        shadowOffset: { width: 0, height: 4 },
                        shadowOpacity: 0.3,
                        shadowRadius: 6,
                        elevation: 8,
                        borderWidth: 2,
                        borderColor: '#FFFFFF',
                    }}
                >
                    <Icon
                        iconSet={"Ionicons"}
                        name={"qr-code-outline"}
                        size={28}
                        color="#FFFFFF"
                    />
                </TouchableOpacity>

                <Icon
                    iconSet={"Ionicons"}
                    name={"gift-outline"}
                    color={"#4A90E2"}
                    size={20}
                    style={{ marginRight: 6 }}
                />
                <MyText
                    text={"DANH SÁCH QUÀ TẶNG"}
                    addSize={-1}
                    style={{
                        color: '#333333',
                        fontSize: 15,
                        fontWeight: '600',
                        flex: 1
                    }}
                />
                <View style={{ position: 'relative', marginLeft: 8 }}>
                    <Icon
                        iconSet={"Ionicons"}
                        name={isGiftListVisible ? "chevron-up" : "chevron-down"}
                        color={"#4A90E2"}
                        size={20}
                    />
                    <View style={{
                        position: 'absolute',
                        top: -6,
                        right: -10,
                        backgroundColor: '#FF3B30',
                        borderRadius: 10,
                        width: 18,
                        height: 18,
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}>
                        <MyText
                            text={giftCount}
                            style={{
                                color: '#FFFFFF',
                                fontSize: 11,
                                fontWeight: 'bold'
                            }}
                        />
                    </View>
                </View>
            </View>
        </TouchableOpacity>
    );
};

export default GiftListToggle;
