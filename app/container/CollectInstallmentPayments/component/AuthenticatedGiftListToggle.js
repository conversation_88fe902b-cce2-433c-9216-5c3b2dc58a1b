import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { Icon, MyText } from '@components';

const AuthenticatedGiftListToggle = ({
    isShowGiftList,
    onToggleGiftList,
    onClosePress,
    phoneNumber,
    giftCount = 0
}) => {
    const phoneText = phoneNumber
        ? `SĐT: ${phoneNumber}`
        : 'SĐT khách hàng không được tặng quà';

    return (
        <TouchableOpacity
            onPress={onToggleGiftList}
            activeOpacity={0.85}
            style={{
                flexDirection: 'row',
                alignItems: 'center',
                paddingVertical: 12,
                paddingHorizontal: 16,
                backgroundColor: '#FFFFFF',
                borderRadius: 12,
                marginTop: 12,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 3 },
                shadowOpacity: 0.08,
                shadowRadius: 6,
                elevation: 4,
                borderWidth: 1,
                borderColor: '#E0E0E0'
            }}
        >
            <TouchableOpacity
                onPress={onClosePress}
                activeOpacity={0.8}
                style={{
                    width: 40,
                    height: 40,
                    borderRadius: 20,
                    backgroundColor: '#FFE5E5',
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginRight: 12,
                    borderWidth: 1,
                    borderColor: '#DC2525',
                    shadowColor: '#DC2525',
                    shadowOffset: { width: 0, height: 2 },
                    shadowOpacity: 0.3,
                    shadowRadius: 4,
                    elevation: 4,
                }}
            >
                <Icon
                    iconSet="Ionicons"
                    name="close"
                    size={20}
                    color="#DC2525"
                />
            </TouchableOpacity>

            <Icon
                iconSet={"Ionicons"}
                name={"gift-outline"}
                color={"#4A90E2"}
                size={20}
            />

            <View style={{ flex: 1, marginLeft: 12 }}>
                <MyText
                    text={`DANH SÁCH QUÀ TẶNG`}
                    addSize={-1}
                    style={{
                        color: '#333333',
                        fontSize: 15,
                        fontWeight: '600',
                        textAlign: 'center'
                    }}
                />
                <MyText
                    text={phoneText}
                    addSize={-2}
                    style={{
                        color: phoneNumber ? '#4A90E2' : '#DC2525',
                        fontSize: 13,
                        fontWeight: '500',
                        textAlign: 'center',
                        marginTop: 2
                    }}
                />
            </View>

            <View style={{ position: 'relative', marginRight: 8 }}>
                <Icon
                    iconSet={"Ionicons"}
                    name={isShowGiftList ? "chevron-up" : "chevron-down"}
                    color={"#4A90E2"}
                    size={20}
                />
                <View style={{
                    position: 'absolute',
                    top: -6,
                    right: -10,
                    backgroundColor: '#FF3B30',
                    borderRadius: 10,
                    width: 18,
                    height: 18,
                    justifyContent: 'center',
                    alignItems: 'center'
                }}>
                    <MyText
                        text={giftCount}
                        style={{
                            color: '#FFFFFF',
                            fontSize: 11,
                            fontWeight: 'bold'
                        }}
                    />
                </View>
            </View>
        </TouchableOpacity>
    );
};

export default AuthenticatedGiftListToggle;
