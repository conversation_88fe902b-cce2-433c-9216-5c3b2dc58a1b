import { StyleSheet, Text, View, TouchableOpacity } from "react-native";
import React from "react";
import { Icon } from "@components";

const Coupon = ({ message }) => {
    const [isExpanded, setIsExpanded] = React.useState(false);
    const [textTooLong, setTextTooLong] = React.useState(false);

    return (
        <View style={styles.container}>
            <View style={styles.iconWrapper}>
                <Icon name="local-offer" color={"#FF5722"} size={28} />
            </View>
            <View style={styles.textWrapper}>
                <View style={{ flex: 1 }}>
                    <View style={styles.textContainer}>
                        <Text
                            style={styles.text}
                            numberOfLines={isExpanded ? undefined : 3}
                        >
                            {message}
                        </Text>
                        {textTooLong && !isExpanded && (
                            <TouchableOpacity
                                style={styles.inlineExpandButton}
                                onPress={() => setIsExpanded(true)}
                            >
                                <Icon name="keyboard-arrow-down" color={"#FF5722"} size={30} />
                            </TouchableOpacity>
                        )}
                        {textTooLong && isExpanded && (
                            <TouchableOpacity
                                style={styles.inlineExpandButton}
                                onPress={() => setIsExpanded(false)}
                            >
                                <Icon name="keyboard-arrow-up" color={"#FF5722"} size={30} />
                            </TouchableOpacity>
                        )}
                    </View>
                    <View style={styles.viewHiddenLine}>
                        <Text
                            style={styles.text}
                            onTextLayout={(e) => {
                                if (!textTooLong) {
                                    const { lines } = e.nativeEvent;
                                    setTextTooLong(lines.length > 3);
                                }
                            }}
                        >
                            {message}
                        </Text>
                    </View>
                </View>
            </View>
        </View>
    );
};

export default Coupon;

const styles = StyleSheet.create({
    container: {
        flexDirection: "row",
        padding: 5,
        borderRadius: 20,
        marginHorizontal: 5,
        marginVertical: 5,
        alignItems: "center",
        backgroundColor: "#FFF4E5",
        borderWidth: 2,
        borderColor: "#FF9800",
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 6,
        elevation: 5,
    },
    iconWrapper: {
        marginRight: 12,
        backgroundColor: "#FFE0B2",
        padding: 12,
        borderRadius: 50,
        alignItems: "center",
        justifyContent: "center",
    },
    textWrapper: {
        flex: 1,
        flexDirection: "row",
        alignItems: "center",
    },
    expandButton: {
        padding: 4,
        marginLeft: 8,
    },
    textContainer: {
        flexDirection: "row",
        alignItems: "center",
    },
    inlineExpandButton: {
        marginLeft: 4,
        padding: 2,
        height: "100%",
    },
    text: {
        color: "#D84315",
        fontSize: 15,
        fontWeight: "700",
        lineHeight: 24,
        flex: 1,
    },
    viewHiddenLine: {
        position: "absolute",
        opacity: 0,
        pointerEvents: "none",
    },
});
