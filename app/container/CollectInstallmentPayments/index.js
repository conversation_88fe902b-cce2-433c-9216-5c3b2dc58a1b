import React, { useEffect, useState } from "react";
import { useFocusEffect } from "@react-navigation/native";
import {
    <PERSON><PERSON>,
    FlatList,
    Pressable,
    SafeAreaView,
    StyleSheet,
    TouchableOpacity,
    View,
} from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import LinearGradient from 'react-native-linear-gradient';
import { constants } from "@constants";
import {
    PickerSearch,
    Icon,
    MyText,
    showBlockUI,
    hideBlockUI,
    BarcodeCamera
} from "@components";
import Border from "./component/Border";
import ButtonAction from "./component/ButtonAction";
import SearchInput from "./component/SearchInput";
import TitleInputMoney from "./component/TitleInputMoney";
import Promotion from './component/Coupon/Promotion';
import debounce from "lodash.debounce";
import { helper } from "@common";
import { BackHeader } from "@header";
import { connect } from "react-redux";
import { translate } from "@translate";
import { bindActionCreators } from "redux";
import * as actionCollectInstallmentCreator from "./action";
import * as actionPaymentOrderCreator from "../SaleOrderPayment/action";
import ButtonIcon from "./component/ButtonIcon";
import { COLORS } from "../../styles";
import * as actionCollectionCreator from "../CollectionTransfer/action";

const PayBillAirtimeService = ({
    actionCollectInstallment,
    actionPaymentOrder,
    navigation,
    itemCatalog,
    actionCollection
}) => {
    //data test PK10001234001 | PK10001234002
    const [contractCode, setContractCode] = useState('');
    const [productList, setProductList] = useState([]);
    const [paymentType, setPaymentType] = useState({});
    const [updateAirTime, setUpdateAirtime] = useState([]);
    const [updateItemField, setUpdateItemField] = useState({});
    const [isQRCode, setIsQRCode] = useState(false);
    const [billingInformation, setBillingInformation] = useState({});
    const [show, setShow] = useState(false);
    const [selectedItems, setSelectedItems] = useState([]);
    const [selectedItemsArray, setSelectedItemsArray] = useState([]);
    const [productPrice, setProductPrice] = useState({});
    const [isLockPaiMoney, setIsLockPaiMoney] = useState(false);
    const [updateTotalAmount, setUpdateTotalAmount] = useState('');
    const [promotion, setPromotion] = useState({});
    const [amount, setAmount] = useState('');
    const [isPaymentPeriodVisible, setPaymentPeriodVisible] = useState(false);
    const [isGiftListVisible, setIsGiftListVisible] = useState(false);
    const [isShowGiftList, setIsShowGiftList] = useState(true);
    const [authenticate, setAuthenticate] = useState({});
    const [showGroupGift, setShowGroupGift] = useState(true);
    const [isVisibleScan, setIsVisibleScan] = useState(false);

    const { FeeInfo, PriceInfo, TotalAmount, PromotionGift } = productPrice ?? {};
    const customerName = billingInformation?.PriceInfo?.ExtraData?.CustomerName ?? '';
    const customerAddress = billingInformation?.PriceInfo?.ExtraData?.CustomerAddress ?? '';
    const listPriceInfoDetailBO = billingInformation?.PriceInfo?.ListPriceInfoDetailBO ?? [];
    const paymentDescription = billingInformation?.PriceInfo?.ExtraData?.PaymentDescription ?? '';
    const paymentRule = billingInformation?.PriceInfo?.ExtraData?.PaymentRule ?? '';
    const extraData = billingInformation?.PriceInfo?.ExtraData;
    const promotionMessage = billingInformation?.PromotionInfo?.PromotionMessage ?? '';
    const fee = FeeInfo?.Fee ?? '';
    const priceVAT = PriceInfo?.PriceVAT ?? '';
    const minAmount = PriceInfo?.MinAmount ?? '';
    const maxAmount = PriceInfo?.MaxAmount ?? '';
    const minMaxAmount = PriceInfo?.MinMaxAmount ?? '';
    const fareDebt = priceVAT - fee;
    const totalDebtRemain = billingInformation?.PriceInfo?.TotalDebtRemain;
    console.log(paymentRule, "Con log để biết đang rơi vào case ---> paymentRule == 1 thanh toán hết, paymentRule == 2 thanh toán cũ nhất, khác thanh toán tuỳ ý")

    const { ServiceCategoryID, AirtimeServiceGroupID, ProductID, PhoneNumber } = itemCatalog ?? '';

    useFocusEffect(
        React.useCallback(() => {
            return () => {
                setContractCode('');
                setProductList([]);
                setUpdateAirtime([]);
                setBillingInformation({});
                setUpdateItemField({});
                setSelectedItems([]);
                setSelectedItemsArray([]);
                setProductPrice({});
                setUpdateTotalAmount('');
                setAmount('');
                setAuthenticate({});
                setShowGroupGift(true)
            };
        }, [])
    );

    useEffect(() => {
        if (!!PhoneNumber) {
            setContractCode(PhoneNumber);
        }
    }, [PhoneNumber]);

    //Khi đã cập nhật state PhoneNumber kiểm tra tra airtimeServiceGroupID-productID-phoneNumber/example: 2-4642571000036-PK10001234001 auto gọi hàm getDataCollectInstallmentProductList
    useEffect(() => {
        if (!!contractCode && !!ProductID && !!PhoneNumber) {
            setIsQRCode(!isQRCode);
            getDataCollectInstallmentProductList();
        }
    }, [contractCode, ProductID, PhoneNumber]);

    //Giải quyết vấn đề bất đồng bộ khi lớn hơn 1 kỳ thanh toán khi chọn 1 trong hai kỳ thì khi lấy phí giá trị TotalAmount đang lấy giá trị trước đó
    useEffect(() => {
        if (!!TotalAmount) {
            setUpdateTotalAmount(TotalAmount)
        }
    }, [TotalAmount]);

    const getTitleCatalog = () => {
        const { AirtimeServiceGroupName } = itemCatalog ?? "";
        const conertServiceGroupName = AirtimeServiceGroupName.toUpperCase();
        return `THU HỘ ${conertServiceGroupName}`;
    }

    const goBack = () => {
        const item = {
            ServiceCatalogID: 4,
            isVisibleScanQrCode: false
        };
        actionCollection.updateItemCatalog({
            ...itemCatalog,
            contractCode: "",
            ProductID: "",
            PhoneNumber: ""
        })
        navigation.navigate("CatalogCollection", {
            item
        });
    }

    const getDataCollectInstallmentProductList = async () => {
        setPaymentType({});
        setProductList([]);
        setUpdateAirtime('');
        setUpdateItemField({});
        setUpdateTotalAmount('');
        if (!!contractCode) {
            showBlockUI();
            const data = {
                "catalogID": ServiceCategoryID,
                "serviceGroupID": AirtimeServiceGroupID,
                "phoneNumber": contractCode
            };
            const callHandleGetPriceAndFeeService = async (product) => {
                if (product) {
                    const item = product?.AirtimeTransactionType?.[0];
                    setPaymentType(product);
                    setUpdateAirtime(product?.AirtimeTransactionType);
                    setUpdateItemField(item);
                    await handleGetPriceAndFeeService(
                        ServiceCategoryID,
                        AirtimeServiceGroupID,
                        item,
                        contractCode,
                        isQRCode,
                        amount
                    );
                }
            };

            try {
                const response = await actionCollectInstallment.getCollectInstallmentProductList(data);
                setProductList(response);

                if (response.length === 1) {
                    await callHandleGetPriceAndFeeService(response[0]);
                } else if (!!ProductID) {
                    const filteredProduct = response?.filter((pre) => pre.ProductID == ProductID);
                    if (filteredProduct) {
                        await callHandleGetPriceAndFeeService(filteredProduct?.[0]);
                    }
                }
                else if (response.length > 1 && !ProductID) {
                    setProductList(response);
                }
            } catch (err) {
                Alert.alert("", err.msgError, [
                    {
                        text: "OK",
                        onPress: () => {
                            hideBlockUI();
                        },
                    },
                ]);
            } finally {
                hideBlockUI();
            }
        }
    };


    const handleGetPriceAndFeeService = async (ServiceCategoryID, AirtimeServiceGroupID, updateItemField, contractCode, isQRCode, amount) => {
        showBlockUI();
        setSelectedItems([]);
        setSelectedItemsArray([]);
        setBillingInformation({});
        setProductPrice({});
        setUpdateTotalAmount('');
        setAuthenticate({});
        setShowGroupGift(true);
        const data = {
            "catalogID": ServiceCategoryID,
            "serviceGroupID": AirtimeServiceGroupID,
            "airtimeTransactionTypeID": updateItemField?.AirtimeTransactionTypeID,
            "productID": updateItemField?.ProductID,
            "phoneNumber": contractCode,
            "isQRCode": isQRCode,
            "amount": amount
        };
        try {
            const response = await actionCollectInstallment.getCollectInstallmentPriceAndFeeService(data);
            const isPaymentRule = response?.PriceInfo?.ExtraData?.PaymentRule ?? 0;
            const isSinglePriceInfoDetail = response?.PriceInfo?.ListPriceInfoDetailBO?.length === 1;
            setBillingInformation(response);
            setSelectedItemsArray([]);
            if (isPaymentRule === 7 || isSinglePriceInfoDetail) {
                const singleItem = response.PriceInfo.ListPriceInfoDetailBO[0];
                const extraDataReponse = response?.PriceInfo?.ExtraData;
                const isSesstion = response?.PriceInfo?.Session;
                setSelectedItems([singleItem.PriceInforDetailID]);
                setSelectedItemsArray([singleItem]);
                setPaymentPeriodVisible(true);
                await getPromotionService(ServiceCategoryID, AirtimeServiceGroupID, updateItemField, contractCode, extraDataReponse, [singleItem], isSesstion);
            }
        } catch (err) {
            Alert.alert("", err.msgError, [
                {
                    text: "OK",
                    onPress: () => {
                        hideBlockUI();
                    },
                },
            ]);
        } finally {
            hideBlockUI();
        }
    };

    const getPromotionService = async (ServiceCategoryID, AirtimeServiceGroupID, updateItemField, contractCode, extraData, singleItem, isSesstion) => {
        showBlockUI();
        const data = {
            "catalogID": ServiceCategoryID,
            "serviceGroupID": AirtimeServiceGroupID,
            "airtimeTransactionTypeID": updateItemField?.AirtimeTransactionTypeID,
            "productID": updateItemField?.ProductID,
            "phoneNumber": contractCode
        };
        try {
            setAuthenticate({});
            setShowGroupGift(true);
            const response = await actionCollectInstallment.getPromotionService(data);
            setPromotion(response);
            await handleGetProductPrice(ServiceCategoryID, AirtimeServiceGroupID, updateItemField, contractCode, singleItem, extraData, isQRCode, isSesstion)
        } catch (err) {
            Alert.alert("", err.msgError, [
                {
                    text: "BỎ QUA",
                    onPress: () => {
                        hideBlockUI();
                        navigation.goBack()
                    },
                },
                {
                    text: "TIẾP TỤC",
                    onPress: () => {
                        hideBlockUI();
                        handleGetProductPrice(ServiceCategoryID, AirtimeServiceGroupID, updateItemField, contractCode, singleItem, extraData, isQRCode, isSesstion)
                    },
                },
            ]);
        } finally {
            hideBlockUI();
        }
    };

    const renderDenomination = ({ item, index }) => {
        const selected = selectedItems.includes(item.PriceInforDetailID);
        return (
            <Pressable
                onPress={() => onClickItem(item, index)}
                key={item.PriceInforDetailID}
                disabled={isPaymentPeriodVisible}
                style={{
                    marginVertical: 10,
                    marginHorizontal: 20,
                    borderRadius: 12,
                    overflow: 'hidden',
                    elevation: selected ? 8 : 2,
                    shadowColor: '#333',
                    shadowOffset: { width: 0, height: 4 },
                    shadowOpacity: 0.2,
                    shadowRadius: 6,
                    borderColor: selected ? "#62DE98" : COLORS.txtA2A4A6,
                    borderWidth: 0.5,
                }}
            >
                <LinearGradient
                    colors={selected ? ['#E0F7EF', '#62DE98'] : [COLORS.bgFFFFFF, COLORS.bgFFFFFF]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                    style={{ borderRadius: 12 }}
                >
                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            padding: 16,
                        }}
                    >
                        <Icon
                            iconSet={"Ionicons"}
                            name={selected ? "ios-checkbox-outline" : "ios-square-outline"}
                            size={24}
                            color={selected ? COLORS.bg00AAFF : COLORS.bgE0E0E0}
                            style={{ marginRight: 12 }}
                        />
                        <View style={{ flex: 1 }}>
                            <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 10 }}>
                                <MyText
                                    text={"Chu kỳ:"}
                                    addSize={-1}
                                    style={{
                                        color: COLORS.bg000000,
                                        fontSize: 14,
                                        fontWeight: '500',
                                    }}
                                />
                                <MyText
                                    text={item?.ExtraData?.Month}
                                    addSize={-1}
                                    style={{
                                        color: COLORS.bg000000,
                                        fontWeight: '600',
                                        fontSize: 14,
                                    }}
                                />
                            </View>
                            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                <MyText
                                    text={"Số tiền:"}
                                    addSize={-1}
                                    style={{
                                        color: COLORS.bg000000,
                                        fontSize: 14,
                                        fontWeight: '500',
                                    }}
                                />
                                <MyText
                                    text={helper.formatMoney(item?.ExtraData?.MoneyAmount)}
                                    addSize={-1}
                                    style={{
                                        color: selected ? COLORS.bg1E88E5 : COLORS.bg000000,
                                        fontWeight: '600',
                                        fontSize: 14,
                                    }}
                                />
                            </View>
                        </View>
                    </View>
                </LinearGradient>
            </Pressable>
        );
    };

    const onClickItem = debounce((item) => {
        if (selectedItems.includes(item.PriceInforDetailID)) {
            if (paymentRule === 1) {
                setSelectedItems([]);
                setSelectedItemsArray([]);
            } else {
                const selectedIndex = selectedItems.indexOf(item.PriceInforDetailID);
                setSelectedItems((prevSelectedItems) => prevSelectedItems.slice(0, selectedIndex));
                setSelectedItemsArray((prevSelectedItemsArray) => prevSelectedItemsArray.slice(0, selectedIndex));
            }
            setProductPrice({});
        } else {
            if (paymentRule === 1) {
                const allItemIds = listPriceInfoDetailBO.map(item => item.PriceInforDetailID);
                const allItems = listPriceInfoDetailBO;
                setSelectedItems(allItemIds);
                setSelectedItemsArray(allItems);
            } else if (paymentRule === 2) {
                const selectedIndex = listPriceInfoDetailBO.findIndex((dataItem) => dataItem.PriceInforDetailID === item.PriceInforDetailID);
                const selectedIds = listPriceInfoDetailBO.slice(0, selectedIndex + 1).map(item => item.PriceInforDetailID);
                setSelectedItems(selectedIds);
                setSelectedItemsArray(listPriceInfoDetailBO.slice(0, selectedIndex + 1));
            } else if (paymentRule === 7) {
                setSelectedItems([item.PriceInforDetailID]);
                setSelectedItemsArray([item]);
            } else {
                setSelectedItems((prevSelectedItems) => [...prevSelectedItems, item.PriceInforDetailID]);
                const selectedItem = listPriceInfoDetailBO.find((dataItem) => dataItem.PriceInforDetailID === item.PriceInforDetailID);
                setSelectedItemsArray((prevSelectedItemsArray) => [...prevSelectedItemsArray, selectedItem]);
            }
            setProductPrice({});
        }
    }, 300);

    const handleGetProductPrice = async (ServiceCategoryID, AirtimeServiceGroupID, updateItemField, contractCode, singleItem, extraData, isQRCode, isSesstion) => {
        showBlockUI();
        setProductPrice({});
        setUpdateTotalAmount('');
        const uniqueItemsArray = helper.getUniqueID(singleItem);
        const data = {
            "catalogID": ServiceCategoryID,
            "serviceGroupID": AirtimeServiceGroupID,
            "airtimeTransactionTypeID": updateItemField?.AirtimeTransactionTypeID,
            "productID": updateItemField?.ProductID,
            "phoneNumber": contractCode,
            "dataInfo": uniqueItemsArray,
            "extraData": extraData,
            "isQRCode": isQRCode,
            "Sesstion": isSesstion
        };
        try {
            const response = await actionCollectInstallment.getCollectInstallmentPriceAndFeeService(data)
            setProductPrice(response);
            setIsLockPaiMoney(response?.PriceInfo?.ExtraData?.IsLockPaidMoney);
        } catch (err) {
            Alert.alert("", err.msgError, [
                {
                    text: "OK",
                    onPress: () => {
                        hideBlockUI();
                    },
                },
            ]);
        } finally {
            hideBlockUI();
        }
    };

    const handleReGetProductPrice = (ServiceCategoryID, AirtimeServiceGroupID, updateTotalAmount, updateItemField, contractCode, extraData, selectedItemsArray, isQRCode, billingInformation, productPrice) => {
        if ((updateTotalAmount < minAmount || updateTotalAmount > maxAmount) && (!isLockPaiMoney)) {
            Alert.alert("", `Số tiền khách hàng muốn thanh toán chỉ được nhập trong khoản ${helper.formatMoney(minAmount)} đến ${helper.formatMoney(maxAmount)}`);
            setUpdateTotalAmount('');
        } else if (selectedItemsArray.length == 0) {
            Alert.alert("", "Vui lòng chọn kỳ thanh toán")
            return false;
        } else {
            showBlockUI();
            const uniqueItemsArray = helper.getUniqueID(selectedItemsArray);
            const updateAirtimeTransactionTypeID = updateItemField?.AirtimeTransactionTypeID;
            const updateProductID = updateItemField?.ProductID;
            const session = billingInformation?.PriceInfo?.Session;
            const data = {
                "catalogID": ServiceCategoryID,
                "totalAmount": updateTotalAmount,
                "serviceGroupID": AirtimeServiceGroupID,
                "airtimeTransactionTypeID": updateAirtimeTransactionTypeID,
                "productID": updateProductID,
                "phoneNumber": contractCode,
                "dataInfo": uniqueItemsArray,
                "extraData": extraData,
                "isQRCode": isQRCode,
                "session": session
            }
            actionCollectInstallment.getCollectInstallmentPriceAndFeeService(data).then((response) => {
                hideBlockUI();
                Alert.alert(
                    '',
                    "Nhân Viên xác nhận kỹ Tài Khoản/ Số Hợp Đồng, thông tin Khách Hàng và số tiền cần thanh toán trước khi nhấn nút xác nhận",
                    [
                        {
                            text: translate('saleOrderPayment.btn_skip_uppercase'),
                            style: 'cancel'
                        },
                        {
                            text: translate('saleOrderPayment.btn_continue'),
                            style: 'default',
                            onPress: () => handleCreateService(updateTotalAmount, response, session, productPrice, PromotionGift, authenticate)
                        }
                    ]
                );
            }).catch((err) => {
                Alert.alert("", err.msgError, [
                    {
                        text: "OK",
                        onPress: () => {
                            hideBlockUI();
                        },
                    },
                ]);
            });
        }
    }

    const handleCreateService = (updateTotalAmount, response, session, productPrice, PromotionGift, authenticate) => {
        const { FeeInfo, PriceInfo } = response;
        const uniqueItemsArray = helper.getUniqueID(selectedItemsArray);
        showBlockUI();
        const data = {
            "catalogID": ServiceCategoryID,
            "serviceGroupID": AirtimeServiceGroupID,
            "totalAmount": updateTotalAmount,
            "airtimeTransactionTypeID": updateItemField?.AirtimeTransactionTypeID,
            "productID": updateItemField?.ProductID,
            "phoneNumber": contractCode,
            "airTimeTransactionBO": {
                "CustomerName": customerName,
                "Customeraddress": customerAddress,
                "CustomerPhone": '',
                "Price": PriceInfo?.Price,
                "productID": updateItemField?.ProductID,
                "amount": PriceInfo?.Amount,
                "fee": FeeInfo?.Fee,
                "phoneNumber": contractCode,
                "saleprice": PriceInfo?.SalePrice,
                "inputprice": PriceInfo?.InputPrice,
                "ExtraData": {
                    ...PriceInfo?.ExtraData ?? {},
                    "PromotionInfo": productPrice?.PromotionInfo || null,
                },
                "PartnerValue": uniqueItemsArray,
                "totalAmount": updateTotalAmount,
                "PromotionPlanCustomer": {},
                "VoucherConcernAuthenticate": {
                    "RequestID": '',
                    "CustomerIdentifyID": '',
                    "createCredential": '',
                    "principal": ''
                },
            },
            "session": session,
            "promotionGift": PromotionGift,
            "voucherConcernAuthenticate": authenticate
        }
        actionCollectInstallment.addToSaleOrderCart(data).then((response) => {
            hideBlockUI();
            goToPaymentSO(response);
        }).catch((err) => {
            Alert.alert("", err.msgError, [
                {
                    text: "OK",
                    onPress: () => {
                        hideBlockUI();
                    },
                },
            ]);
        });
    }

    const goToPaymentSO = (rpSaleOrderCart) => {
        const dataSaleOrderCart = rpSaleOrderCart.object;
        const SaleOrders = dataSaleOrderCart.SaleOrders[0];
        const { SaleOrderID } = SaleOrders;
        actionPaymentOrder
            .setDataSO({
                SaleOrderID: SaleOrderID,
                SaleOrderTypeID: 1000,
            })
            .then((success) => {
                hideBlockUI();
                navigation.navigate("SaleOrderPayment");
                actionPaymentOrder.getSaleOrderPayment(SaleOrderID);
                actionPaymentOrder.getReportPrinterSocket(1000);
                actionPaymentOrder.getDataQRTransaction(SaleOrderID);
                actionCollection.updateItemCatalog({
                    ...itemCatalog,
                    contractCode: "",
                    ProductID: "",
                    PhoneNumber: ""
                })
            });
    };

    const handleScanQrCode = async (barcode, ServiceCategoryID, AirtimeServiceGroupID, updateTotalAmount, updateItemField, contractCode, extraData, selectedItemsArray, isQRCode, billingInformation, productPrice) => {
        showBlockUI();
        const uniqueItemsArray = helper.getUniqueID(selectedItemsArray);
        const updateAirtimeTransactionTypeID = updateItemField?.AirtimeTransactionTypeID;
        const updateProductID = updateItemField?.ProductID;
        const session = billingInformation?.PriceInfo?.Session;
        const data = {
            "catalogID": ServiceCategoryID,
            "totalAmount": updateTotalAmount,
            "serviceGroupID": AirtimeServiceGroupID,
            "airtimeTransactionTypeID": updateAirtimeTransactionTypeID,
            "productID": updateProductID,
            "phoneNumber": contractCode,
            "dataInfo": uniqueItemsArray,
            "extraData": extraData,
            "isQRCode": isQRCode,
            "session": session,
            "voucherConcernAuthenticate": {
                "IdentityData": barcode
            }
        }
        try {
            const response = await actionCollectInstallment.getCollectInstallmentPriceAndFeeService(data)
            setAuthenticate(response?.keyPayBillDebtAmount)
            setShowGroupGift(!showGroupGift);

        } catch (err) {
            Alert.alert("", err.msgError, [
                {
                    text: "OK",
                    onPress: () => {
                        hideBlockUI();
                        setAuthenticate({})
                    },
                },
            ]);
        } finally {
            hideBlockUI();
        }

    }

    return (
        <View
            style={{
                flex: 1,
                backgroundColor: "white",
            }}
        >
            <BackHeader
                onGoBack={goBack}
                title={getTitleCatalog()}
            />
            <KeyboardAwareScrollView
                style={{
                    flex: 1,
                }}
                enableResetScrollToCoords={false}
                keyboardShouldPersistTaps="always"
                bounces={false}
                overScrollMode="always"
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                extraScrollHeight={60}
            >
                <SafeAreaView style={{
                    alignItems: 'center'
                }}>
                    <View style={{
                        width: constants.width - 20,
                        marginTop: 10
                    }}>
                        <View>
                            {AirtimeServiceGroupID === 10 && (

                                <TitleInputMoney
                                    title={"Số tiền muốn nạp"}
                                    isRequired={true}
                                    editable={true}
                                    style={{
                                        flex: 1,
                                        height: 45,
                                        borderWidth: 1,
                                        borderColor: amount > 0
                                            ? COLORS.bg0099E5
                                            : COLORS.bgE0E0E0,
                                        borderRadius: 5,
                                        paddingHorizontal: 5,
                                        marginBottom: 10
                                    }}
                                    placeholder={'0'}
                                    value={amount}
                                    onChange={
                                        (text) => {
                                            setAmount(text)
                                            setContractCode('');
                                            setProductList([]);
                                            setUpdateAirtime([]);
                                            setPaymentType({});
                                            setBillingInformation({});
                                            setUpdateItemField({});
                                            setSelectedItems([]);
                                            setSelectedItemsArray([]);
                                            setProductPrice({});
                                            setUpdateTotalAmount('');
                                            setAuthenticate({});
                                            setShowGroupGift(true);
                                        }
                                    }
                                    length={30}
                                />
                            )
                            }
                            {(AirtimeServiceGroupID !== 10 || (AirtimeServiceGroupID === 10 && amount > 0)) && (
                                < SearchInput
                                    onSubmit={() => getDataCollectInstallmentProductList()}
                                    inputText={contractCode}
                                    onChangeText={(text) => {
                                        setContractCode(text);
                                        setProductList([]);
                                        setUpdateAirtime([]);
                                        setPaymentType({});
                                        setBillingInformation({});
                                        setUpdateItemField({});
                                        setSelectedItems([]);
                                        setSelectedItemsArray([]);
                                        setProductPrice({});
                                        setUpdateTotalAmount('');
                                        setAuthenticate({});
                                        setShowGroupGift(true);
                                    }}
                                    onSubmitEditing={() => {
                                        setProductList([]);
                                        setUpdateAirtime([]);
                                        setPaymentType({});
                                        setBillingInformation({});
                                        setUpdateItemField({});
                                        setSelectedItems([]);
                                        setSelectedItemsArray([]);
                                        setProductPrice({});
                                        setUpdateTotalAmount('');
                                        setAuthenticate({});
                                        setShowGroupGift(true);
                                    }}
                                    onClearText={() => {
                                        setContractCode('');
                                        setProductList([]);
                                        setUpdateAirtime([]);
                                        setPaymentType({});
                                        setBillingInformation({});
                                        setUpdateItemField({});
                                        setSelectedItems([]);
                                        setSelectedItemsArray([]);
                                        setProductPrice({});
                                        setUpdateTotalAmount('');
                                        setAuthenticate({});
                                        setShowGroupGift(true);
                                    }}
                                    placeholder={"Vui lòng nhập mã khách hàng"}
                                    style={{
                                        marginBottom: 10
                                    }}
                                />
                            )
                            }
                        </View>
                        {
                            !!contractCode && helper.IsNonEmptyArray(productList) &&
                            <PickerSearch
                                title={"Loại thanh toán"}
                                label={"ProductName"}
                                value={"ProductID"}
                                valueSelected={paymentType?.ProductID}
                                data={helper.IsNonEmptyArray(productList) ? productList : []}
                                onChange={async (item) => {
                                    setBillingInformation({});
                                    setProductPrice({});
                                    setUpdateTotalAmount('');
                                    setPaymentType(item);
                                    setUpdateAirtime(item?.AirtimeTransactionType);
                                    const selectedItem = item?.AirtimeTransactionType?.[0];
                                    if (item?.AirtimeTransactionType?.length === 1) {
                                        setUpdateItemField(selectedItem);
                                        await handleGetPriceAndFeeService(
                                            ServiceCategoryID,
                                            AirtimeServiceGroupID,
                                            selectedItem,
                                            contractCode,
                                            isQRCode,
                                            amount
                                        );
                                    } else {
                                        setUpdateItemField(selectedItem);
                                    }
                                    // setBillingInformation({});
                                    // setProductPrice({});
                                }}
                                style={{
                                    flexDirection: "row",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    height: 40,
                                    borderRadius: 5,
                                    borderWidth: 1,
                                    borderColor: COLORS.bdCCCCCC,
                                    width: constants.width - 20,
                                    backgroundColor: COLORS.btnFFFFFF,
                                    marginBottom: 10,
                                    marginTop: 5
                                }}
                                disabled={false}
                                defaultLabel={"Chọn loại thanh toán"}
                            />
                        }
                        {
                            helper.IsNonEmptyArray(updateAirTime) &&
                            <View>
                                <PickerSearch
                                    title={"Nhà cung cấp"}
                                    label={"CustomerAlias"}
                                    value={"CustomerID"}
                                    valueSelected={updateItemField?.CustomerID}
                                    data={updateAirTime}
                                    onChange={async (item) => {
                                        setUpdateItemField(item);
                                        setSelectedItems([]);
                                        setSelectedItemsArray([]);
                                        setBillingInformation({});
                                        setProductPrice({});
                                        setUpdateTotalAmount('');
                                        if (productList?.length === 1) {
                                            await handleGetPriceAndFeeService(
                                                ServiceCategoryID,
                                                AirtimeServiceGroupID,
                                                item,
                                                contractCode,
                                                isQRCode,
                                                amount
                                            );
                                        }
                                    }}
                                    style={{
                                        flexDirection: "row",
                                        justifyContent: "center",
                                        alignItems: "center",
                                        height: 40,
                                        borderRadius: 5,
                                        borderWidth: 1,
                                        borderColor: COLORS.bdCCCCCC,
                                        width: constants.width - 20,
                                        backgroundColor: COLORS.btnFFFFFF,
                                        marginBottom: 10,
                                        marginTop: 5
                                    }}
                                    disabled={false}
                                    defaultLabel={"Vui lòng chọn nhà cung cấp"}
                                />
                            </View>
                        }
                        {
                            !helper.IsEmptyObject(updateItemField) &&
                            <ButtonIcon
                                onPress={() => handleGetPriceAndFeeService(
                                    ServiceCategoryID,
                                    AirtimeServiceGroupID,
                                    updateItemField,
                                    contractCode,
                                    isQRCode,
                                    amount
                                )}
                                title={"TRA CỨU"}
                                iconSet={"Ionicons"}
                                nameIcon={"search-outline"}
                                style={{
                                    backgroundColor: COLORS.bg00A896,
                                    marginTop: 10,
                                    padding: 10
                                }}
                            />
                        }
                        {
                            !helper.IsEmptyObject(billingInformation) && !helper.IsEmptyObject(paymentType) &&
                            <View>
                                <Border />
                                <View style={{
                                    flexDirection: 'row',
                                    padding: 5,
                                    marginTop: 5
                                }}>
                                    <MyText
                                        text={"Tên khách hàng"}
                                        addSize={-1.5}
                                        style={{
                                            color: COLORS.bg000000,
                                            fontSize: 15,
                                            marginLeft: 5
                                        }}
                                    />
                                    <View style={{ flex: 1 }} />
                                    <MyText
                                        text={customerName}
                                        addSize={-1.5}
                                        style={{
                                            color: COLORS.bg000000,
                                            fontSize: 15,
                                            fontWeight: 'bold'
                                        }}
                                    />
                                </View>
                                <TouchableOpacity
                                    onPress={() => setShow(!show)}
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        height: 35,
                                        width: '100%',
                                        marginBottom: 10
                                    }}>
                                    <Icon
                                        iconSet={"Ionicons"}
                                        name={show ? "caret-up-sharp" : "caret-down-sharp"}
                                        color={COLORS.bg2FB47C}
                                        size={15}
                                        style={{
                                            marginLeft: 5
                                        }}
                                    />
                                    <View style={{
                                        flexDirection: 'row',
                                        flexWrap: 'wrap'
                                    }}>
                                        <MyText
                                            text={"Địa chỉ: "}
                                            addSize={-1.5}
                                            style={{
                                                color: COLORS.bg000000,
                                                fontSize: 13,
                                                marginLeft: 10
                                            }}
                                        />
                                        <MyText
                                            text={customerAddress}
                                            addSize={-1.5}
                                            style={{
                                                color: COLORS.bg000000,
                                                fontSize: 13,
                                                flex: 1,
                                                flexWrap: 'wrap'
                                            }}
                                        />
                                    </View>
                                </TouchableOpacity>
                                <View>
                                    <FlatList
                                        data={listPriceInfoDetailBO}
                                        keyExtractor={(item, index) => `${index}`}
                                        renderItem={renderDenomination}
                                        stickySectionHeadersEnabled={true}
                                        alwaysBounceVertical={false}
                                        bounces={false}
                                        numColumns={1}
                                        scrollEventThrottle={16}
                                    />
                                    {paymentDescription != '' &&
                                        <View style={{
                                            flexDirection: 'row'
                                        }}>
                                            <MyText
                                                text={"Lưu ý:"}
                                                addSize={-1.5}
                                                style={{
                                                    color: COLORS.bgEA1D5D,
                                                    fontSize: 13,
                                                    textAlign: 'right',
                                                    width: '45%',
                                                    fontStyle: 'italic'
                                                }}
                                            />
                                            <MyText
                                                text={paymentDescription}
                                                addSize={-1.5}
                                                style={{
                                                    color: COLORS.bgEA1D5D,
                                                    fontSize: 13,
                                                    marginLeft: 5
                                                }}
                                            />
                                        </View>
                                    }
                                    {selectedItemsArray.length >= 1 &&
                                        <ButtonIcon
                                            onPress={() => getPromotionService(ServiceCategoryID, AirtimeServiceGroupID, updateItemField, contractCode, extraData, selectedItemsArray)}
                                            title={"TIẾP TỤC"}
                                            iconSet={"MaterialCommunityIcons"}
                                            nameIcon={"restore"}
                                            style={{
                                                backgroundColor: COLORS.bg00A896,
                                                marginTop: 10,
                                                padding: 10
                                            }}
                                        />
                                    }
                                    {
                                        !helper.IsEmptyObject(productPrice) && selectedItemsArray.length >= 1 &&
                                        <View style={{
                                            marginTop: 15,
                                            backgroundColor: 'white',
                                            borderRadius: 8,
                                            padding: 5,
                                            shadowColor: '#000',
                                            shadowOffset: {
                                                width: 0,
                                                height: 3
                                            },
                                            shadowOpacity: 0.1,
                                            shadowRadius: 6,
                                            elevation: 3,
                                            marginBottom: 5
                                        }}>
                                            <LinearGradient
                                                colors={['#FFFFFF', '#e0f7fa']}
                                                start={{ x: 0, y: 0 }}
                                                end={{ x: 1, y: 1 }}
                                                style={{
                                                    borderRadius: 8,
                                                    elevation: 3,
                                                }}
                                            >
                                                <View
                                                    style={{
                                                        padding: 5,
                                                    }}
                                                >
                                                    {
                                                        !!totalDebtRemain &&
                                                        <View
                                                            style={{
                                                                flexDirection: 'row',
                                                                marginTop: 10,
                                                            }}
                                                        >
                                                            <MyText
                                                                text={'Tổng nợ còn lại'}
                                                                addSize={-1.5}
                                                                style={{
                                                                    color: COLORS.bgEA1D5D,
                                                                    fontSize: 13,
                                                                    fontWeight: 'bold'
                                                                }}
                                                            />
                                                            <View style={{ flex: 1 }} />
                                                            <MyText
                                                                text={helper.formatMoney(totalDebtRemain)}
                                                                addSize={-1.5}
                                                                style={{
                                                                    color: COLORS.bgEA1D5D,
                                                                    fontSize: 13,
                                                                    fontWeight: 'bold'
                                                                }}
                                                            />
                                                        </View>
                                                    }
                                                    <View
                                                        style={{
                                                            flexDirection: 'row',
                                                            marginTop: 10,
                                                        }}
                                                    >
                                                        <MyText
                                                            text={'Tiền nợ cước'}
                                                            addSize={-1.5}
                                                            style={{
                                                                color: COLORS.bg8E8E93,
                                                                fontSize: 13,
                                                            }}
                                                        />
                                                        <View style={{ flex: 1 }} />
                                                        <MyText
                                                            text={helper.formatMoney(fareDebt)}
                                                            addSize={-1.5}
                                                            style={{
                                                                color: COLORS.bg000000,
                                                                fontSize: 13,
                                                            }}
                                                        />
                                                    </View>
                                                    <View
                                                        style={{
                                                            flexDirection: 'row',
                                                            marginTop: 10,
                                                        }}
                                                    >
                                                        <MyText
                                                            text={'Phí'}
                                                            addSize={-1.5}
                                                            style={{
                                                                color: COLORS.bg8E8E93,
                                                                fontSize: 13,
                                                            }}
                                                        />
                                                        <View style={{ flex: 1 }} />
                                                        <MyText
                                                            text={helper.formatMoney(fee)}
                                                            addSize={-1.5}
                                                            style={{
                                                                color: COLORS.bg000000,
                                                                fontSize: 13,
                                                            }}
                                                        />
                                                    </View>
                                                    {!!minAmount && (
                                                        <View
                                                            style={{
                                                                flexDirection: 'row',
                                                                marginTop: 10,
                                                            }}
                                                        >
                                                            <MyText
                                                                text={'Tiền tối thiểu'}
                                                                addSize={-1.5}
                                                                style={{
                                                                    color: COLORS.bg8E8E93,
                                                                    fontSize: 13,
                                                                }}
                                                            />
                                                            <View style={{ flex: 1 }} />
                                                            <MyText
                                                                text={helper.formatMoney(minAmount)}
                                                                addSize={-1.5}
                                                                style={{
                                                                    color: COLORS.bg000000,
                                                                    fontSize: 13,
                                                                }}
                                                            />
                                                        </View>
                                                    )}
                                                    <View
                                                        style={{
                                                            flexDirection: 'row',
                                                            marginTop: 10,
                                                            justifyContent: 'center',
                                                            alignItems: 'center',
                                                        }}
                                                    >
                                                        <MyText
                                                            text={'Tiền khách muốn thanh toán: '}
                                                            addSize={-1.5}
                                                            style={{
                                                                color: COLORS.bg1E88E5,
                                                                fontSize: 15,
                                                                fontWeight: 'bold',
                                                            }}
                                                        />
                                                        <TitleInputMoney
                                                            editable={!isLockPaiMoney}
                                                            style={{
                                                                flex: 1,
                                                                height: 40,
                                                                borderWidth: 1,
                                                                borderColor: isLockPaiMoney
                                                                    ? COLORS.bgF0F0F0
                                                                    : COLORS.bg0099E5,
                                                                borderRadius: 5,
                                                                textAlign: 'right',
                                                                paddingHorizontal: 5,
                                                                backgroundColor: isLockPaiMoney
                                                                    ? COLORS.bgE0E0E0
                                                                    : COLORS.bgFFFFFF,
                                                            }}
                                                            placeholder={'0'}
                                                            value={updateTotalAmount}
                                                            onChange={(text) => setUpdateTotalAmount(text)}
                                                            length={30}
                                                        />
                                                    </View>
                                                    {minMaxAmount !== '' && (
                                                        <View>
                                                            <MyText
                                                                text={minMaxAmount}
                                                                addSize={-1.5}
                                                                style={{
                                                                    color: COLORS.bg8E8E93,
                                                                    fontSize: 15,
                                                                    marginLeft: 2,
                                                                    fontStyle: 'italic',
                                                                    marginTop: 10,
                                                                }}
                                                            />
                                                        </View>
                                                    )}
                                                </View>
                                                {
                                                    promotionMessage !== "" &&
                                                    <Promotion message={promotionMessage} />
                                                }
                                                {
                                                    showGroupGift &&
                                                    <>
                                                        <TouchableOpacity
                                                            onPress={() => setIsGiftListVisible(!isGiftListVisible)}
                                                            activeOpacity={0.85}
                                                            style={{
                                                                paddingVertical: 12,
                                                                paddingHorizontal: 16,
                                                                backgroundColor: '#FFFFFF',
                                                                borderRadius: 12,
                                                                marginTop: 12,
                                                                shadowColor: '#000',
                                                                shadowOffset: { width: 0, height: 3 },
                                                                shadowOpacity: 0.08,
                                                                shadowRadius: 6,
                                                                elevation: 4,
                                                                borderWidth: 1,
                                                                borderColor: '#E0E0E0'
                                                            }}
                                                        >
                                                            <MyText
                                                                text={"Vui lòng quét mã trên app QTV nếu khách hàng muốn nhận khuyến mãi"}
                                                                addSize={-1}
                                                                style={{
                                                                    color: '#DC2525',
                                                                    fontSize: 14,
                                                                    fontWeight: '600',
                                                                    marginBottom: 8,
                                                                    textAlign: 'center'
                                                                }}
                                                            />

                                                            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                                                <TouchableOpacity
                                                                    onPress={() => setIsVisibleScan(true)}
                                                                    style={{
                                                                        width: 60,
                                                                        height: 60,
                                                                        borderRadius: 30,
                                                                        backgroundColor: '#00C853',
                                                                        justifyContent: 'center',
                                                                        alignItems: 'center',
                                                                        marginRight: 12,
                                                                        shadowColor: '#000',
                                                                        shadowOffset: { width: 0, height: 4 },
                                                                        shadowOpacity: 0.3,
                                                                        shadowRadius: 6,
                                                                        elevation: 8,
                                                                        borderWidth: 2,
                                                                        borderColor: '#FFFFFF',
                                                                    }}
                                                                >
                                                                    <Icon
                                                                        iconSet={"Ionicons"}
                                                                        name={"qr-code-outline"}
                                                                        size={28}
                                                                        color="#FFFFFF"
                                                                    />
                                                                </TouchableOpacity>

                                                                <Icon
                                                                    iconSet={"Ionicons"}
                                                                    name={"gift-outline"}
                                                                    color={"#4A90E2"}
                                                                    size={20}
                                                                    style={{ marginRight: 6 }}
                                                                />
                                                                <MyText
                                                                    text={"DANH SÁCH QUÀ TẶNG"}
                                                                    addSize={-1}
                                                                    style={{
                                                                        color: '#333333',
                                                                        fontSize: 15,
                                                                        fontWeight: '600',
                                                                        flex: 1
                                                                    }}
                                                                />
                                                                <View style={{ position: 'relative', marginLeft: 8 }}>
                                                                    <Icon
                                                                        iconSet={"Ionicons"}
                                                                        name={isGiftListVisible ? "chevron-up" : "chevron-down"}
                                                                        color={"#4A90E2"}
                                                                        size={20}
                                                                    />
                                                                    <View style={{
                                                                        position: 'absolute',
                                                                        top: -6,
                                                                        right: -10,
                                                                        backgroundColor: '#FF3B30',
                                                                        borderRadius: 10,
                                                                        width: 18,
                                                                        height: 18,
                                                                        justifyContent: 'center',
                                                                        alignItems: 'center'
                                                                    }}>
                                                                        <MyText
                                                                            text={PromotionGift.length}
                                                                            style={{
                                                                                color: '#FFFFFF',
                                                                                fontSize: 11,
                                                                                fontWeight: 'bold'
                                                                            }}
                                                                        />
                                                                    </View>
                                                                </View>
                                                            </View>
                                                        </TouchableOpacity>

                                                        {isGiftListVisible && (
                                                            <FlatList
                                                                data={PromotionGift}
                                                                keyExtractor={(item, index) => `${index}`}
                                                                renderItem={({ item }) => (
                                                                    <Promotion message={item.PromotionMessage} />
                                                                )}
                                                                contentContainerStyle={{ paddingVertical: 10 }}
                                                                showsVerticalScrollIndicator={false}
                                                            />
                                                        )}
                                                    </>
                                                }
                                                {
                                                       helper.IsNonEmptyArray(PromotionGift) && !helper.IsEmptyObject(authenticate) &&
                                                    <>
                                                        <TouchableOpacity
                                                            onPress={() => setIsShowGiftList(!isShowGiftList)}
                                                            activeOpacity={0.85}
                                                            style={{
                                                                flexDirection: 'row',
                                                                alignItems: 'center',
                                                                paddingVertical: 12,
                                                                paddingHorizontal: 16,
                                                                backgroundColor: '#FFFFFF',
                                                                borderRadius: 12,
                                                                marginTop: 12,
                                                                shadowColor: '#000',
                                                                shadowOffset: { width: 0, height: 3 },
                                                                shadowOpacity: 0.08,
                                                                shadowRadius: 6,
                                                                elevation: 4,
                                                                borderWidth: 1,
                                                                borderColor: '#E0E0E0'
                                                            }}
                                                        >
                                                            <TouchableOpacity
                                                                onPress={() => {
                                                                    setAuthenticate({});
                                                                    setShowGroupGift(true);
                                                                }}
                                                                activeOpacity={0.8}
                                                                style={{
                                                                    width: 40,
                                                                    height: 40,
                                                                    borderRadius: 20,
                                                                    backgroundColor: '#FFE5E5',
                                                                    justifyContent: 'center',
                                                                    alignItems: 'center',
                                                                    marginRight: 12,
                                                                    borderWidth: 1,
                                                                    borderColor: '#DC2525',
                                                                    shadowColor: '#DC2525',
                                                                    shadowOffset: { width: 0, height: 2 },
                                                                    shadowOpacity: 0.3,
                                                                    shadowRadius: 4,
                                                                    elevation: 4,
                                                                }}
                                                            >
                                                                <Icon
                                                                    iconSet="Ionicons"
                                                                    name="close"
                                                                    size={20}
                                                                    color="#DC2525"
                                                                />
                                                            </TouchableOpacity>

                                                            <Icon
                                                                iconSet={"Ionicons"}
                                                                name={"gift-outline"}
                                                                color={"#4A90E2"}
                                                                size={20}
                                                            />
                                                            <MyText
                                                                text={`DANH SÁCH QUÀ TẶNG\n SĐT: ${authenticate?.principal}`}
                                                                addSize={-1}
                                                                style={{
                                                                    color: '#333333',
                                                                    fontSize: 15,
                                                                    fontWeight: '600',
                                                                    marginLeft: 12,
                                                                    flex: 1,
                                                                    textAlign: 'center'
                                                                }}
                                                            />
                                                            <View style={{ position: 'relative', marginRight: 8 }}>
                                                                <Icon
                                                                    iconSet={"Ionicons"}
                                                                    name={isShowGiftList ? "chevron-up" : "chevron-down"}
                                                                    color={"#4A90E2"}
                                                                    size={20}
                                                                />
                                                                <View style={{
                                                                    position: 'absolute',
                                                                    top: -6,
                                                                    right: -10,
                                                                    backgroundColor: '#FF3B30',
                                                                    borderRadius: 10,
                                                                    width: 18,
                                                                    height: 18,
                                                                    justifyContent: 'center',
                                                                    alignItems: 'center'
                                                                }}>
                                                                    <MyText
                                                                        text={PromotionGift.length}
                                                                        style={{
                                                                            color: '#FFFFFF',
                                                                            fontSize: 11,
                                                                            fontWeight: 'bold'
                                                                        }}
                                                                    />
                                                                </View>
                                                            </View>
                                                        </TouchableOpacity>

                                                        {isShowGiftList && (
                                                            <FlatList
                                                                data={PromotionGift}
                                                                keyExtractor={(item, index) => `${index}`}
                                                                renderItem={({ item }) => (
                                                                    <Promotion message={item.PromotionMessage} />
                                                                )}
                                                                contentContainerStyle={{ paddingVertical: 10 }}
                                                                showsVerticalScrollIndicator={false}
                                                            />
                                                        )}
                                                    </>
                                                }
                                            </LinearGradient>
                                            <View style={{
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                                flexDirection: 'row',
                                                marginTop: 20
                                            }}>
                                                <ButtonAction
                                                    onPress={() => navigation.goBack()}
                                                    title={"ĐÓNG"}
                                                    style={{
                                                        borderWidth: 1,
                                                        borderColor: COLORS.bg00A896,
                                                        borderRadius: 5,
                                                        paddingVertical: 8,
                                                        paddingHorizontal: 20,
                                                    }}
                                                    styleText={{
                                                        color: COLORS.bg00A896,
                                                        fontWeight: 'bold'
                                                    }}
                                                    opacity={1}
                                                    disabled={false}
                                                />
                                                <View style={{ flex: 1 }} />
                                                <ButtonAction
                                                    onPress={() => handleReGetProductPrice(ServiceCategoryID, AirtimeServiceGroupID, updateTotalAmount, updateItemField, contractCode, extraData, selectedItemsArray, isQRCode, billingInformation, productPrice)}
                                                    title={"XÁC NHẬN"}
                                                    style={{
                                                        backgroundColor: COLORS.bg00A896,
                                                        borderRadius: 5,
                                                        paddingVertical: 8,
                                                        paddingHorizontal: 20,
                                                    }}
                                                    styleText={{
                                                        color: COLORS.bgFFFFFF,
                                                        fontWeight: 'bold'
                                                    }}
                                                    opacity={1}
                                                    disabled={false}
                                                />
                                            </View>
                                        </View>
                                    }
                                </View>

                            </View>
                        }
                    </View>
                </SafeAreaView>
                {isVisibleScan && (
                    <BarcodeCamera
                        isVisible={isVisibleScan}
                        closeCamera={() => setIsVisibleScan(false)}
                        resultScanBarcode={(barcode) => {
                            handleScanQrCode(barcode, ServiceCategoryID, AirtimeServiceGroupID, updateTotalAmount, updateItemField, contractCode, extraData, selectedItemsArray, isQRCode, billingInformation, productPrice)
                            setIsVisibleScan(false);
                        }}
                    />
                )}
            </KeyboardAwareScrollView>
        </View>

    )
}

const styles = StyleSheet.create({});

const mapStateToProps = function (state) {
    return {
        itemCatalog: state.collectionReducer.itemCatalog
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionCollectInstallment: bindActionCreators(actionCollectInstallmentCreator, dispatch),
        actionPaymentOrder: bindActionCreators(actionPaymentOrderCreator, dispatch),
        actionCollection: bindActionCreators(actionCollectionCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(PayBillAirtimeService);
