import { API_CONST } from "@constants";
import { helper, dateHelper } from "@common";
import { apiBase, METHOD, ERROR, EMPTY, SUCCESS } from "@config";
import { translate } from "@translate";

const {
    API_GET_ELECTRICITY_PRODUCT_LIST,
    API_GET_ELECTRICITY_PRICE_AND_FEE,
    API_CREATE_SERVICE_REQUEST,
    API_GET_PROMOTION_SERVICE,
    API_CHECK_PROMOTION_ELECTRICITY,

    API_GET_SERVICE_LIST,
    API_VALIDATE_DATA_SERVICE_REQUEST,
    API_GET_PRICE_SERVICE,
    API_GET_CREATE_SERVICE_REQUEST,
    API_GET_DATA_COLLECTION_MANAGER_NEW,
    API_GET_INFO_REFUND,
    API_CREATE_AIRTIME_REFUND,
    API_CHECK_STATUS_TICKET_SERVICE,
    API_GET_SERVICE_LIST_HISTORY_REFUND,
    API_GET_PROCESSOUT_VOUCHER,
    API_GET_CANCEL_AND_CREATE_AIRTIME,
    API_GET_QUERY_STATUS,
    API_GET_DATA_INFO
} = API_CONST;

const START_ADD_TO_SALE_ORDER_CART = "START_ADD_TO_SALE_ORDER_CART";
const STOP_ADD_TO_SALE_ORDER_CART = "STOP_ADD_TO_SALE_ORDER_CART";

const START_GET_SERVICE_LIST = "START_GET_SERVICE_LIST";
const STOP_GET_SERVICE_LIST = "STOP_GET_SERVICE_LIST";
const START_VALIDATE_DATA_SERVICE_REQUEST = "START_VALIDATE_DATA_SERVICE_REQUEST";
const STOP_VALIDATE_DATA_SERVICE_REQUEST = "STOP_VALIDATE_DATA_SERVICE_REQUEST";
const CLEAR_DATA_VALIDATE_SERVICE_REQUEST = 'CLEAR_DATA_VALIDATE_SERVICE_REQUEST';
const START_SEARCH_HISTORY_INSURANCE = "START_SEARCH_HISTORY_INSURANCE";
const STOP_SEARCH_HISTORY_INSURANCE = "STOP_SEARCH_HISTORY_INSURANCE";
const START_GET_CREATE_AIRTIME_REFUND = "START_GET_CREATE_AIRTIME_REFUND";
const STOP_GET_CREATE_AIRTIME_REFUND = "STOP_GET_CREATE_AIRTIME_REFUND";
const UPDATE_ITEM_SELECTED_PRINT = 'UPDATE_ITEM_SELECTED_PRINT';


export const actionCollectInstallment = {
    START_ADD_TO_SALE_ORDER_CART,
    STOP_ADD_TO_SALE_ORDER_CART,

    START_GET_SERVICE_LIST,
    STOP_GET_SERVICE_LIST,
    START_VALIDATE_DATA_SERVICE_REQUEST,
    STOP_VALIDATE_DATA_SERVICE_REQUEST,
    CLEAR_DATA_VALIDATE_SERVICE_REQUEST,
    START_SEARCH_HISTORY_INSURANCE,
    STOP_SEARCH_HISTORY_INSURANCE,
    START_GET_CREATE_AIRTIME_REFUND,
    STOP_GET_CREATE_AIRTIME_REFUND,
    UPDATE_ITEM_SELECTED_PRINT
};

export const getCollectInstallmentProductList = function (data) {
    return function (_dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "catalogID": data.catalogID,
                "serviceGroupID": data.serviceGroupID,
                "isLoadProductInfo": 1,
                "groupBys": "ProductID,ProductName",
                "phoneNumber": data.phoneNumber
            };
            apiBase(API_GET_ELECTRICITY_PRODUCT_LIST, METHOD.POST, body)
                .then((response) => {
                    console.log("getCollectInstallmentProductList success", response);
                    if (helper.IsNonEmptyArray(response.object)) {
                        resolve(response.object);
                    } else {
                        console.log("Data BE không có dữ liệu")
                        reject("Không tìm thấy thông tin dữ liệu từ Service !!!")
                    }
                })
                .catch((error) => {
                    console.log("getCollectInstallmentProductList error", error);
                    reject(error);
                });
        });
    };
};

export const getCollectInstallmentPriceAndFeeService = function (data) {
    return function (_dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "catalogID": data.catalogID,
                "serviceGroupID": data.serviceGroupID,
                "totalAmount": data.totalAmount,
                "airtimeTransactionTypeID": data.airtimeTransactionTypeID,
                "productID": data.productID,
                "phoneNumber": data.phoneNumber,
                "dataInfo": data.dataInfo,
                "extraData": data.extraData,
                "isQRCode": data.isQRCode,
                "session": data.session,
                "amount": data.amount
            };
            apiBase(API_GET_ELECTRICITY_PRICE_AND_FEE, METHOD.POST, body)
                .then((response) => {
                    console.log("getCollectInstallmentPriceAndFeeService success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("getCollectInstallmentPriceAndFeeService error", error);
                    reject(error);
                });
        });
    };
};

export const addToSaleOrderCart = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "catalogID": data.catalogID,
                "serviceGroupID": data.serviceGroupID,
                "totalAmount": data.totalAmount,
                "airtimeTransactionTypeID": data.airtimeTransactionTypeID,
                "phoneNumber": data.phoneNumber,
                "airTimeTransactionBO": data.airTimeTransactionBO,
                "session": data.session,
                "promotionGift": data.promotionGift
                // "PromotionPlanCustomer": data.PromotionPlanCustomer,
                // "VoucherConcernAuthenticate": data.VoucherConcernAuthenticate
            };
            dispatch(start_add_to_sale_order_cart());
            apiBase(API_CREATE_SERVICE_REQUEST, METHOD.POST, body)
                .then((response) => {
                    console.log("addToSaleOrderCart success", response);
                    const { object } = response;
                    if (!helper.IsEmptyObject(object)) {
                        dispatch(stop_add_to_sale_order_cart(object));
                        resolve(response);
                    } else {
                        dispatch(stop_add_to_sale_order_cart({}));
                        reject({ msgError: translate("saleOrder.error_create_order") });
                    }
                })
                .catch((error) => {
                    dispatch(stop_add_to_sale_order_cart(!SUCCESS, {}));
                    console.log("addToSaleOrderCart error", error);
                    reject(error);
                });
        });
    };
};

export const getPromotionService = function (data) {
    return function (_dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "catalogID": data.catalogID,
                "serviceGroupID": data.serviceGroupID,
                "airtimeTransactionTypeID": data.airtimeTransactionTypeID,
                "productID": data.productID,
                "phoneNumber": data.phoneNumber
            };
            apiBase(API_GET_PROMOTION_SERVICE, METHOD.POST, body)
                .then((response) => {
                    console.log("getPromotionService success", response);
                    if (!helper.IsEmptyObject(response)) {
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("getPromotionService error", error);
                    reject(error);
                });
        });
    };
};

export const checkPromotionCollectInstallment = function (data) {
    return function (_dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "catalogID": data.catalogID,
                "serviceGroupID": data.serviceGroupID,
                "airtimeTransactionTypeID": data.airtimeTransactionTypeID,
                "productID": data.productID,
                "contractID": data.contractID,
                "customerID": data.customerID,
                "promotionID": data.promotionID,
                "promotionPlanID": data.promotionPlanID,
                "giftProductID": data.giftProductID,
                "giftProductName": data.giftProductName
            };
            apiBase(API_CHECK_PROMOTION_ELECTRICITY, METHOD.POST, body)
                .then((response) => {
                    console.log("checkPromotionCollectInstallment success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("checkPromotionCollectInstallment error", error);
                    reject(error);
                });
        });
    };
};

export const getServiceList = function (data) {
    return (dispatch, getState) => {
        const body = {
            loginStoreId: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            moduleID: getState().userReducer.moduleID,
            catalogID: data.catalogID,
            serviceGroupID: data.serviceGroupID
        }
        dispatch(start_get_service_list())
        apiBase(API_GET_SERVICE_LIST, METHOD.POST, body)
            .then((response) => {
                console.log("getServiceList success", response);
                if (helper.IsNonEmptyArray(response.object)) {
                    dispatch(stop_get_service_list(response.object, false, '', false));
                } else {
                    dispatch(stop_get_service_list([], true, 'Không lấy được dữ liệu', false));
                }
            }).catch(error => {
                dispatch(stop_get_service_list([], false, error.msgError, true))
                console.log("getServiceList error", error);
            })
    }
};

export const validateDataServiceRequest = function (data) {
    return (dispatch, getState) => {
        const body = {
            loginStoreId: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            moduleID: getState().userReducer.moduleID,
            catalogID: data.catalogID,
            serviceGroupID: data.serviceGroupID,
            airtimeTransactionTypeID: data.airtimeTransactionTypeID,
            isLoadProductInfo: 1,
            groupBys: "ProductID,ProductName",
            insuranceID: data.insuranceID,
            insCustomerID: data.insCustomerID,
            imei: data.imei
        }
        dispatch(start_validate_data_service_request())
        apiBase(API_VALIDATE_DATA_SERVICE_REQUEST, METHOD.POST, body)
            .then((response) => {
                console.log("validateDataServiceRequest success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    dispatch(stop_validate_data_service_request(response.object, false, '', false));
                } else {
                    dispatch(stop_validate_data_service_request({}, true, 'Không lấy được dữ liệu', false));
                }
            }).catch(error => {
                dispatch(stop_validate_data_service_request({}, false, error.msgError, true))
                console.log("validateDataServiceRequest error", error);
            })
    }
};

export const getPriceService = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                productID: data.productID,
                retailPriceVAT: data.retailPriceVAT,
                outputDate: data.outputDate,
                insuranceMonth: data.insuranceMonth,
                imei: data.imei,
                mainProductID: data.mainProductID,
                productPromotion: data.productPromotion
            };
            apiBase(API_GET_PRICE_SERVICE, METHOD.POST, body)
                .then((response) => {
                    console.log("getPriceService success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("getPriceService error", error);
                    reject(error.msgError);
                });
        });
    };
};


export const getSearchHistoryInsurance = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                Keyword: data.keyword.length > 0 ? data.keyword : '',
                ProcessUser: getState().userReducer.userName,
                FromDate: dateHelper.formatDateYYYYMMDD(data.fromDate) + "T00:00:00",
                ToDate: dateHelper.formatDateYYYYMMDD(data.toDate) + "T23:59:59",
                IsDeleted: false,
                airtimetrsTypeIDList: data.airtimetrsTypeIDList ? data.airtimetrsTypeIDList : '',
                searchType: data.searchType ? data.searchType : ''
            }
            dispatch(start_search_history_insurance())
            apiBase(API_GET_DATA_COLLECTION_MANAGER_NEW, METHOD.POST, body)
                .then((response) => {
                    console.log("getSearchHistoryInsurance BODY", response.object);
                    if (
                        helper.hasProperty(response, 'object') &&
                        helper.isArray(response.object) &&
                        response.object.length > 0
                    ) {
                        console.log("getSearchHistoryInsurance success", response);
                        dispatch(stop_search_history_insurance(response.object));
                        resolve(response.object);
                    } else {
                        dispatch(stop_search_history_insurance([], false, 'Không tìm thấy đơn hàng', true));
                        reject("Lỗi lấy thông tin dữ liệu");
                    }
                })
                .catch((error) => {
                    console.log('getSearchHistoryInsurance err', error);
                    dispatch(stop_search_history_insurance([], !EMPTY, error.msgError, ERROR));
                    reject("Lỗi lấy thông tin dữ liệu");
                });
        })
    }
}

export const getServiceListHistory = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                isLoadSearchBy: data.isLoadSearchBy
            }
            dispatch(start_get_service_list())
            apiBase(API_GET_SERVICE_LIST, METHOD.POST, body)
                .then((response) => {
                    console.log("getServiceListHistory success", response);
                    if (helper.IsNonEmptyArray(response.object)) {
                        resolve(response.object)
                    } else {
                        reject('Không lấy được dữ liệu')
                    }
                }).catch(error => {
                    reject(error.msgError)
                    console.log("getServiceListHistory error", error);
                })
        }
        )

    }
};

export const getInfoRefund = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                productID: data.productID,
                inputTime: data.inputTime,
                serviceVoucherID: data.serviceVoucherID
            };
            apiBase(API_GET_INFO_REFUND, METHOD.POST, body)
                .then((response) => {
                    console.log("getInfoRefund success", response);
                    if (!helper.IsEmptyObject(response)) {
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("getInfoRefund error", error);
                    reject(error.msgError);
                });
        });
    };
};

export const createAirtimeRefund = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                airtimeTransactionID: data.airtimeTransactionID,
                totalRefundAmount: data.totalRefundAmount,
                rqRefundTypeID: data.rqRefundTypeID,
                reasonID: data.reasonID,
                refundNote: data.refundNote,
                approvedUser: data.approvedUser,
                approvedDate: data.approvedDate,
                correctAmount: data.correctAmount,
                voucherConcern: data.voucherConcern
            };
            dispatch(start_get_create_airtime_refund());
            apiBase(API_CREATE_AIRTIME_REFUND, METHOD.POST, body)
                .then((response) => {
                    console.log("createAirtimeRefund success", response);
                    if (!helper.IsEmptyObject(response)) {
                        resolve(response.object);
                        dispatch(stop_get_create_airtime_refund(response.object, false, '', false));
                    }
                })
                .catch((error) => {
                    console.log("createAirtimeRefund error", error);
                    dispatch(stop_get_create_airtime_refund({}, false, error.msgError, true))
                    reject(error.msgError);
                });
        });
    };
};

export const checksSatusTicketService = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                ticketID: data.TICKETID,
                airtimeTransactionID: data.AIRTIMETRANSACTIONID
            };
            apiBase(API_CHECK_STATUS_TICKET_SERVICE, METHOD.POST, body).then((response) => {
                console.log("checksSatusTicketService success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                }
                else {
                    resolve("Lỗi lấy thông tin dữ liệu");
                }
            }).catch(error => {
                console.log("checksSatusTicketService error", error);
                reject(error.msgError)
            })
        });
    }
}

export const getQuerysTatusServiceRequest = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                airtimeTransactionID: data.airtimeTransactionID,
                airtimeTransactionRFID: data.airtimeTransactionRFID
            };
            apiBase(API_GET_CANCEL_AND_CREATE_AIRTIME, METHOD.POST, body)
                .then((response) => {
                    console.log("getQuerysTatusServiceRequest success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("getQuerysTatusServiceRequest error", error);
                    reject(error.msgError);
                });
        });
    };
};

export const getServiceListHistoryRefund = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                Keyword: data.keyword.length > 0 ? data.keyword : '',
                ProcessUser: getState().userReducer.userName,
                FromDate: dateHelper.formatDateYYYYMMDD(data.fromDate) + "T00:00:00",
                ToDate: dateHelper.formatDateYYYYMMDD(data.toDate) + "T23:59:59",
                airtimetrsTypeIDList: data.airtimetrsTypeIDList ? data.airtimetrsTypeIDList : '',
                searchType: data.searchType ? data.searchType : '',
                approvalStatus: data.approvalStatus
            }
            dispatch(start_search_history_insurance())
            apiBase(API_GET_SERVICE_LIST_HISTORY_REFUND, METHOD.POST, body)
                .then((response) => {
                    console.log("getSearchHistoryInsurance BODY", response.object);
                    if (
                        helper.hasProperty(response, 'object') &&
                        helper.isArray(response.object) &&
                        response.object.length > 0
                    ) {
                        console.log("getSearchHistoryInsurance success", response);
                        dispatch(stop_search_history_insurance(response.object));
                        resolve(response.object);
                    } else {
                        dispatch(stop_search_history_insurance([], false, 'Không tìm thấy đơn hàng', true));
                        reject("Lỗi lấy thông tin dữ liệu");
                    }
                })
                .catch((error) => {
                    console.log('getSearchHistoryInsurance err', error);
                    dispatch(stop_search_history_insurance([], !EMPTY, error.msgError, ERROR));
                    reject("Lỗi lấy thông tin dữ liệu");
                });
        })
    }
}

export const getServiceListRefund = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                isLoadSearchBy: data.isLoadSearchBy,
                "isSearchByRefund": data.isLoadSearchBy,
            }
            dispatch(start_get_service_list())
            apiBase(API_GET_SERVICE_LIST, METHOD.POST, body)
                .then((response) => {
                    console.log("getServiceListHistory success", response);
                    if (helper.IsNonEmptyArray(response.object)) {
                        resolve(response.object)
                    } else {
                        reject('Không lấy được dữ liệu')
                    }
                }).catch(error => {
                    reject(error.msgError)
                    console.log("getServiceListHistory error", error);
                })
        }
        )

    }
};
export const getStatusHistory = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                airtimeTransactionID: data.airtimeTransactionID,
                airtimeTransactionRFID: data.airtimeTransactionRFID
            }
            apiBase(API_GET_PROCESSOUT_VOUCHER, METHOD.POST, body)
                .then((response) => {
                    console.log('get statushistory success', response);
                    if (!helper.IsEmptyObject(response.object)) {
                        resolve(response.object);
                    }
                }).catch((error) => {
                    console.log('get statushistory error', error);
                    reject(error);
                })
        })
    }
}

export const queryTransactionPartner = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                productID: data.productID,
                saleOrderID: data.saleOrderID
            };
            apiBase(API_GET_QUERY_STATUS, METHOD.POST, body).then((response) => {
                console.log("queryTransactionPartner success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                }
                else {
                    reject("Lỗi lấy thông tin dữ liệu");
                }
            }).catch(error => {
                console.log("queryTransactionPartner error", error);
                reject(error.msgError)
            })
        });
    }
}

export const getDataInfo = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "serviceVoucherID": data.serviceVoucherID
            };
            apiBase(API_GET_DATA_INFO, METHOD.POST, body).then((response) => {
                console.log("getDataInfo success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                }
                else {
                    reject("Lỗi lấy thông tin dữ liệu");
                }
            })
                .catch((error) => {
                    console.log("getDataInfo error", error);
                    reject(error)
                });
        });
    };
};

export const start_add_to_sale_order_cart = () => {
    return {
        type: START_ADD_TO_SALE_ORDER_CART,
    };
};

export const stop_add_to_sale_order_cart = (dataSaleOrderCart = {}) => {
    return {
        type: STOP_ADD_TO_SALE_ORDER_CART,
        dataSaleOrderCart,
    };
};


export const start_get_service_list = () => {
    return {
        type: START_GET_SERVICE_LIST,
    };
};

export const stop_get_service_list = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_SERVICE_LIST,
    data,
    isEmpty,
    description,
    isError
});

export const start_validate_data_service_request = () => {
    return {
        type: START_VALIDATE_DATA_SERVICE_REQUEST,
    };
};

export const stop_validate_data_service_request = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_VALIDATE_DATA_SERVICE_REQUEST,
    data,
    isEmpty,
    description,
    isError
});

export const clear_data_validate_service_request = () => ({
    type: CLEAR_DATA_VALIDATE_SERVICE_REQUEST
});

export const start_search_history_insurance = () => {
    return {
        type: START_SEARCH_HISTORY_INSURANCE,
    };
};

export const stop_search_history_insurance = (data, isEmpty = false, description = '', isError = false) => ({
    type: STOP_SEARCH_HISTORY_INSURANCE,
    data,
    isEmpty,
    description,
    isError
});

export const start_get_create_airtime_refund = () => {
    return {
        type: START_GET_CREATE_AIRTIME_REFUND,
    };
};

export const stop_get_create_airtime_refund = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_CREATE_AIRTIME_REFUND,
    data,
    isEmpty,
    description,
    isError
});

const IDENTIFY_CODE = {
    "IDENTIFY_CODE_NOT_FOUND": "Mã định danh không tồn tại. Vui lòng thử lại.",
    "PRINCIPAL_VERIFICATION_INVALID": "Mã định danh không hợp lệ. Vui lòng thử lại.",
    "IDENTIFY_WRONG_KEY": "Mã định danh đã hết hiệu lực. Vui lòng thử lại.",
}

const update_item_selected_print = (
    data
) => ({
    type: UPDATE_ITEM_SELECTED_PRINT,
    data
});

export const updateItemSelectedPrint = (data) => {
    return function (dispatch, getState) {
        dispatch(update_item_selected_print(data));
    }
}


