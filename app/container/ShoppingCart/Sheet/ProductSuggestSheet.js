import React, {
    forwardRef,
    useRef,
    useImperativeHandle,
    useEffect,
    useState
} from 'react';
import {
    Image,
    Platform,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { KeyboardAwareFlatList } from 'react-native-keyboard-aware-scroll-view';
import { BaseLoading, ImageURI, MyText } from '@components';
import { COLORS } from '@styles';
import { constants } from '@constants';
import { Sheet } from '../../AnKhangNew/components';
import { helper } from '@common';

const ProductSuggestSheet = forwardRef(
    ({ products, disabled, onGoNext, getCurrentIndex, ...props }, ref) => {
        const sheetRef = useRef(null);
        const [listShowMore, setListShowMore] = useState({});
        const handleShowMore = (productID) => {
            setListShowMore((prevState) => ({
                ...prevState,
                [productID]: !listShowMore[productID],
            }))
        }
        useEffect(() => {
            if (helper.IsNonEmptyArray(products)
            ) {
                products.forEach(product => setListShowMore((prevState) => ({
                    ...prevState,
                    [product.ProductID]: false,
                })));
            }
        }, [products])
        useImperativeHandle(ref, () => ({
            snapToIndex: (index) => {
                sheetRef.current.snapToIndex(index);
            },
            close: () => {
                sheetRef.current.close();
            }
        }));

        return (
            <Sheet
                scrollWrap={false}
                handleStyle={
                    {
                        borderBottomColor: COLORS.bg2FB47C,
                        backgroundColor: COLORS.bg2FB47C,
                        borderTopLeftRadius: 15,
                        borderTopRightRadius: 15
                    }
                }
                contentContainerStyle={{
                    backgroundColor: COLORS.bgF5F5F5
                }}
                isShowButton={false}
                ref={sheetRef}
                Header={<Header title="Sản phẩm gợi ý" />}
                {...props}>
                <BaseLoading
                    content={
                        <View
                            style={{
                                justifyContent: 'center',
                                alignItems: 'center'
                            }}>
                            <KeyboardAwareFlatList
                                data={products}
                                contentContainerStyle={{
                                    paddingBottom: 25,
                                    width: constants.width - 20
                                }}
                                renderItem={({
                                    item,
                                    index
                                }) => (
                                    <View style={{}}>
                                        <View
                                            // activeOpacity={0.5}
                                            style={styles.card}
                                        >
                                            <ImageURI
                                                uri={item.ProductIMG}
                                                style={styles.thumb}
                                                resizeMode={"contain"}
                                            />
                                            <View style={styles.infoContainer}>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        onGoNext(item);
                                                    }}>
                                                    <Text style={styles.name}>
                                                        {item.ProductName}
                                                    </Text>
                                                </TouchableOpacity>

                                                <View style={{}} >
                                                    {item.Reason?.length > 25 ? (
                                                        listShowMore[item.ProductID] ? (
                                                            <TouchableOpacity
                                                                style={{ paddingVertical: 5 }}
                                                                activeOpacity={0.5}
                                                                onPress={() => { handleShowMore(item.ProductID) }}
                                                            >
                                                                <MyText style={styles.body} addSize={-7} text={item.Reason} />
                                                                <Text style={{
                                                                    color: COLORS.bg1E88E5,
                                                                    fontSize: 13,
                                                                    fontStyle: 'italic',
                                                                    textDecorationLine: 'underline',
                                                                }}>Thu gọn</Text>
                                                            </TouchableOpacity>
                                                        ) : (
                                                            <TouchableOpacity
                                                                style={{ paddingVertical: 5 }}
                                                                activeOpacity={0.5}
                                                                onPress={() => { handleShowMore(item.ProductID) }}
                                                            >
                                                                <MyText style={styles.body} addSize={-2} text={`${item.Reason.slice(0, 25)}... `} />
                                                                <Text style={{
                                                                    color: COLORS.bg1E88E5,
                                                                    fontSize: 12,
                                                                    fontStyle: 'italic',
                                                                    textDecorationLine: 'underline',
                                                                }}>Xem thêm</Text>
                                                            </TouchableOpacity>
                                                        )
                                                    ) : (
                                                        <Text style={styles.body}>{item.Reason}</Text>
                                                    )}
                                                </View>
                                            </View>

                                        </View>
                                    </View>
                                )}
                                keyExtractor={(_, index) => index.toString()}
                                showsVerticalScrollIndicator={false}
                                showsHorizontalScrollIndicator={false}
                                removeClippedSubviews={
                                    Platform.OS === 'android'
                                }
                                directionalLockEnabled
                                keyboardShouldPersistTaps="always"
                            />
                        </View>
                    }
                />
            </Sheet>
        );
    }
);

export default ProductSuggestSheet;

const Header = ({ title = 'TIÊU ĐỀ' }) => {
    return (
        <View
            style={{
                borderBottomWidth: 1,
                borderBottomColor: COLORS.bg2FB47C,
                backgroundColor: COLORS.bg2FB47C,
                width: constants.width
            }}>
            <MyText
                selectable={false}
                style={{
                    fontSize: 16,
                    textAlign: 'center',
                    paddingBottom: 8,
                    fontWeight: '500',
                    marginHorizontal: 8,
                    color: "white"
                }}
                numberOfLines={2}
                text={title}
            />
        </View>
    );
};


const styles = StyleSheet.create({
    card: {
        alignItems: 'center',
        backgroundColor: "white",
        borderRadius: 5,
        elevation: 5,
        flexDirection: 'row',
        marginHorizontal: 5,
        marginVertical: 6,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 0 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
    },
    infoContainer: {
        padding: 16,
        width: '75%',

    },
    name: {
        fontSize: 15,
        fontStyle: 'italic',
        fontWeight: 'bold',
        color: "#6096B4"
    },
    price: {
        color: "#6096B4",
        fontSize: 13,
        fontWeight: '600',
        marginBottom: 8
    },
    thumb: {
        height: 50,
        width: '20%',
        marginLeft: 5
    },
    body: {
        color: COLORS.txt808080,
        fontSize: 13,
    }
});
