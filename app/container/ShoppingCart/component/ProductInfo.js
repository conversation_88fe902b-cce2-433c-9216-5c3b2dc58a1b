import React, { useState, useEffect } from 'react';
import {
    View,
    TouchableOpacity,
    Alert,
    StyleSheet,
    Image
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import {
    Icon,
    MyText,
    showBlock<PERSON>,
    hideBlock<PERSON>
} from "@components";
import { ENUM, constants } from '@constants';
import { helper } from "@common";
import PromotionGift from "./PromotionGift";
import PromotionSale from "./PromotionSale";
import DeliveryInfo from "./DeliveryInfo";
import { translate } from '@translate';
import { COLORS } from "@styles";
import BatchInfo from '../../AnKhangPharmacy/components/BatchInfo';
import { loadInforBatchNoByCart, setBatchNoByCart } from '../action';
const { SALE } = ENUM.SALE_SCENARIO_TYPE;

const MainProduct = ({
    product,
    onDelete,
    isApplyCoupon,
    applyDetailIDs,
    isBK,
    onChangeSheet
}) => {
    const {
        ProductName,
        SaleProgramInfo,
        IMEI,
        Quantity,
        QuantityUnitName,
        RetailPriceVAT,
        OutputStoreID,
        OutputStoreName,
        AdjustPriceTypeID,
        AdjustPrice,
        InventoryStatusID,
        InventoryStatusName,
        SaleOrderDetailID,
        SalePriceBKVAT
    } = product;
    const { storeID } = useSelector((state) => state.userReducer);
    const isAdjust = !isApplyCoupon && (AdjustPrice != 0);
    const statusName = (InventoryStatusID != 1) ? ` (${InventoryStatusName})` : "";
    const isNonCartPromotion = (applyDetailIDs.size == 0);
    const IsApplyTotalPromotion = isNonCartPromotion || applyDetailIDs.has(SaleOrderDetailID);
    const priceVAT = isBK ? SalePriceBKVAT : RetailPriceVAT;
    const labelKey = !!SaleProgramInfo ? translate(constants.ADJUST_PRICE_LABEL[AdjustPriceTypeID]) : (helper.configStoreCreatePrice(storeID) ? "Phiếu mua hàng hỗ trợ chiến giá" : translate(constants.ADJUST_PRICE_LABEL[AdjustPriceTypeID]));
    const imagePrice = getImagePrice()
    const onPressDelete = () => {
        Alert.alert("",
            `${translate('shoppingCart.warning_delete_order')} "${ProductName}" ${translate('shoppingCart.out_card')}`,
            [
                {
                    text: translate('common.btn_skip'),
                    style: "cancel",
                },
                {
                    text: translate('common.btn_continue'),
                    style: "default",
                    onPress: onDelete
                }
            ]
        )
    }

    return (
        <View style={{
            width: constants.width,
            paddingTop: 10,
            paddingBottom: 4,
            backgroundColor: COLORS.bgFFFFFF
        }}>
            <View style={{
                paddingLeft: 10,
                paddingRight: 40,
                marginBottom: 8,
                width: constants.width,
            }}>
                {/* <MyText
                    text={ProductName}
                    addSize={2}
                    style={{
                        color: COLORS.txt0000FF,
                        fontWeight: 'bold'
                    }}>
                    <MyText
                        text={statusName}
                        addSize={2}
                        style={{
                            color: COLORS.txt444444,
                            fontWeight: 'normal'
                        }}
                    />
                    {!!SaleProgramInfo && (
                        <MyText
                            text={translate('shoppingCart.installment_payment')}
                            style={{
                                color: COLORS.txtFF6200,
                                fontWeight: 'normal',
                                fontStyle: 'italic'
                            }}
                        />
                    )}
                </MyText> */}
                <NameProduct
                    ProductName={ProductName}
                    statusName={statusName}
                    SaleProgramInfo={SaleProgramInfo}
                />

                {!IsApplyTotalPromotion && (
                    <View
                        style={{
                            paddingRight: 10,
                            marginTop: 4,
                            width: constants.width - 10,
                            justifyContent: "center",
                        }}>
                        <MyText
                            text={translate('shoppingCart.product_not_valid_for_order_promotion')}
                            addSize={-2}
                            style={{
                                color: COLORS.txtFF8900,
                                fontStyle: 'italic'
                            }}
                        />
                    </View>
                )}
            </View>
            {
                !!IMEI &&
                <View style={{
                    paddingHorizontal: 10,
                    marginBottom: 8,
                    width: constants.width,
                }}>
                    <MyText
                        text={`${translate('shoppingCart.IMEI')} ${IMEI}`}
                        style={{
                            color: COLORS.txt333333
                        }}
                    />
                </View>
            }
            <View style={{
                paddingHorizontal: 10,
                marginBottom: 8,
                width: constants.width,
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
            }}>

                <View style={{
                    flexDirection: "row",
                    alignItems: "center",
                }}>
                    <MyText
                        text={`${translate('shoppingCart.quantity_full')} ${Quantity} ${QuantityUnitName}`}
                        style={{
                            color: COLORS.txt333333
                        }} />

                </View>
                {
                    helper.IsEmptyObject(product.PricePolicyApplyBO)
                        ?
                        <MyText
                            text={translate('common.price')}
                            style={{
                                color: COLORS.txt333333,
                                fontWeight: 'bold'
                            }}>
                            <MyText
                                text={helper.convertNum(priceVAT)}
                                style={{
                                    color: COLORS.txtD0021B,
                                    fontWeight: 'normal'
                                }}
                            />
                        </MyText>
                        :
                        <TouchableOpacity
                            onPress={onChangeSheet}
                            style={{
                                flexDirection: "row",
                                alignItems: "center",
                                justifyContent: "center",
                            }}>
                            <Image
                                style={{ width: 17, height: 17, marginHorizontal: 4, marginBottom: 4 }}
                                source={imagePrice}
                            />
                            <MyText
                                text={translate('common.price')}
                                style={{
                                    color: COLORS.txt333333,
                                    fontWeight: 'bold'
                                }}>
                                <MyText
                                    text={helper.convertNum(priceVAT)}
                                    style={{
                                        color: COLORS.txtD0021B,
                                        fontWeight: 'normal'
                                    }}
                                />
                            </MyText>
                        </TouchableOpacity>
                }


            </View>

            <View style={{
                paddingHorizontal: 10,
                width: constants.width,
            }}>
                <MyText
                    text={`${translate('shoppingCart.output_store')} ${OutputStoreID} - ${OutputStoreName}`}
                    style={{
                        color: COLORS.txt333333
                    }}
                />
            </View>
            {
                isAdjust &&
                <View style={{
                    flexDirection: "row",
                    width: constants.width,
                    paddingHorizontal: 10,
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginTop: 8
                }}>
                    <MyText
                        text={labelKey}
                        style={{
                            color: COLORS.txt333333,
                            fontWeight: 'bold'
                        }}
                    />

                    <MyText
                        text={helper.convertNum(AdjustPrice)}
                        style={{
                            color: COLORS.txtD0021B
                        }}
                    />
                </View>
            }


            <TouchableOpacity style={{
                width: 40,
                height: 26,
                top: 0,
                right: 0,
                borderBottomLeftRadius: 20,
                alignItems: 'center',
                justifyContent: "center",
                backgroundColor: COLORS.btnF5F5F5,
                position: 'absolute',
            }}
                onPress={onPressDelete}
            >
                <Icon
                    iconSet={"MaterialIcons"}
                    name={"close"}
                    color={COLORS.icD0021B}
                    size={20}
                />
            </TouchableOpacity>
        </View>
    );
}

const MainProductAnKhang = ({
    product,
    onDelete,
    isApplyCoupon,
    applyDetailIDs,
    isBK,
    handleShowLot,
    handleShowGuide,
    totalPrice
}) => {
    const {
        ProductName,
        SaleProgramInfo,
        IMEI,
        Quantity,
        QuantityUnitName,
        RetailPriceVAT,
        AdjustPriceTypeID,
        AdjustPrice,
        InventoryStatusID,
        InventoryStatusName,
        SaleOrderDetailID,
        SalePriceBKVAT,
        cus_IsEditQuantity,
        cus_IsRequiredBatchNO
    } = product;
    const { storeID } = useSelector((state) => state.userReducer);
    const isAdjust = !isApplyCoupon && AdjustPrice != 0;
    const statusName =
        InventoryStatusID != 1 ? ` (${InventoryStatusName})` : '';
    const isNonCartPromotion = applyDetailIDs.size == 0;
    const IsApplyTotalPromotion =
        isNonCartPromotion || applyDetailIDs.has(SaleOrderDetailID);
    const priceVAT = isBK ? SalePriceBKVAT : RetailPriceVAT;
    const isShowLot = cus_IsEditQuantity || cus_IsRequiredBatchNO;

    const isAloneQuantity = cus_IsEditQuantity && !cus_IsRequiredBatchNO;
    const labelKey = helper.configStoreCreatePrice(storeID) ? "Phiếu mua hàng hỗ trợ chiến giá" : translate(constants.ADJUST_PRICE_LABEL[AdjustPriceTypeID]);

    const onPressDelete = () => {
        Alert.alert(
            '',
            `${translate(
                'shoppingCart.warning_delete_order'
            )} "${ProductName}" ${translate('shoppingCart.out_card')}`,
            [
                {
                    text: translate('common.btn_skip'),
                    style: 'cancel'
                },
                {
                    text: translate('common.btn_continue'),
                    style: 'default',
                    onPress: onDelete
                }
            ]
        );
    };

    return (
        <View
            style={{
                width: constants.width,
                paddingTop: 10,
                paddingBottom: 4,
                backgroundColor: COLORS.bgFFFFFF
            }}>
            <View
                style={{
                    paddingLeft: 10,
                    paddingRight: 40,
                    marginBottom: 8,
                    width: constants.width
                }}>
                <TouchableOpacity onPress={handleShowGuide}>
                    <NameProduct
                        ProductName={ProductName}
                        statusName={statusName}
                        SaleProgramInfo={SaleProgramInfo}
                    />
                </TouchableOpacity>

                {!IsApplyTotalPromotion && (
                    <View
                        style={{
                            paddingRight: 10,
                            marginTop: 4,
                            width: constants.width - 10,
                            justifyContent: 'center'
                        }}>
                        <MyText
                            text={translate(
                                'shoppingCart.product_not_valid_for_order_promotion'
                            )}
                            addSize={-2}
                            style={{
                                color: COLORS.txtFF8900,
                                fontStyle: 'italic'
                            }}
                        />
                    </View>
                )}
            </View>
            {!!IMEI && (
                <View
                    style={{
                        paddingHorizontal: 10,
                        marginBottom: 8,
                        width: constants.width
                    }}>
                    <MyText
                        text={`${translate('shoppingCart.IMEI')} ${IMEI}`}
                        style={{
                            color: COLORS.txt333333
                        }}
                    />
                </View>
            )}
            <View
                style={{
                    paddingHorizontal: 10,
                    marginBottom: 8,
                    width: constants.width,
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between'
                }}>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center'
                    }}>
                    <View style={{ flexDirection: 'row' }}>
                        <MyText
                            text={`${translate('pharmacy.quantity_acronym')}: `}
                            style={{
                                color: COLORS.txt333333,
                                fontWeight: 'bold'
                            }}
                        />
                        <TouchableOpacity
                            disabled={!isShowLot}
                            onPress={() => handleShowLot(isAloneQuantity)}>
                            <MyText
                                text={`${Quantity} ${QuantityUnitName}`}
                                style={{
                                    color: isShowLot
                                        ? COLORS.txt0000FF
                                        : COLORS.txt333333,
                                    textDecorationLine: isShowLot
                                        ? 'underline'
                                        : 'none'
                                }}
                            />
                        </TouchableOpacity>
                    </View>
                </View>

                <MyText
                    text={translate('common.price')}
                    style={{
                        color: COLORS.txt333333,
                        fontWeight: 'bold'
                    }}>
                    <MyText
                        text={helper.convertNum(priceVAT)}
                        style={{
                            color: COLORS.txtD0021B,
                            fontWeight: 'normal'
                        }}
                    />
                </MyText>

                <MyText
                    text={translate('shoppingCart.product_price')}
                    style={{
                        color: COLORS.txt333333,
                        fontWeight: 'bold'
                    }}>
                    <MyText
                        text={helper.convertNum(totalPrice)}
                        style={{
                            color: COLORS.txtD0021B,
                            fontWeight: 'normal'
                        }}
                    />
                </MyText>
            </View>

            {isAdjust && (
                <View
                    style={{
                        flexDirection: 'row',
                        width: constants.width,
                        paddingHorizontal: 10,
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        marginTop: 8
                    }}>
                    <MyText
                        text={labelKey}
                        style={{
                            color: COLORS.txt333333,
                            fontWeight: 'bold'
                        }}
                    />

                    <MyText
                        text={helper.convertNum(AdjustPrice)}
                        style={{
                            color: COLORS.txtD0021B
                        }}
                    />
                </View>
            )}

            <TouchableOpacity
                style={{
                    width: 40,
                    height: 26,
                    top: 0,
                    right: 0,
                    borderBottomLeftRadius: 20,
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: COLORS.btnF5F5F5,
                    position: 'absolute'
                }}
                onPress={onPressDelete}>
                <Icon
                    iconSet="MaterialIcons"
                    name="close"
                    color={COLORS.icD0021B}
                    size={20}
                />
            </TouchableOpacity>
        </View>
    );
};

const TotalMoney = ({ total }) => {
    return (
        <View style={{
            backgroundColor: COLORS.bgEEF0DB,
            flexDirection: 'row',
            width: constants.width,
            justifyContent: 'space-between',
            paddingHorizontal: 10,
            height: 40,
        }}>
            <View style={{
                flexDirection: 'row',
                alignSelf: 'flex-end',
                alignItems: 'center',
                height: 40,
            }}>
                <MyText
                    text={translate('shoppingCart.product_price')}
                    style={{
                        color: COLORS.txt147EFB,
                        fontWeight: 'bold'
                    }}>
                    <MyText
                        text={translate('shoppingCart.not_rounded')}
                        style={{
                            color: COLORS.txt666666,
                            fontStyle: 'italic',
                            fontWeight: 'normal'
                        }}
                    />
                </MyText>
            </View>

            <View style={{
                alignSelf: 'flex-end',
                justifyContent: 'center',
                height: 40,
            }}>
                <MyText
                    text={helper.convertNum(total)}
                    addSize={2}
                    style={{
                        color: COLORS.txtD0021B,
                        fontWeight: 'bold'
                    }}
                />
            </View>
        </View>
    );
}

const ProductInfo = ({
    mainProduct,
    onDelete,
    onDeleteSaleProduct,
    couponDiscount,
    onUpdateSimProduct,
    applyDetailIDs,
    isBK,
    actionShoppingCart,
    isAnnKhang,
    onUpdateSaleOrder,
    onViewUseGuide,
    onChangeSheet,
    onchangeSheet
}) => {
    const {
        RetailPriceVAT,
        DeliveryInfoRequest,
        giftSaleOrders,
        saleSaleOrders,
        Quantity,
        AdjustPrice,
        // PROMOTION_DELIVERY
        giftDeliverySaleOrders,
        saleDeliverySaleOrders,
        SalePriceBKVAT,
        cus_SaleOrderDetailInfoBOList,
        ProductName,
        cus_IsEditQuantity,
        cus_InstockQuantity,
        SaleOrderDetailID,
        cus_AllowChageQuantity,
        giftLostSaleSOs,
        ProductID,
        InventoryStatusID,
        SaleProgramInfo
    } = mainProduct;
    const { storeID } = useSelector((state) => state.userReducer);
    const giftLostSaleSOsNew = giftLostSaleSOs ?? []
    const isApplyCoupon = (couponDiscount > 0);
    const [sumPrice, setSumPrice] = useState(0);
    const priceVAT = isBK ? SalePriceBKVAT : RetailPriceVAT;
    const dispatch = useDispatch();
    const { dataShoppingCart, batchNoByCart } = useSelector(
        (state) => state.shoppingCartReducer
    );
    const { saleScenarioTypeID } = useSelector(
        (state) => state.specialSaleProgramReducer
    );
    const { suggestProducts } = useSelector(
        (state) => state.detailReducer
    );

    const [batchModal, setBatchModal] = useState({
        value: [],
        visible: false,
        ProductName: '',
        totalQuantity: 0,
        productKey: '',
        id: '',
        allowToChangeLess: false
    });


    const handleShowLot =
        (name, totalQuantity, id, allowToChangeLess, isAloneQuantity) =>
            (productKey) => {
                handleApiLoadBatchNo(
                    {
                        ProductName: name,
                        SaleOrderDetailID: id,
                        Quantity: totalQuantity,
                        allowToChangeLess,
                        isAloneQuantity
                    },
                    productKey
                );
            };
    const handleApiLoadBatchNo = (product, productKey) => {
        const batches = batchNoByCart[product.SaleOrderDetailID];
        if (batches || product.isAloneQuantity) {
            setBatchModal({
                visible: true,
                value: batches ?? [],
                ProductName: product.ProductName,
                id: product.SaleOrderDetailID,
                totalQuantity: product.Quantity,
                productKey,
                allowToChangeLess: product.allowToChangeLess
            });
        } else {
            showBlockUI();
            dispatch(
                loadInforBatchNoByCart({
                    saleOrderDetailID: product.SaleOrderDetailID,
                    cartRequest: dataShoppingCart
                })
            )
                .then((newBatches) => {
                    hideBlockUI();
                    setBatchModal({
                        visible: true,
                        value: newBatches,
                        ProductName: product.ProductName,
                        id: product.SaleOrderDetailID,
                        totalQuantity: product.Quantity,
                        productKey,
                        allowToChangeLess: product.allowToChangeLess
                    });
                    dispatch(
                        setBatchNoByCart({
                            id: product.SaleOrderDetailID,
                            batchNo: newBatches
                        })
                    );
                })
                .catch((msgError) => {
                    hideBlockUI();
                    Alert.alert(
                        translate('common.notification_uppercase'),
                        msgError,
                        [{ text: 'OK' }]
                    );
                });
        }
    };

    const generateNewSaleOrder = (
        productKey,
        batch,
        id,
        clientProductQuantity
    ) => {
        const keys = [
            'giftSaleOrders',
            'saleSaleOrders',
            'giftDeliverySaleOrders',
            'saleDeliverySaleOrders'
        ];
        const newMainProduct = helper.deepCopy(mainProduct);
        const isSaleSaleProduct = productKey === 'saleSaleOrders' || productKey === 'saleDeliverySaleOrders'
        if (keys.includes(productKey)) {
            // if (
            //     clientProductQuantity === 0 && isSaleSaleProduct
            // ) {
            //     const newSaleSaleOrders = mainProduct.saleSaleOrders.filter(
            //         (promtion, position) => promtion.SaleOrderDetailID !== id
            //     );
            //     onDeleteSaleProduct(newSaleSaleOrders, productKey);
            // } else {
            const index = newMainProduct[productKey].findIndex(
                (product) => product.SaleOrderDetailID === id
            );
            if (index !== -1) {
                newMainProduct[productKey][
                    index
                ].cus_SaleOrderDetailInfoBOList = batch;
            }
            // }
        } else {
            // main product
            // if (clientProductQuantity === 0) {
            //     onDelete();
            // } else {
            newMainProduct.cus_SaleOrderDetailInfoBOList = batch;
            // }
        }
        return newMainProduct;
    };

    const handleSubmitBatch = (objBatch, productKey, id) => {
        const { batch, clientProductQuantity } = objBatch;
        const newSaleOrder = generateNewSaleOrder(productKey, batch, id, clientProductQuantity);
        dispatch(
            setBatchNoByCart({
                id: batchModal.id,
                batchNo: batch
            })
        );
        onUpdateSaleOrder(newSaleOrder, clientProductQuantity, productKey);
    };

    const effectMainProduct = () => {
        const newAdjustPrice = !helper.IsEmptyObject(SaleProgramInfo) ? AdjustPrice : (helper.configStoreCreatePrice(storeID) ? 0 : AdjustPrice)
        const salePriceAdjust = isApplyCoupon ? 0 : newAdjustPrice;
        let sumMoney = (priceVAT + salePriceAdjust) * Quantity;
        sumMoney += getSumPriceGift(giftSaleOrders, Quantity);
        sumMoney += getSumPriceSale(saleSaleOrders, isApplyCoupon);
        // PROMOTION_DELIVERY
        sumMoney += getSumPriceGift(giftDeliverySaleOrders, Quantity);
        sumMoney += getSumPriceSale(saleDeliverySaleOrders, isApplyCoupon);
        // PROMOTION_LOSTSALE
        if (!isAnnKhang) {
            sumMoney += getSumPriceGift(giftLostSaleSOsNew, Quantity);
        }
        setSumPrice(sumMoney);
    }

    useEffect(
        effectMainProduct,
        [mainProduct]
    )

    const mainProductProps = {
        product: mainProduct,
        isApplyCoupon,
        onDelete,
        applyDetailIDs,
        isBK,
        onChangeSheet
    };
    const keyProductSuggest = `${ProductID}_${InventoryStatusID}`

    return (
        <View
            style={{
                width: constants.width,
                backgroundColor: COLORS.bgFFFFFF,
                shadowColor: COLORS.sd000000,
                shadowOffset: {
                    width: 0,
                    height: 2
                },
                shadowOpacity: 0.25,
                shadowRadius: 2.6,
                elevation: 4
            }}>
            {isAnnKhang ? (
                <MainProductAnKhang
                    {...mainProductProps}
                    lstDetailLot={cus_SaleOrderDetailInfoBOList}
                    handleShowLot={(isAloneQuantity) => {
                        handleShowLot(
                            ProductName,
                            Quantity,
                            SaleOrderDetailID,
                            cus_AllowChageQuantity,
                            isAloneQuantity
                        )('mainProduct');
                    }}
                    handleShowGuide={() => {
                        onViewUseGuide({
                            webInfo: {
                                link: 'tskt',
                                title: 'Hướng dẫn sử dụng'
                            },
                            productOrder: {
                                productIDRef: mainProduct.cus_ProductIDRef
                            }
                        });
                    }}
                    totalPrice={sumPrice}
                />
            ) : (
                <MainProduct {...mainProductProps} />
            )}
            {
                saleScenarioTypeID == SALE && suggestProducts.size > 0 && suggestProducts.has(keyProductSuggest) && (
                    <TouchableOpacity onPress={() => { onchangeSheet(suggestProducts.get(keyProductSuggest)) }} activeOpacity={0.5} style={styles.bar}>
                        <View style={styles.cartShadow}>
                            <Image
                                style={styles.cartIcon}
                                source={require('../../../../assets/trolley.png')}
                            />
                            <MyText style={styles.totalQty}>
                                Gợi ý mua thêm
                            </MyText>

                        </View>
                    </TouchableOpacity>
                )
            }

            <PromotionGift
                title={translate('shoppingCart.promotion')}
                giftSaleOrders={giftSaleOrders}
                mainProduct={mainProduct}
                onUpdateSim={(data) => {
                    onUpdateSimProduct(data, 'giftSaleOrders');
                }}
                actionShoppingCart={actionShoppingCart}
                onShowLot={(name, totalQuantity, id, allowToChangeLess) =>
                    handleShowLot(
                        name,
                        totalQuantity,
                        id,
                        allowToChangeLess
                    )('giftSaleOrders')
                }
            />
            <PromotionSale
                title={translate('shoppingCart.bundle_sale')}
                saleSaleOrders={saleSaleOrders}
                onDeleteSalePromotion={(data, removeItem) => {
                    onDeleteSaleProduct(data, 'saleSaleOrders', removeItem);
                }}
                onUpdateSim={(data) => {
                    onUpdateSimProduct(data, 'saleSaleOrders');
                }}
                isApplyCoupon={isApplyCoupon}
                applyDetailIDs={applyDetailIDs}
                actionShoppingCart={actionShoppingCart}
                onShowLot={(name, totalQuantity, id, allowToChangeLess) =>
                    handleShowLot(
                        name,
                        totalQuantity,
                        id,
                        allowToChangeLess
                    )('saleSaleOrders')
                }
            />
            {
                // PROMOTION_DELIVERY
                <PromotionGift
                    title={translate('shoppingCart.promotion_delivery_type')}
                    giftSaleOrders={giftDeliverySaleOrders}
                    mainProduct={mainProduct}
                    onUpdateSim={(data) => {
                        onUpdateSimProduct(data, 'giftDeliverySaleOrders');
                    }}
                    actionShoppingCart={actionShoppingCart}
                    onShowLot={(name, totalQuantity, id, allowToChangeLess) =>
                        handleShowLot(
                            name,
                            totalQuantity,
                            id,
                            allowToChangeLess
                        )('giftDeliverySaleOrders')
                    }
                />
            }
            {
                // PROMOTION_DELIVERY
                <PromotionSale
                    title={translate('shoppingCart.bundle_sale_delivery_type')}
                    saleSaleOrders={saleDeliverySaleOrders}
                    onDeleteSalePromotion={(data, removeItem) => {
                        onDeleteSaleProduct(data, "saleDeliverySaleOrders", removeItem)
                    }}
                    onUpdateSim={(data) => {
                        onUpdateSimProduct(data, saleDeliverySaleOrders);
                    }}
                    isApplyCoupon={isApplyCoupon}
                    applyDetailIDs={applyDetailIDs}
                    actionShoppingCart={actionShoppingCart}
                    onShowLot={(name, totalQuantity, id, allowToChangeLess) =>
                        handleShowLot(
                            name,
                            totalQuantity,
                            id,
                            allowToChangeLess
                        )('saleDeliverySaleOrders')
                    }
                />
            }
            {
                // PROMOTION_LOSTSALE
                <PromotionGift
                    title={"Khuyến mãi LostSale"}
                    giftSaleOrders={giftLostSaleSOsNew}
                    mainProduct={mainProduct}
                    onUpdateSim={(data) => {
                        onUpdateSimProduct(data, 'giftLostSaleSOsNew');
                    }}
                    actionShoppingCart={actionShoppingCart}
                    onShowLot={(name, totalQuantity, id, allowToChangeLess) =>
                        handleShowLot(
                            name,
                            totalQuantity,
                            id,
                            allowToChangeLess
                        )('giftLostSaleSOsNew')
                    }
                />
            }
            <DeliveryInfo deliveryInfo={DeliveryInfoRequest} />
            {!isAnnKhang && <TotalMoney total={sumPrice} />}
            <BatchInfo
                data={batchModal.value}
                isShowModal={batchModal.visible}
                onClose={() =>
                    setBatchModal({
                        visible: false,
                        value: [],
                        ProductName: '',
                        totalQuantity: 0,
                        id: '',
                        productKey: '',
                        allowToChangeLess: false
                    })
                }
                productName={batchModal.ProductName}
                totalQuantity={batchModal.totalQuantity}
                editable
                quantityEditable={cus_IsEditQuantity}
                totalInStock={cus_InstockQuantity}
                allowToChangeLess={batchModal.allowToChangeLess}
                onSubmit={(objBatch) =>
                    handleSubmitBatch(
                        objBatch,
                        batchModal.productKey,
                        batchModal.id
                    )
                }
            />
        </View>
    );
};

export default ProductInfo;

const getSumPriceGift = (giftSaleOrders, quantity) => {
    let sumMoney = 0;
    const randomDiscountGift = giftSaleOrders.find(gift => gift.IsRandomDiscount)
    const discountValue = helper.IsEmptyObject(randomDiscountGift) ? 0 : randomDiscountGift.ExtensionProperty?.DiscountValue ?? 0;
    giftSaleOrders.forEach(giftPromotion => {
        if (helper.hasProperty(giftPromotion, 'IsPercentDiscount')) {
            const {
                IsPercentDiscount,
                DiscountMoneyVATSODetail,
                DiscountValue
            } = giftPromotion;
            const moneyDiscount = IsPercentDiscount
                ? DiscountMoneyVATSODetail
                : DiscountValue;
            sumMoney -= moneyDiscount * quantity;
        }
    })
    return sumMoney - discountValue;
}

const getSumPriceSale = (saleSaleOrders, isApplyCoupon) => {
    let sumMoney = 0;
    saleSaleOrders.forEach(salePromotion => {
        const {
            SalePriceBKVAT,
            Quantity,
            giftSaleOrders,
            AdjustPrice
        } = salePromotion;
        const salePriceAdjust = isApplyCoupon ? 0 : AdjustPrice;
        sumMoney += (SalePriceBKVAT + salePriceAdjust) * Quantity;
        // PROMOTION_DELIVERY
        if (giftSaleOrders) {
            sumMoney += getSumPriceGift(giftSaleOrders, Quantity);
        }
    })
    return sumMoney;
}

const NameProduct = ({ ProductName, statusName, SaleProgramInfo }) => {
    return (
        <MyText
            text={ProductName}
            addSize={2}
            style={{
                color: COLORS.txt0000FF,
                fontWeight: 'bold'
            }}>
            <MyText
                text={statusName}
                addSize={2}
                style={{
                    color: COLORS.txt444444,
                    fontWeight: 'normal'
                }}
            />
            {!!SaleProgramInfo && (
                <MyText
                    text={translate('shoppingCart.installment_payment')}
                    style={{
                        color: COLORS.txtFF6200,
                        fontWeight: 'normal',
                        fontStyle: 'italic'
                    }}
                />
            )}
        </MyText>
    );
};

const getImagePrice = (type) => {
    let source = require('../../../../assets/box.png')
    return source

}
const styles = StyleSheet.create({
    bar: {
        width: constants.width / 2.5,
        overflow: 'hidden',
        marginLeft: 10,
        paddingBottom: 5
    },
    cartIcon: {
        height: 20,
        width: 20
    },
    cartShadow: {
        flex: 0.5,
        borderRadius: 10,
        alignItems: 'center',
        backgroundColor: COLORS.bg2FB47C,
        elevation: 3,
        flexDirection: 'row',
        height: 25,
        paddingHorizontal: 8,

    },
    totalQty: {
        fontWeight: 'bold',
        marginLeft: 10,
        fontStyle: "italic",
        color: "white"
    }
});
