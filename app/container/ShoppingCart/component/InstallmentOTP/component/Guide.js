import React from 'react';
import { View } from 'react-native';
import { MyText } from "@components";
import { constants } from "@constants";
import { translate } from '@translate';
import { COLORS } from "@styles";

const FieldText = ({ content }) => {
    return (
        <MyText
            text={content}
            style={{
                color: COLORS.txt333333,
                marginBottom: 4
            }}
        />
    );
}

const Guide = ({ type }) => {
    const content = {
        INSTALLMENT: translate('shoppingCart.installment_otp_guide'),
        BATTERY: translate('shoppingCart.pin_otp_guide'),
        STICKER: translate('shoppingCart.sticker_otp_guide'),
        PREORDER: "Mã OTP này dùng để xác thực khách hàng đã đồng ý việc ghi nhận thông tin cho Pre Order",
        CONTRACT: "Nhân viên nhập mã OTP để xác thực khách hàng đồng ý hủy hợp đồng trả góp.					",
        SCREENPROTECTOR: "Vui lòng nhập mã OTP để hoàn tất tạo đơn hàng miếng dán màn hình",
        LATTER: "Mã OTP này dùng để xác thực việc khách hàng đồng ý cung cấp thông tin cá nhân cho TGDĐ thu thập, sử dụng và chuyển giao cho các đối tác.",
        ADJUSTPRICEGIFTVOUCHER: "Vui lòng nhập mã OTP khách hàng cung cấp để hoàn tất thủ tục chiến giá.",
        CUSTOMERFIRSTPURCHASEANKHANG: "Vui lòng nhập mã xác nhận để nhận ưu đãi khách hàng.",
    }
    return (
        <View style={{
            width: constants.width,
            padding: 10
        }}>
            <MyText
                text={translate('shoppingCart.guide')}
                style={{
                    color: COLORS.txt333333,
                    fontStyle: 'italic',
                    fontWeight: 'bold',
                    marginBottom: 2
                }}
            />

            <FieldText
                content={content[type]}
            />
        </View>
    );
}

export default Guide;