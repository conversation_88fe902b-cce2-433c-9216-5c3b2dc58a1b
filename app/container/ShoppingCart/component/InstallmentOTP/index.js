import React, { Component } from 'react';
import {
    SafeAreaView,
    StyleSheet,
    Alert,
    Keyboard
} from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import {
    showB<PERSON>UI,
    hideBlockUI,
} from "@components";
import { helper, storageHelper } from "@common";
import Guide from "./component/Guide";
import Customer from "./component/Customer";
import OtpCode from "./component/OtpCode";
import * as actionShoppingCartCreator from "../../action";
import * as actionSaleOrderCreator from "../../../SaleOrderCart/action";
import * as actionPouchCreator from "../../../PouchRedux/action";
import * as specialSaleProgramActionCreator from "../../../SpecialSaleProgram/action";
import * as staffPromotionActionCreator from "../../../StaffPromotion/action";
import { COLORS } from "@styles";
import { translate } from '@translate';
import { ENUM, constants } from '@constants';
import OTPBackup from "../OTPBackup"
const { PROVINCE_OTP, PARTNER_ID, TYPE_PROFILE } = constants;

class InstallmentOTP extends Component {

    constructor() {
        super();
        this.state = {
            info: {
                customerPhone: '',
                customerName: '',
            },
            expireTime: 0,
            otpCode: "",
            isVisible: false,
            isChecked: false
        }
        this.intervalId = null;
        this.onlySms = false;
        this.isCall = false;
    }

    componentDidMount() {
        const { dataCartInstallment, userInfo: { storeID, provinceID }, typeOTP } = this.props;
        // const isInstallment = typeOTP == "INSTALLMENT";
        // if (!isInstallment && !PROVINCE_OTP.has(`${provinceID}`)) {
        //     this.onlySms = true;
        // }
        const memberInfo = getMemberInfo(dataCartInstallment);
        this.setState({ info: memberInfo });
    }

    componentWillUnmount() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }
    }

    render() {
        const {
            info,
            expireTime,
            otpCode,
        } = this.state;
        const { typeOTP } = this.props;
        return (
            <KeyboardAwareScrollView
                style={{
                    flex: 1,
                    backgroundColor: COLORS.bgFFFFFF,
                }}
                enableResetScrollToCoords={false}
                keyboardShouldPersistTaps="always"
                bounces={false}
                overScrollMode="always"
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                extraScrollHeight={60}
            >
                {
                    helper.validateOtpSend(typeOTP) ? <SafeAreaView style={{ paddingBottom: 10, marginTop: 15 }}>
                        <OTPBackup
                            onPress={() => this.onCheckLoyaltyPoint(this.props.dataCartInstallment)}
                            label={helper.getGuideOTPBackup(typeOTP)}
                        >
                            <Customer
                                info={info}
                            />
                        </OTPBackup>
                    </SafeAreaView> : <SafeAreaView style={{
                        flex: 1,
                    }}>
                        <Guide type={typeOTP} />
                        <Customer
                            info={info}
                        />
                        <OtpCode
                            onCreate={this.onCreateOTP}
                            expireTime={expireTime}
                            code={otpCode}
                            onChange={(text) => {
                                const regExpOTP = new RegExp(/^\d{0,4}$/);
                                const isValidate = regExpOTP.test(text);
                                if (isValidate) {
                                    this.setState({ otpCode: text });
                                }
                            }}
                            onVerify={this.onCheckOTP}
                            onlySms={this.onlySms}
                        />
                    </SafeAreaView>
                }

            </KeyboardAwareScrollView>
        );
    }

    countDown = () => {
        const { expireTime } = this.state;
        const second = expireTime - 1;
        if (second > 0) {
            this.setState({ expireTime: second });
        }
        else {
            this.resetCountDown();
        }
    }

    resetCountDown = () => {
        if (!this.isCall) {
            this.onlySms = true;
        }
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }
        this.setState({
            expireTime: 0,
            otpCode: "",
        });
    }

    setCountDown = () => {
        this.setState({
            expireTime: 60
        })
        this.intervalId = setInterval(
            this.countDown,
            1000
        );
    }

    onCreateOTP = (type) => {
        const { info: {
            customerPhone
        } } = this.state;
        const { userInfo: { brandID }, typeOTP } = this.props;
        this.isCall = type == 'CALLCENTER';
        showBlockUI();
        actionShoppingCartCreator.createOTP({
            "type": type,
            "phoneNumber": customerPhone,
            "typeContent": typeOTP,
            "lenOtp": 4,
            "brandID": brandID,
            "onlySms": this.onlySms
        }).then(isSMS => {
            if (!this.isCall) {
                this.onlySms = isSMS;
            }
            hideBlockUI();
            this.setCountDown();
        }).catch(msgError => {
            this.resetCountDown();
            Alert.alert(translate('common.notification_uppercase'), msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: () => this.onCreateOTP(type)
                    }
                ]
            )
        });
    }

    onCheckOTP = () => {
        Keyboard.dismiss();
        const {
            otpCode,
            info: {
                customerPhone
            }
        } = this.state;
        const isValidate = this.validateOTP(otpCode);
        if (isValidate) {
            this.verifyOTP(otpCode, customerPhone);
        }
    }


    verifyOTP = (otpCode, customerPhone) => {
        const { dataCartInstallment, customerConfirmPolicy, userInfo: { storeID, userName, languageID, moduleID } } = this.props;
        showBlockUI();
        actionShoppingCartCreator.verifyOTP(otpCode, customerPhone)
            .then(async (data) => {
                const hasInstallmentSamsung = dataCartInstallment.cartRequest?.SaleOrderDetails?.some(
                    item => item?.SaleProgramInfo?.PartnerInstallmentID == PARTNER_ID.SAMSUNG
                );
                if (hasInstallmentSamsung) {
                    const body = {
                        "loginStoreId": storeID,
                        "loginUser": userName,
                        "languageId": languageID,
                        "moduleId": moduleID,
                        "CUSTOMERID": customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]?.[0]?.profileId ?? "",
                        "PARTNERINSTALLMENTID": 33,
                        "SERVICETYPE": "OTPCREATEINSTALLMENT"
                    }
                    await actionShoppingCartCreator.saveInfoCustomer(body)
                }
                this.onCheckLoyaltyPoint(dataCartInstallment);
            }).catch(msgError => {
                Alert.alert(translate('common.notification_uppercase'), msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: "cancel",
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: "default",
                            onPress: () => this.verifyOTP(otpCode, customerPhone)
                        }
                    ]
                )
            })
    }

    validateOTP = (code) => {
        const regExpOTP = new RegExp(/^\d{4}$/);
        const isValidate = regExpOTP.test(code);
        if (!helper.IsNonEmptyString(code)) {
            Alert.alert("", translate('shoppingCart.validate_empty_otp'));
            return false;
        }
        if (!isValidate) {
            Alert.alert("", translate('shoppingCart.validate_otp'));
            return false;
        }
        return true;
    }

    addToSaleOrderCart = async (data) => {
        showBlockUI();
        const { userInfo: { storeID }, staffPromotionAction, saleScenarioTypeID, specialSaleProgramAction } = this.props;
        const isHasRight = await helper.checkStorePermission(storeID);
        if (isHasRight) {
            this.props.actionSaleOrder.addToSaleOrderCart(data).then(orderInfo => {
                const { customerInfo, customerIDLoyalty } = data;
                const { object } = orderInfo;
                hideBlockUI();
                if (this.props?.isRecording) {
                    const saleOrderIDs = object.SaleOrders?.map((_item) => (_item?.SaleOrderID))?.join(",") ?? "";
                    this.props?.stopRecording(saleOrderIDs)
                }
                this.props.actionShoppingCart.deleteShoppingCart();
                this.props.actionPouch.setDataCartApply();
                staffPromotionAction.reset_staff_info();
                saleScenarioTypeID !== ENUM.SALE_SCENARIO_TYPE.SALE &&
                    specialSaleProgramAction.setScenarioSaleType(
                        ENUM.SALE_SCENARIO_TYPE.SALE
                    );
                this.props.navigation.navigate("SaleOrderCart");
                storageHelper.updateTopCustomerInfo(customerInfo);
                if (helper.IsNonEmptyString(customerIDLoyalty)) {
                    storageHelper.updateVerifyLoyalty(customerInfo.customerPhone, object.CartID);
                }
            }).catch(msgError => {
                Alert.alert(translate('common.notification_uppercase'), msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: "cancel",
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: "default",
                            onPress: () => this.addToSaleOrderCart(data)
                        }
                    ]
                )
            });
        }
        else {
            Alert.alert(translate('common.notification_uppercase'), `Bạn không có quyền trên siêu thị ${storeID}`,
                [{
                    text: "OK",
                    onPress: hideBlockUI
                }]
            )
        }
    }

    getDataLoyalty = (customerPhone, dataSaleOrder) => {
        this.props.actionShoppingCart.checkCredentialExist(customerPhone, dataSaleOrder)
            .then(success => {
                hideBlockUI();
                this.props.navigation.pop();
                this.props.navigation.navigate("Loyalty");
            })
    }

    onCheckLoyaltyPoint = (dataSaleOrder) => {
        const {
            cartRequest: { TotalPointLoyalty, IsAllowParticipationLoyalty },
            customerInfo: { customerPhone },
            customerIDLoyalty
        } = dataSaleOrder;
        const isLoyalty = IsAllowParticipationLoyalty && !helper.IsNonEmptyString(customerIDLoyalty);
        const isValidatePhone = helper.IsNonEmptyString(customerPhone);
        const isValidatePoint = (TotalPointLoyalty > 0);
        const isMoveToLoyalty = isValidatePoint && isValidatePhone && isLoyalty;
        if (isMoveToLoyalty) {
            this.getDataLoyalty(customerPhone, dataSaleOrder);
        }
        else {
            this.addToSaleOrderCart(dataSaleOrder);
        }
    }
}

const styles = StyleSheet.create({
});

const mapStateToProps = function (state) {
    return {
        dataCartInstallment: state.shoppingCartReducer.dataCartInstallment,
        userInfo: state.userReducer,
        typeOTP: state.shoppingCartReducer.typeOTP,
        saleScenarioTypeID: state.specialSaleProgramReducer.saleScenarioTypeID,
        customerConfirmPolicy: state.shoppingCartReducer.customerConfirmPolicy,
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionShoppingCart: bindActionCreators(actionShoppingCartCreator, dispatch),
        actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),
        actionPouch: bindActionCreators(actionPouchCreator, dispatch),
        specialSaleProgramAction: bindActionCreators(specialSaleProgramActionCreator, dispatch),
        staffPromotionAction: bindActionCreators(staffPromotionActionCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(InstallmentOTP);

const getMemberInfo = (dataCartInstallment) => {
    let info = {
        customerPhone: '',
        customerName: '',
    };
    if (!helper.IsEmptyObject(dataCartInstallment)) {
        const {
            customerInfo,
        } = dataCartInstallment;
        info = {
            customerPhone: customerInfo.customerPhone,
            customerName: customerInfo.customerName,
        };
    }
    return info;
}
