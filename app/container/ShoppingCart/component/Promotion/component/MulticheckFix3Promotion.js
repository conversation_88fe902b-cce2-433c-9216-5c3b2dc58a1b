import React, { useEffect } from 'react';
import {
    View,
    TouchableOpacity
} from 'react-native';
import { Icon, MyText } from "@components";
import { constants } from "@constants";
import { helper } from "@common";
import ContentPromotion from "./ContentPromotion";
import TitlePromotion from "./TitlePromotion";
import ContenCondition from "./ContenCondition";
import ContentExclude from "./ContentExclude";
import ContentLimit from "./ContentLimit";
import ContentSearchBarcode from "./ContentSearchBarcode";
import { COLORS } from "@styles";
import ButtomSuggestPromotion from './ButtomSuggestPromotion';

const { STATUS_PROMOTION } = constants;

const MulticheckFix3Promotion = ({
    promotionGroup,

    setKeyPromotionSelected,
    onCheckItemProduct,

    isCheckCondition,
    setGroupIDCondition,
    updateGroupIDCondition,
    requestSearchBarcode,
    requestSearchProduct,
    getSuggestPromotion = () => { }
}) => {
    const {
        promotionID,

        promotionGroupID,
        promotionGroupName,

        promotionProducts,
        isViewBarcode,
        isCondition,
        conditionContent,

        excludePromotionIDs,
        isLimitPromotionTimes,
        remainPromotionTimes,
        isRequired,
        statusPromotion
    } = promotionGroup;

    const isLock = (isCondition && !isCheckCondition) || [STATUS_PROMOTION.INIT, STATUS_PROMOTION.NON_ACTIVE].includes(statusPromotion);
    const saleProductGroupID = helper.IsNonEmptyArray(promotionProducts) ? promotionProducts[0].promotionListGroupIDForSaleProduct : 0;

    const effectAutoCheck = () => {
        const isOnly = (promotionProducts.length == 1);
        const isAuto = isRequired && !isCondition && isOnly;
        if (isAuto) {
            const {
                productID,
                inventoryStatusID,
                promotionListGroupIDForSaleProduct,
                instockQuantity,
                quantity
            } = promotionProducts[0];
            const isHasInstock = (instockQuantity > 0);
            const isUnit = (quantity == 1);
            const isDefault = isHasInstock && isUnit;
            if (isDefault) {
                const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
                setKeyPromotionSelected.add(key);
                onCheckItemProduct(setKeyPromotionSelected, saleProductGroupID);
            }
        }
    }

    useEffect(
        effectAutoCheck,
        [isCondition]
    )

    const onSelectItemDiscount = (isSelected, keyPromotion) => {
        if (isSelected) {
            promotionProducts.forEach((product, index) => {
                const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
                const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
                setKeyPromotionSelected.delete(key);
            });
        }
        else {
            promotionProducts.forEach((product, index) => {
                const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
                const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
                const { isDiscount } = product;
                if (isDiscount) {
                    setKeyPromotionSelected.add(key);
                }
                else if (!product.cus_IsDisableProductNotInstock) {
                    setKeyPromotionSelected.delete(key);
                }
            });
        }
        onCheckItemProduct(setKeyPromotionSelected, saleProductGroupID);
    }

    const onSelectItemProduct = (isSelected, keyPromotion) => {
        if (isSelected) {
            promotionProducts.forEach((product, index) => {
                const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
                const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
                setKeyPromotionSelected.delete(key);
            });
        }
        else {
            promotionProducts.forEach((product, index) => {
                const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
                const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
                const { isDiscount } = product;
                if (isDiscount) {
                    setKeyPromotionSelected.delete(key);
                }
                else if (!product.cus_IsDisableProductNotInstock) {
                    setKeyPromotionSelected.add(key);
                }
            });
        }
        onCheckItemProduct(setKeyPromotionSelected, saleProductGroupID);
    }

    const onPressItemPromotion = (isSelected, keyPromotion, isDiscount) => () => {
        if (isDiscount) {
            onSelectItemDiscount(isSelected, keyPromotion);
        }
        else {
            onSelectItemProduct(isSelected, keyPromotion);
        }
    }

    const onCheckCondition = () => {
        if (isCheckCondition) {
            setGroupIDCondition.delete(promotionGroupID);
            promotionProducts.forEach((product, index) => {
                const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
                const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
                setKeyPromotionSelected.delete(key);
            });
            updateGroupIDCondition(setGroupIDCondition, setKeyPromotionSelected);
        } else {
            setGroupIDCondition.add(promotionGroupID);
            updateGroupIDCondition(setGroupIDCondition, setKeyPromotionSelected);
        }
    }

    const renderItemPromotion = (product, index) => {
        const {
            productID,
            inventoryStatusID,
            promotionListGroupIDForSaleProduct,
            isDiscount,
            cus_IsDisableProductNotInstock
        } = product;
        const keyPromotion = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
        const isSelected = setKeyPromotionSelected.has(keyPromotion);
        const isHasInstock = (product.instockQuantity > 0);
        const disabled = isLock || (isDiscount && isHasInstock) || product.cus_IsDisableProductNotInstock;

        return (
            <TouchableOpacity
                key={index}
                style={{
                    flexDirection: "row",
                    paddingVertical: 6,
                    width: constants.width - 20,
                    paddingLeft: 8,
                    // opacity: disabled ? 0.5 : 1,
                }}
                activeOpacity={1}
                onPress={onPressItemPromotion(isSelected, keyPromotion, isDiscount)}
                disabled={disabled}
            >
                <Icon
                    iconSet={"Ionicons"}
                    name={isSelected ? "checkbox-outline" : "square-outline"}
                    color={isSelected ? COLORS.ic288AD6 : COLORS.ic333333}
                    size={14}
                    style={{ marginTop: 2 }}
                />
                <View>
                    <View style={{ opacity: disabled ? 0.5 : 1, }}>
                        <ContentPromotion
                            product={product}
                            isCheck={isSelected}
                        />
                    </View>
                    {
                        cus_IsDisableProductNotInstock &&
                        <MyText
                            text={"(Khuyến mãi tổng đơn không ghi nợ)"}
                            style={{ color: COLORS.txt2164F4, fontStyle: "italic" }}
                            addSize={-2}
                        />
                    }
                </View>
            </TouchableOpacity>
        );
    }

    return (
        <View style={{
            width: constants.width,
            padding: 8,
            paddingBottom: 4,
            borderBottomWidth: 1,
            borderBottomColor: COLORS.bdE4E4E4,
        }}>
            {
                excludePromotionIDs.length > 0 &&
                <ContentExclude
                    excludeIDs={excludePromotionIDs}
                />
            }
            {
                isLimitPromotionTimes &&
                <ContentLimit
                    remainTimes={remainPromotionTimes}
                />
            }
            <TitlePromotion
                promotionGroupName={promotionGroupName}
                promotionID={promotionID}
            />
            {
                isCondition &&
                <ContenCondition
                    content={conditionContent}
                    isCheck={isCheckCondition}
                    onCheck={onCheckCondition}
                />
            }
            {
                isViewBarcode &&
                <>
                    <ContentSearchBarcode
                        onVisibleSearchBarcode={requestSearchBarcode}
                        onVisibleSearchProduct={requestSearchProduct}
                        isLock={isLock}
                    />
                    {
                        promotionProducts.length === 0 &&
                        <View style={{ alignItems: "center", marginVertical: 7 }}>
                            <ButtomSuggestPromotion
                                getSuggestPromotion={getSuggestPromotion}
                                disabled={statusPromotion == STATUS_PROMOTION.NON_ACTIVE}
                            />
                        </View>
                    }
                </>
            }
            {
                promotionProducts.map((product, index) => renderItemPromotion(product, index))
            }
        </View>
    );
}

export default MulticheckFix3Promotion;