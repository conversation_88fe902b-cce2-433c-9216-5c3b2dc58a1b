/**
 * Sample React Native App
 * 
 *
 * @format
 * @flow strict-local
 */

import React, { useEffect, useState } from 'react';
import {
  Al<PERSON>,
  ScrollView,
  View,
} from 'react-native';
import {
  BarcodeCamera,
  showBlockUI,
  hideBlockUI,
} from "@components";
import ItemPromotion from "./component/ItemPromotion";
import ModalSearchProduct from "../Modal/ModalSearchProduct";
import { COLORS } from "@styles";
import { translate } from '@translate';
import { getListCSPromotion } from '../../../AnKhangNew/action';
import { useSelector } from 'react-redux';
import { STATUS_PROMOTION } from '../../../../constants/constants';

const Promotion = ({
  dataPromotion,
  setKeyPromotionSelected,
  updateKeyPromotionSelected,
  setGroupIDCondition,
  updateGroupIDCondition,
  actionDetail,
  onGetCSProducts,
  presentTab,
}) => {
  const { storeID, languageID, moduleID
  } = useSelector(
    (state) => state.userReducer
  );
  const baseRequest = {
    moduleID,
    languageID,
    loginStoreId: storeID,
  };
  const [isVisibleSearchBarcode, setIsVisibleSearchBarcode] = useState(false);
  const [isVisibleSearchProduct, setIsVisibleSearchProduct] = useState(false);
  const [indexGroupSearch, setIndexGroupSearch] = useState(0);
  const [infoGroupSearch, setInfoGroupSearch] = useState({
    promotionListGroupID: 0,
    promotionProducts: [],
    promotionID: 0,
    presentTab: presentTab,
    promotionType: 0
  });
  const [isRefresh, setIsRefresh] = useState(false);
  const [isSuggest, setIsSuggest] = useState(null);

  const requestSearchBarcode = (
    indexGroup,
    promotionGroupID,
    promotionProducts,
    promotionID,
    promotionType
  ) => () => {
    setIsVisibleSearchBarcode(true);
    setIndexGroupSearch(indexGroup);
    setInfoGroupSearch({
      ...infoGroupSearch,
      promotionListGroupID: promotionGroupID,
      promotionProducts: promotionProducts,
      promotionID: promotionID,
      promotionType: promotionType
    });
  }

  const requestSearchProduct = (
    indexGroup,
    promotionGroupID,
    promotionProducts,
    promotionID,
    promotionType
  ) => () => {
    setIsVisibleSearchProduct(true);
    setIndexGroupSearch(indexGroup);
    setInfoGroupSearch({
      ...infoGroupSearch,
      promotionListGroupID: promotionGroupID,
      promotionProducts: promotionProducts,
      promotionID: promotionID,
      promotionType: promotionType
    });
  }

  const updatePromotionProducts = (newPromotionProducts) => {
    dataPromotion[indexGroupSearch].promotionProducts = newPromotionProducts;
    setIsVisibleSearchProduct(false);
  }

  const searchPromotionByBarcode = (barcode) => {
    setIsVisibleSearchBarcode(false);
    showBlockUI();
    const searchPromotion = presentTab === 'CROSSSALE'
      ?
      getListCSPromotion({
        ...baseRequest,
        'promotionType': infoGroupSearch.promotionType,
        'promotionListGroupID': infoGroupSearch.promotionListGroupID,
        'promotionId': infoGroupSearch.promotionID,
        'barcode': barcode,
        isAdvise: true

      })
      :
      actionDetail.searchPromotionProducts({
        "barcode": barcode,
        "promotionListGroupID": infoGroupSearch.promotionListGroupID,
        "promotionID": infoGroupSearch.promotionID
      })
    searchPromotion.then(({ products }) => {
      let listPromotion = products
      if (presentTab === "CROSSSALE") {
        // const lowerCase = str => str[0].toLowerCase() + str.slice(1);
        // const newProductList = products.map(
        //   obj => Object.fromEntries(Object.entries(obj).map(
        //     ([k, v]) => [lowerCase(k), v])
        //   )
        // );

        // const newPromotionProducts = newProductList.map(({
        //   vAT: vat,
        //   vATPercent: vatPercent,
        //   ...rest
        // }) => ({
        //   vat,
        //   vatPercent,
        //   ...rest
        // }));

        listPromotion = products
      }
      hideBlockUI();
      if (listPromotion.length > 0) {
        const newPromotionProducts = [];
        const { promotionProducts } = infoGroupSearch;
        const dataProducts = [...promotionProducts, ...listPromotion];
        const listProductID = dataProducts.map(ele => `${ele.productID}${ele.inventoryStatusID}`);
        const setProductID = new Set(listProductID);
        dataProducts.forEach(product => {
          const { productID, inventoryStatusID } = product;
          const keyProduct = `${productID}${inventoryStatusID}`;
          if (setProductID.has(keyProduct)) {
            newPromotionProducts.push(product);
            setProductID.delete(keyProduct);
          }
        });
        dataPromotion[indexGroupSearch].promotionProducts = newPromotionProducts;
        setIsRefresh(!isRefresh);
      }
    }
    ).catch((description) => {
      Alert.alert(translate('common.notification_uppercase'), description,
        [
          {
            text: translate('common.btn_skip'),
            style: "cancel",
            onPress: hideBlockUI
          },
          {
            text: translate('common.btn_notify_try_again'),
            style: "default",
            onPress: () => searchPromotionByBarcode(barcode)
          }
        ]
      )
    });
  }

  const handleGetSuggestPromotion = () => {
    showBlockUI();
    actionDetail.getSuggestPromotion({ "promotionListGroupID": infoGroupSearch.promotionListGroupID }).then(({ products }) => {
      hideBlockUI();
      if (products.length > 0) {
        const newPromotionProducts = [];
        const { promotionProducts } = infoGroupSearch;
        const dataProducts = [...promotionProducts, ...products];
        const listProductID = dataProducts.map(ele => `${ele.productID}${ele.inventoryStatusID}`);
        const setProductID = new Set(listProductID);
        dataProducts.forEach(product => {
          const { productID, inventoryStatusID } = product;
          const keyProduct = `${productID}${inventoryStatusID}`;
          if (setProductID.has(keyProduct)) {
            newPromotionProducts.push(product);
            setProductID.delete(keyProduct);
          }
        });
        dataPromotion[indexGroupSearch].promotionProducts = newPromotionProducts;
        dataPromotion[indexGroupSearch].statusPromotion = STATUS_PROMOTION.ACTIVE
      }
      else {
        dataPromotion[indexGroupSearch] = { ...dataPromotion[indexGroupSearch], isRequired: false, statusPromotion: STATUS_PROMOTION.NON_ACTIVE }
      }
      setIsRefresh(!isRefresh);

    }).catch((msgError) => {
      Alert.alert(translate('common.notification_uppercase'), msgError,
        [
          {
            text: "Bỏ Qua",
            style: "cancel",
            onPress: hideBlockUI
          }
          ,
          {
            text: translate('common.btn_notify_try_again'),
            style: "default",
            onPress: handleGetSuggestPromotion
          }
        ]
      )
    });

  }
  useEffect(() => {
    if (isSuggest != null) {
      handleGetSuggestPromotion()
    }
  }, [isSuggest])
  return (
    <ScrollView
      contentContainerStyle={{
        flexGrow: 1,
      }}>
      <View style={{
        flex: 1,
        backgroundColor: COLORS.bgFDF9E5,
      }}>
        {
          dataPromotion.map((promotionGroup, indexGroup) => {
            const {
              promotionGroupID,
              excludePromotionIDList,
              promotionProducts,
              promotionID,
              promotionType
            } = promotionGroup;
            const excludePromotionIDs = excludePromotionIDList
            const isCheckCondition = setGroupIDCondition.has(promotionGroupID);
            return (
              <ItemPromotion
                key={promotionGroupID}
                promotionGroup={promotionGroup}
                setKeyPromotionSelected={setKeyPromotionSelected}
                onCheckItemProduct={(newSetKeyPromotionSelected, saleProductGroupID) => {
                  updateKeyPromotionSelected(newSetKeyPromotionSelected, excludePromotionIDs, saleProductGroupID);
                }}
                isCheckCondition={isCheckCondition}
                setGroupIDCondition={setGroupIDCondition}
                updateGroupIDCondition={updateGroupIDCondition}
                requestSearchBarcode={requestSearchBarcode(
                  indexGroup,
                  promotionGroupID,
                  promotionProducts,
                  promotionID,
                  promotionType
                )}
                requestSearchProduct={requestSearchProduct(
                  indexGroup,
                  promotionGroupID,
                  promotionProducts,
                  promotionID,
                  promotionType
                )}
                getSuggestPromotion={() => {
                  setIndexGroupSearch(indexGroup);
                  setInfoGroupSearch({
                    ...infoGroupSearch,
                    promotionListGroupID: promotionID,
                    promotionProducts: promotionProducts,
                    promotionID: promotionID
                  });
                  setIsSuggest(!isSuggest)
                }}
                onGetCSProducts={onGetCSProducts}
              />
            );
          })
        }
      </View>
      {
        isVisibleSearchProduct &&
        <ModalSearchProduct
          isVisible={isVisibleSearchProduct}
          hideModal={() => {
            setIsVisibleSearchProduct(false)
          }}
          infoGroupSearch={infoGroupSearch}
          updatePromotionProducts={updatePromotionProducts}
          actionDetail={actionDetail}
        />
      }
      {
        isVisibleSearchBarcode &&
        <BarcodeCamera
          isVisible={isVisibleSearchBarcode}
          closeCamera={() => { setIsVisibleSearchBarcode(false) }}
          resultScanBarcode={searchPromotionByBarcode}
        />
      }
    </ScrollView>
  )
}

export default Promotion;
