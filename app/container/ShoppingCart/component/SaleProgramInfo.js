import { StyleSheet, View } from 'react-native';
import React from 'react';
import { MyText } from '../../../components';
import { COLORS } from '../../../styles';


const SaleProgramInfo = ({ saleProgramInfo }) => {
    const { SaleProgramID, SaleProgramName } =
        saleProgramInfo;
    return (
        <View
            style={{
                borderTopWidth: 1,
                borderTopColor: COLORS.bgA7A7A7,
                marginTop: 5,
                paddingHorizontal: 10
            }}>
            <MyText
                text={`${SaleProgramID} - ${SaleProgramName}`}
                style={{
                    marginBottom: 5,
                    fontWeight: 'bold',
                    color: COLORS.txt288AD6,
                    paddingTop: 10
                }}
            />
        </View>
    );
};

export default SaleProgramInfo;

const styles = StyleSheet.create({});
