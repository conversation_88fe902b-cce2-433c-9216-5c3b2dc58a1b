import { constants } from '@constants';
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { COLORS } from '@styles';

const CheckboxOTP = ({ label, checked, onChange }) => {
    return (
        <TouchableOpacity style={styles.container} onPress={onChange}>
            <View style={[styles.checkbox, checked && styles.checked]}>
                {checked && <Text style={styles.checkmark}>✓</Text>}
            </View>
            {label && <Text style={styles.label}>{label}</Text>}
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 10,
        width: constants.width - 20
    },
    checkbox: {
        width: 18,
        height: 18,
        borderWidth: 2,
        borderColor: "gray",
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 10,
    },
    checked: {
        backgroundColor: COLORS.btn288AD6,
        borderColor: COLORS.btn288AD6,

    },
    checkmark: {
        color: '#FFF',
        fontWeight: 'bold',
        fontSize: 10
    },
    label: {
        fontSize: 13,
    },
});

export default CheckboxOTP;
