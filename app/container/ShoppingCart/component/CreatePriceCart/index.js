import React, { Component } from 'react';
import {
    SafeAreaView,
    StyleSheet,
    View,
    TouchableOpacity,
    ScrollView,
    Alert,
    Keyboard
} from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import {
    Icon,
    Button,
    show<PERSON>lockUI,
    hideBlockUI,
    MyText,
    ScanIdentity
} from "@components";
import { constants } from "@constants";
import { helper } from "@common";
import AdjustProduct from "./component/AdjustProduct";
import * as actionShoppingCartCreator from "../../action";
const TYPE_ADJUST = 1;
const TYPE_CREATE = 2;
import { translate } from '@translate';
import { COLORS } from "@styles";

class CreatePriceCart extends Component {

    constructor() {
        super();
        this.state = {
            isVisibleScan: false,
            userAdjustInfo: {},
            totalAdjust: 0
        }
    }

    componentDidMount() {
        this.getUserInfo();
        this.getTotalAdjust();
    }

    onScanIdentity = () => {
        this.setState({ isVisibleScan: true });
    }

    closeCamera = () => {
        this.setState({ isVisibleScan: false });
    }

    render() {
        const { dataCartAdjust: {
            SaleOrderDetails,
            MaxUseCreatePrice,
            IsSOAnKhang
        } } = this.props;
        const {
            isVisibleScan,
            userAdjustInfo,
            totalAdjust
        } = this.state;
        const { isHasRight } = userAdjustInfo;
        const totalRemain = MaxUseCreatePrice + totalAdjust;
        return (
            <SafeAreaView style={{
                flex: 1,
                backgroundColor: COLORS.bgFFFFFF,
            }}>
                {
                    helper.isArray(SaleOrderDetails) &&
                    <View style={{
                        flex: 1
                    }}>
                        <TotalPrice
                            total={totalRemain}
                        />
                        <ScrollView style={{
                            flex: 1
                        }}
                            keyboardShouldPersistTaps={"always"}
                        >
                            <View style={{
                                width: constants.width,
                            }}>
                                {
                                    SaleOrderDetails.map(mainProduct => (
                                        <AdjustProduct
                                            product={mainProduct}
                                            key={mainProduct.SaleOrderDetailID}
                                            updateTotalAdjust={this.updateTotalAdjust(SaleOrderDetails)}
                                            isAnkhang={IsSOAnKhang}
                                        />
                                    ))
                                }
                                {
                                    SaleOrderDetails.map(mainProduct => {
                                        const { saleSaleOrders } = mainProduct;
                                        return (
                                            saleSaleOrders.map(saleProduct => (
                                                <AdjustProduct
                                                    product={saleProduct}
                                                    key={saleProduct.SaleOrderDetailID}
                                                    updateTotalAdjust={this.updateTotalAdjust(SaleOrderDetails)}
                                                    isAnkhang={IsSOAnKhang}
                                                />
                                            ))
                                        )
                                    })
                                }
                                {
                                    // PROMOTION_DELIVERY
                                    SaleOrderDetails.map(mainProduct => {
                                        const { saleDeliverySaleOrders } = mainProduct;
                                        return (
                                            saleDeliverySaleOrders.map(saleProduct => (
                                                <AdjustProduct
                                                    product={saleProduct}
                                                    key={saleProduct.SaleOrderDetailID}
                                                    updateTotalAdjust={this.updateTotalAdjust(SaleOrderDetails)}
                                                    isAnkhang={IsSOAnKhang}
                                                />
                                            ))
                                        )
                                    })
                                }
                                <EmployeeInfo
                                    userInfo={userAdjustInfo}
                                    onScanIdentity={this.onScanIdentity}
                                />
                                <ButtonConfirm
                                    onPress={this.onPressConfirm(SaleOrderDetails, IsSOAnKhang)}
                                    disabled={!isHasRight}
                                />
                                {
                                    isVisibleScan &&
                                    <ScanIdentity
                                        isVisible={isVisibleScan}
                                        closeCamera={this.closeCamera}
                                        resultScanBarcode={(barcode) => {
                                            this.setState({ isVisibleScan: false });
                                            this.checkPermissionUser(barcode);
                                        }}
                                    />
                                }
                            </View>
                        </ScrollView>
                    </View>
                }
            </SafeAreaView>
        );
    }

    checkValidateAdjust = (SaleOrderDetails, IsSOAnKhang) => {
        const { userAdjustInfo: {
            userName
        } } = this.state;
        let isValidatePrice = true;
        let isValidateFile = true;
        SaleOrderDetails.forEach(mainProduct => {
            if (isValidatePrice && isValidateFile) {
                const {
                    saleSaleOrders,
                    AdjustPrice,
                    AdjustPriceTypeID,
                    SalePriceERPVAT,
                    UrlFilesAdjustPrice,
                    isCheck
                } = mainProduct;
                const salePriceAdjust = SalePriceERPVAT + AdjustPrice;
                if (AdjustPrice != 0) {
                    if (AdjustPriceTypeID != TYPE_ADJUST) {
                        if (isCheck) {
                            isValidatePrice = salePriceAdjust > 0;
                            if (!IsSOAnKhang) {
                                for (let i = 0; i < 2; i++) {
                                    const uri = UrlFilesAdjustPrice[i];
                                    if (!helper.IsNonEmptyString(uri)) {
                                        isValidateFile = false;
                                        break;
                                    }
                                }
                            }
                            mainProduct.AdjustPriceTypeID = TYPE_CREATE;
                            mainProduct.AdjustPriceUser = userName;
                        }
                        // Auto Reset AdjustPrice
                        else {
                            mainProduct.AdjustPrice = 0;
                            mainProduct.AdjustPriceTypeID = 0;
                            mainProduct.AdjustPriceUser = "";
                            mainProduct.UrlFilesAdjustPrice = [];
                            mainProduct.MinSalePriceProductForCreatePrice = 0;
                            mainProduct.MaxSalePriceProductForCreatePrice = 0;
                            mainProduct.CompanyCompetitorID = 0;
                            mainProduct.cus_AdjustPriceByStoreID = 0;
                        }
                    }
                }
                else {
                    mainProduct.AdjustPriceTypeID = 0;
                    mainProduct.AdjustPriceUser = "";
                    mainProduct.UrlFilesAdjustPrice = [];
                    mainProduct.MinSalePriceProductForCreatePrice = 0;
                    mainProduct.MaxSalePriceProductForCreatePrice = 0;
                    mainProduct.CompanyCompetitorID = 0;
                    mainProduct.cus_AdjustPriceByStoreID = 0;
                }
                saleSaleOrders.forEach(saleProduct => {
                    if (isValidatePrice && isValidateFile) {
                        const {
                            AdjustPrice,
                            AdjustPriceTypeID,
                            SalePriceERPVAT,
                            UrlFilesAdjustPrice,
                            isCheck
                        } = saleProduct;
                        const salePriceAdjust = SalePriceERPVAT + AdjustPrice;
                        if (AdjustPrice != 0) {
                            if (AdjustPriceTypeID != TYPE_ADJUST) {
                                if (isCheck) {
                                    isValidatePrice = salePriceAdjust > 0;
                                    if (!IsSOAnKhang) {
                                        for (let i = 0; i < 2; i++) {
                                            const uri = UrlFilesAdjustPrice[i];
                                            if (!helper.IsNonEmptyString(uri)) {
                                                isValidateFile = false;
                                                break;
                                            }
                                        }
                                    }
                                    saleProduct.AdjustPriceTypeID = TYPE_CREATE;
                                    saleProduct.AdjustPriceUser = userName;
                                }
                                // Auto Reset AdjustPrice
                                else {
                                    saleProduct.AdjustPrice = 0;
                                    saleProduct.AdjustPriceTypeID = 0;
                                    saleProduct.AdjustPriceUser = "";
                                    saleProduct.UrlFilesAdjustPrice = [];
                                    saleProduct.MinSalePriceProductForCreatePrice = 0;
                                    saleProduct.MaxSalePriceProductForCreatePrice = 0;
                                    saleProduct.CompanyCompetitorID = 0;
                                    saleProduct.cus_AdjustPriceByStoreID = 0;
                                }
                            }
                        }
                        else {
                            saleProduct.AdjustPriceTypeID = 0;
                            saleProduct.AdjustPriceUser = "";
                            saleProduct.UrlFilesAdjustPrice = [];
                            saleProduct.MinSalePriceProductForCreatePrice = 0;
                            saleProduct.MaxSalePriceProductForCreatePrice = 0;
                            saleProduct.CompanyCompetitorID = 0;
                            saleProduct.cus_AdjustPriceByStoreID = 0;
                        }
                    }
                });
            }
        });
        return { isValidatePrice, isValidateFile };
    }

    getUserInfo = () => {
        const { userInfo: {
            userName
        } } = this.props;
        this.props.actionShoppingCart.getAdjustUserInfo({
            "userName": userName,
            "adjustPriceTypeID": TYPE_CREATE
        }).then(userInfo => {
            this.setState({
                userAdjustInfo: {
                    ...userInfo,
                    isHasRight: true,
                }
            })
        }).catch(msgError => {
            if (msgError == 'NONEPERMISSIONADJUSTPRICE') {
                this.setState({
                    userAdjustInfo: {
                        isHasRight: false,
                    }
                })
            }
            else {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            onPress: () => {
                                this.props.navigation.goBack();
                            },
                            style: "cancel"
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            onPress: this.getUserInfo,
                            style: "default"
                        },
                    ],
                    { cancelable: false },
                );
            }
        });
    }

    checkPermissionUser = (userName) => {
        showBlockUI();
        this.props.actionShoppingCart.getAdjustUserInfo({
            "userName": userName,
            "adjustPriceTypeID": TYPE_CREATE
        }).then(userInfo => {
            hideBlockUI();
            this.setState({
                userAdjustInfo: {
                    ...userInfo,
                    isHasRight: true,
                }
            })
        }).catch(msgError => {
            if (msgError == 'NONEPERMISSIONADJUSTPRICE') {
                Alert.alert("",
                    `User ${userName} ${translate('shoppingCart.cannot_adjust_price')}`,
                    [
                        {
                            text: 'OK',
                            onPress: hideBlockUI,
                        },
                    ],
                );
            }
            else {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            onPress: hideBlockUI,
                            style: "cancel"
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            onPress: () => this.checkPermissionUser(userName),
                            style: "default"
                        },
                    ],
                    { cancelable: false },
                );
            }
        });
    }

    modifyShoppingCart = (data) => {
        showBlockUI();
        this.props.actionShoppingCart.modifyShoppingCart(data).then(dataCart => {
            this.getCartPromotion(dataCart);
            hideBlockUI();
            this.props.navigation.goBack();
        }).catch(error => {
            Alert.alert(translate('common.notification_uppercase'), error.msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: () => this.modifyShoppingCart(data)
                    }
                ]
            )
        })
    }

    getCartPromotion = (dataCart) => {
        this.props.actionShoppingCart.getCartPromotion(dataCart);
    }

    createPriceCart = (dataCartAdjust) => {
        showBlockUI();
        this.props.actionShoppingCart.getInfoCreatePrice(dataCartAdjust).then(info => {
            if (info) {
                Alert.alert("", info,
                    [
                        {
                            text: translate('common.btn_skip'),
                            onPress: hideBlockUI,
                            style: "cancel"
                        },
                        {
                            text: translate('common.btn_continue'),
                            onPress: () => {
                                this.modifyShoppingCart({
                                    cartRequest: dataCartAdjust,
                                    discountCode: "",
                                    giftCode: "",
                                    promotionGroups: []
                                });
                            },
                            style: "default"
                        }
                    ]);
            }
            else {
                this.modifyShoppingCart({
                    cartRequest: dataCartAdjust,
                    discountCode: "",
                    giftCode: "",
                    promotionGroups: []
                });
            }
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: () => this.createPriceCart(dataCartAdjust)
                    }
                ]
            )
        })
    }

    onPressConfirm = (SaleOrderDetails, IsSOAnKhang) => () => {
        Keyboard.dismiss();
        const {
            isValidatePrice,
            isValidateFile
        } = this.checkValidateAdjust(SaleOrderDetails, IsSOAnKhang);
        if (!isValidatePrice) {
            Alert.alert("", translate('shoppingCart.please_enter_adjusted_price'));
        }
        else if (!isValidateFile) {
            Alert.alert("", translate('shoppingCart.please_upload_mandatory_file'));
        }
        else {
            const { dataCartAdjust } = this.props;
            this.createPriceCart(dataCartAdjust);
        }
    }

    getTotalAdjust = () => {
        const { dataCartAdjust: {
            SaleOrderDetails,
        } } = this.props;
        const newTotalAdjust = getTotalAdjust(SaleOrderDetails);
        this.setState({ totalAdjust: newTotalAdjust });
    }

    updateTotalAdjust = (SaleOrderDetails) => () => {
        const newTotalAdjust = getTotalAdjust(SaleOrderDetails);
        this.setState({ totalAdjust: newTotalAdjust });
    }
}

const FieldInfo = ({
    name,
    value
}) => {
    return (
        <MyText
            text={name}
            style={{
                color: COLORS.txt444444,
                fontWeight: 'bold'
            }}>
            <MyText
                text={value}
                style={{
                    color: COLORS.txt444444,
                    fontWeight: 'normal'
                }}
            />
        </MyText>
    )
}

const EmployeeInfo = ({
    userInfo,
    onScanIdentity
}) => {
    const {
        isHasRight,
        userName,
        fullName,
        positionName,
        mobi,
        bcnbDepartmentName
    } = userInfo;
    return (
        <View style={{
            width: constants.width,
        }}>
            <MyText
                text={translate('shoppingCart.scan_employee_code_adjust_price')}
                style={{
                    color: COLORS.txt1E88E5,
                    fontWeight: 'bold',
                    marginHorizontal: 10,
                    marginBottom: 4
                }}
            />
            {
                isHasRight &&
                <View style={{
                    width: constants.width,
                    paddingHorizontal: 16
                }}>
                    <FieldInfo
                        name={translate('shoppingCart.employee')}
                        value={`${userName} - ${fullName}`}
                    />

                    <FieldInfo
                        name={translate('shoppingCart.department')}
                        value={bcnbDepartmentName}
                    />

                    <FieldInfo
                        name={translate('shoppingCart.position')}
                        value={positionName}
                    />
                    <FieldInfo
                        name={translate('shoppingCart.text_input_phone')}
                        value={mobi}
                    />
                </View>
            }
            <View style={{
                width: constants.width,
                paddingHorizontal: 10
            }}>
                <TouchableOpacity style={{
                    justifyContent: "center",
                    alignItems: "center",
                }}
                    onPress={onScanIdentity}
                >
                    <Icon
                        iconSet={"MaterialCommunityIcons"}
                        name={"barcode-scan"}
                        color={COLORS.icFFD400}
                        size={90}
                    />
                </TouchableOpacity>
                <MyText
                    text={
                        translate('shoppingCart.description_scan_identity_2')
                    }
                    style={{
                        color: COLORS.txt444444,
                        fontStyle: 'italic',
                        textAlign: 'center'
                    }}
                />
            </View>
        </View>
    );
}

const ButtonConfirm = ({ onPress, disabled }) => {
    return (
        <View style={{
            width: constants.width,
            alignItems: "center",
            justifyContent: "center",
            paddingVertical: 8,
            opacity: disabled ? 0.5 : 1
        }}>
            <Button
                text={translate('shoppingCart.btn_confirm_uppercase')}
                styleContainer={{
                    paddingHorizontal: 40,
                    borderRadius: 4,
                    backgroundColor: COLORS.btn1E88E5,
                    height: 44,
                }}
                styleText={{
                    color: COLORS.txtFFFFFF,
                    fontSize: 14,
                    fontWeight: "bold"
                }}
                onPress={onPress}
                disabled={disabled}
            />
        </View>
    );
}

const mapStateToProps = function (state) {
    return {
        dataCartAdjust: state.shoppingCartReducer.dataCartAdjust,
        userInfo: state.userReducer
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionShoppingCart: bindActionCreators(actionShoppingCartCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(CreatePriceCart);

const TotalPrice = ({ total }) => {
    return (
        <View style={{
            backgroundColor: COLORS.bgFAFAFA,
            flexDirection: 'row',
            width: constants.width,
            justifyContent: 'space-between',
            paddingHorizontal: 10,
            height: 40,
            borderTopWidth: StyleSheet.hairlineWidth,
            borderTopColor: COLORS.bdFFFFFF,
        }}>
            <View style={{
                flexDirection: 'row',
                alignSelf: 'flex-end',
                alignItems: 'center',
                height: 40,
            }}>
                <MyText
                    text={translate('shoppingCart.remaining_limit')}
                    style={{
                        color: COLORS.txt147EFB,
                        fontWeight: 'bold'
                    }}>
                    <MyText
                        text={translate('shoppingCart.provisional')}
                        style={{
                            color: COLORS.txt666666,
                            fontStyle: 'italic',
                            fontWeight: 'normal'
                        }}
                    />
                </MyText>
            </View>

            <View style={{
                alignSelf: 'flex-end',
                justifyContent: 'center',
                height: 40,
            }}>
                <MyText
                    text={helper.convertNum(total)}
                    addSize={2}
                    style={{
                        color: COLORS.txtF50537,
                        fontWeight: 'bold'
                    }}
                />
            </View>
        </View>
    );
}

const getTotalAdjust = (SaleOrderDetails) => {
    let total = 0;
    SaleOrderDetails.forEach(mainProduct => {
        const {
            saleSaleOrders,
            AdjustPrice,
            AdjustPriceTypeID,
            // PROMOTION_DELIVERY
            saleDeliverySaleOrders
        } = mainProduct;
        if (AdjustPriceTypeID != TYPE_ADJUST) {
            total += AdjustPrice;
        }
        saleSaleOrders.forEach(saleProduct => {
            const {
                AdjustPrice,
                AdjustPriceTypeID,
            } = saleProduct;
            if (AdjustPriceTypeID != TYPE_ADJUST) {
                total += AdjustPrice;
            }
        });
        // PROMOTION_DELIVERY
        saleDeliverySaleOrders.forEach(saleProduct => {
            const {
                AdjustPrice,
                AdjustPriceTypeID,
            } = saleProduct;
            if (AdjustPriceTypeID != TYPE_ADJUST) {
                total += AdjustPrice;
            }
        });
    });
    return total;
}