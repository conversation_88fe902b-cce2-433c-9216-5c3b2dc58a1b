import {
    <PERSON><PERSON>,
    Safe<PERSON>reaView,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import React from 'react';
import { COLORS } from '@styles';
import { constants } from '@constants';
import { Button, Icon, MyText } from '@components';

const QuestionModal = ({
    isShow,
    hideModal,
    onPress,
    questions,
    onSwitchAnswer
}) => {
    return (
        <View>
            <Modal
                visible={isShow}
                animationType="fade"
                transparent
                onRequestClose={hideModal}>
                <SafeAreaView
                    style={{
                        backgroundColor: 'white',
                        flex: 1,
                        alignItems: 'center'
                    }}>
                    <View style={styles.header}>
                        <View
                            style={{
                                flex: 1,
                                alignItems: 'flex-start'
                            }}
                        />
                        <Text style={styles.headerText}>
                            BỘ CÂU HỎI TƯ VẤN GIAO HÀNG LẮP ĐẶT
                        </Text>
                        <View style={{ flex: 1 }} />
                    </View>
                    <View style={styles.headerShadow} />

                    <ScrollView
                        style={{
                            flex: 1,
                            width: constants.width - 20,
                            paddingTop: 10
                        }}>
                        {Object.entries(questions).map(([key, value], index) => {
                            return (
                                <QuestionList
                                    key={index}
                                    questionList={Array.from(
                                        Object.values(value)
                                    )}
                                    onSwitchAnswer={onSwitchAnswer}
                                    keyQuestion={key}
                                />
                            );
                        })}
                    </ScrollView>

                    <View style={styles.bottomBar}>
                        <Button
                            onPress={onPress}
                            text="Xác Nhận Thông Tin"
                            styleContainer={[
                                {
                                    backgroundColor: "#EE4D2D",
                                    borderColor: "#EE4D2D",
                                    borderRadius: constants.getSize(10),
                                    borderWidth: 2,
                                    height: constants.getSize(40),
                                    marginHorizontal: 15,
                                    width: constants.width - 50
                                }
                            ]}
                            styleText={{
                                fontSize: 14,
                                fontWeight: 'bold',
                                color: 'white'
                            }}
                        />
                    </View>
                </SafeAreaView>
            </Modal>
        </View>
    );
};

export default QuestionModal;

const styles = StyleSheet.create({
    bottomBar: {
        flexDirection: 'row',
        justifyContent: 'center'
    },

    header: {
        alignItems: 'center',
        borderBottomColor: COLORS.bgD1D3D8,
        borderBottomWidth: 1,
        elevation: 5,
        flexDirection: 'row',
        padding: 8
    },
    headerShadow: {
        elevation: 4,
        backgroundColor: 'lightgrey'
    },
    headerText: {
        color: "#EE4D2D",
        fontSize: 16,
        fontWeight: 'bold',
        textAlign: 'center',
        textAlignVertical: 'center'
    }
});


const QuestionList = ({ questionList, onSwitchAnswer, keyQuestion }) => {
    const { subgroupName } = questionList?.[0];
    return (
        <View
            style={{
                backgroundColor: COLORS.bgFFFFFF,
                paddingTop: 10
            }}>
            <MyText
                style={{
                    fontSize: 18,
                    color: "#EE4D2D"
                }}>
                {`Nhóm hàng: ${subgroupName || "Chưa rõ nữa"}`}
            </MyText>
            {questionList.map((question) => {
                return (
                    <QuestionDetail
                        key={question.questionId}
                        question={question}
                        onSwitchAnswer={onSwitchAnswer}
                        keyQuestion={keyQuestion}
                    />
                );
            })}
        </View>
    );
};

const QuestionDetail = ({ question, onSwitchAnswer, keyQuestion }) => {
    const { questionValue, answers, questionId } = question;
    return (
        <View style={{ paddingTop: 10 }}>
            <MyText>{questionValue}</MyText>
            <RadioAnsWer
                answers={answers}
                onSwitchAnswer={onSwitchAnswer({ questionId, keyQuestion })}
            />
        </View>
    );
};

const ItemRadio = ({ title, isCheck, onPressItem }) => {
    return (
        <TouchableOpacity
            style={{
                flexDirection: 'row',
                paddingVertical: 4,
                width: "33%"
            }}
            activeOpacity={1}
            onPress={onPressItem}>
            <Icon
                iconSet="MaterialIcons"
                name={isCheck ? 'radio-button-on' : 'radio-button-off'}
                color={isCheck ? "#EE4D2D" : COLORS.ic333333}
                size={14}
                style={{ marginTop: 2 }}
            />
            <MyText
                text={title}
                style={{
                    color: COLORS.txt333333,
                    marginLeft: 4
                }}
            />
        </TouchableOpacity>
    );
};

const RadioAnsWer = ({ answers, onSwitchAnswer }) => {
    const onPressItem = (isCheck, ele) => () => {
        if (!isCheck) {
            onSwitchAnswer(ele);
        }
    };
    return (
        <View
            style={{
                flexDirection: 'row',
                paddingTop: 10,
                flex: 1,
                flexWrap: 'wrap',

            }}>
            {answers.map((ele) => {
                const { answerValue, isSelected, answerId } = ele;
                const isCheck = isSelected;
                return (
                    <ItemRadio
                        key={answerId}
                        title={answerValue}
                        isCheck={isCheck}
                        onPressItem={onPressItem(isCheck, ele)}
                    />
                );
            })}
        </View>
    );
};
