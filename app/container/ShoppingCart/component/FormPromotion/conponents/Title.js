import React from 'react';
import { MyText } from '@components';
import { COLORS } from '@styles';
import { constants } from '@constants';

function Title({ title, isRequired }) {
    return (<MyText
        text={`${title}:`}
        addSize={-1.5}
        style={{
            color: COLORS.txt333333,
            fontWeight: "bold",
            fontStyle: "italic",
            width: constants.width - 20,
            paddingBottom: 10
        }}>
        {
            isRequired &&
            <MyText
                text={"*"}
                addSize={-1.5}
                style={{
                    color: COLORS.txtFF0000
                }}
            />
        }
    </MyText>)
}

export default Title;