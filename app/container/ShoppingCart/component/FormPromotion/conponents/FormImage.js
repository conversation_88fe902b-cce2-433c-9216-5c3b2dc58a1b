

import React from 'react';
import { TouchableOpacity, View } from "react-native"
import { MyText, Icon, ImageCDN } from '@components';
import { COLORS } from '@styles';
import { constants } from '@constants';
import Title from './Title';

function FormImage({ title, value, onChange, onClear, isRequired, index }) {
    return (
        <View style={{ width: constants.width, alignItems: 'center', paddingVertical: 10 }}>
            <Title
                title={title}
                isRequired={isRequired}
            />
            {!value
                ? <TouchableOpacity style={{
                    width: 160,
                    height: 160,
                    justifyContent: "center",
                    alignItems: "center",
                    backgroundColor: COLORS.btnF5F5F5,
                }}
                    onPress={onChange}
                >
                    <Icon
                        iconSet={"Ionicons"}
                        name={"ios-camera"}
                        color={COLORS.icFFB23F}
                        size={60}
                    />
                </TouchableOpacity>
                : <View style={{
                    width: 160,
                    height: 160,
                    justifyContent: "flex-start",
                    alignItems: "center",
                }}>
                    <ImageCDN
                        style={{
                            width: 160,
                            height: 160,
                        }}
                        uri={value}
                        resizeMode={"contain"}
                    />
                    <TouchableOpacity style={{
                        padding: 5,
                        justifyContent: "center",
                        alignItems: "center",
                        position: "absolute",
                        top: 0,
                        right: 0,
                        backgroundColor: COLORS.btnF2F2F2
                    }}
                        onPress={onClear}
                    >
                        <MyText
                            text={"Xoá"}
                            style={{
                                color: COLORS.txtD0021B
                            }}
                        />
                    </TouchableOpacity>
                </View>}
        </View>
    );
}

export default FormImage;