import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import {
    MyText,
    Icon,
} from "@components";
import { constants } from '@constants';
import { COLORS } from "@styles";
import Title from './Title';

const FromRadio = ({ title, value, onChange, onClear, isRequired, index }) => {
    const onPress = (isCheck, ele) => () => {
        if (!isCheck) {
            onChange(ele.value)
        }
    }
    const renderItem = (ele) => {
        const isCheck = ele.value == value;
        return (<TouchableOpacity
            style={{
                flexDirection: "row",
                paddingVertical: 5,
                marginHorizontal: 10
            }}
            activeOpacity={1}
            onPress={onPress(isCheck, ele)}
        >
            <Icon
                iconSet={"MaterialIcons"}
                name={isCheck ? "radio-button-on" : "radio-button-off"}
                color={isCheck ? COLORS.icFF8900 : COLORS.ic333333}
                size={14}
                style={{ marginTop: 2 }}
            />
            <MyText style={{
                color: COLORS.txt333333,
                marginLeft: 4
            }}
                text={ele.label}
            />
        </TouchableOpacity>);
    }
    return (
        <View style={{ width: constants.width, alignItems: 'center' }}>
            <Title
                title={title}
                isRequired={isRequired}
            />
            <View style={{
                flexDirection: "row",
                flexWrap: 'wrap',
                width: constants.width - 20
            }}>
                {RADIO.map(renderItem)}
            </View>
        </View>
    );
}

export default FromRadio;
const RADIO = [{ label: "Có", value: "YES" }, { label: "Không", value: "NO" }];