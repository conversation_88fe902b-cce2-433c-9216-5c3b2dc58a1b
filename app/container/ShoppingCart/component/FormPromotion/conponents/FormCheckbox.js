import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import {
    MyText,
    Icon,
} from "@components";
import { constants } from '@constants';
import { COLORS } from "@styles";

const FormCheckbox = ({ title, value, onChange, onClear, isRequired }) => {
    const onPress = () => {
        onChange(!value)
    }
    return (
        <View style={{ width: constants.width, alignItems: 'center', paddingVertical: 10 }}>
            <TouchableOpacity
                style={{
                    flexDirection: "row",
                    width: constants.width - 20,
                    alignItems: 'center',
                }}
                onPress={onPress}
                activeOpacity={0.8}
            >
                <Icon
                    iconSet={"Ionicons"}
                    name={value ? "ios-checkbox-outline" : "ios-square-outline"}
                    size={14}
                    color={value ? COLORS.ic147EFB : COLORS.txt288AD6}
                />
                <MyText
                    text={title}
                    style={{
                        color: value ? COLORS.txt147EFB : COLORS.txt288AD6,
                        marginLeft: 4,
                        fontWeight: 'bold',
                        width: constants.width - 40
                    }}>
                </MyText>
            </TouchableOpacity>
        </View>
    );
}

export default FormCheckbox;