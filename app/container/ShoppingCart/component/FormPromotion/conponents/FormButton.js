

import React from 'react';
import { View } from 'react-native';
import { Button } from '@components';
import { COLORS } from '@styles';
import { constants } from '@constants';

function FormButton({ title, onSubmit }) {
    return (<View style={{
        width: constants.width, alignItems: 'center',
        paddingVertical: 10
    }}>
        <Button
            text={title}
            onPress={onSubmit}
            styleContainer={{
                flexDirection: 'row',
                height: 40,
                width: constants.width - 20,
                backgroundColor: COLORS.bg147EFB,
                borderRadius: 4
            }}
            styleText={{
                color: COLORS.txtFFFFFF,
                fontSize: 16,
                fontWeight: 'bold'
            }}
        />
    </View>)
}

export default FormButton;