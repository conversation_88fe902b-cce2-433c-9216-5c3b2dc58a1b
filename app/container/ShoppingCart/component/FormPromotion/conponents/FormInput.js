

import React from 'react';
import { View } from 'react-native';
import { COLORS } from '@styles';
import { constants } from '@constants';
import InputLoyalty from '../../InputLoyalty';

function FormInput({ title, value, onChange, onClear, isRequired, onOpenBarcode, index }) {
    return (
        <View style={{ width: constants.width, alignItems: "center", paddingVertical: 10 }}>
            <View>
                <InputLoyalty
                    title={title}
                    isRequired={isRequired}
                    styleInput={{
                        borderWidth: 1,
                        borderRadius: 4,
                        borderColor: COLORS.bdCCCCCC,
                        paddingHorizontal: 10,
                        justifyContent: 'center',
                        paddingVertical: 8,
                        backgroundColor: COLORS.bgFFFFFF
                    }}
                    textAlignVertical={'center'}
                    underlineColorAndroid={'transparent'}
                    placeholder={""}
                    value={value || ""}
                    onChangeText={onChange}
                    keyboardType={"default"}
                    returnKeyType={"done"}
                    width={constants.width - 20}
                    height={40}
                    clearText={onClear}
                    isVisibleBarCodePhone
                    handleGetLoyaltyPhone={
                        onOpenBarcode
                    }
                />


            </View>

        </View>
    )
}

export default FormInput;