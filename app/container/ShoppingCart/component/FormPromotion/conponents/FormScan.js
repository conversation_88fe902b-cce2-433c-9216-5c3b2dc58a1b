import React from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Keyboard } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS } from '@styles';

const FormScan = ({
    title,
    value,
    onChange,
    onClear,
    isRequired,
    onOpenBarcode,
    index,
    editable = true,
    ...props
}) => {
    return (
        <View style={styles.container}>
            <View style={styles.titleContainer}>
                <Text style={styles.title}>{title}</Text>
                {isRequired && <Text style={styles.required}>*</Text>}
            </View>

            <View style={styles.inputContainer}>
                <TextInput
                    style={[
                        styles.input,
                        !editable && styles.disabledInput,
                    ]}
                    value={value}
                    onChangeText={onChange}
                    editable={editable}
                    {...props}
                />

                {value ? (
                    <TouchableOpacity onPress={onClear} style={styles.iconButton}>
                        <Icon name="close" size={20} color={COLORS.txt808080} />
                    </TouchableOpacity>
                ) : (
                    onOpenBarcode && (
                        <TouchableOpacity
                            onPress={onOpenBarcode}
                            style={styles.iconButton}
                        >
                            <Icon name="barcode-scan" size={20} color={COLORS.bgF49B0C} />
                        </TouchableOpacity>
                    )
                )}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        padding: 16,
        width: '100%',
    },
    titleContainer: {
        flexDirection: 'row',
        marginBottom: 8,
    },
    title: {
        fontSize: 14,
        color: COLORS.txt333333,
    },
    required: {
        color: COLORS.bgEA1D5D,
        marginLeft: 4,
    },
    inputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: COLORS.bdE4E4E4,
        borderRadius: 5,
        backgroundColor: COLORS.bgFFFFFF,
        height: 40,
    },
    input: {
        flex: 1,
        paddingHorizontal: 12,
        fontSize: 14,
        height: 38,
    },
    disabledInput: {
        backgroundColor: COLORS.bgF5F5F5,
        color: COLORS.txt808080,
    },
    iconButton: {
        paddingHorizontal: 12,
        height: '100%',
        justifyContent: 'center',
    },
});

export default FormScan;