import React, { useRef, useState } from 'react';
import { SafeAreaView, Alert, View, StyleSheet } from 'react-native';
import { CaptureCamera, showBlockUI, hideBlockUI, BarcodeCamera, MyText } from '@components';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { API_CONST, ENUM } from "@constants";
import { COLORS } from '@styles';
import { helper } from '@common';
import {
    FormButton,
    FormInput,
    FormImage,
    FromRadio,
    FormScan
} from "./conponents";
import * as actionShoppingCartCreator from "../../action";
const { API_GET_IMAGE_CDN, API_GET_IMAGE_CDN_NEW } = API_CONST;
const { FILE_PATH: { FORM_PROMOTION } } = ENUM;

function FormPromotion({ route, navigation, actionShoppingCart, dataShoppingCart }) {
    console.log('FormPromotion', route.params);
    const { promoRequireInfors, dataCart = {}, onChange = () => { } } = route.params;

    const [formData, setFormData] = useState(promoRequireInfors);
    const [idCam, setIDCam] = useState(-1);
    const [idBarcode, setIDBarcode] = useState(-1);
    const [isAllowID, setIsAllowID] = useState(true)

    const isScanIDCard = useRef(-1);


    const onChangeValue = (id) => (value) => {
        formData[id].Value = value;
        setFormData([...formData]);
    }
    const onClearValue = (id) => () => {
        formData[id].Value = "";
        setFormData([...formData]);
    }
    const onCapture = (id) => () => {
        setIDCam(id)
    }
    const onBarcode = (id) => () => {
        setIDBarcode(id)
    }
    const onTakePicture = (photo) => {
        const id = idCam;
        setIDCam(-1);
        const body = helper.createFormData({ uri: photo.uri, type: 'image/jpg', name: `${Date.now()}FormPromotion.jpg`, path: FORM_PROMOTION });
        showBlockUI();
        actionShoppingCartCreator.getImageCDN(body).then(cdnImages => {
            hideBlockUI();
            formData[id].Value = API_GET_IMAGE_CDN_NEW + cdnImages[0];
            setFormData([...formData]);
        }).catch(error => {
            hideBlockUI();
            console.log("onTakePicture error");
        })
    }
    const isCCCDQRCode = (data) => {
        try {
            const parsed = JSON.parse(data);
            return (
                typeof parsed === 'object' &&
                ['fullName', 'id'].every(key => key in parsed)
            );
        } catch {
            // Nếu không phải JSON, kiểm tra dạng | phân cách
            const parts = data.split('|');
            return (
                parts.length >= 6 &&
                /^\d{12}$/.test(parts[0]) // ID đủ 12 số
            );
        }
    };

    const onResultBarcode = (value) => {
        const id = idBarcode;
        setIDBarcode(-1);

        const updatedFormData = [...formData];

        if (isScanIDCard.current != -1) {
            if (isCCCDQRCode(value)) {
                let displayValue = value;

                try {
                    const info = JSON.parse(value);
                    const birth = info.dateOfBirth || '';
                    const birthYear = birth.length === 8 ? birth.slice(-4) : '';
                    displayValue = `${info.id}-${info.fullName}${birthYear ? '-' + birthYear : ''}`;
                    setIsAllowID(birthYear == "1975")
                } catch {
                    const parts = value.split('|');
                    if (parts.length >= 6) {
                        const idPart = parts[0];
                        const fullName = parts[2];
                        const dob = parts[3]; // ddmmyyyy
                        const birthYear = /^\d{8}$/.test(dob) ? dob.slice(-4) : '';
                        setIsAllowID(birthYear == "1975")
                        displayValue = `${idPart}-${fullName}${birthYear ? '-' + birthYear : ''}`;
                    } else {
                        console.log('Dữ liệu không rõ định dạng:', value);
                    }
                }

                updatedFormData[id] = { ...updatedFormData[id], Value: displayValue };
            }
            else {
                Alert.alert('Thông báo', `Dữ liệu không đùng định dạng`);
            }
            isScanIDCard.current = -1
        }

        else {
            updatedFormData[id] = { ...updatedFormData[id], Value: value };
        }

        setFormData(updatedFormData);
    };

    const rederItem = (ele, id) => {
        const { Control, TypeID, ProductName, isDisplayName } = ele;
        switch (Control) {
            case INPUT_CONTROL.camera:
                return (
                    <View>
                        {isDisplayName && <NamePromotion productName={ProductName} />}
                        <FormImage
                            title={ele.RequireInforDescriptionName}
                            value={ele.Value}
                            onChange={onCapture(id)}
                            onClear={onClearValue(id)}
                            isRequired={ele.RequireInforRequireTypeID == 1}
                            key={`${TypeID}_${id}`}
                            index={id}
                        />
                    </View>

                );
            case INPUT_CONTROL.radio:
                return (
                    <View>
                        {isDisplayName && <NamePromotion productName={ProductName} />}
                        <FromRadio
                            title={ele.RequireInforDescriptionName}
                            value={ele.Value}
                            onChange={onChangeValue(id)}
                            onClear={onClearValue(id)}
                            isRequired={ele.RequireInforRequireTypeID == 1}
                            key={`${TypeID}_${id}`}
                            index={id}
                        />
                    </View>
                )
            case INPUT_CONTROL.textbox:
                return (
                    <View>
                        {isDisplayName && <NamePromotion productName={ProductName} />}
                        {
                            helper.configPromotion1975(ele.PromotionID) ? <FormScan
                                title={ele.RequireInforDescriptionName}
                                value={ele.Value}
                                onChange={onChangeValue(id)}
                                onClear={onClearValue(id)}
                                isRequired={ele.RequireInforRequireTypeID == 1}
                                onOpenBarcode={() => {
                                    onBarcode(id)();
                                    isScanIDCard.current = id
                                }}
                                index={id}
                                editable={false}
                            /> : <FormInput
                                title={ele.RequireInforDescriptionName}
                                value={ele.Value}
                                onChange={onChangeValue(id)}
                                onClear={onClearValue(id)}
                                isRequired={ele.RequireInforRequireTypeID == 1}
                                key={`${TypeID}_${id}`}
                                onOpenBarcode={onBarcode(id)}
                                index={id}
                            />
                        }


                    </View>

                )
            default:
                return null;
        }
    }


    const onSubmit = () => {
        const msg = checkDataInvalid(formData);
        if (msg) {
            Alert.alert('Thông báo', `Vui lòng nhập thông tin: ${msg}`);
        }
        else
            if (!isAllowID) {
                Alert.alert('Thông báo', `Số CCCD không phải sinh năm 1975, không thỏa điều kiện Khuyến mãi.`);

            }
            else {
                const imeiPromo = formData.find(ele => (ele.RequireInforRequireTypeID == 1) && (ele.Control == INPUT_CONTROL.textbox));
                if (imeiPromo) {
                    showBlockUI();
                    actionShoppingCart.checkPromotionIMEI(imeiPromo).then(() => {
                        hideBlockUI();
                        updatePromoRequireInfors();
                    }).catch(msgError => {
                        Alert.alert('Thông báo', msgError, [{ onPress: hideBlockUI }]);
                    });
                }
                else {
                    updatePromoRequireInfors();
                }
            }
    }


    const updatePromoRequireInfors = () => {
        const updatePromo = (details) => {
            if (!details) return;
            details.forEach(({ SaleOrderDetailID, PromoRequireInfors }, idx, arr) => {
                if (PromoRequireInfors) {
                    arr[idx].PromoRequireInfors = formData.filter(ele => ele.SaleOrderDetailID === SaleOrderDetailID);
                }
            });
        };

        if (!helper.IsEmptyObject(dataCart)) {
            updatePromo(dataCart.SaleOrderDetails);
            onChange()
            navigation.goBack();
            return;
        }

        const newDataShoppingCart = helper.deepCopy(dataShoppingCart);

        // Cập nhật cho sản phẩm chính
        updatePromo(newDataShoppingCart.SaleOrderDetails);

        // Cập nhật cho sản phẩm quà tặng
        newDataShoppingCart.SaleOrderDetails?.forEach(({ giftSaleOrders }) => {
            updatePromo(giftSaleOrders);
        });

        actionShoppingCart.setDataShoppingCart(newDataShoppingCart);
        navigation.goBack();
    };


    return (<SafeAreaView style={{ flex: 1, backgroundColor: COLORS.bgFFFFFF }}>
        <KeyboardAwareScrollView
            style={{ flex: 1, paddingTop: 10 }}
            enableResetScrollToCoords={false}
            keyboardShouldPersistTaps="always"
            bounces={false}
            overScrollMode="always"
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            extraScrollHeight={60}
        >
            {formData.map(rederItem)}
        </KeyboardAwareScrollView>
        {(idCam != -1) && <CaptureCamera
            isVisibleCamera={true}
            takePicture={onTakePicture}
            closeCamera={() => {
                setIDCam(-1);
            }}
            selectPicture={() => { }}
            disabledUploadImage={true}
        />}
        {(idBarcode != -1) && <BarcodeCamera
            isVisible={true}
            closeCamera={() => {
                setIDBarcode(-1);
            }}
            resultScanBarcode={onResultBarcode}
        />}
        <FormButton
            onSubmit={onSubmit}
            title={"Tiếp tục"}
        />

    </SafeAreaView>);
}


const mapStateToProps = function (state) {
    return {
        dataShoppingCart: state.shoppingCartReducer.dataShoppingCart,
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionShoppingCart: bindActionCreators(actionShoppingCartCreator, dispatch)
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(FormPromotion);


const INPUT_CONTROL = {
    'camera': 'CAMERA',
    'textbox': 'TEXTBOX',
    'radio': 'RADIO',
}
export const checkDataInvalid = (data) => {
    let msg = '';
    if (helper.IsNonEmptyArray(data)) {
        data.forEach(ele => {
            if (!ele.Value && (ele.RequireInforRequireTypeID == 1)) {
                msg += `\n-${ele.RequireInforDescriptionName}`;
            }
        })
    }
    return msg;
}

const NamePromotion = ({ productName }) => {
    return (
        <View style={{
            paddingHorizontal: 10,
            paddingVertical: 8,
            borderWidth: 1,
            borderColor: COLORS.bgA7A7A7,
            backgroundColor: COLORS.btn5482AB,
            borderTopWidth: StyleSheet.hairlineWidth,
            borderTopColor: COLORS.bdFFFFFF,
            justifyContent: "center",
            alignContent: "center"
        }}>
            <MyText style={{ paddingBottom: 4, color: COLORS.bdFFFFFF }} text={"Tên sản phẩm: "} >
                <MyText style={{ fontWeight: "bold", fontSize: 16, marginRight: 10, color: COLORS.bdFFFFFF }} text={productName} />
            </MyText>
        </View>
    )
}


