import React, { Component } from 'react';
import {
    SafeAreaView,
    View,
    TouchableOpacity,
    ScrollView,
    Alert,
    SectionList,
    Text
} from 'react-native';
import { launchImageLibrary } from 'react-native-image-picker';
import { connect } from 'react-redux';
import {
    Icon,
    Button,
    showBlockUI,
    hideBlockUI,
    CaptureCamera,
    ImageCDN,
    MyText
} from '@components';
import { constants, API_CONST, ENUM } from '@constants';
import { helper, dateHelper } from '@common';
import { translate } from '@translate';
import { COLORS } from '@styles';
import { getImageCDN } from '../../action';
import {
    FILE_TYPE,
    IMAGE_TYPE,
    validateImages,
    arrayUnion,
    deduplicateDescription
} from './helper';
import RadioImage from './RadioImage';

const { API_GET_IMAGE_CDN, API_GET_IMAGE_CDN_NEW } = API_CONST;
const { FILE_PATH: { STUDENT_INFO } } = ENUM;

const ButtonConfirm = ({ onPress, disabled }) => {
    return (
        <View
            style={{
                width: constants.width,
                alignItems: 'center',
                justifyContent: 'center',
                paddingVertical: 8,
                opacity: disabled ? 0.5 : 1
            }}>
            <Button
                text={translate('shoppingCart.btn_update_uppercase')}
                styleContainer={{
                    paddingHorizontal: 40,
                    borderRadius: 4,
                    backgroundColor: COLORS.btn1E88E5,
                    height: 44
                }}
                styleText={{
                    color: COLORS.txtFFFFFF,
                    fontSize: 14,
                    fontWeight: 'bold'
                }}
                onPress={onPress}
                disabled={disabled}
            />
        </View>
    );
};
const { CARD_ID, DRIVER, EXAM_SCORE, STUDENT_ID, ADDITIONAL_IMAGES } = IMAGE_TYPE;

class StudentInfo extends Component {
    constructor() {
        super();

        this.TITLES = {
            [CARD_ID]: translate('shoppingCart.take_front_ID_picture'),
            [STUDENT_ID]: translate(
                'shoppingCart.take_front_student_card_picture'
            ),
            [EXAM_SCORE]: translate('shoppingCart.take_front_exam_score')
        };

        this.initStudentImages = [
            {
                Title: this.TITLES[CARD_ID],
                AttachmentID: 0,
                FileTypeID: FILE_TYPE.STUDENT,
                UrlFile: '',
                Description: CARD_ID
            },
            {
                Title: '',
                AttachmentID: 0,
                FileTypeID: FILE_TYPE.STUDENT,
                UrlFile: '',
                Description: ''
            }
        ];

        this.initStudentCouponImages = [
            {
                Title: this.TITLES[CARD_ID],
                AttachmentID: 0,
                FileTypeID: FILE_TYPE.STUDENT_COUPON,
                UrlFile: '',
                Description: CARD_ID
            },
            {
                Title: this.TITLES[EXAM_SCORE],
                AttachmentID: 0,
                FileTypeID: FILE_TYPE.STUDENT_COUPON,
                UrlFile: '',
                Description: EXAM_SCORE
            }
        ];
        this.state = {
            images: [
                {
                    title: 'Khuyến mãi ưu đãi HSSV',
                    data: this.initStudentImages,
                    fileType: FILE_TYPE.STUDENT
                },
                {
                    title: 'BACKTOSCHOOL',
                    data: this.initStudentCouponImages,
                    fileType: FILE_TYPE.STUDENT_COUPON
                }
            ],
            selectedImageType: {
                FileTypeID: 0,
                ImageType: '',
                index: null
            },
            isVisible: false,
            typeImage: STUDENT_ID,
            isOuput: false
        };
    }

    componentDidMount() {
        const { isStudentCoupon, isStudentPromotion, supImages, additionalImages, isOuputSO = false, isManager = false } = this.props
            .route?.params ?? {
            supImages: this.initImages,
            isStudentCoupon: false,
            additionalImages: [],
            isOuputSO: false,
            isManager: false
        };
        let listImage = []
        if (supImages?.some((img) => img.UrlFile !== '')) {
            const studentImages = supImages
                .filter((img) => img.FileTypeID === FILE_TYPE.STUDENT)
                .map((img) => {
                    if (img.Description === CARD_ID) {
                        return {
                            ...img,
                            Title: this.TITLES[img.Description]
                        };
                    } else {
                        return {
                            ...img,
                            Title: ''
                        };
                    }
                });
            if (studentImages?.some((img) => img.Description === DRIVER)) {
                this.setState({
                    typeImage: DRIVER
                });
            } else {
                this.setState({
                    typeImage: STUDENT_ID
                });
            }
            const studentCouponImages = supImages
                .filter((img) => img.FileTypeID === FILE_TYPE.STUDENT_COUPON)
                .map((img) => ({
                    ...img,
                    Title: this.TITLES[img.Description]
                }));
            const fullStudentImages = arrayUnion(
                studentImages,
                this.initStudentImages,
                deduplicateDescription
            );
            const fullStudentCouponImages = arrayUnion(
                studentCouponImages,
                this.initStudentCouponImages,
                deduplicateDescription
            );
            const refImages = {
                [FILE_TYPE.STUDENT]: isStudentPromotion
                    ? fullStudentImages?.length > 2
                        ? fullStudentImages.filter((img) => img.UrlFile !== '')
                        : fullStudentImages
                    : [],
                [FILE_TYPE.STUDENT_COUPON]: isStudentCoupon
                    ? fullStudentCouponImages
                    : []
            };
            const { images } = this.state;
            const newImages = images.map((img) => ({
                ...img,
                data: refImages[img.fileType]
            }));
            listImage = newImages
        } else {
            const { images } = this.state;
            const refImages = {
                [FILE_TYPE.STUDENT]: isStudentPromotion
                    ? this.initStudentImages
                    : [],
                [FILE_TYPE.STUDENT_COUPON]: isStudentCoupon
                    ? this.initStudentCouponImages
                    : []
            };
            const newImages = images.map((img) => ({
                ...img,
                data: refImages[img.fileType]
            }));
            listImage = newImages
        }
        if (isManager) {
            if (additionalImages?.some((img) => img?.cus_UrlFileBO)) {
                const fullAdditionalImages = additionalImages?.map((img) => {
                    let newCus_UrlFileBO = img.cus_UrlFileBO
                    if (img?.cus_UrlFileBO == null) {
                        newCus_UrlFileBO = {
                            FileTypeID: FILE_TYPE.ADDITIONAL_IMAGES,
                            Title: img.ProductName,
                            AttachmentID: 0,
                            Description: ADDITIONAL_IMAGES,
                            SaleOrderDetailID: img.SaleOrderDetailID,
                            Imei: img.IMEI
                        }
                    }
                    return (
                        {
                            ...newCus_UrlFileBO,
                            Title: img.ProductName,
                            SaleOrderDetailID: img.SaleOrderDetailID,
                            Imei: img.IMEI
                        }
                    )
                });
                const newImages = [...listImage,
                {
                    title: 'BỔ SUNG HÌNH ẢNH KÍCH HOẠT BẢO HÀNH APPLE',
                    data: fullAdditionalImages,
                    fileType: FILE_TYPE.ADDITIONAL_IMAGES
                }]
                listImage = newImages
            }
            else {
                const fullAdditionalImages = additionalImages?.map((img) => ({
                    ...img.cus_UrlFileBO,
                    FileTypeID: FILE_TYPE.ADDITIONAL_IMAGES,
                    Title: img.ProductName,
                    AttachmentID: 0,
                    Description: ADDITIONAL_IMAGES,
                    SaleOrderDetailID: img.SaleOrderDetailID,
                    Imei: img.IMEI

                }));
                const newImages = [...listImage,
                {
                    title: 'BỔ SUNG HÌNH ẢNH KÍCH HOẠT BẢO HÀNH APPLE',
                    data: fullAdditionalImages,
                    fileType: FILE_TYPE.ADDITIONAL_IMAGES
                }]
                listImage = newImages
            }
        }

        this.setState({ images: listImage, isOuput: isOuputSO });
        console.log("🚀 ~ file: index.js:140 ~ StudentInfo ~ componentDidMount ~ additionalImages:", additionalImages)


    }

    openCamera = (ImageType, FileTypeID, index) => () => {
        this.setState({
            selectedImageType: {
                FileTypeID,
                ImageType,
                index
            },
            isVisible: true
        });
    };

    closeCamera = () => {
        this.setState({ isVisible: false });
    };

    render() {
        const { isVisible, images, selectedImageType, isOuput } = this.state;
        return (
            <SafeAreaView
                style={{
                    flex: 1,
                    backgroundColor: COLORS.bgFFFFFF
                }}>
                <ScrollView
                    style={{ flex: 1 }}
                    keyboardShouldPersistTaps="always">
                    <View
                        style={{
                            width: constants.width,
                            alignItems: 'center'
                        }}>
                        <SectionList
                            sections={images}
                            keyExtractor={(item, index) =>
                                item.Description + index
                            }
                            renderItem={({ item, index }) => {
                                const {
                                    Title,
                                    UrlFile,
                                    Description: ImageType,
                                    FileTypeID,
                                    Imei
                                } = item;
                                return (
                                    <>
                                        {ImageType !== CARD_ID &&
                                            FileTypeID ===
                                            FILE_TYPE.STUDENT && (
                                                <RadioImage
                                                    typeImage={
                                                        this.state.typeImage
                                                    }
                                                    onSwitchTypeImage={(
                                                        value
                                                    ) => {
                                                        this.setState({
                                                            typeImage: value
                                                        });
                                                    }}
                                                />
                                            )}

                                        <ImageAdjust
                                            Imei={Imei}
                                            key={ImageType}
                                            title={Title}
                                            fileTypeID={FileTypeID}
                                            onCamera={this.openCamera(
                                                ImageType,
                                                FileTypeID,
                                                index

                                            )}
                                            onDelete={this.deleteImage(
                                                ImageType,
                                                FileTypeID,
                                                index
                                            )}
                                            uri={UrlFile}
                                            isOuput={isOuput}
                                        />
                                    </>
                                );
                            }}
                            renderSectionHeader={({
                                section: { data, title, fileType }
                            }) => {
                                const dataLength = images?.filter(
                                    (image) => image?.data?.length > 0
                                ).length;
                                const shouldRenderHeader = dataLength >= 1;
                                return (
                                    shouldRenderHeader && data?.length > 0 && (
                                        <HeaderSection title={title} />
                                    )
                                );
                            }}
                        />
                        <ButtonConfirm
                            onPress={this.validateInfo}
                            disabled={false}
                        />
                    </View>
                </ScrollView>
                <CaptureCamera
                    isVisibleCamera={isVisible}
                    takePicture={(photo) => {
                        this.takePicture(photo, selectedImageType);
                    }}
                    closeCamera={this.closeCamera}
                    selectPicture={this.onPickerPhoto(selectedImageType)}
                />
            </SafeAreaView>
        );
    }

    deleteImage = (ImageType, FileTypeID, index) => () => {
        const { images } = this.state;
        const imgIndex = images.findIndex((img) => img.fileType === FileTypeID);
        if (imgIndex !== -1) {
            if (FileTypeID === FILE_TYPE.ADDITIONAL_IMAGES) {
                const newImage = [...images];
                newImage[imgIndex].data[index].UrlFile = '';

                this.setState({ images: newImage });
            }
            else {
                const idx = images[imgIndex].data.findIndex(
                    (img) => img.Description === ImageType
                );
                if (idx !== -1) {
                    const newImage = [...images];
                    newImage[imgIndex].data[idx].UrlFile = '';

                    this.setState({ images: newImage });
                }
            }
        }
    };

    takePicture = (photo, selectedImageType) => {
        this.setState({
            isVisible: false,
        });
        showBlockUI()
        const { uriImages, images } = this.state;
        if (helper.hasProperty(photo, 'uri')) {
            helper
                .resizeImage(photo)
                .then(({ uri, name }) => {
                    const body = helper.createFormData({ uri: uri, type: 'image/jpg', name: name, path: STUDENT_INFO });
                    getImageCDN(body)
                        .then((response) => {
                            const remoteURI =
                                API_CONST.API_GET_IMAGE_CDN_NEW +
                                response[0];
                            const { FileTypeID, ImageType, index } = selectedImageType;
                            const imgIndex = images.findIndex(
                                (img) => img.fileType === FileTypeID
                            );
                            if (imgIndex !== -1) {
                                if (FileTypeID === FILE_TYPE.ADDITIONAL_IMAGES) {
                                    const newImages = this.updateImages(
                                        remoteURI,
                                        imgIndex,
                                        index
                                    );
                                    this.setState({
                                        images: newImages,
                                        uriImages
                                    });
                                }
                                else {
                                    const dataIndex = images[
                                        imgIndex
                                    ].data.findIndex(
                                        (img) => img.Description === ImageType
                                    );
                                    if (dataIndex !== -1) {
                                        const newImages = this.updateImages(
                                            remoteURI,
                                            imgIndex,
                                            dataIndex
                                        );
                                        this.setState({
                                            images: newImages,
                                            uriImages
                                        });
                                    }
                                }
                            }
                            hideBlockUI();
                        })
                        .catch((error) => {
                            hideBlockUI();
                            console.log('uploadPicture', error);
                        });

                })
                .catch((error) => {
                    hideBlockUI();
                    console.log('resizeImage', error);
                });
        }
    };

    onPickerPhoto = (selectedImageType) => () => {
        const { uriImages, images } = this.state;
        launchImageLibrary(
            {
                mediaType: 'photo',
                noData: true
            },
            (response) => {
                this.setState({
                    isVisible: false,
                });
                showBlockUI()
                if (helper.hasProperty(response, 'uri')) {
                    helper
                        .resizeImage(response)
                        .then(({ uri, name }) => {
                            const body = helper.createFormData({ uri: uri, type: 'image/jpg', name: name, path: STUDENT_INFO });
                            getImageCDN(body)
                                .then((response) => {
                                    const remoteURI =
                                        API_CONST.API_GET_IMAGE_CDN_NEW +
                                        response[0];
                                    const { FileTypeID, ImageType, index } = selectedImageType;
                                    const imgIndex = images.findIndex(
                                        (img) => img.fileType === FileTypeID
                                    );
                                    if (imgIndex !== -1) {
                                        if (FileTypeID === FILE_TYPE.ADDITIONAL_IMAGES) {
                                            const newImages = this.updateImages(
                                                remoteURI,
                                                imgIndex,
                                                index
                                            );
                                            this.setState({
                                                images: newImages,
                                                uriImages
                                            });
                                        }
                                        else {
                                            const dataIndex = images[
                                                imgIndex
                                            ].data.findIndex(
                                                (img) => img.Description === ImageType
                                            );
                                            if (dataIndex !== -1) {
                                                const newImages = this.updateImages(
                                                    remoteURI,
                                                    imgIndex,
                                                    dataIndex
                                                );
                                                this.setState({
                                                    images: newImages,
                                                    uriImages
                                                });
                                            }
                                        }
                                    }
                                    hideBlockUI();
                                })
                                .catch((error) => {
                                    hideBlockUI();
                                    console.log('uploadPicture', error);
                                });


                        })
                        .catch((error) => {
                            hideBlockUI()
                            console.log('resizeImage', error);
                        });
                }
                else {
                    hideBlockUI()
                }
            }
        );
    };

    updateImages = (uri, imgIndex, dataIndex) => {
        const newImages = [...this.state.images];
        const selectedImage = newImages[imgIndex];
        selectedImage.data[dataIndex].UrlFile = uri;
        return newImages;
    };

    validateInfo = () => {
        const { images } = this.state;
        const { isStudentCoupon, isStudentPromotion, dataShoppingCart } = this.props.route
            ?.params ?? {
            isStudentCoupon: false,
            isStudentPromotion: false
        };
        const isValidateImage = validateImages({
            images,
            isStudentCoupon: isStudentCoupon && dataShoppingCart?.cus_IsRequiredCandidateInput,
            isStudentPromotion
        });
        if (!isValidateImage) {
            Alert.alert(
                '',
                translate('shoppingCart.please_take_full_info_picture')
            );
        } else {
            // this.getFromData(images);
            this.updateImageSupplement(images);

        }
    };

    getFromData = (_images) => {
        const formData = new FormData();
        let mapIndex = {};
        let indexOfImage = 0;
        _images.forEach((img) => {
            img.data.forEach((dataImage, index) => {
                if (
                    helper.IsNonEmptyString(dataImage.UrlFile) &&
                    !dataImage.UrlFile.startsWith('http') &&
                    !dataImage.UrlFile.startsWith('data:')
                ) {

                    const uri = dataImage.UrlFile
                    const type = 'image/jpg'
                    const name = `${dateHelper.getTimestamp()}_${index}.jpg`
                    const path = "/StudentInfo"

                    formData.append('file', { uri, type, name });
                    formData.append('path', path);
                    formData.append('isGenDate', "false");
                    formData.append('isGenName', "false");

                    mapIndex = {
                        ...mapIndex,
                        [indexOfImage]: {
                            dataIndex: index,
                            fileType: img.fileType
                        }
                    };
                    indexOfImage += 1;
                }
            });
        });
        this.uploadPicture(formData, mapIndex);
    };

    uploadPicture = (fromData, mapIndex) => {
        const { images } = this.state;
        if (!helper.IsEmptyObject(mapIndex)) {
            showBlockUI();
            getImageCDN(fromData)
                .then((data) => {
                    hideBlockUI();
                    data.forEach((name, index) => {
                        const uri = `${API_GET_IMAGE_CDN_NEW}${name}`;
                        const { fileType, dataIndex } = mapIndex[index];
                        const image = images.find(
                            (img) => img.fileType === fileType
                        );
                        image.data[dataIndex].UrlFile = uri;
                    });
                    this.updateImageSupplement(images);
                })
                .catch((error) => {
                    Alert.alert(
                        translate('common.notification_uppercase'),
                        error.msgError ?? error,
                        [
                            {
                                text: translate('common.btn_skip'),
                                onPress: hideBlockUI,
                                style: 'cancel'
                            },
                            {
                                text: translate('common.btn_notify_try_again'),
                                onPress: () =>
                                    this.uploadPicture(fromData, mapIndex),
                                style: 'default'
                            }
                        ],
                        { cancelable: false }
                    );
                });
        } else {
            this.updateImageSupplement(images);
        }
    };

    updateImageSupplement = (cdnImages) => {
        const { typeImage } = this.state;
        const { dataShoppingCart, setDataShoppingCart, supImages, noGoBack } =
            this.props.route?.params ?? {
                dataShoppingCart: { SaleOrderCusPromotion: { UrlFiles: [] } },
                setDataShoppingCart: () => {
                    console.log('setDataShoppingCart');
                }
            };
        const newUrlFiles = cdnImages
            .map((image) => [...image.data])
            .flatMap((urlFile) => urlFile);
        const studentUrlFiles = newUrlFiles.filter(
            (img) => img.FileTypeID === FILE_TYPE.STUDENT
        );
        if (studentUrlFiles) {
            const studentIndex = studentUrlFiles.findIndex(
                (img) => img.Description !== CARD_ID
            );
            if (studentIndex !== -1) {
                studentUrlFiles[studentIndex].Description = typeImage;
            }
        }
        const studentCouponUrlFiles = newUrlFiles.filter(
            (img) => img.FileTypeID === FILE_TYPE.STUDENT_COUPON
        );
        const additionalFiles = newUrlFiles.filter(
            (img) => img.FileTypeID === FILE_TYPE.ADDITIONAL_IMAGES
        );
        if (!noGoBack) {
            // Hình ảnh toa thuốc hoặc những hình khác lưu ở cus_UrlFilesShoppingCart
            const restUrlFiles = supImages.filter(
                (img) =>
                    img.FileTypeID !== FILE_TYPE.STUDENT_COUPON &&
                    img.FileTypeID !== FILE_TYPE.STUDENT
            );
            if (dataShoppingCart.SaleOrderCusPromotion?.UrlFiles) {
                dataShoppingCart.SaleOrderCusPromotion.UrlFiles =
                    studentUrlFiles;
            }
            if (helper.isArray(dataShoppingCart.cus_UrlFilesShoppingCart)) {
                dataShoppingCart.cus_UrlFilesShoppingCart = [
                    ...studentCouponUrlFiles,
                    ...restUrlFiles
                ];
            }
            setDataShoppingCart(dataShoppingCart);
            this.props.navigation.goBack();
        } else {
            const newUrlFileStudent = studentUrlFiles.concat(
                studentCouponUrlFiles, additionalFiles
            );
            setDataShoppingCart(newUrlFileStudent, dataShoppingCart);
        }
    };
}

const mapStateToProps = function (state) {
    return {
        userInfo: state.userReducer
    };
};

export default connect(mapStateToProps)(StudentInfo);

const ImageAdjust = ({ onCamera, onDelete, uri, title, fileTypeID, Imei, isOuput }) => {
    const isImage = helper.IsNonEmptyString(uri);
    const imageWidth = constants.width / 2;
    return (
        <View
            style={{
                width: constants.width,
                justifyContent: 'center',
                alignItems: 'center',
                paddingVertical: 10
            }}>
            {helper.IsNonEmptyString(title) && (
                fileTypeID === FILE_TYPE.ADDITIONAL_IMAGES ?
                    <View>
                        <MyText
                            text="Sản phẩm:  "
                            addSize={-1.5}
                            style={{
                                color: COLORS.txt6A6A6A,
                                fontWeight: 'bold',
                                fontStyle: 'italic',
                                width: constants.width - 20,
                                marginBottom: 6

                            }}
                        >
                            <MyText
                                text={title}
                                addSize={-1.5}
                                style={{
                                    color: COLORS.txt333333,
                                    fontWeight: 'bold',
                                    fontStyle: 'italic',
                                }} />

                        </MyText>
                        {!!Imei && <MyText
                            text="IMEI:  "
                            addSize={-1.5}
                            style={{
                                color: COLORS.txt6A6A6A,
                                fontWeight: 'bold',
                                fontStyle: 'italic',
                                width: constants.width - 20,
                                marginBottom: 6

                            }}
                        >
                            <MyText
                                text={Imei}
                                addSize={-1.5}
                                style={{
                                    color: COLORS.txt333333,
                                    fontWeight: 'bold',
                                    fontStyle: 'italic',
                                }} />

                        </MyText>}
                    </View>
                    : <MyText
                        text={title}
                        addSize={-1.5}
                        style={{
                            color: COLORS.txt333333,
                            fontWeight: 'bold',
                            fontStyle: 'italic',
                            width: constants.width - 20,
                            marginBottom: 6
                        }}>
                        <MyText
                            text="*"
                            addSize={-1.5}
                            style={{
                                color: COLORS.txtFF0000
                            }}
                        />
                    </MyText>
            )}

            {isImage ? (
                <View
                    style={{
                        width: imageWidth,
                        height: imageWidth,
                        justifyContent: 'flex-start',
                        alignItems: 'center',
                        marginHorizontal: 1,
                        backgroundColor: COLORS.bgF5F5F5
                    }}>
                    <View
                        style={{
                            width: imageWidth,
                            height: imageWidth,
                            justifyContent: 'center',
                            alignItems: 'center'
                        }}>
                        <ImageCDN
                            style={{
                                width: imageWidth - 10,
                                height: imageWidth - 10
                            }}
                            uri={uri}
                            resizeMode="contain"
                        />
                    </View>
                    {
                        fileTypeID === FILE_TYPE.ADDITIONAL_IMAGES
                            ?
                            <TouchableOpacity
                                style={{
                                    padding: 5,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    position: 'absolute',
                                    top: 0,
                                    right: 0,
                                    backgroundColor: COLORS.btnFFFFFF,
                                    borderWidth: 1,
                                    borderColor: COLORS.bdF5F5F5
                                }}
                                onPress={onDelete}>
                                <MyText
                                    text={translate('shoppingCart.delete')}
                                    style={{
                                        color: COLORS.txtD0021B
                                    }}
                                />
                            </TouchableOpacity>
                            :
                            (
                                !isOuput &&
                                <TouchableOpacity
                                    style={{
                                        padding: 5,
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        position: 'absolute',
                                        top: 0,
                                        right: 0,
                                        backgroundColor: COLORS.btnFFFFFF,
                                        borderWidth: 1,
                                        borderColor: COLORS.bdF5F5F5
                                    }}
                                    onPress={onDelete}>
                                    <MyText
                                        text={translate('shoppingCart.delete')}
                                        style={{
                                            color: COLORS.txtD0021B
                                        }}
                                    />
                                </TouchableOpacity>
                            )
                    }
                </View>
            ) : (
                <TouchableOpacity
                    style={{
                        width: imageWidth,
                        height: imageWidth,
                        justifyContent: 'center',
                        alignItems: 'center',
                        backgroundColor: COLORS.btnF5F5F5,
                        marginHorizontal: 1
                    }}
                    onPress={onCamera}>
                    <Icon
                        iconSet="Ionicons"
                        name="ios-camera"
                        color={COLORS.icFFB23F}
                        size={60}
                    />
                </TouchableOpacity>
            )}
        </View>
    );
};

const HeaderSection = ({ title }) => (
    <View
        style={{
            shadowColor: COLORS.bg000000,
            shadowOffset: {
                width: 0,
                height: 2
            },
            shadowOpacity: 0.25,
            shadowRadius: 3.84,
            elevation: 5,
            marginBottom: 15
        }}>
        <View
            style={{
                padding: 10,
                width: constants.width,
                backgroundColor: COLORS.bgCBE5B2
            }}>
            <Text style={{ fontSize: 16, width: constants.width - 20 }}>{title}</Text>
        </View>
    </View>
);
