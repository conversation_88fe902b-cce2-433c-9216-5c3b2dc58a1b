import React from 'react';
import { StyleSheet, View } from 'react-native';
import { MyText, Picker } from '@components';
import { constants } from '@constants';
import { helper } from '@common';
import { translate } from '@translate';
import { COLORS } from '@styles';

const BNPLInformation = ({
    info,
    data,
    updateData
}) => {
    const {
        SaleProgramID,
        SaleProgramName,
        TermLoanList,
    } = info;
    const {
        TermLoan = 0,
    } = data;
    return (
        <View
            style={{
                width: constants.width,
                backgroundColor: COLORS.bgFFFFFF
            }}>
            <View
                style={{
                    width: constants.width,
                    paddingHorizontal: 10,
                    paddingVertical: 6,
                    backgroundColor: COLORS.bgFDF9E5
                }}>
                <MyText
                    text={`${SaleProgramID} - ${SaleProgramName}`}
                    style={{
                        marginBottom: 2,
                        fontWeight: 'bold',
                        color: COLORS.txt288AD6
                    }}
                />
                {helper.IsNonEmptyArray(TermLoanList) && (
                    <Picker
                        label="TermLoanName"
                        value="TermLoanNumber"
                        data={TermLoanList}
                        valueSelected={TermLoan}
                        onChange={(item) => {
                            data.TermLoan = item.TermLoanNumber;
                            updateData(data);
                        }}
                        numColumns={4}
                        defaultLabel={translate(
                            'shoppingCart.picker_loan_term'
                        )}
                        title={translate(
                            'shoppingCart.picker_loan_term'
                        )}
                        isRequired
                    />
                )}
            </View>
        </View>
    )
}

export default BNPLInformation

const styles = StyleSheet.create({})