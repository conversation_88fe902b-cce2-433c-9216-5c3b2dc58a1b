import React, { Component } from 'react';
import {
    SafeAreaView,
    StyleSheet,
    Alert,
    Keyboard
} from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import {
    showB<PERSON><PERSON>,
    hideBlockUI,
    BarcodeCamera,
} from "@components";
import { helper, storageHelper } from "@common";
import Guide from "./component/Guide";
import Customer from "./component/Customer";
import ButtonCancel from "./component/ButtonCancel";
import OtpCode from "./component/OtpCode";
import IdentityCode from "./component/IdentityCode";
import * as actionShoppingCartCreator from "../../action";
import * as actionSaleOrderCreator from "../../../SaleOrderCart/action";
import * as actionPouchCreator from "../../../PouchRedux/action";
import * as specialSaleProgramActionCreator from "../../../SpecialSaleProgram/action";
import * as staffPromotionActionCreator from "../../../StaffPromotion/action";
import { COLORS } from "@styles";
import { translate } from '@translate';
import { ENUM } from '@constants';

class Loyalty extends Component {

    constructor() {
        super();
        this.state = {
            info: {
                customerPhone: '',
                customerName: '',
                totalPointLoyalty: ''
            },
            expireTime: 0,
            otpCode: "",
            identifyCode: "",
            isVisible: false
        }
        this.intervalId = null;
    }

    componentDidMount() {
        const { dataCartLoyalty } = this.props;
        const loyaltyInfo = getLoyaltyInfo(dataCartLoyalty);
        this.setState({ info: loyaltyInfo });
    }

    componentWillUnmount() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }
    }

    onSkip = () => {
        const { dataCartLoyalty } = this.props;
        this.addToSaleOrderCart(dataCartLoyalty);
    }

    goBack = () => {
        this.props.navigation.goBack();
    }

    closeBarcode = () => {
        this.setState({ isVisible: false });
    }

    openBarcode = () => {
        this.setState({
            isVisible: true,
            identifyCode: "",
        });
    }

    render() {
        const {
            info,
            expireTime,
            otpCode,
            identifyCode,
            isVisible
        } = this.state;
        const { dataCartLoyalty } = this.props;
        const { isCredentialExist } = dataCartLoyalty;
        return (
            <KeyboardAwareScrollView
                style={{
                    flex: 1,
                    backgroundColor: COLORS.bgFFFFFF,
                }}
                enableResetScrollToCoords={false}
                keyboardShouldPersistTaps="always"
                bounces={false}
                overScrollMode="always"
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                extraScrollHeight={60}
            >
                <SafeAreaView style={{
                    flex: 1,
                }}>
                    <Guide />
                    <Customer
                        info={info}
                    />
                    {
                        isCredentialExist
                            ? <IdentityCode
                                code={identifyCode}
                                onChange={(text) => {
                                    const regExpId4 = new RegExp(/^\d{0,4}$/);
                                    const regExpId6 = new RegExp(/^\d{0,6}$/);
                                    const isValidate = regExpId4.test(text) || regExpId6.test(text);
                                    if (isValidate) {
                                        this.setState({ identifyCode: text });
                                    }
                                }}
                                onVerify={this.onCheckIdentitfyCode}
                                onScan={this.openBarcode}
                            />
                            : <OtpCode
                                onCreate={this.onCreateOTP}
                                expireTime={expireTime}
                                code={otpCode}
                                onChange={(text) => {
                                    const regExpOTP = new RegExp(/^\d{0,4}$/);
                                    const isValidate = regExpOTP.test(text);
                                    if (isValidate) {
                                        this.setState({ otpCode: text });
                                    }
                                }}
                                onVerify={this.onCheckOTP}
                            />
                    }
                    <ButtonCancel
                        onChangePhone={this.goBack}
                        onSkipVerify={this.onSkip}
                    />
                    {
                        isVisible &&
                        <BarcodeCamera
                            isVisible={isVisible}
                            closeCamera={this.closeBarcode}
                            resultScanBarcode={(barcode) => {
                                this.verifyIdentity("", barcode, info.customerPhone);
                            }}
                        />
                    }
                </SafeAreaView>
            </KeyboardAwareScrollView>
        );
    }

    countDown = () => {
        const { expireTime } = this.state;
        const second = expireTime - 1;
        if (second > 0) {
            this.setState({ expireTime: second });
        }
        else {
            this.resetCountDown();
        }
    }

    resetCountDown = () => {
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }
        this.setState({
            expireTime: 0,
            otpCode: "",
        });
    }

    setCountDown = () => {
        this.setState({
            expireTime: 60
        })
        this.intervalId = setInterval(
            this.countDown,
            1000
        );
    }

    onCreateOTP = (type) => {
        const { info: {
            customerPhone
        } } = this.state;
        const { userInfo: { brandID } } = this.props;
        showBlockUI();
        actionShoppingCartCreator.createOTP({
            "type": type,
            "phoneNumber": customerPhone,
            "typeContent": "VIP",
            "lenOtp": 4,
            "brandID": brandID
        }).then(success => {
            this.setCountDown();
            Alert.alert(translate('common.notification_uppercase'), `${translate('shoppingCart.otp_sent')} ${customerPhone}`,
                [
                    {
                        text: "OK",
                        style: "cancel",
                        onPress: () => {
                            hideBlockUI();
                        }
                    }
                ]
            )
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: () => this.onCreateOTP(type)
                    }
                ]
            )
        });
    }

    onCheckOTP = () => {
        Keyboard.dismiss();
        const {
            otpCode,
            info: {
                customerPhone
            }
        } = this.state;
        const isValidate = this.validateOTP(otpCode);
        if (isValidate) {
            this.verifyOTP(otpCode, customerPhone);
        }
    }

    verifyOTP = (otpCode, customerPhone) => {
        const { dataCartLoyalty } = this.props;
        showBlockUI();
        actionShoppingCartCreator.verifyOTP(otpCode, customerPhone)
            .then(data => {
                const dataSaleOrder = {
                    ...dataCartLoyalty,
                    "requestIDLoyalty": data.requestId,
                    "customerIDLoyalty": data.customerId,
                }
                this.addToSaleOrderCart(dataSaleOrder);
            }).catch(msgError => {
                Alert.alert(translate('common.notification_uppercase'), msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: "cancel",
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: "default",
                            onPress: () => this.verifyOTP(otpCode, customerPhone)
                        }
                    ]
                )
            })
    }

    onCheckIdentitfyCode = () => {
        Keyboard.dismiss();
        const {
            identifyCode,
            info: {
                customerPhone
            }
        } = this.state;
        const isValidateCode = this.validateIdentity(identifyCode);
        if (isValidateCode) {
            this.verifyIdentity(identifyCode, "", customerPhone)
        }
    }

    verifyIdentity = (identifyCode, identifyData, customerPhone) => {
        const { dataCartLoyalty, userInfo: { brandID } } = this.props;
        this.setState({ isVisible: false });
        showBlockUI();
        actionShoppingCartCreator.verifyIdentify(identifyCode, identifyData, customerPhone, brandID)
            .then(data => {
                const dataSaleOrder = {
                    ...dataCartLoyalty,
                    "requestIDLoyalty": data.requestId,
                    "customerIDLoyalty": data.customerId,
                }
                this.addToSaleOrderCart(dataSaleOrder);
            }).catch(msgError => {
                Alert.alert(translate('common.notification_uppercase'), msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: "cancel",
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: "default",
                            onPress: () => this.verifyIdentity(identifyCode, identifyData, customerPhone)
                        }
                    ]
                )
            })

    }

    validateOTP = (code) => {
        const regExpOTP = new RegExp(/^\d{4}$/);
        const isValidate = regExpOTP.test(code);
        if (!helper.IsNonEmptyString(code)) {
            Alert.alert("", translate('shoppingCart.validate_empty_otp'));
            return false;
        }
        if (!isValidate) {
            Alert.alert("", translate('shoppingCart.validate_otp_4'));
            return false;
        }
        return true;
    }

    validateIdentity = (identifyCode) => {
        const regExpId4 = new RegExp(/^\d{4}$/);
        const regExpId6 = new RegExp(/^\d{6}$/);
        const isValidate = regExpId4.test(identifyCode) || regExpId6.test(identifyCode);
        if (!helper.IsNonEmptyString(identifyCode)) {
            Alert.alert("", translate('shoppingCart.please_enter_id_code'));
            return false;
        }
        if (!isValidate) {
            Alert.alert("", "Vui lòng nhập mã định danh đúng 4 hoặc 6 chữ số");
            return false;
        }
        return true;
    }

    addToSaleOrderCart = async (data) => {
        showBlockUI();
        const { userInfo: { storeID }, staffPromotionAction, saleScenarioTypeID, specialSaleProgramAction } = this.props;
        const isHasRight = await helper.checkStorePermission(storeID);
        if (isHasRight) {
            this.props.actionSaleOrder.addToSaleOrderCart(data).then(orderInfo => {
                const { customerInfo, customerIDLoyalty } = data;
                const { object } = orderInfo;
                hideBlockUI();
                if (this.props?.isRecording) {
                    const saleOrderIDs = object.SaleOrders?.map((_item) => (_item?.SaleOrderID))?.join(",") ?? "";
                    this.props?.stopRecording(saleOrderIDs)
                }
                this.props.actionShoppingCart.deleteShoppingCart();
                this.props.actionPouch.setDataCartApply();
                staffPromotionAction.reset_staff_info();
                saleScenarioTypeID !== ENUM.SALE_SCENARIO_TYPE.SALE &&
                    specialSaleProgramAction.setScenarioSaleType(
                        ENUM.SALE_SCENARIO_TYPE.SALE
                    );
                this.props.navigation.navigate("SaleOrderCart");
                storageHelper.updateTopCustomerInfo(customerInfo);
                if (helper.IsNonEmptyString(customerIDLoyalty)) {
                    storageHelper.updateVerifyLoyalty(customerInfo.customerPhone, object.CartID);
                }

            }).catch(msgError => {
                Alert.alert(translate('common.notification_uppercase'), msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: "cancel",
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: "default",
                            onPress: () => this.addToSaleOrderCart(data)
                        }
                    ]
                )
            });
        }
        else {
            Alert.alert(translate('common.notification_uppercase'), `Bạn không có quyền trên siêu thị ${storeID}`,
                [{
                    text: "OK",
                    onPress: hideBlockUI
                }]
            )
        }
    }
}

const styles = StyleSheet.create({
});

const mapStateToProps = function (state) {
    return {
        dataCartLoyalty: state.shoppingCartReducer.dataCartLoyalty,
        userInfo: state.userReducer,
        saleScenarioTypeID: state.specialSaleProgramReducer.saleScenarioTypeID,
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionShoppingCart: bindActionCreators(actionShoppingCartCreator, dispatch),
        actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),
        actionPouch: bindActionCreators(actionPouchCreator, dispatch),
        specialSaleProgramAction: bindActionCreators(specialSaleProgramActionCreator, dispatch),
        staffPromotionAction: bindActionCreators(staffPromotionActionCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(Loyalty);

const getLoyaltyInfo = (dataCartLoyalty) => {
    let info = {
        customerPhone: '',
        customerName: '',
        totalPointLoyalty: ''
    };
    if (!helper.IsEmptyObject(dataCartLoyalty)) {
        const {
            customerInfo,
            cartRequest
        } = dataCartLoyalty;
        info = {
            customerPhone: customerInfo.customerPhone,
            customerName: customerInfo.customerName,
            totalPointLoyalty: cartRequest.TotalPointLoyalty
        };
    }
    return info;
}