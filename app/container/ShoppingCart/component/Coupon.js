import React, { useEffect, useRef, useState } from 'react';
import {
    View,
    TouchableOpacity,
    TextInput,
    StyleSheet,
    Keyboard,
    Animated
} from 'react-native';
import { Icon, Button, MyText } from '@components';
import { constants } from '@constants';
import { helper } from '@common';
import { translate } from '@translate';
import { COLORS } from '@styles';

const Coupon = ({
    discountCode,
    onchangeDiscountCode,
    couponDiscount,
    applyDiscountCode,
    cancelDiscountCode,
    openBarcode,
    isCartCoupon = false,
    couponExpired
}) => {
    const isApplyCoupon = couponDiscount > 0;
    const [isShowDetail, setIsShowDetail] = useState(false);
    const [isFocus, setIsFocus] = useState(false);
    const isNonEmptyKeyword = helper.IsNonEmptyString(discountCode);
    const isClose = isFocus && isNonEmptyKeyword;

    const blinkAnim = useRef(new Animated.Value(1)).current;

    useEffect(() => {
        Animated.loop(
            Animated.sequence([
                Animated.timing(blinkAnim, {
                    toValue: 0.3,
                    duration: 500,
                    useNativeDriver: true
                }),
                Animated.timing(blinkAnim, {
                    toValue: 1,
                    duration: 500,
                    useNativeDriver: true
                })
            ])
        ).start();
    }, []);

    return (
        <View
            style={{
                width: constants.width
            }}>
            <TouchableOpacity
                style={{
                    width: constants.width,
                    flexDirection: 'row',
                    alignItems: 'center',
                    backgroundColor: isCartCoupon
                        ? COLORS.bgFFC62D
                        : COLORS.btnE4E4E4,
                    height: 40,
                    paddingHorizontal: 10,
                    borderTopWidth: StyleSheet.hairlineWidth,
                    borderTopColor: COLORS.bdFFFFFF
                }}
                activeOpacity={0.8}
                onPress={() => {
                    setIsShowDetail(!isShowDetail);
                }}>
                <MyText
                    text={translate('shoppingCart.use_Coupon')}
                    style={{
                        color: COLORS.txtFC3158,
                        fontWeight: 'bold'
                    }}
                />

                <Icon
                    iconSet="Ionicons"
                    name={isShowDetail ? 'chevron-up' : 'chevron-down'}
                    color={COLORS.icFC3158}
                    size={20}
                />
                {couponExpired > 0 && (
                    <Animated.View
                        style={{
                            alignItems: 'center',
                            // position: 'absolute',
                            top: -1,
                            right: 5,
                            opacity: blinkAnim,
                            paddingLeft: 10,
                            justifyContent: 'center'
                        }}>
                        <MyText
                            addSize={2}
                            style={{
                                paddingRight: 3,
                                fontWeight: 'bold',
                                // color: COLORS.bg147EFB
                            }}
                            text={`Ưu đãi trên Quà Tặng VIP (${couponExpired})`}
                        />
                    </Animated.View>
                )}
            </TouchableOpacity>
            {isShowDetail && (
                <View
                    style={{
                        width: constants.width,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        paddingHorizontal: 10,
                        paddingVertical: 6
                    }}>
                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center'
                        }}>
                        <View
                            style={{
                                flexDirection: 'row',
                                width: 170,
                                marginBottom: 8,
                                borderWidth: 1,
                                borderColor: COLORS.bdE4E4E4,
                                height: 40,
                                backgroundColor: COLORS.bgFFFFFF,
                                borderRadius: 5,
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                alignSelf: 'center',
                                marginTop: 10,
                                marginRight: 10
                            }}>
                            <TextInput
                                value={discountCode}
                                onChangeText={onchangeDiscountCode}
                                editable={!isApplyCoupon}
                                placeholder={translate(
                                    'shoppingCart.placeholder_coupon'
                                )}
                                placeholderTextColor={COLORS.txt808080}
                                onSubmitEditing={Keyboard.dismiss}
                                style={{
                                    height: 40,
                                    width: 122,
                                    paddingLeft: 20
                                }}
                                maxLength={150}
                                onFocus={() => {
                                    setIsFocus(true);
                                }}
                                onBlur={() => {
                                    setIsFocus(false);
                                }}
                            />
                            {isClose ? (
                                <TouchableOpacity
                                    style={{
                                        height: 40,
                                        width: 48,
                                        justifyContent: 'center',
                                        alignItems: 'flex-end',
                                        paddingRight: 20
                                    }}
                                    onPress={() => {
                                        onchangeDiscountCode('');
                                    }}>
                                    <Icon
                                        iconSet="Ionicons"
                                        name="close"
                                        color={COLORS.ic848A8C}
                                        size={22}
                                    />
                                </TouchableOpacity>
                            ) : isApplyCoupon ? null : (
                                <TouchableOpacity
                                    style={{
                                        height: 40,
                                        width: 48,
                                        justifyContent: 'center',
                                        alignItems: 'flex-end',
                                        paddingRight: 20
                                    }}
                                    onPress={openBarcode}>
                                    <Icon
                                        iconSet="MaterialCommunityIcons"
                                        name="barcode-scan"
                                        color={COLORS.icFFD400}
                                        size={22}
                                    />
                                </TouchableOpacity>
                            )}
                        </View>
                        {isApplyCoupon ? (
                            <Button
                                text={translate('common.btn_cancel')}
                                onPress={cancelDiscountCode}
                                styleContainer={{
                                    paddingHorizontal: 10,
                                    backgroundColor: COLORS.btn288AD6,
                                    borderRadius: 4,
                                    width: 75,
                                    height: 40
                                }}
                                styleText={{
                                    fontSize: 14,
                                    color: COLORS.txtFFFF00
                                }}
                            />
                        ) : (
                            <Button
                                text={translate('shoppingCart.btn_apply')}
                                onPress={applyDiscountCode}
                                styleContainer={{
                                    paddingHorizontal: 10,
                                    backgroundColor: COLORS.btn288AD6,
                                    borderRadius: 4,
                                    width: 75,
                                    height: 40
                                }}
                                styleText={{
                                    fontSize: 14,
                                    color: COLORS.txtFFFFFF
                                }}
                            />
                        )}
                    </View>
                    {isApplyCoupon && (
                        <MyText
                            text={`-${helper.convertNum(couponDiscount)}`}
                            style={{
                                color: COLORS.txt288AD6,
                                flex: 1,
                                marginLeft: 10
                            }}
                        />
                    )}
                </View>
            )}
        </View>
    );
};

export default Coupon;
