import React from 'react';
import { View } from 'react-native';
import { MyText, NumberInput, Picker, TitleInput } from '@components';
import { constants } from '@constants';
import { helper } from '@common';
import { translate } from '@translate';
import { COLORS } from '@styles';

const ContractInfo = ({
    info,
    data,
    getData,
    contractID,
    onChange,
    onClear,
    totalAmount,
    updateData
}) => {
    const {
        SaleProgramID,
        SaleProgramName,
        RecordsProcessingFee,
        PartnerInstallmentID,
        FormatContractID,
        IsCardPartner,
        TermLoanList,
        MinPrepaid,
        MaxPrepaid
    } = info;
    const {
        ContractID = '',
        PGProcessUserID = '',
        PGProcessUserName = '',
        packageRates = '',
        TotalPrePaid = 0,
        TermLoan = 0,
        PaymentAmountMonthly = 0,
        isPartnerConnectAPI = false
    } = data;
    const processUserID = PGProcessUserID || '';
    const processUserName = PGProcessUserName || '';
    const valueProcess = `${processUserID} - ${processUserName}`;
    const valueTermLoan = TermLoan > 0 ? `${TermLoan} tháng` : '';
    const percent = (parseFloat(TotalPrePaid) / totalAmount) * 100;
    const totalPrePaid =
        IsCardPartner && !helper.IsNonEmptyString(contractID)
            ? totalAmount
            : TotalPrePaid;

    const onValidate = () => {
        const isNonEmpty = helper.IsNonEmptyString(contractID);
        const isChange = contractID != ContractID;
        const isValidate = isNonEmpty && isChange;
        if (isValidate) {
            getData({
                contractID,
                partnerInstallmentID: PartnerInstallmentID,
                formatContractID: FormatContractID,
                saleProgramID: SaleProgramID
            });
        }
        if (!isNonEmpty) {
            onClear();
        }
    };

    return (
        <View
            style={{
                width: constants.width,
                backgroundColor: COLORS.bgFFFFFF
            }}>
            <View
                style={{
                    width: constants.width,
                    paddingHorizontal: 10,
                    paddingVertical: 6,
                    backgroundColor: COLORS.bgFDF9E5
                }}>
                <MyText
                    text={`${SaleProgramID} - ${SaleProgramName}`}
                    style={{
                        marginBottom: 2,
                        fontWeight: 'bold',
                        color: COLORS.txt288AD6
                    }}
                />
                <TitleInput
                    title={translate('shoppingCart.text_input_contract_code')}
                    // isRequired={true}
                    styleInput={{
                        borderWidth: 1,
                        borderRadius: 4,
                        borderColor: COLORS.bdCCCCCC,
                        marginBottom: 5,
                        paddingHorizontal: 10,
                        backgroundColor: COLORS.bgFFFFFF
                    }}
                    value={contractID}
                    width={constants.width - 20}
                    height={36}
                    key="ContractID"
                    placeholder=""
                    onChangeText={onChange}
                    clearText={onClear}
                    onBlur={onValidate}
                    isClear={false}
                    placeholder={FormatContractID}
                />
                {!isPartnerConnectAPI ? (
                    <View>
                        <LockField
                            title={translate(
                                'shoppingCart.lf_contract_employee'
                            )}
                            style={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                height: 36,
                                width: constants.width - 20,
                                justifyContent: 'center',
                                backgroundColor: COLORS.bgE4E4E4
                            }}
                            value={valueProcess}
                            key="PGProcessUser"
                            placeholder=""
                        />
                        <LockField
                            title={translate(
                                'shoppingCart.lf_dealer_product_code'
                            )}
                            style={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                height: 36,
                                width: constants.width - 20,
                                justifyContent: 'center',
                                backgroundColor: COLORS.bgE4E4E4
                            }}
                            value={packageRates}
                            key="packageRates"
                            placeholder=""
                        />
                        <LockField
                            title={translate(
                                'shoppingCart.text_input_cost_contract'
                            )}
                            style={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                height: 36,
                                width: constants.width - 20,
                                justifyContent: 'center',
                                backgroundColor: COLORS.bgE4E4E4
                            }}
                            value={helper.convertNum(RecordsProcessingFee)}
                            key="RecordsProcessingFee"
                            placeholder=""
                        />
                        <LockField
                            title={translate('shoppingCart.text_input_deposit')}
                            isRequired
                            percent={percent}
                            style={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                height: 36,
                                width: constants.width - 20,
                                justifyContent: 'center',
                                backgroundColor: COLORS.bgE4E4E4
                            }}
                            value={helper.convertNum(totalPrePaid)}
                            key="TotalPrePaid"
                            placeholder=""
                        />
                        <LockField
                            title={translate('shoppingCart.picker_loan_term')}
                            style={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                height: 36,
                                width: constants.width - 20,
                                justifyContent: 'center',
                                backgroundColor: COLORS.bgE4E4E4
                            }}
                            value={valueTermLoan}
                            key="TermLoan"
                            placeholder=""
                        />
                        <LockField
                            title={translate(
                                'shoppingCart.text_input_payment_per_month'
                            )}
                            style={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                height: 36,
                                width: constants.width - 20,
                                justifyContent: 'center',
                                backgroundColor: COLORS.bgE4E4E4
                            }}
                            value={helper.convertNum(PaymentAmountMonthly)}
                            key="PaymentAmountMonthly"
                            placeholder=""
                        />
                    </View>
                ) : (
                    <View>
                        <LockField
                            title={translate(
                                'shoppingCart.lf_contract_employee'
                            )}
                            style={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                height: 36,
                                width: constants.width - 20,
                                justifyContent: 'center',
                                backgroundColor: COLORS.bgE4E4E4
                            }}
                            value={valueProcess}
                            key="PGProcessUser"
                            placeholder=""
                        />
                        <TitleInput
                            isRequired
                            title={translate(
                                'saleOrderPayment.lf_dealer_product_code'
                            )}
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                backgroundColor: COLORS.bgFFFFFF
                            }}
                            value={packageRates}
                            width={constants.width - 20}
                            height={36}
                            key="packageRates"
                            placeholder=""
                            onChangeText={(text) => {
                                data.packageRates = text;
                                updateData(data);
                            }}
                            clearText={() => {
                                data.packageRates = '';
                                updateData(data);
                            }}
                            isClear={false}
                        />
                        <LockField
                            title={translate(
                                'shoppingCart.text_input_cost_contract'
                            )}
                            style={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                height: 36,
                                width: constants.width - 20,
                                justifyContent: 'center',
                                backgroundColor: COLORS.bgE4E4E4
                            }}
                            value={helper.convertNum(RecordsProcessingFee)}
                            key="RecordsProcessingFee"
                            placeholder=""
                        />
                        {/* <LockField
                    title={translate('shoppingCart.text_input_deposit')}
                    isRequired={true}
                    percent={percent}
                    style={{
                        borderWidth: 1,
                        borderRadius: 4,
                        borderColor: COLORS.bdCCCCCC,
                        marginBottom: 5,
                        paddingHorizontal: 10,
                        height: 36,
                        width: constants.width - 20,
                        justifyContent: "center",
                        backgroundColor: COLORS.bgE4E4E4
                    }}
                    value={helper.convertNum(totalPrePaid)}
                    key={"TotalPrePaid"}
                    placeholder={""}
                /> */}
                        <InputMoney
                            isRequired
                            title={translate('shoppingCart.text_input_deposit')}
                            value={TotalPrePaid}
                            onChange={(value) => {
                                data.TotalPrePaid = value;
                                updateData(data);
                            }}
                            onBlur={() => {
                                let prePaid = TotalPrePaid;
                                if (prePaid < MinPrepaid) {
                                    prePaid = MinPrepaid;
                                } else if (prePaid > MaxPrepaid) {
                                    prePaid = MaxPrepaid;
                                } else {
                                    prePaid = Math.round(prePaid / 1000) * 1000;
                                }
                                data.TotalPrePaid = prePaid;
                                updateData(data);
                            }}
                        />
                        {/* <LockField
                    title={translate('shoppingCart.picker_loan_term')}
                    style={{
                        borderWidth: 1,
                        borderRadius: 4,
                        borderColor: COLORS.bdCCCCCC,
                        marginBottom: 5,
                        paddingHorizontal: 10,
                        height: 36,
                        width: constants.width - 20,
                        justifyContent: "center",
                        backgroundColor: COLORS.bgE4E4E4
                    }}
                    value={valueTermLoan}
                    key={"TermLoan"}
                    placeholder={""}
                /> */}
                        {helper.IsNonEmptyArray(TermLoanList) && (
                            <Picker
                                label="TermLoanName"
                                value="TermLoanNumber"
                                data={TermLoanList}
                                valueSelected={TermLoan}
                                onChange={(item) => {
                                    data.TermLoan = item.TermLoanNumber;
                                    updateData(data);
                                }}
                                numColumns={4}
                                defaultLabel={translate(
                                    'shoppingCart.picker_loan_term'
                                )}
                                title={translate(
                                    'shoppingCart.picker_loan_term'
                                )}
                                isRequired
                            />
                        )}
                        {/* <LockField
                    title={translate('shoppingCart.text_input_payment_per_month')}
                    style={{
                        borderWidth: 1,
                        borderRadius: 4,
                        borderColor: COLORS.bdCCCCCC,
                        marginBottom: 5,
                        paddingHorizontal: 10,
                        height: 36,
                        width: constants.width - 20,
                        justifyContent: "center",
                        backgroundColor: COLORS.bgE4E4E4
                    }}
                    value={helper.convertNum(PaymentAmountMonthly)}
                    key={"PaymentAmountMonthly"}
                    placeholder={""}
                /> */}
                        <InputMoney
                            title={translate(
                                'shoppingCart.text_input_payment_per_month'
                            )}
                            value={PaymentAmountMonthly}
                            onChange={(value) => {
                                data.PaymentAmountMonthly = value;
                                updateData(data);
                            }}
                        />
                    </View>
                )}
            </View>
        </View>
    );
};

export default ContractInfo;

export const LockField = ({
    style,
    placeholder,
    title,
    isRequired,
    percent = 0,
    value
}) => {
    const { width } = style;
    const roundPercent = percent > 0 ? ` (${percent.toFixed()}%) ` : '';
    return (
        <>
            <MyText
                text={title}
                addSize={-1.5}
                style={{
                    color: COLORS.txt333333,
                    fontWeight: 'bold',
                    fontStyle: 'italic',
                    width
                }}>
                {isRequired && (
                    <MyText
                        text={roundPercent}
                        addSize={-1.5}
                        style={{
                            color: COLORS.txtFF0000,
                            fontWeight: 'normal'
                        }}
                    />
                )}
            </MyText>
            <View style={style}>
                <MyText
                    text={value || placeholder}
                    style={{
                        color: COLORS.txt333333
                    }}
                />
            </View>
        </>
    );
};

const InputMoney = ({
    title,
    value,
    onChange,
    isRequired,
    onBlur = () => { }
}) => {
    return (
        <View
            style={{
                width: constants.width - 20
            }}>
            <MyText
                text={title}
                addSize={-1}
                style={{
                    color: COLORS.txt333333,
                    fontWeight: 'bold'
                }}>
                {isRequired && (
                    <MyText
                        text="*"
                        addSize={-1.5}
                        style={{
                            color: COLORS.txtFF0000
                        }}
                    />
                )}
            </MyText>
            <NumberInput
                style={{
                    height: 40,
                    width: constants.width - 20,
                    backgroundColor: COLORS.bgFFFFFF,
                    borderWidth: 1,
                    borderRadius: 4,
                    borderColor: COLORS.bdCCCCCC,
                    paddingHorizontal: 10
                }}
                placeholder="0"
                value={value}
                onChangeText={onChange}
                blurOnSubmit
                onBlur={onBlur}
            />
        </View>
    );
};
