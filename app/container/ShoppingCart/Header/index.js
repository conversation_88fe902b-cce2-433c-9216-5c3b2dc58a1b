import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { HeaderRight, HeaderCenter, BaseHeader } from "@header";
import { MyText, Icon } from "@components";
import { helper } from "@common";
import * as actionCartCreator from "../action";
import { COLORS } from "@styles";
import { ENUM, constants } from '@constants';

const { PARTNER_ID } = constants
const { PRE_ORDER } = ENUM.SALE_SCENARIO_TYPE;

const Header = ({
    navigation,
    netInfo,
    userInfo,
    title,
    dataShoppingCart,
    dataCartApply,
    actionCart,
    screenSale = "Sale",
    saleScenarioTypeID
}) => {
    const total = countProduct(dataShoppingCart);
    const { SaleOrderDetails } = dataShoppingCart || {};
    const { SaleProgramInfo } = SaleOrderDetails?.[0] || {};
    const isShow = saleScenarioTypeID != PRE_ORDER && SaleProgramInfo?.PartnerInstallmentID != PARTNER_ID.MOMO;
    const onGoBack = () => {
        const { CustomerInfo } = dataShoppingCart;
        actionCart.setDefaultCustomer({
            gender: CustomerInfo.Gender,
            customerPhone: CustomerInfo.CustomerPhone,
            customerName: CustomerInfo.CustomerName,
            customerAddress: CustomerInfo.CustomerAddress
        });
        dataCartApply.CustomerInfo = CustomerInfo;
        navigation.reset({
            index: 0,
            routes: [{ name: screenSale }],
        });
    }
    return (
        <BaseHeader>
            <HeaderLeft
                onPress={onGoBack}
                total={total}
                isShow={isShow}
            />
            <HeaderCenter
                title={title}
                info={userInfo}
            />
            <HeaderRight info={netInfo} />
        </BaseHeader>
    );
}

const mapStateToProps = (state) => ({
    netInfo: state.networkReducer,
    userInfo: state.userReducer,
    dataCartApply: state.pouchCartApply.dataCartApply,
    dataShoppingCart: state.shoppingCartReducer.dataShoppingCart,
    saleScenarioTypeID: state.specialSaleProgramReducer.saleScenarioTypeID,
});

const mapDispatchToProps = function (dispatch) {
    return {
        actionCart: bindActionCreators(actionCartCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(Header);

const HeaderLeft = ({ onPress, total, isShow }) => {
    return (
        <View>
            {
                isShow
                    ?
                    <TouchableOpacity style={{
                        height: 54,
                        width: 50,
                        justifyContent: "center",
                        alignItems: "center",
                    }}
                        activeOpacity={0.8}
                        onPress={onPress}
                    >
                        <Icon
                            iconSet={"MaterialIcons"}
                            name={"add-box"}
                            color={COLORS.icFFFFFF}
                            size={30}
                        />
                        <View style={{
                            width: 18,
                            height: 18,
                            borderRadius: 9,
                            position: "absolute",
                            backgroundColor: COLORS.bgEA1D5D,
                            marginRight: 4,
                            justifyContent: "center",
                            alignItems: "center",
                            top: 6,
                            right: 2
                        }}>
                            <MyText style={{
                                color: COLORS.txtFFFFFF,
                                fontWeight: "bold",
                            }}
                                text={total}
                                addSize={-4}
                            />
                        </View>
                    </TouchableOpacity>
                    :
                    <TouchableOpacity style={{
                        height: 54,
                        width: 50,
                        justifyContent: "center",
                        alignItems: "center",
                    }}
                        activeOpacity={0.8}
                    >
                    </TouchableOpacity>
            }
        </View>

    );
}

export const countProduct = (dataShoppingCart) => {
    let total = 0;
    const { SaleOrderDetails } = dataShoppingCart;
    if (helper.IsNonEmptyArray(SaleOrderDetails)) {
        SaleOrderDetails.forEach(mainProduct => {
            const {
                Quantity: quantity,
                saleSaleOrders,
                saleDeliverySaleOrders
            } = mainProduct;
            total += quantity;
            saleSaleOrders.forEach(saleProduct => {
                const { Quantity } = saleProduct;
                total += quantity * Quantity;
            });
            saleDeliverySaleOrders.forEach(saleProduct => {
                const { Quantity } = saleProduct;
                total += quantity * Quantity;
            });
        });
    }
    return total;
};