/* eslint-disable react/jsx-fragments */
import React, { Component, PureComponent } from 'react';
import {
    View,
    FlatList,
    TouchableOpacity,
    BackHandler,
    Alert,
    Linking,
    ActivityIndicator
} from 'react-native';
import Toast from 'react-native-toast-message';
import {
    MyText,
    Icon,
    Button,
    showBlockUI,
    hideBlockUI,
    ViewHTML,
    NumberInput
} from '@components';
import SafeAreaView from 'react-native-safe-area-view';
import { useIsFocused } from '@react-navigation/native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import KModal from 'react-native-modal';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { constants, CONFIG } from '@constants';
import { helper, dateHelper, debounce } from '@common';
import { translate } from '@translate';
import ModalSIMProcess from '../ModalSIMProcess';
import * as actionSaleOrderCartCreator from './action';
import * as actionGetSimCreator from '../ActiveSimManager/action';
import * as actionInstallmentCreator from './../InstallmentManager/action';
import * as actionSaleOrderCreator from '../SaleOrderPayment/action';
import * as actionManagerSOCreator from "../SaleOrderManager/action";
import * as actionDetailCreator from '../Detail/action';
import * as actionLoyaltyCreator from '../Loyalty/action';
import { ModalHeader } from '@header';
import { COLORS } from "@styles";
import { SCREENS } from '../AnKhangNew/constants';
import CustomerInfo from './components/CustomerInfo';
import PaymentBankTransfer from '../AnKhangNew/components/PaymentBankTransfer';
import PaymentTransferSheet from '../SaleOrderPayment/Sheet/PaymentTransferSheet';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';


const { PAYMENT_LABEL, PAYMENT_TYPE, PARTNER_ID } = constants;
class SaleOrderCart extends Component {
    constructor(props) {
        super(props);
        this.state = {
            isVisibleSODetailInfo: false,
            dataSaleOrderDetail: [],

            indexSimRequest: 0,
            indexSOSimRequest: 0,
            totalSimProcess: 0,

            saleOrderID: '',
            isVisibleBank: false,
            base64PDF: "",
            dataBankInfo: [],
            bankSelected: {},
            statusTransferPaymentSuccess: {
                type: "INIT",
                message: "GIAO DỊCH ĐANG ĐỢI XỬ LÝ...",
                saleOrderID: ""
            }
        };
        this.getSaleOrderSODetailInfo =
            this.getSaleOrderSODetailInfo.bind(this);
        this.paymentTransferSheetRef = React.createRef(null)
        this.intervalPaymentId = React.createRef(-1);

    }

    componentDidMount() {

        this.setState({
            totalSimProcess: this.getTotalSimProcess(this.props.dataSaleOrders)
        });
        // addEventListener "addEventListener"
        BackHandler.addEventListener(
            'hardwareBackPress',
            this.onBackButtonPressed
        );
    }
    componentDidUpdate(prevProps) {
        if (prevProps.route != this.props.route) {
            const { dataUpdate } = this.props.route?.params ?? { dataUpdate: {} }
            if (helper.IsEmptyObject(dataUpdate)) return
            const { indexSOSimRequest, indexSimRequest } = dataUpdate
            this.props.dataSaleOrders[
                indexSOSimRequest
            ].SIMProcessRequestIDBrandIDList[
                indexSimRequest
            ].isProcessSucess = true;
            this.setState({
                totalSimProcess: this.state.totalSimProcess - 1
            });
        }
    }
    onBackButtonPressed() {
        return true;
    }

    componentWillUnmount() {
        BackHandler.removeEventListener(
            'hardwareBackPress',
            this.onBackButtonPressed
        );
    }

    renderItemSaleOrderSODetail = (item, index) => {
        const imeiPMH = item.isGiftVoucher ? "xxx" : item.imei;
        return (
            <View
                style={{
                    flex: 1
                }}>
                <View
                    style={{
                        flex: 1,
                        backgroundColor: COLORS.bgF5F5F5,
                        paddingVertical: 15,
                        paddingHorizontal: 10
                    }}>
                    <MyText
                        text={item.productName}
                        style={{
                            color: COLORS.txt288AD6,
                            fontWeight: 'bold'
                        }}
                    />
                </View>

                <View
                    style={{
                        flex: 1,
                        backgroundColor: COLORS.bgFFFFFF,
                        paddingVertical: 15,
                        paddingHorizontal: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        borderWidth: 0.5,
                        borderColor: COLORS.bdF5F5F5
                    }}>
                    <MyText
                        text={translate('common.code_product')}
                        style={{
                            color: COLORS.txt333333
                        }}
                    />
                    <MyText
                        text={item.productId}
                        style={{
                            color: COLORS.txt333333,
                            fontWeight: 'bold'
                        }}
                    />
                </View>

                <View
                    style={{
                        flex: 1,
                        backgroundColor: COLORS.bgFFFFFF,
                        paddingVertical: 15,
                        paddingHorizontal: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        borderWidth: 0.5,
                        borderColor: COLORS.bdF5F5F5
                    }}>
                    <MyText
                        text={'IMEI:'}
                        style={{
                            color: COLORS.txt333333
                        }}
                    />
                    <MyText
                        text={imeiPMH}
                        style={{
                            color: COLORS.txt333333,
                            fontWeight: 'bold'
                        }}
                    />
                </View>

                <View
                    style={{
                        flex: 1,
                        backgroundColor: COLORS.bgFFFFFF,
                        paddingVertical: 15,
                        paddingHorizontal: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        borderWidth: 0.5,
                        borderColor: COLORS.bdF5F5F5
                    }}>
                    <MyText
                        text={translate('saleOrder.output_type')}
                        style={{
                            color: COLORS.txt333333
                        }}
                    />
                    <MyText
                        text={item.outputTypeId + ' - ' + item.outputTypeName}
                        style={{
                            color: COLORS.txt333333,
                            fontWeight: 'bold'
                        }}
                    />
                </View>

                <View
                    style={{
                        flex: 1,
                        backgroundColor: COLORS.bgFFFFFF,
                        paddingVertical: 15,
                        paddingHorizontal: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        borderWidth: 0.5,
                        borderColor: COLORS.bdF5F5F5
                    }}>
                    <MyText
                        text={translate('common.inventory_status')}
                        style={{
                            color: COLORS.txt333333
                        }}
                    />
                    <MyText
                        text={item.inventoryStatusName}
                        style={{
                            color: COLORS.txt2FB47C,
                            fontWeight: 'bold'
                        }}
                    />
                </View>

                <View
                    style={{
                        flex: 1,
                        backgroundColor: COLORS.bgFFFFFF,
                        paddingVertical: 15,
                        paddingHorizontal: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        borderWidth: 0.5,
                        borderColor: COLORS.bdF5F5F5
                    }}>
                    <MyText
                        text={translate('common.quanlity')}
                        style={{
                            color: COLORS.txt333333
                        }}
                    />
                    <MyText
                        text={item.quantity}
                        style={{
                            color: COLORS.txt333333,
                            fontWeight: 'bold'
                        }}
                    />
                </View>

                <View
                    style={{
                        flex: 1,
                        backgroundColor: COLORS.bgFFFFFF,
                        paddingVertical: 15,
                        paddingHorizontal: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        borderWidth: 0.5,
                        borderColor: COLORS.bdF5F5F5
                    }}>
                    <MyText
                        text={translate('common.cost_price')}
                        style={{
                            color: COLORS.txt333333
                        }}
                    />
                    <MyText
                        text={helper.convertNum(item.totalCost)}
                        style={{
                            color: COLORS.txtD0021B,
                            fontWeight: 'bold'
                        }}
                    />
                </View>
            </View>
        );
    };

    getSaleOrderSODetailInfo = (saleOrderID) => () => {
        const { actionSaleOrderCart } = this.props;
        showBlockUI();
        actionSaleOrderCart
            .getSaleOrderSODetail(saleOrderID)
            .then((res) => {
                hideBlockUI();
                this.setState({
                    isVisibleSODetailInfo: true,
                    dataSaleOrderDetail: res
                });
            })
            .catch((err) => {
                console.log(err);
                Alert.alert(
                    translate('common.notification_uppercase'),
                    err.msgError,
                    [
                        {
                            text: translate("common.btn_skip"),
                            onPress: () => hideBlockUI,
                            style: 'cancel'
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            onPress: this.getSaleOrderSODetailInfo(saleOrderID),
                            style: 'default'
                        }
                    ],
                    { cancelable: false }
                );
            });
    };

    handleAPIBankTransfer = (amount, saleOrderID) => {
        showBlockUI()
        const {
            actionSaleOrder
        } = this.props;
        actionSaleOrder.getBankInfo({
            saleOrderID: saleOrderID, paymentAmount: amount
        }).then((result) => {
            hideBlockUI()
            this.setState({
                dataBankInfo: result, bankSelected: result[0], statusTransferPaymentSuccess: {
                    type: "INIT",
                    message: "GIAO DỊCH ĐANG ĐỢI XỬ LÝ...",
                    saleOrderID: saleOrderID
                }
            }, () => {
                this.paymentTransferSheetRef.current?.present()
                if (this.state.bankSelected?.QRCodeData?.length > 0) {
                    this.handleIntervalPayment()
                }
            })
        }).catch((error) => {
            Alert.alert(
                translate('common.notification_uppercase'),
                error.msgError,
                [
                    {
                        text: translate('saleExpress.retry'),
                        style: 'cancel',
                        onPress: () => this.handleAPIBankTransfer(amount, saleOrderID)
                    },
                    {
                        text: translate('common.btn_skip'),
                        style: 'cancel',
                        onPress: () => hideBlockUI()
                    }

                ]
            );
        })
    }

    handleChangeBank = (bank) => {
        clearInterval(this.intervalPaymentId.current);
        this.setState({ bankSelected: bank }, () => {
            this.handleIntervalPayment()
        })
    }
    handleIntervalPayment = () => {
        if (!helper.configIntervalPayment()) return
        this.intervalPaymentId.current = setInterval(this.intervalPaymentFunction, 5000);
        setTimeout(() => {
            clearInterval(this.intervalPaymentId.current);
        }, 3000000);
    }
    intervalPaymentFunction = async () => {
        const { saleOrderID } = this.state.statusTransferPaymentSuccess
        const { bankSelected: { CreatedDate } } = this.state

        actionSaleOrderCreator.getTransactionTransfer(saleOrderID, CreatedDate).then(() => {
            this.setState({
                statusTransferPaymentSuccess: {
                    type: "SUCCESS",
                    message: "GIAO DỊCH ĐÃ THỰC HIỆN THÀNH CÔNG",
                    saleOrderID: saleOrderID
                }
            })
            clearInterval(this.intervalPaymentId.current);

        }).catch((msgError) => {
            this.setState({
                statusTransferPaymentSuccess: {
                    type: "ERROR",
                    message: msgError,
                    saleOrderID: saleOrderID

                }
            })
            clearInterval(this.intervalPaymentId.current);
        })
    }


    render() {
        const { isVisibleBank, base64PDF } = this.state;
        let {
            cartID,
            dataSaleOrders,
            initScreen,
            actionGetSim,
            actionSaleOrder,
            navigation,
            route,
            userInfo
        } = this.props;
        const { storeID } = userInfo;
        return (
            <View
                style={{
                    flex: 1
                }}>
                <BottomSheetModalProvider>
                    <View
                        style={{
                            justifyContent: 'center',
                            alignItems: 'center',
                            backgroundColor: COLORS.bg2FB47C,
                            // paddingTop: constants.BAR_HEIGHT,
                            height: constants.getSize(50)
                        }}>
                        <MyText
                            addSize={2}
                            text={translate('saleOrder.title')}
                            style={{
                                color: COLORS.txtFFF6AD,
                                fontWeight: 'bold'
                            }}
                        />
                    </View>

                    <SafeAreaView
                        style={{
                            flex: 1
                        }}>
                        <KeyboardAwareScrollView
                            keyboardShouldPersistTaps="always"
                            keyboardDismissMode="on-drag"
                            bounces={false}
                            overScrollMode="always"
                            showsHorizontalScrollIndicator={false}
                            showsVerticalScrollIndicator={false}
                            extraScrollHeight={48}
                            contentContainerStyle={{
                                paddingBottom: 20
                            }}>
                            <View
                                style={{
                                    flex: 1,
                                    backgroundColor: COLORS.bgF5F5F5
                                }}>
                                <View
                                    style={{
                                        height: 130,
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        backgroundColor: COLORS.bgFDF9E5,
                                        paddingVertical: constants.getSize(10)
                                    }}>
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            marginBottom: constants.getSize(10)
                                        }}>
                                        <Icon
                                            iconSet={'Ionicons'}
                                            name={'ios-checkmark-circle'}
                                            color={COLORS.ic2FB47C}
                                            size={40}
                                        />

                                        <View
                                            style={{
                                                marginLeft: constants.getSize(7)
                                            }}>
                                            <MyText
                                                addSize={2}
                                                text={translate(
                                                    'saleOrder.description_crate_order_success'
                                                )}
                                                style={{
                                                    color: COLORS.txt333333
                                                }}
                                            />

                                            <View
                                                style={{
                                                    flexDirection: 'row'
                                                }}>
                                                <MyText
                                                    addSize={2}
                                                    text={translate(
                                                        'saleOrder.code_shopping_cart'
                                                    )}
                                                    style={{
                                                        color: COLORS.txt333333
                                                    }}
                                                />

                                                <MyText
                                                    addSize={2}
                                                    text={cartID}
                                                    style={{
                                                        color: COLORS.txt333333,
                                                        fontWeight: 'bold'
                                                    }}
                                                />
                                            </View>
                                        </View>
                                    </View>

                                    <View
                                        style={{
                                            justifyContent: 'center',
                                            alignItems: 'center'
                                        }}>
                                        <MyText
                                            addSize={2}
                                            text={
                                                dataSaleOrders.length == 1
                                                    ? translate(
                                                        'saleOrder.description_separate_sale_order'
                                                    )
                                                    : translate(
                                                        'saleOrder.description_separate_sale_order_1'
                                                    ) +
                                                    dataSaleOrders.length +
                                                    translate(
                                                        'saleOrder.description_separate_sale_order_2'
                                                    )
                                            }
                                            style={{
                                                color: COLORS.txt333333,
                                                fontStyle: 'italic'
                                            }}
                                        />
                                        <MyText
                                            addSize={2}
                                            text={translate(
                                                'saleOrder.description_separate_sale_order_3'
                                            )}
                                            style={{
                                                color: COLORS.txt333333,
                                                fontStyle: 'italic'
                                            }}
                                        />
                                    </View>
                                </View>

                                <FlatList
                                    data={dataSaleOrders}
                                    keyboardShouldPersistTaps="handled"
                                    renderItem={({ item, index }) => {
                                        return (
                                            <ConnectedReduxSaleOrderItem
                                                index={index}
                                                route={route}
                                                key={index}
                                                dataSaleOrderItem={item}
                                                isAdditionalPromotion={route?.params?.isAdditionalPromotion}
                                                onPressDetail={this.getSaleOrderSODetailInfo(item.SaleOrderID)}
                                                isVisible={this.state.isVisibleSODetailInfo}
                                                hideModal={(() => {
                                                    this.setState({
                                                        isVisibleSODetailInfo: false
                                                    });
                                                }).bind(this)}
                                                dataSaleOrderDetail={this.state.dataSaleOrderDetail}
                                                renderItemSaleOrderSODetail={(item, index) => this.renderItemSaleOrderSODetail(item, index)}
                                                saleOrderID={item.SaleOrderID}
                                                onRequestSIMProcess={(dataSimRequest, indexSimRequest) => {
                                                    showBlockUI();
                                                    const dataSim = {
                                                        brandID: dataSimRequest.BrandID,
                                                        SIMProcessID: dataSimRequest.SIMProcessRequestID,
                                                        isGetDetail: true
                                                    };
                                                    actionGetSim.getSIMProcessInfo(dataSim)
                                                        .then(response => {

                                                            hideBlockUI();
                                                            this.props.navigation.navigate(
                                                                'SimProcess', {
                                                                screenName: "SaleOrderCart", dataPrepareUpdate: {
                                                                    indexSimRequest: indexSimRequest,
                                                                    indexSOSimRequest: index
                                                                }
                                                            }
                                                            );

                                                        }).catch(hideBlockUI)
                                                }}
                                                extraData={this.state.totalSimProcess}
                                                onRequestPayCashOutput={(saleOrder) => {
                                                    this.onPaymentSO(saleOrder)
                                                }}
                                                // 580
                                                loginStoreID={storeID}
                                                redirectScreenInstallment={(epTransactionId, partnerInstallmentID) => {
                                                    this.redirectScreenInstallment(epTransactionId, partnerInstallmentID);
                                                }}
                                                getContentBase64={this.getContentBase64View(item.SaleOrderID)}
                                                handleAPIBankTransfer={(amount) => { this.handleAPIBankTransfer(amount, item.SaleOrderID) }}
                                            />
                                        );
                                    }}
                                    keyExtractor={(item, index) => index.toString()}
                                    ItemSeparatorComponent={() => {
                                        return (
                                            <View
                                                style={{
                                                    height: constants.getSize(5),
                                                    backgroundColor: COLORS.bgF5F5F5
                                                }}
                                            />
                                        );
                                    }}
                                />

                                {this.state.totalSimProcess <= 0 && (
                                    <View
                                        style={{
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            marginVertical: constants.getSize(20)
                                        }}>
                                        <Button
                                            text="OK"
                                            styleContainer={{
                                                paddingHorizontal:
                                                    constants.getSize(10),
                                                paddingVertical:
                                                    constants.getSize(8),
                                                backgroundColor: COLORS.btn288AD6,
                                                borderRadius: 4,
                                                height: 50,
                                                width: 120
                                            }}
                                            styleText={{
                                                fontSize: 16,
                                                color: COLORS.txtFFFFBA
                                            }}
                                            onPress={() => {
                                                if (initScreen) {
                                                    navigation.reset({
                                                        index: 0,
                                                        routes: [{ name: initScreen }],
                                                    });
                                                }
                                            }}
                                        />
                                    </View>
                                )}
                            </View>
                        </KeyboardAwareScrollView>


                        <ViewHTML
                            isVisible={isVisibleBank}
                            source={base64PDF}
                            hideModal={() => {
                                this.setState({ isVisibleBank: false })
                            }}
                            title={translate('saleOrderManager.transfer_information_uppercase')}

                        />
                        <PaymentTransferSheet
                            paymentTransferSheetRef={this.paymentTransferSheetRef}
                            bankList={this.state.dataBankInfo}
                            bankSelected={this.state.bankSelected}
                            saleOrderID={this.state.statusTransferPaymentSuccess.saleOrderID}
                            onChangeBank={this.handleChangeBank}
                            handleIntervalPayment={this.handleIntervalPayment}
                            onChangeStatusSheet={(position) => {
                                if (position == -1) {
                                    clearInterval(this.intervalPaymentId.current);
                                }
                            }}
                            statusTransferPaymentSuccess={this.state.statusTransferPaymentSuccess}
                        />
                    </SafeAreaView>

                </BottomSheetModalProvider>
            </View>
        );
    }

    getTotalSimProcess = (dataSaleOrders) => {
        let totalSimProcess = 0;
        dataSaleOrders.forEach((saleOrder) => {
            if (helper.IsNonEmptyArray(saleOrder.SIMProcessRequestIDBrandIDList)) {
                totalSimProcess += saleOrder.SIMProcessRequestIDBrandIDList.length;
            }
        });
        return totalSimProcess;
    };
    onPaymentSO = (item) => {
        const {
            SaleOrderTypeID,
            SaleOrderID
        } = item;
        if (SaleOrderTypeID == 73) {
            Alert.alert('', translate('saleOrderManager.warning_yellow'));
        } else {
            this.props.actionSaleOrder
                .setDataSO(item)
                .then((success) => {
                    const isPre = `,${this.props.PM_PreOrder_SaleOrderTypeList},`.includes(`,${SaleOrderTypeID.toString()},`);
                    if (!this.props.shouldCallPromotion && isPre) {
                        this.props.navigation.navigate("PreOrderProduct");
                        this.props.actionManagerSO.getPreOrderCart(SaleOrderID);
                    }
                    else {
                        this.handleGetDataSaleOrder(item)
                    }
                });
        }
    }

    handleGetDataSaleOrder = async (SOInfo) => {
        const { SaleOrderID, SaleOrderTypeID } = SOInfo;
        try {
            showBlockUI();
            const { isExpressSale } = this.props.route.params ?? { isExpressSale: false };
            const paymentScreen = isExpressSale ? SCREENS.PaymentScreen : 'SaleOrderPayment';
            const { actionSaleOrder, navigation } = this.props;
            const dataSaleOrder = await actionSaleOrder.getSaleOrderPayment(SaleOrderID);
            hideBlockUI();
            const paymentTransactionTypeId = dataSaleOrder?.ExtensionProperty?.paymentTransactionTypeId || ""
            if (dataSaleOrder.cus_WarningMessage) {
                Alert.alert(
                    translate("common.notification"),
                    dataSaleOrder.cus_WarningMessage,
                    [
                        {
                            text: translate("common.customer_decline"),
                            onPress: () => this.handleSkipRandomDiscountPromotion(SOInfo)
                        },
                        {
                            text: translate("common.customer_accept"),
                            onPress: hideBlockUI,
                            style: "cancel"
                        }
                    ]
                );
            } else {
                navigation.navigate(paymentScreen, { shouldOriginUpdate: true });
                actionSaleOrder.getReportPrinterSocket(SaleOrderTypeID);
                actionSaleOrder.getDataQRTransaction(SaleOrderID, paymentTransactionTypeId);
                actionSaleOrder.getDataSCTransaction(SaleOrderID);
            }
        } catch ({ msgError }) {
            Alert.alert(translate('common.notification_uppercase'), msgError, [
                {
                    text: translate('common.btn_skip'),
                    onPress: hideBlockUI
                },
                {
                    text: translate('common.btn_notify_try_again'),
                    onPress: () => this.handleGetDataSaleOrder(SOInfo),
                }
            ]);
        }
    };

    handleSkipRandomDiscountPromotion = async (SOInfo) => {
        try {
            const { dataSaleOrder } = this.props;
            const appliedRandomDiscountList = [];
            dataSaleOrder.listMainProduct.forEach((product) => {
                if (product.cus_RandomDiscountApplyB0) {
                    appliedRandomDiscountList.push({
                        ...product.cus_RandomDiscountApplyB0,
                        Status: 1
                    });
                }
            });
            if (appliedRandomDiscountList.length > 0) {
                const { actionDetail } = this.props;
                await actionDetail.updateStatusRandomDiscountPromotion(appliedRandomDiscountList);
                hideBlockUI();
                this.onPaymentSO(SOInfo);
            } else {
                throw new Error("Không thể thu tiền xuất hàng với đơn hàng có tham gia khuyến mãi Vòng quay may mắn.");
            }
        } catch (error) {
            Alert.alert(translate("common.notification"), error, [
                {
                    text: translate("common.btn_notify_try_again"),
                    style: "default",
                    onPress: () => this.handleSkipRandomDiscountPromotion(SOInfo)
                },
                {
                    text: translate("common.btn_skip"),
                    onPress: hideBlockUI
                }
            ]);
        }
    };

    handleSkipRandomDiscountPromotionInstallment = async (epTransactionId, appliedRandomDiscountList) => {
        try {
            if (appliedRandomDiscountList?.length > 0) {
                const newAppliedRandomDiscountList = appliedRandomDiscountList.map(item => ({
                    ...item,
                    Status: 1
                }));
                await this.props.actionDetail.updateStatusRandomDiscountPromotion(newAppliedRandomDiscountList);
            } else {
                throw new Error("Không thể xem thông tin trả góp với đơn hàng có tham gia khuyến mãi Vòng quay may mắn.");
            }

            this.redirectScreenInstallment(epTransactionId);
        } catch (error) {
            Alert.alert(translate("common.notification"), error, [
                {
                    text: translate("common.btn_notify_try_again"),
                    style: "default",
                    onPress: () =>
                        this.handleSkipRandomDiscountPromotionInstallment(
                            epTransactionId,
                            appliedRandomDiscountList
                        )
                },
                {
                    text: translate("common.btn_skip"),
                    onPress: hideBlockUI
                }
            ]);
        }
    }

    redirectScreenInstallment = (epTransactionId, partnerInstallmentID) => {
        const { installmentAction, navigation, userInfo } = this.props;
        const { storeID } = userInfo;
        const storeIDNew = helper.checkConfigStoreInstallmentNew(storeID);
        showBlockUI();
        if (partnerInstallmentID == 16) {// is F88 instalment partner
            installmentAction.getInstalmentInfoF88(epTransactionId, false)
                .then((dataInstalment) => {
                    hideBlockUI();
                    navigation.navigate('Installment', {
                        screen: 'PortalInstalmentF88',
                        params: {
                            dataInstalment
                        },
                    });
                })
                .catch(mesError => {
                    Alert.alert("Thông báo", mesError, [
                        {
                            text: "OK",
                            onPress: hideBlockUI
                        }
                    ])
                });
        }
        else {
            if (storeIDNew) {
                const data = {
                    EPTransactionID: epTransactionId,
                    times: 1
                }
                installmentAction.getEPTransactionNew(data).then((res) => {
                    if (!res.objEPOSTransactionBO?.cus_WarningMessage) {
                        hideBlockUI();
                        if (res.objEPOSTransactionBO.cus_ExistCompleteRandomDis) {
                            res.objEPOSTransactionBO.TotalPrepaid = 0;
                            res.objEPOSTransactionBO.MonthlyPayment = 0;
                        }
                        navigation.navigate('Installment', {
                            screen: 'InstallmentSteps',
                            params: {
                                data: res,
                                instalmentData: res,
                                isCreateFromSO: true,
                                itemInstallmentManager: {
                                    isCreatedSt: true,
                                    isCreatedNd: false
                                }
                            },
                        });
                    } else {
                        Alert.alert(
                            translate('common.notification'),
                            res.objEPOSTransactionBO.cus_WarningMessage,
                            [
                                {
                                    text: translate("common.customer_decline"),
                                    onPress: () =>
                                        this.handleSkipRandomDiscountPromotionInstallment(
                                            epTransactionId,
                                            res.lstRandomDiscountApply
                                        )
                                },
                                {
                                    text: translate("common.customer_accept"),
                                    style: "cancel",
                                    onPress: hideBlockUI
                                }
                            ],
                            { cancelable: false }
                        );
                    }
                })
                    .catch((err) => {
                        Alert.alert(
                            translate('common.notification'),
                            err.msgError,
                            [
                                {
                                    text: translate('common.btn_close'),
                                    onPress: () => {
                                        hideBlockUI();
                                    }
                                }
                            ],
                            { cancelable: false }
                        );
                    });
            } else {
                installmentAction
                    .getEPTransaction(epTransactionId)
                    .then((res) => {
                        if (!res.objEPOSTransactionBO?.cus_WarningMessage) {
                            hideBlockUI();
                            if (res.objEPOSTransactionBO.cus_ExistCompleteRandomDis) {
                                res.objEPOSTransactionBO.TotalPrepaid = 0;
                                res.objEPOSTransactionBO.MonthlyPayment = 0;
                            }
                            navigation.navigate('Installment', {
                                screen: 'InstallmentSteps',
                                params: {
                                    data: res,
                                    instalmentData: res,
                                    isCreateFromSO: true
                                },
                            });
                        } else {
                            Alert.alert(
                                translate('common.notification'),
                                res.objEPOSTransactionBO.cus_WarningMessage,
                                [
                                    {
                                        text: translate("common.customer_decline"),
                                        onPress: () =>
                                            this.handleSkipRandomDiscountPromotionInstallment(
                                                epTransactionId,
                                                res.lstRandomDiscountApply
                                            )
                                    },
                                    {
                                        text: translate("common.customer_accept"),
                                        style: "cancel",
                                        onPress: hideBlockUI
                                    }
                                ],
                                { cancelable: false }
                            );
                        }
                    })
                    .catch((err) => {
                        Alert.alert(
                            translate('common.notification'),
                            err.msgError,
                            [
                                {
                                    text: translate('common.btn_close'),
                                    onPress: () => {
                                        hideBlockUI();
                                    }
                                }
                            ],
                            { cancelable: false }
                        );
                    });
            }
        }
    };

    getContentBase64View = (saleOrderID) => () => {
        showBlockUI();
        const { actionManagerSO } = this.props;
        actionManagerSO.getContentBase64View({
            "reportContent": 'BankAccountContent',
            "saleOrderID": saleOrderID
        }).then(base64 => {
            this.setState({
                isVisibleBank: true,
                base64PDF: base64
            });
            hideBlockUI();
        }).catch(msgError => {
            Alert.alert(translate('common.notification'), msgError, [{
                text: "OK",
                onPress: hideBlockUI
            }]);
        })
    }
}

const mapStateToProps = function (state) {
    return {
        cartID: state.saleOrderCartReducer.dataSaleOrderCart.CartID,
        dataSaleOrders: state.saleOrderCartReducer.dataSaleOrderCart.SaleOrders,
        initScreen: state.saleOrderCartReducer.initScreen,
        saleScenarioTypeID: state.specialSaleProgramReducer.saleScenarioTypeID,
        userInfo: state.userReducer,
        dataSaleOrder: state.saleOrderPaymentReducer.dataSaleOrder,
        shouldCallPromotion: state.detailReducer.shouldCallPromotion,
        PM_PreOrder_SaleOrderTypeList: state.appSettingReducer.PM_PreOrder_SaleOrderTypeList
    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        actionGetSim: bindActionCreators(actionGetSimCreator, dispatch),
        actionSaleOrderCart: bindActionCreators(actionSaleOrderCartCreator, dispatch),
        actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),
        installmentAction: bindActionCreators(actionInstallmentCreator, dispatch),
        actionManagerSO: bindActionCreators(actionManagerSOCreator, dispatch),
        actionDetail: bindActionCreators(actionDetailCreator, dispatch),
    };
};

const SaleOrderCartWithFocused = (props) => {
    const isFocused = useIsFocused();
    return <SaleOrderCart {...props} isFocused={isFocused} />
}

export default connect(mapStateToProps, mapDispatchToProps)(SaleOrderCartWithFocused);

class SaleOrderItem extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            isShowDetail: true,
            cash: 0,

        };


    }

    componentDidMount() {
        const { dataSaleOrderItem, actionLoyalty } = this.props;

        handleApiGetPointsByPayment({
            PaymentType: PAYMENT_TYPE.BANKING,
            PaymentValue: dataSaleOrderItem.TotalAmount,
            apiAction: actionLoyalty.getPointsByPayment
        });
    }

    componentDidUpdate(prevProps) {
        const { dataSaleOrderItem, actionLoyalty, isFocused, index } = this.props;
        if (isFocused !== prevProps.isFocused && isFocused) {
            index === 0 &&
                handleApiGetPointsByPayment({
                    PaymentType: PAYMENT_TYPE.BANKING,
                    PaymentValue: dataSaleOrderItem.TotalAmount,
                    apiAction: actionLoyalty.getPointsByPayment
                });
        }
    }


    renderItem(item, index) {
        return (
            <View
                key={index}
                style={{
                    flex: 1,
                    backgroundColor: index % 2 == 0 ? COLORS.bgE4EBD5 : COLORS.bgFFFFFF,
                    flexDirection: 'row',
                    padding: constants.getSize(10)
                }}>
                <MyText
                    text={item.key}
                    style={{
                        color: COLORS.txt333333,
                        fontWeight: 'bold',
                        width: '40%'
                    }}
                />
                <MyText
                    text={item.value}
                    style={{
                        color: COLORS.txt333333,
                        width: '60%'
                    }}
                />
            </View>
        );
    }

    getDataSaleOderDetail(itemSaleOrder, route) {
        if (itemSaleOrder.TaxID) {
            return [
                {
                    key: translate('saleOrder.type_require_output'),
                    value:
                        itemSaleOrder.SaleOrderTypeID +
                        ' - ' +
                        itemSaleOrder.SaleOrderTypeName
                },
                {
                    key: translate('saleOrder.output_store'),
                    value:
                        itemSaleOrder.OutputStoreID +
                        ' - ' +
                        itemSaleOrder.OutputStoreName
                },
                {
                    key: translate('saleOrder.pay_type'),
                    value:
                        itemSaleOrder.PayableTypeID +
                        ' - ' +
                        itemSaleOrder.PayableTypeName
                },
                {
                    key: translate('saleOrder.delivery_type'),
                    value:
                        itemSaleOrder.DeliveryTypeID +
                        ' - ' +
                        itemSaleOrder.DeliveryTypeName
                },
                {
                    key: translate('common.customer'),
                    value: itemSaleOrder.CustomerName
                },
                {
                    key: translate('saleOrder.taxId'),
                    value: itemSaleOrder.TaxID
                },
                {
                    key: translate('common.delivery_time'),
                    value: itemSaleOrder.DeliveryTime
                        ? dateHelper.formatStrDateFULL(
                            itemSaleOrder.DeliveryTime,
                            'yyyy-mm-ddThh:ii:ss'
                        )
                        : ''
                },
                {
                    key: translate('saleOrder.sum_price_sale_order'),
                    value: helper.convertNum(itemSaleOrder.TotalAmount)
                },
                {
                    key: translate('saleOrder.advance'),
                    value: helper.convertNum(itemSaleOrder.DepositAmount)
                },
                { key: translate('saleOrder.note'), value: itemSaleOrder.Note }
            ];
        }
        else {
            return [
                {
                    key: translate('saleOrder.type_require_output'),
                    value:
                        itemSaleOrder.SaleOrderTypeID +
                        ' - ' +
                        itemSaleOrder.SaleOrderTypeName
                },
                {
                    key: translate('saleOrder.output_store'),
                    value:
                        itemSaleOrder.OutputStoreID +
                        ' - ' +
                        itemSaleOrder.OutputStoreName
                },
                {
                    key: translate('saleOrder.pay_type'),
                    value:
                        itemSaleOrder.PayableTypeID +
                        ' - ' +
                        itemSaleOrder.PayableTypeName
                },
                {
                    key: translate('saleOrder.delivery_type'),
                    value:
                        itemSaleOrder.DeliveryTypeID +
                        ' - ' +
                        itemSaleOrder.DeliveryTypeName
                },
                {
                    key: translate('common.customer'),
                    value: itemSaleOrder.CustomerName
                },

                {
                    key: translate('common.delivery_time'),
                    value: itemSaleOrder.DeliveryTime
                        ? dateHelper.formatStrDateFULL(
                            itemSaleOrder.DeliveryTime,
                            'yyyy-mm-ddThh:ii:ss'
                        )
                        : ''
                },
                {
                    key: translate('saleOrder.sum_price_sale_order'),
                    value: helper.convertNum(itemSaleOrder.TotalAmount)
                },
                {
                    key: translate('saleOrder.advance'),
                    value: helper.convertNum(itemSaleOrder.DepositAmount)
                },
                { key: translate('saleOrder.note'), value: itemSaleOrder.Note }
            ];
        }
    }

    handleGetPointsByPayment = (value) => {
        const { loyaltyPercentBonus, actionLoyalty } = this.props;
        handleApiGetPointsByPayment({
            PaymentType: PAYMENT_TYPE.BANKING,
            PaymentValue: value,
            apiAction: actionLoyalty.getPointsByPayment,
            percentBonus: loyaltyPercentBonus
        });
    };

    debounceGetPoints = debounce(this.handleGetPointsByPayment, 1000);


    render() {
        const {
            dataSaleOrderItem,
            onPressDetail,
            onRequestSIMProcess,
            redirectScreenInstallment,
            extraData,
            onRequestPayCashOutput,
            isAdditionalPromotion,
            getContentBase64,
            route,
            loyaltyPointBonus,
            loyaltyPercentBonus,
            saleOrderID
        } = this.props;
        const { isShowDetail, cash } = this.state;
        const { TotalAmount, TotalPrePaid, SaleProgramID, PartnerInstallmentID } = dataSaleOrderItem
        const maxValueTransfer = !!SaleProgramID ? TotalPrePaid : TotalAmount
        const isNewFollowPartner = helper.isNewFollowPartner(PartnerInstallmentID);
        return (
            <View
                style={{
                    flex: 1
                }}>
                <TouchableOpacity
                    style={{
                        backgroundColor: isShowDetail
                            ? COLORS.bg2FB47C
                            : COLORS.bgFFFFFF,
                        padding: constants.getSize(12),
                        flexDirection: 'row',
                        alignItems: 'center'
                    }}
                    onPress={() => {
                        this.setState({
                            isShowDetail: !isShowDetail
                        });
                    }}
                    activeOpacity={0.6}>
                    <MyText
                        addSize={2}
                        text={translate('saleOrder.code_require_output')}
                        style={{
                            color: isShowDetail
                                ? COLORS.txtFFFFFF
                                : COLORS.txt333333,
                            fontWeight: 'bold'
                        }}
                    />

                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center'
                        }}>
                        <MyText
                            addSize={2}
                            text={dataSaleOrderItem.SaleOrderID}
                            style={{
                                color: isShowDetail
                                    ? COLORS.txtFFFFBA
                                    : COLORS.txt288AD6,
                                fontWeight: 'bold'
                            }}
                        />
                        <Icon
                            iconSet={'MaterialIcons'}
                            name={'arrow-drop-down'}
                            color={
                                isShowDetail ? COLORS.icFFFFBA : COLORS.ic288AD6
                            }
                            size={22}
                        />
                    </View>
                </TouchableOpacity>

                {isShowDetail && (
                    <View
                        style={{
                            flex: 1
                        }}>
                        <CustomerInfo dataSaleOrderItem={dataSaleOrderItem} />
                        {this.getDataSaleOderDetail(dataSaleOrderItem, route).map(
                            (item, index) => this.renderItem(item, index)
                        )}
                        {!isNewFollowPartner && <PaymentBankTransfer
                            saleOrderID={saleOrderID}
                            payableAmount={maxValueTransfer || 0}
                            colorPrimary={COLORS.btn2FB47C}
                            colorSecondary={COLORS.btn2FB47C}
                            colorTertiary={COLORS.btn2FB47C}
                            defaultShow
                            handleAPIBankTransfer={this.props.handleAPIBankTransfer}
                        />}

                        <View style={{
                            flexDirection: "row",
                            width: constants.width,
                            // alignItems: "center",
                            justifyContent: "space-between",
                            paddingVertical: 4
                        }}>
                            <View>
                                {loyaltyPercentBonus ? (
                                    <View style={{ paddingHorizontal: 10 }}>
                                        <TempInputCash
                                            value={cash}
                                            onChangeText={(text) => {
                                                if (cash !== text) {
                                                    this.setState({
                                                        cash: text
                                                    });
                                                    this.debounceGetPoints(
                                                        text
                                                    );
                                                }
                                            }}
                                        />
                                        <GainExtraPoints
                                            amount={
                                                cash ? loyaltyPointBonus : 0
                                            }
                                            percentValue={loyaltyPercentBonus}
                                        />
                                    </View>
                                ) : null}
                            </View>
                            <MyText
                                text={translate('common.view_detail')}
                                style={{
                                    color: "#147efb",
                                    padding: 10,
                                    fontStyle: "italic",
                                    textDecorationLine: 'underline'
                                }}
                                onPress={onPressDetail}
                            />
                        </View>
                        {
                            global.isVN &&
                            <>
                                <FlatList
                                    data={dataSaleOrderItem.SIMProcessRequestIDBrandIDList}
                                    renderItem={({ item, index }) => (
                                        <Button
                                            key={1}
                                            iconRight={{
                                                iconSet: 'FontAwesome',
                                                name: 'chevron-right',
                                                size: 12,
                                                marginLeft: 8,
                                                color: COLORS.icFFFFBC
                                            }}
                                            text={
                                                translate('saleOrder.sim_requirements') +
                                                item.SIMProcessRequestID
                                            }
                                            styleContainer={{
                                                flexDirection: 'row',
                                                width: constants.width,
                                                height: 40,
                                                backgroundColor: COLORS.btn5B917B,
                                                marginTop: 8,
                                                opacity: !!item.isProcessSucess
                                                    ? 0.5
                                                    : 1
                                            }}
                                            styleText={{
                                                color: COLORS.txtFFFFBC,
                                                fontSize: 16,
                                                fontWeight: 'bold'
                                            }}
                                            onPress={() => {
                                                onRequestSIMProcess(item, index);
                                            }}
                                            disabled={!!item.isProcessSucess}
                                        />
                                    )}
                                    keyExtractor={(item, index) => index.toString()}
                                    extraData={extraData}
                                />

                                {
                                    helper.IsNonEmptyString(dataSaleOrderItem.EPOSTransactionID) &&
                                    <Button
                                        key={1}
                                        iconRight={{
                                            iconSet: 'FontAwesome',
                                            name: 'chevron-right',
                                            size: 12,
                                            marginLeft: 8,
                                            color: COLORS.icFFFFBC
                                        }}
                                        text={`${translate('saleOrder.create_profile')} ${dataSaleOrderItem.EPOSTransactionID}`}
                                        styleContainer={{
                                            flexDirection: 'row',
                                            width: constants.width,
                                            height: 40,
                                            backgroundColor: COLORS.btn5B917B,
                                            marginTop: 8
                                        }}
                                        styleText={{
                                            color: COLORS.txtFFFFBC,
                                            fontSize: 16,
                                            fontWeight: 'bold'
                                        }}
                                        onPress={() => {
                                            redirectScreenInstallment(
                                                dataSaleOrderItem.EPTransactionID, dataSaleOrderItem.PartnerInstallmentID
                                            );
                                        }}
                                    />
                                }
                                {
                                    !isAdditionalPromotion &&
                                    < View
                                        style={{
                                            flex: 1,
                                            justifyContent: 'center',
                                            alignItems: 'center'
                                        }}>
                                        <Button
                                            text={translate('saleOrder.export_cash')}
                                            styleContainer={{
                                                flexDirection: 'row',
                                                width: 200,
                                                height: 40,
                                                backgroundColor: COLORS.btn2FB47C,
                                                marginTop: 8,
                                                borderRadius: 4
                                            }}
                                            styleText={{
                                                color: COLORS.txtFFFFFF,
                                                fontSize: 16,
                                                fontWeight: 'bold'
                                            }}
                                            onPress={() => {
                                                onRequestPayCashOutput(dataSaleOrderItem);
                                            }}
                                        />
                                    </View>
                                }
                            </>
                        }
                        <ModalSaleOrderSODetail {...this.props} />
                    </View>
                )}

            </View>
        );
    }
}

const mapStateToPropsSaleOrderItem = (state) => ({
    loyaltyPercentBonus: state.loyaltyReducer.loyaltyPercentBonus,
    loyaltyPointBonus: state.loyaltyReducer.loyaltyPointBonus,
    userInfo: state.userReducer,
});

const mapDispatchToPropsSaleOrderItem = function (dispatch) {
    return {
        actionLoyalty: bindActionCreators(actionLoyaltyCreator, dispatch),
        actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),

    };
};

const SaleOrderPaymentWithFocused = (props) => {
    const isFocused = useIsFocused();
    return <SaleOrderItem {...props} isFocused={isFocused} />
}
const ConnectedReduxSaleOrderItem = connect(
    mapStateToPropsSaleOrderItem,
    mapDispatchToPropsSaleOrderItem
)(SaleOrderPaymentWithFocused);

class ModalSaleOrderSODetail extends Component {
    constructor() {
        super();
        this.state = {};
    }
    render() {
        let {
            isVisible,
            hideModal,
            dataSaleOrderDetail,
            renderItemSaleOrderSODetail,
            saleOrderID
        } = this.props;
        return (
            <KModal
                isVisible={isVisible}
                transparent={true}
                style={{ margin: 0 }}
                deviceWidth={constants.width}
                deviceHeight={constants.height}
                animationIn={'slideInUp'}
                animationOut={'slideOutUp'}
                animationInTiming={100}
                animationOutTiming={50}>
                <ModalHeader
                    title={`YCX: ${saleOrderID}`}
                    onClose={hideModal}
                />
                <SafeAreaView
                    style={{
                        flex: 1,
                        justifyContent: 'center',
                        backgroundColor: COLORS.bgFFFFFF
                    }}>
                    <FlatList
                        data={dataSaleOrderDetail}
                        renderItem={({ item, index }) =>
                            renderItemSaleOrderSODetail(item, index)
                        }
                        keyExtractor={(item, index) => index.toString()}
                    />
                </SafeAreaView>
            </KModal>
        );
    }
}

const onLinkingMWGApp = () => {
    Linking.canOpenURL('mwgapp://mwgpos').then((supported) => {
        if (supported) {
            Linking.openURL('mwgapp://mwgpos');
        }
    });
};

export const GainExtraPoints = ({ percentValue }) => {
    const text = `Tặng thêm ${percentValue}% điểm QTV trên số chuyển khoản khách thanh toán.`;
    return <MyText style={{ width: 192 }} text={text} />;
};

const TempInputCash = ({ value, onChangeText, onGetPoints, isLoading }) => (
    <View style={{ flexDirection: 'row', marginVertical: 4 }}>
        <NumberInput
            style={{
                height: 38,
                width: 192,
                backgroundColor: COLORS.bgFFFFFF,
                paddingHorizontal: 9,
                color: COLORS.txt000000,
                borderColor: COLORS.bd218DEB,
                borderWidth: 1,
                borderRadius: 4,
                marginRight: 4
            }}
            value={value}
            onChangeText={onChangeText}
            placeholder="Nhập số tiền để kiểm tra"
        />
        {onGetPoints && (
            <TouchableOpacity
                style={{
                    width: 32,
                    height: 32,
                    borderColor: COLORS.bd218DEB,
                    backgroundColor: COLORS.bd218DEB,
                    borderWidth: 1,
                    borderRadius: 4,
                    marginLeft: 4,
                    justifyContent: 'center',
                    alignItems: 'center',
                    opacity: isLoading ? 0.5 : 1
                }}
                disabled={isLoading}
                onPress={onGetPoints}>
                {isLoading ? (
                    <ActivityIndicator color={COLORS.icFFFFFF} />
                ) : (
                    <Icon
                        iconSet="MaterialCommunityIcons"
                        name="text-box-search-outline"
                        color={COLORS.icFFFFFF}
                        size={18}
                    />
                )}
            </TouchableOpacity>
        )}
    </View>
);

export const handleApiGetPointsByPayment = ({
    PaymentValue = 0,
    PaymentType = PAYMENT_TYPE.CASH,
    percentBonus,
    apiAction
}) => {
    const paymentDetailRequests = [
        {
            BankID: 0,
            PaymentValue,
            PaymentType // 2 -> Chuyển khoản; 1 -> Tiền mặt
        }
    ];
    const paymentLabel = PAYMENT_LABEL[PaymentType];
    if (percentBonus > 0) {
        if (PaymentValue !== 0) {
            const percent = percentBonus / 100;
            const value = PaymentValue * percent;
            const formattedValue = helper.convertNum(value, false);
            Toast.show({
                type: 'success',
                text1: `Khách hàng được tặng thêm ${formattedValue} điểm khi thanh toán ${paymentLabel} (tạm tính).`,
                position: 'top',
                visibilityTime: 5000
            });
        }
    }
    // else {
    //     apiAction(paymentDetailRequests)
    //         .then((response) => {
    //             const { PERCENT } = response[0];
    //             Toast.show({
    //                 type: 'success',
    //                 text1: `Tặng thêm ${PERCENT}% điểm QTV trên số ${paymentLabel} khách thanh toán.`,
    //                 position: 'top'
    //             });
    //         })
    //         .catch((error) => {
    //             !CONFIG.isPRODUCTION &&
    //                 Toast.show({
    //                     type: 'error',
    //                     text1: error ?? 'Contact: Nghĩa Trần 165059',
    //                     position: 'top'
    //                 });
    //         });
    // }
};
