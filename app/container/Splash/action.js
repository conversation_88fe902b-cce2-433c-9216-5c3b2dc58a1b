import { Alert } from "react-native";
import {
    API_CONST,
    DEVICE,
    STORAGE_CONST,
    constants,
    ENUM
} from '@constants';
import { helper, storageHelper } from '@common';
import { METHOD, apiBase, apiBaseFW, getQueryString } from '@config';
import { translate } from "@translate";
import { getBodyAuthInfo } from '../Login/action';
import { getAppSetingConfig } from '../AppSetting/action';
const { URL_DOWNLOAD_APP } = constants;
const {
    API_REQUEST_TOKEN,
    API_AUTHEN_FW_OFFLINE,
} = API_CONST;
const { COMPANY_ID } = ENUM


const START_CHECK_AUTHEN = "START_CHECK_AUTHEN";
const STOP_CHECK_AUTHEN = "STOP_CHECK_AUTHEN";

export const authenAction = {
    START_CHECK_AUTHEN,
    STOP_CHECK_AUTHEN
}

export const requestOauthToken = (response) => {
    return async function (dispatch, getState) {
        console.log("requestOauthToken success: ", response);
        response.change_device = false;
        const { access_token, change_device, DEVICE_TOKEN } = response;
        global.isChangeDevice = change_device;
        if (helper.IsNonEmptyString(access_token)) {
            const requireOTP = change_device ? "true" : "false";
            await storageHelper.multiSet([
                [STORAGE_CONST.ACCESS_TOKEN, access_token],
                [STORAGE_CONST.DEVICE_TOKEN, DEVICE_TOKEN],
                [STORAGE_CONST.REQUIRE_OTP, requireOTP]
            ]);
            dispatch(callApiRequestToken());
        }
    }
}

export const callApiRequestToken = function () {
    return async function (dispatch, getState) {
        const body = await getBodyAuthInfo();
        dispatch(start_check_authen());
        apiBase(API_REQUEST_TOKEN, METHOD.POST, body).then((response) => {
            console.log("callApiRequestToken success: ", response);
            const { object } = response;
            dispatch(checkAuthenInfo(object));
            dispatch(getAppSetingConfig());
        }).catch(error => {
            console.log("callApiRequestToken error: ", error);
            const { statusCode, msgError } = error;
            if (statusCode == 401) {
                dispatch(stop_check_authen({
                    description: translate("splash.token_expired"),
                    isError: false,
                    isExpired: true
                }));
            }
            else {
                dispatch(stop_check_authen({
                    description: msgError,
                    isError: true,
                    isExpired: false
                }));
            }
        })
    }
}

export const callApiRequestTokenFW = function () {
    return async function (dispatch, getState) {
        global.config = {
            AllowTimeCreateSO: "-1",
            SERVICESPRINTNEW_STORE: "0"
        };
        const userInfo = await storageHelper.getDefaultUserInfo();
        try {
            const body = {
                "User": userInfo.userName,
                "DeviceToken": userInfo.deviceTokenOffline
            }
            dispatch(start_check_authen());
            const response = await apiBaseFW(API_AUTHEN_FW_OFFLINE, METHOD.POST, body,
                { setTimeOut: 3000 }
            );
            console.log("callApiRequestTokenFW success", response);
            dispatch(setDataAuthenFW(userInfo));
        } catch (error) {
            console.log("callApiRequestTokenFW error", error);
            if (OFFLINE_AUTHEN.has(`${userInfo.userName}`)) {
                dispatch(setDataAuthenFW(userInfo));
            }
            else {
                dispatch(stop_check_authen({
                    description: `Chức năng bán hàng OFFLINE đã được kích hoạt: ${error.msgError}`,
                    isError: true,
                    isExpired: false
                }));
            }
        }
    }
}

const checkAuthenInfo = (data) => {
    return function (dispatch, getState) {
        const {
            locationInfo,
            setting,
            userInfo
        } = data;
        const isValidLocalInfo = !helper.IsEmptyObject(locationInfo);
        const isValidSetting = !helper.IsEmptyObject(setting);
        const isValidUser = !helper.IsEmptyObject(userInfo);
        if (!isValidUser) {
            dispatch(stop_check_authen({
                description: translate("splash.cannot_information_work"),
                isError: true,
                isExpired: false
            }));
            return;
        }
        // Check store permission
        if (!isValidLocalInfo) {
            dispatch(stop_check_authen({
                description: translate("splash.cannot_information_warehouse"),
                isError: true,
                isExpired: false
            }));
            return;
        }
        else {
            const {
                userName,
                fullName
            } = userInfo;
            const {
                hasRight,
                isCanOutOrder,
                isDefaultStore,
                storeID
            } = locationInfo;
            const hasDefault = (storeID > 0);
            const isValidStore = (isDefaultStore && hasDefault);
            const hasSale = (hasRight && isCanOutOrder);
            if (!hasDefault) {
                dispatch(stop_check_authen({
                    description: `[${userName} - ${fullName}] ${translate("login.declare_default_store")}`,
                    isError: false,
                    isExpired: true
                }));
                return;
            }
            if (!isValidStore) {
                dispatch(stop_check_authen({
                    description: `[${userName} - ${fullName}] ${translate("splash.impormation_supermarket_defaut")} ${translate("splash.info_change")}`,
                    isError: false,
                    isExpired: true
                }));
                return;
            }
            if (!hasSale) {
                dispatch(stop_check_authen({
                    description: `[${userName} - ${fullName}] ${translate("login.buy_store_default")}`,
                    isError: true,
                    isExpired: false
                }));
                return;
            }
        }
        const { settingValue } = setting;
        const isOldVersion = (settingValue > DEVICE.version);
        if (isOldVersion) {
            const content = `${translate("login.version")} “${DEVICE.version}” ${translate("login.version_old_cus")} “${settingValue}” ${translate("login.use_app")}`;
            Alert.alert("", content, [{
                text: "OK",
                onPress: () => {
                    const params = {
                        action: 'update',
                        bundleID: DEVICE.bundleId,
                        device: DEVICE.uniqueId,
                        opaque: helper.uuidv4()
                    };
                    const query = getQueryString(params);
                    const url = `xmanager://xmanager?${query}`;
                    helper.openURL(url)
                }
            }]);
            return;
        }
        // const isCTV = userInfo?.bcnbDepartmentID == 1581;
        // if (isCTV) {
        //     const content = `[CTV] Phiên bản của app hiện tại đã cũ. Vui lòng cập nhật phiên bản mới nhất`;
        //     Alert.alert("", content, [{
        //         text: "OK",
        //         onPress: () => {
        //             const params = {
        //                 action: 'update',
        //                 bundleID: DEVICE.bundleId,
        //                 device: DEVICE.uniqueId,
        //                 opaque: helper.uuidv4()
        //             };
        //             const query = getQueryString(params);
        //             const url = `xmanager://xmanager?${query}`;
        //             helper.openURL(url)
        //         }
        //     }]);
        //     return;
        // }
        dispatch(setDataAuthen(data));
    }
}

const setDataAuthen = ({
    couchSecret,
    couchURI,
    couchUser,
    locationInfo,
    userInfo,
    deviceToken
}) => {
    return function (dispatch, getState) {
        const data = {
            brandID: locationInfo.brandID,
            companyID: locationInfo.companyID,
            provinceID: locationInfo.provinceID,
            storeAddress: locationInfo.storeAddress,
            storeName: locationInfo.storeName,
            storeShortName: locationInfo.storeShortName,
            fullName: userInfo.fullName,
            positionName: userInfo.positionName,
            mobi: userInfo.mobi,
            departmentName: userInfo.departmentName,
            areaID: locationInfo.areaID,
            storeID: locationInfo.storeID,
            userName: userInfo.userName,
            permissions: locationInfo.permissions,
            currency: locationInfo.currencyUnitSymbol,
            bcnbAreaID: locationInfo.bcnbAreaId,
            languageID: global.languageID,
            deviceTokenOffline: DEVICE.uniqueId,
            isDefaultStore: locationInfo.isDefaultStore,
            storeGroupID: locationInfo.storeGroupID,
            moduleID: 1,
            isShowWeb: locationInfo.isShowWeb
        }
        global.currency = locationInfo.currencyUnitSymbol || "đ";
        global.isVN = (locationInfo.companyID != 6);
        global.loginStoreID = locationInfo.storeID;
        global.isBHX = locationInfo.companyID == COMPANY_ID.BHX
        const arrKeyValue = [
            [STORAGE_CONST.COUCH_SECRET, couchSecret],
            [STORAGE_CONST.COUCH_URI, couchURI],
            [STORAGE_CONST.COUCH_USER, couchUser],
            [STORAGE_CONST.USER_NAME, userInfo.userName],
            [STORAGE_CONST.DEFAULT_USER_INFO, JSON.stringify(data)],
        ];
        storageHelper.multiSet(arrKeyValue).then(result => {
            console.log("storageHelper.multiSet", result);
            dispatch(set_user_info(data));
            dispatch(stop_check_authen({
                isFetching: false,
                description: "",
                isError: false,
                isExpired: false
            }));
        })
    }
}

const setDataAuthenFW = (data) => {
    return function (dispatch, getState) {
        global.currency = data.currency || "đ";
        global.isVN = (data.companyID != 6);
        dispatch(set_user_info(data));
        dispatch(stop_check_authen({
            isFetching: false,
            description: "",
            isError: false,
            isExpired: false
        }));
    }
}

const start_check_authen = () => {
    return ({
        type: START_CHECK_AUTHEN
    });
}

const stop_check_authen = ({
    description,
    isError,
    isExpired
}) => {
    return {
        type: STOP_CHECK_AUTHEN,
        description: description,
        isError: isError,
        isExpired: isExpired
    };
}

const set_user_info = (data) => {
    return ({
        type: "SET_USER_INFO",
        data
    });
}

const OFFLINE_AUTHEN = new Set(["85790", "65482", "103174", "85705", "76921", "23495"]);