import React, { useState, useEffect } from "react";
import {
  View,
  StyleSheet,
  TouchableOpacity,
  TouchableHighlight,
  Alert,
} from "react-native";
import { MyText, Icon } from "@components";
import { Color, helper } from "@common";
import { translate } from '@translate';
import { ENUM } from '@constants'

import Tooltip from "react-native-walkthrough-tooltip";
import { COLORS } from "@styles";

const { TYPE_QR } = ENUM;

function DetailSim({
  so,
  ind,
  connectSIMMobifone,
  detailSimProps: {
    setModalProcessSIMVisible,
    getSimData,
    showIndicator
  },
  userInfo,
  getQrCodeEsim,
  onDelete,
  getQueryStatus
}) {
  // console.log("huy parent", parent)
  const [tipVisible, setTip] = useState(false);
  const goToDetail = (SIMProcessRequestID, ISOUTPRODUCT) => {
    setModalProcessSIMVisible(SIMProcessRequestID)
    getSimData(SIMProcessRequestID, ISOUTPRODUCT);
    //parent.props.actionGetSim.getSIMProcessInfo(dataSim);
  };

  const {
    storeID,
    provinceID
  } = userInfo;

  const MenuOptions = ({ }) => {
    return (
      <View>
        <View style={{ flexDirection: 'row' }}>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              marginBottom: 10,
              flex: 1
            }}
          >
            <TouchableOpacity
              style={[styles.btn]}
              activeOpacity={0.7}
              onPress={() => {
                showIndicator();
                goToDetail(so.SIMPROCESSREQUESTID, so.ISOUTPRODUCT);
              }}
            >
              <Icon
                iconSet="Feather"
                name="external-link"
                style={{ color: "rgb(91,135,103)" }}
              />
              <MyText
                text={translate('common.view_detail')}
                style={[styles.txtBtn, { color: "rgb(91,135,103)" }]}
              />
            </TouchableOpacity>

          </View>
          {
            so.ISALLOWCONNECT &&
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                marginBottom: 10,
              }}
            >
              <TouchableOpacity
                style={[styles.btn]}
                activeOpacity={0.7}
                onPress={() => connectSIMMobifone(so)}
              >
                <Icon
                  iconSet="MaterialCommunityIcons"
                  name="transit-connection-variant"
                  style={{ color: COLORS.bgF49B0C }}
                />
                <MyText
                  text={translate('activeSimManager.btn_connect')}
                  style={{ color: COLORS.bgF49B0C, marginLeft: 5 }}
                />
              </TouchableOpacity>

            </View>
          }
        </View>



        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            flex: 1,
            flexWrap: "wrap"
          }}
        >
          {so.ISGETQRESIMACIVE && <View style={{
            flexDirection: "row",
            justifyContent: "space-between",
            marginBottom: 10,
          }}>
            <TouchableOpacity
              style={[styles.btn]}
              activeOpacity={0.7}
              onPress={() => getQrCodeEsim(so, TYPE_QR.INFO)}
            >
              <Icon
                iconSet="MaterialCommunityIcons"
                name="qrcode-plus"
                style={{ color: COLORS.bg1E88E5 }}
              />
              <MyText
                text={('Lấy QR Đăng Ký Thông Tin').toUpperCase()}
                style={{ color: COLORS.bg1E88E5, marginLeft: 5 }}
              />
            </TouchableOpacity>
          </View>}

          {
            so.ISGETQRESIM && <View style={{
              flexDirection: "row",
              justifyContent: "space-between",
              marginBottom: 10,
            }}>
              <TouchableOpacity
                style={[styles.btn]}
                activeOpacity={0.7}
                onPress={() => getQrCodeEsim(so, TYPE_QR.ESIM)}
              >
                <Icon
                  iconSet="MaterialCommunityIcons"
                  name="qrcode-plus"
                  style={{ color: COLORS.bg1E88E5 }}
                />
                <MyText
                  text={('Lấy QR ESIM').toUpperCase()}
                  style={{ color: COLORS.bg1E88E5, marginLeft: 5 }}
                />
              </TouchableOpacity>
            </View>
          }
        </View>
      </View>
    );
  };
  return (

    <View
      style={{
        padding: 10,
        backgroundColor: ind % 2 == 0 ? Color.white : "rgb(245,245,245)",
      }}
      activeOpacity={0.7}
    >

      <View style={styles.fieldSet}>
        <MyText
          text={so.SIMPROCESSREQUESTID}
          children={
            <MyText
              style={{ color: "red" }}
              text={" - " + so.STATUSCONNECTED + " - " + so.BRANDNAME}
            />
          }
          style={{ color: "#a2a4a6" }}
        />
      </View>
      <View style={styles.fieldSet}>
        <MyText
          text={"Trạng thái đấu nối: "}
          children={<MyText text={so.SIMRQSTATUSNAME} />}
          style={{ color: "#a2a4a6" }}
        />
        {
          so.ISALLOWQUERYSTATUS &&
          <View
            style={{
              flexDirection: "row",
              justifyContent: 'center',
              marginTop: -4,
              backgroundColor: 'white',
              borderRadius: 25 / 2,
              alignItems: 'center',
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              width: 25,
              height: 25,
              shadowOpacity: 0.25,
              shadowRadius: 4,
              elevation: 5,
              marginRight: 148,
            }}
          >
            <TouchableOpacity
              style={{
                borderWidth: 0.5,
                borderColor: COLORS.bgF0F0F0,
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: 25 / 2,

              }}
              activeOpacity={0.7}
              onPress={() => getQueryStatus(so)}
            >
              <Icon
                iconSet="Ionicons"
                name="reload-circle-sharp"
                style={{ color: COLORS.bgF49B0C, marginTop: -1.2 }}
                size={26}
              />
            </TouchableOpacity>
          </View>
        }
      </View>
      <View style={styles.fieldSet}>
        <MyText
          text={translate('activeSimManager.order_status')}
          children={
            <MyText
              style={{ color: "blue" }}
              text={" - " + (so.ISOUTPRODUCT ? translate('activeSimManager.exported') : translate('activeSimManager.not_exported'))}
            />
          }
          style={{ color: "#a2a4a6" }}
        />
      </View>
      <View style={styles.fieldSet}>
        <MyText
          text={translate('common.customer')}
          children={<MyText text={so.CUSTOMERNAME} />}
          style={{ color: "#a2a4a6" }}
        />
      </View>
      <View style={styles.fieldSet}>
        <MyText
          text={translate('common.phone_number')}
          children={<MyText text={so.CUSTOMERPHONE} />}
          style={{ color: "#a2a4a6" }}
        />
      </View>
      <View style={styles.fieldSet}>
        <MyText
          text={translate('activeSimManager.imei')}
          children={<MyText text={so.IMEI} />}
          style={{ color: "#a2a4a6" }}
        />
      </View>
      <View style={styles.fieldSet}>
        <MyText
          text={translate('activeSimManager.package')}
          children={<MyText text={so.PACKAGENAME} />}
          style={{ color: "#a2a4a6" }}
        />
      </View>
      {
        so.ISALLOWDELETE == true &&
        <ButtonDelete
          isVisible={true}
          onDelete={() => onDelete(so)}
          appName={"POS"}
        />
      }
      <MenuOptions />

      <TouchableHighlight
        style={[
          styles.wrapCreateBy,
          { top: !so.ISOUTPRODUCT && !so.ISDELETED ? 35 : 10 },
        ]}
        onPress={() => {
          setTip(true);
        }}
        underlayColor="transparent"
      >
        <Tooltip
          isVisible={tipVisible}
          tooltipStyle={{
            shadowColor: Color.backgroundHeader,
            shadowOpacity: 0,
            // marginLeft: 10,
          }}
          contentStyle={{
            backgroundColor: Color.backgroundHeader,
            // left: 125,
          }}
          supportedOrientations={["portrait"]}
          content={
            <MyText
              style={{
                color: Color.white,
              }}
              text={translate('activeSimManager.source')}
            />
          }
          placement="left"
          onClose={() => setTip(false)}
        >
          <MyText
            text={so.CREATEDBYOTHERAPPS}
            style={{
              fontSize: 12,
              color: Color.white,
              fontWeight: "bold",
            }}
          />
        </Tooltip>
      </TouchableHighlight>


    </View>

  );
}

const ButtonDelete = ({ onDelete, appName, isVisible }) => {
  return (
    <View style={{
      position: "absolute",
      top: 0,
      right: 0,
      alignItems: "flex-end"
    }}>
      {
        isVisible &&
        <TouchableOpacity style={{
          padding: 6,
          flexDirection: "row",
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: COLORS.btnDCE1E1,
          borderBottomStartRadius: 14,
        }}
          activeOpacity={0.6}
          onPress={onDelete}
        >
          <Icon
            iconSet={"Ionicons"}
            name={"trash"}
            color={COLORS.icFF0000}
            size={14}
          />
          <MyText
            text={translate('common.btn_cancel')}
            addSize={-1.5}
            style={{
              color: COLORS.txtFF0000
            }}
          />
        </TouchableOpacity>
      }
    </View>
  );
}

const styles = StyleSheet.create({
  type: {
    fontWeight: "normal",
    overflow: "hidden",
    paddingVertical: 2,
    paddingHorizontal: 10,
    borderRadius: 10,
    fontSize: 13,
  },
  fieldSet: {
    flexDirection: "row",
    marginVertical: 5,
    justifyContent: "space-between",
  },
  btn: {
    flexDirection: "row",
    alignItems: "center",
  },
  txtBtn: {
    paddingLeft: 2,
    fontSize: 12,
  },
  btnRePrint: {
    flexDirection: "row",
    // backgroundColor: "rgb(44,139,215)",
    alignItems: "center",
  },
  btnDel: {
    height: 30,
    width: 50,
    position: "absolute",
    top: 0,
    right: 0,
    backgroundColor: Color.border,
    alignItems: "center",
    borderBottomStartRadius: 10,
    flexDirection: "row",
    justifyContent: "center",
    // opacity: 0.5,
  },
  wrapCreateBy: {
    marginRight: 10,
    padding: 5,
    backgroundColor: "rgb(44,139,215)",
    // borderWidth: 2,
    borderRadius: 5,
    position: "absolute",
    right: 0,
  },
});
export default DetailSim;
