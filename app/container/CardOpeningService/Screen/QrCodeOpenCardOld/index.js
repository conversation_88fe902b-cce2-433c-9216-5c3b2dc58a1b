import {
  Image,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import React from "react";
import { COLORS } from "@styles";
import { constants } from "@constants";
import { connect } from "react-redux";
import { MyText } from "@components";
import FastImage from "react-native-fast-image";
import QRCode from "react-native-qrcode-svg";

const QrCodeOpenCardOld = ({ updateHeaderAirtime, navigation, userInfo }) => {
  const airTimeTransactionTypeName =
    updateHeaderAirtime?.AirTimeTransactionTypeName ?? "";
  const logo = updateHeaderAirtime?.AirtimeParams?.LOGO ?? "";
  const link = updateHeaderAirtime?.AirtimeParams?.LINK ?? "";
  const qrCode = updateHeaderAirtime?.AirtimeParams?.QRCODE ?? "";
  const airTimeTransactionTypeID =
    updateHeaderAirtime?.AirTimeTransactionTypeID;

  const getDescription = (airTimeTransactionTypeID) => {
    switch (airTimeTransactionTypeID) {
      case 2012:
        return "Vui lòng hướng dẫn khách hàng dùng Camera thường của điện thoại quét mã QRcode để cài app và đăng ký mở thẻ, nếu thao tác lỗi thì quét lại QRcode này.";
      case 1992:
        return "Nhân viên siêu thị vui lòng hướng dẫn Khách hàng quét mã QRCode để cài app và thực hiện đăng ký mở thẻ.";
      case 2072:
        return "Nhân viên hướng dẫn KH quét mã QRcode để mở thẻ và nhập mã giới thiệu [MWG+user] để ghi nhận thưởng.";
      case 2073:
        return "Nhân viên hướng dẫn KH quét mã QRcode để mở tài khoản thanh toán và nhập mã giới thiệu [MWG+user] để ghi nhận thưởng.";
      case 2172:
        return "Nhân viên hướng dẫn KH quét mã QRcode để mở tài khoản thanh toán và nhập mã giới thiệu [MWG+user] để ghi nhận thưởng."
      default:
        return "Nhân viên siêu thị vui lòng hướng dẫn Khách hàng quét mã QRCode để cài app và thực hiện đăng ký mở thẻ.";
    }
  };

  const description = getDescription(airTimeTransactionTypeID);

  return (
    <View
      style={{
        flex: 1,
      }}
    >
      <Header title={airTimeTransactionTypeName} />
      <ScrollView
        contentContainerStyle={{
          flexGrow: 1,
          paddingVertical: 10,
        }}
      >
        <SafeAreaView
          style={{
            flex: 1,
            backgroundColor: COLORS.bgFAFAFA,
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <FastImage
            style={{ width: 290, height: 90, marginLeft: 5 }}
            source={{
              uri: logo,
              priority: FastImage.priority.normal,
            }}
            resizeMode={FastImage.resizeMode.contain}
          />
          <MyText
            text={"Vui lòng quét mã QRCode"}
            addSize={1}
            style={{
              color: COLORS.txt147EFB,
              marginBottom: 20,
              textAlign: "center",
              marginTop: 8,
              width: constants.width - 40,
              fontStyle: "italic",
            }}
          />
          {link.trim() && <QRCode size={180} value={link} />}
          {qrCode.trim() && (
            <Image
              source={{ uri: qrCode }}
              style={{
                height: constants.getSize(200),
                width: constants.getSize(200),
              }}
            />
          )}
          <View
            style={{
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              style={{
                backgroundColor: "pink",
                width: 150,
                height: 50,
                borderRadius: 18,
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: COLORS.bg147EFB,
                marginTop: 30,
              }}
            >
              <MyText
                text={"QUAY LẠI"}
                style={{
                  fontWeight: "bold",
                  color: COLORS.bgFFFFFF,
                }}
              />
            </TouchableOpacity>
            <Note titleNote={`${description})`} />
          </View>
        </SafeAreaView>
      </ScrollView>
    </View>
  );
};

const mapStateToProps = function (state) {
  return {
    updateHeaderAirtime: state.cardOpeningServiceReducer.updateHeaderAirtime,
    userInfo: state.userReducer,
  };
};

const mapDispatchToProps = function (dispatch) {
  return {};
};

export default connect(mapStateToProps, mapDispatchToProps)(QrCodeOpenCardOld);

const styles = StyleSheet.create({});

const Header = ({ title }) => {
  return (
    <View
      style={{
        width: constants.width,
        height: 50,
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: COLORS.bg2FB47C,
      }}
    >
      <MyText
        text={title}
        addSize={2}
        style={{
          fontWeight: "bold",
          color: COLORS.txtFFF6AD,
          textAlign: "center",
        }}
      />
    </View>
  );
};

const Note = ({ titleNote }) => {
  return (
    <View
      style={{
        flexDirection: "row",
        width: "90%",
        justifyContent: "center",
        alignItems: "center",
        marginTop: 20,
      }}
    >
      <MyText
        text={"("}
        addSize={-1.5}
        style={{
          color: COLORS.txt333333,
          textAlign: "center",
          fontStyle: "italic",
        }}
      >
        <MyText
          text={"Lưu ý: "}
          addSize={-1.5}
          style={{
            color: COLORS.txt147EFB,
            textAlign: "center",
            fontStyle: "normal",
            fontWeight: "bold",
          }}
        />
        <MyText
          text={titleNote}
          addSize={-1.5}
          style={{
            color: COLORS.txt333333,
            textAlign: "center",
            fontStyle: "italic",
            width: constants.width - 10,
          }}
        />
      </MyText>
    </View>
  );
};
