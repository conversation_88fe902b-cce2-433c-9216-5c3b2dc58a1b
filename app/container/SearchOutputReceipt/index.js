import { View, TouchableOpacity, SafeAreaView, FlatList, StyleSheet } from 'react-native';
import { useSelector } from 'react-redux';
import React, { useMemo, useState } from 'react';
import { COLORS } from '@styles';
import { Icon, BaseLoading, MyText, Button } from '@components';
import moment from 'moment';
import SearchBar from './components/SearchBar';
import InfoOVReceipt from './components/InfoOVReceipt';
import { searchOutputReceipt } from './action';
import CheckFilter from './components/CheckFilter';
import CalendarPicker from 'react-native-calendar-picker';
import { constants } from '@constants';
import Toast, { BaseToast, ErrorToast } from 'react-native-toast-message';

const SearchOutputReceipt = () => {
    const TOAST_CONFIG = {
        success: (props) => (
            <BaseToast {...props} text1NumberOfLines={2} text2NumberOfLines={2} />
        ),
        error: (props) => (
            <ErrorToast {...props} text1NumberOfLines={2} text2NumberOfLines={2} />
        )
    };
    const WEEKDAYS = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];
    const MONTHS = [
        'Tháng 1',
        'Tháng 2',
        'Tháng 3',
        'Tháng 4',
        'Tháng 5',
        'Tháng 6',
        'Tháng 7',
        'Tháng 8',
        'Tháng 9',
        'Tháng 10',
        'Tháng 11',
        'Tháng 12'
    ];
    const dateNow = new Date().setHours(23, 59, 59, 59);
    const [stateSearch, setStateSearch] = useState({
        isFetching: false,
        isError: false,
        description: '',
        isEmpty: false,
        data: []
    });
    const [filter, setFilter] = useState({
        keyword: '',
        fromDate: new Date(),
        toDate: new Date(),
        isCreateUser: 1
    });
    const [isShowDatePicker, setIsShowDatePicker] = useState(false);
    const {
        storeID: loginStoreId,
        moduleID,
        languageID
    } = useSelector((state) => state.userReducer);

    const txtStartDate = useMemo(
        () => moment(filter.fromDate).format('DD/MM/YYYY'),
        [filter]
    );
    const txtEndDate = useMemo(
        () => moment(filter.toDate).format('DD/MM/YYYY'),
        [filter]
    );

    const handleOnSubmitDate = () => {
        const dayDifference = moment(filter.toDate).diff(moment(filter.fromDate), 'days');
        if (dayDifference > 2) {
            Toast.show({
                type: 'error',
                text1: 'Ngày bắt đầu và ngày kết thúc chỉ được cách nhau tối đa 3 ngày!'
            });
        } else if (
            filter.toDate != null &&
            (moment(filter.fromDate).month() !== moment(filter.toDate).month() ||
                moment(filter.fromDate).year() !== moment(filter.toDate).year())
        ) {
            Toast.show({
                type: 'error',
                text1: 'Ngày bắt đầu và ngày kết thúc phải trong cùng một tháng.'
            });
        } else {
            setIsShowDatePicker(false);
            handleOnSearch({
                ...filter,
                fromDate: filter.fromDate,
                toDate: filter.toDate ?? filter.fromDate,
                isCreateUser: filter.isCreateUser
            });
        }
    };

    const onDateChange = (date, type) => {
        if (type === 'END_DATE') {
            setFilter((prev) => ({
                ...prev,
                toDate: date
            }));
        } else {
            setFilter((prev) => ({
                ...prev,
                fromDate: date,
                isCreateUser: prev.isCreateUser
            }));
        }
    };


    const handleOnSearch = (filter) => {
        setStateSearch({
            isFetching: true,
            isError: false,
            description: '',
            isEmpty: false,
            data: []
        });
        const body = {
            loginStoreId,
            languageID,
            moduleID,
            saleScenarioTypeID: 0,
            SaleOrderID: filter.keyword,
            fromDate: moment(filter.fromDate).format('YYYY-MM-DD'),
            toDate: moment(filter.toDate).format('YYYY-MM-DD'),
            IsCreateUser: filter.isCreateUser
        };
        searchOutputReceipt(body)
            .then((response) => {
                if (response.length > 0) {
                    setStateSearch({
                        isFetching: false,
                        isError: false,
                        description: '',
                        isEmpty: false,
                        data: response
                    });
                } else {
                    setStateSearch({
                        isFetching: false,
                        isError: false,
                        description:
                            'Không tìm thấy thông tin phiếu biên nhận thẻ cào',
                        isEmpty: true,
                        data: []
                    });
                }
            })
            .catch((error) => {
                setStateSearch({
                    isFetching: false,
                    isError: true,
                    description: error,
                    isEmpty: true,
                    data: []
                });
            });
    };

    return (
        <SafeAreaView style={{ flex: 1, backgroundColor: COLORS.bgFFFFFF }}>
            <CheckFilter
                createUser={filter.isCreateUser}
                onChangeParam={(value) => {
                    setFilter((prev) => ({
                        ...prev,
                        isCreateUser: value
                    }));
                    handleOnSearch({
                        ...filter,
                        isCreateUser: value
                    });
                }}
            />
            <View style={{ flexDirection: 'row', marginHorizontal: 10 }}>
                <SearchBar
                    value={filter.keyword}
                    onChangeText={(value) => {
                        setFilter((prev) => ({
                            ...prev,
                            keyword: value
                        }));
                    }}
                    onSearch={() => handleOnSearch({
                        ...filter,
                        keyword: filter.keyword
                    })}
                    disabled={stateSearch.isFetching}
                />
            </View>
            <SafeAreaView
                style={{}}>
                <TouchableOpacity
                    onPress={() => setIsShowDatePicker(true)}
                    style={{
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignItems: 'center',
                        borderWidth: 1,
                        padding: 5,
                        borderRadius: 5,
                        borderColor: COLORS.ic2C8BD7,
                        marginHorizontal: 20,
                    }}>
                    <MyText
                        text={`${txtStartDate} - ${txtEndDate}`}
                        style={{
                            marginLeft: 10,
                            flex: 1
                        }}
                    />
                    <Icon
                        iconSet="Ionicons"
                        name="calendar-sharp"
                        style={{
                            fontSize: 20,
                            color: COLORS.ic2C8BD7
                        }}
                    />
                </TouchableOpacity>
            </SafeAreaView>
            <BaseLoading
                isLoading={stateSearch.isFetching}
                isError={stateSearch.isError}
                isEmpty={stateSearch.isEmpty}
                textLoadingError={stateSearch.description}
                onPressTryAgains={handleOnSearch}
                content={
                    <View style={{
                        marginTop: 5
                    }}>
                        <FlatList
                            data={stateSearch.data}
                            renderItem={({ item }) => (
                                <InfoOVReceipt data={item} />
                            )}
                        />
                    </View>
                }
            />
            {isShowDatePicker && (
                <View
                    style={{
                        width: constants.width,
                        height: constants.height,
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        justifyContent: 'center',
                        alignItems: 'center',
                        backgroundColor: COLORS.bg0000005
                    }}>
                    <View style={{ backgroundColor: COLORS.bgFFFFFF }}>
                        <CalendarPicker
                            weekdays={WEEKDAYS}
                            maxDate={dateNow}
                            months={MONTHS}
                            allowRangeSelection
                            selectedDayColor="#98F1F3"
                            allowBackwardRangeSelect
                            restrictMonthNavigation
                            disabledDates={(date) => date > dateNow}
                            onDateChange={onDateChange}
                        />

                        <View
                            style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'center'
                            }}>
                            <Button
                                text="Đóng"
                                styleContainer={[
                                    styles.button,
                                    {
                                        backgroundColor: COLORS.bgFF0000,
                                        marginRight: 10
                                    }
                                ]}
                                styleText={{
                                    color: COLORS.txtFFFFFF,
                                    fontSize: 14
                                }}
                                onPress={() => {
                                    setIsShowDatePicker(false);
                                }}
                            />
                            <Button
                                text="Xác nhận"
                                styleContainer={styles.button}
                                styleText={{
                                    color: COLORS.txtFFFFFF,
                                    fontSize: 14
                                }}
                                onPress={() => handleOnSubmitDate(filter)}
                            />
                        </View>
                        <Toast
                            config={TOAST_CONFIG}
                            position="bottom"
                            visibilityTime={2000}
                        />
                    </View>

                </View>
            )}
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    button: {
        alignSelf: 'center',
        backgroundColor: COLORS.bg147EFB,
        borderRadius: constants.getSize(4),
        height: constants.getSize(40),
        marginVertical: 10,
        width: constants.getSize(80)
    }
});
export default SearchOutputReceipt;
