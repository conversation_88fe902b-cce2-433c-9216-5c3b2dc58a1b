/* eslint-disable no-await-in-loop */
import { <PERSON><PERSON>reaView, Alert, View } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import React from 'react';
import { translate } from '@translate';
import { constants } from '@constants';
import { helper, convertHtml2Image, printSocket } from '@common';
import { showBlockUI, hideBlockUI, Button } from '@components';
import { COLORS } from '@styles';
import PrintReport from '../../SaleOrderPayment/component/PrintReport';
import Report from '../../SaleOrderPayment/component/PrintReport/Report';
import { getReportContentBase64New } from '../../SaleOrderPayment/action';

import {
    getReportPrinterSocket
} from '../../SaleOrderPayment/action';

const SO_TYPE_OFFLINE = 100;

const ReprintCard = ({ navigation, route }) => {
    const dispatch = useDispatch();
    const { saleOrderID: saleOrderID } = route.params;
    const statePrinter = useSelector(
        (state) => state.saleOrderPaymentReducer.statePrinter
    );
    const printerRetail = useSelector(
        (state) => state.saleOrderPaymentReducer.printerRetail
    );
    const { storeID, languageID, moduleID } = useSelector(
        (state) => state.userReducer
    );

    const [reportRetail, setReportRetail] = React.useState({});

    const handleGetReportPrinter = () => {
        dispatch(getReportPrinterSocket(SO_TYPE_OFFLINE));
    };

    const handlePrint = () => {
        if (helper.IsEmptyObject(reportRetail)) {
            Alert.alert(
                '',
                translate('saleOrderPayment.please_choose_printer')
            );
        } else {
            const data = {
                loginStoreId: storeID,
                languageID,
                moduleID,
                reportContents: [{ ReportContent: 'OutputReceiptContent', NumberOfCopy: 1 }],
                saleOrderID
            };
            showBlockUI();
            data.isFitContent = true;
            data.isGetContentHTML = true;
            getReceiptContentHtml(data);
        }
    };

    const getReceiptContentHtml = (info) => {
        showBlockUI();
        dispatch(getReportContentBase64New(info)).then(data => {
            onConvertHTML(data, info.reportContents);
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError, [
                {
                    text: translate('saleOrderPayment.btn_skip_uppercase'),
                    style: "default",
                    onPress: hideBlockUI
                },
                {
                    text: translate('saleOrderPayment.btn_retry_uppercase'),
                    style: "default",
                    onPress: () => getReceiptContentHtml(info)
                }
            ]);
        })
    }

    const requestConvertHtml = async (data, reportContents) => {
        try {
            const requestConvert = [];
            const { OutputReceiptContentHTML } = data;
            for (const ele of reportContents) {
                const { ReportContent } = ele;
                switch (ReportContent) {
                    case 'OutputReceiptContent':
                        if (helper.IsNonEmptyString(OutputReceiptContentHTML)) {
                            const receiptContent = await convertHtml2Image(OutputReceiptContentHTML, constants.H_KEY);
                            requestConvert.push([receiptContent]);
                        }
                        break;
                    default:
                        console.log(ele);
                        break;
                }
            }
            return requestConvert;
        } catch (error) {
            console.log('convertHtml2Image', error);
            Alert.alert("", 'Quá trình in lỗi. Vui lòng vào chức năng "In lại" để thao tác tiếp.', [
                {
                    text: "OK",
                    style: "default",
                    onPress: hideBlockUI
                }
            ]);
        }
    }

    const onConvertHTML = async (data, reportContents) => {
        const dataBit = await requestConvertHtml(data, reportContents);
        if (helper.IsNonEmptyArray(dataBit)) {
            onPrintBillHTML(data, reportContents, dataBit);
        }
        else {
            hideBlockUI();
        }
    }

    const onPrintBillHTML = (data, reportContents, dataBit) => {
        const requestAPI = getPrintHTMLRequestAPI(data, reportContents, dataBit);
        if (helper.IsNonEmptyArray(requestAPI)) {
            printAllRequestSocket(requestAPI);
        }
        else {
            hideBlockUI();
        }
    }

    const printAllRequestSocket = async (allPromise) => {
        try {
            for (const { data, ip, delay } of allPromise) {
                await printSocket(data, ip);
                if (delay > 0) {
                    await helper.sleep(delay);
                }
            }
            Alert.alert("", translate('saleOrderPayment.print_successfully'), [
                {
                    text: "OK",
                    style: "default",
                    onPress: goBack
                }
            ]);
        } catch (msgError) {
            Alert.alert(translate('common.notification'), msgError, [
                {
                    text: "OK",
                    style: "default",
                    onPress: hideBlockUI
                }
            ]);
        }
    }

    const getPrintHTMLRequestAPI = (data, reportContents, dataBit) => {
        const requestAPI = [];
        const { outputReceiptContentPrinterTypeID } = data;
        reportContents.forEach((ele, index) => {
            const { ReportContent, NumberOfCopy } = ele;
            const dataConvert = dataBit[index];
            let report = {};
            switch (ReportContent) {
                case 'OutputReceiptContent':
                    report = getReport(outputReceiptContentPrinterTypeID);
                    break;
                default:
                    report = reportRetail;
                    break;
            }
            if (helper.IsNonEmptyArray(dataConvert)) {
                for (let i = 0; i < NumberOfCopy; i++) {
                    dataConvert.forEach(info => {
                        if (!report.IPPRINTER) {
                            report.IPPRINTER = "*************";
                            report.DELAY = 500;
                        }
                        const printService = getPrintServiceSocket(report, info, ReportContent);
                        requestAPI.push(printService);
                    })
                }
            }
        })
        console.log('onPrintBillHTML IsNonEmptyArray', requestAPI);
        return requestAPI;
    }

    const getReport = (type) => {
        switch (type) {
            case 'InvoiceRetailPrinter':
                return reportRetail;
            case 'VATContentPrint':
                return null;
            default:
                // CommonPrinter
                return null;
        }
    };

    const getPrintServiceSocket = (report, info) => {
        let body = {
            "ip": report.IPPRINTER,
            "delay": report.DELAY,
            "data": info
        }
        return body;
    }

    const goBack = () => {
        hideBlockUI();
        navigation.goBack();
    };

    React.useEffect(() => {
        handleGetReportPrinter();
    }, []);

    return (
        <SafeAreaView
            style={{
                flex: 1,
                backgroundColor: COLORS.bgF5F5F5
            }}>
            <View style={{ height: '30%' }}>
                <PrintReport
                    title={translate('saleOrderManager.select_printer')}
                    statePrinter={statePrinter}
                    onTryAgains={handleGetReportPrinter}
                    dataRetail={printerRetail}
                    renderItemRetail={({ item }) => (
                        <Report
                            key="ReportRetail"
                            info={item}
                            report={reportRetail}
                            onCheck={() => {
                                setReportRetail(item);
                            }}
                        />
                    )}
                />
            </View>
            <View style={{ justifyContent: 'center', alignItems: 'center' }}>
                <Button
                    text="TIẾP TỤC IN"
                    onPress={handlePrint}
                    styleContainer={{
                        borderRadius: 5,
                        height: 40,
                        backgroundColor: COLORS.bg147EFB,
                        width: '40%'
                    }}
                    styleText={{
                        color: COLORS.bgFFFFFF,
                        fontSize: 14,
                        fontWeight: 'bold'
                    }}
                />
            </View>
        </SafeAreaView>
    );
};

export default ReprintCard;
