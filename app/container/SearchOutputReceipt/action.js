import { apiBase, METHOD } from '@config';
import { API_CONST } from '@constants';
import { helper } from '@common';
import { translate } from '@translate';

const { API_GET_SALE_ORDER_OUT_TRANSACTION, API_REPRINT_SALEORDER } = API_CONST;

export const searchOutputReceipt = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_GET_SALE_ORDER_OUT_TRANSACTION, METHOD.POST, body)
            .then((res) => {
                console.log('searchOutputReceipt success', res.object);
                if (
                    helper.hasProperty(res, 'object') &&
                    helper.isArray(res.object) &&
                    res.object.length > 0
                ) {
                    resolve(res.object);
                } else {
                    resolve([]);
                }
            })
            .catch((err) => {
                console.log('searchOutputReceipt error', err);
                reject(err.msgError);
            });
    });
};

export const getReportContentBase64 = function (body) {
    return new Promise((resolve, reject) => {
        apiBase(API_REPRINT_SALEORDER, METHOD.POST, body)
            .then((response) => {
                console.log('getReportContentBase64 success', response);
                const { object } = response;
                if (helper.hasProperty(object, 'data')) {
                    const { data } = object;
                    if (helper.IsEmptyObject(data)) {
                        reject(
                            translate('saleOrderPayment.cannot_content_printer')
                        );
                    } else {
                        resolve(data);
                    }
                } else {
                    reject(
                        translate('saleOrderPayment.cannot_content_printer')
                    );
                }
            })
            .catch((error) => {
                console.log('getReportContentBase64 error', error);
                reject(error.msgError);
            });
    });
};
