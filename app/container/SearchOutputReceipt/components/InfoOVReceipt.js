import React from 'react';
import { useNavigation } from '@react-navigation/core';
import { TouchableOpacity, View } from 'react-native';
import { COLORS } from '@styles';
import { MyText, Icon } from '@components';
import { translate } from '@translate';
import { dateHelper, helper } from '@common';
import NameTag from './NameTag';

const InfoOVReceipt = ({ data }) => {
    const navigation = useNavigation();

    const {
        SALEORDERID,
        CREATEORDERFULLNAME,
        CUSTOMERNAME,
        CREATEBYAPPNAME,
        TOTALAMOUNT,
        CREATEDATE
    } = data;

    return (
        <View
            style={{
                backgroundColor: COLORS.bgFFFFFF,
                padding: 10,
                marginHorizontal: 10,
                marginVertical: 5,
                borderRadius: 5,
                elevation: 5,
                borderColor: COLORS.inversePrimary,
                shadowColor: "#000000",
                shadowOffset: {
                    width: 0,
                    height: 0,
                },
                shadowOpacity: 0.25,
                shadowRadius: 2,
            }}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <MyText
                    text={SALEORDERID}
                    style={{
                        color: COLORS.txt333333,
                        fontWeight: 'bold',
                        flex: 1
                    }}
                />
                {CREATEBYAPPNAME && (
                    <NameTag
                        name={CREATEBYAPPNAME}
                        tooltipText={translate('searchInOutVoucher.source')}
                        backgroundColor={COLORS.bg288AD6}
                    />
                )}
            </View>
            <TextField name="Khách hàng" value={CUSTOMERNAME} />
            <TextField name="Nhân viên" value={CREATEORDERFULLNAME} />
            <MoneyField name="Tổng tiền" value={TOTALAMOUNT} />

            <View
                style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between'
                }}>
                <TextField
                    name="Ngày tạo"
                    value={dateHelper.formatStrDateFULL(CREATEDATE)}
                />
                <View
                    style={{
                        alignSelf: 'flex-end'
                    }}>
                    <IconField
                        title="In lại"
                        name="print-outline"
                        color={COLORS.icC822B0}
                        textAlign="left"
                        onPress={() =>
                            navigation.navigate('ReprintCard', {
                                saleOrderID: SALEORDERID
                            })
                        }
                    />
                </View>
            </View>
        </View>
    );
};

const TextField = ({ name, value }) => {
    return (
        <MyText
            selectable={false}
            text={`${name}: `}
            style={{
                color: COLORS.txt8E8E93,
                marginTop: 4
            }}>
            <MyText
                text={value}
                style={{
                    color: COLORS.txt333333
                }}
            />
        </MyText>
    );
};

const MoneyField = ({ name, value, color = COLORS.bgFF0000 }) => {
    return (
        <MyText
            selectable={false}
            text={`${name}: `}
            style={{
                color: COLORS.txt8E8E93,
                marginTop: 4
            }}>
            <MyText
                text={helper.convertNum(value)}
                style={{
                    color,
                    fontWeight: 'bold'
                }}
            />
        </MyText>
    );
};

const IconField = ({ title, name, color, textAlign, onPress }) => {
    return (
        <TouchableOpacity activeOpacity={0.6} onPress={onPress}>
            <MyText
                selectable={false}
                text={title}
                addSize={-1.5}
                style={{
                    color,
                    textAlign,
                    marginTop: 4
                }}>
                {' '}
                <Icon iconSet="Ionicons" name={name} color={color} size={14} />
            </MyText>
        </TouchableOpacity>
    );
};
export default InfoOVReceipt;
