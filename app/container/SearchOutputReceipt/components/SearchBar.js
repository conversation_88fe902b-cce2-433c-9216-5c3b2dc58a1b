import React from 'react';
import { View, TouchableOpacity, TextInput, Keyboard } from 'react-native';
import { Icon } from '@components';

import { COLORS } from '@styles';

const SearchBar = ({ value, onChangeText, onSearch, disabled }) => {
    return (
        <View
            style={{
                flexDirection: 'row',
                flex: 1,
                marginVertical: 10,
                borderWidth: 1,
                borderColor: COLORS.bdE4E4E4,
                height: 38,
                backgroundColor: COLORS.bgFFFFFF,
                borderRadius: 19,
                justifyContent: 'space-between',
                alignItems: 'center',
                alignSelf: 'center',
                opacity: disabled ? 0.4 : 1
            }}>
            <TextInput
                value={value}
                onChangeText={onChangeText}
                placeholder="Nhập mã SO:"
                placeholderTextColor={COLORS.txt808080}
                onSubmitEditing={() => {
                    Keyboard.dismiss();
                    onSearch();
                }}
                style={{
                    flex: 1,
                    height: 36,
                    paddingLeft: 20,
                    fontSize: 12.5
                }}
                maxLength={20}
                editable={!disabled}
            />
            <TouchableOpacity
                style={{
                    height: 38,
                    width: 48,
                    justifyContent: 'center',
                    alignItems: 'flex-end',
                    paddingRight: 20
                }}
                onPress={onSearch}
                disabled={disabled}>
                <Icon
                    iconSet="Ionicons"
                    name="search"
                    color={COLORS.ic288AD6}
                    size={22}
                />
            </TouchableOpacity>
        </View>
    );
};

export default SearchBar;
