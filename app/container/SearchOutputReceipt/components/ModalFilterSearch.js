import React, { useState, useMemo } from 'react';
import {
    View,
    SafeAreaView,
    TouchableOpacity,
    Platform,
    StyleSheet
} from 'react-native';
import KModal from 'react-native-modal';
import { MyText, Icon, Button } from '@components';
import { constants } from '@constants';
import { COLORS } from '@styles';
import CalendarPicker from 'react-native-calendar-picker';
import moment from 'moment';
import Toast, { BaseToast, ErrorToast } from 'react-native-toast-message';
import CheckFilter from './CheckFilter';

const TOAST_CONFIG = {
    success: (props) => (
        <BaseToast {...props} text1NumberOfLines={2} text2NumberOfLines={2} />
    ),
    error: (props) => (
        <ErrorToast {...props} text1NumberOfLines={2} text2NumberOfLines={2} />
    )
};
const WEEKDAYS = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];
const MONTHS = [
    'Tháng 1',
    'Tháng 2',
    'Tháng 3',
    'Tháng 4',
    'Tháng 5',
    'Tháng 6',
    'Tháng 7',
    'Tháng 8',
    'Tháng 9',
    'Tháng 10',
    'Tháng 11',
    'Tháng 12'
];
const dateNow = new Date().setHours(23, 59, 59, 59);

const ModalFilterSearch = ({
    hideModal,
    isVisible,
    filter,
    setFilter,
    onSearch,
}) => {
    const [isShowDatePicker, setIsShowDatePicker] = useState(false);
    const [tempCreateUser, setTempCreateUser] = useState(0)
    const [tempData, setTempData] = useState({
        fromDate: moment(),
        toDate: moment()
    });
    console.log('tempCreateUser', tempCreateUser)

    const txtStartDate = useMemo(
        () => moment(filter.fromDate).format('DD/MM/YYYY'),
        [filter]
    );
    const txtEndDate = useMemo(
        () => moment(filter.toDate).format('DD/MM/YYYY'),
        [filter]
    );

    const onDateChange = (date, type) => {
        if (type === 'END_DATE') {
            setTempData((prev) => ({
                ...prev,
                toDate: date
            }));
        } else {
            setTempData({
                fromDate: date
            });
        }
    };

    const handleOnSubmitDate = () => {
        const dayDifference = moment(tempData.toDate).diff(moment(tempData.fromDate), 'days');
        if (dayDifference > 2) {
            Toast.show({
                type: 'error',
                text1: 'Ngày bắt đầu và ngày kết thúc chỉ được cách nhau tối đa 3 ngày!'
            });
        }
        else if (
            tempData.toDate != null &&
            (tempData.fromDate.month() !== tempData.toDate.month() ||
                tempData.fromDate.year() !== tempData.toDate.year())
        ) {
            Toast.show({
                type: 'error',
                text1: 'Ngày bắt đầu và ngày kết thúc phải trong cùng một tháng.'
            });
        } else {
            setFilter((prev) => ({
                ...prev,
                fromDate: tempData.fromDate,
                toDate: tempData.toDate ?? tempData.fromDate
            }));
            setIsShowDatePicker(false);
        }
    };

    return (
        <KModal
            isVisible={isVisible}
            style={{
                margin: 0,
                justifyContent: 'space-between',
                flexDirection: 'row'
            }}
            deviceWidth={constants.width / 2}
            deviceHeight={constants.height}
            backdropTransitionOutTiming={0}
            animationIn="slideInRight"
            animationOut="slideOutRight"
            useNativeDriver
            hideModalContentWhileAnimating>
            <TouchableOpacity
                style={{ width: '15%', alignItems: 'flex-start' }}
                activeOpacity={1}
                onPress={hideModal}
            />
            <View
                style={{
                    backgroundColor: COLORS.bgFFFFFF,
                    width: '85%',
                    height: '100%'
                }}>
                <SafeAreaView
                    style={{ marginTop: Platform.OS === 'ios' ? 50 : 20 }}>
                    <MyText
                        style={{
                            marginLeft: 20,
                            fontWeight: 'bold',
                            marginBottom: 5
                        }}
                        text="Chọn khoảng thời gian:"
                    />
                    <TouchableOpacity
                        onPress={() => setIsShowDatePicker(true)}
                        style={{
                            flexDirection: 'row',
                            justifyContent: 'center',
                            alignItems: 'center',
                            borderWidth: 1,
                            padding: 5,
                            borderRadius: 5,
                            borderColor: COLORS.ic2C8BD7,
                            marginHorizontal: 20,
                            marginBottom: 10
                        }}>
                        <MyText
                            text={`${txtStartDate} - ${txtEndDate}`}
                            style={{
                                marginLeft: 10,
                                flex: 1
                            }}
                        />
                        <Icon
                            iconSet="Ionicons"
                            name="calendar-sharp"
                            style={{
                                fontSize: 20,
                                color: COLORS.ic2C8BD7
                            }}
                        />
                    </TouchableOpacity>
                    <CheckFilter
                        createUser={tempCreateUser}
                        onChangeParam={(value) => {
                            setTempCreateUser(value);
                        }}
                    />
                    <Button
                        text="Tìm kiếm"
                        styleContainer={styles.button}
                        styleText={{
                            color: COLORS.txtFFFFFF,
                            fontSize: 14
                        }}
                        onPress={() => {
                            setFilter((prev) => ({
                              ...prev,
                              isCreateUser: tempCreateUser
                            }));
                            onSearch(tempCreateUser);
                            hideModal();
                          }}
                    />
                </SafeAreaView>
            </View>
            {isShowDatePicker && (
                <View
                    style={{
                        width: constants.width,
                        height: constants.height,
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        justifyContent: 'center',
                        alignItems: 'center',
                        backgroundColor: COLORS.bg0000005
                    }}>
                    <View style={{ backgroundColor: COLORS.bgFFFFFF }}>
                        <CalendarPicker
                            weekdays={WEEKDAYS}
                            maxDate={dateNow}
                            months={MONTHS}
                            allowRangeSelection
                            selectedDayColor="#98F1F3"
                            allowBackwardRangeSelect
                            restrictMonthNavigation
                            disabledDates={(date) => date > dateNow}
                            onDateChange={onDateChange}
                        />
                        <View
                            style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'center'
                            }}>
                            <Button
                                text="Đóng"
                                styleContainer={[
                                    styles.button,
                                    {
                                        backgroundColor: COLORS.bgFF0000,
                                        marginRight: 10
                                    }
                                ]}
                                styleText={{
                                    color: COLORS.txtFFFFFF,
                                    fontSize: 14
                                }}
                                onPress={() => {
                                    setIsShowDatePicker(false);
                                }}
                            />
                            <Button
                                text="Xác nhận"
                                styleContainer={styles.button}
                                styleText={{
                                    color: COLORS.txtFFFFFF,
                                    fontSize: 14
                                }}
                                onPress={handleOnSubmitDate}
                            />
                        </View>
                    </View>
                    <Toast
                        config={TOAST_CONFIG}
                        position="bottom"
                        visibilityTime={2000}
                    />
                </View>
            )}
        </KModal>
    );
};

const styles = StyleSheet.create({
    button: {
        alignSelf: 'center',
        backgroundColor: COLORS.bg147EFB,
        borderRadius: constants.getSize(4),
        height: constants.getSize(40),
        marginVertical: 10,
        width: constants.getSize(80)
    }
});
export default ModalFilterSearch;
