import React, { Component } from 'react';
import {
    View,
    Image,
    ImageBackground,
    TouchableOpacity,
    TextInput,
    Keyboard
} from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import {
    My<PERSON>ex<PERSON>,
    Button,
    showBlockUI,
    hideBlockUI
} from "@components";
import {
    translate,
    changeLanguage,
    getCurrentLocaleLanguage
} from "@translate";
import { helper, storageHelper } from '@common';
import { STORAGE_CONST, ENUM } from '@constants';
import { COLORS } from "@styles";
import { styles } from './style';
import * as loginActionCreator from './action';
import * as appSwitchActionCreator from "../AppSwitch/action";
const { ID_LANGUAGE } = ENUM;

class Login extends Component {

    /**
     * ******************* Life Cycle ****************************
     */
    constructor(props) {
        super(props);
        this.state = {
            messageErrorLogin: "",
            language: "vi",
            userName: "",
            passWord: "",
        };
    }

    initFocus = () => {
        if (helper.IsValidateObject(this.inputUser)) {
            this.inputUser.focus();
            this.inputUser.blur();
        }
    }

    clearErrorMessage = () => {
        this.setState({ messageErrorLogin: '' });
    }

    doActionLogin = () => {
        const { loginAction } = this.props;
        const { userName, passWord } = this.state;
        this.clearErrorMessage();
        const regExpUser = new RegExp(/^0/);
        /**
         * Logic :
         *  username : only number and not empty
         *  password : not empty
         */
        if (!helper.IsNonEmptyString(userName)) {
            this.setState({ messageErrorLogin: translate("login.empty_username") });
            return;
        }
        if (!helper.IsNonEmptyString(passWord)) {
            this.setState({ messageErrorLogin: translate("login.empty_password") });
            return;
        }
        if (regExpUser.test(userName)) {
            this.setState({ messageErrorLogin: translate('fetchAPI.urs_pwd_incorrect') });
            return;
        }
        Keyboard.dismiss();
        showBlockUI();
        loginAction.requestOauthToken({
            "username": userName.trim(),
            "password": passWord
        });
    }

    componentDidMount() {
        this.initFocus();
        this.setState({ language: getCurrentLocaleLanguage() });
    }

    componentDidUpdate(preProps, preState) {
        const { isFetching } = this.props;
        if (preProps.isFetching !== isFetching) {
            if (!isFetching) {
                this.handleResultAuthen(this.props);
            }
        }
    }

    handleResultAuthen = async (props) => {
        const {
            description,
            isError,
            appSwitchAction
        } = props;
        if (isError) {
            hideBlockUI();
            this.setState({ messageErrorLogin: description });
        }
        else {
            storageHelper.setItem(STORAGE_CONST.CURRENT_LANGUAGE, this.state.language);
            const requireOTP = await storageHelper.getItem(STORAGE_CONST.REQUIRE_OTP);
            hideBlockUI();
            if (requireOTP == "true") {
                appSwitchAction.switchToOtpScreen();
            } else {
                appSwitchAction.switchToMainScreen();
            }
        }
    }

    render() {
        return (
            <KeyboardAwareScrollView
                keyboardShouldPersistTaps="always"
                keyboardDismissMode="on-drag"
                bounces={false}
                overScrollMode="always"
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                enableOnAndroid
                contentContainerStyle={{
                    flexGrow: 1
                }}
                extraScrollHeight={60}
            >
                <ImageBackground
                    style={{ flex: 1 }}
                    source={{ uri: "background_login" }}
                // source={require('../../../assets/background_login.png')}
                >
                    <View style={styles.containerFlex}>
                        <View style={styles.container}>
                            <FormLogo />
                            <View style={{ marginTop: 20 }}>

                                <FormInputLogin
                                    sourceIcon={{ uri: "ic_username" }}
                                    placeholder={translate("login.text_input_username")}
                                    _inputRef={ref => this.inputUser = ref}
                                    onSubmitEditing={(event) => { this.inputPassword.focus(); }}
                                    onChangeText={(username) => this.setState({
                                        userName: username,
                                        messageErrorLogin: ""
                                    })}
                                    value={this.state.userName}
                                    autoFocus={true}
                                />
                                <FormInputLogin
                                    sourceIcon={{ uri: "ic_password" }}
                                    _inputRef={ref => this.inputPassword = ref}
                                    placeholder={translate("login.text_input_password")}
                                    secureTextEntry={true}
                                    onSubmitEditing={this.doActionLogin}
                                    onChangeText={(password) => this.setState({
                                        passWord: password,
                                        messageErrorLogin: ""
                                    })}
                                    value={this.state.passWord}
                                    autoFocus={false}
                                />

                            </View>

                            <FormDesciptionError messageErrorLogin={this.state.messageErrorLogin} />

                            <FormButtonLogin onPress={this.doActionLogin} />

                            {/* <TouchableOpacity style={styles.container_qrcode}>
                                <MyText
                                    addSize={2}
                                    text={translate("login.scan_qr_code")}
                                    style={{
                                        fontWeight: 'bold',
                                        color: COLORS.txt333333
                                    }}
                                />
                                <View>
                                    <Image
                                        style={{ width: 50, height: 50 }}
                                        source={{ uri: "qr_code" }} />
                                </View>
                            </TouchableOpacity> */}

                            <GroupSelectLanguage
                                language={this.state.language}
                                selectLanguage={(tag) => {
                                    global.languageID = ID_LANGUAGE[tag];
                                    changeLanguage(tag);
                                    this.setState({ language: tag });
                                }}
                            />
                        </View>
                    </View>

                </ImageBackground>
            </KeyboardAwareScrollView>

        );
    }

}

const mapStateToProps = (state) => ({
    isFetching: state.loginReducer.isFetching,
    description: state.loginReducer.description,
    isError: state.loginReducer.isError,
});

const mapDispatchToProps = (dispatch) => ({
    loginAction: bindActionCreators(loginActionCreator, dispatch),
    appSwitchAction: bindActionCreators(appSwitchActionCreator, dispatch),
});

export default connect(mapStateToProps, mapDispatchToProps)(Login);

/**
 * ******************* Extend Components ****************************
 */

const FormLogo = () => {
    return (
        <View
            style={{
                justifyContent: "center",
                alignContent: "center",
                alignItems: "center"
            }}
        >
            <Image
                style={styles.ic_logo}
                source={{ uri: "logo_tgdd" }}
            />

            <MyText
                addSize={6}
                style={{
                    fontWeight: 'bold'
                }}
                text={"MWG"}
            />
        </View>
    )
}

const FormInputLogin = (props) => {
    return (
        <View style={styles.wrap_container}>
            <View style={styles.container_icon}>
                <Image
                    style={{ width: 20, height: 20 }}
                    source={props.sourceIcon}
                />
            </View>
            <View style={styles.container_input}>
                <TextInput
                    ref={props._inputRef}
                    style={{ flex: 1, paddingLeft: 20 }}
                    autoFocus={props.autoFocus}
                    {...props}
                />
            </View>
        </View>
    )
}

const FormDesciptionError = ({ messageErrorLogin }) => {
    return (
        <View
            style={{
                width: 255,
                marginBottom: 10,
            }}
        >
            <MyText
                style={{
                    color: COLORS.txtFF0000
                }}
                text={messageErrorLogin}
            />
        </View>
    )
}

const FormButtonLogin = ({ onPress }) => {
    return (
        <Button
            onPress={onPress}
            text={translate("login.btn_login")}
            styleContainer={styles.button}
            styleText={{
                fontWeight: 'bold',
                color: COLORS.txt288AD6
            }}
        />
    )
}

const GroupSelectLanguage = ({ language, selectLanguage }) => {
    const isVN = (language == 'vi');

    const onChange = (tag) => () => {
        if (language != tag) {
            selectLanguage(tag);
        }
    }

    return (
        <View
            style={{
                flexDirection: 'row'
            }}
        >
            <TouchableOpacity
                onPress={onChange('vi')}
                style={{ padding: 16 }}
            >
                <MyText
                    addSize={2}
                    style={{
                        fontWeight: 'bold',
                        color: isVN ? COLORS.txt000000 : COLORS.txtFFFFFF,
                        marginRight: 14
                    }}
                    text={"Tiếng Việt"}
                />
            </TouchableOpacity>
            <TouchableOpacity
                onPress={onChange('en')}
                style={{ padding: 16 }}
            >
                <MyText
                    addSize={2}
                    style={{
                        fontWeight: 'bold',
                        color: isVN ? COLORS.txtFFFFFF : COLORS.txt000000
                    }}
                    text={"English"}
                />
            </TouchableOpacity>
        </View>
    )
}


