import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, Alert, Linking, Text } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { MyText, Icon, showBlock<PERSON>, hideBlockUI } from '@components';
import { Color, helper } from '@common';
import { constants } from "@constants";
import ModalDelete from './ModalDelete';
import ModalQRRegistration from './ModalQRRegistration';
import ModalCancel from './ModalCancel';
import * as installmentAction from './../action';
import { translate } from '@translate';
import { COLORS } from "@styles";
import ModalOffer from './Modal/ModalOffer';
import { updateStatusRandomDiscountPromotion } from '../../Detail/action';
import * as detaiAction from '../../Detail/action'
import InnerQRContext from './InnerQRContext'
import { useBottomSheet } from '@context';
const { PARTNER_ID } = constants

function FlatListItem({
    navigation,
    item,
    ind,
    showIndicator,
    hideIndicator,
    SearchData,
    isVisible,
    upIsVisible
}) {
    const { showBottomSheet } = useBottomSheet();

    const [option, setOption] = React.useState({
        isDeleted: false,
        isCancel: false
    });
    const dispatch = useDispatch();
    const [isDeleteVisible, setIsDeleteVisible] = useState(false);
    const [isCancelVisible, setIsCancelVisible] = useState(false);
    const [isVisibleQRRegistration, setIsVisibleQRRegistration] =
        useState(false);
    const [isUseEffect, setIsUseEffect] = useState(false);
    const [linkURL, setLinkURL] = useState('');
    // const InstallmentReasonReducer = useSelector(state => state.InstallmentReducer.lstReasonCancel);
    const [block, setBlock] = useState(false);
    const [dataReason, setDataReason] = useState([]);
    const [hiddenMenuStatusList, setHiddenMenuStatusList] = useState(["1", "3", "MWG04", "MWG06", "MWG22", "MWG23", "MWG24", "'MWG25", "MWG27", "MWG28"]);
    const [hiddenMenuCancelStatusList, setHiddenMenuCancelStatusList] = useState(["1", "MWG04"]);
    const [EPTypeOnLineList, setEPTypeOnLineList] = useState(["6", "7"]);
    const [isVisibleModal, setIsVisibleModal] = useState(false);
    const [dataOffer, setDataOffer] = useState({});
    const { storeID } = useSelector((state) => state.userReducer);
    const [isLoading, setIsLoading] = useState(false);
    const handleSkipRandomDiscountPromotion = async (ep, appliedRandomDiscountList) => {
        try {
            if (appliedRandomDiscountList?.length > 0) {
                const newAppliedRandomDiscountList = appliedRandomDiscountList.map(item => ({
                    ...item,
                    Status: 1
                }));
                await dispatch(updateStatusRandomDiscountPromotion(newAppliedRandomDiscountList));
            } else {
                throw new Error("Không thể xem thông tin trả góp với đơn hàng có tham gia khuyến mãi Vòng quay may mắn.");
            }

            goToDetailV2(ep);
        } catch (error) {
            console.log("error ", error)
            Alert.alert(translate("common.notification"), error, [
                {
                    text: translate("common.btn_notify_try_again"),
                    style: "default",
                    onPress: () => handleSkipRandomDiscountPromotion(ep, appliedRandomDiscountList)
                },
                {
                    text: translate("common.btn_skip"),
                    onPress: hideBlockUI
                }
            ]);
        }
    }

    const goToDetailV2 = (ep) => {
        //showIndicator();
        showBlockUI();
        dispatch(installmentAction.getEPTransaction(ep.epTransactionId))
            .then((res) => {
                if (!res.objEPOSTransactionBO?.cus_WarningMessage) {
                    hideBlockUI();
                    if (res.objEPOSTransactionBO.cus_ExistCompleteRandomDis) {
                        res.objEPOSTransactionBO.TotalPrepaid = 0;
                        res.objEPOSTransactionBO.MonthlyPayment = 0;
                    }
                    navigation.push('InstallmentSteps', {
                        instalmentData: res,
                        isCreateFromSO: false
                    });
                } else {
                    Alert.alert(
                        translate('common.notification'),
                        res.objEPOSTransactionBO.cus_WarningMessage,
                        [
                            {
                                text: translate("common.customer_decline"),
                                onPress: () => handleSkipRandomDiscountPromotion(ep, res.lstRandomDiscountApply)
                            },
                            {
                                text: translate("common.customer_accept"),
                                style: "cancel",
                                onPress: hideBlockUI
                            }
                        ],
                        { cancelable: false }
                    );
                }
            })
            .catch((err) => {
                Alert.alert(
                    translate('common.notification'),
                    err.msgError,
                    [
                        {
                            text: translate('common.btn_close'),
                            onPress: () => {
                                //hideIndicator();
                                hideBlockUI();
                            }
                        }
                    ],
                    { cancelable: false }
                );
            });
        if (helper.isMatchedInstallmentStore(storeID)) {
            dispatch(detaiAction.getRewardInstallment(ep?.partnerInstallmentId))
        }
        dispatch(detaiAction.getPartnerInstallmentUserCode(ep?.partnerInstallmentId))
    };

    const goToDetailInstallmentNew = (ep) => {
        showBlockUI();
        const { epTransactionId, isCreatedSt } = ep;
        const data = {
            EPTransactionID: epTransactionId,
            times: isCreatedSt ? 1 : 2
        }
        dispatch(installmentAction.getEPTransactionNew(data))
            .then((res) => {
                if (!res.objEPOSTransactionBO?.cus_WarningMessage) {
                    hideBlockUI();
                    if (res.objEPOSTransactionBO.cus_ExistCompleteRandomDis) {
                        res.objEPOSTransactionBO.TotalPrepaid = 0;
                        res.objEPOSTransactionBO.MonthlyPayment = 0;
                    }
                    navigation.push('InstallmentSteps', {
                        instalmentData: res,
                        isCreateFromSO: false,
                        itemInstallmentManager: ep
                    });
                } else {
                    Alert.alert(
                        translate('common.notification'),
                        res.objEPOSTransactionBO.cus_WarningMessage,
                        [
                            {
                                text: translate("common.customer_decline"),
                                onPress: () => handleSkipRandomDiscountPromotion(ep, res.lstRandomDiscountApply)
                            },
                            {
                                text: translate("common.customer_accept"),
                                style: "cancel",
                                onPress: hideBlockUI
                            }
                        ],
                        { cancelable: false }
                    );
                }
            })
            .catch((err) => {
                Alert.alert(
                    translate('common.notification'),
                    err.msgError,
                    [
                        {
                            text: translate('common.btn_close'),
                            onPress: () => {
                                //hideIndicator();
                                hideBlockUI();
                            }
                        }
                    ],
                    { cancelable: false }
                );
            });
        if (helper.isMatchedInstallmentStore(storeID)) {
            dispatch(detaiAction.getRewardInstallment(ep?.partnerInstallmentId))
        }
        dispatch(detaiAction.getPartnerInstallmentUserCode(ep?.partnerInstallmentId))
    };

    const goToOTPCode = (ep) => {
        //showIndicator();
        showBlockUI();
        dispatch(installmentAction.getEPTransaction(ep.epTransactionId))
            .then((res) => {
                //hideIndicator();
                hideBlockUI();
                navigation.push('InstallmentSteps', {
                    instalmentData: res,
                    isCreateFromSO: false
                });
            })
            .catch((err) => {
                Alert.alert(
                    translate('common.notification'),
                    err.msgError,
                    [
                        {
                            text: translate('common.btn_close'),
                            onPress: () => {
                                //hideIndicator();
                                hideBlockUI();
                            }
                        }
                    ],
                    { cancelable: false }
                );
            });
    };

    const GetStatusEPOSTransaction = (ep) => {
        if (ep.partnerInstallmentId != PARTNER_ID.SAMSUNG) {
            var intMinutes = 5; //Mặc định gán 5 phút mới dc hỏi trạng thái (đối với HC FE)
            //Kiểm tra nếu ACS thì gán lại 20 phút
            if (ep.partnerInstallmentId == 1) {
                intMinutes = 20;
            }
            if (ep.partnerInstallmentId == 10 || ep.partnerInstallmentId == 15) {
                intMinutes = 1;
            }
            // var newDate = new Date(ep.updateDate);
            var newDate = new Date(ep.timeSpanUpdateDate);
            console.log('newDate: ', newDate);
            newDate.setMinutes(newDate.getMinutes() + intMinutes);
            var Minutes = (newDate - new Date()) / 60000;
            if (Minutes > 0 && ep.statusId != "MWG02") {
                Minutes = Math.round(Minutes) + 1;
                Alert.alert(
                    translate('common.notification'),
                    translate('instalmentManager.processing_information') +
                    Minutes +
                    translate('instalmentManager.check_status'),
                    [{
                        text: translate('common.btn_close'),
                        onPress: () => {
                            hideBlockUI();
                            SearchData(item.eposTransactionId);
                        }
                    }],
                    { cancelable: false }
                );
                return;
            }
        }
        let params = {
            EPTransactionID: ep.epTransactionId,
            PartnerInstallmentID: ep.partnerInstallmentId
        };
        showBlockUI();
        dispatch(installmentAction.getStatusEPOSTransaction(params))
            .then((res) => {
                hideBlockUI();
                Alert.alert(
                    translate('common.notification'),
                    translate('instalmentManager.check_record_status_success'),
                    [
                        {
                            text: 'OK',
                            onPress: () => {
                                SearchData(ep.eposTransactionId.trim());
                            }
                        }
                    ]
                );
            })
            .catch((err) => {
                console.log('err: ', err);
                hideBlockUI();
                const { errorType, msgError } = err;
                console.log('errorType: ', errorType);
                if (errorType == 1) {
                    Alert.alert(translate('common.notification'), msgError, [
                        {
                            text: translate('instalmentManager.btn_cancel'),
                            onPress: () => { }
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            onPress: () => {
                                GetStatusEPOSTransaction(ep);
                            }
                        }
                    ]);
                } else {
                    Alert.alert(translate('common.notification'), msgError, [
                        {
                            text: 'OK',
                            onPress: () => {
                                hideBlockUI();
                                SearchData(ep.eposTransactionId.trim());
                            }
                        }
                    ]);
                }
            });
    };

    const SubmitApplication = (ep) => {
        let params = {
            EPTransactionID: ep.epTransactionId,
            PartnerInstallmentID: ep.partnerInstallmentId
        };
        showBlockUI();
        dispatch(installmentAction.submitApplication(params))
            .then((res) => {
                hideBlockUI();
                Alert.alert(
                    translate('common.notification'),
                    translate('instalmentManager.send_record_success'),
                    [
                        {
                            text: 'OK',
                            onPress: () => {
                                SearchData(ep.eposTransactionId.trim());
                            }
                        }
                    ]
                );
            })
            .catch((err) => {
                console.log('err: ', err);
                hideBlockUI();
                const { errorType, msgError } = err;
                console.log('errorType: ', errorType);
                if (errorType == 1) {
                    Alert.alert(translate('common.notification'), msgError, [
                        {
                            text: translate('instalmentManager.btn_cancel'),
                            onPress: () => { }
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            onPress: () => {
                                SubmitApplication(ep);
                            }
                        }
                    ]);
                } else {
                    Alert.alert(translate('common.notification'), msgError, [
                        {
                            text: 'OK',
                            onPress: () => {
                                hideBlockUI();
                                SearchData(ep.eposTransactionId.trim());
                            }
                        }
                    ]);
                }
            });
    };

    const GetPrepareContractInfo = (ep) => {
        let params = {
            EPTransactionID: ep.epTransactionId,
            PartnerInstallmentID: ep.partnerInstallmentId
        };
        showBlockUI();
        dispatch(installmentAction.getPrepareContractInfo(params))
            .then((res) => {
                hideBlockUI();
                const { data } = res;
                if (data != '' && data != null && data != 'null') {
                    const url = JSON.parse(data);
                    setLinkURL(url);
                    setIsVisibleQRRegistration(true);
                } else {
                    Alert.alert(
                        translate('common.notification'),
                        translate('instalmentManager.check_customer_information_success'),
                        [
                            {
                                text: 'OK',
                                onPress: () => {
                                    SearchData(ep.eposTransactionId.trim());
                                }
                            }
                        ]
                    );
                }
            })
            .catch((err) => {
                console.log('err: ', err);
                hideBlockUI();
                const { errorType, msgError } = err;
                console.log('errorType: ', errorType);
                if (errorType == 1) {
                    Alert.alert(translate('common.notification'), msgError, [
                        {
                            text: translate('instalmentManager.btn_cancel'),
                            onPress: () => { }
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            onPress: () => {
                                GetPrepareContractInfo(ep);
                            }
                        }
                    ]);
                } else {
                    Alert.alert(translate('common.notification'), msgError, [
                        {
                            text: 'OK',
                            onPress: () => {
                                hideBlockUI();
                                SearchData(ep.eposTransactionId.trim());
                                //dispatch(installmentAction.searchInstallment(item.eposTransactionId, InstallmentReducer.paramFilter));
                            }
                        }
                    ]);
                }
            });
    };

    const showCancel = (ep) => {
        navigation.navigate('CancelEP', { item: ep });
    };

    const goToAttachments = (ep) => {
        let params = {
            EPTransactionID: ep.epTransactionId
        };
        //showIndicator();
        showBlockUI();
        dispatch(installmentAction.getAttachmentTypeByID(params))
            .then((res) => {
                //hideIndicator();
                hideBlockUI();
                navigation.navigate('Attachments', {
                    data: ep,
                    dataPaperTypeInforBO: res
                });
            })
            .catch((err) => {
                Alert.alert(
                    translate('common.notification'),
                    err.msgError,
                    [
                        {
                            text: translate('common.btn_close'),
                            onPress: () => {
                                //hideIndicator();
                                hideBlockUI();
                            }
                        }
                    ],
                    { cancelable: false }
                );
            });
    };

    const sendAttachFileToPartner = (ep) => {
        let params = {
            EPTransactionID: ep.epTransactionId,
            PartnerInstallmentID: ep.partnerInstallmentId
        };
        showBlockUI();
        dispatch(installmentAction.sendAttachFileToPartner(params))
            .then((res) => {
                hideBlockUI();
                Alert.alert(
                    translate('common.notification'),
                    translate('instalmentManager.send_success'),
                    [
                        {
                            text: 'OK',
                            onPress: () => {
                                SearchData(ep.eposTransactionId.trim());
                            }
                        }
                    ]
                );
            })
            .catch((err) => {
                console.log('err: ', err);
                hideBlockUI();
                const { errorType, msgError } = err;
                console.log('errorType: ', errorType);
                if (errorType == 1) {
                    Alert.alert(translate('common.notification'), msgError, [
                        {
                            text: translate('instalmentManager.btn_cancel'),
                            onPress: () => { }
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            onPress: () => {
                                sendAttachFileToPartner(ep);
                            }
                        }
                    ]);
                } else {
                    Alert.alert(translate('common.notification'), msgError, [
                        {
                            text: 'OK',
                            onPress: () => {
                                hideBlockUI();
                                SearchData(ep.eposTransactionId.trim());
                            }
                        }
                    ]);
                }
            });
    };

    const checkUrlContract = (ep) => {
        let params = {
            EPTransactionID: ep.epTransactionId.trim(),
            LinkPrintContract: ep.linkPrintContract,
            EPOSTransactionID: ep.eposTransactionId.trim()
        };
        showBlockUI();
        dispatch(installmentAction.checkurlcontract(params))
            .then((res) => {
                hideBlockUI();
                navigation.navigate('InstallmentPrinter', {
                    ep: ep,
                    data: res.data
                });
                // Alert.alert(translate('common.notification'), translate('instalmentManager.send_success'),
                //   [
                //       {
                //           text: "OK",
                //           onPress: () => {
                //             SearchData(ep.eposTransactionId.trim())
                //           },
                //       },
                //   ]);
            })
            .catch((err) => {
                console.log('err: ', err);
                hideBlockUI();
                const { errorType, msgError } = err;
                console.log('errorType: ', errorType);
                if (errorType == 1) {
                    Alert.alert(translate('common.notification'), msgError, [
                        {
                            text: translate('instalmentManager.btn_cancel'),
                            onPress: () => { }
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            onPress: () => {
                                sendAttachFileToPartner(ep);
                            }
                        }
                    ]);
                } else {
                    Alert.alert(translate('common.notification'), msgError, [
                        {
                            text: 'OK',
                            onPress: () => {
                                hideBlockUI();
                                SearchData(ep.eposTransactionId.trim());
                            }
                        }
                    ]);
                }
            });
    };

    const handleOfferInstallmetStep = (ep) => {
        showBlockUI();
        dispatch(installmentAction.getListApplicationOffer(ep.epTransactionId))
            .then((res) => {
                setIsVisibleModal(true)
                setDataOffer(res);
                hideBlockUI();
            })
            .catch((err) => {
                Alert.alert(
                    translate('common.notification'),
                    err.msgError,
                    [
                        {
                            text: translate('common.btn_close'),
                            onPress: () => {
                                hideBlockUI();
                            }
                        }
                    ],
                    { cancelable: false }
                );
            });
    }

    const handeAcceptOfferInstallment = () => {
        const {
            OfferRelipCode,
            MonthlyPaymentAT,
            TermLoanAT,
            TotalPrePaidAT,
            MonthlyPaymentBF,
            TermLoanBF,
            TotalPrePaidBF,
            EPTransactionID,
            EPOfferInfoID
        } = dataOffer;
        setIsLoading(true);
        dispatch(installmentAction.getAcceptAlternativeOffers({
            data: {
                OfferRelipCode,
                MonthlyPaymentAT,
                TermLoanAT,
                TotalPrePaidAT,
                MonthlyPaymentBF,
                TermLoanBF,
                TotalPrePaidBF,
                EPTransactionID,
                EPOfferInfoID
            },
        }))
            .then((res) => {
                setIsVisibleModal(true);
                GetStatusEPOSTransaction(item);
                setIsLoading(false);
            })
            .catch((err) => {
                Alert.alert(
                    translate('common.notification'),
                    err.msgError,
                    [
                        {
                            text: translate('instalmentManager.btn_cancel'),
                            onPress: () => {
                                setIsLoading(false)
                                SearchData(item.eposTransactionId)
                            }
                        }
                    ],
                    { cancelable: false }
                );
            });
    }

    const MenuOptions = ({ }) => {
        const { statusId, partnerInstallmentId, IsContinueMakeEpos, IsInputOTPDeeplink, extraData, epTransactionId } = item
        if (partnerInstallmentId == PARTNER_ID.SAMSUNG) {
            const handlePressOTPDeepLink = async () => {
                try {
                    showBlockUI()
                    let params = {
                        EPTransactionID: epTransactionId,
                        PartnerInstallmentID: partnerInstallmentId
                    };
                    const response = await dispatch(installmentAction.getPrepareContractInfo(params))
                    const { data } = response
                    if (JSON.parse(data)?.length > 0) {
                        const url = JSON.parse(data) || "samsungfinance"
                        Linking.openURL(url).catch(() => {
                            // Alert.alert('', 'Vui lòng cài app SAMSUNG trước khi gọi.');
                            handleShowBottomSheet(url)
                        });
                        hideBlockUI()
                    }
                    else {
                        Alert.alert(
                            translate('common.notification'),
                            "Không lấy được deeplink.",
                            [
                                {
                                    text: "Bỏ Qua",
                                    onPress: hideBlockUI
                                }
                            ],
                        );
                    }
                } catch (error) {
                    Alert.alert(
                        translate('common.notification'),
                        error.msgError,
                        [
                            {
                                text: "Thử Lại",
                                style: "cancel",
                                onPress: handlePressOTPDeepLink
                            },
                            {
                                text: "Bỏ Qua",
                                onPress: hideBlockUI
                            }

                        ],
                    );
                }
            };

            const handlePressContinueMakeEpo = () => {
                const url = extraData?.sfplusDeeplink ?? `samsungfinance`;
                Linking.openURL(url).catch(() => {
                    // Alert.alert('', 'Vui lòng cài app SAMSUNG trước khi gọi.');
                    handleShowBottomSheet(url)
                });
            };
            if (IsInputOTPDeeplink) {
                return <TouchableOpacity
                    style={{ flexDirection: "row", alignSelf: "flex-end" }}
                    activeOpacity={0.7}
                    onPress={handlePressOTPDeepLink}>
                    <Icon
                        iconSet="Feather"
                        name="external-link"
                        style={{ color: COLORS.ic5B8767 }}
                    />
                    <MyText
                        text={"Nhập mã OTP"}
                        style={[
                            styles.txtBtn,
                            { color: COLORS.txt5B8767 }
                        ]}
                    />
                </TouchableOpacity>
            }
            if (IsContinueMakeEpos) {
                return <TouchableOpacity
                    style={{ flexDirection: "row", alignSelf: "flex-start" }}
                    activeOpacity={0.7}
                    onPress={handlePressContinueMakeEpo}>
                    <Icon
                        iconSet="Feather"
                        name="external-link"
                        style={{ color: COLORS.ic5B8767 }}
                    />
                    <MyText
                        text={"Tiếp tục thực hiện hồ sơ"}
                        style={[
                            styles.txtBtn,
                            { color: COLORS.txt5B8767 }
                        ]}
                    />
                </TouchableOpacity>
            }
        }
        return (
            <View>
                <View
                    style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        marginBottom: 10
                    }}>
                    {item.isShowViewDetail && (
                        <TouchableOpacity
                            style={[styles.btn]}
                            activeOpacity={0.7}
                            onPress={() => {
                                goToDetailV2(item);
                            }}>
                            <Icon
                                iconSet="Feather"
                                name="external-link"
                                style={{ color: COLORS.ic5B8767 }}
                            />
                            <MyText
                                text={translate('common.view_detail')}
                                style={[
                                    styles.txtBtn,
                                    { color: COLORS.txt5B8767 }
                                ]}
                            />
                        </TouchableOpacity>
                    )}
                    {item.isCreatedSt && (
                        <TouchableOpacity
                            style={[styles.btn]}
                            activeOpacity={0.7}
                            onPress={() => {
                                goToDetailInstallmentNew(item);
                            }}>
                            <Icon
                                iconSet="Feather"
                                name="external-link"
                                style={{ color: COLORS.ic5B8767 }}
                            />
                            <MyText
                                text={"Nhập hồ sơ lần 1"}
                                style={[
                                    styles.txtBtn,
                                    { color: COLORS.txt5B8767 }
                                ]}
                            />
                        </TouchableOpacity>
                    )}
                    {item.isCreatedNd && (
                        <TouchableOpacity
                            style={[styles.btn]}
                            activeOpacity={0.7}
                            onPress={() => {
                                goToDetailInstallmentNew(item);
                            }}>
                            <Icon
                                iconSet="Feather"
                                name="external-link"
                                style={{ color: COLORS.ic5B8767 }}
                            />
                            <MyText
                                text={"Nhập hồ sơ lần 2"}
                                style={[
                                    styles.txtBtn,
                                    { color: COLORS.txt5B8767 }
                                ]}
                            />
                        </TouchableOpacity>
                    )}
                    {item.isShowGetStatus && (
                        <TouchableOpacity
                            style={[styles.btn]}
                            activeOpacity={0.7}
                            onPress={() => {
                                if (item.partnerInstallmentId == 16) { //id = 16 => F88 Instalment
                                    showBlockUI();
                                    dispatch(installmentAction.getInstalmentInfoF88(item.epTransactionId, true))
                                        .then((dataInstalment) => {
                                            if (dataInstalment.IscallPorttalPartner) {
                                                hideBlockUI();
                                                navigation.navigate("PortalInstalmentF88", { dataInstalment });
                                            } else {
                                                GetStatusEPOSTransaction(item);
                                            }
                                        })
                                        .catch(mesError => {
                                            Alert.alert("Thông báo", mesError, [
                                                {
                                                    text: "OK",
                                                    onPress: hideBlockUI
                                                }
                                            ])
                                        });
                                } else {
                                    GetStatusEPOSTransaction(item);
                                }
                            }}>
                            <Icon
                                iconSet="Feather"
                                name="fast-forward"
                                style={{ color: COLORS.ic0088F2 }}
                            />
                            <MyText
                                text={translate('instalmentManager.btn_check_record_status')}
                                style={[styles.txtBtn, { color: COLORS.txt0088F2 }]}
                            />
                        </TouchableOpacity>
                    )}
                    {item.isShowConfirmOTP && (
                        <TouchableOpacity
                            style={[styles.btn]}
                            activeOpacity={0.7}
                            onPress={() => {
                                goToOTPCode(item);
                            }}>
                            <Icon
                                iconSet="MaterialCommunityIcons"
                                name="cellphone-message"
                                style={{ color: COLORS.ic0088F2 }}
                            />
                            <MyText
                                text={translate('instalmentManager.btn_confirm_otp')}
                                style={[styles.txtBtn, { color: COLORS.txt0088F2 }]}
                            />
                        </TouchableOpacity>
                    )}
                    {item.isShowSendSubmitApplication && (
                        <TouchableOpacity
                            style={[styles.btn]}
                            activeOpacity={0.7}
                            onPress={() => {
                                SubmitApplication(item);
                            }}>
                            <Icon
                                iconSet="Feather"
                                name="send"
                                style={{ color: COLORS.ic0088F2 }}
                            />
                            <MyText
                                text={translate('instalmentManager.btn_send_record')}
                                style={[styles.txtBtn, { color: COLORS.txt0088F2 }]}
                            />
                        </TouchableOpacity>
                    )}
                    {item.isShowPrepareContract && (
                        <TouchableOpacity
                            style={[styles.btn]}
                            activeOpacity={0.7}
                            onPress={() => {
                                GetPrepareContractInfo(item);
                            }}>
                            <Icon
                                iconSet="Entypo"
                                name="attachment"
                                style={{ color: COLORS.ic0088F2 }}
                            />
                            <MyText
                                text={translate('instalmentManager.btn_prepare_document')}
                                style={[styles.txtBtn, { color: COLORS.txt0088F2 }]}
                            />
                        </TouchableOpacity>
                    )}
                    {item.isShowGotoAddAttachment && (
                        <TouchableOpacity
                            style={[styles.btn]}
                            activeOpacity={0.7}
                            onPress={() => {
                                goToAttachments(item);
                            }}>
                            <Icon
                                iconSet="MaterialIcons"
                                name="note-add"
                                style={{ color: COLORS.ic0088F2 }}
                            />
                            <MyText
                                text={translate('instalmentManager.btn_add_record')}
                                style={[styles.txtBtn, { color: COLORS.txt0088F2 }]}
                            />
                        </TouchableOpacity>
                    )}
                    {item.isShowGotoAddContract && (
                        <TouchableOpacity
                            style={[styles.btn]}
                            activeOpacity={0.7}
                            onPress={() => {
                                goToAttachments(item);
                            }}>
                            <Icon
                                iconSet="Entypo"
                                name="attachment"
                                style={{ color: COLORS.ic0088F2 }}
                            />
                            <MyText
                                text={translate('instalmentManager.btn_capture_contract')}
                                style={[styles.txtBtn, { color: COLORS.txt0088F2 }]}
                            />
                        </TouchableOpacity>
                    )}
                    {item.isShowSendAttachFile && (
                        <TouchableOpacity
                            style={[styles.btn]}
                            activeOpacity={0.7}
                            onPress={() => {
                                sendAttachFileToPartner(item);
                            }}>
                            <Icon
                                iconSet="Entypo"
                                name="attachment"
                                style={{ color: COLORS.ic0088F2 }}
                            />
                            <MyText
                                text={translate('instalmentManager.btn_send_record_to_partner')}
                                style={[styles.txtBtn, { color: COLORS.txt0088F2 }]}
                            />
                        </TouchableOpacity>
                    )}
                    {item.isShowGoToPrintContract && (
                        <TouchableOpacity
                            style={[styles.btn]}
                            activeOpacity={0.7}
                            onPress={() => {
                                checkUrlContract(item);
                            }}>
                            <Icon
                                iconSet="Ionicons"
                                name="print"
                                style={{ color: COLORS.ic0088F2 }}
                            />
                            <MyText
                                text={translate('instalmentManager.btn_print_contract')}
                                style={[styles.txtBtn, { color: COLORS.txt0088F2 }]}
                            />
                        </TouchableOpacity>
                    )}
                    {item.IsGetAlthernativeOffer == true && (
                        <TouchableOpacity
                            style={[styles.btn]}
                            activeOpacity={0.7}
                            onPress={() =>
                                handleOfferInstallmetStep(item)}>
                            <Icon
                                iconSet="EvilIcons"
                                name="sc-telegram"
                                size={25}
                                style={{ color: COLORS.txt0088F2 }}
                            />
                            <MyText
                                text={"Lấy offer khác"}
                                style={[
                                    styles.txtBtn,
                                    { color: COLORS.txt0088F2 }
                                ]}
                            />
                        </TouchableOpacity>
                    )}
                </View>
            </View>
        );
    };


    const handleShowBottomSheet = (url) => {
        showBottomSheet({
            component: <InnerQRContext data={{ url: url }} />,
            title: "Mã QR"
        });
    };

    return (
        <View
            style={{
                padding: 10,
                backgroundColor: ind % 2 == 0 ? COLORS.bgFFFFFF : COLORS.bgF5F5F5
            }}
            activeOpacity={0.7}>
            <View style={styles.fieldSet}>
                <MyText
                    style={{
                        color: COLORS.txt333333
                    }}
                    text={item.eposTransactionId.trim()}
                />
                <MyText text={' - '} />
                <MyText
                    style={{ color: COLORS.txtFF0000 }}
                    text={item.partnerInstallmentName}
                />
            </View>
            <View style={styles.fieldSet}>
                <MyText text={translate('instalmentManager.export_request')} style={{ color: COLORS.txtA2A4A6 }} />
                <MyText text={item.saleOrderId?.trim()} />
            </View>
            <View style={styles.fieldSet}>
                <MyText
                    text={translate('common.customer')}
                    style={{ color: COLORS.txtA2A4A6 }}
                    children={
                        <MyText
                            style={{ fontWeight: '700' }}
                            text={item.fullName}
                        />
                    }
                />
            </View>
            <View style={styles.fieldSet}>
                <MyText
                    text={translate('instalmentManager.record_status')}
                    style={{ color: COLORS.txtA2A4A6 }}
                    children={
                        <MyText
                            style={{ color: COLORS.txt006400 }}
                            text={item.eposStatusName}
                        />
                    }
                />
            </View>
            {item.isShowTextNote && (
                <View
                    style={[
                        styles.fieldSet,
                        { flexDirection: 'column', padding: 0 }
                    ]}>
                    <MyText
                        text={translate('instalmentManager.note')}
                        style={{ color: COLORS.txtA2A4A6 }}
                        children={
                            <MyText style={{ color: COLORS.txtFF0000 }} text={item.note} />
                        }
                    />
                    {/* <View style={{
              flex: 1,
              paddingHorizontal: constants.getSize(5),
              backgroundColor: ind % 2 != 0 ? COLORS.bgFFFFFF : COLORS.bgF5F5F5,
              width: constants.width - constants.getSize(20),
              borderWidth: 1,
              marginVertical: 5,
              borderColor: '#DEDEDE',
              borderRadius: 4,
          }}>
            {
              <View style={{
                flex: 1,
                flexDirection: 'row',
                marginHorizontal: constants.getSize(5),
                marginVertical: constants.getSize(5),
              }}>
                  <MyText
                      text={item.note}
                      //children={<MyText text={"12/2020"}/>}
                  />
              </View>
            }
          </View> */}
                </View>
            )}
            {item.isShowPartnerApplicationNO && (
                <View style={styles.fieldSet}>
                    <MyText
                        text={translate('instalmentManager.partner_record_code')}
                        style={{ color: COLORS.txtA2A4A6 }}
                    />
                    <MyText
                        style={{ color: COLORS.txt007AFF }}
                        text={item.partnerApplicationNo}
                    />
                </View>
            )}
            {item.isShowTextContractID && (
                <View style={styles.fieldSet}>
                    <MyText
                        text={translate('instalmentManager.contract_number')}
                        style={{ color: COLORS.txtA2A4A6 }}
                    />
                    <MyText
                        style={{ color: COLORS.txt007AFF }}
                        text={item.contractId}
                    />
                </View>
            )}
            {helper.IsNonEmptyString(item?.extraData?.HPLLink) &&
                <View style={styles.fieldSet}>
                    <MyText
                        text={"Trạng thái Home PayLater: "}
                        style={{ color: COLORS.txtA2A4A6 }}
                        children={
                            <MyText
                                text={item?.extraData?.NotifyHPL}
                                addSize={-1.5}
                                style={{
                                    color: COLORS.bgFF0000,
                                    marginBottom: 10,
                                    fontSize: 14,
                                    marginTop: 10,
                                    marginLeft: 5,
                                    width: '95%'
                                }} >
                                {
                                    <MyText
                                        text={` "TẠI ĐÂY"`}
                                        onPress={() => { `${Linking.openURL(item?.extraData?.HPLLink)}` }}
                                        addSize={-1.5}
                                        style={{
                                            color: COLORS.bg1E88E5,
                                            fontSize: 14,
                                            marginLeft: 3,
                                            fontWeight: 'bold',
                                        }}
                                    />
                                }
                            </MyText>
                        }
                    />
                </View>
            }
            {item.isDeleted == 0 && (
                <>
                    {(EPTypeOnLineList.includes(item.epTransactionTypeTypeId)) ?
                        (
                            <>
                                {hiddenMenuStatusList.includes(item.statusId) &&
                                    <MenuOptions item={item} />
                                }
                                {hiddenMenuCancelStatusList.includes(item.statusId) &&
                                    <TouchableOpacity
                                        style={[
                                            styles.btnDel,
                                            { backgroundColor: COLORS.btnD3D3D3 }
                                        ]}
                                        activeOpacity={0.7}
                                        onPress={() => {
                                            if (item.isShowDeleted) {
                                                setIsDeleteVisible(true);
                                            } else if (item.isShowCancel) {
                                                showCancel(item);
                                            }
                                            setOption({
                                                isDeleted: item.isShowDeleted
                                                    ? true
                                                    : false,
                                                isCancel: item.isShowCancel ? true : false
                                            });
                                        }}
                                        disabled={block ? true : false}>
                                        <Icon
                                            iconSet="MaterialCommunityIcons"
                                            name="trash-can-outline"
                                            style={{ color: COLORS.icFF0000 }}
                                        />
                                        <MyText
                                            text={item.isShowDeleted ? translate('instalmentManager.btn_delete') : translate('instalmentManager.btn_cancel')}
                                            style={[styles.txtBtn, { color: COLORS.txtFF0000 }]}
                                        />
                                    </TouchableOpacity>
                                }
                            </>
                        )
                        :
                        (
                            <>
                                <MenuOptions item={item} />
                                {(item.isShowDeleted || item.isShowCancel) && (
                                    <TouchableOpacity
                                        style={[
                                            styles.btnDel,
                                            { backgroundColor: COLORS.btnD3D3D3 }
                                        ]}
                                        activeOpacity={0.7}
                                        onPress={() => {
                                            if (item.isShowDeleted) {
                                                setIsDeleteVisible(true);
                                            } else if (item.isShowCancel) {
                                                if (item.partnerInstallmentId == PARTNER_ID.SAMSUNG) {
                                                    const url = item.extraData?.sfplusDeeplink ?? `samsungfinance`;
                                                    Linking.openURL(url).catch(() => {
                                                        // Alert.alert('', 'Vui lòng cài app SAMSUNG trước khi gọi.');
                                                        handleShowBottomSheet(url)
                                                    });
                                                    return
                                                }
                                                showCancel(item);
                                            }
                                            setOption({
                                                isDeleted: item.isShowDeleted
                                                    ? true
                                                    : false,
                                                isCancel: item.isShowCancel ? true : false
                                            });
                                        }}
                                        disabled={block ? true : false}>
                                        <Icon
                                            iconSet="MaterialCommunityIcons"
                                            name="trash-can-outline"
                                            style={{ color: COLORS.icFF0000 }}
                                        />
                                        <MyText
                                            text={item.isShowDeleted ? translate('instalmentManager.btn_delete') : translate('instalmentManager.btn_cancel')}
                                            style={[styles.txtBtn, { color: COLORS.txtFF0000 }]}
                                        />
                                    </TouchableOpacity>
                                )}
                            </>
                        )}
                </>
            )}
            <ModalDelete
                isDeleteVisible={isDeleteVisible}
                hideModal={() => {
                    setIsDeleteVisible(false);
                }}
                item={item}
                option={option}
                SearchData={() => {
                    SearchData();
                }}
                showIndicator={() => {
                    showIndicator();
                }}
                hideIndicator={() => {
                    hideIndicator();
                }}
            />
            <ModalCancel
                dataReason={dataReason}
                isCancelVisible={isCancelVisible}
                hideModal={() => {
                    setIsCancelVisible(false);
                }}
                item={item}
                option={option}
                SearchData={() => {
                    SearchData();
                }}
                showIndicator={() => {
                    showIndicator();
                }}
                hideIndicator={() => {
                    hideIndicator();
                }}
            />
            <ModalQRRegistration
                isVisible={isVisibleQRRegistration}
                hideModal={() => {
                    setIsVisibleQRRegistration(false);
                }}
                url={linkURL}
                item={item}
                option={option}
                SearchData={() => {
                    SearchData();
                }}
                showIndicator={() => {
                    showIndicator();
                }}
                hideIndicator={() => {
                    hideIndicator();
                }}
            />
            {isVisibleModal && <ModalOffer
                isLoading={isLoading}
                isVisible={isVisibleModal}
                hideModal={() => {
                    setIsVisibleModal(false);
                }}
                item={item}
                storeID={storeID}
                dataOffer={dataOffer}
                handeAcceptOfferInstallment={() => handeAcceptOfferInstallment(dataOffer)}
            />}
        </View>
    );
}


const styles = StyleSheet.create({
    type: {
        fontWeight: 'normal',
        overflow: 'hidden',
        paddingVertical: 2,
        paddingHorizontal: 10,
        borderRadius: 10,
        fontSize: 13
    },
    fieldSet: {
        flexDirection: 'row',
        paddingVertical: 5,
        // justifyContent: "space-between",
    },
    btn: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingTop: 5
    },
    txtBtn: {
        paddingLeft: 2,
        fontSize: 12
    },
    btnRePrint: {
        flexDirection: 'row',
        // backgroundColor: COLORS.btn2C8BD7,
        alignItems: 'center'
    },
    btnDel: {
        height: 30,
        width: 50,
        position: 'absolute',
        top: 0,
        right: 0,
        backgroundColor: Color.border,
        alignItems: 'center',
        borderBottomStartRadius: 10,
        flexDirection: 'row',
        justifyContent: 'center'
        // opacity: 0.5,
    },
    wrapCreateBy: {
        marginRight: 10,
        padding: 5,
        backgroundColor: COLORS.bg2C8BD7,
        // borderWidth: 2,
        borderRadius: 5,
        position: 'absolute',
        right: 0
    }
});
export default FlatListItem;
