import React, { useState, useEffect } from "react";
import {
    View,
    TouchableOpacity,
    Keyboard,
    StyleSheet,
    Alert,
} from 'react-native';
import {
    MyText,
    Icon,
    TitleInput,
    Picker,
    showBlockUI,
    hideBlock<PERSON>,
    DatePicker
} from "@components";
import { constants } from "@constants";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import moment from 'moment';
import PickerLocation from "./PickerLocation"
import { useDispatch, useSelector } from 'react-redux';
import * as installmentHelper from "../common/installmentHelper";
import * as locationAction from "../../Location/action";
import { translate } from '@translate';
import { COLORS } from "@styles";
import TooltipWrapper from './TooltipWrapper';
import * as installmentAction from "../action";
import { helper } from "@common";
import ModalConfirmNew from "./Modal/ModalConfirmNew";
import * as dataCacheInstallmentAction from "../action";

const StepFiveNew = function ({ parent, onNext, onPrev, validateObject, updatestate, updateprevstate, updateparent, instalmentPartnerList, SearchDataInstallment, itemInstallmentManager }) {
    console.log("parent step 5 new: ", parent);
    const dispatch = useDispatch();
    const [state, setState] = React.useState({
        EPOSTransactionBO: parent.EPOSTransactionBO
    })
    const MaritalStatusList = parent.InitData.lstMaritalStatus;
    const CompanyTypeList = parent.InitData.lstCompanyType;
    const AllPositionList = parent.InitData.lstPosition;
    const SocialStatusList = parent.InitData.lstSocialStatus;
    const [PositionList, setPositionList] = React.useState([])
    const EducationList = parent.InitData.lstEducation;
    const CareerList = parent.InitData.lstCareer;

    const [province, setProvince] = React.useState([]);
    const [district, setDistrict] = React.useState([]);
    const [ward, setWard] = React.useState([]);
    const [indexPager, setIndexPager] = useState(0);
    const [isShowIndicator, setIsShowIndicator] = useState(false);
    const locationReducer = useSelector(state => state.locationReducer)
    const [EmployedFrom, setEmployedFrom] = React.useState();
    const refScroll5 = React.useRef(null);
    const [block, setBlock] = useState(false);
    const { isCreatedSt } = itemInstallmentManager;
    const storeID = useSelector(state => state.userReducer.storeID);
    const { GroupPaperTypeInforBOList } = parent;
    const [isVisibleConfirm, setIsVisibleConfirm] = useState(false);
    const isCheckBCSecondNew = state?.EPOSTransactionBO?.EpBroadcastID;
    let PartnerInstallmentID_MC = 10;
    const isBroadcastInstalment = instalmentPartnerList.length > 1;
    const dataCacheInstallmentReducer = useSelector(state => state.DataCacheInstallmentReducer);
    const broadcastSaleProgram = useSelector(state => state.InstallmentReducer.broadcastSaleProgram);
    const client_ListStatusMarried = parent.EPOSTransactionBO?.client_ListStatusMarried;

    const storeIDFENew = helper.checkConfigStoreInstallmentFENew(storeID);
    const isCheckFE = state.EPOSTransactionBO.PartnerInstallmentID === 3 && storeIDFENew;

    const storeIDHCAndACSNew = helper.checkConfigStoreInstallmentHCAndACSNew(storeID);
    const isCheckACS = state.EPOSTransactionBO.PartnerInstallmentID === 1 && storeIDHCAndACSNew;
    const isCheckHC = state.EPOSTransactionBO.PartnerInstallmentID === 2 && storeIDHCAndACSNew;

    const storeIDMCNew = helper.checkConfigStoreInstallmentMCNew(storeID);
    const isCheckMC = state.EPOSTransactionBO.PartnerInstallmentID === 10 && storeIDMCNew;

    const isCheckMAFC = state.EPOSTransactionBO.PartnerInstallmentID === 15;
    const isCheckShihan= state.EPOSTransactionBO.PartnerInstallmentID === 22;

    useEffect(() => {
        setState({
            ...state,
            EPOSTransactionBO: {
                ...parent.EPOSTransactionBO,
                ProvinceIDCompany: state.EPOSTransactionBO.ProvinceIDCompany === 0 ? -1 : parent.EPOSTransactionBO.ProvinceIDCompany,
                DistrictIDCompany: state.EPOSTransactionBO.DistrictIDCompany === 0 ? -1 : parent.EPOSTransactionBO.DistrictIDCompany,
                WardIDCompany: state.EPOSTransactionBO.WardIDCompany === 0 ? -1 : parent.EPOSTransactionBO.WardIDCompany,
            }
        })
        installmentHelper.initObjectFields = parent.FieldObjects;
        positionsByCompanyType();
        if (state.EPOSTransactionBO.EmployedFrom != null) {
            var EmployedFromDate = moment(state.EPOSTransactionBO.EmployedFrom, "YYYY-MM-DD");
            // var EmployedFromDate = moment(new Date(state.EPOSTransactionBO.EmployedFrom));
            setEmployedFrom(EmployedFromDate);
        }
        if (refScroll5) {
            refScroll5.current.scrollToPosition(0, 0, false);
        }
    }, [parent.currentPosition]);
    useEffect(() => {
        dispatch(locationAction.getDataProvince())
        setProvince(locationReducer.dataProvince);
    }, [])
    useEffect(() => {
        if (state.EPOSTransactionBO.ProvinceIDCompany > 0) {
            setIsShowIndicator(true);
            locationAction.getDistrict(state.EPOSTransactionBO.ProvinceIDCompany)
                .then((res) => {
                    setDistrict(res);
                    setIndexPager(1);
                    setIsShowIndicator(false);
                })
                .catch(error => {
                    Alert.alert(translate('common.notification_uppercase'), error.msgError,
                        [
                            {
                                text: "OK",
                                style: "cancel",
                                onPress: () => setIsShowIndicator(false)
                            }
                        ]
                    )
                });
        }
    }, [state.EPOSTransactionBO.ProvinceIDCompany])
    useEffect(() => {
        if (state.EPOSTransactionBO.DistrictIDCompany > 0) {
            setIsShowIndicator(true);
            locationAction.getWard(state.EPOSTransactionBO.ProvinceIDCompany, state.EPOSTransactionBO.DistrictIDCompany)
                .then((res) => {
                    setWard(res);
                    setIndexPager(2);
                    setIsShowIndicator(false);
                })
                .catch(error => {
                    Alert.alert(translate('common.notification_uppercase'), error.msgError,
                        [
                            {
                                text: "OK",
                                style: "cancel",
                                onPress: () => setIsShowIndicator(false)
                            }
                        ]
                    )
                });
        }
    }, [state.EPOSTransactionBO.DistrictIDCompany])
    useEffect(() => {
        positionsByCompanyType();
    }, [state.EPOSTransactionBO.client_CompanyType])

    const checkValidateData = () => {
        //trim data
        state.EPOSTransactionBO.HouseNumberCompany = state.EPOSTransactionBO.HouseNumberCompany?.trim()

        var FieldObjectsByStep = installmentHelper.initObjectFields.filter(item => item.Step == parent.currentPosition);
        for (var i = 0; i < FieldObjectsByStep.length; i++) {
            var item = FieldObjectsByStep[i];
            if (!validateObject(item, state.EPOSTransactionBO)) {
                return false;
            }
        };
        return true;
    }

    const positionsByCompanyType = () => {
        if (state.EPOSTransactionBO) {
            var companyTypeSelected = CompanyTypeList.find(x => x.InformationID == state.EPOSTransactionBO.client_CompanyType)
            setPositionList([]);
            if (companyTypeSelected) {
                console.log("companyTypeSelected: ", companyTypeSelected);
                var getPositions = AllPositionList.filter(x => x.InformationAPI == companyTypeSelected.InformationAPI);
                console.log("getPositions: ", getPositions);
                setPositionList(getPositions);
            } else {
                setPositionList(AllPositionList);
            }
        }
    }

    const ChangeSocialStatus = function (item) {
        if (item != undefined && state.EPOSTransactionBO.client_ListSocialStatus != null && state.EPOSTransactionBO.client_ListSocialStatus.length > 0 || isCheckFE || isCheckACS || isCheckHC || isCheckMC || isCheckMAFC || isCheckShihan) {
            if (state.EPOSTransactionBO.client_ListSocialStatus?.indexOf("," + item.InformationAPI + ",") > -1 || isCheckFE || isCheckACS || isCheckHC || isCheckMC || isCheckMAFC || isCheckShihan) {
                if (state.EPOSTransactionBO.PartnerInstallmentID == PartnerInstallmentID_MC) {
                    var varEmployedFrom_CompanyName = item.InformationAPI == 1 || item.InformationAPI == 2 || item.InformationAPI == 10 ? true : false;
                    var varCompanyPosition = item.InformationAPI == 1 || item.InformationAPI == 2 ? true : false;
                    OnOffIsRequired('CompanyName', varEmployedFrom_CompanyName);
                    OnOffIsRequired('StreetCompany', true);
                    OnOffIsRequired('ProvinceIDCompany', true);
                    OnOffIsRequired('DistrictIDCompany', true);
                    OnOffIsRequired('WardIDCompany', true);
                    OnOffIsRequired('CompanyPosition', varCompanyPosition);
                    OnOffIsRequired('EmployedFrom', varEmployedFrom_CompanyName);
                } else if (isCheckFE || isCheckACS || isCheckHC || isCheckMC || isCheckMAFC || isCheckShihan) {
                    var varChangeSocial_Status = item.InformationAPI == 3 || item.InformationAPI == 8 || item.InformationAPI == 5 ? true : false;
                    OnOffIsRequired('CompanyName', varChangeSocial_Status);
                    OnOffIsRequired('StreetCompany', varChangeSocial_Status);
                    OnOffIsRequired('ProvinceIDCompany', varChangeSocial_Status);
                    OnOffIsRequired('DistrictIDCompany', varChangeSocial_Status);
                    OnOffIsRequired('WardIDCompany', varChangeSocial_Status);
                    OnOffIsRequired('CompanyPosition', varChangeSocial_Status);
                    OnOffIsRequired('EmployedFrom', varChangeSocial_Status);
                }
                else {
                    OnOffIsRequired('CompanyName', true);
                    OnOffIsRequired('StreetCompany', true);
                    OnOffIsRequired('ProvinceIDCompany', true);
                    OnOffIsRequired('DistrictIDCompany', true);
                    OnOffIsRequired('WardIDCompany', true);
                    OnOffIsRequired('CompanyPosition', true);
                    OnOffIsRequired('EmployedFrom', true);
                }
            } else {
                OnOffIsRequired('CompanyName', false);
                OnOffIsRequired('StreetCompany', false);
                OnOffIsRequired('ProvinceIDCompany', false);
                OnOffIsRequired('DistrictIDCompany', false);
                OnOffIsRequired('WardIDCompany', false);
                OnOffIsRequired('CompanyPosition', false);
                OnOffIsRequired('EmployedFrom', false);
            }
            installmentHelper.initObjectFields = parent.FieldObjects;
            updateparent(parent);
        }
    }

    const OnOffIsRequired = function (fieldname, value) {
        if (parent.FieldObjects) {
            var idx = parent.FieldObjects.findIndex(p => p.FieldName === fieldname);
            if (idx >= 0) {
                parent.FieldObjects[idx].IsRequired = value;
            }
        }
    }

    const removeDataCacheEP = () => {
        var dataCacheList = dataCacheInstallmentReducer.lstCacheDataEP.data;
        if (dataCacheList != null && dataCacheList.length > 0) {
            var idx = dataCacheList.findIndex(p => p.EPTransactionID === state.EPOSTransactionBO.EPTransactionID);
            if (idx >= 0) {
                dataCacheList.splice(idx, 1);
                dispatch(dataCacheInstallmentAction.DeleteAndCreatedDataCacheEPByStep(dataCacheList));
            }
        }
    };


    const updateEPosTransactionNew = () => {
        showBlockUI();
        const {
            EPOSTransactionBO,
        } = state;
        const { TotalPrepaid } = EPOSTransactionBO;
        EPOSTransactionBO.TotalPrepaid = TotalPrepaid ? parseFloat(TotalPrepaid) : 0;

        if (!CheckShowReferenIsHusbandOrWife('client_ReferenceHusbandOrWifeName')) {
            EPOSTransactionBO.client_ReferenceHusbandOrWifeName = "";
        }

        console.log("updateEPosTransactionNew: ", EPOSTransactionBO);
        console.log("updateEPosTransactionNew: ", GroupPaperTypeInforBOList);

        if (!helper.isNumber(EPOSTransactionBO.NumberOfChildren)
            && !helper.IsNonEmptyString(EPOSTransactionBO.NumberOfChildren)) {
            EPOSTransactionBO.NumberOfChildren = 0;
        }

        if (!helper.isNumber(EPOSTransactionBO.MonthlyPaymentLoan)
            && !helper.IsNonEmptyString(EPOSTransactionBO.MonthlyPaymentLoan)) {
            EPOSTransactionBO.MonthlyPaymentLoan = 0;
        }

        if (!helper.IsNonEmptyString(EPOSTransactionBO.Note)) {
            EPOSTransactionBO.Note = ".";
        }


        let data = {
            EPOSTransactionBO: EPOSTransactionBO,
            GroupPaperTypeInforBOList: GroupPaperTypeInforBOList,
            times: isCreatedSt ? 1 : 2
        };
        dispatch(installmentAction.updateEPosTransactionNew(data))
            .then(res => {
                console.log("client_StepSendOTP: ", res);
                setBlock(false);
                hideBlockUI();
                removeDataCacheEP();
                const EPOSTransactionBO = JSON.parse(res.EPOSTransactionBO);
                const { client_StepSendOTP } = EPOSTransactionBO;
                console.log("client_StepSendOTP: ", client_StepSendOTP);
                if (client_StepSendOTP) {
                    updatestate(EPOSTransactionBO);
                    onNext();
                }
                else {
                    Alert.alert("", translate('instalmentManager.update_record_success'),
                        [
                            {
                                text: "OK",
                                style: "default",
                                onPress: () => {
                                    SearchDataInstallment(state.EPOSTransactionBO.EPOSTransactionID);
                                    //goBack();
                                }
                            }
                        ]
                    );
                }
            })
            .catch(error => {
                setBlock(false);
                hideBlockUI();
                const { errorType, msgError } = error;
                if (errorType == 1) {
                    Alert.alert(translate('common.notification'), msgError, [
                        {
                            text: translate('instalmentManager.btn_cancel'),
                            onPress: () => { }
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            onPress: () => {
                                updateEPosTransactionNew();
                            },
                        },
                    ]);
                }
                else if (errorType == 3) {
                    Alert.alert(translate('common.notification'), msgError, [
                        {
                            text: "OK",
                            onPress: () => {
                                //hideBlockUI();
                                removeDataCacheEP();
                                SearchDataInstallment(state.EPOSTransactionBO.EPOSTransactionID);
                            },
                        },
                    ]);
                }
                else {
                    Alert.alert(translate('common.notification'), msgError, [
                        {
                            text: "OK",
                            onPress: () => {
                                //hideBlockUI();
                                if (helper.IsNonEmptyString(msgError) && msgError.includes("đối tác")) {
                                    SearchDataInstallment(state.EPOSTransactionBO.EPOSTransactionID);
                                }
                            },
                        },
                    ]);
                }
            });
    };

    const updateBroadcastTransactionNew = () => {
        showBlockUI();
        const {
            EPOSTransactionBO,
        } = state;
        const { TotalPrepaid } = EPOSTransactionBO;
        EPOSTransactionBO.TotalPrepaid = TotalPrepaid ? parseFloat(TotalPrepaid) : 0;

        if (!CheckShowReferenIsHusbandOrWife('client_ReferenceHusbandOrWifeName')) {
            EPOSTransactionBO.client_ReferenceHusbandOrWifeName = "";
        }

        if (!helper.isNumber(EPOSTransactionBO.NumberOfChildren)
            && !helper.IsNonEmptyString(EPOSTransactionBO.NumberOfChildren)) {
            EPOSTransactionBO.NumberOfChildren = 0;
        }

        if (!helper.isNumber(EPOSTransactionBO.MonthlyPaymentLoan)
            && !helper.IsNonEmptyString(EPOSTransactionBO.MonthlyPaymentLoan)) {
            EPOSTransactionBO.MonthlyPaymentLoan = 0;
        }

        if (!helper.IsNonEmptyString(EPOSTransactionBO.Note)) {
            EPOSTransactionBO.Note = ".";
        }

        const listBroadcastSaleProgram = broadcastSaleProgram.filter(element =>
            !helper.IsEmptyObject(element.broadcastSaleProgramData)
        ).map(element =>
            element.broadcastSaleProgramData.saleProgram
        );

        let data = {
            EPOSTransactionBO,
            listGroupPaperTypeInforBO: GroupPaperTypeInforBOList,
            listSaleProgramBO: listBroadcastSaleProgram,
            times: isCreatedSt ? 1 : 2
        };
        dispatch(installmentAction.updateBroadcastTransactionNew(data))
            .then(EPOSTransactionBO => {
                setBlock(false);
                hideBlockUI();
                removeDataCacheEP();
                const { client_StepSendOTP } = EPOSTransactionBO;
                if (client_StepSendOTP) {
                    updatestate(EPOSTransactionBO);
                    onNext();
                }
                else {
                    Alert.alert("", translate('instalmentManager.update_record_success'),
                        [
                            {
                                text: "OK",
                                style: "default",
                                onPress: () => {
                                    SearchDataInstallment(state.EPOSTransactionBO.EPOSTransactionID);
                                    //goBack();
                                }
                            }
                        ]
                    );
                }
            })
            .catch(error => {
                setBlock(false);
                hideBlockUI();
                const { errorType, msgError } = error;
                if (errorType == 1) {
                    Alert.alert(translate('common.notification'), msgError, [
                        {
                            text: translate('instalmentManager.btn_cancel'),
                            onPress: () => { }
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            onPress: () => {
                                updateBroadcastTransactionNew();
                            },
                        },
                    ]);
                }
                else if (errorType == 3) {
                    Alert.alert(translate('common.notification'), msgError, [
                        {
                            text: "OK",
                            onPress: () => {
                                //hideBlockUI();
                                removeDataCacheEP();
                                SearchDataInstallment(state.EPOSTransactionBO.EPOSTransactionID);
                            },
                        },
                    ]);
                }
                else {
                    Alert.alert(translate('common.notification'), msgError, [
                        {
                            text: "OK",
                            onPress: () => {
                                //hideBlockUI();
                                if (helper.IsNonEmptyString(msgError) && msgError.includes("đối tác")) {
                                    SearchDataInstallment(state.EPOSTransactionBO.EPOSTransactionID);
                                }
                            },
                        },
                    ]);
                }
            });
    };

    const CheckShowReferenIsHusbandOrWife = function (fieldname) {
        if (installmentHelper.initObjectFields) {
            console.log("EPOSTransactionBO: ", state?.EPOSTransactionBO?.cus_EPOSTransactionReferenceBOList?.[0]?.InformationID);
            var idx = installmentHelper.initObjectFields.findIndex(p => p.FieldName === fieldname);
            if (idx >= 0) {
                if (client_ListStatusMarried?.length > 0
                    && client_ListStatusMarried.indexOf("<" + state.EPOSTransactionBO.client_MaritalStatus + ">") != -1
                    && state.EPOSTransactionBO.cus_EPOSTransactionReferenceBOList[0].InformationID != state.EPOSTransactionBO.client_InformationHusbandOrWifeID
                    && state.EPOSTransactionBO.cus_EPOSTransactionReferenceBOList[1].InformationID != state.EPOSTransactionBO.client_InformationHusbandOrWifeID) {
                    installmentHelper.initObjectFields[idx].IsRequired = true;
                    console.log("vo1");
                    return true;
                } else {
                    installmentHelper.initObjectFields[idx].IsRequired = false;
                    console.log("vo2");
                    return false;
                }
            } else {
                console.log("vo3");
                return false;
            }
        } else {
            console.log("vo4");
            return false;
        }
    };

    return (
        <KeyboardAwareScrollView
            ref={refScroll5}
            enableResetScrollToCoords={false}
            keyboardShouldPersistTaps="always"
            bounces={false}
            overScrollMode="always"
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            extraScrollHeight={60} >
            <View style={{
                paddingHorizontal: 10,
                width: constants.width
            }}
                activeOpacity={0.7}
            >
                {installmentHelper.CheckShowField("client_MaritalStatus") ? (
                    <View style={{ marginVertical: 5 }}>
                        <MyText style={{ fontWeight: '700', fontStyle: 'italic', fontSize: 13 }}
                            text={installmentHelper.objField.FieldDescription}
                            children={<MyText text={installmentHelper.CheckIsRequiredField("client_MaritalStatus") ? "*" : ""} style={{ color: COLORS.txtFF0000, fontSize: 16 }} />}
                        />
                        <View style={[styles.view_picker]}>
                            <Picker
                                label={"InformationName"}
                                value={"InformationID"}
                                data={MaritalStatusList}
                                valueSelected={state.EPOSTransactionBO.client_MaritalStatus}
                                onChange={(item) => {
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            client_MaritalStatus: item.InformationID
                                        }
                                    });
                                }}
                                defaultLabel={translate('instalmentManager.header_marriage_status')}
                                style={{
                                    flex: 1,
                                    flexDirection: "row",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    height: 27,
                                    marginVertical: 5,
                                }}
                            />
                        </View>
                    </View>
                ) : null}
                {installmentHelper.CheckShowField("NumberOfChildren") ? (
                    <View style={{ marginTop: 5 }}>
                        <View style={{
                            width: constants.width - 20,
                            flexDirection: "row",
                            alignItems: "center"
                        }}>
                            <TooltipWrapper
                                placement={"bottom"}
                                content={
                                    <MyText style={{ color: COLORS.txtFFFFFF }}
                                        text={"Là số con của KH (bao gồm con ruột và con nuôi)"}
                                    />
                                }
                                wrapper={
                                    <View style={{ flexDirection: "row", alignItems: "center", width: constants.getSize(100) }}>
                                        <MyText style={{ fontWeight: '700', fontStyle: 'italic' }}
                                            text={installmentHelper.objField.FieldDescription}
                                            addSize={-1.5}
                                            children={<MyText text={installmentHelper.CheckIsRequiredField('NumberOfChildren') ? "*" : ""} style={{ color: COLORS.txtFF0000, fontSize: 16 }} />}
                                        />
                                        <Icon
                                            iconSet={"FontAwesome"}
                                            name={"question-circle"}
                                            size={16}
                                            color={COLORS.txtFF0000}
                                            style={{
                                                marginLeft: 3
                                            }}
                                        />
                                    </View>
                                }
                            />
                        </View>
                        <TitleInput
                            // title={installmentHelper.objField.FieldDescription}
                            // isRequired={installmentHelper.CheckIsRequiredField('NumberOfChildren')}
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                paddingVertical: 8,
                            }}
                            placeholder={translate('instalmentManager.enter') + installmentHelper.objField.FieldDescription.toLowerCase()}
                            value={`${state.EPOSTransactionBO.NumberOfChildren}`}
                            onChangeText={(text) => {
                                if (text == "" || text == "0") {
                                    if (text == "") {
                                        setState({
                                            ...state,
                                            EPOSTransactionBO: {
                                                ...state.EPOSTransactionBO,
                                                NumberOfChildren: ""
                                            },
                                        });
                                    } else if (text == 0) {
                                        setState({
                                            ...state,
                                            EPOSTransactionBO: {
                                                ...state.EPOSTransactionBO,
                                                NumberOfChildren: 0
                                            },
                                        });
                                    }
                                } else {
                                    text = parseInt(text, 10);
                                    let validateNumber = new RegExp(/^[0-9\b]+$/);
                                    if (validateNumber.test(text) || text == "") {
                                        setState({
                                            ...state,
                                            EPOSTransactionBO:
                                            {
                                                ...state.EPOSTransactionBO,
                                                NumberOfChildren: text.toString()
                                            }
                                        });
                                    }
                                }
                            }}
                            maxLength={2}
                            keyboardType={"numeric"}
                            returnKeyType={"done"}
                            onSubmitEditing={Keyboard.dismiss}
                            blurOnSubmit={true}
                            width={constants.width - 20}
                            height={40}
                            clearText={() => {
                                setState({
                                    ...state,
                                    EPOSTransactionBO: {
                                        ...state.EPOSTransactionBO,
                                        NumberOfChildren: ""
                                    },
                                });
                            }}
                            key={"NumberOfChildren"}
                        />
                    </View>
                ) : null}
                {installmentHelper.CheckShowField("client_Education") ? (
                    <View style={{ marginVertical: 5 }}>
                        <MyText style={{ fontWeight: '700', fontStyle: 'italic', fontSize: 13 }}
                            text={installmentHelper.objField.FieldDescription}
                            children={<MyText text={installmentHelper.CheckIsRequiredField("client_Education") ? "*" : ""} style={{ color: COLORS.txtFF0000, fontSize: 16 }} />}
                        />
                        <View style={[styles.view_picker]}>
                            <Picker
                                label={"InformationName"}
                                value={"InformationID"}
                                data={EducationList}
                                valueSelected={state.EPOSTransactionBO.client_Education}
                                onChange={(item) => {
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            client_Education: item.InformationID,
                                        }
                                    });
                                }}
                                defaultLabel={translate('instalmentManager.header_academic_standard')}
                                style={{
                                    flex: 1,
                                    flexDirection: "row",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    height: 27,
                                    marginVertical: 5,
                                }}
                            />
                        </View>
                    </View>
                ) : null}
                {installmentHelper.CheckShowField("client_SocialStatus") ? (
                    <View style={{ marginVertical: 5 }}>
                        <MyText style={{ fontWeight: '700', fontStyle: 'italic', fontSize: 13 }}
                            text={installmentHelper.objField.FieldDescription}
                            children={<MyText text={installmentHelper.CheckIsRequiredField("client_SocialStatus") ? "*" : ""} style={{ color: COLORS.txtFF0000, fontSize: 16 }} />}
                        />
                        <View style={[styles.view_picker]}>
                            <Picker
                                label={"InformationName"}
                                value={"InformationID"}
                                data={SocialStatusList}
                                valueSelected={state.EPOSTransactionBO.client_SocialStatus}
                                onChange={(item) => {
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            client_SocialStatus: item.InformationID,
                                        }
                                    });
                                    ChangeSocialStatus(item);
                                }}
                                defaultLabel={translate('instalmentManager.header_job')}
                                style={{
                                    flex: 1,
                                    flexDirection: "row",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    height: 27,
                                    marginVertical: 5,
                                }}
                            />
                        </View>
                    </View>
                ) : null}
                {installmentHelper.CheckShowField("client_Career") ? (
                    <View style={{ marginVertical: 5 }}>
                        <MyText style={{ fontWeight: '700', fontStyle: 'italic', fontSize: 13 }}
                            text={installmentHelper.objField.FieldDescription}
                            children={<MyText text={installmentHelper.CheckIsRequiredField("client_Career") ? "*" : ""} style={{ color: COLORS.txtFF0000, fontSize: 16 }} />}
                        />
                        <View style={[styles.view_picker]}>
                            <Picker
                                label={"InformationName"}
                                value={"InformationID"}
                                data={CareerList}
                                valueSelected={state.EPOSTransactionBO.client_Career}
                                onChange={(item) => {
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            client_Career: item.InformationID,
                                        }
                                    });
                                }}
                                defaultLabel={"--Ngành nghề--"}
                                style={{
                                    flex: 1,
                                    flexDirection: "row",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    height: 27,
                                    marginVertical: 5,
                                }}
                            />
                        </View>
                    </View>
                ) : null}
                {installmentHelper.CheckShowField("client_CompanyType") ? (
                    <View style={{ marginVertical: 5 }}>
                        <MyText style={{ fontWeight: '700', fontStyle: 'italic', fontSize: 13 }}
                            text={installmentHelper.objField.FieldDescription}
                            children={<MyText text={installmentHelper.CheckIsRequiredField("client_CompanyType") ? "*" : ""} style={{ color: COLORS.txtFF0000, fontSize: 16 }} />}
                        />
                        <View style={[styles.view_picker]}>
                            <Picker
                                label={"InformationName"}
                                value={"InformationID"}
                                data={CompanyTypeList}
                                valueSelected={state.EPOSTransactionBO.client_CompanyType}
                                onChange={(item) => {
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            client_CompanyType: item.InformationID,
                                            CompanyPosition: -1
                                        }
                                    });
                                }}
                                defaultLabel={translate('instalmentManager.header_company_type')}
                                style={{
                                    flex: 1,
                                    flexDirection: "row",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    height: 27,
                                    marginVertical: 5,
                                }}
                            />
                        </View>
                    </View>
                ) : null}
                {installmentHelper.CheckShowField("CompanyName") ? (
                    <View style={{ marginTop: 3 }}>

                        <View style={{
                            width: constants.width - 20,
                            flexDirection: "row",
                            alignItems: "center"
                        }}>
                            <TooltipWrapper
                                placement={"bottom"}
                                content={
                                    <MyText style={{ color: COLORS.txtFFFFFF }}
                                        text={"Đối với khách hàng làm nghề tự do thì nhập thông tin tên nghề nghiệp (Ví dụ: Bán Trái Cây, ...)"}
                                    />
                                }
                                wrapper={
                                    <View style={{ flexDirection: "row", alignItems: "center" }}>
                                        <MyText style={{ fontWeight: '700', fontStyle: 'italic' }}
                                            text={installmentHelper.objField.FieldDescription}
                                            addSize={-1.5}
                                            children={<MyText text={installmentHelper.CheckIsRequiredField('CompanyName') ? "*" : ""} style={{ color: COLORS.txtFF0000, fontSize: 16 }} />}
                                        />
                                        <Icon
                                            iconSet={"FontAwesome"}
                                            name={"question-circle"}
                                            size={16}
                                            color={COLORS.txtFF0000}
                                            style={{
                                                marginLeft: 3
                                            }}
                                        />
                                    </View>
                                }
                            />
                            <MyText
                                style={{
                                    fontSize: 14,
                                    color: 'red',
                                    fontStyle: 'italic',
                                    marginLeft: 5
                                }}
                                text="(Không nhập ký tự đặc biệt)"
                            />
                        </View>

                        <TitleInput
                            // title={installmentHelper.objField.FieldDescription}
                            // isRequired={installmentHelper.CheckIsRequiredField("CompanyName")}
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                paddingVertical: 8,
                                marginTop: -10
                            }}
                            placeholder={translate('instalmentManager.enter') + installmentHelper.objField.FieldDescription.toLowerCase()}
                            value={state.EPOSTransactionBO.CompanyName}
                            onChangeText={(text) => {
                                setState({
                                    ...state,
                                    EPOSTransactionBO:
                                    {
                                        ...state.EPOSTransactionBO,
                                        CompanyName: text
                                    }
                                });
                            }}
                            keyboardType={"default"}
                            returnKeyType={"done"}
                            blurOnSubmit={true}
                            onSubmitEditing={Keyboard.dismiss}
                            width={constants.width - 20}
                            height={40}
                            clearText={() => {
                                Keyboard.dismiss();
                                setState({
                                    ...state,
                                    EPOSTransactionBO:
                                    {
                                        ...state.EPOSTransactionBO,
                                        CompanyName: ""
                                    }
                                });
                            }}
                            maxLength={60}
                            //editable={!isLockName}
                            key={"CompanyName"}
                        />
                    </View>
                ) : null}
                {
                    <View style={styles.tab_list}>
                        <View style={[styles.header_title, {
                            flexDirection: "row",
                            alignItems: "center", justifyContent: "flex-start"
                        }]}>
                            <MyText
                                style={{
                                    //width: 40,
                                    fontWeight: 'bold',
                                    color: COLORS.txtFFFFFF,
                                    fontSize: 14,
                                    color: COLORS.txt4DA6FF
                                }}
                                text={translate('instalmentManager.company_address')}
                                children={<MyText text={installmentHelper.CheckIsRequiredField("StreetCompany") ? "*" : ""} style={{ color: COLORS.txtFF0000, fontSize: 16 }} />}
                            />
                        </View>
                    </View>
                }
                {installmentHelper.CheckShowField("HouseNumberCompany") ? (
                    <View style={{ marginVertical: 5 }}>
                        <TitleInput
                            title={translate('instalmentManager.house_number')}
                            isRequired={installmentHelper.CheckIsRequiredField("HouseNumberCompany")}
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                paddingVertical: 8,
                            }}
                            placeholder={translate('instalmentManager.enter_house_number')}
                            value={state.EPOSTransactionBO.HouseNumberCompany}
                            onChangeText={(text) => {
                                var hasObj = installmentHelper.GetObjField("HouseNumberCompany");
                                //installmentHelper.objField.CharacterInputValue = "^[a-zA-Z0-9À-ȕẠ-ỹ\xC0-\xFF.,-/' ]{0,20}$" //ACS
                                if (hasObj && installmentHelper.isValidateInput(installmentHelper.objField.CharacterInputValue, text)) {
                                    setState({
                                        ...state,
                                        EPOSTransactionBO:
                                        {
                                            ...state.EPOSTransactionBO,
                                            HouseNumberCompany: text
                                        }
                                    });
                                }
                                else {
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            HouseNumberCompany: state.EPOSTransactionBO.HouseNumberCompany ?? ""
                                        }
                                    });
                                }
                            }}
                            keyboardType={"default"}
                            returnKeyType={"done"}
                            blurOnSubmit={true}
                            onSubmitEditing={Keyboard.dismiss}
                            width={constants.width - 20}
                            height={40}
                            clearText={() => {
                                Keyboard.dismiss();
                                setState({
                                    ...state,
                                    EPOSTransactionBO:
                                    {
                                        ...state.EPOSTransactionBO,
                                        HouseNumberCompany: ""
                                    }
                                });
                            }}
                            maxLength={60}
                            //editable={!isLockName}
                            key={"HouseNumberCompany"}
                        />
                    </View>
                ) : null}
                {installmentHelper.CheckShowField("StreetCompany") ? (
                    <View style={{ marginTop: -3 }}>
                        <TitleInput
                            title={translate('instalmentManager.street')}
                            isRequired={installmentHelper.CheckIsRequiredField("StreetCompany")}
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                paddingVertical: 8,
                            }}
                            placeholder={translate('instalmentManager.enter_street')}
                            value={state.EPOSTransactionBO.StreetCompany}
                            onChangeText={(text) => {
                                var hasObj = installmentHelper.GetObjField("StreetCompany");
                                //installmentHelper.objField.CharacterInputValue = "^[a-zA-Z0-9À-ȕẠ-ỹ\xC0-\xFF.,-/' ]{0,40}$" //ACS
                                if (hasObj && installmentHelper.isValidateInput(installmentHelper.objField.CharacterInputValue, text)) {
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            StreetCompany: text
                                        }
                                    });
                                }
                                else {
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            StreetCompany: state.EPOSTransactionBO.StreetCompany ?? ""
                                        }
                                    });
                                }
                            }}
                            keyboardType={"default"}
                            returnKeyType={"done"}
                            blurOnSubmit={true}
                            onSubmitEditing={Keyboard.dismiss}
                            width={constants.width - 20}
                            height={40}
                            clearText={() => {
                                setState({
                                    ...state,
                                    EPOSTransactionBO: {
                                        ...state.EPOSTransactionBO,
                                        StreetCompany: ""
                                    }
                                });
                            }}
                            //editable={!isLockName}
                            key={"StreetCompany"}
                        />
                    </View>
                ) : null}
                {
                    <View style={{ marginTop: 5 }}>
                        <PickerLocation
                            dataProvince={{
                                data: locationReducer.dataProvince,
                                id: "provinceID",
                                value: "provinceName"
                            }}
                            dataDistrict={{
                                data: district,
                                id: "districtID",
                                value: "districtName"
                            }}
                            dataWard={{
                                data: ward,
                                id: "wardID",
                                value: "wardName"
                            }}
                            provinceID={state.EPOSTransactionBO.ProvinceIDCompany}
                            districtID={state.EPOSTransactionBO.DistrictIDCompany}
                            wardID={state.EPOSTransactionBO.WardIDCompany}
                            onSelectProvince={(item) => {
                                setState({
                                    ...state,
                                    EPOSTransactionBO: {
                                        ...state.EPOSTransactionBO,
                                        ProvinceIDCompany: item.provinceID,
                                        DistrictIDCompany: -1,
                                        WardIDCompany: -1,
                                    }
                                })
                            }}
                            onSelectDistrict={(item) => {
                                setState({
                                    ...state,
                                    EPOSTransactionBO: {
                                        ...state.EPOSTransactionBO,
                                        DistrictIDCompany: item.districtID,
                                        WardIDCompany: -1,
                                    }
                                })
                            }}
                            onSelectWard={(item) => {
                                setState({
                                    ...state,
                                    EPOSTransactionBO: {
                                        ...state.EPOSTransactionBO,
                                        WardIDCompany: item.wardID,
                                    }
                                })
                            }}
                            indexPager={indexPager}
                            onShowPicker={(index) => {
                                setIndexPager(index);
                            }}
                            updatePager={(index) => {
                                setIndexPager(index);
                            }}
                            isShowIndicator={isShowIndicator}
                        />
                    </View>
                }
                {installmentHelper.CheckShowField("CompanyPosition") ? (
                    <View>
                        <MyText style={{ fontWeight: '700', fontStyle: 'italic', fontSize: 13 }}
                            text={installmentHelper.objField.FieldDescription}
                            children={<MyText text={installmentHelper.CheckIsRequiredField("CompanyPosition") ? "*" : ""} style={{ color: COLORS.txtFF0000, fontSize: 16 }} />}
                        />
                        <View style={[styles.view_picker]}>
                            <Picker
                                label={"InformationName"}
                                value={"InformationID"}
                                data={PositionList}
                                valueSelected={state.EPOSTransactionBO.CompanyPosition}
                                onChange={(item) => {
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            CompanyPosition: item.InformationID,
                                        }
                                    });
                                }}
                                defaultLabel={translate('instalmentManager.header_position')}
                                style={{
                                    flex: 1,
                                    flexDirection: "row",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    height: 27,
                                    marginVertical: 5,
                                }}
                            />
                        </View>
                    </View>
                ) : null}
                {installmentHelper.CheckShowField("EmployedFrom") ? (
                    <View style={{ marginTop: 3 }}>
                        <MyText style={{ fontWeight: '700', fontStyle: 'italic', fontSize: 13 }}
                            text={translate('instalmentManager.date_start')}
                            children={<MyText text={installmentHelper.CheckIsRequiredField("EmployedFrom") ? "*" : ""} style={{ color: COLORS.txtFF0000, fontSize: 16 }} />}
                        />
                        <View style={styles.wrapperInputUserCreated}>
                            <DatePicker
                                date={EmployedFrom}
                                format={'YYYY-MM-DD'}
                                onDateChange={(dateStr) => {
                                    setEmployedFrom(dateStr);
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            // EmployedFrom: new Date(newdate),
                                            EmployedFrom: `${dateStr}T00:00:00`,
                                        }
                                    });
                                }}
                            />
                        </View>
                    </View>
                ) : null}
                {installmentHelper.CheckShowField("CompanyRegisterCode") ? (
                    <View style={{ marginVertical: 5 }}>
                        <TitleInput
                            title={installmentHelper.objField.FieldDescription}
                            isRequired={installmentHelper.CheckIsRequiredField("CompanyRegisterCode")}
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                marginBottom: 5,
                                paddingHorizontal: 10,
                                paddingVertical: 8,
                            }}
                            placeholder={translate('instalmentManager.enter') + installmentHelper.objField.FieldDescription.toLowerCase()}
                            value={state.EPOSTransactionBO.CompanyRegisterCode}
                            onChangeText={(text) => {
                                var hasObj = installmentHelper.GetObjField("CompanyRegisterCode");
                                if (hasObj && installmentHelper.isValidateInput(installmentHelper.objField.CharacterInputValue, text)) {
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            CompanyRegisterCode: text
                                        }
                                    });
                                }
                                else {
                                    setState({
                                        ...state,
                                        EPOSTransactionBO: {
                                            ...state.EPOSTransactionBO,
                                            CompanyRegisterCode: state.EPOSTransactionBO.CompanyRegisterCode ?? ""
                                        }
                                    });
                                }
                            }}
                            keyboardType={"default"}
                            returnKeyType={"done"}
                            blurOnSubmit={true}
                            onSubmitEditing={Keyboard.dismiss}
                            width={constants.width - 20}
                            height={40}
                            clearText={() => {
                                setState({
                                    ...state,
                                    EPOSTransactionBO: {
                                        ...state.EPOSTransactionBO,
                                        CompanyRegisterCode: ""
                                    }
                                });
                            }}
                            //editable={!isLockName}
                            key={"CompanyRegisterCode"}
                        />
                    </View>
                ) : null}
                <View
                    style={{
                        flexDirection: "row",
                        justifyContent: "space-between",
                        paddingHorizontal: 20,
                        marginVertical: 10,
                    }}
                >
                    <TouchableOpacity
                        style={[styles.btn, { backgroundColor: COLORS.btnFFFFFF, }]}
                        activeOpacity={0.7}
                        onPress={() => {
                            updateprevstate(state.EPOSTransactionBO);
                            onPrev();
                        }}
                    >
                        <MyText text={translate('common.btn_back')} style={{ fontWeight: "bold", backgroundColor: COLORS.btnFFFFFF, color: COLORS.txt4DA6FF }} />
                    </TouchableOpacity>
                    <TouchableOpacity
                        style={[styles.btn, { backgroundColor: COLORS.btn4DA6FF }]}
                        activeOpacity={0.7}
                        onPress={() => {
                            if (checkValidateData()) {
                                if ((state.EPOSTransactionBO.client_CompanyType == 30 && state.EPOSTransactionBO.CompanyRegisterCode == "")) {
                                    Alert.alert(
                                        "THÔNG BÁO",
                                        "Vui lòng nhập thông tin [Giấy phép kinh doanh] ở bước 5",
                                        [
                                            {
                                                text: "Đóng",
                                                onPress: () => { }
                                            }
                                        ]
                                    );
                                }
                                else if (state.EPOSTransactionBO.client_Career != '' && state.EPOSTransactionBO.CompanyName == '' && (state.EPOSTransactionBO.PartnerInstallmentID == 10 || isBroadcastInstalment)) {
                                    Alert.alert(
                                        "THÔNG BÁO",
                                        "Vui lòng nhập thông tin [Tên công ty] ở bước 5",
                                        [
                                            {
                                                text: "Đóng",
                                                onPress: () => { }
                                            }
                                        ]
                                    );
                                }
                                else if (state.EPOSTransactionBO.client_Career != '' && state.EPOSTransactionBO.HouseNumberCompany == null && (state.EPOSTransactionBO.PartnerInstallmentID == 10 || isBroadcastInstalment)) {
                                    Alert.alert(
                                        "THÔNG BÁO",
                                        "Vui lòng nhập thông tin [Số nhà] ở bước 5",
                                        [
                                            {
                                                text: "Đóng",
                                                onPress: () => { }
                                            }
                                        ]
                                    );
                                }
                                else if (state.EPOSTransactionBO.client_Career != '' && state.EPOSTransactionBO.StreetCompany == null && (state.EPOSTransactionBO.PartnerInstallmentID == 10 || isBroadcastInstalment)) {
                                    Alert.alert(
                                        "THÔNG BÁO",
                                        "Vui lòng nhập thông tin [Đường/Ấp] ở bước 5",
                                        [
                                            {
                                                text: "Đóng",
                                                onPress: () => { }
                                            }
                                        ]
                                    );
                                }
                                else if (state.EPOSTransactionBO.client_Career != '' && state.EPOSTransactionBO.ProvinceIDCompany == -1 && (state.EPOSTransactionBO.PartnerInstallmentID == 10 || isBroadcastInstalment)) {
                                    Alert.alert(
                                        "THÔNG BÁO",
                                        "Vui lòng chọn thông tin [Tỉnh/Thành] ở bước 5",
                                        [
                                            {
                                                text: "Đóng",
                                                onPress: () => { }
                                            }
                                        ]
                                    );
                                }
                                else if (state.EPOSTransactionBO.client_Career != '' && state.EPOSTransactionBO.DistrictIDCompany == -1 && (state.EPOSTransactionBO.PartnerInstallmentID == 10 || isBroadcastInstalment)) {
                                    Alert.alert(
                                        "THÔNG BÁO",
                                        "Vui lòng chọn thông tin [Quận/Huyện] ở bước 5",
                                        [
                                            {
                                                text: "Đóng",
                                                onPress: () => { }
                                            }
                                        ]
                                    );
                                }
                                else if (state.EPOSTransactionBO.client_Career != '' && state.EPOSTransactionBO.WardIDCompany == -1 && (state.EPOSTransactionBO.PartnerInstallmentID == 10 || isBroadcastInstalment)) {
                                    Alert.alert(
                                        "THÔNG BÁO",
                                        "Vui lòng chọn thông tin [Phường/Xã] ở bước 5",
                                        [
                                            {
                                                text: "Đóng",
                                                onPress: () => { }
                                            }
                                        ]
                                    );
                                }
                                else {
                                    if (checkValidateData() && (isCheckFE || isCheckACS || isCheckHC || isCheckMC || isCheckMAFC || isCheckShihan)) {
                                        setIsVisibleConfirm(true);
                                        updatestate(state.EPOSTransactionBO);
                                    }
                                }
                            }
                        }}
                    >
                        <MyText text={translate('instalmentManager.btn_update')} style={styles.txtBtn}
                        />
                    </TouchableOpacity>
                </View>
                <ModalConfirmNew
                    isVisible={isVisibleConfirm}
                    hideModal={() => {
                        setIsVisibleConfirm(false);
                    }}
                    transactionInfo={state.EPOSTransactionBO}
                    paperTypeInfor={GroupPaperTypeInforBOList}
                    onConfirm={() => {
                        // updatestate(state.EPOSTransactionBO);
                        setBlock(true);
                        if (isBroadcastInstalment || !!isCheckBCSecondNew) {
                            updateBroadcastTransactionNew();
                        } else {
                            updateEPosTransactionNew();
                        }
                        setIsVisibleConfirm(false);
                    }}
                    installmentHelper={installmentHelper}
                    itemInstallmentManager={itemInstallmentManager}
                />
            </View>
        </KeyboardAwareScrollView>
    );
}

export default StepFiveNew;
const styles = StyleSheet.create({
    fieldSet: {
        flexDirection: "row",
        marginVertical: 5,
        justifyContent: "space-between",
    },
    view_picker: {
        borderWidth: 1,
        borderColor: COLORS.bdE4E4E4,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 5,
        height: "auto",
        backgroundColor: COLORS.bgFFFFFF,
    },
    picker: {
        height: 27,
        //width: constants.width/2 + 20,
        marginVertical: 5,
    },
    btn: {
        padding: 10,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 5,
        width: "47%",
        borderWidth: 1,
        borderColor: COLORS.bd4DA6FF,
        //shadowColor: COLORS.sd000000,
        // shadowOffset: {
        //     width: 0,
        //     height: 1,
        // },
        // shadowOpacity: 0.15,
        // elevation: 3,
        height: "auto",
        //margin: 10
    },
    txtBtn: {
        fontWeight: "bold",
        color: COLORS.txtFFFFFF,
    },
    wrapperInputUserCreated: {
        width: "100%",
        flexDirection: "row",
        //backgroundColor: COLORS.bgF5F5F5,
        justifyContent: "center",
        //paddingHorizontal: 10,
        //paddingVertical: 10,
        paddingBottom: 10,
        alignItems: "center",
    },
    wrapperSearch: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        backgroundColor: COLORS.bgFFFFFF,
        borderRadius: 5,
        width: "100%",
        padding: 5,
        borderRadius: 5,
        borderWidth: 1,
        borderColor: COLORS.bdE4E4E4,
        height: "auto",
        backgroundColor: COLORS.bgFFFFFF,
        shadowColor: COLORS.sd000000,
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.1,
        elevation: 3,
    },
    inputUser: {
        width: "87%",
        paddingHorizontal: 5,
    },
    tab_list: {
        backgroundColor: COLORS.bgFDF9E5,
        height: constants.getSize(32),
        //justifyContent: "center",
        flexDirection: "row",
        marginBottom: 5,
        paddingLeft: 10,
        marginHorizontal: -10,
        marginVertical: 10
    },
});
