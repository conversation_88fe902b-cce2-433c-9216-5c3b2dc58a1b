import React, { useEffect, useState } from "react";
import {
    View,
    FlatList,
    StyleSheet,
    Animated,
    Alert,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import StepIndicator from 'react-native-step-indicator';
import {
    MyText,
    hide<PERSON><PERSON><PERSON>,
    UIIndicator,
} from "@components";
import { dateHelper, helper } from "@common";
import { constants } from "@constants";
import StepOne from './StepOne'
import StepTwo from './StepTwo'
import StepThree from './StepThree'
import StepFour from './StepFour'
import StepFive from './StepFive'
import StepSix from './StepSix'
import StepSeven from './StepSeven'
import DetailInstallment from './DetailInstallment'
import * as installmentHelper from "../common/installmentHelper";
import * as installmentAction from "./../action";
import * as dataCacheInstallmentAction from "./../action";
import { translate } from '@translate';
import { COLORS } from "@styles";
import StepTwoNew from "./StepTwoNew";
import StepThreeNew from "./StepThreeNew";
import StepSixNew from "./StepSixNew";
import StepFiveNew from "./StepFiveNew";

const customStyles = {
    stepIndicatorSize: 25,
    currentStepIndicatorSize: 30,
    separatorStrokeWidth: 2,
    currentStepStrokeWidth: 3,
    stepStrokeCurrentColor: COLORS.txtFE7013,
    stepStrokeWidth: 3,
    stepStrokeFinishedColor: COLORS.txtFE7013,
    stepStrokeUnFinishedColor: COLORS.txtAAAAAA,
    separatorFinishedColor: COLORS.txtFE7013,
    separatorUnFinishedColor: COLORS.txtAAAAAA,
    stepIndicatorFinishedColor: COLORS.txtFE7013,
    stepIndicatorUnFinishedColor: COLORS.txtFFFFFF,
    stepIndicatorCurrentColor: COLORS.txtFFFFFF,
    stepIndicatorLabelFontSize: 13,
    currentStepIndicatorLabelFontSize: 13,
    stepIndicatorLabelCurrentColor: COLORS.txtFE7013,
    stepIndicatorLabelFinishedColor: COLORS.txtFFFFFF,
    stepIndicatorLabelUnFinishedColor: COLORS.txtAAAAAA,
    labelColor: COLORS.txt999999,
    labelSize: 13,
    currentStepLabelColor: COLORS.txtFE7013
}

const HEADER_MAX_HEIGHT = 1;
const InstallmentSteps = function (props) {
    const { instalmentData, isCreateFromSO, itemInstallmentManager } = props.route.params;
    const [data, setData] = useState(helper.deepCopy(instalmentData));
    const dispatch = useDispatch();
    const dataCacheInstallmentReducer = useSelector(state => state.DataCacheInstallmentReducer);
    const InstallmentReducer = useSelector(state => state.InstallmentReducer.lstInstallment);
    const instalmentPartnerList = useSelector(state => state.InstallmentReducer.broadcastPartnerList);
    const isBroadcastInstalment = instalmentPartnerList.length > 1;
    const dataRewardInstallment = useSelector(state => state.detailReducer.dataRewardInstallment);
    const storeID = useSelector(state => state.userReducer.storeID);
    let { objEPOSTransactionBO } = data;
    let { client_steps } = data.objEPOSTransactionBO;
    // let client_steps = ["3", "4", "5", "6"];
    // let client_steps = ["0", "2"];
    // let client_steps = ["0", "2", "3", "4", "5", "6"];
    let indexStep = 0;
    let PartnerInstallmentID_MC = 10;
    const [state, setState] = useState({
        currentPosition: client_steps[0],
        scrollY: new Animated.Value(0),
        EPOSTransactionBO: objEPOSTransactionBO,
        GroupPaperTypeInforBOList: [],
        SaleProgramInfo: data?.objSaleProgramBO,
        FieldObjects: data?.client_ParnerInstallmentFieldObjectList,
        indexStep: 0,
        client_IsHasRegistrationBook: false,
        InitData: data,
    })
    const [partnerInstallmentName, setPartnerInstallmentName] = useState(null);
    const [lstGroupPaperTypeInforBO, setLstGroupPaperTypeInforBO] = useState([]);
    const [isShowIndicator, setIsShowIndicator] = useState(false)
    const [isHiddenIndicator, setIsHiddenIndicator] = useState(true)
    const [temp_IsHasRegistrationBook, setTemp_IsHasRegistrationBook] = useState(false);
    const diffClamp = Animated.diffClamp(
        state.scrollY,
        0,
        HEADER_MAX_HEIGHT
    );
    const translateY = diffClamp.interpolate({
        inputRange: [0, HEADER_MAX_HEIGHT],
        outputRange: [0, -HEADER_MAX_HEIGHT],
    });
    const flatListSteps = React.useRef(null);
    const [isUseEffect, setIsUseEffect] = useState(false);
    const [isRender, setIsRender] = useState(false);
    const [EPOSTransactionBOStep3, setEPOSTransactionBOStep3] = useState();
    const updateitemInstallmentManager = helper.deepCopy({ ...itemInstallmentManager });
    useEffect(() => {
        dispatch(installmentAction.setBroadcastPartnerList([]));
        dispatch(installmentAction.updateBroadcastSaleProgram([]));
        if (isCreateFromSO) {
            hideBlockUI();
        }
        var { objEPOSTransactionBO, client_ParnerInstallmentFieldObjectList } = GetDataCacheEP(data);
        installmentHelper.initObjectFields = data?.client_ParnerInstallmentFieldObjectList;
        dispatch(installmentAction.getDataIDCardHomeTown())
        setState({
            ...state,
            EPOSTransactionBO: objEPOSTransactionBO,
            FieldObjects: client_ParnerInstallmentFieldObjectList,
        });
        setIsRender(true);
        setIsUseEffect(false);
        GetPartnerInstallmentName(objEPOSTransactionBO.PartnerInstallmentID);
    }, []);

    useEffect(() => {
        if (isUseEffect) {
            objEPOSTransactionBO = EPOSTransactionBOStep3;
            DeleteAndCreatedDataCacheEPByStep();
            setIsUseEffect(false);
        }
    }, [isUseEffect]);

    const goBack = () => {
        dispatch(installmentAction.searchInstallment(InstallmentReducer.keyword, InstallmentReducer.paramFilter));
        props.navigation.goBack();
    }

    const SearchDataInstallment = (valueSearch = null) => {
        if (valueSearch != null && valueSearch != "") {
            dispatch(installmentAction.searchInstallment(valueSearch, InstallmentReducer.paramFilter));
            props.navigation.navigate("Installment");
        }
        else {
            dispatch(installmentAction.searchInstallment(InstallmentReducer.keyword, InstallmentReducer.paramFilter));
            props.navigation.navigate("Installment");
        }
    }

    useEffect(() => {
        if (isBroadcastInstalment) {
            var step = state.indexStep + 1;
            onPageChange(client_steps[step], step);
        }
    }, [state.InitData])

    useEffect(() => {
        if (helper.IsNonEmptyArray(lstGroupPaperTypeInforBO)) {
            setState({
                ...state,
                GroupPaperTypeInforBOList: lstGroupPaperTypeInforBO
            })
        }
    }, [lstGroupPaperTypeInforBO])

    const onPageChange = (position, valueindexStep) => {
        setState({
            ...state,
            indexStep: valueindexStep,
            currentPosition: parseInt(position), //
            EPOSTransactionBO: objEPOSTransactionBO,
            client_IsHasRegistrationBook: temp_IsHasRegistrationBook,
            GroupPaperTypeInforBOList: lstGroupPaperTypeInforBO,
            InitData: data
        });
        flatListSteps.current.scrollToIndex({ index: valueindexStep })
    }
    const showIndicator = () => {
        setIsShowIndicator(true);
    };
    const hideIndicator = () => {
        setIsShowIndicator(false);
    };
    const validateObject = (itemFieldObjects, itemBO, item) => {
        console.log("itemFieldObjects - step: ", itemFieldObjects);
        console.log("validateObject - objData: ", itemBO);
        if (itemFieldObjects.ChildObject) {
            // Nếu đối tượng con là 1 mảng
            if (itemFieldObjects.ChildObject.IsArray) {
                var itemChildBOList = itemBO[itemFieldObjects.ChildObject.ChildName];// danh sách đối tượng con
                return (validateObject(itemFieldObjects.ChildObject, itemChildBOList[itemFieldObjects.ChildObject.ArrayIndex]))
            }
            else {
                return validateObject(itemFieldObjects.ChildObject, itemBO[itemFieldObjects.ChildObject.ChildName]);
            }
        }
        else {
            var value = itemBO[itemFieldObjects.FieldName];
            // Bắt buộc nhập
            if (itemFieldObjects.IsRequired) {
                if (value == undefined || value.toString().length == 0 || value === -1 || (value === 0 && itemFieldObjects.FieldName != 'Gender')) {
                    // Báo lỗi
                    Alert.alert(
                        translate('common.notification'),
                        translate('instalmentManager.please_enter_information') + itemFieldObjects.FieldDescription + translate('instalmentManager.at_step') + itemFieldObjects.Step,
                        [
                            {
                                text: translate('common.btn_close'), onPress: () => {

                                }
                            }
                        ],
                        { cancelable: false }
                    )
                    return false;
                }
            }
            // Nếu có giá trị thì kiểm tra tiếp
            if (value != undefined && value !== -1 && value.toString().length > 0) {
                // Có kiểm tra độ dài kí tự
                if (itemFieldObjects.objFieldLength != undefined) {
                    if (itemFieldObjects.objFieldLength.MinLength > 0 && value.length < itemFieldObjects.objFieldLength.MinLength) {
                        // Báo lỗi
                        Alert.alert(
                            translate('common.notification'),
                            translate('instalmentManager.information') + itemFieldObjects.FieldDescription + translate('instalmentManager.at_step') + itemFieldObjects.Step + translate('instalmentManager.invalid_1'),
                            [
                                {
                                    text: translate('common.btn_close'), onPress: () => {

                                    }
                                }
                            ],
                            { cancelable: false }
                        )
                        return false;
                    }
                    if (itemFieldObjects.objFieldLength.MaxLength > 0 && value.length > itemFieldObjects.objFieldLength.MaxLength) {
                        // Báo lỗi
                        Alert.alert(
                            translate('common.notification'),
                            translate('instalmentManager.information') + itemFieldObjects.FieldDescription + translate('instalmentManager.at_step') + itemFieldObjects.Step + translate('instalmentManager.invalid_2'),
                            [
                                {
                                    text: translate('common.btn_close'), onPress: () => {

                                    }
                                }
                            ],
                            { cancelable: false }
                        )
                        return false;
                    }
                    if (itemFieldObjects.objFieldLength.EqualLength > 0 && value.length != itemFieldObjects.objFieldLength.EqualLength && value.length !== 12) {
                        // Báo lỗi
                        Alert.alert(
                            translate('common.notification'),
                            translate('instalmentManager.information') + itemFieldObjects.FieldDescription + translate('instalmentManager.at_step') + itemFieldObjects.Step + translate('instalmentManager.invalid_3'),
                            [
                                {
                                    text: translate('common.btn_close'), onPress: () => {

                                    }
                                }
                            ],
                            { cancelable: false }
                        )
                        return false;
                    }
                }
                // Phạm vi giá trị
                if (itemFieldObjects.objFieldRange) {
                    if (itemFieldObjects.objFieldRange.MinValue != undefined && itemFieldObjects.objFieldRange.MinValue > 0 && value < itemFieldObjects.objFieldRange.MinValue) {
                        // Báo lỗi
                        Alert.alert(
                            translate('common.notification'),
                            translate('instalmentManager.information') + itemFieldObjects.FieldDescription + translate('instalmentManager.at_step') + itemFieldObjects.Step + translate('instalmentManager.invalid_4'),
                            [
                                {
                                    text: translate('common.btn_close'), onPress: () => {

                                    }
                                }
                            ],
                            { cancelable: false }
                        )
                        return false;
                    }
                    if (itemFieldObjects.objFieldRange.MaxValue != undefined && itemFieldObjects.objFieldRange.MaxValue > 0 && value > itemFieldObjects.objFieldRange.MaxValue) {
                        // Báo lỗi
                        Alert.alert(
                            translate('common.notification'),
                            translate('instalmentManager.information') + itemFieldObjects.FieldDescription + translate('instalmentManager.at_step') + itemFieldObjects.Step + translate('instalmentManager.invalid_5'),
                            [
                                {
                                    text: translate('common.btn_close'), onPress: () => {

                                    }
                                }
                            ],
                            { cancelable: false }
                        )
                        return false;
                    }

                    var dateValue = new Date(value);
                    if (itemFieldObjects.objFieldRange.BeginDate != undefined && dateValue.setHours(23) < new Date(itemFieldObjects.objFieldRange.BeginDate)) {
                        // Báo lỗi
                        // var dateZero = new Date(itemFieldObjects.objFieldRange.BeginDate).setHours(0,0,0,0);
                        // var date = new Date(dateZero);
                        // var dateFormat = `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
                        Alert.alert(
                            translate('common.notification'),
                            translate('instalmentManager.information') + itemFieldObjects.FieldDescription + translate('instalmentManager.at_step') + itemFieldObjects.Step + translate('instalmentManager.invalid') + itemFieldObjects.FieldDescription + translate('instalmentManager.later_or_equal') + dateHelper.formatStrDateDDMMYYYY(itemFieldObjects.objFieldRange.BeginDate),
                            [
                                {
                                    text: translate('common.btn_close'), onPress: () => {

                                    }
                                }
                            ],
                            { cancelable: false }
                        )
                        return false;
                    }
                    if (itemFieldObjects.objFieldRange.EndDate != undefined && dateValue.setHours(0) > new Date(itemFieldObjects.objFieldRange.EndDate)) {
                        // Báo lỗi
                        // var dateZero = new Date(itemFieldObjects.objFieldRange.EndDate).setHours(0,0,0,0);
                        // var date = new Date(dateZero);
                        // var dateFormat = `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
                        Alert.alert(
                            translate('common.notification'),
                            translate('instalmentManager.information') + itemFieldObjects.FieldDescription + translate('instalmentManager.at_step') + itemFieldObjects.Step + translate('instalmentManager.invalid') + itemFieldObjects.FieldDescription + translate('instalmentManager.sooner_or_equal') + dateHelper.formatStrDateDDMMYYYY(itemFieldObjects.objFieldRange.EndDate),
                            [
                                {
                                    text: translate('common.btn_close'), onPress: () => {

                                    }
                                }
                            ],
                            { cancelable: false }
                        )
                        return false;
                    }
                }
                // Là Email
                if (itemFieldObjects.IsEmail) {
                    let regExp = new RegExp(/^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/g);
                    if (!regExp.test(value)) {
                        Alert.alert(
                            translate('common.notification'),
                            translate('instalmentManager.information') + itemFieldObjects.FieldDescription + translate('instalmentManager.at_step') + itemFieldObjects.Step + translate('instalmentManager.wrong_format'),
                            [
                                {
                                    text: translate('common.btn_close'), onPress: () => {

                                    }
                                }
                            ],
                            { cancelable: false }
                        )
                        return false;
                    }
                }
                // Là SDT
                if (itemFieldObjects.FormatPhoneNumber != undefined && itemFieldObjects.FormatPhoneNumber.length > 0) {
                    var threenumberofphone = "," + value.slice(0, 3) + ",";
                    if (itemFieldObjects.FormatPhoneNumber.indexOf(threenumberofphone) == -1) {
                        Alert.alert(
                            translate('common.notification'),
                            "[" + itemFieldObjects.FieldDescription + translate('instalmentManager.at_step') + itemFieldObjects.Step + translate('instalmentManager.wrong_format'),
                            [
                                {
                                    text: translate('common.btn_close'), onPress: () => {

                                    }
                                }
                            ],
                            { cancelable: false }
                        )
                        return false;
                    }
                }
            }
            return true;
        }
    }

    const DeleteAndCreatedDataCacheEPByStep = () => {
        console.log("isUseEffect: ", objEPOSTransactionBO);
        let InitDataCache = {
            EPTransactionID: objEPOSTransactionBO.EPTransactionID,
            objEPOSTransactionBO: objEPOSTransactionBO,
            client_ParnerInstallmentFieldObjectList: state.FieldObjects,
            GroupPaperTypeInforBOList: lstGroupPaperTypeInforBO,
            LogTime: new Date()
        }
        var dataCacheList = dataCacheInstallmentReducer.lstCacheDataEP.data;
        if (dataCacheList != null && dataCacheList.length > 0) {
            var idx = dataCacheList.findIndex(p => p.EPTransactionID === state.EPOSTransactionBO.EPTransactionID);
            if (idx >= 0) {
                dataCacheList[idx] = InitDataCache;
            }
            else dataCacheList.push(InitDataCache);
        }
        else dataCacheList.push(InitDataCache);

        dispatch(dataCacheInstallmentAction.DeleteAndCreatedDataCacheEPByStep(dataCacheList))
    }

    const GetDataCacheEP = (data) => {
        var dataCacheList = dataCacheInstallmentReducer.lstCacheDataEP.data;
        if (dataCacheList != null && dataCacheList.length > 0) {
            var idx = dataCacheList.findIndex(p => p.EPTransactionID === data.objEPOSTransactionBO.EPTransactionID);
            if (idx >= 0) {
                var objData = dataCacheList[idx]
                var logTime = new Date(objData.LogTime)
                var date = new Date();
                var difMin = (date - logTime);
                difMin = Math.round((difMin / 1000) / 60);
                console.log("logTime: ", difMin);
                if (difMin > 10) {
                    dataCacheList.splice(idx, 1);
                    dispatch(dataCacheInstallmentAction.DeleteAndCreatedDataCacheEPByStep(dataCacheList))
                    return data;
                }
                else {
                    if (objData.GroupPaperTypeInforBOList != null && objData.GroupPaperTypeInforBOList.length > 0)
                        setLstGroupPaperTypeInforBO(objData.GroupPaperTypeInforBOList);
                    const {
                        Debt,
                        TotalPrepaid,
                        cus_DefferenceTotalPrepaid,
                        TermLoan,
                        client_InsuranceFeesBO,
                        client_MasterGoodsInsuranceBO,
                        MonthlyPayment,
                        EPOSStatusID,
                        client_steps,
                        cus_EPOSTransactionReferenceBOList,
                        client_ListSocialStatus,
                        client_StepSendOTP
                    } = data?.objEPOSTransactionBO;
                    const { client_ParnerInstallmentFieldObjectList } = data;
                    return {
                        ...objData,
                        client_ParnerInstallmentFieldObjectList: client_ParnerInstallmentFieldObjectList,
                        objEPOSTransactionBO: {
                            ...objData.objEPOSTransactionBO,
                            Debt,
                            TotalPrepaid,
                            cus_DefferenceTotalPrepaid,
                            TermLoan,
                            client_InsuranceFeesBO,
                            client_MasterGoodsInsuranceBO,
                            MonthlyPayment,
                            EPOSStatusID,
                            client_steps,
                            cus_EPOSTransactionReferenceBOList,
                            client_ListSocialStatus,
                            client_StepSendOTP
                        }
                    };
                }
            }
        }
        return data;
    }

    const GetPartnerInstallmentName = (PartnerInstallmentID) => {
        switch (PartnerInstallmentID) {
            case 1:
                setPartnerInstallmentName("ACS")
                break;
            case 2:
                setPartnerInstallmentName("HC")
                break;
            case 3:
                setPartnerInstallmentName("FE")
                break;
            case 10:
                setPartnerInstallmentName("MC")
                break;
            case 15:
                setPartnerInstallmentName("MAFC")
                break;
            default:
                setPartnerInstallmentName("")
                break;
        }
    }

    const renderPage = (item, position) => {
        const storeIDNew = helper.checkConfigStoreInstallmentNew(storeID);

        const storeIDHCAndACSNew = helper.checkConfigStoreInstallmentHCAndACSNew(storeID);
        const isCheckACS = state.EPOSTransactionBO.PartnerInstallmentID === 1;
        const checkACSAndConfig = isCheckACS && storeIDHCAndACSNew;
        const isCheckHC = state.EPOSTransactionBO.PartnerInstallmentID === 2;
        const checkHCAndConfig = isCheckHC && storeIDHCAndACSNew;

        const storeIDFENew = helper.checkConfigStoreInstallmentFENew(storeID);
        const isCheckFE = state.EPOSTransactionBO.PartnerInstallmentID === 3;
        const checkFEAndConfig = isCheckFE && storeIDFENew;

        const storeIDMCNew = helper.checkConfigStoreInstallmentMCNew(storeID);
        const isCheckMC = state.EPOSTransactionBO.PartnerInstallmentID === 10;
        const checkMCAndConfig = isCheckMC && storeIDMCNew;
        const isCheckMAFC = state.EPOSTransactionBO.PartnerInstallmentID === 15;

        const isCheckShihan = state.EPOSTransactionBO.PartnerInstallmentID === 22;

        switch (parseInt(item)) {
            case 0:
                return (<DetailInstallment
                    parent={state}
                    onNext={() => {
                        var step = state.indexStep + 1;
                        onPageChange(client_steps[step], step);
                    }}
                    onPrev={() => {
                        var step = state.indexStep - 1
                        if (step < 0) goBack();
                        else onPageChange(client_steps[step], step);
                    }}
                    validateObject={validateObject}
                    updatestate={(stateStep) => {
                        objEPOSTransactionBO = stateStep;
                        DeleteAndCreatedDataCacheEPByStep();
                    }}
                    updateprevstate={(stateStep) => {
                        objEPOSTransactionBO = stateStep;
                    }}
                    onGetBroadcastInformation={(broadcastInfo) => {
                        setData(broadcastInfo);
                        var { objEPOSTransactionBO, client_ParnerInstallmentFieldObjectList } = broadcastInfo;
                        installmentHelper.initObjectFields = client_ParnerInstallmentFieldObjectList;
                        setState({
                            ...state,
                            EPOSTransactionBO: objEPOSTransactionBO,
                            FieldObjects: client_ParnerInstallmentFieldObjectList,
                            SaleProgramInfo: broadcastInfo?.objSaleProgramBO,
                            // FieldObjects: data?.client_ParnerInstallmentFieldObjectList,
                            InitData: broadcastInfo,
                        });
                    }}
                    initialData={helper.deepCopy(instalmentData)}
                    dataRewardInstallment={dataRewardInstallment}
                    itemInstallmentManager={updateitemInstallmentManager}
                    storeID={storeID}
                />);
            case 1:
                return (<StepOne
                    parent={state}
                    onNext={() => {
                        var step = state.indexStep + 1;
                        onPageChange(client_steps[step], step);
                    }}
                    onPrev={() => {
                        var step = state.indexStep - 1;
                        if (step < 0) goBack();
                        else onPageChange(client_steps[step], step);
                    }}
                    validateObject={validateObject}
                    updatestate={(stateStep) => {
                        objEPOSTransactionBO = stateStep;
                        DeleteAndCreatedDataCacheEPByStep();
                    }}
                    updateprevstate={(stateStep) => {
                        objEPOSTransactionBO = stateStep;
                    }}
                    updateparent={(stateParent) => {
                        const { FieldObjects } = stateParent;
                        setState({
                            ...state,
                            FieldObjects: FieldObjects
                        })
                    }}
                    showIndicator={() => {
                        showIndicator();
                    }}
                    hideIndicator={() => {
                        hideIndicator();
                    }}
                    instalmentPartnerList={instalmentPartnerList}
                />);
            case 2:
                if (storeIDNew) {
                    return (<StepTwoNew
                        parent={state}
                        onNext={() => {
                            var step = state.indexStep + 1;
                            onPageChange(client_steps[step], step);
                        }}
                        onPrev={() => {
                            var step = state.indexStep - 1;
                            onPageChange(client_steps[step], step);
                        }}
                        validateObject={validateObject}
                        updatestate={(stateStep) => {
                            objEPOSTransactionBO = stateStep;
                            DeleteAndCreatedDataCacheEPByStep();
                        }}
                        updateprevstate={(stateStep) => {
                            objEPOSTransactionBO = stateStep;
                        }}
                        updateparent={(stateParent) => {
                            const { FieldObjects } = stateParent;
                            setState({
                                ...state,
                                FieldObjects: FieldObjects
                            })
                        }}
                        itemInstallmentManager={updateitemInstallmentManager}
                        SearchDataInstallment={SearchDataInstallment}
                    />);
                } else {
                    return (<StepTwo
                        parent={state}
                        onNext={() => {
                            var step = state.indexStep + 1;
                            onPageChange(client_steps[step], step);
                        }}
                        onPrev={() => {
                            var step = state.indexStep - 1;
                            onPageChange(client_steps[step], step);
                        }}
                        validateObject={validateObject}
                        updatestate={(stateStep) => {
                            objEPOSTransactionBO = stateStep;
                            DeleteAndCreatedDataCacheEPByStep();
                        }}
                        updateprevstate={(stateStep) => {
                            objEPOSTransactionBO = stateStep;
                        }}
                        updateparent={(stateParent) => {
                            const { FieldObjects } = stateParent;
                            setState({
                                ...state,
                                FieldObjects: FieldObjects
                            })
                        }}
                    />);

                }
            case 3:
                if (storeIDNew) {
                    return (<StepThreeNew
                        parent={state}
                        onNext={() => {
                            var step = state.indexStep + 1;
                            onPageChange(client_steps[step], step);
                        }}
                        onPrev={() => {
                            var step = state.indexStep - 1;
                            onPageChange(client_steps[step], step);
                        }}
                        validateObject={validateObject}
                        updatestate={(stateStep, client_IsHasRegistrationBook = false, lstGroupPaperTypeInforBO = []) => {
                            const { EPOSTransactionBO } = stateStep
                            objEPOSTransactionBO = EPOSTransactionBO;
                            setTemp_IsHasRegistrationBook(client_IsHasRegistrationBook);
                            setLstGroupPaperTypeInforBO(lstGroupPaperTypeInforBO);
                            setEPOSTransactionBOStep3(EPOSTransactionBO);
                            setIsUseEffect(true);
                        }}
                        updateprevstate={(stateStep, client_IsHasRegistrationBook = false, lstGroupPaperTypeInforBO = []) => {
                            const { EPOSTransactionBO } = stateStep
                            objEPOSTransactionBO = EPOSTransactionBO;
                            setTemp_IsHasRegistrationBook(client_IsHasRegistrationBook);
                        }}
                        instalmentPartnerList={instalmentPartnerList}
                    />);
                } else {
                    return (<StepThree
                        parent={state}
                        onNext={() => {
                            var step = state.indexStep + 1;
                            onPageChange(client_steps[step], step);
                        }}
                        onPrev={() => {
                            var step = state.indexStep - 1;
                            onPageChange(client_steps[step], step);
                        }}
                        validateObject={validateObject}
                        updatestate={(stateStep, client_IsHasRegistrationBook = false, lstGroupPaperTypeInforBO = []) => {
                            const { EPOSTransactionBO } = stateStep
                            objEPOSTransactionBO = EPOSTransactionBO;
                            setTemp_IsHasRegistrationBook(client_IsHasRegistrationBook);
                            setLstGroupPaperTypeInforBO(lstGroupPaperTypeInforBO);
                            setEPOSTransactionBOStep3(EPOSTransactionBO);
                            setIsUseEffect(true);
                        }}
                        updateprevstate={(stateStep, client_IsHasRegistrationBook = false, lstGroupPaperTypeInforBO = []) => {
                            const { EPOSTransactionBO } = stateStep
                            objEPOSTransactionBO = EPOSTransactionBO;
                            setTemp_IsHasRegistrationBook(client_IsHasRegistrationBook);
                        }}
                        instalmentPartnerList={instalmentPartnerList}
                    />);

                }
            case 4:
                return (<StepFour
                    parent={state}
                    onNext={() => {
                        var step = state.indexStep + 1;
                        onPageChange(client_steps[step], step);
                    }}
                    onPrev={() => {
                        var step = state.indexStep - 1;
                        onPageChange(client_steps[step], step);
                    }}
                    validateObject={validateObject}
                    updatestate={(stateStep) => {
                        objEPOSTransactionBO = stateStep;
                        DeleteAndCreatedDataCacheEPByStep();
                    }}
                    updateprevstate={(stateStep) => {
                        objEPOSTransactionBO = stateStep;
                    }}
                />);
            case 5:
                if (
                    checkACSAndConfig ||
                    checkHCAndConfig ||
                    checkFEAndConfig ||
                    checkMCAndConfig ||
                    isCheckMAFC ||
                    isCheckShihan
                ) {
                    return (<StepFiveNew
                        parent={state}
                        onNext={() => {
                            var step = state.indexStep + 1;
                            onPageChange(client_steps[step], step);
                        }}
                        onPrev={() => {
                            var step = state.indexStep - 1;
                            onPageChange(client_steps[step], step);
                        }}
                        validateObject={validateObject}
                        updatestate={(stateStep) => {
                            objEPOSTransactionBO = stateStep;
                            DeleteAndCreatedDataCacheEPByStep();
                        }}
                        updateprevstate={(stateStep) => {
                            objEPOSTransactionBO = stateStep;
                        }}
                        updateparent={(stateParent) => {
                            const { FieldObjects } = stateParent;
                            setState({
                                ...state,
                                FieldObjects: FieldObjects
                            })
                        }}
                        SearchDataInstallment={SearchDataInstallment}
                        instalmentPartnerList={instalmentPartnerList}
                        itemInstallmentManager={updateitemInstallmentManager}
                    />);
                } else {
                    return (<StepFive
                        parent={state}
                        onNext={() => {
                            var step = state.indexStep + 1;
                            onPageChange(client_steps[step], step);
                        }}
                        onPrev={() => {
                            var step = state.indexStep - 1;
                            onPageChange(client_steps[step], step);
                        }}
                        validateObject={validateObject}
                        updatestate={(stateStep) => {
                            objEPOSTransactionBO = stateStep;
                            DeleteAndCreatedDataCacheEPByStep();
                        }}
                        updateprevstate={(stateStep) => {
                            objEPOSTransactionBO = stateStep;
                        }}
                        updateparent={(stateParent) => {
                            const { FieldObjects } = stateParent;
                            setState({
                                ...state,
                                FieldObjects: FieldObjects
                            })
                        }}
                        instalmentPartnerList={instalmentPartnerList}
                    />);
                }
            // case 6:
            //     if (storeIDNew) {
            //         return (<StepSixNew
            //             parent={state}
            //             navigation={props.navigation}
            //             goBack={goBack}
            //             onNext={() => {
            //                 var step = state.indexStep + 1;
            //                 onPageChange(client_steps[step], step);
            //             }}
            //             onPrev={() => {
            //                 var step = state.indexStep - 1;
            //                 onPageChange(client_steps[step], step);
            //             }}
            //             validateObject={validateObject}
            //             updatestate={(stateStep) => {
            //                 objEPOSTransactionBO = stateStep;
            //             }}
            //             updateprevstate={(stateStep) => {
            //                 objEPOSTransactionBO = stateStep;
            //             }}
            //             SearchDataInstallment={SearchDataInstallment}
            //             instalmentPartnerList={instalmentPartnerList}
            //             itemInstallmentManager={updateitemInstallmentManager}
            //         />);
            //     }
            //     else {
            //         return (<StepSix
            //             parent={state}
            //             navigation={props.navigation}
            //             goBack={goBack}
            //             onNext={() => {
            //                 var step = state.indexStep + 1;
            //                 onPageChange(client_steps[step], step);
            //             }}
            //             onPrev={() => {
            //                 var step = state.indexStep - 1;
            //                 onPageChange(client_steps[step], step);
            //             }}
            //             validateObject={validateObject}
            //             updatestate={(stateStep) => {
            //                 objEPOSTransactionBO = stateStep;
            //             }}
            //             updateprevstate={(stateStep) => {
            //                 objEPOSTransactionBO = stateStep;
            //             }}
            //             SearchDataInstallment={SearchDataInstallment}
            //             instalmentPartnerList={instalmentPartnerList}
            //         />);

            //     }
            case 7:
                return (<StepSeven
                    parent={state}
                    navigation={props.navigation}
                    onPrev={() => {
                        SearchDataInstallment();
                        //onPageChange(steps[step], step);
                    }}
                    SearchDataInstallment={SearchDataInstallment}
                />);
        }
    }

    return (
        <View style={{ flex: 1, backgroundColor: COLORS.bgFFFFFF }}>
            <View style={styles.con_header}>
                <View style={styles.header_title}>
                    {(!isBroadcastInstalment) ? <MyText
                        addSize={2}
                        style={{
                            width: constants.width,
                            fontWeight: "bold",
                            color: COLORS.txtFFFFFF,
                        }}
                        text={`${translate('instalmentManager.record_2')} ${state.EPOSTransactionBO.EPOSTransactionID.trim()} - `}
                        children={
                            <MyText
                                text={partnerInstallmentName}
                                style={{ color: COLORS.txtFFFF00, fontWeight: "bold" }}
                            />
                        }
                    /> : <MyText
                        addSize={2}
                        style={{
                            width: constants.width,
                            fontWeight: "bold",
                            color: COLORS.txtFFFFFF,
                        }}
                        text={`Đơn hàng ${state.EPOSTransactionBO.SaleOrderID}`}
                    />}
                </View>
            </View>
            {isRender && (
                <View style={{ flex: 1 }}>
                    {(state.currentPosition != 7 && state.currentPosition != 0) &&
                        <View style={{ paddingVertical: 10, alignSelf: "center", width: constants.width - constants.getSize(60), justifyContent: "center" }}>
                            <StepIndicator
                                customStyles={customStyles}
                                currentPosition={state.currentPosition - 1}
                                stepCount={5}
                            />
                        </View>
                    }
                    <Animated.View style={{
                        width: constants.width, flex: 1,
                        height: "auto",
                        transform: [{ translateY: translateY }, { perspective: 1000 }],
                    }}>
                        <FlatList
                            ref={flatListSteps}
                            data={client_steps}
                            renderItem={({ item, index }) => renderPage(item, index)}
                            keyExtractor={(item, index) => index.toString()}
                            horizontal={true}
                            scrollEnabled={false}
                            keyboardShouldPersistTaps={"always"}
                        />
                    </Animated.View>
                </View>
            )}
            <UIIndicator isVisible={isShowIndicator} />
        </View>

    );
}

export default InstallmentSteps;

const styles = StyleSheet.create({
    con_header: {
        backgroundColor: COLORS.bg19A796,
        height: constants.getSize(40),
        justifyContent: "center",
        flexDirection: "row",
        marginBottom: 5,
        paddingLeft: 10
    },
    goBack: {
        height: constants.getSize(40),
        width: constants.getSize(40),
        justifyContent: "center"
    },
    img_goBack: {
        alignSelf: "center",
        height: constants.getSize(13),
        width: constants.getSize(13),
        tintColor: COLORS.imgFFFFFF
    },
    header_title: {
        flex: 1,
        justifyContent: "center"
    },
    fieldSet: {
        flexDirection: "row",
        marginVertical: 5,
        justifyContent: "space-between",
    },
    view_picker: {
        borderWidth: 1,
        borderColor: COLORS.bdE4E4E4,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 8,
        height: "auto",
        backgroundColor: COLORS.bgFFFFFF,
    },
    picker: {
        height: 30,
        //width: constants.width/2 + 20,
        margin: 5,
    },
    btn: {
        padding: 10,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 5,
        width: "47%",
        borderWidth: 2,
        borderColor: COLORS.bdE4E4E4,
        height: "auto",
    },
    txtBtn: {
        fontWeight: "bold",
        color: COLORS.txtFFFFFF,
    },
});
