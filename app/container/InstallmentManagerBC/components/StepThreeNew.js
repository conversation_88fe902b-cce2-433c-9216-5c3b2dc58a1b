import React, { useEffect } from "react";
import {
    View,
    FlatList,
    TouchableOpacity,
    Keyboard,
    StyleSheet,
    Alert
} from 'react-native';
import {
    MyText,
    Icon,
    showBlock<PERSON>,
    hideBlockUI,
    MyPicker,
    CaptureCamera,
    FieldInput,
    TitleInput,
    DocCamera,
    Picker
} from "@components";
import { helper, dateHelper } from "@common";
import { constants, API_CONST, ENUM } from "@constants";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import ImageProcess from "./ImageProcess";
import ImageResizer from 'react-native-image-resizer';
import { launchImageLibrary } from 'react-native-image-picker';
import Dash from 'react-native-dash';
import * as installmentHelper from "../common/installmentHelper";
import TitleInputMoney from "./TitleInputMoney";
import { useDispatch, useSelector } from 'react-redux';
import * as installmentAction from "../action";
import { getImageCDN } from "../../ActiveSimManager/action";
import { translate } from '@translate';
import TooltipWrapper from './TooltipWrapper';

const { API_GET_IMAGE_CDN, API_GET_IMAGE_CDN_NEW } = API_CONST;
const { HYPER_VERGE } = constants;
const { FILE_PATH: { STEP_THREE } } = ENUM
import { COLORS } from "@styles";

const StepThreeNew = function ({ parent, onNext, onPrev, validateObject, updatestate, updateprevstate, instalmentPartnerList }) {
    const [state, setState] = React.useState({
        isVisibleCamera: false,
        EPOSTransactionBO: parent.EPOSTransactionBO
    })
    const dispatch = useDispatch();
    const dataCacheInstallmentReducer = useSelector(state => state.DataCacheInstallmentReducer);
    const [lstGroupPaperTypeInforBO, setLstGroupPaperTypeInforBO] = React.useState([])
    const [lstPaperTypeInforBO, setLstPaperTypeInforBO] = React.useState([])
    const [property, setProperty] = React.useState("");
    const [radioGroupPaperTypeInfoList, setRadioGroupPaperTypeInfoList] = React.useState([]);
    const [indexLoanInfoSelected, setIndexLoanInfoSelected] = React.useState();
    const [indexAttachTypeIDChanged, setIndexAttachTypeIDChanged] = React.useState();
    const [paperTypeIDOFCus_PaperTypeInforBOList, setPaperTypeIDOFCus_PaperTypeInforBOList] = React.useState();
    const [indexOFEPosTransacTionTypeAttBOList, setIndexOFEPosTransacTionTypeAttBOList] = React.useState();
    const [EPosTransacTionTypeAttBO, setEPosTransacTionTypeAttBO] = React.useState();
    const [LoanInfoID, setLoanInfoID] = React.useState(-1);
    const [isGetPaperTypeInfor, setIsGetPaperTypeInfor] = React.useState(false);
    const refScroll3 = React.useRef(null);
    const userInfo = useSelector(state => state.userReducer);

    const isHasFEPartner = state.EPOSTransactionBO.PartnerInstallmentID == 3 || (instalmentPartnerList.findIndex(id => id == 3) != -1);
    const isBroadcastInstalment = instalmentPartnerList.length > 1;

    useEffect(() => {
        setIsGetPaperTypeInfor(false);
    }, [])

    useEffect(() => {
        setLstGroupPaperTypeInforBO(parent.InitData.ListGroupPaperTypeInforBO);
        setLstPaperTypeInforBO([]);
        setLoanInfoID(-1);
        setIndexLoanInfoSelected(-1);
    }, [parent.InitData])

    useEffect(() => {
        installmentHelper.initObjectFields = parent.FieldObjects;
        setState({
            ...state,
            EPOSTransactionBO: parent.EPOSTransactionBO,
        })
        setRadioGroupPaperTypeInfoList(GetFromGroupPaperTypeInfo())
        if (refScroll3) {
            refScroll3.current.scrollToPosition(0, 0, false);
        }
    }, [parent.currentPosition]);

    useEffect(() => {
        showBlockUI();
        GetPaperTypeInforByEP(parent.EPOSTransactionBO).then(res => {
            hideBlockUI();
            setLstGroupPaperTypeInforBO(res);
            setRadioGroupPaperTypeInfoList(GetFromGroupPaperTypeInfo(res));
        });
    }, [state.EPOSTransactionBO.TotalPrepaid]);

    const showCamera = (property, PaperTypeID, index, data = null, indexImage = null) => () => {
        setState({
            ...state,
            isVisibleCamera: true,
        })
        setPaperTypeIDOFCus_PaperTypeInforBOList(PaperTypeID);
        setIndexOFEPosTransacTionTypeAttBOList(index)
        setProperty(property);
        setEPosTransacTionTypeAttBO(data);
    }

    const removePicture = (property, property1, PaperTypeID, index) => () => {
        let temp_lstGroupPaperTypeInforBO = [...lstGroupPaperTypeInforBO]
        let temp_CusPaperTypeInforBOList = [...temp_lstGroupPaperTypeInforBO[indexLoanInfoSelected].cus_PaperTypeInforBOList]
        let temp_CusEPosTransacTionTypeAttBOList = temp_CusPaperTypeInforBOList.find(x => x.PaperTypeID == PaperTypeID).cus_EPosTransacTionTypeAttBOList;
        temp_CusEPosTransacTionTypeAttBOList[index] = {
            ...temp_CusEPosTransacTionTypeAttBOList[index],
            [property]: "",
            [property1]: "",
        }

        let newtLst = [];
        let findObj = temp_lstGroupPaperTypeInforBO.find(item => item.LoanInfoID == LoanInfoID)
        if (findObj != {} && findObj.cus_PaperTypeInforBOList != {}) {
            findObj.cus_PaperTypeInforBOList.forEach((e) => {
                if (
                    newtLst.find((item) => item.PaperTypeID == e.PaperTypeID) == undefined
                ) {
                    newtLst.push(e);
                }
            });
        }
        setLstPaperTypeInforBO(newtLst);
    }

    const selectImage = () => {
        let options = {
            base64: false,
            forceUpOrientation: true,
            fixOrientation: true,
            pauseAfterCapture: true,
            skipProcessing: true,
            writeExif: true
        };
        launchImageLibrary(options = {
            mediaType: 'photo',
            noData: true
        }, (response) => {
            if (helper.hasProperty(response, 'uri')) {
                resizeImage(response).then(
                    ({ path, uri, size, name }) => {
                        showBlockUI();
                        setState({
                            ...state,
                            isVisibleCamera: false,
                        })
                        //BLX
                        if (helper.isObject(EPosTransacTionTypeAttBO)
                            && EPosTransacTionTypeAttBO.PaperTypeID === 2
                            && EPosTransacTionTypeAttBO.ShortName.toUpperCase().includes("BLXMT")) {
                            var hardurl = "http://**********:9999/static/gplx.jpg";
                            getInfoDriverGLXByImage(uri)
                                .then((response) => {
                                    hideBlockUI();
                                    console.log("response");
                                    //hideBlockUI();
                                    let temp_lstGroupPaperTypeInforBO = [...lstGroupPaperTypeInforBO]
                                    let temp_CusPaperTypeInforBOList = [...lstPaperTypeInforBO]
                                    temp_CusPaperTypeInforBOList = temp_CusPaperTypeInforBOList.map(el => (
                                        el.PaperTypeID === EPosTransacTionTypeAttBO.PaperTypeID ? { ...el, IDNumber: response?.no } : { ...el }
                                    ))
                                    temp_lstGroupPaperTypeInforBO[indexLoanInfoSelected] = {
                                        ...temp_lstGroupPaperTypeInforBO[indexLoanInfoSelected],
                                        cus_PaperTypeInforBOList: temp_CusPaperTypeInforBOList,
                                    }
                                    setLstGroupPaperTypeInforBO(temp_lstGroupPaperTypeInforBO);
                                    setLstPaperTypeInforBO(temp_CusPaperTypeInforBOList);
                                    updateImgEPosTransacTionTypeAtt(uri, name, temp_lstGroupPaperTypeInforBO);
                                })
                                .catch(
                                    err => {
                                        hideBlockUI();
                                        updateImgEPosTransacTionTypeAtt(uri, name);
                                    }
                                )
                        }
                        else {
                            hideBlockUI();
                            updateImgEPosTransacTionTypeAtt(uri, name);
                        }
                    }
                ).catch(
                    (error) => {
                        console.log("resizeImage", error);
                    }
                );
            }
        });
    }

    const resizeImage = (e) => {
        return new Promise((resolve, reject) => {
            let max = 1024;
            let w = e.width;
            let h = e.height;

            if (e.width > e.height) {
                // orientation = 'landscape';
                if (w > max) {
                    w = max;
                    h = max * e.height / e.width
                }
            } else if (e.width < e.height) {
                //orientation = 'portrait';
                if (h > max) {
                    h = max;
                    w = max * e.width / e.height
                }
            } else { //width == height
                //orientation = 'event';
                if (w > max) {
                    w = max;
                    h = max * e.height / e.width
                }
            }
            ImageResizer.createResizedImage(
                e.uri,
                w,
                h,
                "JPEG",
                100,
                0
            ).then(
                ({ path, uri, size, name }) => {
                    resolve({ path, uri, size, name })
                }
            ).catch(
                (error) => {
                    reject(error)
                }
            );
        });
    }

    const takePicture = (photo) => {
        if (helper.hasProperty(photo, 'uri')) {
            resizeImage(photo).then(
                ({ path, uri, size, name }) => {
                    showBlockUI();
                    setState({
                        ...state,
                        isVisibleCamera: false,
                    })
                    //BLX
                    if (helper.isObject(EPosTransacTionTypeAttBO)
                        && EPosTransacTionTypeAttBO.PaperTypeID === 2
                        && EPosTransacTionTypeAttBO.ShortName.toUpperCase().includes("BLXMT")) {
                        var hardurl = "http://**********:9999/static/gplx.jpg";
                        getInfoDriverGLXByImage(uri)
                            .then((response) => {
                                hideBlockUI();
                                console.log("response");
                                //hideBlockUI();
                                let temp_lstGroupPaperTypeInforBO = [...lstGroupPaperTypeInforBO]
                                let temp_CusPaperTypeInforBOList = [...lstPaperTypeInforBO]
                                temp_CusPaperTypeInforBOList = temp_CusPaperTypeInforBOList.map(el => (
                                    el.PaperTypeID === EPosTransacTionTypeAttBO.PaperTypeID ? { ...el, IDNumber: response?.no } : { ...el }
                                ))
                                temp_lstGroupPaperTypeInforBO[indexLoanInfoSelected] = {
                                    ...temp_lstGroupPaperTypeInforBO[indexLoanInfoSelected],
                                    cus_PaperTypeInforBOList: temp_CusPaperTypeInforBOList,
                                }
                                setLstGroupPaperTypeInforBO(temp_lstGroupPaperTypeInforBO);
                                setLstPaperTypeInforBO(temp_CusPaperTypeInforBOList);
                                updateImgEPosTransacTionTypeAtt(uri, name, temp_lstGroupPaperTypeInforBO);
                            })
                            .catch(
                                err => {
                                    hideBlockUI();
                                    updateImgEPosTransacTionTypeAtt(uri, name);
                                }
                            )
                    }
                    else {
                        hideBlockUI();
                        updateImgEPosTransacTionTypeAtt(uri, name);
                    }
                }
            ).catch(
                (error) => {
                    console.log("resizeImage", error);
                }
            );
        }
    }

    // //hàm nhà FE
    // //----->
    const takePictureFE = (imageInfo, PaperTypeID, index, data) => {
        setPaperTypeIDOFCus_PaperTypeInforBOList(PaperTypeID);
        setIndexOFEPosTransacTionTypeAttBOList(index);
        helper.resizeImage(imageInfo).then(({ path, uri, size, name }) => {
            showBlockUI();
            //BLX
            if (helper.isObject(data)
                && data.PaperTypeID === 2
                && data.ShortName.toUpperCase().includes("BLXMT")) {
                var hardurl = "http://**********:9999/static/gplx.jpg";
                getInfoDriverGLXByImage(uri)
                    .then((response) => {
                        hideBlockUI();
                        console.log("response");
                        //hideBlockUI();
                        let temp_lstGroupPaperTypeInforBO = [...lstGroupPaperTypeInforBO]
                        let temp_CusPaperTypeInforBOList = [...lstPaperTypeInforBO]
                        temp_CusPaperTypeInforBOList = temp_CusPaperTypeInforBOList.map(el => (
                            el.PaperTypeID === data.PaperTypeID ? { ...el, IDNumber: response?.no } : { ...el }
                        ))
                        temp_lstGroupPaperTypeInforBO[indexLoanInfoSelected] = {
                            ...temp_lstGroupPaperTypeInforBO[indexLoanInfoSelected],
                            cus_PaperTypeInforBOList: temp_CusPaperTypeInforBOList,
                        }
                        setLstGroupPaperTypeInforBO(temp_lstGroupPaperTypeInforBO);
                        setLstPaperTypeInforBO(temp_CusPaperTypeInforBOList);
                        updateImgEPosTransacTionTypeAttFE(uri, name, PaperTypeID, index, temp_lstGroupPaperTypeInforBO);
                    })
                    .catch(
                        err => {
                            hideBlockUI();
                            updateImgEPosTransacTionTypeAttFE(uri, name, PaperTypeID, index);
                        }
                    )
            }
            else {
                hideBlockUI();
                updateImgEPosTransacTionTypeAttFE(uri, name, PaperTypeID, index);
            }
        }).catch((error) => {
            console.log("resizeImage", error);
        });
    }

    const updateImgEPosTransacTionTypeAttFE = (uri, name, PaperTypeID, index, data = null) => {
        getBodyUpload({
            uri: uri,
            type: 'image/jpeg',
            name: name,
        }).then(res => {
            let temp_lstGroupPaperTypeInforBO = [...data ?? lstGroupPaperTypeInforBO];
            let temp_CusPaperTypeInforBOList = [...temp_lstGroupPaperTypeInforBO[indexLoanInfoSelected].cus_PaperTypeInforBOList]
            let temp_CusEPosTransacTionTypeAttBOList = temp_CusPaperTypeInforBOList.find(x => x.PaperTypeID == PaperTypeID).cus_EPosTransacTionTypeAttBOList;
            temp_CusEPosTransacTionTypeAttBOList[index] = {
                ...temp_CusEPosTransacTionTypeAttBOList[index],
                client_FilePath: res.url,
            }

            let newtLst = [];
            let findObj = temp_lstGroupPaperTypeInforBO.find(item => item.LoanInfoID == LoanInfoID)
            if (findObj != {} && findObj.cus_PaperTypeInforBOList != {}) {
                findObj.cus_PaperTypeInforBOList.forEach((e) => {
                    if (
                        newtLst.find((item) => item.PaperTypeID == e.PaperTypeID) == undefined
                    ) {
                        newtLst.push(e);
                    }
                });
            }
            setLstPaperTypeInforBO(newtLst);
        });
    }

    // //<-----

    const updateImgEPosTransacTionTypeAtt = (uri, name, data = null) => {
        console.log("vo 1 ", indexOFEPosTransacTionTypeAttBOList);
        getBodyUpload({
            uri: uri,
            type: 'image/jpeg',
            name: name,
        }).then(res => {
            let temp_lstGroupPaperTypeInforBO = [...data ?? lstGroupPaperTypeInforBO];
            let temp_CusPaperTypeInforBOList = [...temp_lstGroupPaperTypeInforBO[indexLoanInfoSelected].cus_PaperTypeInforBOList]
            let temp_CusEPosTransacTionTypeAttBOList = temp_CusPaperTypeInforBOList.find(x => x.PaperTypeID == paperTypeIDOFCus_PaperTypeInforBOList).cus_EPosTransacTionTypeAttBOList;
            temp_CusEPosTransacTionTypeAttBOList[indexOFEPosTransacTionTypeAttBOList] = {
                ...temp_CusEPosTransacTionTypeAttBOList[indexOFEPosTransacTionTypeAttBOList],
                client_FilePath: res.url,
            }

            let newtLst = [];
            let findObj = temp_lstGroupPaperTypeInforBO.find(item => item.LoanInfoID == LoanInfoID)
            if (findObj != {} && findObj.cus_PaperTypeInforBOList != {}) {
                findObj.cus_PaperTypeInforBOList.forEach((e) => {
                    if (
                        newtLst.find((item) => item.PaperTypeID == e.PaperTypeID) == undefined
                    ) {
                        newtLst.push(e);
                    }
                });
            }
            setLstPaperTypeInforBO(newtLst);
        });
    }

    const GetFromGroupPaperTypeInfo = (group) => {
        let newtLst = [];
        if (helper.IsNonEmptyArray(group)) {
            group.forEach((e) => {
                if (
                    newtLst.find((item) => item.LoanInfoID == e.LoanInfoID) == undefined
                ) {
                    newtLst.push({ title: e.PaperTypeNameList, selected: LoanInfoID == e.LoanInfoID ? true : false, value: e.LoanInfoID });
                }
            });
        };
        return newtLst;
    };

    const GetPaperTypeInforBOByLoanInfoID = (LoanInfoID) => {
        console.log("lstGroupPaperTypeInforBO ", lstGroupPaperTypeInforBO);
        let newtLst = [];
        if (lstGroupPaperTypeInforBO != null && lstGroupPaperTypeInforBO.length > 0) {
            let findObj = lstGroupPaperTypeInforBO.find(item => item.LoanInfoID == LoanInfoID)
            if (findObj != {} && findObj.cus_PaperTypeInforBOList != {}) {
                parent.client_IsHasRegistrationBook = findObj.IsHasRegistrationBook;
                findObj.cus_PaperTypeInforBOList.forEach((e) => {
                    if (
                        newtLst.find((item) => item.PaperTypeID == e.PaperTypeID) == undefined
                    ) {
                        newtLst.push(e);
                    }

                });
            }
            console.log("parent.client_IsHasRegistrationBook ", parent.client_IsHasRegistrationBook);
        }
        return newtLst;
    };

    const GetPaperTypeInforByEP = (objEPOSTransactionBO) => {

        if (!helper.isNumber(objEPOSTransactionBO.TotalPrepaid)
            && !helper.IsNonEmptyString(objEPOSTransactionBO.TotalPrepaid)) {
            objEPOSTransactionBO.TotalPrepaid = 0;
        }

        if (!isBroadcastInstalment) {
            return new Promise((resolve, reject) => {
                dispatch(installmentAction.getPaperTypeInforByEP(objEPOSTransactionBO))
                    .then((res) => {
                        console.log("reponsePaperType", res);
                        if (res != null && res.length > 0) {
                            var GroupPaperTypeInforBOSelect = res.find(x => x.IsSelected == true);
                            if (GroupPaperTypeInforBOSelect != null && GroupPaperTypeInforBOSelect.LoanInfoID > 0
                                && res.find(x => x.LoanInfoID === GroupPaperTypeInforBOSelect.LoanInfoID) != null) {
                                parent.client_IsHasRegistrationBook = GroupPaperTypeInforBOSelect.IsHasRegistrationBook;
                                let index = res.findIndex(x => x.LoanInfoID === GroupPaperTypeInforBOSelect.LoanInfoID);
                                setIndexLoanInfoSelected(index);
                                setLoanInfoID(GroupPaperTypeInforBOSelect.LoanInfoID);
                                let temp_lstGroupPaperTypeInforBO = [...res]
                                temp_lstGroupPaperTypeInforBO.map(el => (
                                    el.LoanInfoID === GroupPaperTypeInforBOSelect.LoanInfoID ? { ...el, IsSelected: true } : { ...el, IsSelected: false }
                                ))
                                temp_lstGroupPaperTypeInforBO[index] = GroupPaperTypeInforBOSelect;
                                let temp_CusPaperTypeInforBOList = [...lstPaperTypeInforBO]
                                temp_CusPaperTypeInforBOList = GroupPaperTypeInforBOSelect.cus_PaperTypeInforBOList;
                                setLstPaperTypeInforBO(temp_CusPaperTypeInforBOList);
                            }
                        }
                        resolve([...res])
                    }).catch((err) => {
                        Alert.alert(
                            translate('common.notification'),
                            err.msgError,
                            [
                                {
                                    text: translate('common.btn_close'), onPress: () => {
                                    }
                                }
                            ],
                            { cancelable: false }
                        )
                    });
            })

        }
    }

    const getInfoDriverGLXByImage = (uriImage) => {
        return new Promise((resolve, reject) => {
            let bodyFromData = new FormData();
            bodyFromData.append('file', {
                uri: uriImage,
                type: 'image/jpg',
                name: "getInfoDriverGLXByImage" + dateHelper.getTimestamp()
            });
            bodyFromData.append('client_id', `MWGPOS_${userInfo.userName}`);
            installmentAction.getInfoDriverGLXByImage(bodyFromData)
                .then(
                    res => {
                        resolve(res)
                    }
                ).catch(
                    err => {
                        console.log("err getInfoCustomerByImage ", err);
                        reject(err);
                    }
                );
        });
    }

    const selectItemGroupPaperTypeInfo = (data, item) => {
        let find = obj => obj.value === item.value;
        let index = data.findIndex(find)
        const newData = [...data];
        data.forEach((item) => {
            item.selected = false;
        });
        setState({
            ...state,
            EPOSTransactionBO: {
                ...state.EPOSTransactionBO,
                LoanInfoID: 1
            }
        });
        data[index].selected = true;
        //gán IsSelected cho LoanInfoID ko dùng LoanInfoID nửa
        let newlstGroupPaperTypeInforBO = lstGroupPaperTypeInforBO.map(el => (
            el.LoanInfoID === item.value ? { ...el, IsSelected: true } : { ...el, IsSelected: false }
        ))
        setLstGroupPaperTypeInforBO(newlstGroupPaperTypeInforBO);
        setLoanInfoID(data[index].value);
        setIndexLoanInfoSelected(index);
        setRadioGroupPaperTypeInfoList(newData);
        setLstPaperTypeInforBO(GetPaperTypeInforBOByLoanInfoID(newData[index].value));
    }

    const uploadPicture = (fromData) => {
        return new Promise((resolve, reject) => {
            getImageCDN(fromData).then(cdnImages => {
                console.log("uploadPicture url", API_GET_IMAGE_CDN_NEW + cdnImages[0]);
                resolve({ url: API_GET_IMAGE_CDN_NEW + cdnImages[0] })
            }).catch(error => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    translate('instalmentManager.upload_image_error'),
                    [
                        {
                            text: translate('common.btn_skip'),
                            onPress: hideBlockUI,
                            style: "cancel"
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            onPress: () => uploadPicture(fromData),
                            style: "default"
                        },
                    ],
                    { cancelable: false },
                );
            })
        });
    }

    const getBodyUpload = (file) => {
        const body = helper.createFormData({ uri: file.uri, type: file.type, name: file.name, path: STEP_THREE });
        return new Promise((resolve, reject) => {
            uploadPicture(body).then(res => {
                resolve(res)
            });
        });
    }

    const renderEPosTransacTionTypeAttItem = (item, index) => {
        return (
            <View key={index}>
                {installmentHelper.CheckShowField("client_CustomerIsFBOwner") && item.IsRegistrationBook &&
                    <View style={{ marginBottom: -10, marginTop: 5 }}>
                        <TouchableOpacity
                            style={{
                                flexDirection: "row",
                                alignItems: "center",
                                marginBottom: 10
                            }}
                            onPress={async () => {
                                await setState({
                                    ...state,
                                    EPOSTransactionBO: {
                                        ...state.EPOSTransactionBO,
                                        client_CustomerIsFBOwner: !state.EPOSTransactionBO.client_CustomerIsFBOwner
                                    }
                                });
                            }}
                            activeOpacity={1}
                        >
                            <Icon
                                iconSet="Ionicons"
                                name={state.EPOSTransactionBO.client_CustomerIsFBOwner ? "md-checkbox-outline" : "ios-square-outline"}
                                style={{
                                    fontSize: 18,
                                    color: state.EPOSTransactionBO.client_CustomerIsFBOwner ? COLORS.icFF8900 : COLORS.ic333333,
                                    borderRadius: 20,
                                    backgroundColor: COLORS.bgFFFFFF,
                                }}
                            />
                            <MyText
                                text={translate('instalmentManager.no_id_card')}
                                style={{
                                    fontWeight: state.EPOSTransactionBO.client_CustomerIsFBOwner ? 'bold' : '',
                                    marginLeft: 5,
                                    marginBottom: 3,
                                    color: state.EPOSTransactionBO.client_CustomerIsFBOwner ? COLORS.txt543729 : COLORS.txt000000,
                                }}
                            />
                        </TouchableOpacity>
                    </View>
                }
                {!item.IsHiddenIDNumber &&
                    <View style={{ marginVertical: 5 }}>
                        <MyText
                            style={{ fontWeight: '600' }}
                            text={`${translate('instalmentManager.enter_code')} ${item.PaperTypeName} ${item.IsShowIDNumber2 ? translate('instalmentManager.front') : ''}:`}
                            children={item.IsRequireIDNumber ? <MyText text={"*"} style={{ color: COLORS.txtFF0000, fontSize: 16 }} /> : null}
                        />
                        <FieldInput
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                paddingHorizontal: 10,
                                marginBottom: 0,
                                backgroundColor: COLORS.bgFFFFFF,
                                opacity: 1,
                                paddingVertical: 8,
                                //marginTop: 10
                            }}
                            placeholder={`${translate('instalmentManager.enter_code')} ${item.PaperTypeName} ${item.IsShowIDNumber2 ? translate('instalmentManager.front') : ''}`}
                            placeholderTextColor={COLORS.txtCCCCCC}
                            value={item.IDNumber ?? ""}
                            numberOfLines={1}
                            multiline={false}
                            keyboardType={"default"}
                            returnKeyType={"done"}
                            onChangeText={(text) => {
                                let regExp = new RegExp("^[a-zA-Z0-9 ]{0,12}$");
                                let temp_lstGroupPaperTypeInforBO = [...lstGroupPaperTypeInforBO]
                                let temp_CusPaperTypeInforBOList = [...lstPaperTypeInforBO]
                                if (regExp.test(text) || text == "") {
                                    temp_CusPaperTypeInforBOList[index] = {
                                        ...temp_CusPaperTypeInforBOList[index],
                                        IDNumber: text,
                                    }
                                    temp_lstGroupPaperTypeInforBO[indexLoanInfoSelected] = {
                                        ...temp_lstGroupPaperTypeInforBO[indexLoanInfoSelected],
                                        cus_PaperTypeInforBOList: temp_CusPaperTypeInforBOList,
                                    }
                                    setLstGroupPaperTypeInforBO(temp_lstGroupPaperTypeInforBO);
                                    setLstPaperTypeInforBO(temp_CusPaperTypeInforBOList);
                                }
                            }}
                            width={constants.width - 20}
                            height={40}
                            maxLength={12}
                            clearText={() => {
                                Keyboard.dismiss();
                                let temp_lstGroupPaperTypeInforBO = [...lstGroupPaperTypeInforBO]
                                let temp_CusPaperTypeInforBOList = [...lstPaperTypeInforBO]
                                temp_CusPaperTypeInforBOList[index] = {
                                    ...temp_CusPaperTypeInforBOList[index],
                                    IDNumber: "",
                                }
                                temp_lstGroupPaperTypeInforBO[indexLoanInfoSelected] = {
                                    ...temp_lstGroupPaperTypeInforBO[indexLoanInfoSelected],
                                    cus_PaperTypeInforBOList: temp_CusPaperTypeInforBOList,
                                }

                                setLstGroupPaperTypeInforBO(temp_lstGroupPaperTypeInforBO);
                                setLstPaperTypeInforBO(temp_CusPaperTypeInforBOList);
                            }}
                        />
                    </View>

                }
                {item.IsShowIDNumber2 &&
                    <View style={{ marginVertical: 5 }}>
                        <MyText
                            style={{ fontWeight: '600' }}
                            text={`${translate('instalmentManager.enter_code')} ${item.PaperTypeName} ${translate('instalmentManager.back')}`}
                            children={item.IsRequireIDNumber2 ? <MyText text={"*"} style={{ color: COLORS.txtFF0000, fontSize: 16 }} /> : null}
                        />
                        <FieldInput
                            styleInput={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                paddingHorizontal: 10,
                                marginBottom: 0,
                                backgroundColor: COLORS.bgFFFFFF,
                                opacity: 1,
                                paddingVertical: 8,
                                //marginTop: 10
                            }}
                            placeholder={`${translate('instalmentManager.enter_code')} ${item.PaperTypeName} ${translate('instalmentManager.back')}`}
                            placeholderTextColor={COLORS.txtCCCCCC}
                            value={item.IDNumber2 ?? ""}
                            numberOfLines={1}
                            multiline={false}
                            keyboardType={"default"}
                            returnKeyType={"done"}
                            onChangeText={(text) => {
                                let regExp = new RegExp("^[a-zA-Z0-9 ]{0,12}$");
                                if (regExp.test(text) || text == "") {
                                    let temp_lstGroupPaperTypeInforBO = [...lstGroupPaperTypeInforBO]
                                    let temp_CusPaperTypeInforBOList = [...lstPaperTypeInforBO]
                                    temp_CusPaperTypeInforBOList[index] = {
                                        ...temp_CusPaperTypeInforBOList[index],
                                        IDNumber2: text,
                                    }
                                    temp_lstGroupPaperTypeInforBO[indexLoanInfoSelected] = {
                                        ...temp_lstGroupPaperTypeInforBO[indexLoanInfoSelected],
                                        cus_PaperTypeInforBOList: temp_CusPaperTypeInforBOList,
                                    }

                                    setLstGroupPaperTypeInforBO(temp_lstGroupPaperTypeInforBO);
                                    setLstPaperTypeInforBO(temp_CusPaperTypeInforBOList);
                                }
                            }}
                            width={constants.width - 20}
                            height={40}
                            maxLength={12}
                            clearText={() => {
                                Keyboard.dismiss();
                                let temp_lstGroupPaperTypeInforBO = [...lstGroupPaperTypeInforBO]
                                let temp_CusPaperTypeInforBOList = [...lstPaperTypeInforBO]
                                temp_CusPaperTypeInforBOList[index] = {
                                    ...temp_CusPaperTypeInforBOList[index],
                                    IDNumber2: "",
                                }
                                temp_lstGroupPaperTypeInforBO[indexLoanInfoSelected] = {
                                    ...temp_lstGroupPaperTypeInforBO[indexLoanInfoSelected],
                                    cus_PaperTypeInforBOList: temp_CusPaperTypeInforBOList,
                                }

                                setLstGroupPaperTypeInforBO(temp_lstGroupPaperTypeInforBO);
                                setLstPaperTypeInforBO(temp_CusPaperTypeInforBOList);
                            }}
                        />
                    </View>
                }
                {!!item.cus_EPosTransacTionTypeAttBOList &&
                    <View style={{
                        paddingHorizontal: 10,
                        borderWidth: 1,
                        borderColor: COLORS.bdE4E4E4,
                        padding: 10,
                        marginVertical: 10,
                        borderRadius: 5,
                        justifyContent: "center",
                        flexDirection: (item.PaperTypeID == 3 ? "row" : "column"),
                        flexWrap: 'wrap',
                    }}>
                        {
                            item.cus_EPosTransacTionTypeAttBOList.map((cus, index) => {
                                console.log("cus: ", cus);
                                return (
                                    <View style={{
                                        marginVertical: 0,
                                        paddingBottom: 10,
                                        width: (item.PaperTypeID == 3 ? "50%" : "100%"),
                                    }}>
                                        <MyText
                                            text={cus.AttactTypeName}
                                            style={{ fontSize: 15, marginVertical: 5 }}
                                            children={cus.IsRequireAttach ? <MyText text={"*"} style={{ color: COLORS.txtFF0000, fontSize: 16 }} /> : null}
                                        />
                                        {true ? (
                                            <DocCamera
                                                uriImage={cus.client_FilePath?.includes("http") ? cus.client_FilePath : `${cus.client_Image ?? ""}`}
                                                onDelete={removePicture("client_FilePath", "client_Image", cus.PaperTypeID, index)}
                                                onTakePicture={(response) => {
                                                    console.log("response onTakePicture", response);
                                                    takePictureFE(response, cus.PaperTypeID, index, cus);
                                                }}
                                                docSide={HYPER_VERGE.DocumentNoneSide}
                                                docType={HYPER_VERGE.DocumentOTHER}
                                                content={HYPER_VERGE.ContentOther}
                                            />
                                        ) : (
                                            <ImageProcess
                                                onCamera={showCamera("client_FilePath", cus.PaperTypeID, index, cus)}
                                                urlImageLocal={cus.client_FilePath?.includes("http") ? cus.client_FilePath : `${cus.client_Image ?? ""}`}
                                                urlImageRemote={cus.client_FilePath?.includes("http") ? cus.client_FilePath : `${cus.client_Image ?? ""}`}
                                                deleteImage={removePicture("client_FilePath", "client_Image", cus.PaperTypeID, index)}
                                                type={item.PaperTypeID == 3 ? 2 : 1}
                                            />
                                        )}
                                    </View>
                                )
                            })
                        }
                    </View>
                }
                <Dash dashColor={COLORS.txtCCCCCC} style={{ width: constants.width, paddingVertical: 5 }} />
            </View>
        );
    }

    const checkValidateData = () => {
        console.log(LoanInfoID);
        var FieldObjectsByStep = installmentHelper.initObjectFields.filter(item => item.Step == parent.currentPosition);
        for (var i = 0; i < FieldObjectsByStep.length; i++) {
            var item = FieldObjectsByStep[i];
            if (item.FieldName == "LoanInfoID" && LoanInfoID > 0) {
                continue;
            }
            if (!validateObject(item, state.EPOSTransactionBO)) {
                return false;
            }
        };
        var GroupPaperTypeInforSelect = lstGroupPaperTypeInforBO.find(item => item.LoanInfoID == LoanInfoID)
        if (GroupPaperTypeInforSelect != null && GroupPaperTypeInforSelect.cus_PaperTypeInforBOList != null) {
            var isError = false;
            if (GroupPaperTypeInforSelect.cus_PaperTypeInforBOList != undefined && GroupPaperTypeInforSelect.cus_PaperTypeInforBOList.length > 0) {
                // danh sách giấy tờ
                for (var i = 0; i < GroupPaperTypeInforSelect.cus_PaperTypeInforBOList.length; i++) {
                    var itemDetail = GroupPaperTypeInforSelect.cus_PaperTypeInforBOList[i];
                    // Số giấy tờ
                    if (itemDetail.IsRequireIDNumber && (itemDetail.IDNumber == undefined || itemDetail.IDNumber.length == 0)) {
                        Alert.alert(
                            translate('common.notification'),
                            translate('instalmentManager.please_enter_record_number') + itemDetail.PaperTypeName + "]",
                            [
                                {
                                    text: translate('common.btn_close'), onPress: () => {

                                    }
                                }
                            ],
                            { cancelable: false }
                        )
                        return false;
                    }
                    else if (itemDetail.IDNumber != undefined && itemDetail.IDNumber.length > 0 && itemDetail.IDNumber.length <= 4) {
                        Alert.alert(
                            translate('common.notification'),
                            translate('instalmentManager.record_number') + itemDetail.PaperTypeName + translate('instalmentManager.at_least_5_letters'),
                            [
                                {
                                    text: translate('common.btn_close'), onPress: () => {

                                    }
                                }
                            ],
                            { cancelable: false }
                        )
                        return false;
                    }
                    // Số giấy tờ mặt sau
                    else if (itemDetail.IsRequireIDNumber2 && (itemDetail.IDNumber2 == undefined || itemDetail.IDNumber2.length == 0)) {
                        Alert.alert(
                            translate('common.notification'),
                            translate('instalmentManager.please_enter_record_number_2') + itemDetail.PaperTypeName + "]",
                            [
                                {
                                    text: translate('common.btn_close'), onPress: () => {

                                    }
                                }
                            ],
                            { cancelable: false }
                        )
                        return false;
                    }
                    else if (itemDetail.IDNumber2 != undefined && itemDetail.IDNumber2.length > 0 && itemDetail.IDNumber2.length <= 4) {
                        Alert.alert(
                            translate('common.notification'),
                            translate('instalmentManager.record_number_2') + itemDetail.PaperTypeName + translate('instalmentManager.at_least_5_letters'),
                            [
                                {
                                    text: translate('common.btn_close'), onPress: () => {

                                    }
                                }
                            ],
                            { cancelable: false }
                        )
                        return false;
                    }

                    if (itemDetail.cus_EPosTransacTionTypeAttBOList != undefined && itemDetail.cus_EPosTransacTionTypeAttBOList.length > 0) {
                        // danh sách file đính kèm của giấy tờ
                        for (var k = 0; k < itemDetail.cus_EPosTransacTionTypeAttBOList.length; k++) {
                            var itemAttBO = itemDetail.cus_EPosTransacTionTypeAttBOList[k];
                            if (itemAttBO.IsRequireAttach && (itemAttBO.client_FilePath == undefined || itemAttBO.client_FilePath.length == 0)) {
                                Alert.alert(
                                    translate('common.notification'),
                                    translate('instalmentManager.please_capture') + itemAttBO.AttactTypeName + "]",
                                    [
                                        {
                                            text: translate('common.btn_close'), onPress: () => {

                                            }
                                        }
                                    ],
                                    { cancelable: false }
                                )
                                return false;
                            }
                        }
                    }
                }
            }
        }
        return true;
    }

    useEffect(() => {
        if (parent.indexStep == 3)
            getCacheLoanInfoBySelect();
    }, [parent.currentPosition]);

    const getCacheLoanInfoBySelect = () => {
        var dataCacheList = dataCacheInstallmentReducer.lstCacheDataEP.data;
        if (dataCacheList != null && dataCacheList.length > 0) {
            var dataCache = dataCacheList.find(p => p.EPTransactionID === state.EPOSTransactionBO.EPTransactionID);
            if (dataCache != null && dataCache.GroupPaperTypeInforBOList != null && dataCache.GroupPaperTypeInforBOList.length > 0) {
                var GroupPaperTypeInforBOCacheSelect = dataCache.GroupPaperTypeInforBOList.find(x => x.IsSelected == true);
                parent.client_IsHasRegistrationBook = GroupPaperTypeInforBOCacheSelect.IsHasRegistrationBook;
                if (GroupPaperTypeInforBOCacheSelect != null && GroupPaperTypeInforBOCacheSelect.LoanInfoID > 0
                    && lstGroupPaperTypeInforBO.find(x => x.LoanInfoID === GroupPaperTypeInforBOCacheSelect.LoanInfoID) != null) {
                    let index = lstGroupPaperTypeInforBO.findIndex(x => x.LoanInfoID === GroupPaperTypeInforBOCacheSelect.LoanInfoID);
                    setLoanInfoID(GroupPaperTypeInforBOCacheSelect.LoanInfoID);
                    setIndexLoanInfoSelected(index);
                    let temp_lstGroupPaperTypeInforBO = [...lstGroupPaperTypeInforBO]
                    temp_lstGroupPaperTypeInforBO.map(el => (
                        el.LoanInfoID === GroupPaperTypeInforBOCacheSelect.LoanInfoID ? { ...el, IsSelected: true } : { ...el, IsSelected: false }
                    ))
                    temp_lstGroupPaperTypeInforBO[index] = GroupPaperTypeInforBOCacheSelect;
                    let temp_CusPaperTypeInforBOList = [...lstPaperTypeInforBO]
                    temp_CusPaperTypeInforBOList = GroupPaperTypeInforBOCacheSelect.cus_PaperTypeInforBOList;
                    console.log("temp_lstGroupPaperTypeInforBO: ", temp_lstGroupPaperTypeInforBO);
                    setLstGroupPaperTypeInforBO(temp_lstGroupPaperTypeInforBO);
                    setLstPaperTypeInforBO(temp_CusPaperTypeInforBOList);
                }
            }
        }
    }

    return (
        <KeyboardAwareScrollView
            ref={refScroll3}
            enableResetScrollToCoords={false}
            keyboardShouldPersistTaps="always"
            bounces={false}
            overScrollMode="always"
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            extraScrollHeight={60}>
            <View style={{
                paddingHorizontal: 10,
                width: constants.width,
                height: "auto"
            }}
                activeOpacity={0.7}
            >
                <View style={styles.tab_list}>
                    <View style={[styles.header_title, {
                        flexDirection: "row",
                        alignItems: "center", justifyContent: "flex-start"
                    }]}>
                        <TooltipWrapper
                            placement={"bottom"}
                            content={
                                <View>
                                    <MyText style={{ color: COLORS.txtFFFFFF, fontWeight: 'bold' }}
                                        text={"Sổ hộ khẩu:"}
                                    />
                                    <MyText style={{ color: COLORS.txtFFFFFF }}
                                        text={"+ HC, FE, MC, MAFC: chụp 2 trang 1 lúc, chụp đầy đủ 16 trang"}
                                    />
                                    <MyText style={{ color: COLORS.txtFFFFFF }}
                                        text={"+ ACS: chụp 3 trang (chụp trang bìa, trang chủ hộ, trang KH làm hồ sơ)"}
                                    />
                                </View>
                            }
                            wrapper={
                                <View style={{ flexDirection: "row", alignItems: "center" }}>
                                    <MyText
                                        style={{
                                            //width: 40,
                                            fontWeight: 'bold',
                                            color: COLORS.txtFFFFFF,
                                            marginRight: constants.getSize(2)
                                        }}
                                        text={translate('instalmentManager.record_type')}
                                        addSize={2}
                                    />
                                    <Icon
                                        iconSet={"FontAwesome"}
                                        name={"question-circle"}
                                        size={16}
                                        color={COLORS.txtFF0000}
                                        style={{
                                            marginLeft: 3
                                        }}
                                    />
                                </View>
                            }
                        />
                    </View>
                </View>

                {/* //sss */}
                {installmentHelper.CheckShowField("LoanInfoID") ? (
                    <View style={{ marginVertical: 5 }}>
                        {/* <View style={{flexDirection: 'row'}}>
                            <RadioButton
                                style={{

                                }}
                                containerStyle={{
                                    width: constants.width - constants.getSize(25),
                                    marginVertical: 10
                                }}
                                dataItems={radioGroupPaperTypeInfoList}
                                selectItem={
                                    (index) => {selectItemGroupPaperTypeInfo(radioGroupPaperTypeInfoList, index) }
                                }
                                mainComponent={(item) => {
                                    return (
                                        <MyText
                                            text={item.title}
                                            style={{
                                                color: item.selected ? COLORS.txtFF8900 : COLORS.txt333333,
                                                marginLeft: 2,
                                                fontSize: 15
                                            }}
                                        />
                                    )
                                }}
                            />

                        </View> */}
                        <View style={[styles.view_picker, { marginVertical: 5 }]}>
                            <Picker
                                label={"title"}
                                value={"value"}
                                data={radioGroupPaperTypeInfoList}
                                valueSelected={LoanInfoID}
                                onChange={(item) => {
                                    selectItemGroupPaperTypeInfo(radioGroupPaperTypeInfoList, item);
                                }}
                                defaultLabel={translate('instalmentManager.header_record_type')}
                                style={{
                                    flex: 1,
                                    flexDirection: "row",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    height: 30,
                                    marginVertical: 5,
                                    marginLeft: -5,
                                    marginRight: -10,
                                }}
                            />
                        </View>
                    </View>
                ) : null}
                <FlatList
                    data={lstPaperTypeInforBO}
                    renderItem={({ item, index }) => renderEPosTransacTionTypeAttItem(item, index)}
                    keyExtractor={(item, index) => index.toString()}
                    keyboardShouldPersistTaps={"always"}
                />
                <CaptureCamera
                    isVisibleCamera={state.isVisibleCamera}
                    disabledUploadImage={true}
                    takePicture={takePicture}
                    closeCamera={() => {
                        setState({ ...state, isVisibleCamera: false })
                    }}
                    selectPicture={() => {
                        selectImage();
                    }}
                />
                <View style={{
                    padding: 10,
                    width: constants.width
                }}
                    activeOpacity={0.7}
                >
                    <View
                        style={{
                            flexDirection: "row",
                            justifyContent: "space-between",
                            paddingHorizontal: 20,
                            marginVertical: 10,
                            justifyContent: 'center'
                        }}
                    >
                        <TouchableOpacity
                            style={[styles.btn, { backgroundColor: COLORS.btn4DA6FF }]}
                            activeOpacity={0.7}
                            onPress={() => {
                                if (checkValidateData()) {
                                    updatestate(state, parent.client_IsHasRegistrationBook, lstGroupPaperTypeInforBO);
                                    onNext();
                                }
                            }}
                        >
                            <MyText text={translate('common.btn_continue')} style={styles.txtBtn} />
                        </TouchableOpacity>
                    </View>
                </View>

            </View>
        </KeyboardAwareScrollView>
    );
}

export default StepThreeNew;
const styles = StyleSheet.create({
    fieldSet: {
        flexDirection: "row",
        marginVertical: 5,
        justifyContent: "space-between",
    },
    view_picker: {
        borderWidth: 1,
        borderColor: COLORS.bdE4E4E4,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 5,
        height: "auto",
        backgroundColor: COLORS.bgFFFFFF,
    },
    picker: {
        height: 30,
        marginVertical: 5,
        marginLeft: -5,
        marginRight: -10,
    },
    btn: {
        padding: 10,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 5,
        width: "47%",
        borderWidth: 1,
        borderColor: COLORS.bd4DA6FF,
        height: "auto",
    },
    txtBtn: {
        fontWeight: "bold",
        color: COLORS.txtFFFFFF,
    },
    wrapperInputUserCreated: {
        width: "100%",
        flexDirection: "row",
        //backgroundColor: COLORS.bgF5F5F5,
        justifyContent: "center",
        //paddingHorizontal: 10,
        paddingVertical: 10,
        paddingBottom: 10,
        alignItems: "center",
    },
    wrapperSearch: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        backgroundColor: COLORS.bgFFFFFF,
        borderRadius: 5,
        width: "100%",
        padding: 5,
        borderRadius: 5,
        borderWidth: 1,
        borderColor: COLORS.bdE4E4E4,
        height: "auto",
        backgroundColor: COLORS.bgFFFFFF,
        shadowColor: COLORS.sd000000,
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.1,
        elevation: 3,
    },
    inputUser: {
        width: "87%",
        paddingHorizontal: 5,
    },
    tab_list: {
        backgroundColor: COLORS.bg2FB47C,
        height: constants.getSize(36),
        //justifyContent: "center",
        flexDirection: "row",
        marginBottom: 5,
        paddingLeft: 10,
        marginHorizontal: -10,
        marginVertical: 10
    },
});
