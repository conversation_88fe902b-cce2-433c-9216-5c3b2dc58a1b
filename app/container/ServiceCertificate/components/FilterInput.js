import React, { useCallback, useEffect, useRef } from 'react';
import {
    View,
    TextInput,
    StyleSheet,
    Text,
    TouchableOpacity,
} from 'react-native';
import CheckBox from '@react-native-community/checkbox';
import Icon from 'react-native-vector-icons/Ionicons';
import { COLORS } from '@styles';
import { MyText } from '../../../components';

const FilterInput = ({
    searchQuery,
    onChangeSearchQuery,
    onSearch
}) => {
    const inputRef = useRef(null);

    useEffect(() => {
        inputRef.current?.focus();
    }, [])

    const handleClearInput = useCallback(() => {
        onChangeSearchQuery('');
        inputRef.current?.focus();
    }, [onChangeSearchQuery]);

    return (
        <View style={styles.container}>
            <View style={styles.searchWrapper}>
                {/* Search Input */}
                <View style={styles.inputContainer}>
                    <Icon name="search" size={20} color="#666" style={styles.icon} />
                    <TextInput
                        ref={inputRef}
                        style={styles.input}
                        placeholder="Nhập SĐT, Mã Đơn hàng"
                        value={searchQuery}
                        onChangeText={(text) => { onChangeSearchQuery(text) }}
                        returnKeyType="search"
                        onSubmitEditing={() => { onSearch(searchQuery) }}
                        maxLength={35}
                    />
                    {searchQuery.length > 0 && (
                        <TouchableOpacity onPress={handleClearInput}>
                            <Icon name="close" size={20} color="#666" style={styles.icon} />
                        </TouchableOpacity>
                    )}
                </View>
            </View>
        </View>
    );
};

// Define styles with better organization and optimization
const styles = StyleSheet.create({
    container: {
        backgroundColor: COLORS.bgFFFFFF,
    },
    searchWrapper: {
        backgroundColor: COLORS.bgF0F0F0,
        padding: 16,
    },
    checkboxItem: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
    },
    checkbox: {
        height: 16,
        width: 16,
        marginRight: 7,
    },
    inputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 8,
        paddingHorizontal: 10,
        backgroundColor: '#fff',
    },
    input: {
        flex: 1,
        height: 40,
        paddingVertical: 0,
    },
    icon: {
        marginHorizontal: 8,
    },
});

export default React.memo(FilterInput);