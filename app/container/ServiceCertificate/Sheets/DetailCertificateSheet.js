import * as React from 'react';
import { StyleSheet, View, TouchableOpacity, Pressable, TouchableWithoutFeedback } from 'react-native';
import { Icon, MyText, BottomSheet } from '@components';
import { COLORS } from '@styles';
import { constants } from '@constants';
import { ScrollView } from 'react-native-gesture-handler';

export default DetailCertificateSheet = ({
    bottomSheetModalRef,
    onChangeStatusSheet,
    data,
    handleCheckCertificate,
    onActive
}) => {
    const renderInner = () => {
        return (
            <ScrollView
                style={{
                    backgroundColor: COLORS.bgFFFFFF,
                    flex: 1,
                    padding: 16
                }}>
                {data?.certficateOfSaleOrderlst?.map(
                    ({
                        servicePackageName,
                        servicePackageCertCode,
                        IsSelected,
                        servicePackageCode,
                        certificateStatusCode,
                        certificateStatusName
                    }) => {
                        const disabled = certificateStatusCode !== "activationwaiting"
                        const id = `${servicePackageCode}_${servicePackageCertCode}`;
                        return (
                            <TouchableOpacity
                                disabled={disabled}
                                key={id}
                                onPress={() => handleCheckCertificate(id)}
                                activeOpacity={0.7}
                                style={styles.item}>

                                <Icon
                                    iconSet={'MaterialCommunityIcons'}
                                    name={
                                        IsSelected
                                            ? 'checkbox-marked'
                                            : 'checkbox-blank-outline'
                                    }
                                    color={disabled ? COLORS.bgC4C4C4 : COLORS.txt147EFB}
                                    size={22}
                                />
                                <View style={{ paddingLeft: 15 }}>
                                    <MyText selectable={false} style={styles.itemTitle}>
                                        {servicePackageName}{' '}
                                    </MyText>
                                    <MyText selectable={false} style={styles.itemText}>
                                        Mã gói dịch vụ:{' '}
                                        <MyText selectable={false} text={servicePackageCode} />
                                    </MyText>
                                    <MyText style={styles.itemText}>
                                        Mã chứng nhận:{' '}
                                        <MyText text={servicePackageCertCode} />
                                    </MyText>
                                    <MyText style={styles.itemText}>
                                        Trạng thái :{' '}
                                        <MyText style={{ color: COLORS.bg1E88E5 }} text={certificateStatusName} />
                                    </MyText>
                                </View>
                            </TouchableOpacity>
                        );
                    }
                )}
            </ScrollView>
        );
    };

    const handleComponent = () => {
        return (
            <View style={styles.handle}>
                <View style={{ flex: 1 }} />
                <View
                    style={{
                        flex: 6,
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}>
                    <MyText
                        addSize={1}
                        style={{
                            fontWeight: 'bold',
                            color: COLORS.txt000000
                        }}
                        text="Chứng nhận dịch vụ"
                    />
                </View>
                <View style={{ flex: 1 }}>
                    <TouchableWithoutFeedback
                        style={{ marginLeft: 10 }}
                        onPress={() => {
                            bottomSheetModalRef.current?.dismiss();
                        }}>
                        <Icon
                            iconSet={'MaterialIcons'}
                            name={'clear'}
                            color={COLORS.txt000000}
                            size={22}
                        />
                    </TouchableWithoutFeedback>
                </View>
            </View>
        );
    };

    const footerComponent = () => {
        const certificateList = data?.certficateOfSaleOrderlst;
        if (!certificateList?.length) return null;

        const allActive = certificateList.every(
            ({ certificateStatusCode }) => certificateStatusCode !== "activationwaiting"
        );
        if (allActive) return null;

        const hasSelectedTrue = certificateList.some(
            ({ IsSelected }) => IsSelected === true
        );
        if (!hasSelectedTrue) return null;

        return (
            <View
                style={{
                    backgroundColor: COLORS.bgFFFFFF,
                    justifyContent: 'center',
                    alignItems: 'center',
                    flexDirection: 'row',
                    height: 100,
                    paddingBottom: 50,
                    padding: 10
                }}>
                <Pressable style={styles.button} onPress={onActive}>
                    <MyText addSize={2} style={styles.text}>
                        {'KÍCH HOẠT'}
                    </MyText>
                </Pressable>
            </View>
        );

    };

    return (
        <BottomSheet
            bs={bottomSheetModalRef}
            snapPoints={['79%']}
            handleComponent={handleComponent}
            footerComponent={footerComponent}
            onChangeStatusSheet={onChangeStatusSheet}>
            {renderInner()}
        </BottomSheet>
    );
};

const styles = StyleSheet.create({
    button: {
        backgroundColor: COLORS.bg1E88E5,
        width: constants.width / 2,
        padding: 10,
        borderRadius: 10,
        alignItems: 'center'
    },
    text: {
        color: COLORS.txtFFFFFF,
        fontWeight: '600'
    },

    handle: {
        backgroundColor: COLORS.bgFFFFFF,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
        height: 50,
        elevation: 2,
        borderTopStartRadius: 22,
        borderTopEndRadius: 22,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: COLORS.bgC4C4C4
    },
    item: {
        padding: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
        alignItems: 'center',
        flexDirection: 'row'
    },
    itemTitle: {
        fontWeight: 'bold',
        paddingVertical: 5
    },
    itemText: {
        color: COLORS.bg7F7F7F,
        paddingVertical: 3
    },
    itemDetail: {
        color: COLORS.bg1E88E5,
        marginLeft: 7
    }
});
