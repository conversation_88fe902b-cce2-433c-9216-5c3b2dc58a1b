import React, { useState, useCallback, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import { Alert, FlatList, StyleSheet, TouchableOpacity, View } from 'react-native';
import SafeAreaView from 'react-native-safe-area-view';
import { MyText } from '@components';
import FilterInput from '../components/FilterInput';
import { COLORS } from '@styles';
import { activeCertificate, searchCertificate } from '../action';
import { BaseLoading, hideBlockUI, Icon, showBlockUI } from '../../../components';
import { debounce, helper } from '../../../common';
import DetailCertificateSheet from "../Sheets/DetailCertificateSheet"
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { translate } from '@translate';

// Constants
const STATUS_CODES = {
    ACTIVE: 'activated',
    INACTIVE: 'activationwaiting'
};

const CertificateScreen = () => {
    const { storeID, languageID, moduleID, userName } = useSelector(
        (state) => state.userReducer
    );

    const [searchState, setSearchState] = useState({
        isLoading: false,
        isEmpty: false,
        isError: false,
        description: '',
        data: []
    });
    const [searchQuery, setSearchQuery] = useState('');
    const [itemSelected, setItemSelected] = useState({})

    const sheetRef = useRef(null)

    const onChangeSearchQuery = useCallback((value) => {
        setSearchQuery(value);
        // if (helper.IsNonEmptyString(value)) {
        //     debounceSearchProducts(value);
        // }
    }, []);
    // const debounceSearchProducts = useCallback(
    //     debounce((value) => handleSearch(value), 2500),
    //     [searchQuery]
    // );



    const handleSearch = async (query = '') => {
        setSearchState(prev => ({
            ...prev,
            isLoading: true,
            isEmpty: false,
            isError: false,
            description: '',
            data: []
        }));

        try {
            const body = {
                moduleId: moduleID,
                languageId: languageID,
                loginStoreId: storeID,
                loginUser: userName,
                keyword: query,
            };

            const results = await searchCertificate(body);

            setSearchState(prev => ({
                ...prev,
                isLoading: false,
                isEmpty: !helper.IsNonEmptyArray(results),
                description: helper.IsNonEmptyArray(results) ? '' : 'Không tìm thấy chứng nhận',
                data: results || []
            }));
        } catch (error) {
            console.log("🚀 ~ handleSearch ~ error:", error)
            const content = error?.msgError
            setSearchState(prev => ({
                ...prev,
                isLoading: false,
                description: content || 'Đã xảy ra lỗi',
                data: [],
                isError: true
            }));
        }
    };

    const handleGetDetail = async (item) => {
        if (!item.isDelivery) {
            return Alert.alert("Thông báo", "Đơn hàng chưa được giao.")
        }
        await new Promise((resolve) => {
            setItemSelected((prev) => {
                resolve();
                return {
                    ...item,
                    certficateOfSaleOrderlst: item.certficateOfSaleOrderlst.map(cert => ({
                        ...cert,
                        IsSelected: cert.certificateStatusCode == "activationwaiting" ? true : false
                    }))
                };
            });
        });
        sheetRef.current?.present();
    };
    const handleCheckCertificate = (id) => {
        setItemSelected((prev) => {
            return {
                ...prev,
                certficateOfSaleOrderlst: prev.certficateOfSaleOrderlst.map(cert => ({
                    ...cert,
                    IsSelected: id == `${cert.servicePackageCode}_${cert.servicePackageCertCode}` ? !cert.IsSelected : cert.IsSelected
                }))
            };
        });

    };


    const onActive = async () => {
        showBlockUI()
        try {
            const selectedItems = itemSelected.certficateOfSaleOrderlst
                ?.filter(({ IsSelected }) => IsSelected) || [];
            const servicePackageCodes = selectedItems.map(({ servicePackageCode }) => servicePackageCode);
            const servicePackageCertIds = selectedItems.map(({ servicePackageCertId }) => servicePackageCertId);
            const body = {
                "moduleId": moduleID,
                "languageId": languageID,
                "loginStoreId": storeID,
                "loginUser": userName,
                "deliveryUserUpdateTime": itemSelected.deliveryUserUpdateTime,
                "servicePackageCodes": servicePackageCodes,
                "servicePackageCertIds": servicePackageCertIds
            }
            await activeCertificate(body)
            Alert.alert(translate('common.notification_uppercase'), "Bạn đã kích hoạt thành công chứng nhận dịch vụ.",
                [
                    {
                        text: "OK",
                        style: "cancel",
                        onPress: () => {
                            hideBlockUI();
                            sheetRef.current?.dismiss();
                            handleSearch(searchQuery)
                        }
                    }
                ]
            )

        } catch (error) {
            console.log("🚀 ~ onActive ~ error:", error)
            Alert.alert(translate('common.notification_uppercase'), error.msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: onActive
                    }
                ]
            )
        }

    }





    const renderItem = ({ item }) => (
        <View style={styles.item}>
            <MyText style={styles.itemTitle}>
                {item.saleOrderId}{'   '}
            </MyText>
            <MyText
                style={{
                    color: item.isDelivery ? COLORS.bg40B93C : COLORS.bgFF0000, paddingVertical: 3

                }}
                text={item.isDelivery ? 'Đã giao hàng' : 'Chưa giao hàng'}
            />

            <MyText style={styles.itemText}>
                Tên khách hàng: <MyText text={item.customerName} />
            </MyText>
            <MyText style={styles.itemText}>
                Số điện thoại: <MyText text={item.oldCustomerPhone || item.customerPhone} />
            </MyText>
            <TouchableOpacity
                onPress={() => handleGetDetail(item)}
                style={{ alignItems: "center", flexDirection: "row", justifyContent: "flex-end" }}>
                <MyText addSize={-1} style={styles.itemDetail}>
                    Xem chi tiết
                </MyText>
            </TouchableOpacity>
        </View>
    );

    const keyExtractor = useCallback((item) => item.saleOrderId, []);

    return (
        <View style={styles.container}>
            <BottomSheetModalProvider>
                <FilterInput
                    onSearch={handleSearch}
                    searchQuery={searchQuery}
                    onChangeSearchQuery={onChangeSearchQuery}
                />
                <SafeAreaView style={styles.contentWrapper}>
                    <BaseLoading
                        isLoading={searchState.isLoading}
                        isEmpty={searchState.isEmpty}
                        textLoadingError={searchState.description}
                        isError={searchState.isError}
                        onPressTryAgains={() => handleSearch(searchQuery)}
                        content={
                            <FlatList
                                data={searchState.data}
                                renderItem={renderItem}
                                keyExtractor={keyExtractor}
                                keyboardShouldPersistTaps="never"
                                keyboardDismissMode="on-drag"
                                initialNumToRender={10}
                                maxToRenderPerBatch={10}
                                windowSize={5}
                            />
                        }
                    />
                </SafeAreaView>
                <DetailCertificateSheet
                    bottomSheetModalRef={sheetRef}
                    onChangeStatusSheet={(index) => {
                        if (index == -1) {
                            setItemSelected({})
                        }
                    }}
                    data={itemSelected}
                    handleCheckCertificate={handleCheckCertificate}
                    onActive={onActive}
                />
            </BottomSheetModalProvider>

        </View>
    );
};

// Styles
const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: COLORS.bgFFFFFF
    },
    contentWrapper: {
        flex: 1,
        paddingVertical: 16
    },
    item: {
        padding: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#eee'
    },
    itemTitle: {
        fontWeight: 'bold',
        paddingVertical: 5
    },
    itemText: {
        paddingVertical: 5,
        color: COLORS.bg7F7F7F
    },
    itemDetail: {
        color: COLORS.bg1E88E5,
        marginLeft: 7
    }
});

export default CertificateScreen;