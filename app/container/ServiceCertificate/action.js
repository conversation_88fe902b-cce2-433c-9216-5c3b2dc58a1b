
import { helper } from '@common';
import { apiBase, METHOD } from '@config';
import { API_CONST } from '@constants';
const {
    API_SEARCH_CERTIFICATE,
    API_ACTIVE_CERTIFICATE
} = API_CONST;



export const searchCertificate = function (body) {
    return new Promise((resolve, reject) => {
        apiBase(API_SEARCH_CERTIFICATE, METHOD.POST, body).then(response => {
            console.log("searchCertificate success", response);
            const { object } = response;
            if (helper.IsNonEmptyArray(object)) {
                resolve(object)
            }
            else {
                resolve([]);
            }
        }).catch(error => {
            console.log("searchCertificate error", error);
            reject(error)
        })
    })
}

export const activeCertificate = function (body) {
    return new Promise((resolve, reject) => {
        apiBase(API_ACTIVE_CERTIFICATE, METHOD.POST, body).then(response => {
            console.log("activeCertificate success", response);
            resolve()
        }).catch(error => {
            console.log("activeCertificate error", error);
            reject(error)
        })
    })
}

