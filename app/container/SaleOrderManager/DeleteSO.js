import React, { useState, useEffect } from "react";
import {
    View,
    StyleSheet,
    TextInput,
    TouchableOpacity,
    Alert,
    Keyboard,
    ScrollView,
    SafeAreaView,
    KeyboardAvoidingView,
    Platform,
} from "react-native";
import { connect, useSelector } from "react-redux";
import { bindActionCreators } from "redux";
import { MyText, showBlockUI, hideBlockUI, Picker } from "@components";
import { Color, helper } from "@common";
import * as actionManagerSOCreator from "./action";
import { translate } from '@translate';
import { COLORS } from "@styles";
import * as actionCollectionManagerCreator from '../CollectionTransferManager/action'

const DeleteSO = (props) => {
    const REASON = [
        { id: 1, content: translate('saleOrderManager.reason_1') },
        { id: 2, content: translate('saleOrderManager.reason_2') },
        { id: 3, content: translate('saleOrderManager.reason_3') },
        { id: 4, content: translate('saleOrderManager.reason_4') },
        { id: 5, content: translate('saleOrderManager.reason_5') },
    ];
    // console.log(props)
    const {
        navigation,
        actionManagerSO,
        saleOrder,
        paramFilter,
        actionCollectionManager,
        userInfo
    } = props;
    const { CancelSOReasonList } = saleOrder;

    const [isIncomeAndTotalPaid, setIsIncomeAndTotalPaid] = useState(false);
    const [block, setBlock] = useState(false);
    const [chose, setChose] = useState(new Object());
    const [txtReason, setReason] = useState("");
    const [isSendSMS, setIsSendSMS] = useState(0);
    const [isCancelSOReasonList, setIsCancelSOReasonList] = useState(false);
    const [cancelSOReasonPicker, setCancelSOReasonPicker] = useState([])
    const { brandID } = useSelector((state) => state.userReducer)
    const { REASONCANCELSONONSENDSMS } = useSelector((state) => state.appSettingReducer)
    const [contentBtn, setContentBtn] = useState("")
    //bắt sự kiện show và hide keyboard
    useEffect(() => {
        if (brandID == 8) {
            setChose(REASON[4])
        }
        // console.log('useeffect')
        if (saleOrder.IsIncome && saleOrder.TotalPaid > 0) {
            setIsIncomeAndTotalPaid(true);
        } else {
            if (CancelSOReasonList && CancelSOReasonList.length > 0) {
                let index = 0;
                let listCancelSOReason = [];
                CancelSOReasonList.forEach(x => {
                    let obj = {
                        id: index++,
                        content: x
                    }
                    listCancelSOReason.push(obj)
                });
                // console.log('listCancelSOReaso',listCancelSOReason)
                setCancelSOReasonPicker(listCancelSOReason);
                setIsCancelSOReasonList(true)
            }
            if (saleOrder.IsSendSMS && saleOrder.IsSendSMS100k) {
                setIsSendSMS(1)
            } else if (saleOrder.IsSendSMS && !saleOrder.IsSendSMS100k) {
                setIsSendSMS(2)
            }
        }
        Keyboard.addListener("keyboardDidShow", _keyboardDidShow);
        Keyboard.addListener("keyboardDidHide", _keyboardDidHide);
        // cleanup function
        return () => {
            Keyboard.removeListener("keyboardDidShow", _keyboardDidShow);
            Keyboard.removeListener("keyboardDidHide", _keyboardDidHide);
        };
    }, []);
    useEffect(() => {
        if (chose.content === REASONCANCELSONONSENDSMS) {
            setContentBtn("Hủy")
        }
        else {
            const newContent = isSendSMS == 1 ? translate('saleOrderManager.send_coupon') : translate('saleOrderManager.product_not_for_sale')
            setContentBtn(newContent)
        }
    }, [chose])
    const [keyboardStatus, setKeyboardStatus] = useState(true);
    const _keyboardDidShow = () => setKeyboardStatus(false);
    const _keyboardDidHide = () => setKeyboardStatus(true);

    const goBack = () => {
        const { VoucherConcernType } = saleOrder;
        const { fromDate, toDate } = paramFilter;
        const { userName } = userInfo;
        hideBlockUI();
        if (VoucherConcernType == 6) {
            const data = {
                keyword: '',
                ProcessUser: userName,
                FromDate: fromDate,
                ToDate: toDate,
                IsDeleted: false,
            }
            actionCollectionManager.getSearchCollectionManager(data)
        } else {
            actionManagerSO.getDataSearchSO(paramFilter);
        }
        navigation.goBack();
        setBlock(false);
    }

    const deleteSO = (reason, isConfirm) => {
        setBlock(true);
        showBlockUI();
        let params = {
            saleOrderID: saleOrder.SaleOrderID,
            contentDeleted: reason,
            IsSendSMS: saleOrder.IsSendSMS,
            IsSendSMS100k: saleOrder.IsSendSMS100k,
            isConfirm: isConfirm
        }
        actionManagerSO
            .removeSaleOrder(params)
            .then(({ message, isMessageWarning }) => {
                if (isMessageWarning) {
                    Alert.alert(translate('common.notification'), message, [
                        {
                            text: translate('common.btn_confirm'),
                            onPress: () => {
                                deleteSO(reason, true);
                            },
                        },
                        {
                            text: 'Không huỷ',
                            onPress: () => {
                                goBack();
                            },
                        },
                    ]);
                } else {
                    Alert.alert(translate('common.notification'), message, [
                        {
                            text: "OK",
                            onPress: () => {
                                goBack();
                            },
                        },
                    ]);
                }
            })
            .catch((err) => {
                Alert.alert(translate('common.notification'), err.msgError, [
                    {
                        text: "OK",
                        onPress: () => {
                            setBlock(false);
                            hideBlockUI();
                        },
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        onPress: () => {
                            deleteSO(reason);
                        },
                    },
                ]);
            });
    };
    const cancelSO = () => {
        setBlock(true);
        showBlockUI();
        actionManagerSO
            .cancelSaleOrder(saleOrder)
            .then((smartPayRefund) => {
                let content = translate('saleOrderManager.cancel_order_success')
                if (smartPayRefund > 0) {
                    content += `${translate('saleOrderManager.amount_new_line')} ${helper.convertNum(smartPayRefund)} ${translate('saleOrderManager.will_be_returned')}`;
                }
                Alert.alert(translate('common.notification'), content, [
                    {
                        text: "OK",
                        onPress: () => {
                            goBack();
                        },
                    },
                ]);
            })
            .catch((err) => {
                Alert.alert(translate('common.notification'), err.msgError, [
                    {
                        text: "OK",
                        onPress: () => {
                            setBlock(false);
                            hideBlockUI();
                        },
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        onPress: () => {
                            cancelSO();
                        },
                    },
                ]);
            });
    };
    const formatDT = (datetime) => {
        let date_ob = new Date(datetime);
        // adjust 0 before single digit date
        let date = ("0" + date_ob.getDate()).slice(-2);
        // current month
        let month = ("0" + (date_ob.getMonth() + 1)).slice(-2);
        // current year
        let year = date_ob.getFullYear();
        // current hours
        let hours = ("0" + date_ob.getHours()).slice(-2);
        // current minutes
        let minutes = ("0" + date_ob.getMinutes()).slice(-2);
        // prints date & time in DD-MM-YYYY HH:MM format
        return date + "-" + month + "-" + year + " " + hours + ":" + minutes;
    }
    const getMessageCreateVoucher = () => {
        let messageCreateVoucher = translate('saleOrderManager.additional_receipt_order');
        let strTimeToPayDeposit;
        if (saleOrder.DepositFeePolicy > 0) {
            messageCreateVoucher += translate('saleOrderManager.deposit') + helper.convertNum(saleOrder.DepositFeePolicy) + '.';
            if (saleOrder.TimeToPayDeposit)
                strTimeToPayDeposit = formatDT(saleOrder.TimeToPayDeposit);
        }
        if (saleOrder.TransferAndChanceFeePolicy > 0) {
            messageCreateVoucher += translate('saleOrderManager.detain_from_CO') + helper.convertNum(saleOrder.TransferAndChanceFeePolicy) + '.';
        }
        if (strTimeToPayDeposit) {
            messageCreateVoucher += translate('saleOrderManager.deposit_term') + strTimeToPayDeposit;
        }
        return messageCreateVoucher;
    }
    const updateSO = () => {
        if (isIncomeAndTotalPaid) {
            if (!txtReason) {
                Alert.alert(translate('common.notification'), translate('saleOrderManager.enter_cancel_reason'), [
                    {
                        text: translate('common.btn_accept')
                    }
                ]);
                return;
            }
            saleOrder.ContentDeleted = txtReason;
            if (saleOrder.TransferAndChanceFeePolicy > 0 || saleOrder.DepositFeePolicy > 0) {
                let messageCreateVoucher = getMessageCreateVoucher();
                Alert.alert(translate('common.notification'), messageCreateVoucher, [
                    {
                        text: translate('common.btn_accept'),
                        onPress: () => {
                            cancelSO();
                        },
                    },
                    {
                        text: translate('saleOrderManager.btn_cancel'),
                        onPress: () => {
                        },
                    },
                ]);
            } else {
                cancelSO();
            }
        } else {
            let reason = "";
            if (isSendSMS == 0 && brandID == 8) {
                if (!txtReason || !txtReason.trim()) {
                    Alert.alert(
                        translate('common.notification'),
                        translate('saleOrderManager.please_select_more_reason'),
                        [
                            {
                                text: translate('common.btn_accept')
                            }
                        ]
                    );
                } else {
                    deleteSO(chose.content + " | " + txtReason);
                }
            } else {
                if (isSendSMS == 0) {
                    reason = !txtReason ? chose.content : chose.content + " | " + txtReason;
                } else {
                    if (isCancelSOReasonList) {
                        reason = chose.content;
                    } else {
                        reason = txtReason;
                    }
                }
                if (!reason || !reason.trim()) {
                    Alert.alert(translate('common.notification'), translate('saleOrderManager.please_select_more_reason'), [
                        {
                            text: translate('common.btn_accept'),
                        }
                    ]);
                }
                deleteSO(reason);
            }
        }
    }
    const viewGiftVoucherIssue = () => {
        navigation.navigate("ViewGiftVoucherIssue", { SaleOrderID: saleOrder.SaleOrderID, GiftVoucherIssueBOList: saleOrder.cus_GiftVoucherIssueBOList });
    }
    const RenderItem = ({ title, value }) => {
        return (
            <View style={styles.viewItem} >
                <MyText text={title} style={styles.titleItem} />
                <MyText text={value} style={styles.valueItem} />
            </View>
        )
    }
    return (
        <SafeAreaView style={styles.container}>
            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                keyboardVerticalOffset={Platform.OS === 'ios' ? 96 : 0}
                style={{
                    flex: 1,
                    backgroundColor: COLORS.bgFFFFFF
                }}
                contentContainerStyle={{ flex: 1 }}
            >
                <ScrollView
                    showsVerticalScrollIndicator={false}
                >
                    {
                        isIncomeAndTotalPaid ?
                            <View style={styles.viewInfo}>
                                <RenderItem title={translate('saleOrderManager.receipt_code')} value={saleOrder.InOutVoucherID} />
                                <RenderItem title={translate('saleOrderManager.must_pay_for_export_request')} value={helper.convertNum(saleOrder.Debt)} />
                                <RenderItem title={translate('saleOrderManager.received')} value={helper.convertNum(saleOrder.TotalPaid)} />
                                <View style={styles.viewItemGiftVoucherIssue} >
                                    {
                                        saleOrder.TotalGiftVoucherFee > 0 ?
                                            <TouchableOpacity
                                                style={styles.btnGiftVoucherIssue}
                                                activeOpacity={0.7}
                                                onPress={() => viewGiftVoucherIssue()}
                                            >
                                                <MyText text={translate('saleOrderManager.voucher_fee')} style={styles.textBtnGiftVoucherIssue} />
                                            </TouchableOpacity>
                                            :
                                            <MyText text={translate('saleOrderManager.voucher_fee')} style={styles.textGiftVoucherIssue} />
                                    }
                                    <MyText text={helper.convertNum(saleOrder.TotalGiftVoucherFee)} style={styles.valueGiftVoucherIssue} />
                                </View>
                                <RenderItem title={translate('saleOrderManager.additional_fee')} value={helper.convertNum(saleOrder.ShippingCost)} />
                                <View style={styles.viewTranAndOpp} >
                                    <View style={styles.viewHoldMoney} >
                                        <MyText text={translate('saleOrderManager.detain')} />
                                        <MyText text={translate('saleOrderManager.shipping_fee')} style={styles.textTranAndOpp} />
                                    </View>
                                    <View style={styles.viewValueTranAndOpp} >
                                        <MyText text={`${helper.convertNum(saleOrder.HoldFeePolicy)}`} />
                                    </View>
                                </View>
                                <View style={styles.viewOutVoucherMoney} >
                                    <MyText text={translate('saleOrderManager.must_expend')} style={styles.textOutVoucherMoney} />
                                    <MyText text={`${helper.convertNum(saleOrder.OutVoucherMoney)}`} style={styles.outVoucherMoney} />
                                </View>
                                <View style={styles.viewReason} >
                                    <MyText text={translate('saleOrderManager.cancel_reason')} style={styles.textReason} />
                                    <TextInput
                                        style={styles.content}
                                        value={String(txtReason)}
                                        onChangeText={(text) => {
                                            setReason(text);
                                        }}
                                        placeholder={translate('saleOrderManager.mandatory')}
                                        numberOfLines={6}
                                        multiline={true}
                                        textAlignVertical="top"
                                        returnKeyType="done"
                                        onSubmitEditing={Keyboard.dismiss}
                                    />
                                </View>
                            </View>
                            : (
                                //hủy bình thường
                                isSendSMS == 0 ?
                                    <View
                                        style={styles.containerReason}
                                    >
                                        <View style={styles.viewReasonNotIncome}>
                                            <MyText text={translate('saleOrderManager.title_cancel_reason')} style={styles.textReasonNotIncome} />
                                        </View>
                                        <View
                                            style={styles.viewReasonNotIncome}
                                        >
                                            <Picker
                                                label={"content"}
                                                value={"id"}
                                                data={REASON}
                                                valueSelected={chose.id}
                                                onChange={(item) => {
                                                    setChose(item);
                                                }}
                                                defaultLabel={translate('saleOrderManager.select_cancel_reason')}
                                                style={{
                                                    flex: 1,
                                                    flexDirection: "row",
                                                    justifyContent: "center",
                                                    alignItems: "center",
                                                    height: 40,
                                                    borderWidth: 1,
                                                    borderColor: Color.hiddenGray,
                                                    paddingHorizontal: 10,
                                                    marginVertical: 10,
                                                    backgroundColor: Color.white,
                                                }}
                                            />
                                            <TextInput
                                                style={styles.content}
                                                value={String(txtReason)}
                                                onChangeText={(text) => {
                                                    setReason(text);
                                                }}
                                                placeholder={brandID == 8 ? "Bắt buộc nhập" : translate('saleOrderManager.not_mandatory')}
                                                numberOfLines={6}
                                                multiline={true}
                                                textAlignVertical="top"
                                                returnKeyType="done"
                                                onSubmitEditing={Keyboard.dismiss}
                                            />
                                        </View>
                                    </View>
                                    ://có copoun
                                    <View
                                        style={styles.containerReason}
                                    >
                                        <View
                                            style={styles.viewReasonNotIncome}
                                        >
                                            <View style={styles.viewReasonNotIncome}>
                                                <MyText text={translate('saleOrderManager.product_cannot_sale')} style={[
                                                    styles.textReasonNotIncome,
                                                    {
                                                        fontWeight: 'bold',
                                                        marginBottom: 5
                                                    }
                                                ]}
                                                />
                                            </View>
                                            {
                                                isCancelSOReasonList ?
                                                    <Picker
                                                        label={"content"}
                                                        value={"id"}
                                                        data={cancelSOReasonPicker}
                                                        valueSelected={chose.id}
                                                        onChange={(item) => {
                                                            setChose(item);
                                                        }}
                                                        defaultLabel={translate('saleOrderManager.select_cancel_reason')}
                                                        style={{
                                                            flex: 1,
                                                            flexDirection: "row",
                                                            justifyContent: "center",
                                                            alignItems: "center",
                                                            height: 40,
                                                            borderWidth: 1,
                                                            borderColor: Color.hiddenGray,
                                                            paddingHorizontal: 10,
                                                            marginVertical: 10,
                                                            backgroundColor: Color.white,
                                                        }}
                                                    />
                                                    :
                                                    <TextInput
                                                        style={styles.content}
                                                        value={String(txtReason)}
                                                        onChangeText={(text) => {
                                                            setReason(text);
                                                        }}
                                                        placeholder={translate('saleOrderManager.text_input_reason')}
                                                        numberOfLines={6}
                                                        multiline={true}
                                                        textAlignVertical="top"
                                                        returnKeyType="done"
                                                        onSubmitEditing={Keyboard.dismiss}
                                                    />
                                            }
                                        </View>
                                    </View>
                            )
                    }
                </ScrollView>
                {
                    // keyboardStatus ?
                    <View style={styles.viewBtn} >
                        {
                            isIncomeAndTotalPaid || isSendSMS == 0 ?
                                <TouchableOpacity
                                    style={[
                                        styles.btn,
                                        { opacity: (isIncomeAndTotalPaid ? !txtReason : (Object.keys(chose).length === 0)) ? 0.5 : 1 },
                                    ]}
                                    activeOpacity={0.7}
                                    onPress={() => {
                                        updateSO();
                                    }}
                                    disabled={(isIncomeAndTotalPaid ? !txtReason : (Object.keys(chose).length === 0)) || block ? true : false}
                                >
                                    <MyText text={isIncomeAndTotalPaid ? translate('saleOrderManager.update_export_request') : translate('saleOrderManager.btn_cancel_order')} style={styles.txtBtn} />
                                </TouchableOpacity>
                                :
                                <TouchableOpacity
                                    style={[
                                        styles.btn,
                                        { opacity: (!isCancelSOReasonList ? !txtReason : (Object.keys(chose).length === 0)) ? 0.5 : 1 },
                                    ]}
                                    activeOpacity={0.7}
                                    onPress={() => {
                                        updateSO();
                                    }}
                                    disabled={(!isCancelSOReasonList ? !txtReason : (Object.keys(chose).length === 0)) || block ? true : false}
                                >
                                    <MyText text={contentBtn} style={styles.txtBtn} />
                                </TouchableOpacity>
                        }
                    </View>
                    // : null
                }

                {/* <UIIndicator isVisible={block} /> */}
            </KeyboardAvoidingView>

        </SafeAreaView>
    );
}
const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: COLORS.bgFFFFFF
    },
    picker: {
        height: 40,
        borderWidth: 1,
        borderColor: Color.hiddenGray,
        paddingHorizontal: 10,
        marginVertical: 10,
        backgroundColor: Color.white,
    },
    viewBtn: {
        justifyContent: "center",
        alignItems: 'center',
        marginVertical: 10,
    },
    btn: {
        padding: 10,
        backgroundColor: COLORS.btn2C8BD7,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 5,
        width: "80%",
    },
    txtBtn: {
        fontWeight: "bold",
        color: Color.white
    },
    content: {
        borderWidth: 1,
        height: 100,
        borderColor: Color.hiddenGray,
        padding: 10,
    },
    viewItem: {
        width: "95%",
        flexDirection: "row",
        paddingVertical: 3
    },
    titleItem: {
        width: "50%",
        backgroundColor: COLORS.bgF2F2F2,
        paddingVertical: 3,
        paddingHorizontal: 5
    },
    valueItem: {
        width: "49%",
        textAlign: 'right',
        backgroundColor: COLORS.bgF2F2F2,
        paddingVertical: 3,
        marginLeft: "1%",
        paddingHorizontal: 5
    },
    viewInfo: {
        flex: 1,
        marginTop: 10,
        alignItems: 'center',
        justifyContent: 'flex-start'
    },
    viewItemGiftVoucherIssue: {
        width: "95%",
        flexDirection: "row",
        paddingVertical: 3
    },
    btnGiftVoucherIssue: {
        width: "50%",
        backgroundColor: COLORS.btnF2F2F2,
        paddingVertical: 3,
        paddingHorizontal: 5,
    },
    textBtnGiftVoucherIssue: {
        color: COLORS.txt0000FF,
        textDecorationLine: 'underline',
    },
    textGiftVoucherIssue: {
        width: "50%",
        backgroundColor: COLORS.bgF2F2F2,
        paddingVertical: 3,
        paddingHorizontal: 5
    },
    valueGiftVoucherIssue: {
        width: "49%",
        textAlign: 'right',
        backgroundColor: COLORS.bgF2F2F2,
        paddingVertical: 3,
        marginLeft: "1%",
        paddingHorizontal: 5
    },
    viewTranAndOpp: {
        width: "95%",
        flexDirection: "row",
        paddingVertical: 3
    },
    viewHoldMoney: {
        width: "50%",
        backgroundColor: COLORS.bgF2F2F2,
        paddingVertical: 3,
        paddingHorizontal: 5
    },
    textTranAndOpp: {
        fontSize: 11
    },
    viewValueTranAndOpp: {
        width: "49%",
        textAlign: 'right',
        backgroundColor: COLORS.bgF2F2F2,
        paddingVertical: 3,
        marginLeft: "1%",
        paddingHorizontal: 5,
        justifyContent: 'center',
        alignItems: 'flex-end'
    },
    viewOutVoucherMoney: {
        width: "95%",
        flexDirection: "row",
        paddingVertical: 3
    },
    textOutVoucherMoney: {
        width: "50%",
        backgroundColor: COLORS.bgF2F2F2,
        paddingVertical: 3,
        paddingHorizontal: 5,
        fontWeight: 'bold'
    },
    outVoucherMoney: {
        width: "49%",
        textAlign: 'right',
        backgroundColor: COLORS.bgF2F2F2,
        paddingVertical: 3,
        marginLeft: "1%",
        paddingHorizontal: 5,
        fontWeight: 'bold',
        color: COLORS.txtFF0000
    },
    viewReason: {
        width: "95%",
        paddingVertical: 2
    },
    textReason: {
        width: "50%",
        paddingVertical: 2,
        paddingHorizontal: 5
    },
    containerReason: {
        alignItems: "center",
        justifyContent: 'flex-start',
        marginTop: 10,
        flex: 1
    },
    viewReasonNotIncome: {
        width: "95%"
    },
    textReasonNotIncome: {
        fontSize: 16
    }
});
const mapStateToProps = (state) => ({
    saleOrder: state.managerSOReducer.infoSODelete,
    paramFilter: state.managerSOReducer.paramFilter,
    userInfo: state.userReducer
});
const mapDispatchToProps = (dispatch) => ({
    actionManagerSO: bindActionCreators(actionManagerSOCreator, dispatch),
    actionCollectionManager: bindActionCreators(actionCollectionManagerCreator, dispatch),
});
export default connect(mapStateToProps, mapDispatchToProps)(DeleteSO);
