import React, { useState } from 'react';
import {
    View,
    TextInput,
    StyleSheet,
    Alert,
} from 'react-native';
import {
    MyText,
    Button,
    NumberInput,
    Picker
} from "@components";
import { constants } from '@constants';
import { helper } from "@common";
import ModalAPPV from "./ModalAPPV";
import { translate } from '@translate';
import { COLORS } from "@styles";

const InputCard = ({
    dataPOS,
    applyMoney,
    dataMoney,
    maxPayment
}) => {
    const [posID, setPosID] = useState(0);
    const [moneyCardName, setMoneyCardName] = useState("");
    const [code, setCode] = useState("");
    const [money, setMoney] = useState(0);
    const [isPayment, setIsPayment] = useState(true);
    const [isVisible, setIsVisible] = useState(false);
    const keyMoneyCard = getKeyMoneyCard(dataMoney);

    const onPayment = () => {
        const isValidate = validateCard();
        if (isValidate) {
            const cardInfo = {
                "MoneyCardID": posID,
                "MoneyCardVoucherID": code,
                "MoneyCard": money,
                "MoneyCardName": moneyCardName
            }
            applyMoney(cardInfo);
            setPosID(0);
            setCode("");
            setMoney(0);
            setIsPayment(false);
        }
    }

    const validateCard = () => {
        const key = `${posID}${code}`;
        const isValidateAPPV = regExpAPPV.test(code);
        if (posID == 0) {
            Alert.alert("", translate('saleOrderManager.please_select_pos'));
            return false;
        }
        if (!helper.IsNonEmptyString(code)) {
            Alert.alert("", translate('saleOrderManager.please_enter_expense_code'));
            return false;
        }
        if (!isValidateAPPV) {
            Alert.alert("", translate('saleOrderManager.expense_code_must_have_6_letters'));
            return false;
        }
        if (money == 0) {
            Alert.alert("", translate('saleOrderManager.please_enter_payment'));
            return false;
        }
        if (money > maxPayment) {
            Alert.alert("", translate('saleOrderManager.expense_more_than_must_expend'));
            return false;
        }
        if (keyMoneyCard.has(key)) {
            Alert.alert("", translate('saleOrderManager.existed_expense_code'));
            return false;
        }
        return true;
    }

    const onAddform = () => {
        setIsPayment(true);
    }

    const onSwitchVisible = () => {
        setIsVisible(!isVisible);
    }

    return (
        <View style={{
            backgroundColor: COLORS.bgE4EBD5,
            width: constants.width,
            paddingTop: isPayment ? 1 : 0
        }}>
            {
                isPayment &&
                <>
                    <View style={{
                        flexDirection: "row",
                        borderBottomWidth: StyleSheet.hairlineWidth,
                        borderBottomColor: COLORS.bdFFFFFF,
                        width: constants.width
                    }}>
                        <View style={{
                            height: 40,
                            justifyContent: "center",
                            alignItems: "flex-start",
                            width: 138,
                            borderRightWidth: 1,
                            borderRightColor: COLORS.bdFFFFFF,
                            paddingHorizontal: 10,
                            backgroundColor: COLORS.bgE4EBD5,
                        }}>
                            <MyText
                                text={translate("editSaleOrder.pos")}
                                style={{
                                    color: COLORS.txt000000,
                                }}
                            />
                        </View>
                        <View style={{
                            height: 40,
                            justifyContent: "center",
                            alignItems: "flex-end",
                            width: constants.width - 140,
                            borderWidth: StyleSheet.hairlineWidth,
                            borderColor: COLORS.bdCBE5B2
                        }}>
                            <Picker
                                style={{
                                    flexDirection: "row",
                                    height: 38,
                                    width: constants.width - 142,
                                    backgroundColor: COLORS.btnFFFFFF,
                                    paddingHorizontal: 10,
                                    justifyContent: "center",
                                    alignItems: "center"
                                }}
                                label={"MoneyCardName"}
                                value={"MoneyCardID"}
                                defaultLabel={translate('saleOrderManager.select_pos')}
                                valueSelected={posID}
                                data={dataPOS}
                                onChange={(item) => {
                                    setPosID(item.MoneyCardID);
                                    setMoneyCardName(item.MoneyCardName);
                                }}
                            />
                        </View>
                    </View>

                    <View style={{
                        flexDirection: "row",
                        borderBottomWidth: StyleSheet.hairlineWidth,
                        borderBottomColor: COLORS.bdFFFFFF,
                        width: constants.width
                    }}>
                        <View style={{
                            height: 40,
                            justifyContent: "center",
                            alignItems: "flex-start",
                            width: 138,
                            borderRightWidth: 1,
                            borderRightColor: COLORS.bdFFFFFF,
                            paddingHorizontal: 10,
                            backgroundColor: COLORS.bgE4EBD5,
                        }}>
                            <MyText
                                text={"APPV Code: "}
                                style={{
                                    color: COLORS.txt000000,
                                }}
                            />
                        </View>
                        <View style={{
                            height: 40,
                            justifyContent: "center",
                            alignItems: "flex-end",
                            width: constants.width - 140,
                            borderWidth: StyleSheet.hairlineWidth,
                            borderColor: COLORS.bdCBE5B2
                        }}>
                            <View
                                style={{
                                    height: 40,
                                    justifyContent: "center",
                                    alignItems: "flex-end",
                                    width: constants.width - 140,
                                }}
                            >
                                <TextInput
                                    style={{
                                        height: 38,
                                        width: constants.width - 142,
                                        paddingHorizontal: 8,
                                        justifyContent: "center",
                                        alignItems: "flex-end",
                                        textAlign: "right",
                                        backgroundColor: COLORS.bgFFFFFF
                                    }}
                                    value={code}
                                    onChangeText={(text) => {
                                        if (regExpInputAPPV.test(text)) {
                                            setCode(text);
                                        }
                                    }}
                                    returnKeyType={"done"}
                                    placeholder={translate('saleOrderManager.text_input_appv_code')}
                                />
                            </View>

                        </View>
                    </View>

                    <View style={{
                        flexDirection: "row",
                        width: constants.width,
                    }}>
                        <View style={{
                            height: 40,
                            justifyContent: "center",
                            alignItems: "flex-start",
                            width: 138,
                            borderRightWidth: 1,
                            borderRightColor: COLORS.bdFFFFFF,
                            paddingHorizontal: 10,
                            backgroundColor: COLORS.bgE4EBD5,
                        }}>
                            <MyText
                                text={translate('saleOrderManager.amount')}
                                style={{
                                    color: COLORS.txt000000,
                                }}
                            />
                        </View>
                        <View style={{
                            height: 40,
                            justifyContent: "center",
                            alignItems: "flex-end",
                            width: constants.width - 140,
                            borderWidth: StyleSheet.hairlineWidth,
                            borderColor: COLORS.bdCBE5B2
                        }}>
                            <View
                                style={{
                                    height: 40,
                                    justifyContent: "center",
                                    alignItems: "flex-end",
                                    width: constants.width - 140,
                                }}
                            >
                                <NumberInput
                                    style={{
                                        height: 38,
                                        width: constants.width - 142,
                                        backgroundColor: COLORS.bgFFFFFF,
                                        paddingHorizontal: 8,
                                        justifyContent: "center",
                                        alignItems: "flex-end",
                                        textAlign: "right"
                                    }}
                                    placeholder={""}
                                    value={money}
                                    onChangeText={(value) => {
                                        setMoney(value);
                                    }}
                                />
                            </View>
                        </View>
                    </View>
                </>
            }
            <View style={{
                justifyContent: "space-between",
                alignItems: "center",
                paddingHorizontal: 10,
                paddingVertical: 8,
                flexDirection: "row",
                width: constants.width,
                backgroundColor: COLORS.bgE4EBD5,
                borderTopWidth: StyleSheet.hairlineWidth,
                borderTopColor: COLORS.bdFFFFFF,

            }}>
                <Button
                    text={translate('saleOrderManager.get_appv_code')}
                    styleContainer={{
                        borderColor: COLORS.bdF49B0C,
                        borderWidth: 1,
                        borderRadius: 4,
                        justifyContent: "center",
                        alignItems: "center",
                        padding: 8,
                        backgroundColor: COLORS.btnFFFFFF,
                        width: 165,
                    }}
                    styleText={{
                        color: COLORS.txtF49B0C,
                        fontSize: 14,
                    }}
                    onPress={onSwitchVisible}
                />
                {
                    isPayment
                        ? <Button
                            text={translate('saleOrderManager.btn_transaction_confirm')}
                            styleContainer={{
                                backgroundColor: COLORS.btn288AD6,
                                borderRadius: 4,
                                justifyContent: "center",
                                alignItems: "center",
                                padding: 8,
                                borderColor: COLORS.bd288AD6,
                                borderWidth: 1,
                                width: 165,
                            }}
                            styleText={{
                                color: COLORS.txtFFFFFF,
                                fontSize: 14,
                                fontWeight: "bold"
                            }}
                            onPress={onPayment}
                        />
                        : <Button
                            text={translate('saleOrderManager.btn_add_card')}
                            styleContainer={{
                                backgroundColor: COLORS.btn288AD6,
                                borderRadius: 4,
                                justifyContent: "center",
                                alignItems: "center",
                                padding: 8,
                                borderColor: COLORS.bd288AD6,
                                borderWidth: 1,
                                width: 165,
                            }}
                            styleText={{
                                color: COLORS.txtFFFFFF,
                                fontSize: 14,
                                fontWeight: "bold"
                            }}
                            onPress={onAddform}
                        />
                }
            </View>
            {
                <ModalAPPV
                    isVisible={isVisible}
                    hideModal={onSwitchVisible}
                />
            }
        </View>
    );
}

export default InputCard;

const regExpInputAPPV = new RegExp(/^\w{0,6}$/);
const regExpAPPV = new RegExp(/^\w{6}$/);


export const getKeyMoneyCard = (data) => {
    const keyMoneyCard = new Set();
    data.forEach(ele => {
        const { MoneyCardID, MoneyCardVoucherID } = ele;
        const key = `${MoneyCardID}${MoneyCardVoucherID}`;
        keyMoneyCard.add(key);
    });
    return keyMoneyCard;
}