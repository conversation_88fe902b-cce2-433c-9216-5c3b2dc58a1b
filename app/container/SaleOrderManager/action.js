

import { API_CONST } from '@constants';
import { helper, dateHelper } from "@common";
import {
    apiBase,
    METHOD,
    ERROR,
    EMPTY
} from '@config';
import { translate } from '@translate';

const {
    API_SEARCH_SO,
    API_LOAD_INFO_SALEORDER_CANCEL,
    API_CANCEL_SO,
    API_REMOVE_SO,
    API_CHECK_SO_PREORDER,
    API_GET_PREORDER_CART,
    API_ADD_PROMOTION_PREORDER,
    API_GET_LIST_REPORT_BY_SO,
    API_REVIEWPRINT_SALEORDER,
    API_GET_SIM_PREORDER,
    API_UPDATE_SIM_PREORDER,
    API_GET_REPORT_LOG,
    API_GET_EDIT_LOG
} = API_CONST;

const START_SEARCH_SALEORDER = "START_SEARCH_SALEORDER";
const STOP_SEARCH_SALEORDER = "STOP_SEARCH_SALEORDER";
const UPDATE_DATA_SEARCH_SO = "UPDATE_DATA_SEARCH_SO";
const SET_PARAM_FILTER = "SET_PARAM_FILTER";
const SET_INFO_SO_DELETE = "SET_INFO_SO_DELETE";
const START_GET_CART_PREORDER = "START_GET_CART_PREORDER";
const STOP_GET_CART_PREORDER = "STOP_GET_CART_PREORDER";
const SET_PRODUCT_PROMOTION_PRE = "SET_PRODUCT_PROMOTION_PRE";
const START_MODIFY_PREORDER_CART = "START_MODIFY_PREORDER_CART";
const STOP_MODIFY_PREORDER_CART = "STOP_MODIFY_PREORDER_CART";
const START_GET_TYPE_REPORT = "START_GET_TYPE_REPORT";
const STOP_GET_TYPE_REPORT = "STOP_GET_TYPE_REPORT";
const SET_SIM_REQUEST_PRE = "SET_SIM_REQUEST_PRE";

export const managerSOAction = {
    START_SEARCH_SALEORDER,
    STOP_SEARCH_SALEORDER,
    UPDATE_DATA_SEARCH_SO,
    SET_PARAM_FILTER,
    SET_INFO_SO_DELETE,
    START_GET_CART_PREORDER,
    STOP_GET_CART_PREORDER,
    SET_PRODUCT_PROMOTION_PRE,
    START_MODIFY_PREORDER_CART,
    STOP_MODIFY_PREORDER_CART,
    START_GET_TYPE_REPORT,
    STOP_GET_TYPE_REPORT,
    SET_SIM_REQUEST_PRE
}

export const getDataSearchSO = function (data) {
    return function (dispatch, getState) {
        let body = {
            loginStoreId: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            moduleID: getState().userReducer.moduleID,
            "outputStoreID": data.outputStoreID,
            "fromDate": data.fromDate,
            "toDate": data.toDate,
            "isNullInputUser": data.isNullInputUser,
            "isOutProduct": data.isOutProduct,
            "keyWord": data.keyWord,
            "isDelete": data.isDelete,
            "attachmentType": data.attachmentType
        };
        let result = { dataSearchSO: [], isFilterDay: true };
        dispatch(start_search_sale_order());
        apiBase(API_SEARCH_SO, METHOD.POST, body).then((response) => {
            console.log("getDataSearchOrder success", response);
            const { object } = response;
            if (!helper.IsEmptyObject(object)) {
                const { FilterByCreatedDate, SaleOrderList } = object;
                const dataSearchSO = getDataSectionsList(SaleOrderList);
                result = {
                    dataSearchSO: dataSearchSO,
                    isFilterDay: FilterByCreatedDate
                }
                if (helper.IsNonEmptyArray(dataSearchSO)) {
                    dispatch(stop_search_sale_order(result));
                    dispatch(set_param_filter(data));
                }
                else {
                    dispatch(stop_search_sale_order(result, EMPTY, translate('saleOrderManager.order_not_found')));
                }
            }
            else {
                dispatch(stop_search_sale_order(result, EMPTY, translate('saleOrderManager.order_not_found')));
            }
        }).catch(error => {
            console.log("getDataSearchOrder error", error);
            dispatch(stop_search_sale_order(result, !EMPTY, error.msgError, ERROR));
        })
    }
}

export const updateDataSearchSO = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "outputStoreID": data.outputStoreID,
                "fromDate": data.fromDate,
                "toDate": data.toDate,
                "isNullInputUser": data.isNullInputUser,
                "isOutProduct": data.isOutProduct,
                "keyWord": data.keyWord,
                "isDelete": data.isDelete,
                "attachmentType": data.attachmentType
            };
            apiBase(API_SEARCH_SO, METHOD.POST, body).then((response) => {
                console.log("updateDataSearchSO success", response);
                const { object } = response;
                if (!helper.IsEmptyObject(object)) {
                    const { SaleOrderList } = object;
                    const dataSearchSO = getDataSectionsList(SaleOrderList);
                    if (helper.IsNonEmptyArray(dataSearchSO)) {
                        resolve(true);
                        dispatch(update_data_search_so(dataSearchSO));
                        dispatch(set_param_filter(data));
                    }
                }
                resolve(true);
            }).catch(error => {
                console.log("updateDataSearchSO error", error);
                reject(error.msgError);
            })
        })
    }
}

export const getInfoSODelete = function (saleOrderID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "saleOrderID": saleOrderID,
            };
            dispatch(set_info_so_delete({}));
            apiBase(API_LOAD_INFO_SALEORDER_CANCEL, METHOD.POST, body).then((response) => {
                console.log("getInfoSODelete success", response);
                const { object } = response;
                if (!helper.IsEmptyObject(object)) {
                    dispatch(set_info_so_delete(object));
                    resolve(object);
                }
                else {
                    reject(translate('saleOrderManager.cannot_get_order_information'));
                }
            }).catch(error => {
                console.log("getInfoSODelete error", error);
                reject(error.msgError);
            })
        })
    }
}

export const removeSaleOrder = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "saleOrderID": data.saleOrderID,
                "contentDeleted": data.contentDeleted,
                "IsSendSMS": data.IsSendSMS,
                "IsSendSMS100k": data.IsSendSMS100k,
                "IsConfirm": data.isConfirm
            };
            helper.LoggerDebug({ "{+DeleteSaleOrder+} REQUEST": body });
            apiBase(API_REMOVE_SO, METHOD.POST, body).then((response) => {
                helper.LoggerInfo({ "{+DeleteSaleOrder+} RESPONSE": response });
                const { object, errorReason } = response;
                let message = errorReason || translate('saleOrderManager.delete_order_success')
                let isMessageWarning = false
                if (object?.MessageWarning) {
                    message = object?.MessageWarning
                    isMessageWarning = true
                }
                resolve({ message, isMessageWarning })
            }).catch(error => {
                console.log("removeSaleOrder error", error);
                reject(error);
            })
        });
    };
};

export const cancelSaleOrder = function (cartRequest) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "cartRequest": cartRequest,
                "isGetContentHTML": true
            };
            helper.LoggerDebug({ "{+CancelSaleOrder+} REQUEST": body });
            apiBase(API_CANCEL_SO, METHOD.POST, body).then((response) => {
                helper.LoggerInfo({ "{+CancelSaleOrder+} RESPONSE": response });
                const { object } = response;
                if (helper.IsEmptyObject(object)) {
                    resolve({});
                }
                else {
                    resolve(object);
                }
            }).catch(error => {
                console.log("cancelSaleOrder error", error);
                reject(error);
            })
        });
    };
};

export const checkSOPreOrder = function (saleOrderID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "saleOrderID": saleOrderID,
            };
            apiBase(API_CHECK_SO_PREORDER, METHOD.POST, body).then((response) => {
                console.log("checkSOPreOrder success", response);
                const { object } = response;
                if (object == "true") {
                    resolve(true);
                }
                else {
                    resolve(false);
                }
            }).catch(error => {
                console.log("checkSOPreOrder error", error);
                reject(error.msgError);
            })
        });
    };
};

export const getPreOrderCart = function (saleOrderID) {
    return function (dispatch, getState) {
        let body = {
            loginStoreId: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            moduleID: getState().userReducer.moduleID,
            "saleOrderID": saleOrderID,
        };
        dispatch(start_get_cart_preorder())
        apiBase(API_GET_PREORDER_CART, METHOD.POST, body).then((response) => {
            console.log("getPreOrderCart success", response);
            const { object } = response;
            if (!helper.IsEmptyObject(object)) {
                const {
                    saleOrder,
                    changePromotionSODetail,
                } = object;
                dispatch(stop_get_cart_preorder(saleOrder));
                dispatch(getProductPromotion(changePromotionSODetail));
            }
            else {
                dispatch(stop_get_cart_preorder({}, EMPTY, translate('saleOrderManager.order_information_not_found')));
            }
        }).catch(error => {
            console.log("getPreOrderCart error", error);
            const { errorType, msgError } = error;
            if (errorType == 3) {
                dispatch(stop_get_cart_preorder({}, EMPTY, msgError, !ERROR));
            }
            else {
                dispatch(stop_get_cart_preorder({}, !EMPTY, msgError, ERROR));
            }
        })
    };
};

export const addPromotionToPreOrder = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "promotionGroups": data.promotionGroups,
                "saleOrder": data.saleOrder,
                "mainProduct": data.mainProduct
            };
            dispatch(start_modify_preorder_cart());
            apiBase(API_ADD_PROMOTION_PREORDER, METHOD.POST, body).then((response) => {
                console.log("addPromotionToPreOrder success", response);
                const { object } = response;
                if (!helper.IsEmptyObject(object)) {
                    const { SaleOrderPaycashOutput } = object;
                    dispatch(stop_modify_preorder_cart(object, SaleOrderPaycashOutput));
                    resolve(true);
                }
                else {
                    reject(translate('saleOrderManager.order_information_not_exist'));
                }
            }).catch(error => {
                console.log("addPromotionToPreOrder error", error);
                reject(error.msgError);
            })
        })
    }
}

export const getContentTypeReport = (saleOrderId) => {
    return function (dispatch, getState) {
        let body = {
            loginStoreId: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            moduleID: getState().userReducer.moduleID,
            "saleOrderID": saleOrderId,
        };
        dispatch(start_get_type_report());
        apiBase(API_GET_LIST_REPORT_BY_SO, METHOD.POST, body).then(response => {
            console.log("addPromotionToPreOrder success", response);
            const { object } = response;
            if (helper.IsNonEmptyArray(object)) {
                dispatch(stop_get_type_report(object));
            } else {
                dispatch(stop_get_type_report([], EMPTY, translate('saleOrderManager.cannot_information_printer')));
            }
        }).catch(error => {
            console.log("addPromotionToPreOrder error", error);
            dispatch(stop_get_type_report([], !EMPTY, error.msgError, ERROR));
        });
    };
};

export const getContentBase64View = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                moduleID: getState().userReducer.moduleID,
                languageID: getState().userReducer.languageID,
                loginStoreId: getState().userReducer.storeID,
                "reportContent": data.reportContent,
                "saleOrderID": data.saleOrderID,
                "isGetContentHTML": true,
                "getNewTemplate": true,
                "extendProperties": {
                    "PaymentAmount": data.paymentAmount
                }
            };
            apiBase(API_REVIEWPRINT_SALEORDER, METHOD.POST, body).then(response => {
                console.log("getContentBase64 success", response);
                const base64 = response?.object?.data?.ViewContent;
                if (helper.IsNonEmptyString(base64)) {
                    resolve(base64.replace("<meta", `<meta name="viewport" content="width=device-width, initial-scale=0.6"`));
                }
                else {
                    reject("Hình thức thanh toán chuyển khoản đang gặp sự cố, vui lòng sử dụng hình thức thanh toán khác.")
                }
            }).catch(error => {
                console.log("getContentBase64 error", error);
                reject(error.msgError)
            })
        })
    }
}

export const getSIMProcessPreOrder = function (saleOrder) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                moduleID: getState().userReducer.moduleID,
                languageID: getState().userReducer.languageID,
                loginStoreId: getState().userReducer.storeID,
                "saleOrder": saleOrder
            };
            apiBase(API_GET_SIM_PREORDER, METHOD.POST, body).then((response) => {
                console.log("getSIMProcessPreOrder success", response);
                const { object } = response;
                if (!helper.IsEmptyObject(object)) {
                    const { SaleOrderPaycashOutput, SIMProcessRequestBOList } = object;
                    dispatch(set_sim_request_pre(SaleOrderPaycashOutput, SIMProcessRequestBOList));
                    resolve(SIMProcessRequestBOList);
                }
                else {
                    reject(translate("saleOrderManager.no_information_sim"));
                }
            }).catch(error => {
                reject(error.msgError)
                console.log("getSIMProcessPreOrder error", error);
            })
        })
    }
}

export const updateSIMProcessPreOrder = function (simProcessInfo) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                moduleID: getState().userReducer.moduleID,
                languageID: getState().userReducer.languageID,
                loginStoreId: getState().userReducer.storeID,
                "simRequest": simProcessInfo
            };
            apiBase(API_UPDATE_SIM_PREORDER, METHOD.POST, body).then((response) => {
                console.log("updateSIMProcessPreOrder success", response);
                const { object } = response;
                if (!helper.IsEmptyObject(object)) {
                    resolve(object);
                }
                else {
                    reject(translate("saleOrderManager.no_update_informatio_sim"));
                }
            }).catch(error => {
                reject(error.msgError)
                console.log("updateSIMProcessPreOrder error", error);
            })
        })
    }
}

export const getReportHistory = function (saleOrderID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                moduleID: getState().userReducer.moduleID,
                languageID: getState().userReducer.languageID,
                loginStoreId: getState().userReducer.storeID,
                "saleOrderID": saleOrderID
            };
            apiBase(API_GET_REPORT_LOG, METHOD.POST, body).then(response => {
                console.log("getReportHistory success", response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    resolve(object);
                } else {
                    reject(translate('saleOrderManager.non_report_history'));
                }
            }).catch(error => {
                console.log("getReportHistory error", error);
                reject(error.msgError);
            });
        });
    };
};

export const getHistoryEditSO = function (saleOrderID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                moduleID: getState().userReducer.moduleID,
                languageID: getState().userReducer.languageID,
                loginStoreId: getState().userReducer.storeID,
                "saleOrderID": saleOrderID
            };
            apiBase(API_GET_EDIT_LOG, METHOD.POST, body).then((response) => {
                console.log("getHistoryEditSO success", response);
                const { object } = response;
                if (helper.IsNonEmptyArray(object)) {
                    resolve(object);
                }
                else {
                    reject(translate('saleOrderManager.non_edit_history'));
                }
            }).catch(error => {
                console.log("getHistoryEditSO success", error);
                reject(error.msgError);
            })
        })
    }
}


const getProductPromotion = (data) => {
    return (dispatch, getState) => {
        const {
            mainProduct,
            giftPromotion,
            deliveryGiftPromotions,
        } = data;
        const dataPromotion = helper.handelGiftPromotion(giftPromotion);
        const dataPromotionDelivery = helper.handelGiftPromotion(deliveryGiftPromotions);
        const allPromotion = [...dataPromotion, ...dataPromotionDelivery];
        const { allPromotionID } = helper.getAllKeyPromotion(allPromotion, []);
        dataPromotion.forEach(groupPromotion => {
            const { contentExclude, newExcludePromotionIDs } = helper.getExcludeContent(
                groupPromotion,
                allPromotionID,
                dataPromotionDelivery,
                [],
            );
            groupPromotion.excludePromotionIDs = newExcludePromotionIDs;
            groupPromotion.contentExclude = contentExclude;
        });
        dataPromotionDelivery.forEach(groupPromotion => {
            const { contentExclude, newExcludePromotionIDs } = helper.getExcludeContent(
                groupPromotion,
                allPromotionID,
                dataPromotion,
                [],
                translate('saleOrderManager.promotion'),
                translate('saleOrderManager.attached')
            );
            groupPromotion.excludePromotionIDs = newExcludePromotionIDs;
            groupPromotion.contentExclude = contentExclude;
        });
        dispatch(set_product_promotion_pre(
            mainProduct,
            dataPromotion,
            dataPromotionDelivery,
        ));
    }
}

const getDataSectionsList = (listOrder) => {
    const mapData = new Map();
    listOrder.forEach((ele, index) => {
        const { CREATEDATE } = ele;
        const keyDate = dateHelper.formatStrDateDDMMYYYY(CREATEDATE);
        if (mapData.has(keyDate)) {
            const dataSection = mapData.get(keyDate);
            dataSection.push(ele);
            mapData.set(keyDate, dataSection);
        } else {
            mapData.set(keyDate, [ele]);
        }
    });
    const dataList = [];
    for (let [key, value] of mapData) {
        dataList.push({ title: key, data: value });
    }
    return dataList;
};

const start_search_sale_order = () => {
    return ({
        type: START_SEARCH_SALEORDER
    });
}

const stop_search_sale_order = (
    { dataSearchSO, isFilterDay },
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_SEARCH_SALEORDER,
        dataSearchSO,
        isFilterDay,
        isEmpty,
        description,
        isError
    });
}

const update_data_search_so = (dataSearchSO = []) => {
    return ({
        type: UPDATE_DATA_SEARCH_SO,
        dataSearchSO,
    });
}

const set_param_filter = (paramFilter) => {
    return {
        type: SET_PARAM_FILTER,
        paramFilter
    };
};

const set_info_so_delete = (infoSODelete) => {
    return {
        type: SET_INFO_SO_DELETE,
        infoSODelete
    };
};

const start_get_cart_preorder = () => {
    return ({
        type: START_GET_CART_PREORDER
    });
}

const stop_get_cart_preorder = (
    preOrderCart,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_CART_PREORDER,
        preOrderCart,
        isEmpty,
        description,
        isError
    });
}

const set_product_promotion_pre = (
    product,
    promotion,
    promotionDelivery,
) => {
    return ({
        type: SET_PRODUCT_PROMOTION_PRE,
        product,
        promotion,
        promotionDelivery,
    });
}

const start_modify_preorder_cart = () => {
    return ({
        type: START_MODIFY_PREORDER_CART
    });
}

const stop_modify_preorder_cart = (preOrderCart, preSaleOrder) => {
    return ({
        type: STOP_MODIFY_PREORDER_CART,
        preOrderCart,
        preSaleOrder
    });
}

const start_get_type_report = () => {
    return ({
        type: START_GET_TYPE_REPORT
    });
}

const stop_get_type_report = (
    reportContent,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_TYPE_REPORT,
        reportContent,
        isEmpty,
        description,
        isError
    });
}

const set_sim_request_pre = (
    preSaleOrder,
    simProcessRequest,
) => {
    return ({
        type: SET_SIM_REQUEST_PRE,
        preSaleOrder,
        simProcessRequest,
    });
}