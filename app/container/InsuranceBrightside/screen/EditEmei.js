import { <PERSON><PERSON>, Animated, SafeAreaView, StyleSheet, Text, View } from 'react-native';
import React, { useRef, useState, useCallback } from 'react';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { COLORS } from '@styles';
import { BarcodeCamera, Button, Icon, MyText, SearchInput, hideBlockUI, showBlockUI } from '@components';
import { constants } from '@constants';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import * as actionInsurancePVICreator from "../action";
import * as actionPaymentOrderCreator from "../../SaleOrderPayment/action";
import ButtonCollection from '../component/ButtonCollection';
import { helper } from '@common';
import { useFocusEffect } from '@react-navigation/native';
import ModalEditImei from '../component/Modal/ModalEditImei';
import AcceptCancel from '../../CollectionTransferManager/component/AcceptCancel';
import * as actionCollectionCreator from '../../CollectionTransfer/action';
import { translate } from '@translate';
const MAX_HEIGHT = 117;
const MIN_HEIGHT = 40;
const DISTANCE = MAX_HEIGHT - MIN_HEIGHT;

const EditEmei = ({ actionInsurancePVI, actionCollection, itemCatalog, route, dataInserAndCreateTicket, navigation }) => {
    const [scrollY, setScrollY] = useState(new Animated.Value(0));
    const [keyword, setKeyword] = useState('');
    const isScrolling = useRef(false);
    const [isVisibleScan, setIsVisibleScan] = useState(false);
    const [dataValidateImei, setdataValidateImei] = useState({});
    const diffClamp = Animated.diffClamp(scrollY, 0, DISTANCE);
    const [isVisibleModalCollection, setIsVisibleModalCollection] = useState(false);
    const [statusTicket, setStatusTicket] = useState({});
    const translateY = diffClamp.interpolate({
        inputRange: [0, DISTANCE],
        outputRange: [0, -DISTANCE],
    });
    const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog ?? '';
    const { data } = route.params ?? {};
    const getTicketStatus = statusTicket?.STATUSID;
    const getStatusMess = statusTicket?.STATUSMESS;
    const { LISTUSERAPPROVE } = dataInserAndCreateTicket ?? '';
    const { SERVICEVOUCHERID, AIRTIMETRANSACTIONID, AIRTIMETRANSACTIONTYPEID } = data;


    useFocusEffect(
        useCallback(() => {
            if (dataInserAndCreateTicket) {
                actionCollection.cleardDataTicket();
            }
        }, [dataInserAndCreateTicket.STATUSID, actionCollection])
    );

    const handleSearch = (keyword) => {
        showBlockUI();
        const data = {
            catalogID: ServiceCategoryID,
            serviceGroupID: AirtimeServiceGroupID,
            airtimeTransactionID: AIRTIMETRANSACTIONID,
            airtimeTransactionTypeID: AIRTIMETRANSACTIONTYPEID,
            imei: keyword
        }
        actionInsurancePVI.validateImeiAirtimeService(data).then((reponse) => {
            hideBlockUI();
            setdataValidateImei(reponse)
        })
            .catch((msgError) => {
                Alert.alert("", msgError, [
                    {
                        text: "OK",
                        onPress: () => {
                            hideBlockUI();
                        },
                    },
                ]);
            });
    }

    const handleQueryStatus = () => {
        showBlockUI();
        const { TICKETID, AIRTIMETRANSACTIONID } = dataInserAndCreateTicket ?? {};
        const data = {
            TICKETID: TICKETID,
            AIRTIMETRANSACTIONID: AIRTIMETRANSACTIONID,
        }
        actionInsurancePVI.checksSatusTicketService(data).then((reponseStatus) => {
            hideBlockUI();
            setStatusTicket(reponseStatus);

        }).catch(msgError => {
            Alert.alert("", msgError, [
                {
                    text: "OK",
                    onPress: hideBlockUI,
                },
                {
                    text: "Thử lại",
                    onPress: () => { handleQueryStatus() }
                }
            ])
        });
    }

    const onCancelSOAndCreateCM = () => {
        Alert.alert("", "Bạn có chắc muốn chỉnh sửa IMEI cho đơn hàng này", [
            {
                text: translate('saleOrderManager.btn_skip_uppercase'),
                style: "cancel"
            },
            {
                text: translate('saleOrderManager.btn_continue_uppercase'),
                style: "default",
                onPress: () => createAirtimeEditRequest()
            }
        ])
    }

    const createAirtimeEditRequest = () => {
        showBlockUI();
        const data = {
            catalogID: ServiceCategoryID,
            serviceGroupID: AirtimeServiceGroupID,
            airtimeTransactionID: AIRTIMETRANSACTIONID,
            airtimeTransactionTypeID: AIRTIMETRANSACTIONTYPEID,
            imei: keyword
        }
        actionInsurancePVI.createAirtimeEditRequest(data).then((reponse) => {
            hideBlockUI();
            setIsVisibleModalCollection(true);
        })
            .catch((msgError) => {
                Alert.alert("", msgError, [
                    {
                        text: "OK",
                        onPress: () => {
                            hideBlockUI();
                        },
                    },
                ]);
            });
    }

    const handleReplyTicket = () => {
        showBlockUI();
        const data = {
            airtimeTransactionID: AIRTIMETRANSACTIONID,
            imei: keyword
        }
        actionCollection.createTicletEditImeiServiceRequest(data).then((reponseDataTicket) => {
            hideBlockUI();
            setStatusTicket({});
        }).catch(msgError => {
            Alert.alert("", msgError, [
                {
                    text: "OK",
                    onPress: hideBlockUI,
                },
                {
                    text: "Thử lại",
                    onPress: () => handleReplyTicket()
                }
            ])
        });
    }

    const onNavigation = () => {
        setIsVisibleModalCollection(false);
        navigation.navigate("HistoryEditEmei", { SERVICEVOUCHERID });
    }

    const handleStatusSuccess = () => {
        setIsVisibleModalCollection(false);
        navigation.navigate("HistoryEditEmei", { SERVICEVOUCHERID });
    }

    return (
        <View
            style={{
                flex: 1,
                backgroundColor: COLORS.bgFFFFFF
            }}
        >
            <KeyboardAwareScrollView
                style={{
                    flex: 1,
                }}
                enableResetScrollToCoords={false}
                keyboardShouldPersistTaps="always"
                bounces={false}
                overScrollMode="always"
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                extraScrollHeight={60}
            >
                <SafeAreaView
                    style={{
                        flex: 1,
                        alignItems: "center",
                        justifyContent: 'center'
                    }}
                >
                    <Animated.View style={{
                        transform: [{ translateY: translateY }],
                        backgroundColor: COLORS.bgFFFFFF,
                        position: 'relative',
                        top: 0, left: 0, right: 0, zIndex: 1,
                    }}>
                        <View style={{
                            marginTop: 5,
                            justifyContent: 'center',
                            alignItems: 'center'
                        }}>
                            <SearchInput
                                width={constants.width * 0.98}
                                height={40}
                                placeholder={"Nhập imei mới của sản phẩm "}
                                rightComponent={[
                                    !keyword
                                        ? {
                                            source: { uri: 'ic_barcode_input' },
                                            onpress: () => {
                                                setIsVisibleScan(true);
                                                setScrollY(new Animated.Value(0));
                                                setKeyword('');
                                                setdataValidateImei({});
                                                setStatusTicket({});
                                            }
                                        }
                                        : {
                                            source: { uri: 'ic_search' },
                                            onpress: () => {
                                                handleSearch(keyword);
                                                setScrollY(new Animated.Value(0));
                                                setdataValidateImei({});
                                                setStatusTicket({});
                                            }
                                        },
                                    !!keyword
                                        ? {
                                            source: { uri: 'ic_close' },
                                            style: {
                                                width: 12,
                                                height: 12,
                                                tintColor: COLORS.ic0000002
                                            },
                                            onpress: () => {
                                                setKeyword('');
                                                setScrollY(new Animated.Value(0));
                                                setdataValidateImei({});
                                                setStatusTicket({});
                                            }
                                        }
                                        : null
                                ]}
                                onChangeText={(text) => {
                                    if (!isScrolling.current) {
                                        setKeyword(text);
                                        setScrollY(new Animated.Value(0));
                                        setdataValidateImei({});
                                    }
                                }}
                                value={keyword}
                                onSubmitEditing={() => {
                                    handleSearch(keyword);
                                    setdataValidateImei({});
                                    setStatusTicket({});
                                }}
                                returnKeyType="search"
                            />
                        </View>
                        <View>
                            {
                                !helper.IsEmptyObject(dataValidateImei) ?
                                    <AcceptCancel
                                        title="QUẢN LÝ SIÊU THỊ XÁC NHẬN"
                                        onPress={() => handleReplyTicket()}
                                    />
                                    :
                                    null

                            }
                            {
                                !helper.IsEmptyObject(dataInserAndCreateTicket) && !helper.IsEmptyObject(dataValidateImei) &&
                                <View>
                                    <View style={{
                                        padding: 10
                                    }}>
                                        <View style={{
                                            backgroundColor: COLORS.bgFFFFFF,
                                            borderRadius: 7,
                                            padding: 10,
                                            shadowColor: COLORS.bg7F7F7F,
                                            shadowOffset: {
                                                width: 0,
                                                height: 0,
                                            },
                                            shadowOpacity: 0.5,
                                            shadowRadius: 1,
                                            elevation: 5,
                                        }}>
                                            <MyText
                                                text={"Yêu cầu hoàn tiền của giao dịch "}
                                                addSize={-1.5}
                                                style={{
                                                    color: COLORS.txt333333,
                                                    marginBottom: 10,
                                                    fontSize: 15,
                                                    marginTop: 10,
                                                    marginLeft: 5
                                                }} >
                                                {
                                                    <MyText
                                                        text={`[${SERVICEVOUCHERID}]`}
                                                        addSize={-1.5}
                                                        style={{
                                                            color: COLORS.txtD0021B,
                                                            fontSize: 15
                                                        }}
                                                    >
                                                        {
                                                            <MyText
                                                                text={` của bạn đã được gửi thông báo đến app X-Work của quản lý siêu thị có chấm công trong ca gồm: ${LISTUSERAPPROVE}. Vui lòng chờ quản lý siêu thị xác nhận trên App X-Work!`}
                                                                addSize={-1.5}
                                                                style={{
                                                                    color: COLORS.txt333333,
                                                                    fontSize: 15
                                                                }}
                                                            />
                                                        }
                                                    </MyText>
                                                }
                                            </MyText>
                                            <View style={{
                                                flexDirection: 'row'
                                            }}>
                                                <Icon
                                                    iconSet={'MaterialIcons'}
                                                    name={'info-outline'}
                                                    color={COLORS.bgFF0000}
                                                    size={18}
                                                />
                                                <MyText
                                                    text={"Ticket có hiệu lực trong 10 phút"}
                                                    addSize={-1.5}
                                                    style={{
                                                        color: COLORS.bgFF0000,
                                                        fontSize: 15,
                                                        marginLeft: 5,
                                                        fontStyle: 'italic'
                                                    }}
                                                />
                                            </View>
                                        </View>
                                        <View style={{
                                            flexDirection: 'row',
                                            backgroundColor: COLORS.bgFFFFFF,
                                            borderRadius: 7,
                                            padding: 10,
                                            alignItems: 'center',
                                            shadowColor: COLORS.bg7F7F7F,
                                            shadowOffset: {
                                                width: 0,
                                                height: 0,
                                            },
                                            shadowOpacity: 0.5,
                                            shadowRadius: 1,
                                            elevation: 5,
                                            marginTop: 10
                                        }}>
                                            <MyText
                                                text={"Trạng thái ticket:"}
                                                addSize={-1.5}
                                                style={{
                                                    color: COLORS.txt333333,
                                                    marginBottom: 10,
                                                    fontSize: 15,
                                                    marginTop: 10,
                                                    fontWeight: 'bold'
                                                }} />
                                            <MyText
                                                text={getTicketStatus != null ? getStatusMess : "Đã gửi cho QLST"}
                                                addSize={-1.5}
                                                style={{
                                                    color: (getTicketStatus != "APPROVE" && getTicketStatus != null) ? COLORS.bgFF0000 : COLORS.bg00AAFF,
                                                    fontSize: 15,
                                                    fontWeight: 'bold',
                                                    marginLeft: 5
                                                }}
                                            />
                                        </View>
                                    </View>
                                    <View style={{
                                        padding: 10
                                    }}>
                                        {

                                            getTicketStatus == "APPROVE" ?
                                                <ButtonAction
                                                    onPress={onCancelSOAndCreateCM}
                                                    disabled={false}
                                                />
                                                :
                                                <View style={{
                                                    justifyContent: 'center',
                                                    alignItems: 'center',
                                                    flexDirection: 'row'
                                                }}>
                                                    <ButtonCollection
                                                        onPress={() => handleReplyTicket()}
                                                        disabled={helper.IsEmptyObject(statusTicket) || getTicketStatus == undefined || getTicketStatus == "WAITING" ? true : false}
                                                        title={"GỬI LẠI TICKET"}
                                                        iconSet={"Ionicons"}
                                                        nameIcon={"reload"}
                                                        style={{
                                                            backgroundColor: COLORS.bg00A98F
                                                        }}
                                                        opacity={helper.IsEmptyObject(statusTicket) || getTicketStatus == undefined || getTicketStatus == "WAITING" ? 0.5 : 1}
                                                    />
                                                    <View style={{ flex: 1 }} />
                                                    <ButtonCollection
                                                        onPress={() => handleQueryStatus()}
                                                        title={"KIỂM TRA KẾT QUẢ"}
                                                        iconSet={"Ionicons"}
                                                        nameIcon={"search"}
                                                        style={{
                                                            backgroundColor: COLORS.bg1E88E5,
                                                        }}
                                                    />
                                                </View>
                                        }
                                    </View>
                                </View>
                            }
                        </View>
                    </Animated.View>
                </SafeAreaView>
                {isVisibleScan && (
                    <BarcodeCamera
                        isVisible={isVisibleScan}
                        closeCamera={() => setIsVisibleScan(false)}
                        resultScanBarcode={(barcode) => {
                            setKeyword(barcode);
                            handleSearch(barcode);
                            setIsVisibleScan(false);
                        }}
                    />
                )}
                {
                    isVisibleModalCollection && <ModalEditImei
                        isVisible={isVisibleModalCollection}
                        onClose={() => onNavigation()}
                        onSuccess={() => handleStatusSuccess()}
                    />
                }
            </KeyboardAwareScrollView>
        </View>
    )
}

const mapStateToProps = function (state) {
    return {
        itemCatalog: state.collectionReducer.itemCatalog,
        dataInserAndCreateTicket: state.collectionReducer.dataInserAndCreateTicket,
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionInsurancePVI: bindActionCreators(actionInsurancePVICreator, dispatch),
        actionPaymentOrder: bindActionCreators(actionPaymentOrderCreator, dispatch),
        actionCollection: bindActionCreators(actionCollectionCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(EditEmei);

const styles = StyleSheet.create({})


const ButtonAction = ({ disabled, onPress }) => {
    return (
        <View style={{
            alignItems: "center",
            width: constants.width,
            paddingVertical: 10
        }}>
            <Button
                text={"TẠO YÊU CẦU CHỈNH SỬA IMEI"}
                styleContainer={{
                    backgroundColor: COLORS.btn288AD6,
                    borderColor: COLORS.bd288AD6,
                    borderWidth: 1,
                    borderRadius: 4,
                    paddingVertical: 10,
                    paddingHorizontal: 20,
                    opacity: disabled ? 0.5 : 1
                }}
                styleText={{
                    color: COLORS.txtFFFFFF,
                }}
                onPress={onPress}
                disabled={disabled}
            />
        </View>
    );
}