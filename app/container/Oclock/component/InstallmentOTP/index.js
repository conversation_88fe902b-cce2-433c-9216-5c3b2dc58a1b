import React, { Component } from 'react';
import { SafeAreaView, StyleSheet, Alert, Keyboard } from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { showBlockUI, hideBlockUI } from '@components';
import { helper, storageHelper } from '@common';
import Guide from './component/Guide';
import Customer from './component/Customer';
import OtpCode from './component/OtpCode';
import * as actionShoppingCartCreator from '../../../ShoppingCart/action';
import * as actionSaleOrderCreator from '../../../SaleOrderCart/action';
import * as actionPouchCreator from '../../../PouchRedux/action';
import * as actionOclockCreator from '../../action';
import { COLORS } from '@styles';
import { translate } from '@translate';

class InstallmentOTP extends Component {
    constructor() {
        super();
        this.state = {
            info: {
                customerPhone: '',
                customerName: ''
            },
            expireTime: 0,
            otpCode: '',
            isVisible: false
        };
        this.intervalId = null;
    }

    componentDidMount() {
        const { dataCartInstallment } = this.props;
        const memberInfo = getMemberInfo(dataCartInstallment);
        this.setState({ info: memberInfo });
    }

    componentWillUnmount() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }
    }

    render() {

        const { info, expireTime, otpCode } = this.state;
        return (
            <KeyboardAwareScrollView
                style={{
                    flex: 1,
                    backgroundColor: COLORS.bgFFFFFF
                }}
                enableResetScrollToCoords={false}
                keyboardShouldPersistTaps="always"
                bounces={false}
                overScrollMode="always"
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                extraScrollHeight={60}>
                {
                    helper.validateOtpSend() ? <SafeAreaView style={{ marginTop: 10, paddingBottom: 10 }}>
                        <OTPBackup
                            onPress={() => this.addToSaleOrderCart(this.props.dataCartInstallment)}
                            label={"Khách hàng đồng ý dán lại Màn Hình."}
                        >
                            <Customer info={info} />
                        </OTPBackup>
                    </SafeAreaView> : <SafeAreaView
                        style={{
                            flex: 1
                        }}>
                        <Guide />
                        <Customer info={info} />
                        <OtpCode
                            onCreate={this.onCreateOTP}
                            expireTime={expireTime}
                            code={otpCode}
                            onChange={(text) => {
                                const regExpOTP = new RegExp(/^\d{0,4}$/);
                                const isValidate = regExpOTP.test(text);
                                if (isValidate) {
                                    this.setState({ otpCode: text });
                                }
                            }}
                            onVerify={this.onCheckOTP}
                        />
                    </SafeAreaView>
                }

            </KeyboardAwareScrollView>
        );
    }

    countDown = () => {
        const { expireTime } = this.state;
        const second = expireTime - 1;
        if (second > 0) {
            this.setState({ expireTime: second });
        } else {
            this.resetCountDown();
        }
    };

    resetCountDown = () => {
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }
        this.setState({
            expireTime: 0,
            otpCode: ''
        });
    };

    setCountDown = () => {
        this.setState({
            expireTime: 60
        });
        this.intervalId = setInterval(this.countDown, 1000);
    };

    onCreateOTP = (type) => {
        // this.setCountDown();
        const {
            info: { customerPhone }
        } = this.state;
        const {
            userInfo: { brandID }
        } = this.props;
        showBlockUI();
        actionShoppingCartCreator
            .createOTP({
                type: type,
                phoneNumber: customerPhone,
                typeContent: 'OCLOCK',
                lenOtp: 4,
                brandID: brandID
            })
            .then((success) => {
                hideBlockUI();
                this.setCountDown();
            })
            .catch((msgError) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: 'cancel',
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: 'default',
                            onPress: () => this.onCreateOTP(type)
                        }
                    ]
                );
            });
    };

    onCheckOTP = () => {
        Keyboard.dismiss();
        const {
            otpCode,
            info: { customerPhone }
        } = this.state;
        const isValidate = this.validateOTP(otpCode);
        if (isValidate) {
            this.verifyOTP(otpCode, customerPhone);
        }
    };

    verifyOTP = (otpCode, customerPhone) => {
        const { dataCartInstallment } = this.props;
        showBlockUI();
        actionShoppingCartCreator
            .verifyOTP(otpCode, customerPhone)
            .then((data) => {
                this.addToSaleOrderCart(dataCartInstallment);
                // hideBlockUI();
                // alert("thành công")
            })
            .catch((msgError) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: 'cancel',
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: 'default',
                            onPress: () =>
                                this.verifyOTP(otpCode, customerPhone)
                        }
                    ]
                );
            });
    };

    validateOTP = (code) => {
        const regExpOTP = new RegExp(/^\d{4}$/);
        const isValidate = regExpOTP.test(code);
        if (!helper.IsNonEmptyString(code)) {
            Alert.alert('', translate('shoppingCart.validate_empty_otp'));
            return false;
        }
        if (!isValidate) {
            Alert.alert('', translate('shoppingCart.validate_otp'));
            return false;
        }
        return true;
    };

    createOrder = () => {
        const navigation = this.props.route.params.navigation;
        const dataPinOclock = this.props.route.params.batteryList;
        const item = this.props.route.params.item;
        this.props.actionOclock
            .addToSaleOrderCart(dataPinOclock, item)
            .then((orderInfo) => {
                hideBlockUI();
                navigation.navigate('OrderCart');
                // alert('TẠO ĐƠN THÀNH CÔNG');
            })
            .catch((msgError) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: 'cancel',
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: 'default',
                            onPress: () => this.addToSaleOrderCart(data)
                        }
                    ]
                );
            });
    };
    addToSaleOrderCart = (data) => {
        showBlockUI();
        this.props.actionSaleOrder
            .addToSaleOrderCart(data)
            .then((orderInfo) => {
                hideBlockUI();
                this.props.actionShoppingCart.deleteShoppingCart();
                this.props.actionPouch.setDataCartApply();
                this.props.navigation.navigate('SaleOrderCart');
                storageHelper.updateTopCustomerInfo(data.customerInfo);
            })
            .catch((msgError) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: 'cancel',
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: 'default',
                            onPress: () => this.addToSaleOrderCart(data)
                        }
                    ]
                );
            });
    };

    // getDataLoyalty = (customerPhone, dataSaleOrder) => {
    //     this.props.actionShoppingCart
    //         .checkCredentialExist(customerPhone, dataSaleOrder)
    //         .then((success) => {
    //             hideBlockUI();
    //             this.props.navigation.pop();
    //             this.props.navigation.navigate('Loyalty');
    //         });
    // };

    // onCheckLoyaltyPoint = (dataSaleOrder) => {
    //     const {
    //         cartRequest: { TotalPointLoyalty },
    //         customerInfo: { customerPhone }
    //     } = dataSaleOrder;
    //     const {
    //         userInfo: { storeID }
    //     } = this.props;
    //     const isLoyalty =
    //         helper.checkConfigPhoneLoyalty(customerPhone) &&
    //         helper.checkConfigStoreLoyalty(storeID);
    //     const isValidatePhone = helper.IsNonEmptyString(customerPhone);
    //     const isValidatePoint = TotalPointLoyalty > 0;
    //     const isMoveToLoyalty = isValidatePoint && isValidatePhone && isLoyalty;
    //     if (isMoveToLoyalty) {
    //         this.getDataLoyalty(customerPhone, dataSaleOrder);
    //     } else {
    //         this.addToSaleOrderCart(dataSaleOrder);
    //     }
    // };
}

const styles = StyleSheet.create({});

const mapStateToProps = function (state) {
    return {
        dataCartInstallment: state.shoppingCartReducer.dataCartInstallment,
        // dataCartInstallment:
        //     state.oclockReducer.dataOclock.SaleOrderBatteryBOList[0],
        userInfo: state.userReducer
    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        actionShoppingCart: bindActionCreators(
            actionShoppingCartCreator,
            dispatch
        ),
        actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),
        actionPouch: bindActionCreators(actionPouchCreator, dispatch),
        actionOclock: bindActionCreators(actionOclockCreator, dispatch)
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(InstallmentOTP);

const getMemberInfo = (dataCartInstallment) => {
    let info = {
        customerPhone: '',
        customerName: ''
    };
    if (!helper.IsEmptyObject(dataCartInstallment)) {
        const {
            customerInfo,
        } = dataCartInstallment;
        info = {
            customerPhone: customerInfo.customerPhone,
            customerName: customerInfo.customerName,
        };
    }
    return info;
};
