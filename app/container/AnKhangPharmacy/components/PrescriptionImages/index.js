import React, { useEffect, useRef, useState } from 'react';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { COLORS } from '@styles';
import {
    Alert,
    Keyboard,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    View,
    Modal,
} from 'react-native';
import {
    BaseLoading,
    Button,
    hideBlockUI,
    MyText,
    showBlockUI,
    Picker,
    Icon,
    TitleInput,
    CaptureCamera
} from '@components';
import { constants, API_CONST, ENUM } from '@constants';
import { translate } from '@translate';
import { helper } from '@common';

import { launchImageLibrary } from 'react-native-image-picker';
import { getImageCDN } from '../../../ActiveSimManager/action';
import ImageProcess from '../ImageProcess';
const { FILE_PATH: { PRESCRIPTION_IMAGES } } = ENUM;

const PrescriptionImages = ({
    navigation,
    route,
    handleStringImage,
    dataImage
}) => {
    const [strImage, setStrImage] = useState([
        {
            AttachmentID: 0,
            FileTypeID: 4,
            UrlFile: ''
        }
    ]);
    const [currentPicture, setCurrentPicture] = useState('');
    const [currentStrImageIndex, setCurrentStrImageIndex] = useState(0);
    const [isVisibleCamera, setIsVisibleCamera] = useState(false);
    const [showImage, setShowImage] = useState(false);

    useEffect(() => {
        handleStringImage(strImage);
    }, [strImage]);
    const takePicture = (photo) => {
        setIsVisibleCamera(false);
        showBlockUI();
        if (helper.hasProperty(photo, 'uri')) {
            helper
                .resizeImage(photo)
                .then(({ path, uri, size, name }) => {
                    const body = helper.createFormData({ uri: uri, type: 'image/jpg', name: name, path: PRESCRIPTION_IMAGES });
                    getImageCDN(body)
                        .then((response) => {
                            const remoteURI =
                                API_CONST.API_GET_IMAGE_CDN_NEW +
                                response[0];
                            switch (currentPicture) {
                                case 'strImage':
                                    const newContract = [...strImage];
                                    newContract[
                                        currentStrImageIndex
                                    ].UrlFile = remoteURI;
                                    setStrImage(newContract);
                                    break;
                                default:
                                    break;
                            }
                            hideBlockUI();
                        })
                        .catch((error) => {
                            hideBlockUI();
                            console.log('uploadPicture', error);
                        });
                })
                .catch((error) => {
                    hideBlockUI();
                    console.log('resizeImage', error);
                });
        } else {
            hideBlockUI();
        }
    };
    const selectPicture = () => {
        launchImageLibrary(
            {
                mediaType: 'photo',
                noData: true
            },
            (response) => {
                setIsVisibleCamera(false);
                showBlockUI();
                if (helper.hasProperty(response, 'uri')) {
                    helper
                        .resizeImage(response)
                        .then(({ path, uri, size, name }) => {
                            const body = helper.createFormData({ uri: uri, type: 'image/jpg', name: name, path: PRESCRIPTION_IMAGES });
                            getImageCDN(body)
                                .then((response) => {
                                    const remoteURI =
                                        API_CONST.API_GET_IMAGE_CDN_NEW +
                                        response[0];
                                    switch (currentPicture) {
                                        case 'strImage':
                                            const newContract = [...strImage];
                                            newContract[currentStrImageIndex].UrlFile =
                                                remoteURI;
                                            setStrImage(newContract);
                                            break;
                                        default:
                                            break;
                                    }
                                    hideBlockUI();
                                })
                                .catch((error) => {
                                    hideBlockUI();
                                    console.log('uploadPicture', error);
                                });
                        })
                        .catch((error) => {
                            hideBlockUI();
                            console.log('resizeImage', error);
                        });
                } else {
                    hideBlockUI();
                }
            }
        );
    };
    useEffect(() => {
        if (dataImage?.length > 0) {
            const lstImage = dataImage.map((item) => ({
                AttachmentID: item.AttachmentID,
                FileTypeID: 4,
                UrlFile: item.UrlFile
            }));
            setStrImage(lstImage);
        }
    }, []);
    return (
        <SafeAreaView
            style={{
                flex: 1,
                width: constants.width,
                alignItems: 'center'
            }}>
            <View
                style={{
                    alignItems: 'center',
                    backgroundColor: COLORS.btn5B9A68,
                    height: 40,
                    paddingHorizontal: 10
                }}>
                <Button
                    text="Hình ảnh toa thuốc"
                    onPress={() => setShowImage(!showImage)}
                    styleContainer={{
                        flexDirection: 'row',
                        width: constants.width,
                        height: 40,
                        justifyContent: 'space-between',
                        width: constants.width - 10
                    }}
                    styleText={{
                        color: COLORS.txtFFFFFF,
                        fontSize: 16,
                        marginRight: 8,
                        fontWeight: 'bold'
                    }}
                    iconRight={{
                        iconSet: 'FontAwesome',
                        name: showImage ? 'chevron-up' : 'chevron-down',
                        size: 14,
                        color: COLORS.icFFFFBC
                    }}
                />
            </View>
            {showImage && (
                <View>
                    <View
                        style={{
                            justifyContent: 'center',
                            alignItems: 'center'
                        }}>
                        <MyText
                            text="Bạn vui lòng tải hình ảnh hoặc chụp hình ảnh toa thuốc. "
                            style={{
                                color: COLORS.txt333333,
                                marginTop: 10,
                                fontSize: 15
                            }}
                        />
                    </View>

                    <View
                        style={{
                            borderWidth: StyleSheet.hairlineWidth,
                            margin: 10,
                            paddingBottom: 15,
                            paddingHorizontal: 10,
                            borderRadius: 5,
                            width: constants.width - 40
                        }}>
                        {strImage.map((item, index) => (
                            <ImageProcess
                                onCamera={() => {
                                    setIsVisibleCamera(true);
                                    setCurrentPicture('strImage');
                                    setCurrentStrImageIndex(index);
                                }}
                                urlImageLocal={item.UrlFile}
                                urlImageRemote={item.UrlFile}
                                deleteImage={() => {
                                    strImage.length > 1
                                        ? setStrImage(
                                            strImage.filter(
                                                (ct) =>
                                                    ct.UrlFile != item.UrlFile
                                            )
                                        )
                                        : setStrImage([
                                            {
                                                AttachmentID: 0,
                                                FileTypeID: 4,
                                                UrlFile: ''
                                            }
                                        ]);
                                }}
                                key={index.toString()}
                            />
                        ))}
                        {strImage.length < 3 && (
                            <TouchableOpacity
                                style={{
                                    flex: 1,
                                    height: 150,
                                    width: 150,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    alignSelf: 'center',
                                    marginTop: 15,
                                    backgroundColor: COLORS.btnF5F5F5,
                                    marginHorizontal: 2
                                }}
                                onPress={() => {
                                    setStrImage([
                                        ...strImage,
                                        {
                                            AttachmentID: 0,
                                            FileTypeID: 4,
                                            UrlFile: ''
                                        }
                                    ]);
                                }}
                                activeOpacity={0.6}>
                                <View
                                    style={{
                                        justifyContent: 'center',
                                        alignItems: 'center'
                                    }}>
                                    <Icon
                                        iconSet="Ionicons"
                                        name="add-circle-outline"
                                        color="#40B93C"
                                        size={60}
                                    />
                                </View>
                            </TouchableOpacity>
                        )}
                    </View>
                </View>
            )}

            <CaptureCamera
                isVisibleCamera={isVisibleCamera}
                takePicture={takePicture}
                closeCamera={() => {
                    setIsVisibleCamera(false);
                }}
                selectPicture={selectPicture}
            />
        </SafeAreaView>
    );
};
export default PrescriptionImages;
