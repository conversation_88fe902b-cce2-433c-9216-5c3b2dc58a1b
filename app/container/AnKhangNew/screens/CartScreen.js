/* eslint-disable react-native/no-unused-styles */
import React, { useState, useEffect, useRef } from 'react';
import { useSelector, useDispatch, batch } from 'react-redux';
import {
    View,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    Alert,
    KeyboardAvoidingView,
    Platform,
    Modal,
    Animated,
    SafeAreaView,
    Image
} from 'react-native';
import { v4 as uuidv4 } from 'uuid';
import { COLORS } from '@styles';
import {
    MyText,
    Button,
    Icon,
    CaptureCamera,
    LoyaltyScanner
} from '@components';
import { constants, API_CONST, DEVICE, STORAGE_CONST, ENUM } from '@constants';
import {
    IndicatorViewPager,
    PagerTitleIndicator
} from 'react-native-best-viewpager';
// eslint-disable-next-line import/no-unresolved
import { launchImageLibrary } from 'react-native-image-picker';
import ImageViewer from 'react-native-image-zoom-viewer';
import { CommonActions } from '@react-navigation/native';
import Toast from 'react-native-toast-message';
import { helper, storageHelper } from '@common';
import { translate, keys } from '@translate';
import { showMessage } from 'react-native-flash-message';
import {
    PAGER_TITLE_HEIGHT,
    DOT_SIZE,
    IMAGE_SIZE,
    SCREENS,
    ICON_BY_TITLE,
    TYPE_DELIVERY
} from '../constants';
import CartItem from '../components/CartItem';
import PriceText from '../components/PriceText';
import RadioItem from '../components/RadioItem';
import InputField from '../components/InputField';
import CheckBox from '../components/CheckBox';
import { PromotionProfit } from '../containers';
import { getImageCDN } from '../../ActiveSimManager/action';
import ImageProcess from '../components/ImageProcess';
import { CartCount } from '../components';
import {
    checkInstalledQTV,
    createSaleOrder,
    resetCartInfo,
    resetCustomerInfo,
    reset_map_prescriptions,
    setTempPhoneNumber,
    updateCustomerInfo,
    updateUrlFiles
} from '../action';
import {
    checkApplyGiftVoucher,
    checkCredentialExist,
    checkNumberPhoneApplyMedicine,
    getCompanyByTax,
    getCustomerByPhone,
    getCustomerProfile,
    getGiftVoucherCustomer,
    modifyCustomerProfile,
    reset_map_customer_confirm_policy,
    set_map_customer_confirm_policy
} from '../../ShoppingCart/action';
import { customerInfoValidation } from '../helpers';
import { checkMaxQuantity, getGender } from '../../ShoppingCart';
import useTitle from '../hooks/useTitle';
import useTheme from '../useTheme';
import useThemedStyles from '../useThemdStyles';
import { useBrandCheck, useCart } from '../hooks';
import { pharmacyState } from '../state';
import { Indicator } from '../index';
import { TYPE_PROFILE } from '../../../constants/constants';
import CheckBoxPolicy from '../../ShoppingCart/component/CheckBoxPolicy';
import { OTPSheet } from '../../../components';
import OTPInner from '../../ShoppingCart/component/OTPInner';
import VoucherSheet from '../../SaleOrderPayment/Sheet/VoucherSheet';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { checkDataInvalid } from '../../ShoppingCart/component/FormPromotion';

const AnimatedPagerView = Animated.createAnimatedComponent(IndicatorViewPager);
const { saleExpress, common, shoppingCart } = keys;
const { FILE_PATH: { CART_SCREEN } } = ENUM;

const renderPagerTitleIndicator = ({ hasPromotionProfit }) => {
    const theme = useTheme();
    const COLOR = theme.colors;
    const styles = useThemedStyles(style);
    let TITLES = useTitle('CART_TITLE');
    if (hasPromotionProfit) {
        TITLES = [...TITLES, saleExpress.title_promotion_profit];
    } else {
        TITLES = TITLES.filter(
            (title) => title !== saleExpress.title_promotion_profit
        );
    }
    const width = constants.width / TITLES.length;
    return (
        <PagerTitleIndicator
            initialPage={0}
            style={styles.pagerTitleContainer}
            titles={TITLES}
            trackScroll
            itemStyle={{ width }}
            selectedItemStyle={{ width }}
            selectedBorderStyle={styles.borderStyle}
            renderTitle={(_, titleKey, isSelected) => {
                const iconProps = ICON_BY_TITLE[titleKey];
                return (
                    <View
                        style={[
                            styles.title,
                            {
                                backgroundColor: isSelected
                                    ? COLOR.primary
                                    : COLOR.white
                            }
                        ]}>
                        <Icon
                            {...iconProps}
                            color={COLOR.primary}
                            size={24}
                            style={{
                                color: isSelected ? COLOR.white : COLOR.primary
                            }}
                        />
                    </View>
                );
            }}
            disabled={false}
        />
    );
};

const CartList = ({ data }) => {
    const theme = useTheme();
    const COLOR = theme.colors;
    return (
        <ScrollView style={{ flexGrow: 0 }} bounces={false}>
            {data.map((item, index) => {
                const bgColor =
                    index % 2 === 0 ? COLOR.white : COLOR.bgCartItem;
                return (
                    <CartItem
                        key={item[0]}
                        data={item[1]}
                        backgroundColor={bgColor}
                    />
                );
            })}
        </ScrollView>
    );
};

const CartScreen = ({ route, navigation }) => {
    const theme = useTheme();
    const COLOR = theme.colors;
    const styles = useThemedStyles(style);

    const [token, setToken] = useState('');
    const [loadingBlock, setLoadingBlock] = useState(false);
    const dispatch = useDispatch();
    const { storeID, moduleID, languageID, brandID } = useSelector(
        (state) => state.userReducer
    );

    const { customerInfo, urlFiles, typeDelivery, electricalPrescriptionBO } =
        useSelector((state) => state._pharmacyReducer);
    const { customerConfirmPolicy } = useSelector((_state) => _state.shoppingCartReducer)
    const { PRIORITYGIFTCODES } = useSelector((_state) => _state.appSettingReducer)
    const isAva = useBrandCheck(ENUM.BRAND_ID.AVA);
    const { handleSaleOrder } = useCart();
    const [isRequireCustomerInfo, setIsRequireCustomerInfo] = useState(false);
    const [isCompany, setIsCompany] = useState(false);
    const [dataVerifyInfo, setDataVerifyInfo] = useState({});
    // Toa thuốc
    const [isVisibleCamera, setIsVisibleCamera] = useState(false);
    const [isShowImageViewer, setIsShowImageViewer] = useState(false);
    const [indexImage, setIndexImage] = useState(0);
    const [products, setProducts] = useState([]);
    const [totalCost, setTotalCost] = useState({
        ShippingCost: 0,
        TotalAmount: 0,
        TotalDiscountByCoupon: 0,
        TotalDiscountByPromotionOrder: 0,
        TotalPaid: 0
    });

    const [productPromos, setProductPromos] = useState([]); // Khuyến mãi tổng đơn tặng sản phẩm
    const [discountPromos, setDiscountPromos] = useState([]); // Khuyến mãi giảm giá tổng đơn

    const [statePolicy, setStatePolicy] = useState({
        isSelectedPolicy: 0,
        disabledPolicy: false,
    })
    const [isUpdate, setIsUpdate] = useState(0)
    const [tempDataCart, setTempDataCart] = useState({})
    const [vouchers, setVouchers] = useState([])
    const [isPresented, setIsPresented] = useState(false)
    const [isRefetch, setIsRefetch] = useState(false)



    const pageViewerRef = useRef(null);
    const isFirstRenderProfile = useRef(true);
    const applyTimes = useRef(0)
    const OTPSheetRef = useRef(null)
    const voucherCustomerPhone = useRef(null)
    const voucherSheetRef = useRef(null)



    const getToken = () => {
        storageHelper
            .getItem(STORAGE_CONST.ACCESS_TOKEN)
            .then((accessToken) => {
                setToken(accessToken);
            })
            .catch((error) => {
                console.log('authen error', error);
            });
    };
    useEffect(getToken, []);

    const propsImage = {
        width: constants.width,
        height: constants.height,
        props: {
            source: {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            },
            style: {
                resizeMode: 'contain'
            }
        }
    };

    const {
        data,
        dosages,
        isDisableCustomerPhone,
        loyaltyInfo,
        totalQuantity
    } = route.params;

    const textDeliveryAddress =
        data.SaleOrderDetails[0].DeliveryInfoRequest?.DeliveryAddress;

    const hasSaleOrderPromos =
        productPromos.length > 0 || discountPromos.length > 0;
    const shouldSetHeight =
        hasSaleOrderPromos || (!!textDeliveryAddress && isAva);

    useEffect(() => {
        const mapData = {};
        let hasControlSpecs = false;
        let dataCrossSellingByOrder = {};
        data.SaleOrderDetails.forEach((SODetail) => {
            const {
                SaleOrderDetailID,
                ApplySaleOrderDetailID,
                SalePriceVAT,
                PromotionID,
                IsPromotionByShoppingCart
            } = SODetail;
            if (SaleOrderDetailID) {
                // Lấy ra sản phẩm chính hoặc Bán kèm
                const isCrossSelling =
                    ApplySaleOrderDetailID && SalePriceVAT > 0 && PromotionID;
                const isMainProduct =
                    !ApplySaleOrderDetailID && SalePriceVAT > 0 && !PromotionID;
                if (isCrossSelling || isMainProduct) {
                    if (!mapData[SaleOrderDetailID]) {
                        mapData[SaleOrderDetailID] = {
                            ...SODetail
                        };
                    } else {
                        mapData[SaleOrderDetailID] = {
                            ...mapData[SaleOrderDetailID],
                            ...SODetail
                        };
                    }
                }
                const isCrossSellingByOrder =
                    !ApplySaleOrderDetailID && SalePriceVAT > 0 && PromotionID;
                if (isCrossSellingByOrder) {
                    dataCrossSellingByOrder = {
                        ...dataCrossSellingByOrder,
                        [SaleOrderDetailID]: {
                            ...SODetail,
                            isCSPromotion: true
                        }
                    };
                } else if (SalePriceVAT === 0 && PromotionID) {
                    if (IsPromotionByShoppingCart) {
                        // Lấy ra khuyến mãi tổng đơn
                        productPromos.push({ ...SODetail });
                    } else if (ApplySaleOrderDetailID) {
                        // Lấy ra khuyến mãi theo sản phẩm
                        if (!mapData[ApplySaleOrderDetailID]) {
                            mapData[ApplySaleOrderDetailID] = {
                                giftPromotions: [{ ...SODetail }]
                            };
                        } else if (
                            !mapData[ApplySaleOrderDetailID].giftPromotions
                        ) {
                            mapData[ApplySaleOrderDetailID].giftPromotions = [
                                { ...SODetail }
                            ];
                        } else {
                            mapData[ApplySaleOrderDetailID].giftPromotions.push(
                                { ...SODetail }
                            );
                        }
                    }
                }
            }
            if (!hasControlSpecs && SODetail.cus_IsMedicineControlSpecs) {
                hasControlSpecs = SODetail.cus_IsMedicineControlSpecs;
            }
        });
        // Gắn thêm khuyến mãi giảm tiền
        if (
            data.SaleOrderPromotionDiscounts &&
            data.SaleOrderPromotionDiscounts.length > 0
        ) {
            data.SaleOrderPromotionDiscounts.forEach((discount) => {
                // Tạo biến để UI sử dụng vì `IsPercentDiscount` kiểu Int và có 3 value 0, 1, 2
                //  1- % từ giá bán; 0 - Giá bán(VAT) ; 2 - Giảm theo số tiền
                discount.ui_IsPercentDiscount = discount.IsPercentDiscount;
                const { SaleOrderDetailID } = discount;
                if (
                    discount.IsPercentDiscount &&
                    discount.DiscountValue > 100
                ) {
                    discount.ui_IsPercentDiscount = 0;
                }
                if (SaleOrderDetailID) {
                    // khuyến mãi theo sản phẩm
                    if (mapData[SaleOrderDetailID]) {
                        if (!mapData[SaleOrderDetailID].discountPromotions) {
                            mapData[SaleOrderDetailID].discountPromotions = [
                                { ...discount }
                            ];
                        } else {
                            mapData[SaleOrderDetailID].discountPromotions.push({
                                ...discount
                            });
                        }
                    } else if (dataCrossSellingByOrder[SaleOrderDetailID]) {
                        if (
                            !dataCrossSellingByOrder[SaleOrderDetailID]
                                .discountPromotions
                        ) {
                            dataCrossSellingByOrder[
                                SaleOrderDetailID
                            ].discountPromotions = [{ ...discount }];
                        } else {
                            dataCrossSellingByOrder[
                                SaleOrderDetailID
                            ].discountPromotions.push({
                                ...discount
                            });
                        }
                    }
                } else {
                    // khuyến mãi tổng đơn
                    discountPromos.push({ ...discount });
                }
            });
        }

        setProductPromos(productPromos);
        setDiscountPromos(discountPromos);
        setIsRequireCustomerInfo(hasControlSpecs);
        setTotalCost({
            ...data.TotalCostSHBO
        });

        const arrangedProducts = arrangeProducts(Object.entries(mapData));
        const newProducts = [
            ...arrangedProducts,
            ...Object.entries(dataCrossSellingByOrder)
        ];
        setProducts(newProducts);
    }, [data]);

    useEffect(() => {
        if (loyaltyInfo && !helper.IsEmptyObject(loyaltyInfo)) {
            setDataVerifyInfo(loyaltyInfo);
        }
    }, [loyaltyInfo]);

    useEffect(() => {
        if (CustomerPhone && isDisableCustomerPhone) {
            // getContactInfo(CustomerPhone);
            handleAPIGetCustomerProfile(CustomerPhone)
        }
    }, [isDisableCustomerPhone, customerInfo.CustomerPhone, isUpdate]);
    useEffect(() => {
        if ((!!customerInfo.ContactPhone || !!customerInfo.TempCartContactPhone) && isFirstRenderProfile.current && typeDelivery !== TYPE_DELIVERY.HOME) {
            // getContactInfo(CustomerPhone);
            handleAPIGetCustomerProfile(customerInfo.ContactPhone || customerInfo.TempCartContactPhone)
            isFirstRenderProfile.current = false
        }
    }, [customerInfo.ContactPhone, customerInfo.TempCartContactPhone]);

    useEffect(() => {
        if (helper.IsNonEmptyArray(customerConfirmPolicy[TYPE_PROFILE.CUSTOMER])) {
            const {
                isSigned,
            } = customerConfirmPolicy?.[TYPE_PROFILE.CUSTOMER]?.[0] ?? {}
            const oldInfoCustomer = {
                "customerName": customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]?.[0]?.customerName,
                "customerPhone": customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]?.[0]?.phoneNumber,
                "gender": customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]?.[0]?.gender,
            }
            const infoCustomer = {
                "customerName": ContactName,
                "customerPhone": ContactPhone,
                "gender": getGender(Gender),
            }
            console.log("🚀 ~ useEffect ~ oldInfoCustomer:", oldInfoCustomer, infoCustomer)

            const hasChangeValueProfile = helper.checkChangeValueOfPrototype(infoCustomer, oldInfoCustomer)
            if (hasChangeValueProfile) {
                setStatePolicy({
                    isSelectedPolicy: false,
                    disabledPolicy: false
                })
            }
            else {
                setStatePolicy({
                    isSelectedPolicy: isSigned,
                    disabledPolicy: isSigned == 1 ? true : false
                })
            }
        }
        if (helper.IsNonEmptyArray(customerConfirmPolicy[TYPE_PROFILE.COMPANY])) {
            !isCompany && delete customerConfirmPolicy?.[TYPE_PROFILE.COMPANY]
        }
    }, [customerInfo]);

    useEffect(() => {
        const customerId = customerConfirmPolicy?.[TYPE_PROFILE.CUSTOMER]?.[0]?.profileId
        if (!!customerId && !!ContactPhone) {
            getGiftVoucherCustomer({
                "customerId": customerId,
                "queryType": 1,
                "applyBrands": [brandID]
            }).then(vouchers => {
                const sortVouchers = (vouchers) => {
                    return vouchers.sort((a, b) => {
                        // Kiểm tra voucher có thuộc config A không
                        const aInConfigA = PRIORITYGIFTCODES.includes(a.partnerGiftcode);
                        const bInConfigA = PRIORITYGIFTCODES.includes(b.partnerGiftcode);

                        // Ưu tiên voucher thuộc config A lên đầu
                        if (aInConfigA && !bInConfigA) return -1;
                        if (!aInConfigA && bInConfigA) return 1;

                        // Cùng nhóm thì sắp xếp theo expiredDate tăng dần (hết hạn sớm hơn lên trước)
                        return a.expiredDate - b.expiredDate;
                    });
                }
                voucherCustomerPhone.current = customerConfirmPolicy?.[TYPE_PROFILE.CUSTOMER]?.[0]?.phoneNumber
                console.log("🚀 ~ useEffect ~ sortVouchers(vouchers):", sortVouchers(vouchers))

                setVouchers(sortVouchers(vouchers))
            });
        }

    }, [customerConfirmPolicy?.[TYPE_PROFILE.CUSTOMER]?.[0]?.profileId])

    useEffect(() => {
        if (vouchers.length > 0) {
            voucherSheetRef.current?.present()

        }

    }, [vouchers])

    const arrangeProducts = (list) => {
        const newList = [...list];
        const result = [];
        newList.forEach(([id, product]) => {
            if (product.ApplySaleOrderDetailID) {
                const mainProductIndex = result.findIndex(
                    (item) => item[0] === product.ApplySaleOrderDetailID
                );
                if (mainProductIndex !== -1) {
                    result.splice(mainProductIndex + 1, 0, [id, product]);
                }
            } else {
                result.push([id, product]);
            }
        });
        return result;
    };

    const takePicture = (photo) => {
        setIsVisibleCamera(false);
        showBlockUI();
        if (helper.hasProperty(photo, 'uri')) {
            helper
                .resizeImage(photo)
                .then(({ uri, name }) => {
                    const body = helper.createFormData({ uri: uri, type: 'image/jpg', name: name, path: CART_SCREEN });
                    getImageCDN(body)
                        .then((res) => {
                            const remoteURI =
                                API_CONST.API_GET_IMAGE_CDN_NEW + res[0];
                            const newUrlFiles = [...urlFiles];
                            newUrlFiles.push({
                                url: remoteURI,
                                ...propsImage
                            });
                            dispatch(updateUrlFiles(newUrlFiles));
                            hideBlockUI();
                        })
                        .catch((error) => {
                            hideBlockUI();
                            console.log('uploadPicture', error);
                        });
                })
                .catch((error) => {
                    hideBlockUI();
                    console.log('resizeImage', error);
                });
        } else {
            hideBlockUI();
        }
    };
    const selectPicture = () => {
        launchImageLibrary(
            {
                mediaType: 'photo',
                noData: true
            },
            (response) => {
                setIsVisibleCamera(false);
                showBlockUI();
                if (helper.hasProperty(response, 'uri')) {
                    helper
                        .resizeImage(response)
                        .then(({ uri, name }) => {
                            const body = helper.createFormData({ uri: uri, type: 'image/jpg', name: name, path: CART_SCREEN });
                            getImageCDN(body)
                                .then((res) => {
                                    const remoteURI =
                                        API_CONST.API_GET_IMAGE_CDN_NEW + res[0];

                                    const newUrlFiles = [...urlFiles];
                                    newUrlFiles.push({
                                        url: remoteURI,
                                        ...propsImage
                                    });
                                    dispatch(updateUrlFiles(newUrlFiles));
                                    hideBlockUI();
                                })
                                .catch((error) => {
                                    hideBlockUI();
                                    console.log('uploadPicture', error);
                                });
                        })
                        .catch((error) => {
                            hideBlockUI();
                            console.log('resizeImage', error);
                        });
                } else {
                    hideBlockUI();
                }
            }
        );
    };

    const handleSwitchGender = (Gender) => {
        // true -> Anh | false -> Chị
        dispatch(updateCustomerInfo({ Gender }));
    };

    const getCustomerInfo = (phoneNumber, CustomerInfo) => {
        const baseBody = {
            "loginStoreId": storeID,
            "languageID": languageID,
            "moduleID": moduleID,
        }
        const regExpPhone = new RegExp(/^[0]\d{9}$/);
        const isValidate = regExpPhone.test(phoneNumber);
        if (isValidate) {
            showBlockUI()
            const { ContactName } = CustomerInfo;
            checkAppLoyalty(phoneNumber);
            getCustomerProfile({ ...baseBody, phoneNumber, typeProfile: TYPE_PROFILE.CUSTOMER }).then(customerProfile => {
                hideBlockUI()
                if (customerProfile == null) return dispatch(set_map_customer_confirm_policy({
                    type: TYPE_PROFILE.CUSTOMER,
                    infoCustomerCRM: []
                }))
                const customerInfo = customerProfile[0]
                const customerName = customerInfo.customerName || ContactName;
                batch(() => {
                    dispatch(
                        updateCustomerInfo({
                            Gender: getGender(customerInfo.gender) == null ? (Gender || 1) : getGender(customerInfo.gender),
                            ContactName: customerName,
                            ContactPhone: phoneNumber,
                            TempCartPhoneNumber: '',
                            TempCartContactPhone: phoneNumber
                        })
                    );
                    dispatch(set_map_customer_confirm_policy({
                        type: TYPE_PROFILE.CUSTOMER,
                        infoCustomerCRM: customerProfile
                    }))
                    setStatePolicy({
                        isSelectedPolicy: customerInfo.isSigned,
                        disabledPolicy: customerInfo.isSigned == 1 ? true : false
                    })
                });
            }).catch(() => {
                hideBlockUI();
            }).finally((error) => {
                console.log("lỗi profile", error);
            })

        }
    };

    const getOldCustomerInfo = (CustomerInfo) => {
        const { ContactPhone } = CustomerInfo;
        if (ContactPhone) {
            getCustomerInfo(ContactPhone, CustomerInfo);
        } else {
            storageHelper
                .getItem(STORAGE_CONST.CUSTOMER_INFO)
                .then((result) => {
                    if (helper.IsNonEmptyString(result)) {
                        const dataTopInfo = JSON.parse(result);
                        const customerInfoLocal = dataTopInfo.find((ele) =>
                            helper.IsEmptyString(ele.taxID)
                        );
                        if (customerInfoLocal) {
                            const {
                                customerPhone,
                                customerName,
                                customerAddress,
                                contactAddress,
                                contactName,
                                contactPhone,
                                gender
                            } = customerInfoLocal;
                            const newPhone = customerPhone || contactPhone
                            getCustomerInfo(newPhone, CustomerInfo);

                            // dispatch(
                            //     updateCustomerInfo({
                            //         Gender: gender,
                            //         ContactName: customerName || contactName,
                            //         ContactPhone: customerPhone || contactPhone,
                            //         // ContactAddress:
                            //         //     customerAddress || contactAddress,
                            //         TempCartContactPhone:
                            //             customerPhone || contactPhone
                            //     })
                            // );
                            dispatch(setTempPhoneNumber(customerPhone || contactPhone));
                        }
                    }
                })
                .catch((error) => {
                    console.log('getOldCustomerInfo error', error);
                });
        }
    };

    const handleAPIGetCustomerProfile = (phoneNumber) => {
        const baseBody = {
            "loginStoreId": storeID,
            "languageID": languageID,
            "moduleID": moduleID,
        }
        const regExpPhone = new RegExp(/^[0]\d{9}$/);
        const isValidate = regExpPhone.test(phoneNumber);
        if (isValidate) {
            checkAppLoyalty(phoneNumber);
            showBlockUI()
            if (customerConfirmPolicy?.[TYPE_PROFILE.CUSTOMER]?.[0]?.phoneNumber != phoneNumber) {
                dispatch(set_map_customer_confirm_policy({
                    type: TYPE_PROFILE.CUSTOMER,
                    infoCustomerCRM: []
                }))

            }
            getCustomerProfile({ ...baseBody, phoneNumber, typeProfile: TYPE_PROFILE.CUSTOMER }).then(customerProfile => {
                hideBlockUI()
                if (customerProfile == null) return dispatch(set_map_customer_confirm_policy({
                    type: TYPE_PROFILE.CUSTOMER,
                    infoCustomerCRM: []
                }))
                const customerInfo = customerProfile[0]

                batch(() => {
                    dispatch(
                        updateCustomerInfo({
                            Gender: getGender(customerInfo.gender) == null ? (Gender || 1) : getGender(customerInfo.gender),
                            ContactName: customerInfo.customerName,
                            ContactPhone: phoneNumber,
                            TempCartPhoneNumber: '',
                            TempCartContactPhone: phoneNumber
                        })
                    );
                    dispatch(set_map_customer_confirm_policy({
                        type: TYPE_PROFILE.CUSTOMER,
                        infoCustomerCRM: customerProfile
                    }))
                    setStatePolicy({
                        isSelectedPolicy: customerInfo.isSigned,
                        disabledPolicy: customerInfo.isSigned == 1 ? true : false
                    })
                });
            }).catch(() => {
                hideBlockUI();
            }).finally((error) => {
                console.log("lỗi profile", error);
            })


        }
    }

    const getContactInfo = (phoneNumber) => {
        const regExpPhone = new RegExp(/^[0]\d{9}$/);
        const isValidate = regExpPhone.test(phoneNumber);
        if (isValidate) {
            checkAppLoyalty(phoneNumber);
            getCustomerByPhone(phoneNumber).then((info) => {
                dispatch(
                    updateCustomerInfo({
                        Gender: info.gender,
                        ContactName: info.customerName,
                        ContactPhone: phoneNumber,
                        ContactAddress: info.customerAddress,
                        TempCartPhoneNumber: '',
                        TempCartContactPhone: phoneNumber
                    })
                );
            });
        }
    };

    const checkAppLoyalty = (phoneNumber) => {
        const { TotalPointLoyalty, IsAllowParticipationLoyalty } = data;
        if (IsAllowParticipationLoyalty && TotalPointLoyalty > 0) {
            checkInstalledQTV(phoneNumber).then((isExistApp) => {
                const message = isExistApp
                    ? 'Khách hàng đã cài app QTV.'
                    : 'Khách hàng chưa cài app QTV.';
                const type = isExistApp ? 'info' : 'warning';
                showMessage({ message, type, duration: 5000 });
            });
        }
    };
    const handleAPIGetCompanyProfile = (taxID) => {
        const baseBody = {
            "loginStoreId": storeID,
            "languageID": languageID,
            "moduleID": moduleID,
        }
        const regExpTax10 = new RegExp(/^\d{10}$/);
        const regExpTax14 = new RegExp(/^\d{10}[-]\d{3}$/);
        const isValidateTax10 = regExpTax10.test(taxID);
        const isValidateTax14 = regExpTax14.test(taxID);
        const isValidate = (isValidateTax10 || isValidateTax14);
        if (isValidate) {
            getCustomerProfile({ ...baseBody, phoneNumber: taxID, typeProfile: TYPE_PROFILE.COMPANY }).then(companyProfile => {
                if (companyProfile == null) return dispatch(set_map_customer_confirm_policy({
                    type: TYPE_PROFILE.COMPANY,
                    infoCustomerCRM: []
                }))
                const companyInfo = companyProfile[0]
                batch(() => {
                    dispatch(
                        updateCustomerInfo({
                            CustomerName: companyInfo.companyName,
                            CustomerAddress: companyInfo.address,
                            TaxID: taxID,
                            TempCartContactPhone: ''
                        })
                    );
                    dispatch(
                        set_map_customer_confirm_policy({
                            type: TYPE_PROFILE.COMPANY,
                            infoCustomerCRM: companyProfile
                        })
                    );
                });

            })
        }
    }
    const getCompanyInfo = (taxID) => {
        const regExpTax10 = new RegExp(/^\d{10}$/);
        const regExpTax14 = new RegExp(/^\d{10}[-]\d{3}$/);
        const isValidateTax10 = regExpTax10.test(taxID);
        const isValidateTax14 = regExpTax14.test(taxID);
        const isValidate = isValidateTax10 || isValidateTax14;
        if (isValidate) {
            getCompanyByTax(taxID).then((info) => {
                dispatch(
                    updateCustomerInfo({
                        Gender: info.gender,
                        CustomerPhone: info.customerPhone,
                        CustomerName: info.customerName,
                        CustomerAddress: info.customerAddress,
                        TaxID: taxID,
                        ContactName: '',
                        ContactPhone: '',
                        ContactAddress: '',
                        TempCartPhoneNumber: info.customerPhone,
                        TempCartContactPhone: ''
                    })
                );
            });
        } else {
            Toast.show({
                type: 'error',
                text1: translate(shoppingCart.validation_tax)
            });
        }
    };

    const {
        Gender,
        CustomerPhone,
        CustomerName,
        CustomerAddress,
        TaxID,
        ContactName,
        ContactPhone,
        ContactAddress,
        TempCartContactPhone,
        TempCartPhoneNumber
    } = customerInfo;

    const checkValidateCustomerInfo = () => {
        const regExpPhone = new RegExp(/^[0]\d{9}$/);
        const regExpPhone11 = new RegExp(/^[0]\d{10}$/);
        const isValidatePhone11 =
            isCompany && regExpPhone11.test(CustomerPhone);
        const isValidateCompanyPhone =
            regExpPhone.test(CustomerPhone) || isValidatePhone11;
        const isValidateCustomerPhone = regExpPhone.test(ContactPhone);
        const regExpTax10 = new RegExp(/^\d{10}$/);
        const regExpTax14 = new RegExp(/^\d{10}[-]\d{3}$/);
        const isValidateTax10 = regExpTax10.test(TaxID);
        const isValidateTax14 = regExpTax14.test(TaxID);
        const isValidateTax = isValidateTax10 || isValidateTax14;
        const msgWarningQuantity = checkMaxQuantity(data);
        if (isCompany) {
            if (!helper.IsNonEmptyString(TaxID)) {
                Toast.show({
                    type: 'error',
                    text1: translate(shoppingCart.please_enter_tax_code)
                });
                return false;
            }
            if (!isValidateTax) {
                Toast.show({
                    type: 'error',
                    text1: translate(shoppingCart.validation_tax)
                });
                return false;
            }
            if (!helper.IsNonEmptyString(CustomerName)) {
                Toast.show({
                    type: 'error',
                    text1: translate(shoppingCart.validation_company_name)
                });
                return false;
            }
            if (!helper.IsNonEmptyString(CustomerAddress)) {
                Toast.show({
                    type: 'error',
                    text1: translate(shoppingCart.validation_company_address)
                });
                return false;
            }
            if (!statePolicy.isSelectedPolicy && helper.IsNonEmptyString(CustomerPhone)) {
                Toast.show({
                    type: 'error',
                    text1: 'Khách hàng vui lòng đồng ý với chính sách xử lý dữ liệu cá nhân.'
                });
                return false;
            }
        }
        if (helper.IsNonEmptyString(CustomerPhone)) {
            // if (!isValidateCompanyPhone) {
            //     Toast.show({
            //         type: 'error',
            //         text1: translate(shoppingCart.validation_phone_number)
            //     });
            //     return false;
            // }
            // if (!helper.isValidatePhonePrefix(CustomerPhone)) {
            //     Toast.show({
            //         type: 'error',
            //         text1: translate(shoppingCart.validation_phone_number_1)
            //     });
            //     return false;
            // }
        }
        if (helper.IsNonEmptyString(ContactPhone)) {
            if (!isValidateCustomerPhone) {
                Toast.show({
                    type: 'error',
                    text1: translate(shoppingCart.validation_phone_number)
                });
                return false;
            }
            if (!helper.isValidatePhonePrefix(ContactPhone)) {
                Toast.show({
                    type: 'error',
                    text1: translate(shoppingCart.validation_phone_number_1)
                });
                return false;
            }
        }
        if (isRequireCustomerInfo) {
            const validateInfo = {
                name: isCompany ? CustomerName : ContactName,
                phone: ContactPhone,
                address: CustomerAddress
            };
            const message = customerInfoValidation(validateInfo);

            if (message) {
                Toast.show({
                    type: 'error',
                    text1: message
                });
                pageViewerRef.current.setPage(1);
                return false;
            }
        }
        if (msgWarningQuantity) {
            Alert.alert('', msgWarningQuantity);
            return false;
        }
        if (isAva) {
            if (!helper.IsNonEmptyString(ContactName)) {
                const text = isCompany ? "Vui lòng nhập họ tên người mua." : translate(shoppingCart.validation_customer_name)
                Toast.show({
                    type: 'error',
                    text1: text
                });
                pageViewerRef.current.setPage(1);
                return false;
            }
        }
        if (ENUM.BRAND_ID.AN_KHANG == brandID) {
            if (helper.IsNonEmptyString(ContactPhone) && !helper.IsNonEmptyString(ContactName)) {
                Toast.show({
                    type: 'error',
                    text1: translate(shoppingCart.validation_customer_name)
                });
                return false;
            }
        }
        if (
            !statePolicy.isSelectedPolicy &&
            helper.IsNonEmptyString(ContactPhone) &&
            !isCompany &&
            typeDelivery == 1
        ) {
            Toast.show({
                type: 'error',
                text1: 'Khách hàng vui lòng đồng ý với chính sách xử lý dữ liệu cá nhân.'
            });
            return false;
        }
        // if (Gender == null) {
        //     Alert.alert("", translate('shoppingCart.validation_gender'));
        //     return false;
        // }
        if (checkDataInvalid(promoRequireInfors)) {
            Alert.alert(
                translate('common.notification_uppercase'),
                `Vui lòng chụp hình ảnh bổ sung`,
                [

                    {
                        text: "OK",
                        style: 'cancel',
                        onPress: () => navigation.navigate("FormPromotion", { promoRequireInfors: promoRequireInfors, dataCart: data, onChange: () => { setIsRefetch(!isRefetch) } })

                    }
                ]
            );
            return false;

        }
        return true;
    };
    const handleCreateSaleOrder = async () => {
        /// vì giao hàng ava đã gom chung 1 màn hình thong tin khách hàng nên check thêm
        const isCompanyDeliveryHome =
            typeDelivery === TYPE_DELIVERY.HOME && customerInfo.IsCompany;
        if (checkValidateCustomerInfo()) {
            const CustomerInfo =
                isCompany || isCompanyDeliveryHome
                    ? {
                        CustomerName,
                        CustomerAddress,
                        CustomerPhone: isCompanyDeliveryHome ? CustomerPhone : ContactPhone || TempCartPhoneNumber,
                        TaxID,
                        Gender: 1,
                        ContactName,
                        ContactPhone: ContactPhone || TempCartContactPhone,
                        DeliveryAddress: ContactAddress,
                        ContactGender: Gender ? 1 : 0,
                        AgeID: '',
                        Birthday: ''
                    }
                    : {
                        CustomerName: ContactName?.trim() || 'Khách lẻ',
                        CustomerAddress: ContactAddress || 'x',
                        CustomerPhone: ContactPhone || TempCartContactPhone,
                        TaxID: '',
                        Gender: Gender ? 1 : 0,
                        ContactName: '',
                        ContactPhone: '',
                        DeliveryAddress: '',
                        ContactGender: Gender ? 1 : 0,
                        AgeID: '',
                        Birthday: ''
                    };

            const UrlFiles = urlFiles.map((item) => ({
                AttachmentID: 0,
                FileTypeID: 4,
                UrlFile: item.url
            }));

            let dosageBOList = null;
            if (dosages && dosages.length > 0) {
                dosageBOList = dosages.map((dosage) => ({
                    ...dosage,
                    cus_ProductName: dosage.ProductName,
                    SaleOrderDoSageID: null,
                    SaleOrderID: null,
                    CreatedUser: null,
                    UpdateUser: null
                }));
            }
            const cartRequest = {
                ...data,
                CustomerInfo,
                SaleOrderRelationShip: {
                    RelationShipType: -1
                },
                cus_UrlFilesShoppingCart: UrlFiles,
                cus_SaleOrderDoSageBOList: dosageBOList
            };
            if (helper.IsNonEmptyString(ContactPhone)) {
                const dataMedicine = await handleAPICheckMedicine(cartRequest)
                cartRequest.CheckNumberOfSlot = dataMedicine?.Data ?? false
                cartRequest.BussineesTypeOfSlot = dataMedicine?.BussineesTypeResult ?? {}
            }
            const profile = await handleAPIProfile(cartRequest)
            if (!helper.IsEmptyObject(profile)) {
                ///////
                if (!helper.IsNonEmptyArray(profile[TYPE_PROFILE.CUSTOMER])) {
                    delete profile?.[TYPE_PROFILE.CUSTOMER]
                }
                if (!helper.IsNonEmptyArray(profile[TYPE_PROFILE.COMPANY])) {
                    delete profile?.[TYPE_PROFILE.COMPANY]
                }
                if (!helper.IsNonEmptyArray(profile[TYPE_PROFILE.ADDRESS_RECEIVE])) {
                    delete profile?.[TYPE_PROFILE.ADDRESS_RECEIVE]
                }
                if (!helper.IsNonEmptyArray(profile[TYPE_PROFILE.CUSTOMER_RECEIVE])) {
                    delete profile?.[TYPE_PROFILE.CUSTOMER_RECEIVE]
                }
                cartRequest.SaleOrderDetails.forEach(saleOrderDetail => {
                    let newProfile = { ...profile }
                    if (helper.IsNonEmptyArray(newProfile[TYPE_PROFILE.ADDRESS_RECEIVE])) {
                        newProfile = { ...newProfile, [TYPE_PROFILE.ADDRESS_RECEIVE]: newProfile[TYPE_PROFILE.ADDRESS_RECEIVE].filter((item) => item.soProfileId == saleOrderDetail.SaleOrderDetailID) }
                    }
                    if (helper.IsNonEmptyArray(newProfile[TYPE_PROFILE.CUSTOMER_RECEIVE])) {
                        newProfile = { ...newProfile, [TYPE_PROFILE.CUSTOMER_RECEIVE]: newProfile[TYPE_PROFILE.CUSTOMER_RECEIVE].filter((item) => item.soProfileId == saleOrderDetail.SaleOrderDetailID) }
                    }
                    saleOrderDetail.Profile = newProfile;
                })
            }
            if (!helper.IsEmptyObject(dataVerifyInfo)) {
                if (ContactPhone == dataVerifyInfo.customerPhone) {
                    cartRequest.RequestIDLoyalty = dataVerifyInfo.requestId;
                    cartRequest.CustomerIDLoyalty = dataVerifyInfo.customerId;
                    cartRequest.cus_LoyaltyCustomerPhone = ContactPhone;
                }
            }
            if (cartRequest.BussineesTypeOfSlot?.CUSTOMERFIRST) {
                setTempDataCart(cartRequest)
                OTPSheetRef.current?.present()
            }
            else {
                handleSaleOrderFlow(cartRequest)
            }

        }
    };

    const handleSaleOrderFlow = (cartRequest) => {
        const isLoyalty =
            cartRequest.IsAllowParticipationLoyalty &&
            !helper.IsNonEmptyString(cartRequest.CustomerIDLoyalty);
        if (isLoyalty) {
            onCheckLoyaltyPoint(cartRequest);
        } else {
            const body = {
                moduleID,
                languageID,
                loginStoreId: storeID,
                cartRequest
            };
            addToSaleOrderCart(body);
        }
    }

    const handleAPIProfile = async (dataSaleOrder) => {
        showBlockUI()
        let profileAddressAndRecieve = {}
        let profileCustomer = {}
        let profileCompany = {}
        let extraCustomerConfirmPolicy = {}
        let profileModify = {}
        let isInsertAddress = false
        let newPhoneNumber = ContactPhone
        // người mua là người nhận
        let isSameCustomer = true
        const deliveryAtHome = dataSaleOrder.SaleOrderDetails.findIndex(saleorder => {
            const { DeliveryInfoRequest: {
                DeliveryTypeID } } = saleorder;
            return (DeliveryTypeID != 1);
        });
        if ((deliveryAtHome !== -1) && helper.IsNonEmptyString(newPhoneNumber)) {
            dataSaleOrder.SaleOrderDetails.forEach(({ Profile, SaleOrderDetailID }) => {
                if (!helper.IsEmptyObject(Profile)) {
                    Object.entries(Profile).forEach(([key, value]) => {
                        if (`${TYPE_PROFILE.CUSTOMER_RECEIVE},${TYPE_PROFILE.ADDRESS_RECEIVE}`.includes(`${key}`)) {
                            for (let index = 0; index < value.length; index++) {
                                isInsertAddress = value[index].profileId == null || value[index].profileId != customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]?.[0]?.profileId;
                                value[index].soProfileId = SaleOrderDetailID;
                                value[index].profileId = customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]?.[0]?.profileId;
                                extraCustomerConfirmPolicy = { ...extraCustomerConfirmPolicy, [key]: [...extraCustomerConfirmPolicy?.[key] ?? [], value[index]] };
                            }
                            if (key == TYPE_PROFILE.CUSTOMER_RECEIVE) {
                                isSameCustomer = false
                            }
                        }

                    });
                }

            })
            /// Người mua là người nhận phải đổi thông tin của contact 
            if (isSameCustomer && !helper.IsEmptyObject(customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]?.[0])) {
                const { customerName, phoneNumber, gender } = customerConfirmPolicy[TYPE_PROFILE.CUSTOMER][0]
                dataSaleOrder.SaleOrderDetails.forEach(saleOrderDetail => {
                    saleOrderDetail.DeliveryInfoRequest = {
                        ...saleOrderDetail.DeliveryInfoRequest,
                        ContactGender: gender,
                        ContactName: customerName,
                        ContactPhone: phoneNumber
                    }
                })
            }

        }
        let newProfile = { ...customerConfirmPolicy, ...extraCustomerConfirmPolicy }
        try {
            // if (helper.IsNonEmptyArray(customerConfirmPolicy[TYPE_PROFILE.CUSTOMER])) {
            const defaultCustomerInfo = {
                "customerName": "",
                "cardCustomerId": null,
                "gender": null,
                "profileId": 0,
                "type": 1,
                "versionCode": "",
                "phoneNumber": "",
                "isModify": 0,
                "isSigned": 0,
                "signatureId": 0,
                "soProfileId": null,
                "relationshipTypeId": 0,
                "relationshipId": 0
            }
            const companyDefault = {
                "companyId": 0,
                "companyName": "",
                "companyPhone": null,
                "address": "",
                "email": null,
                "taxNo": "",
                "profileId": 0,
                "type": 5,
                "versionCode": "",
                "phoneNumber": null,
                "isModify": 0,
                "isSigned": 0,
                "signatureId": 0,
                "soProfileId": null,
                "relationshipTypeId": 0,
                "relationshipId": 0
            }
            const customerInfo = helper.IsNonEmptyArray(customerConfirmPolicy[TYPE_PROFILE.CUSTOMER]) ? { ...customerConfirmPolicy[TYPE_PROFILE.CUSTOMER][0] } : defaultCustomerInfo
            const oldInfoCustomer = {
                "customerName": customerInfo.customerName,
                "customerPhone": customerInfo.phoneNumber,
                "gender": customerInfo.gender,
            }
            const infoCustomer = {
                "customerName": ContactName,
                "customerPhone": ContactPhone,
                "gender": getGender(Gender),
            }
            const infoSignCustomer = infoCustomer
            const hasChangeValueProfile = helper.checkChangeValueOfPrototype(infoSignCustomer, oldInfoCustomer)
            if ((hasChangeValueProfile || !customerInfo.isSigned) && !!ContactPhone) {
                customerInfo.isModify = 1
                if (customerInfo.phoneNumber != newPhoneNumber) {
                    customerInfo.versionCode = "";
                    customerInfo.profileId = 0;
                }
                customerInfo.customerName = ContactName
                customerInfo.phoneNumber = ContactPhone
                customerInfo.gender = getGender(Gender)
                profileCustomer = { [TYPE_PROFILE.CUSTOMER]: [customerInfo] }
            }
            if (helper.IsEmptyString(ContactPhone)) {
                delete newProfile?.[TYPE_PROFILE.CUSTOMER]
            }
            // }
            if (isCompany) {
                const companyInfo = helper.IsNonEmptyArray(customerConfirmPolicy[TYPE_PROFILE.COMPANY]) ? { ...customerConfirmPolicy[TYPE_PROFILE.COMPANY][0] } : companyDefault
                const { companyName, companyPhone, taxNo, address } = companyInfo
                if (companyName != CustomerName || address != CustomerAddress || taxNo != TaxID) {
                    companyInfo.isModify = 1
                    companyInfo.companyName = CustomerName
                    companyInfo.address = CustomerAddress
                    companyInfo.taxNo = TaxID
                    profileCompany = { [TYPE_PROFILE.COMPANY]: [companyInfo] }
                }
            }
            // insert địa chỉ củ của giao về nhà cho khách hàng mới nếu như thay đổi số điện thoại
            if ((deliveryAtHome !== -1) && helper.IsNonEmptyString(newPhoneNumber)) {
                dataSaleOrder.SaleOrderDetails.forEach(({ Profile, SaleOrderDetailID }) => {
                    if (!helper.IsEmptyObject(Profile)) {
                        Object.entries(Profile).forEach(([key, value]) => {
                            if (`${TYPE_PROFILE.CUSTOMER_RECEIVE},${TYPE_PROFILE.ADDRESS_RECEIVE}`.includes(`${key}`)) {
                                for (let index = 0; index < value.length; index++) {
                                    if (isInsertAddress) {
                                        if (key == TYPE_PROFILE.ADDRESS_RECEIVE) {
                                            value[index].profileId = customerInfo.profileId;
                                            value[index].deliveryId = 0;
                                            value[index].isModify = 0;
                                        }
                                        if (key == TYPE_PROFILE.CUSTOMER_RECEIVE) {
                                            value[index].profileId = customerInfo.profileId;
                                            value[index].receiverId = 0;
                                            value[index].isModify = 0;
                                        }
                                        value[index].soProfileId = SaleOrderDetailID
                                        profileAddressAndRecieve = { ...profileAddressAndRecieve, [key]: [...profileAddressAndRecieve?.[key] ?? [], value[index]] };
                                    }
                                }
                            }

                        });
                    }
                })
            }
            const profileRequest = {
                ...profileAddressAndRecieve, ...profileCompany, ...profileCustomer
            }
            if (!helper.IsEmptyObject(profileRequest)) {
                const body = {
                    "loginStoreId": storeID,
                    "languageID": languageID,
                    "moduleID": moduleID,
                    "profile": profileRequest
                }
                profileModify = await modifyCustomerProfile(body)
            }
            newProfile[TYPE_PROFILE.CUSTOMER] = profileModify[TYPE_PROFILE.CUSTOMER] || newProfile[TYPE_PROFILE.CUSTOMER]?.filter((_, index) => (index == 0)) || []
            newProfile[TYPE_PROFILE.COMPANY] = profileModify[TYPE_PROFILE.COMPANY] || newProfile[TYPE_PROFILE.COMPANY]?.filter((_, index) => (index == 0)) || []
            newProfile[TYPE_PROFILE.ADDRESS_RECEIVE] = profileModify[TYPE_PROFILE.ADDRESS_RECEIVE] || newProfile[TYPE_PROFILE.ADDRESS_RECEIVE] || []
            newProfile[TYPE_PROFILE.CUSTOMER_RECEIVE] = profileModify[TYPE_PROFILE.CUSTOMER_RECEIVE] || newProfile[TYPE_PROFILE.CUSTOMER_RECEIVE] || []
            return newProfile
        } catch (error) {
            dataSaleOrder.SaleOrderDetails.forEach(element => {
                element.Profile = {}
            });
            console.log("🚀 ~ ShoppingCart ~ handleAPIProfile= ~ error:", error)
            return {}
        }
        finally {
            hideBlockUI()

        }

    }

    const onCheckLoyaltyPoint = (dataSaleOrder) => {
        const {
            TotalPointLoyalty,
            CustomerInfo: { CustomerPhone }
        } = dataSaleOrder;
        const isValidatePhone = helper.IsNonEmptyString(CustomerPhone);
        const isValidatePoint = TotalPointLoyalty > 0;
        const isMoveToLoyalty = isValidatePoint && isValidatePhone;
        if (isMoveToLoyalty) {
            getDataLoyalty(CustomerPhone, dataSaleOrder);
        } else {
            const body = {
                moduleID,
                languageID,
                loginStoreId: storeID,
                cartRequest: dataSaleOrder
            };
            addToSaleOrderCart(body);
        }
    };

    const getDataLoyalty = (phone, dataSaleOrder) => {
        dispatch(checkCredentialExist(phone, dataSaleOrder)).then(() => {
            navigation.navigate(SCREENS.Loyalty);
        });
    };

    const addToSaleOrderCart = async (body) => {
        try {
            if (!helper.IsEmptyObject(electricalPrescriptionBO)) {
                body.cartRequest.ElectricalPrescriptionBO = electricalPrescriptionBO;
            }

            showBlockUI();
            const response = await createSaleOrder(body);
            const { cartRequest } = body;
            const { CustomerInfo, CustomerIDLoyalty } = cartRequest;

            if (response.SaleOrders?.length === 1 && checkApplyVoucher(cartRequest, brandID, storeID)) {
                return await handleAPICheckApplyVoucher(response, cartRequest);
            }

            finalizeSaleOrder(response, CustomerInfo, CustomerIDLoyalty);
        } catch (error) {
            hideBlockUI();
            Alert.alert('', error);
        }
    };

    const handleAPICheckApplyVoucher = async (dataSaleOrder, cartRequest) => {
        try {
            if (applyTimes.current > 2) {
                return finalizeSaleOrder(dataSaleOrder, cartRequest.CustomerInfo, cartRequest.CustomerIDLoyalty);
            }

            applyTimes.current += 1;

            const bodyRequest = {
                loginStoreId: storeID,
                languageID,
                moduleID,
                saleScenarioTypeID: 8,
                saleOrderId: dataSaleOrder.SaleOrders?.[0]?.SaleOrderID,
            };

            await checkApplyGiftVoucher(bodyRequest);
            finalizeSaleOrder(dataSaleOrder, cartRequest.CustomerInfo, cartRequest.CustomerIDLoyalty);
        } catch (error) {
            // Chờ 3 giây trước khi retry
            await new Promise(resolve => setTimeout(resolve, 3000));
            await handleAPICheckApplyVoucher(dataSaleOrder, cartRequest);
        }
    };

    const finalizeSaleOrder = (response, CustomerInfo, CustomerIDLoyalty) => {
        handleSaleOrder({ response, info: { CustomerInfo, CustomerIDLoyalty } });

        if (!helper.IsEmptyObject(electricalPrescriptionBO)) {
            dispatch(reset_map_prescriptions());
        }
        dispatch(reset_map_customer_confirm_policy());
    };




    const handleDeleteCart = () => {
        Alert.alert('', translate(saleExpress.confirm_clear_cart), [
            {
                text: translate(common.btn_skip)
            },
            {
                text: translate(common.btn_continue),
                onPress: () => {
                    dispatch(resetCartInfo());
                    navigation.dispatch(
                        CommonActions.reset({
                            index: 0,
                            routes: [{ name: SCREENS.Pharmacy }]
                        })
                    );
                }
            }
        ]);
    };

    const handleFinishScanLoyalty = (info, customerProfile) => {
        setDataVerifyInfo(info);
        if (customerProfile == null) return dispatch(set_map_customer_confirm_policy({
            type: TYPE_PROFILE.CUSTOMER,
            infoCustomerCRM: []
        }))
        const customerInfo = customerProfile[0]
        batch(() => {
            dispatch(
                updateCustomerInfo({
                    Gender: getGender(customerInfo.gender) == null ? (Gender || 1) : getGender(customerInfo.gender),
                    ContactName: customerInfo.customerName,
                    ContactPhone: info.customerPhone,
                    TempCartPhoneNumber: '',
                    TempCartContactPhone: info.customerPhone
                })
            );
            dispatch(set_map_customer_confirm_policy({
                type: TYPE_PROFILE.CUSTOMER,
                infoCustomerCRM: customerProfile
            }))
            setStatePolicy({
                isSelectedPolicy: customerInfo.isSigned,
                disabledPolicy: customerInfo.isSigned == 1 ? true : false
            })
        });
        // dispatch(
        //     updateCustomerInfo({
        //         Gender: info.gender,
        //         ContactName: info.customerName,
        //         ContactPhone: info.customerPhone,
        //         CustomerPhone: info.customerPhone,
        //         CustomerAddress: info.customerAddress,
        //         ContactAddress: info.customerAddress,
        //         TempCartContactPhone: info.customerPhone,
        //         TempCartPhoneNumber: info.customerPhone
        //     })
        // );
    };

    const hasPromotionProfit = !helper.IsEmptyObject(
        data.cus_PromDiscountExtraMaster
    );

    const showBlockUI = () => {
        setLoadingBlock(true);
    };
    const hideBlockUI = () => {
        setLoadingBlock(false);
    };

    const handleAPICheckMedicine = async (cartRequest) => {
        const body = {
            "loginStoreId": storeID,
            "languageID": languageID,
            "moduleID": moduleID,
            "saleScenarioTypeID": 8,
            "customerPhone": customerInfo.ContactPhone,
            "bussineesTypes": [
                "CUSTOMERPHONEOFMONTH",
                "CUSTOMERFIRST"
            ],
            "cartRequest": cartRequest
        }
        showBlockUI();
        const dataMedicine = await checkNumberPhoneApplyMedicine(body)
        return dataMedicine;
    };

    const isVisibleVoucherCustomer = vouchers?.length > 0 && voucherCustomerPhone.current == ContactPhone && !isPresented
    const promoRequireInfors = getPromoRequireInfors(data.SaleOrderDetails)

    // Render here
    return (
        <SafeAreaView style={styles.mainContainer}>
            <BottomSheetModalProvider>
                <KeyboardAvoidingView
                    style={styles.mainContainer}
                    behavior={Platform.OS === 'ios' ? 'padding' : null}
                    keyboardVerticalOffset={Platform.OS === 'ios' ? 96 : 0}>
                    <AnimatedPagerView
                        ref={pageViewerRef}
                        style={{
                            flex: 1,
                            flexDirection: 'column-reverse'
                        }}
                        bounces={false}
                        indicator={renderPagerTitleIndicator({
                            hasPromotionProfit
                        })}
                        keyboardShouldPersistTaps="always"
                        horizontalScroll={false}>
                        {/* Giỏ hàng */}
                        <View style={styles.pageContainer} key="CartInfo">
                            <View style={styles.bar}>
                                <View style={styles.cartShadow}>
                                    <CartCount
                                        rowCount={products.length}
                                        quantity={totalQuantity}
                                    />
                                    {helper.IsNonEmptyArray(promoRequireInfors) && <View style={{
                                        justifyContent: "center",
                                        alignItems: "center",
                                    }}>

                                        <Button
                                            text={"Chụp hình bổ sung"}
                                            onPress={() => {
                                                navigation.navigate("FormPromotion", { promoRequireInfors: promoRequireInfors, dataCart: data, onChange: () => { setIsRefetch(!isRefetch) } });
                                            }}
                                            styleContainer={{
                                                flexDirection: 'row',
                                                height: 40,
                                                borderTopWidth: StyleSheet.hairlineWidth,
                                                borderTopColor: COLORS.bdFFFFFF,
                                                justifyContent: 'space-between',
                                            }}
                                            styleText={{
                                                color: COLOR.primary,
                                                fontSize: 16,
                                                marginRight: 8,
                                                fontWeight: "bold",
                                            }}
                                            iconRight={{
                                                iconSet: "FontAwesome",
                                                name: "chevron-right",
                                                size: 16,
                                                color: COLOR.primary
                                            }}
                                        />
                                    </View>}
                                </View>
                            </View>
                            <View style={{ flex: 1 }}>
                                <View
                                    style={{
                                        maxHeight: shouldSetHeight ? '80%' : '100%'
                                    }}>
                                    <CartList data={products} />
                                </View>
                                {shouldSetHeight && (
                                    <ScrollView
                                        style={{
                                            flexGrow: 0,
                                            borderTopColor: COLOR.border,
                                            borderTopWidth: 1,
                                            marginHorizontal: 10
                                        }}>
                                        {hasSaleOrderPromos && (
                                            <View style={styles.giftContainer}>
                                                <Icon
                                                    iconSet="MaterialCommunityIcons"
                                                    name="gift"
                                                    color={COLOR.primary}
                                                    size={24}
                                                    style={styles.giftIcon}
                                                />
                                                <View style={styles.gift}>
                                                    {discountPromos.map(
                                                        (discount) => {
                                                            const {
                                                                IsPercentDiscount,
                                                                DiscountValue: value
                                                            } = discount;
                                                            const textDiscount =
                                                                translate(
                                                                    common.discount
                                                                );
                                                            // Giảm: 0%
                                                            const textPercent = `${textDiscount}${value}%`;
                                                            const textValue = `${textDiscount}${helper.convertNum(
                                                                value
                                                            )}`;
                                                            return (
                                                                <MyText
                                                                    selectable={
                                                                        false
                                                                    }
                                                                    text={
                                                                        IsPercentDiscount
                                                                            ? textPercent
                                                                            : textValue
                                                                    }
                                                                />
                                                            );
                                                        }
                                                    )}
                                                    {productPromos.map(
                                                        (giftPromotion) => (
                                                            <MyText
                                                                key={
                                                                    giftPromotion.PromotionID
                                                                }
                                                                selectable={false}
                                                                // TODO: translate
                                                                text={`${giftPromotion.ProductName} (SL: ${giftPromotion.Quantity}) `}
                                                            />
                                                        )
                                                    )}
                                                </View>
                                            </View>
                                        )}

                                        {!!textDeliveryAddress && isAva && (
                                            <View style={styles.giftContainer}>
                                                <Icon
                                                    iconSet="MaterialCommunityIcons"
                                                    name="map-marker-radius"
                                                    color={COLOR.PRIMARY_500}
                                                    size={18}
                                                    style={style.giftIcon}
                                                />
                                                <MyText
                                                    style={[
                                                        styles.gift,
                                                        { fontSize: 12 }
                                                    ]}
                                                    text="Hàng giao đến địa chỉ:">
                                                    <MyText
                                                        style={{
                                                            fontSize: 12,
                                                            fontWeight: '700',
                                                            color: COLOR.lightBlack
                                                        }}
                                                        text={textDeliveryAddress}
                                                    />
                                                </MyText>
                                            </View>
                                        )}
                                    </ScrollView>
                                )}
                            </View>
                            <View style={styles.summaryContainer}>
                                <View style={styles.detailPriceContainer}>
                                    {hasPromotionProfit && (
                                        <PriceText
                                            onPress={() => {
                                                const profitIndex =
                                                    pageViewerRef.current.props
                                                        .indicator.props.titles
                                                        .length - 1;
                                                pageViewerRef.current.setPage(
                                                    profitIndex
                                                );
                                            }}
                                            style={{
                                                ...styles.priceText,
                                                borderBottomColor: COLOR.border,
                                                borderBottomWidth: 1,
                                                paddingBottom: 5
                                            }}
                                            fontSize={16}
                                            label={translate(
                                                saleExpress.extra_discount_amount
                                            )}
                                            value={
                                                data.cus_PromDiscountExtraMaster
                                                    .TotalDiscountMoneyVAT
                                            }
                                            subText={translate(
                                                saleExpress.title_promotion_profit
                                            )}
                                        />
                                    )}
                                    {typeDelivery !== TYPE_DELIVERY.STORE && (
                                        <PriceText
                                            style={styles.priceText}
                                            fontSize={16}
                                            // TODO: common
                                            label="Phí giao"
                                            value={totalCost.ShippingCost}
                                        />
                                    )}
                                    <PriceText
                                        style={styles.priceText}
                                        fontSize={16}
                                        // TODO: common
                                        label={translate(saleExpress.total_amount)}
                                        value={totalCost.TotalAmount}
                                    />
                                    <PriceText
                                        style={styles.priceText}
                                        fontSize={16}
                                        label={translate(
                                            saleExpress.discount_by_coupon
                                        )}
                                        value={totalCost.TotalDiscountByCoupon}
                                    />
                                    <PriceText
                                        style={styles.priceText}
                                        fontSize={16}
                                        label={translate(
                                            saleExpress.discount_by_cart_promotion
                                        )}
                                        value={
                                            totalCost.TotalDiscountByPromotionOrder
                                        }
                                    />
                                    <PriceText
                                        fontSize={16}
                                        style={styles.totalPriceCart}
                                        label={translate(saleExpress.total_paid)}
                                        value={totalCost.TotalPaid}
                                        valueColor={COLOR.red}
                                    />
                                    {data.TotalPointLoyalty > 0 && (
                                        <PriceText
                                            style={[
                                                styles.totalPriceCart,
                                                { borderTopWidth: 0 }
                                            ]}
                                            fontSize={16}
                                            label="Điểm tích luỹ KHTT"
                                            value={data.TotalPointLoyalty}
                                            valueColor="#FF00BF"
                                        />
                                    )}
                                </View>
                            </View>
                        </View>
                        {/* Khách hàng */}
                        <View
                            key="CustomerInfo"
                            style={[
                                styles.pageContainer,
                                { paddingVertical: 10, paddingHorizontal: 20 }
                            ]}>
                            {typeDelivery !== TYPE_DELIVERY.HOME && (
                                <ScrollView
                                    bounces={false}
                                    keyboardShouldPersistTaps="handled">
                                    <CheckBox
                                        style={{ marginVertical: 10 }}
                                        isCheck={isCompany}
                                        label={translate(
                                            shoppingCart.customer_print_company_bill
                                        )}
                                        onCheck={() => {
                                            setIsCompany((prev) => !prev);
                                            if (!isDisableCustomerPhone) {
                                                dispatch(resetCustomerInfo());
                                            } else {
                                                dispatch(
                                                    updateCustomerInfo({
                                                        ...pharmacyState.customerInfo,
                                                        CustomerPhone,
                                                        ContactPhone: CustomerPhone
                                                    })
                                                );
                                                setIsUpdate((prev) => prev + 1);
                                            }
                                        }}
                                    />
                                    {isCompany && (
                                        <View>
                                            <InputField
                                                isRequired
                                                label={translate(
                                                    saleExpress.tax_id
                                                )}
                                                placeholder={translate(
                                                    saleExpress.placeholder_tax_id
                                                )}
                                                keyboardType={
                                                    DEVICE.isIOS
                                                        ? 'numbers-and-punctuation'
                                                        : 'visible-password'
                                                }
                                                value={TaxID}
                                                onChangeText={(text) => {
                                                    const regExpTax = new RegExp(
                                                        /^[0-9-KL]{0,14}$/
                                                    );
                                                    const isValidate =
                                                        regExpTax.test(text) ||
                                                        text === '';
                                                    if (isValidate) {
                                                        dispatch(
                                                            updateCustomerInfo({
                                                                TaxID: text
                                                            })
                                                        );
                                                    }
                                                }}
                                                onClear={() => {
                                                    dispatch(
                                                        updateCustomerInfo({
                                                            TaxID: ''
                                                        })
                                                    );
                                                }}
                                                onBlur={() => {
                                                    if (TaxID.length > 0) {
                                                        handleAPIGetCompanyProfile(TaxID);
                                                    }
                                                }}
                                            />
                                            <InputField
                                                isRequired
                                                label={translate(
                                                    saleExpress.company_name
                                                )}
                                                placeholder={translate(
                                                    shoppingCart.placeholder_customer_company_name
                                                )}
                                                value={CustomerName}
                                                onChangeText={(text) => {
                                                    if (
                                                        helper.isValidateCharVN(
                                                            text
                                                        )
                                                    ) {
                                                        dispatch(
                                                            updateCustomerInfo({
                                                                CustomerName: text
                                                            })
                                                        );
                                                    }
                                                }}
                                                onClear={() => {
                                                    dispatch(
                                                        updateCustomerInfo({
                                                            CustomerName: ''
                                                        })
                                                    );
                                                }}
                                            />
                                            <InputField
                                                isRequired
                                                label={translate(
                                                    saleExpress.company_address
                                                )}
                                                placeholder={translate(
                                                    saleExpress.placeholder_company_address
                                                )}
                                                value={CustomerAddress}
                                                onChangeText={(text) => {
                                                    if (
                                                        helper.isValidateCharVN(
                                                            text
                                                        )
                                                    ) {
                                                        dispatch(
                                                            updateCustomerInfo({
                                                                CustomerAddress:
                                                                    text
                                                            })
                                                        );
                                                    }
                                                }}
                                                onClear={() => {
                                                    dispatch(
                                                        updateCustomerInfo({
                                                            CustomerAddress: ''
                                                        })
                                                    );
                                                }}
                                            />
                                            {/* <InputField
                                            label={translate(
                                                saleExpress.company_phone
                                            )}
                                            placeholder={translate(
                                                saleExpress.placeholder_company_phone
                                            )}
                                            value={
                                                CustomerPhone ||
                                                TempCartPhoneNumber
                                            }
                                            onChangeText={(text) => {
                                                const regExpPhone = new RegExp(
                                                    /^[0]\d{0,10}$/
                                                );
                                                const isValidate =
                                                    regExpPhone.test(text) ||
                                                    text === '';
                                                if (isValidate) {
                                                    dispatch(
                                                        updateCustomerInfo({
                                                            CustomerPhone: text,
                                                            ContactPhone: text,
                                                            TempCartContactPhone:
                                                                text,
                                                            TempCartPhoneNumber:
                                                                text
                                                        })
                                                    );
                                                }
                                            }}
                                            onClear={() => {
                                                dispatch(
                                                    updateCustomerInfo({
                                                        CustomerPhone: '',
                                                        ContactPhone: '',
                                                        TempCartContactPhone:
                                                            '',
                                                        TempCartPhoneNumber: ''
                                                    })
                                                );
                                            }}
                                            keyboardType="numeric"
                                            editable={!isDisableCustomerPhone}
                                        /> */}
                                        </View>
                                    )}
                                    <View style={styles.rowRadio}>
                                        <RadioItem
                                            isCheck={Gender}
                                            disabled={Gender == 1}
                                            style={{ marginRight: 40 }}
                                            title={translate(saleExpress.mr)}
                                            onPressItem={() =>
                                                handleSwitchGender(true)
                                            }
                                        />
                                        <RadioItem
                                            isCheck={!Gender}
                                            title={translate(saleExpress.ms)}
                                            disabled={Gender == 0}
                                            onPressItem={() =>
                                                handleSwitchGender(false)
                                            }
                                        />
                                        <TouchableOpacity
                                            style={{
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                                marginLeft: 'auto'
                                            }}
                                            onPress={() =>
                                                getOldCustomerInfo(customerInfo)
                                            }>
                                            <MyText
                                                text={translate(
                                                    shoppingCart.old_customer
                                                )}
                                                style={{
                                                    color: COLOR.primary,
                                                    textDecorationLine: 'underline',
                                                    fontWeight: 'bold'
                                                }}
                                            />
                                        </TouchableOpacity>
                                    </View>
                                    <InputField
                                        isRequired={isRequireCustomerInfo}
                                        keyboardType="numeric"
                                        label={
                                            isCompany
                                                ? "Số điện thoại người mua:"
                                                : translate(saleExpress.phone)
                                        }
                                        placeholder={translate(
                                            saleExpress.placeholder_phone
                                        )}
                                        value={ContactPhone || TempCartContactPhone}
                                        onBlur={() => {
                                            // getContactInfo(ContactPhone)
                                            handleAPIGetCustomerProfile(ContactPhone)
                                            dispatch(setTempPhoneNumber(ContactPhone));
                                        }}
                                        onChangeText={(text) => {
                                            const regExpPhone = new RegExp(
                                                /^[0]\d{0,9}$/
                                            );
                                            const isValidate =
                                                regExpPhone.test(text) ||
                                                text === '';
                                            if (isValidate) {
                                                dispatch(
                                                    updateCustomerInfo({
                                                        ContactPhone: text,
                                                        TempCartContactPhone: text
                                                    })
                                                );
                                            }
                                            if (text == '') {
                                                dispatch(setTempPhoneNumber(''));
                                            }
                                        }}
                                        onClear={() => {
                                            dispatch(
                                                updateCustomerInfo({
                                                    ContactPhone: '',
                                                    TempCartContactPhone: ''
                                                })
                                            );
                                            dispatch(setTempPhoneNumber(''));
                                        }}
                                        editable={
                                            !isDisableCustomerPhone
                                        }
                                        RightComponent={
                                            <LoyaltyScanner
                                                onFinishScan={
                                                    handleFinishScanLoyalty
                                                }
                                            />
                                        }
                                    />
                                    <InputField
                                        isRequired={isRequireCustomerInfo || isAva}
                                        label={
                                            isCompany
                                                ? "Họ tên người mua:"
                                                : translate(saleExpress.full_name)
                                        }
                                        placeholder={translate(
                                            saleExpress.placeholder_customer_name
                                        )}
                                        value={ContactName}
                                        onChangeText={(text) => {
                                            if (helper.isValidateCharVN(text)) {
                                                dispatch(
                                                    updateCustomerInfo({
                                                        ContactName: text
                                                    })
                                                );
                                            }
                                        }}
                                        onClear={() => {
                                            dispatch(
                                                updateCustomerInfo({
                                                    ContactName: ''
                                                })
                                            );
                                        }}
                                    />
                                    {(ENUM.BRAND_ID.AN_KHANG == brandID) && <InputField
                                        isRequired={isRequireCustomerInfo}
                                        label={
                                            isCompany
                                                ? translate(
                                                    saleExpress.contact_address
                                                )
                                                : translate(saleExpress.address)
                                        }
                                        placeholder={translate(
                                            saleExpress.placeholder_address
                                        )}
                                        value={ContactAddress}
                                        onChangeText={(text) => {
                                            if (helper.isValidateCharVN(text)) {
                                                dispatch(
                                                    updateCustomerInfo({
                                                        ContactAddress: text
                                                    })
                                                );
                                            }
                                        }}
                                        onClear={() => {
                                            dispatch(
                                                updateCustomerInfo({
                                                    ContactAddress: ''
                                                })
                                            );
                                        }}
                                    />}
                                    <CheckBoxPolicy
                                        isSelected={statePolicy.isSelectedPolicy}
                                        onSelectPolicy={() => {
                                            setStatePolicy({ ...statePolicy, isSelectedPolicy: !statePolicy.isSelectedPolicy })
                                        }}
                                        disabled={statePolicy.disabledPolicy}
                                    />
                                </ScrollView>
                            )}
                        </View>

                        {/* Toa thuốc */}
                        <View
                            style={[
                                styles.pageContainer,
                                styles.dosageContainer
                            ]}
                            key="Dosage">
                            {!isAva && (
                                <View>
                                    <MyText>
                                        {translate(
                                            saleExpress.info_upload_take_photo_of_prescription
                                        )}
                                    </MyText>
                                    {urlFiles.length === 0 ? (
                                        <View style={styles.cameraContainer}>
                                            <Icon
                                                iconSet="Entypo"
                                                name="upload"
                                                color={COLOR.darkGray}
                                                size={32}
                                            />
                                            <Button
                                                text={translate(
                                                    saleExpress.upload_or_take_photo
                                                )}
                                                styleContainer={styles.buttonTakePhoto}
                                                styleText={{
                                                    color: COLOR.white,
                                                    fontSize: 14,
                                                    fontWeight: 'bold'
                                                }}
                                                onPress={() => setIsVisibleCamera(true)}
                                            />
                                            <MyText style={styles.cameraDescription}>
                                                {translate(
                                                    saleExpress.info_max_size_photo
                                                )}
                                            </MyText>
                                        </View>
                                    ) : (
                                        <View
                                            style={[
                                                styles.cameraContainer,
                                                {
                                                    flexDirection: 'row',
                                                    justifyContent: 'flex-start'
                                                }
                                            ]}>
                                            {urlFiles.map((item, index) => (
                                                <ImageProcess
                                                    onPress={() => {
                                                        setIsShowImageViewer(true);
                                                        setIndexImage(index);
                                                    }}
                                                    urlImageLocal={item.url}
                                                    urlImageRemote={item.url}
                                                    deleteImage={() =>
                                                        dispatch(
                                                            updateUrlFiles(
                                                                urlFiles.filter(
                                                                    (ct) =>
                                                                        ct.url !==
                                                                        item.url
                                                                )
                                                            )
                                                        )
                                                    }
                                                    key={index.toString()}
                                                />
                                            ))}
                                            {urlFiles.length > 0 &&
                                                urlFiles.length < 3 && (
                                                    <TouchableOpacity
                                                        style={styles.buttonTakeMore}
                                                        onPress={() =>
                                                            setIsVisibleCamera(true)
                                                        }>
                                                        <Icon
                                                            iconSet="Ionicons"
                                                            name="camera"
                                                            color={COLOR.primary}
                                                            size={40}
                                                        />
                                                    </TouchableOpacity>
                                                )}
                                        </View>
                                    )}

                                    <CaptureCamera
                                        isVisibleCamera={isVisibleCamera}
                                        takePicture={takePicture}
                                        closeCamera={() => {
                                            setIsVisibleCamera(false);
                                        }}
                                        selectPicture={selectPicture}
                                    />
                                    <Modal visible={isShowImageViewer} transparent>
                                        <ImageViewer
                                            renderHeader={() => (
                                                <TouchableOpacity
                                                    style={{
                                                        position: 'absolute',
                                                        right: 5,
                                                        top:
                                                            constants.heightTopSafe + 5,
                                                        zIndex: 100
                                                    }}
                                                    onPress={() =>
                                                        setIsShowImageViewer(false)
                                                    }>
                                                    <Icon
                                                        iconSet="Ionicons"
                                                        name="close"
                                                        color={COLOR.white}
                                                        size={40}
                                                    />
                                                </TouchableOpacity>
                                            )}
                                            imageUrls={urlFiles}
                                            index={indexImage}
                                            enableSwipeDown
                                            onCancel={() => setIsShowImageViewer(false)}
                                        />
                                    </Modal>
                                </View>
                            )}
                        </View>
                        {/* Khuyễn mãi 22 */}
                        <View
                            key="PromotionProfit"
                            style={styles.pageContainer}>
                            {hasPromotionProfit && (
                                <PromotionProfit
                                    containerStyle={styles.pageContainer}
                                    info={data.cus_PromDiscountExtraMaster}
                                />
                            )}
                        </View>
                    </AnimatedPagerView>
                    <View style={styles.bottomBar}>
                        <Button
                            text={translate(shoppingCart.btn_delete_cart)}
                            styleContainer={styles.button}
                            styleText={{
                                color: COLOR.primary,
                                fontSize: 14,
                                fontWeight: 'bold'
                            }}
                            onPress={handleDeleteCart}
                        />
                        <Button
                            text={translate(shoppingCart.btn_create_sale_order)}
                            styleContainer={[
                                styles.button,
                                { backgroundColor: COLOR.primary, borderWidth: 0 }
                            ]}
                            styleText={{
                                color: COLOR.white,
                                fontSize: 14,
                                fontWeight: 'bold'
                            }}
                            onPress={handleCreateSaleOrder}
                        />
                    </View>
                    <OTPSheet
                        bottomSheetRef={OTPSheetRef}
                        onChangeStatusSheet={() => { }}
                    >
                        <OTPInner
                            typeOTP={"CUSTOMERFIRSTPURCHASEANKHANG"}
                            onConfirm={() => {
                                OTPSheetRef.current?.dismiss()
                                handleSaleOrderFlow(tempDataCart)
                            }}
                            customerInfo={{
                                customerPhone: ContactPhone,
                                customerName: ContactName,
                            }}
                            isHideBlock
                        />
                    </OTPSheet>
                    <VoucherSheet
                        voucherSheetRef={voucherSheetRef}
                        onChangeStatusSheet={(index) => {
                            setIsPresented(index !== -1)
                        }}
                        vouchers={vouchers}
                        getGiftVoucherCustomer={() => { }}
                        isFetchingGiftVoucher={false}
                        customerInfo={{ customerName: ContactName, gender: Gender }}
                    />
                </KeyboardAvoidingView>
                <Indicator visible={loadingBlock} />
                {
                    isVisibleVoucherCustomer && <TouchableOpacity
                        style={styles.scrollTopButton}
                        onPress={() => { voucherSheetRef.current?.present() }}>
                        <Image
                            source={require('../../../../assets/tag_price.png')}
                            style={styles.productImage}
                        />
                    </TouchableOpacity>
                }
            </BottomSheetModalProvider>
        </SafeAreaView>
    );
};

const style = (theme) => {
    const COLOR = theme.colors;
    return StyleSheet.create({
        bar: {
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
            overflow: 'hidden',
            paddingBottom: 3
        },
        borderStyle: {
            backgroundColor: COLOR.bgCart
        },
        bottomBar: {
            backgroundColor: COLOR.white,
            flexDirection: 'row',
            justifyContent: 'center'
        },
        button: {
            backgroundColor: COLOR.white,
            borderColor: COLOR.primary,
            borderRadius: constants.getSize(10),
            borderWidth: 2,
            height: constants.getSize(40),
            marginHorizontal: 15,
            marginVertical: 10,
            width: constants.width / 2 - 50
        },
        buttonTakeMore: {
            alignItems: 'center',
            backgroundColor: COLOR.bgBtnTakeMore,
            borderRadius: 10,
            height: IMAGE_SIZE,
            justifyContent: 'center',
            marginLeft: 10,
            width: IMAGE_SIZE
        },
        buttonTakePhoto: {
            backgroundColor: COLOR.primary,
            borderRadius: constants.getSize(20),
            height: constants.getSize(30),
            marginVertical: 10,
            width: constants.getSize(130)
        },
        cameraContainer: {
            alignItems: 'center',
            borderColor: COLOR.border,
            borderRadius: 10,
            borderStyle: 'dashed',
            borderWidth: 1,
            height: IMAGE_SIZE + 20,
            justifyContent: 'center',
            marginTop: 10,
            paddingVertical: 10,
            width: constants.width - 40
        },
        cameraDescription: {
            color: COLOR.darkGray,
            fontSize: 13
        },
        cartIcon: {
            height: 25,
            width: 25
        },
        cartShadow: {
            alignItems: 'center',
            backgroundColor: COLORS.bgFFFFFF,
            elevation: 3,
            flexDirection: 'row',
            height: 44,
            paddingHorizontal: 20,
            shadowColor: COLORS.bg000000,
            shadowOffset: { width: 1, height: 1 },
            shadowOpacity: 0.4,
            shadowRadius: 3,
            width: '100%'
        },
        detailPriceContainer: {
            backgroundColor: COLOR.bgDetailPrice,
            borderRadius: 10,
            marginTop: 5,
            paddingHorizontal: 20,
            paddingVertical: 10
        },
        dosageContainer: {
            alignItems: 'center',
            paddingHorizontal: 20,
            paddingTop: 20
        },
        gift: {
            marginLeft: 10,
            marginTop: 6,
            paddingRight: 10
        },
        giftContainer: {
            flexDirection: 'row',
            marginHorizontal: 10,
            paddingTop: 5
        },
        giftIcon: {
            alignSelf: 'flex-start'
        },
        mainContainer: {
            backgroundColor: COLOR.bgCart,
            flex: 1,
            position: 'relative'
        },
        pageContainer: {
            backgroundColor: COLOR.white,
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
            flex: 1
        },
        pagerTitleContainer: {
            backgroundColor: COLOR.bgCart,
            height: PAGER_TITLE_HEIGHT,
            justifyContent: 'space-between'
        },
        priceText: {
            marginBottom: 5
        },
        rowRadio: {
            flexDirection: 'row',
            marginBottom: 10
        },
        summaryContainer: {
            marginHorizontal: 10,
            paddingVertical: 10
        },
        title: {
            alignItems: 'center',
            borderRadius: DOT_SIZE / 2,
            elevation: 5,
            height: DOT_SIZE,
            justifyContent: 'center',
            shadowColor: COLOR.primary,

            shadowOffset: {
                width: 0,
                height: 3
            },

            shadowOpacity: 0.29,
            shadowRadius: 4.65,
            width: DOT_SIZE
        },
        totalPriceCart: {
            borderTopColor: COLOR.border,
            borderTopWidth: 1,
            paddingTop: 5
        },
        scrollTopButton: {
            position: 'absolute',
            bottom: 120,
            right: 20,
            backgroundColor: '#2D8A4B',
            borderRadius: 30,
            width: 50,
            height: 50,
            justifyContent: 'center',
            alignItems: 'center',
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.3,
            shadowRadius: 4,
            elevation: 5
        },
        productImage: {
            width: 30,
            height: 50,
            resizeMode: 'contain'
        },
    });
};

export default CartScreen;

export const checkApplyVoucher = (cartRequest, brandID, storeID) => {
    // if (storeID != "10374") return false
    const eligibleProductIDs = helper.configScreenProtectorDiscount(brandID) || [];
    return cartRequest.SaleOrderDetails.some((product) => {
        const validMainProduct = eligibleProductIDs.includes(String(product.ProductID))
        const validSaleSaleOrders = product.saleSaleOrders?.some(
            (p) => eligibleProductIDs.includes(String(p.ProductID))
        );
        return (
            validMainProduct ||
            validSaleSaleOrders
        );
    });
};

const getPromoRequireInfors = (SaleOrderDetails) => {
    if (!helper.IsNonEmptyArray(SaleOrderDetails)) return [];

    return SaleOrderDetails.flatMap(({ PromoRequireInfors, SaleOrderDetailID, ProductName }) =>
        helper.IsNonEmptyArray(PromoRequireInfors)
            ? PromoRequireInfors.map((ele, index) => ({
                ...ele,
                SaleOrderDetailID,
                ProductName,
                isDisplayName: index === 0,
            }))
            : []
    );
};


