import React, { Component } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON>ing, BackHandler, Keyboard } from 'react-native';
import {
    BaseLoading,
    showB<PERSON><PERSON>,
    hide<PERSON>lock<PERSON>,
    MyText,
    ViewHTML,
    OTPSheet
} from '@components';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import SafeAreaView from 'react-native-safe-area-view';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import Toast from 'react-native-toast-message';
import {
    helper,
    storageHelper,
    convertHtml2Image,
    convertToBase64,
    printSocket
} from '@common';
import { constants, DEVICE } from '@constants';
import { keys, translate } from '@translate';
import * as actionSaleOrderCreator from '../../SaleOrderPayment/action';
import * as actionManagerSOCreator from '../../SaleOrderManager/action';
import * as actionCardCreator from '../../Card/action';
import * as actionShoppingCartCreator from "../../ShoppingCart/action";
import * as actionAnKhangNewCreator from "../../AnKhangNew/action"
import CustomerInfo from '../components/CustomerInfo';
import PaymentVoucher from '../components/PaymentVoucher';
import PaymentCard from '../components/PaymentCard';
import PaymentQRCode from '../components/PaymentQRCode';
import DebtRefundInfo from '../components/DebtRefundInfo';
import PrintReport from '../components/PrintReport';
import Report from '../components/PrintReport/Report';
import CheckPrint from '../components/CheckPrint';
import CheckCustomer from '../components/CheckCustomer';
import ButtonCreate from '../components/ButtonCreate';
import AmountInfo from '../components/AmountInfo';
import ProductOrder from '../components/ProductOrder';
import MainProduct from '../components/ProductOrder/MainProduct';
import GiftProduct from '../components/ProductOrder/GiftProduct';
import Voucher from '../components/PaymentVoucher/Voucher';
import MoneyCard from '../components/PaymentCard/MoneyCard';
import BatchInfo from '../../AnKhangPharmacy/components/BatchInfo';
import CheckBoxPayment from '../components/CheckBoxPayment';
import PaymentLoyalty from '../components/PaymentLoyalty';
import { SCREENS } from '../constants';
import { checkShowPackagingBagScreen } from '../../SaleOrderPayment/action';
import ModalPinCode from '../../SaleOrderPayment/component/PinCodeModal';
import OTPCustomerVoucher from '../../SaleOrderPayment/component/OTPCustomerVoucher';
import { Transaction, AlertMessage } from '../components';
import useTheme from '../useTheme';
import PaymentBankTransfer from '../components/PaymentBankTransfer';
import { COLORS } from '@styles';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import PaymentTransferSheet from '../../SaleOrderPayment/Sheet/PaymentTransferSheet';
import OTPInner from '../../ShoppingCart/component/OTPInner';
import { TYPE_PROFILE } from '../../../constants/constants';



const { H_BILL, H_VOUCHER, H_KEY, PRINT_CONFIG, TRANSACTION_STATUS, REPORT_NAME } = constants;

const VoucherWarningMessage = 'Khách đang bị mất tiền trên PMH.';

class PaymentScreen extends Component {
    constructor(props) {
        super(props);
        this.state = {
            isOutput: true,
            isPayCash: true,
            mainProducts: [],
            giftProducts: [],
            saleProducts: [],
            keyGiftRemove: new Set(),
            reportRetail: {},
            reportVAT: {},
            reportCommon: {},
            cashPayment: 0,
            keyboardTaps: 'always',
            saleProgramInfo: {},
            contractID: '',
            isCustomer: false,
            isVisibleVoucher: false,
            dataPhoneVoucher: [],
            isBlockUI: false,
            isVerify: false,
            isPrintDosage: true,
            isPrintBatchNo: false,
            batchModal: {
                visible: false,
                value: [],
                name: '',
                totalQuantity: 0,
                allowToChangeLess: false
            },
            batchesBySOD: {},
            dataMoneys: [],
            packagings: [],
            isVisibleHTML: false,
            base64PDF: '',
            visiblePinCode: false,
            shouldCallLoyalty: false,
            visibleOTPCusVoucher: false,
            phoneCertifyPMH: '',
            loyaltyTransaction: {
                status: 0,
                amount: 0,
                createdAt: 0,
                message: ''
            },
            isVisibleAlert: false,
            isFocusCash: false,
            dataBankInfo: [],
            bankSelected: {},
            statusTransferPaymentSuccess: {
                type: "INIT",
                message: "GIAO DỊCH ĐANG ĐỢI XỬ LÝ..."
            }
        };
        this.isModifyPaymentMoney = false;
        this.isFirst = true;
        this.dataPrint = {};
        this.oldImei = "";
        this.shouldOriginUpdate = false;
        this.ebillQTV = new Set();
        this.isCheckTransfer = false;
        this.paymentTransferSheetRef = React.createRef(null)
        this.intervalPaymentId = React.createRef(-1);
        this.extraDataGenQR = React.createRef(null);
        this.OTPSheetRef = React.createRef(null)
        this.dataTempCreateQR = React.createRef(null);


    }

    handleGetmoneycardlist = () => {
        this.props.actionCard
            .getMoneycardlistReponse()
            .then((dataMoneys) => {
                this.setState({ dataMoneys });
            })
            .catch((error) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    error.msgError,
                    [
                        {
                            text: 'OK',
                            style: 'default'
                        }
                    ]
                );
            });
    };

    componentDidMount() {
        // addEventListener "addEventListener"
        BackHandler.addEventListener(
            'addEventListener',
            this.onBackButtonPressed
        );
        // Linking.addEventListener('url', this.handleOpenURL);
        this.handleGetmoneycardlist();
    }

    onBackButtonPressed = () => {
        return true;
    };

    // EventListener
    handleOpenURL = (event) => {
        const { url } = event;
        if (url) {
            const saleOrderID = url.split('=').pop();
            if (this.props.so == saleOrderID) {
                const newDataPayAndOutput = helper.deepCopy(
                    this.props.dataPayAndOutput
                );
                this.modifyPaymentMoney(newDataPayAndOutput);
            } else {
                this.hideUIIndicator();
            }
        }
    };

    componentWillUnmount() {
        // this.backHandler.remove();
        BackHandler.removeEventListener(
            'hardwareBackPress',
            this.onBackButtonPressed
        );
        // Linking.removeEventListener('url', this.handleOpenURL);
    }

    componentDidUpdate(preProps, preState) {
        const { shouldOriginUpdate } = this.props.route.params ?? { shouldOriginUpdate: false };
        this.shouldOriginUpdate = shouldOriginUpdate
        if (preProps.defaultReport !== this.props.defaultReport) {
            const { defaultReport } = this.props;
            this.setState({
                reportRetail: defaultReport.retail,
                reportVAT: defaultReport.vat,
                reportCommon: defaultReport.common
            });
        }
        if (
            this.isFirst &&
            ((preProps.dataSaleOrder !== this.props.dataSaleOrder) || this.shouldOriginUpdate)
        ) {
            const { dataSaleOrder } = this.props;
            if (!helper.IsEmptyObject(dataSaleOrder)) {
                this.isFirst = false;
                this.shouldOriginUpdate = false
                const {
                    listMainProduct = [],
                    listGiftPromotion = [],
                    listSalePromotion = [],
                    AllowOutput,
                    AllowPayCash,
                    SaleOrderSaleProgramInfo,
                    TotalRemain,
                    IsSOAnKhang,
                    IsSOAVA
                } = dataSaleOrder;
                const saleProgramInfo = SaleOrderSaleProgramInfo || {};
                const keyGiftRemove = new Set();
                listGiftPromotion.forEach((ele) => {
                    const {
                        SaleOrderDetailID,
                        IsOutput,
                        IsRequestIMEI,
                        IMEIByLoad
                    } = ele;
                    // const isImeiLoad = IsRequestIMEI && !!IMEIByLoad;
                    if (!IsOutput) {
                        keyGiftRemove.add(SaleOrderDetailID);
                    }
                });
                const expressSale = IsSOAnKhang || IsSOAVA;
                const cashPayment = expressSale ? TotalRemain : 0;
                this.setState(
                    {
                        shouldCallLoyalty: true,
                        mainProducts: listMainProduct,
                        giftProducts: listGiftPromotion,
                        saleProducts: listSalePromotion,
                        isOutput: AllowOutput,
                        isPayCash: AllowPayCash,
                        contractID: saleProgramInfo.ContractID || '',
                        saleProgramInfo: {
                            ContractID: saleProgramInfo.ContractID || '',
                            customerIDCard:
                                saleProgramInfo.customerIDCard || '',
                            PGProcessUserID:
                                saleProgramInfo.PGProcessUserID || '',
                            PGProcessUserName:
                                saleProgramInfo.PGProcessUserName || '',
                            packageRates: saleProgramInfo.packageRates || '',
                            TotalPrePaid: saleProgramInfo.TotalPrePaid || 0,
                            TermLoan: saleProgramInfo.TermLoan || 0,
                            PaymentAmountMonthly:
                                saleProgramInfo.PaymentAmountMonthly || 0,
                            CustomerName: saleProgramInfo.CustomerName || '',
                            CustomerAddress:
                                saleProgramInfo.CustomerAddress || '',
                            customerPhone: saleProgramInfo.customerPhone || '',
                            customerIDCard: saleProgramInfo.customerIDCard || ''
                        },
                        keyGiftRemove,
                        cashPayment
                    },
                    () => {
                        if (expressSale) {
                            this.onBlurInputCash(dataSaleOrder, cashPayment)();
                        }
                    }
                );
            }
        }
    }

    getSaleOrderPayment = () => {
        const {
            dataSO: { SaleOrderID },
            actionSaleOrder
        } = this.props;
        actionSaleOrder.getSaleOrderPayment(SaleOrderID);
    };

    getReportPrinter = () => {
        const {
            dataSO: { SaleOrderTypeID },
            actionSaleOrder
        } = this.props;
        actionSaleOrder.getReportPrinterSocket(SaleOrderTypeID);
    };

    getDataQRTransaction = () => {
        const {
            dataSO: { SaleOrderTypeID }
        } = this.props;
        this.props.actionSaleOrder.getDataQRTransaction(SaleOrderTypeID);
    };

    handleGetLoyaltyPoint = (dataLoyalty) => {
        const { dataSaleOrder, actionSaleOrder } = this.props
        const dataModifySaleOrder = { ...dataSaleOrder };
        const {
            DisableTextLoyalty,
            DefaultPointUse,
            MaxPointApply,
            AvailablePoint,
            IsAllowParticipationLoyalty,
            IsCustomerCodeByCompany,
            CustomerCode
        } = dataLoyalty
        dataModifySaleOrder.DisableTextLoyalty = DisableTextLoyalty
        dataModifySaleOrder.DefaultPointUse = DefaultPointUse
        dataModifySaleOrder.MaxPointApply = MaxPointApply
        dataModifySaleOrder.AvailablePoint = AvailablePoint
        dataModifySaleOrder.IsAllowParticipationLoyalty = IsAllowParticipationLoyalty
        dataModifySaleOrder.IsCustomerCodeByCompany = IsCustomerCodeByCompany
        dataModifySaleOrder.CustomerCode = CustomerCode
        actionSaleOrder.stop_modify_saleorder_payment(dataModifySaleOrder);
    }


    handleApiLoadBatchNo = (product) => {
        const batches = this.state.batchesBySOD[product.SaleOrderDetailID];
        if (batches) {
            this.setState({
                batchModal: {
                    visible: true,
                    value: batches,
                    name: product.ProductName,
                    id: product.SaleOrderDetailID,
                    totalQuantity: product.Quantity,
                    allowToChangeLess: product.cus_AllowChageQuantity
                }
            });
        } else {
            showBlockUI();
            this.props.actionSaleOrder
                .loadBatchNoBySO({
                    unSaleOrder: this.props.dataSaleOrder,
                    saleOrderDetailID: product.SaleOrderDetailID,
                    CheckOutput: true,
                    CheckIncome: true
                })
                .then((newBatches) => {
                    hideBlockUI();
                    this.setState({
                        batchModal: {
                            visible: true,
                            value: newBatches,
                            name: product.ProductName,
                            id: product.SaleOrderDetailID,
                            totalQuantity: product.Quantity,
                            allowToChangeLess: product.cus_AllowChageQuantity
                        },
                        batchesBySOD: {
                            ...this.state.batchesBySOD,
                            [product.SaleOrderDetailID]: newBatches
                        }
                    });
                })
                .catch((msgError) => {
                    hideBlockUI();
                    Alert.alert(
                        translate('common.notification_uppercase'),
                        msgError,
                        [{ text: 'OK' }]
                    );
                });
        }
    };

    getContentBase64View = () => {
        if (helper.IsNonEmptyString(this.state.base64PDF)) {
            this.setState({ isVisibleHTML: true });
        } else {
            showBlockUI();
            const { actionManagerSO, dataSO } = this.props;
            actionManagerSO
                .getContentBase64View({
                    reportContent: 'BankAccountContent',
                    saleOrderID: dataSO.SaleOrderID
                })
                .then((base64) => {
                    this.setState({
                        isVisibleHTML: true,
                        base64PDF: base64
                    });
                    hideBlockUI();
                })
                .catch((msgError) => {
                    Alert.alert(translate('common.notification'), msgError, [
                        {
                            text: 'OK',
                            onPress: hideBlockUI
                        }
                    ]);
                });
        }
    };

    handleChangeBank = (bank) => {
        clearInterval(this.intervalPaymentId.current);
        this.setState({ bankSelected: bank }, () => {
            this.handleIntervalPayment()
        })
    }
    handleIntervalPayment = () => {
        if (!helper.configIntervalPayment()) return
        this.intervalPaymentId.current = setInterval(this.intervalPaymentFunction, 5000);
        setTimeout(() => {
            clearInterval(this.intervalPaymentId.current);
        }, 3000000);
    }
    intervalPaymentFunction = async () => {
        const {
            dataSaleOrder: { SaleOrderID }
        } = this.props;
        const { bankSelected: { CreatedDate } } = this.state
        actionSaleOrderCreator.getTransactionTransfer(SaleOrderID, CreatedDate).then(() => {
            this.setState({
                statusTransferPaymentSuccess: {
                    type: "SUCCESS",
                    message: "GIAO DỊCH ĐÃ THỰC HIỆN THÀNH CÔNG"
                }
            })
            clearInterval(this.intervalPaymentId.current);

        }).catch((msgError) => {
            this.setState({
                statusTransferPaymentSuccess: {
                    type: "ERROR",
                    message: msgError
                }
            })
            clearInterval(this.intervalPaymentId.current);
        })

    }
    handleBankTransfer = (amount) => {
        showBlockUI()
        const {
            dataSO: { SaleOrderID },
            actionSaleOrder
        } = this.props;
        actionSaleOrder.getBankInfo({
            saleOrderID: SaleOrderID, paymentAmount: amount
        }).then((result) => {
            hideBlockUI()
            this.setState({
                dataBankInfo: result, bankSelected: result[0], statusTransferPaymentSuccess: {
                    type: "INIT",
                    message: "GIAO DỊCH ĐANG ĐỢI XỬ LÝ..."
                }
            }, () => {
                this.paymentTransferSheetRef.current?.present()
                if (this.state.bankSelected?.QRCodeData?.length > 0) {
                    this.handleIntervalPayment()
                }
            })
        }).catch((error) => {
            Alert.alert(
                translate('common.notification_uppercase'),
                error.msgError,
                [
                    {
                        text: translate('saleExpress.retry'),
                        style: 'cancel',
                        onPress: () => this.handleBankTransfer(amount)
                    },
                    {
                        text: translate('common.btn_skip'),
                        style: 'cancel',
                        onPress: () => hideBlockUI()
                    }

                ]
            );
        })
    }

    render() {
        const {
            isOutput,
            isPayCash,
            mainProducts,
            saleProducts,
            giftProducts,
            keyGiftRemove,
            reportRetail,
            reportVAT,
            reportCommon,
            cashPayment,
            keyboardTaps,
            saleProgramInfo,
            contractID,
            isCustomer,
            isVisibleVoucher,
            dataPhoneVoucher,
            isBlockUI,
            isVerify,
            isPrintDosage,
            batchModal,
            isPrintBatchNo,
            dataMoneys,
            base64PDF,
            isVisibleHTML,
            visiblePinCode,
            visibleOTPCusVoucher,
            phoneCertifyPMH,
            loyaltyTransaction,
            isVisibleAlert
        } = this.state;
        const {
            dataSaleOrder,
            printerRetail,
            printerVAT,
            printerCommon,
            stateSaleOrder,
            statePrinter,
            actionSaleOrder,
            dataQRTransaction: { dataQRType, dataTransaction },
            stateQRTransaction,
            userInfo: { brandID, storeID, provinceID },
            COLOR
        } = this.props;
        const {
            IsInstallment,
            GiftVoucherIssueRequests,
            TotalGiftVoucherAmount,
            ApplyMoneyCardDetails,
            MoneyCardList,
            MoneyCard: TotalMoneyCard,
            LoyaltyInfo,
            AvailablePoint,
            MaxPointApply,
            PointRefund,
            TotalPointLoyalty,
            CashVND,
            AllowOutput,
            AllowPayCash,
            PayableAmount,
            ErrorMessageOutput,
            ErrorMessagePayCash,
            SaleOrderSaleProgramInfo,
            SaleOrderID,
            EPOSTransactionID,
            CustomerPhone,
            MoneyBank,
            TotalRemainNotCashVND,
            IsCheckExistCustomerInStall,
            DefaultPointUse,
            DisableTextLoyalty,
            cus_IsAddPromotionSO,
            CartID,
            IsAllowParticipationLoyalty,
            cus_IsPrintDosageContent,
            IsSOAnKhang,
            TotalRemain,
            CustomerName
        } = dataSaleOrder;
        const IsAutoCreateEP = helper.IsNonEmptyString(EPOSTransactionID);
        const dataVoucher = GiftVoucherIssueRequests || [];
        const dataCard = ApplyMoneyCardDetails || [];
        const dataPOS = MoneyCardList || [];
        const dataLoyalty = LoyaltyInfo || {};
        const isVisibleMain = helper.IsNonEmptyArray(mainProducts);
        const isVisibleSale = helper.IsNonEmptyArray(saleProducts);
        const isVisibleGift = helper.IsNonEmptyArray(giftProducts);
        const isVisible = AllowOutput || AllowPayCash;
        const keyImeiProduct = getKeyImeiProduct(
            mainProducts,
            saleProducts,
            giftProducts
        );
        const isVisibleCustomer = isOutput && IsCheckExistCustomerInStall;
        const isLoyalty = IsAllowParticipationLoyalty;
        const isPhoneVoucher =
            isPayCash && helper.IsNonEmptyString(CustomerPhone);
        const cartID = helper.IsNonEmptyString(CartID) ? CartID.trim() : '';
        const shouldPrintDosage =
            cus_IsPrintDosageContent && `${brandID}` == '8';
        return (
            <SafeAreaView
                style={{
                    flex: 1
                }}>
                <BottomSheetModalProvider>
                    <BaseLoading
                        isLoading={stateSaleOrder.isFetching}
                        isEmpty={stateSaleOrder.isEmpty}
                        textLoadingError={stateSaleOrder.description}
                        isError={stateSaleOrder.isError}
                        onPressTryAgains={this.getSaleOrderPayment}
                        content={
                            <KeyboardAwareScrollView
                                style={{
                                    flex: 1
                                }}
                                enableResetScrollToCoords={false}
                                keyboardShouldPersistTaps={keyboardTaps}
                                bounces={false}
                                overScrollMode="always"
                                showsHorizontalScrollIndicator={false}
                                showsVerticalScrollIndicator={false}
                                extraScrollHeight={100}>
                                <View
                                    style={{
                                        flex: 1
                                    }}>
                                    <CustomerInfo info={dataSaleOrder} />
                                    <CheckBoxPayment
                                        isCheck={isOutput}
                                        onCheck={this.onChangOutput}
                                        disabled={!AllowOutput}
                                        title={translate('saleOrderPayment.output')}
                                        message={ErrorMessageOutput}
                                        activeIconColor={COLOR.primary}
                                        activeTextColor={COLOR.primary}
                                    />
                                    {isOutput && (
                                        <>
                                            {isVisibleMain && (
                                                <ProductOrder
                                                    title={translate(
                                                        'saleOrderPayment.products_list'
                                                    )}
                                                    data={mainProducts}
                                                    renderItem={({
                                                        item,
                                                        index
                                                    }) => (
                                                        <MainProduct
                                                            product={item}
                                                            updateImeiProduct={(
                                                                value
                                                            ) => {
                                                                const newMainProducts =
                                                                    helper.deepCopy(
                                                                        mainProducts
                                                                    );
                                                                newMainProducts[
                                                                    index
                                                                ].IMEI = value;
                                                                this.setState({
                                                                    mainProducts:
                                                                        newMainProducts
                                                                });
                                                            }}
                                                            updateSerialProduct={(
                                                                value
                                                            ) => {
                                                                const newMainProducts =
                                                                    helper.deepCopy(
                                                                        mainProducts
                                                                    );
                                                                newMainProducts[
                                                                    index
                                                                ] = value;
                                                                this.setState({
                                                                    mainProducts:
                                                                        newMainProducts
                                                                });
                                                            }}
                                                            updateImeiSticker={(
                                                                value
                                                            ) => {
                                                                const newMainProducts =
                                                                    helper.deepCopy(
                                                                        mainProducts
                                                                    );
                                                                newMainProducts[
                                                                    index
                                                                ].InputIMEIList = value;
                                                                this.setState({
                                                                    mainProducts:
                                                                        newMainProducts
                                                                });
                                                            }}
                                                            checkImei={
                                                                actionSaleOrder.checkOutputImei
                                                            }
                                                            checkSerial={
                                                                actionSaleOrder.checkOutputSerial
                                                            }
                                                            keyImeiProduct={
                                                                keyImeiProduct
                                                            }
                                                            onPressLotDate={() => {
                                                                this.handleApiLoadBatchNo(
                                                                    item
                                                                );
                                                            }}
                                                        />
                                                    )}
                                                />
                                            )}
                                            {isVisibleSale && (
                                                <ProductOrder
                                                    title={translate(
                                                        'saleOrderPayment.attached_sale_list'
                                                    )}
                                                    data={saleProducts}
                                                    renderItem={({
                                                        item,
                                                        index
                                                    }) => (
                                                        <MainProduct
                                                            product={item}
                                                            updateImeiProduct={(
                                                                value
                                                            ) => {
                                                                const newSaleProducts =
                                                                    helper.deepCopy(
                                                                        saleProducts
                                                                    );
                                                                newSaleProducts[
                                                                    index
                                                                ].IMEI = value;
                                                                this.setState({
                                                                    saleProducts:
                                                                        newSaleProducts
                                                                });
                                                            }}
                                                            updateSerialProduct={(
                                                                value
                                                            ) => {
                                                                const newSaleProducts =
                                                                    helper.deepCopy(
                                                                        saleProducts
                                                                    );
                                                                newSaleProducts[
                                                                    index
                                                                ] = value;
                                                                this.setState({
                                                                    saleProducts:
                                                                        newSaleProducts
                                                                });
                                                            }}
                                                            updateImeiSticker={(
                                                                value
                                                            ) => {
                                                                const newSaleProducts =
                                                                    helper.deepCopy(
                                                                        saleProducts
                                                                    );
                                                                newSaleProducts[
                                                                    index
                                                                ].InputIMEIList = value;
                                                                this.setState({
                                                                    saleProducts:
                                                                        newSaleProducts
                                                                });
                                                            }}
                                                            checkImei={
                                                                actionSaleOrder.checkOutputImei
                                                            }
                                                            checkSerial={
                                                                actionSaleOrder.checkOutputSerial
                                                            }
                                                            keyImeiProduct={
                                                                keyImeiProduct
                                                            }
                                                            onPressLotDate={() => {
                                                                this.handleApiLoadBatchNo(
                                                                    item
                                                                );
                                                            }}
                                                        />
                                                    )}
                                                />
                                            )}
                                            {isVisibleGift && (
                                                <ProductOrder
                                                    title={translate(
                                                        'saleOrderPayment.promotion_list'
                                                    )}
                                                    data={giftProducts}
                                                    renderItem={({
                                                        item,
                                                        index
                                                    }) => (
                                                        <GiftProduct
                                                            product={item}
                                                            updateImeiProduct={(
                                                                value
                                                            ) => {
                                                                const newGiftProducts =
                                                                    helper.deepCopy(
                                                                        giftProducts
                                                                    );
                                                                newGiftProducts[
                                                                    index
                                                                ].IMEI = value;
                                                                this.setState({
                                                                    giftProducts:
                                                                        newGiftProducts
                                                                });
                                                            }}
                                                            updateSerialProduct={(
                                                                value
                                                            ) => {
                                                                const newGiftProducts =
                                                                    helper.deepCopy(
                                                                        giftProducts
                                                                    );
                                                                newGiftProducts[
                                                                    index
                                                                ] = value;
                                                                this.setState({
                                                                    giftProducts:
                                                                        newGiftProducts
                                                                });
                                                            }}
                                                            updateImeiSticker={(
                                                                value
                                                            ) => {
                                                                const newGiftProducts =
                                                                    helper.deepCopy(
                                                                        giftProducts
                                                                    );
                                                                newGiftProducts[
                                                                    index
                                                                ].InputIMEIList = value;
                                                                this.setState({
                                                                    giftProducts:
                                                                        newGiftProducts
                                                                });
                                                            }}
                                                            keyGiftRemove={
                                                                keyGiftRemove
                                                            }
                                                            updateKeyRemove={(
                                                                keyRemove
                                                            ) => {
                                                                this.setState({
                                                                    keyGiftRemove:
                                                                        keyRemove
                                                                });
                                                            }}
                                                            checkImei={
                                                                actionSaleOrder.checkOutputImei
                                                            }
                                                            checkSerial={
                                                                actionSaleOrder.checkOutputSerial
                                                            }
                                                            keyImeiProduct={
                                                                keyImeiProduct
                                                            }
                                                            onPressLotDate={() => {
                                                                this.handleApiLoadBatchNo(
                                                                    item
                                                                );
                                                            }}
                                                        />
                                                    )}
                                                />
                                            )}
                                            <BatchInfo
                                                data={batchModal.value}
                                                isShowModal={batchModal.visible}
                                                onClose={() =>
                                                    this.setState({
                                                        batchModal: {
                                                            value: [],
                                                            visible: false,
                                                            id: '',
                                                            totalQuantity: 0,
                                                            allowToChangeLess: false
                                                        }
                                                    })
                                                }
                                                editable
                                                productName={batchModal.name}
                                                totalQuantity={
                                                    batchModal.totalQuantity
                                                }
                                                allowToChangeLess={
                                                    batchModal.allowToChangeLess
                                                }
                                                onSubmit={({ batch }) => {
                                                    this.setState({
                                                        batchesBySOD: {
                                                            ...this.state
                                                                .batchesBySOD,
                                                            [batchModal.id]: batch
                                                        }
                                                    });
                                                }}
                                            />
                                        </>
                                    )}
                                    <CheckBoxPayment
                                        isCheck={isPayCash}
                                        onCheck={this.onChangPayCash}
                                        disabled={!AllowPayCash}
                                        title={translate('saleOrderPayment.charge')}
                                        message={ErrorMessagePayCash}
                                        onGetVoucher={this.getDataPhoneVoucher}
                                        isPhoneVoucher={isPhoneVoucher}
                                        activeIconColor={COLOR.primary}
                                        activeTextColor={COLOR.primary}
                                    />
                                    {isPayCash && (
                                        <>
                                            <AmountInfo info={dataSaleOrder} />
                                            <PaymentVoucher
                                                title={translate(
                                                    'saleOrderPayment.customer_voucher'
                                                )}
                                                data={dataVoucher}
                                                renderItem={({ item, index }) => (
                                                    <Voucher
                                                        key={`PaymentVoucher ${index}`}
                                                        info={item}
                                                        onRemove={this.removeGiftVoucher(
                                                            dataVoucher,
                                                            index
                                                        )}
                                                    />
                                                )}
                                                applyVoucher={this.applyGiftVoucher}
                                                total={TotalGiftVoucherAmount}
                                            />
                                            <OTPCustomerVoucher
                                                visible={visibleOTPCusVoucher}
                                                numberPhone={phoneCertifyPMH}
                                                onClose={() => this.closeModalVerify()}
                                                onCreateCertify={(GiftVoucherCertifyType) => this.createVerify(GiftVoucherCertifyType)}
                                                onSubmit={(otpCode, GiftVoucherCertifyType) => this.onCheckOTP(otpCode, GiftVoucherCertifyType)}
                                            />
                                            {/* {DefaultPointUse > 0 && isLoyalty && ( */}
                                            <PaymentLoyalty
                                                shouldCallLoyalty={this.state.shouldCallLoyalty}
                                                onDataLoyaltyChange={this.handleGetLoyaltyPoint}
                                                title={translate(
                                                    'saleOrderPayment.membership_point'
                                                )}
                                                total={TotalPointLoyalty}
                                                data={dataLoyalty}
                                                availablePoint={AvailablePoint}
                                                maxUsePoint={MaxPointApply}
                                                refundPoint={PointRefund}
                                                onUpdateDataLoyalty={
                                                    this.updateDataLoyalty
                                                }
                                                customerPhone={CustomerPhone}
                                                maxPayment={
                                                    TotalRemainNotCashVND
                                                }
                                                brandID={brandID}
                                                defaultPointUse={
                                                    DefaultPointUse
                                                }
                                                isDefault={helper.IsNonEmptyString(
                                                    DisableTextLoyalty
                                                )}
                                                cartID={cartID}
                                            />
                                            <PaymentBankTransfer
                                                saleOrderID={SaleOrderID}
                                                payableAmount={TotalRemain}
                                                colorPrimary={COLOR.primary}
                                                colorSecondary={COLORS.bg000000}
                                                colorTertiary={COLOR.primary}
                                                onReloadSOPayment={() => {
                                                    this.isCheckTransfer = true;
                                                    this.props.actionSaleOrder.getSaleOrderPayment(SaleOrderID).then((reponse) => {
                                                        const { TotalRemain } = reponse;
                                                        this.setState({ cashPayment: TotalRemain });
                                                    })
                                                }}
                                                isCheck={this.isCheckTransfer || helper.IsNonEmptyArray(this.state.dataBankInfo)}
                                                handleAPIBankTransfer={this.handleBankTransfer}

                                            />

                                            {/* )} */}
                                            <PaymentCard
                                                title={translate(
                                                    'saleOrderPayment.customer_pay_card'
                                                )}
                                                dataMoney={dataCard}
                                                renderItem={({ item, index }) => (
                                                    <MoneyCard
                                                        key={`PaymentCard ${index}`}
                                                        info={item}
                                                        onRemove={this.removeMoneyCard(
                                                            dataCard,
                                                            index
                                                        )}
                                                    />
                                                )}
                                                dataPOS={dataMoneys}
                                                applyMoney={(value) => {
                                                    this.applyMoneyCard(
                                                        dataCard,
                                                        value
                                                    );
                                                }}
                                                total={TotalMoneyCard}
                                                maxPayment={TotalRemainNotCashVND}
                                                onPayMpos={this.getTokenMpos}
                                            />
                                            {!cus_IsAddPromotionSO && (
                                                <BaseLoading
                                                    isLoading={
                                                        stateQRTransaction.isFetching
                                                    }
                                                    isEmpty={
                                                        stateQRTransaction.isEmpty
                                                    }
                                                    textLoadingError={
                                                        stateQRTransaction.description
                                                    }
                                                    isError={
                                                        stateQRTransaction.isError
                                                    }
                                                    onPressTryAgains={
                                                        this.getDataQRTransaction
                                                    }
                                                    content={
                                                        <PaymentQRCode
                                                            title={translate(
                                                                'saleOrderPayment.customer_pay_QR'
                                                            )}
                                                            total={MoneyBank}
                                                            maxPayment={
                                                                TotalRemainNotCashVND
                                                            }
                                                            dataSO={dataSaleOrder}
                                                            dataQRType={dataQRType}
                                                            dataTransaction={
                                                                dataTransaction
                                                            }
                                                            onApplyTransaction={
                                                                this
                                                                    .applyTransaction
                                                            }
                                                            updateTransaction={(
                                                                data
                                                            ) => {
                                                                this.props.actionSaleOrder.updateDataTransaction(
                                                                    data
                                                                );
                                                            }}
                                                            createQR={(data) => {
                                                                this.extraDataGenQR.current = null
                                                                this.createQR(data)
                                                            }}
                                                            onGetQR={this.getQRInfo}
                                                            actionSaleOrder={
                                                                actionSaleOrder
                                                            }
                                                        />
                                                    }
                                                />
                                            )}
                                            <DebtRefundInfo
                                                info={dataSaleOrder}
                                                cash={cashPayment}
                                                onChange={(value) => {
                                                    this.setState({
                                                        cashPayment: value
                                                    });
                                                }}
                                                onFocus={this.setKeyboardTaps}
                                                onBlur={this.onBlurInputCash(
                                                    dataSaleOrder,
                                                    cashPayment
                                                )}
                                                autoFocus={false}
                                            />
                                        </>
                                    )}
                                    {/* <MyText
                                    text={translate('saleOrderManager.view_transfer_information')}
                                    style={{
                                        color: "#147efb",
                                        padding: 10,
                                        fontStyle: "italic",
                                        textDecorationLine: 'underline',
                                        backgroundColor: '#F5F5f5'
                                    }}
                                    onPress={this.getContentBase64View}
                                /> */}
                                    <PrintReport
                                        title={translate(
                                            'saleOrderPayment.choose_printer'
                                        )}
                                        statePrinter={statePrinter}
                                        onTryAgains={this.getReportPrinter}
                                        dataRetail={printerRetail}
                                        dataVAT={printerVAT}
                                        dataCommon={printerCommon}
                                        renderItemRetail={({ item, index }) => (
                                            <Report
                                                key="ReportRetail"
                                                info={item}
                                                report={reportRetail}
                                                onCheck={() => {
                                                    storageHelper.setDefaultPrinter(
                                                        item.STOREPRINTERID,
                                                        0
                                                    );
                                                    this.setState({
                                                        reportRetail: item
                                                    });
                                                }}
                                            />
                                        )}
                                        renderItemVAT={({ item, index }) => (
                                            <Report
                                                key="ReportVAT"
                                                info={item}
                                                report={reportVAT}
                                                onCheck={() => {
                                                    storageHelper.setDefaultPrinter(
                                                        item.STOREPRINTERID,
                                                        1
                                                    );
                                                    this.setState({
                                                        reportVAT: item
                                                    });
                                                }}
                                            />
                                        )}
                                        renderItemCommon={({ item, index }) => (
                                            <Report
                                                key="ReportCommon"
                                                info={item}
                                                report={reportCommon}
                                                onCheck={() => {
                                                    storageHelper.setDefaultPrinter(
                                                        item.STOREPRINTERID,
                                                        2
                                                    );
                                                    this.setState({
                                                        reportCommon: item
                                                    });
                                                }}
                                            />
                                        )}
                                    />
                                    {shouldPrintDosage && (
                                        <CheckPrint
                                            isCheck={isPrintDosage}
                                            onCheck={(value) => {
                                                this.setState({
                                                    isPrintDosage: value
                                                });
                                            }}
                                            title="In liều dùng"
                                        />
                                    )}
                                    {IsSOAnKhang && (
                                        <CheckPrint
                                            isCheck={isPrintBatchNo}
                                            onCheck={(value) => {
                                                this.setState({
                                                    isPrintBatchNo: value
                                                });
                                            }}
                                            title="In chi tiết Lô date"
                                        />
                                    )}
                                    {isVisibleCustomer && (
                                        <CheckCustomer
                                            isCheck={isCustomer}
                                            onCheck={(value) => {
                                                this.setState({
                                                    isCustomer: value
                                                });
                                            }}
                                            title={translate(
                                                'saleOrderPayment.customer_at_store'
                                            )}
                                            note={translate(
                                                'saleOrderPayment.check_here'
                                            )}
                                        />
                                    )}
                                    {isVisible && (
                                        <ButtonCreate
                                            disabled={!isOutput && !isPayCash}
                                            onCreate={this.onCheckSaleOrder}
                                            onCancel={() => {
                                                this.props.navigation.navigate(
                                                    'Pharmacy'
                                                );
                                            }}
                                        />
                                    )}
                                    <ModalPinCode
                                        visible={visiblePinCode}
                                        onClose={() => {
                                            this.setState({ visiblePinCode: false })
                                        }}
                                        onSubmit={(pinCode) => {
                                            if (pinCode.trim().length > 0) {
                                                if (pinCode.trim() === this.oldImei.trim()) {
                                                    Alert.alert('', "Mã pin code không hợp lệ.", [
                                                        {
                                                            text: 'OK',
                                                            style: 'default',
                                                        }
                                                    ]);
                                                }
                                                else {
                                                    this.setState({ visiblePinCode: false })
                                                    this.applyGiftVoucher(pinCode)
                                                }

                                            }
                                            else {
                                                Alert.alert('', "Vui lòng nhập pin code.", [
                                                    {
                                                        text: 'OK',
                                                        style: 'default',
                                                    }
                                                ]);
                                            }
                                        }}
                                    />
                                </View>
                                {/* <PhoneVoucher
                                isVisible={isVisibleVoucher}
                                data={dataPhoneVoucher}
                                onClose={() => {
                                    this.setState({ isVisibleVoucher: false })
                                }}
                                onApply={this.applyEncryptVoucher}
                                isBlockUI={isBlockUI}
                                showBlock={this.showBlock}
                                hideBlock={this.hideBlock}
                                isVerify={isVerify}
                                setVerify={this.setVerify}
                                dataSaleOrder={dataSaleOrder}
                            /> */}

                                <ViewHTML
                                    isVisible={isVisibleHTML}
                                    source={base64PDF}
                                    hideModal={() => {
                                        this.setState({ isVisibleHTML: false });
                                    }}
                                    title="Thông Tin Chuyển Khoản"
                                />
                                <AlertMessage
                                    title="Chi tiết giao dịch"
                                    visible={isVisibleAlert}
                                    onClose={() => this.setState({ isVisibleAlert: false })}
                                    message={loyaltyTransaction.message}
                                    buttons={loyaltyTransaction.status !== TRANSACTION_STATUS.PROCESSING ? [] : [
                                        {
                                            key: 0,
                                            text:
                                                translate(keys.common.btn_notify_try_again),
                                            outline: true,
                                            onPress: () => {
                                                this.onCheckSaleOrder()
                                                this.setState({ isVisibleAlert: false })
                                            }
                                        },
                                        {
                                            key: 1,
                                            text: 'OK',
                                            outline: false,
                                            onPress: () => this.setState({ isVisibleAlert: false })
                                        }
                                    ]}
                                >
                                    <Transaction
                                        name="Quà tặng VIP"
                                        status={loyaltyTransaction.status}
                                        amount={loyaltyTransaction.amount}
                                        time={loyaltyTransaction.createdAt}
                                    />
                                </AlertMessage>
                                <PaymentTransferSheet
                                    paymentTransferSheetRef={this.paymentTransferSheetRef}
                                    bankList={this.state.dataBankInfo}
                                    bankSelected={this.state.bankSelected}
                                    saleOrderID={SaleOrderID}
                                    onChangeBank={this.handleChangeBank}
                                    handleIntervalPayment={this.handleIntervalPayment}
                                    onChangeStatusSheet={(position) => {
                                        if (position == -1) {
                                            clearInterval(this.intervalPaymentId.current);
                                            if (this.state.statusTransferPaymentSuccess.type == "SUCCESS") {
                                                showBlockUI()
                                                actionSaleOrder.ReLoadSaleOrderPayment(SaleOrderID).then((reponse) => {
                                                    actionSaleOrder.setSaleOrderPayment(reponse)
                                                    this.isFirst = true
                                                    hideBlockUI()
                                                }).catch(({ msgError }) => {
                                                    Alert.alert('', msgError, [
                                                        {
                                                            text: 'OK',
                                                            onPress: hideBlockUI
                                                        }
                                                    ]);
                                                })
                                            }
                                        }
                                    }}
                                    statusTransferPaymentSuccess={this.state.statusTransferPaymentSuccess}
                                />
                                <OTPSheet
                                    bottomSheetRef={this.OTPSheetRef}
                                    onChangeStatusSheet={() => { }}
                                >
                                    <OTPInner
                                        typeOTP={"INSTALLMENT"}
                                        onConfirm={() => {
                                            this.OTPSheetRef.current?.dismiss()
                                            this.createQR(this.dataTempCreateQR.current)
                                        }}
                                        customerInfo={{
                                            customerPhone: CustomerPhone,
                                            customerName: CustomerName,
                                        }}
                                    />
                                </OTPSheet>
                            </KeyboardAwareScrollView>
                        }
                    />
                </BottomSheetModalProvider>

            </SafeAreaView>
        );
    }

    modifySaleOrder = (data, isNonBlockUI) => {
        const { isOutput, isPayCash } = this.state;
        !isNonBlockUI && showBlockUI();
        this.props.actionSaleOrder
            .modifySaleOrderPayment({
                saleOrder: data,
                isOutput,
                isPayCash
            })
            .then((success) => {
                hideBlockUI();
            })
            .catch((error) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    error.msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: 'default',
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: 'default',
                            onPress: () => {
                                this.modifySaleOrder(data);
                            }
                        }
                    ]
                );
            });
    };

    createVerify = (GiftVoucherCertifyType) => {
        showBlockUI();
        const { phoneCertifyPMH } = this.state;
        const { userInfo: { brandID, storeID, provinceID } } = this.props
        if (GiftVoucherCertifyType === 2) {
            // type : "SMS"
            actionSaleOrderCreator.createOTP({
                type: "SMS",
                phoneNumber: phoneCertifyPMH,
                typeContent: 'GIFTVOUCHER',
                lenOtp: 4,
                brandID: brandID
            })
                .then((success) => {
                    hideBlockUI();
                })
                .catch((msgError) => {
                    Alert.alert(
                        translate('common.notification_uppercase'),
                        msgError,
                        [
                            {
                                text: translate('common.btn_skip'),
                                style: 'cancel',
                                onPress: hideBlockUI
                            },
                            {
                                text: translate('common.btn_notify_try_again'),
                                style: 'default',
                                onPress: () => this.createVerify(GiftVoucherCertifyType)
                            }
                        ]
                    );
                })
        }
    }

    validateOTP = (code, type) => {
        const regExpOTP = new RegExp(/^\d{4}$/);
        const isValidate = regExpOTP.test(code);
        if (helper.IsEmptyString(code)) {
            switch (type) {
                case 2:
                    Alert.alert("", translate('editSaleOrder.OTP_alert'));
                    break;
                case 1:
                    Alert.alert("", translate('shoppingCart.please_enter_id_code'));
                    break;
                default:
                    break;
            }
            return false;
        }
        if (!isValidate) {
            switch (type) {
                case 2:
                    Alert.alert("", translate('editSaleOrder.OTP_full_alert'));
                    break;
                case 1:
                    Alert.alert("", translate('shoppingCart.please_enter_4_digits_id_code'));
                    break;
                default:
                    break;
            }
            return false;
        }
        return true;
    }

    onCheckOTP = (otpCode, GiftVoucherCertifyType) => {
        Keyboard.dismiss();
        const isValidate = this.validateOTP(otpCode, GiftVoucherCertifyType);
        if (isValidate) {
            this.checkVerify(otpCode, GiftVoucherCertifyType);
        }
    }

    alertErrorCheckVeryfy = (msgError, value, GiftVoucherCertifyType) => {
        Alert.alert(
            translate('common.notification_uppercase'),
            msgError,
            [
                {
                    text: translate('common.btn_skip'),
                    style: 'cancel',
                    onPress: hideBlockUI
                },
                {
                    text: translate('common.btn_notify_try_again'),
                    style: 'default',
                    onPress: () =>
                        this.checkVerify(value, GiftVoucherCertifyType)
                }
            ]
        );
    }

    checkVerify = (value, GiftVoucherCertifyType) => {
        const { phoneCertifyPMH } = this.state;
        const { userInfo: { brandID } } = this.props

        showBlockUI();
        switch (GiftVoucherCertifyType) {
            case 2: {
                actionShoppingCartCreator.verifyOTP(value, phoneCertifyPMH)
                    .then((data) => {
                        this.applySMSVerify(value, data.customerId, this.oldImei, GiftVoucherCertifyType, true)
                        hideBlockUI();
                    })
                    .catch((msgError) => {
                        this.alertErrorCheckVeryfy(msgError, value, GiftVoucherCertifyType)
                    });
                break;
            }
            case 1: {
                actionShoppingCartCreator.verifyIdentify(value, "", phoneCertifyPMH, brandID)
                    .then(data => {
                        this.applySMSVerify(value, data.customerId, this.oldImei, GiftVoucherCertifyType, true)
                        hideBlockUI();
                    }).catch(msgError => {
                        this.alertErrorCheckVeryfy(msgError, value, GiftVoucherCertifyType)
                    });
                break;
            }
            default: {
                break;
            }

        }
    }

    applySMSVerify = (CertifyOTP, CustomerIDCertify, value, GiftVoucherCertifyType, IsVerifiedGiftVoucher) => {
        const { dataSaleOrder } = this.props;
        const newData = { ...dataSaleOrder }
        newData.GiftVoucherCertifyType = GiftVoucherCertifyType
        newData.IsVerifiedGiftVoucher = IsVerifiedGiftVoucher
        newData.CertifyOTP = CertifyOTP
        newData.CustomerIDCertify = CustomerIDCertify
        const { isOutput, isPayCash } = this.state;
        showBlockUI();
        this.props.actionSaleOrder
            .verifiedGiftVoucherIssue({
                saleOrder: newData,
                imei: value,
                imeiEncrypt: '',
                isOutput,
                isPayCash
            })
            .then((response) => {
                hideBlockUI();
                this.setState({ visibleOTPCusVoucher: false });
                const { dataSaleOrder: newDataSaleOrder } = this.props;
                if (newDataSaleOrder.GiftVoucherIssueRequests) {
                    const { GiftVoucherIssueRequests } = newDataSaleOrder;
                    const isWarning = GiftVoucherIssueRequests.some(
                        (voucher) => voucher.IsShowWarning
                    );
                    isWarning &&
                        Toast.show({
                            type: 'error',
                            text1: VoucherWarningMessage,
                            visibilityTime: 4500
                        });
                    const expressSale =
                        dataSaleOrder.IsSOAnKhang || dataSaleOrder.IsSOAVA;
                    if (expressSale) {
                        const { cashPayment } = this.state;
                        const lastIndex = GiftVoucherIssueRequests.length - 1;
                        const amount =
                            GiftVoucherIssueRequests[lastIndex]?.Amount ?? 0;
                        const newCashVND = Math.max(cashPayment - amount, 0);
                        newDataSaleOrder.CashVND = newCashVND;

                        this.modifySaleOrder(newDataSaleOrder);
                        this.setState({ cashPayment: newCashVND });
                    }
                }
            })
            .catch((error) => {
                const { msgError, errorType } = error
                if (errorType === -999) {
                    this.setState({ visiblePinCode: true })
                    this.oldImei = value
                    hideBlockUI()
                }
                else {
                    Alert.alert(translate('common.notification_uppercase'), msgError,
                        [
                            {
                                text: translate('common.btn_notify_try_again'),
                                style: "default",
                                onPress: () => this.applySMSVerify(CertifyOTP, CustomerIDCertify, value, GiftVoucherCertifyType, IsVerifiedGiftVoucher)
                            }
                        ]
                    )
                }
            });
    };

    applyGiftVoucher = (value) => {
        const { dataSaleOrder } = this.props;
        const { isOutput, isPayCash } = this.state;
        showBlockUI();
        this.props.actionSaleOrder
            .applyGiftVoucher({
                saleOrder: dataSaleOrder,
                imei: value,
                imeiEncrypt: '',
                isOutput,
                isPayCash
            })
            .then(async (response) => {
                let isAuthLoyalty = false;
                let phoneNumber = '';
                let isCheckedCertify = false;
                let lastIndex = -1;
                let isCertifyOTP = false;
                if (response.GiftVoucherIssueRequests) {
                    lastIndex =
                        response.GiftVoucherIssueRequests.length - 1 ?? -1;
                    if (lastIndex !== -1) {
                        const { CustomerPhoneCertify, IsCheckedCertify, IMEI, IsCertifyOTP } =
                            response.GiftVoucherIssueRequests[lastIndex];
                        phoneNumber = CustomerPhoneCertify;
                        isCheckedCertify = IsCheckedCertify;
                        isCertifyOTP = IsCertifyOTP;
                        this.oldImei = IMEI;
                        isAuthLoyalty = await storageHelper.checkVerifyLoyalty(
                            phoneNumber,
                            dataSaleOrder.CartID.trim()
                        );
                    }
                }
                // PMH loại yêu cầu xác thực Khách hàng và chưa được xác thực
                if (!isCheckedCertify && isCertifyOTP) {
                    if (!isAuthLoyalty) {
                        hideBlockUI();
                        this.setState({
                            visibleOTPCusVoucher: true,
                            phoneCertifyPMH: phoneNumber
                        });
                    } else {
                        const OTPType = 2;
                        const emptyCode = '';
                        const emptyCustomerId = '';

                        this.applySMSVerify(
                            emptyCode,
                            emptyCustomerId,
                            this.oldImei,
                            OTPType,
                            true
                        );
                    }
                } else {
                    hideBlockUI();
                    this.setState({ visiblePinCode: false });
                    this.oldImei = '';
                    const { dataSaleOrder: newDataSaleOrder } = this.props;
                    if (newDataSaleOrder.GiftVoucherIssueRequests) {
                        const isWarning =
                            newDataSaleOrder.GiftVoucherIssueRequests.some(
                                (voucher) => voucher.IsShowWarning
                            );

                        isWarning &&
                            Toast.show({
                                type: 'error',
                                text1: VoucherWarningMessage,
                                visibilityTime: 4500
                            });
                    }
                    const expressSale =
                        dataSaleOrder.IsSOAnKhang || dataSaleOrder.IsSOAVA;
                    if (expressSale) {
                        const { cashPayment } = this.state;
                        const amount =
                            newDataSaleOrder.GiftVoucherIssueRequests[lastIndex]
                                ?.Amount ?? 0;
                        const newCashVND = Math.max(cashPayment - amount, 0);
                        newDataSaleOrder.CashVND = newCashVND;

                        this.modifySaleOrder(newDataSaleOrder);
                        this.setState({ cashPayment: newCashVND });
                    }
                }
            })
            .catch((error) => {
                const { msgError, errorType } = error
                if (errorType === -999) {
                    this.setState({ visiblePinCode: true })
                    this.oldImei = value
                    hideBlockUI()
                }
                else {
                    Alert.alert('', msgError, [
                        {
                            text: 'OK',
                            style: 'default',
                            onPress: hideBlockUI
                        }
                    ]);
                }
            });
    };

    closeModalVerify = () => {
        this.setState({ visibleOTPCusVoucher: false, phoneCertifyPMH: '' })
        //delete PMH chưa xác thực
        const { dataSaleOrder } = this.props;
        const lstGiftVoucherIssueNew = dataSaleOrder.GiftVoucherIssueRequests.filter(x => x.IMEI != this.oldImei);
        dataSaleOrder.GiftVoucherIssueRequests = lstGiftVoucherIssueNew.length == 0 ? null : lstGiftVoucherIssueNew;
        dataSaleOrder.IsCertifyGiftVoucher = false;
        this.props.actionSaleOrder.stop_modify_saleorder_payment(dataSaleOrder);
        this.oldImei = "";
    }

    removeGiftVoucher = (dataVoucher, index) => () => {
        const { dataSaleOrder } = this.props;
        const newDataVoucher = dataVoucher.filter((ele, id) => id != index);
        const dataModifySaleOrder = helper.deepCopy(dataSaleOrder);
        dataModifySaleOrder.GiftVoucherIssueRequests = newDataVoucher;
        dataModifySaleOrder.IsVerifiedGiftVoucher = false
        // IsCertifyGiftVoucher
        dataModifySaleOrder.CertifyOTP = null
        dataModifySaleOrder.CustomerIDCertify = null
        const expressSale = dataSaleOrder.IsSOAnKhang || dataSaleOrder.IsSOAVA;
        if (expressSale) {
            const { cashPayment } = this.state;
            const amount = dataVoucher[index]?.Amount ?? 0;
            const newCashVND = Math.max(cashPayment + amount, 0);
            dataModifySaleOrder.CashVND = newCashVND;
            this.setState({ cashPayment: newCashVND });
        }
        // dataModifySaleOrder.CashVND = 0;
        // this.setState({ cashPayment: 0 });
        this.modifySaleOrder(dataModifySaleOrder);
    };

    applyMoneyCard = (dataCard, value) => {
        const { cashPayment } = this.state;
        const { dataSaleOrder } = this.props;
        const newDataCard = helper.deepCopy(dataCard);
        const dataModifySaleOrder = helper.deepCopy(dataSaleOrder);
        newDataCard.push(value);
        dataModifySaleOrder.ApplyMoneyCardDetails = newDataCard;
        const expressSale = dataSaleOrder.IsSOAnKhang || dataSaleOrder.IsSOAVA;
        if (cashPayment > 0 && expressSale) {
            const newCashVND = Math.max(cashPayment - value.MoneyCard, 0);
            dataModifySaleOrder.CashVND = newCashVND;
            this.setState({ cashPayment: newCashVND });
        }
        this.modifySaleOrder(dataModifySaleOrder);
    };

    getTokenMpos = (info) => {
        const {
            dataSaleOrder: { SaleOrderID },
            userInfo: { storeID }
        } = this.props;
        const data = {
            saleOrderID: SaleOrderID,
            storeId: storeID,
            amount: info.MoneyCard
        };
        showBlockUI();
        actionSaleOrderCreator
            .getTokenPaymentMpos(data)
            .then((token) => {
                hideBlockUI();
                this.onLinkingXMPOS(token);
            })
            .catch((msgError) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: 'OK',
                            onPress: hideBlockUI
                        }
                    ]
                );
            });
    };

    onLinkingXMPOS = (token) => {
        const urlXMPOS = 'xmpos://xmpos/token/';
        Linking.canOpenURL(urlXMPOS).then((supported) => {
            if (supported) {
                Linking.openURL(`${urlXMPOS}${token}`);
            } else {
                Alert.alert('', translate('saleOrderPayment.please_use_XMPOS'));
            }
        });
    };

    removeMoneyCard = (dataCard, index) => () => {
        const { dataSaleOrder } = this.props;
        const newDataCard = dataCard.filter((ele, id) => id != index);
        const dataModifySaleOrder = helper.deepCopy(dataSaleOrder);
        dataModifySaleOrder.ApplyMoneyCardDetails = newDataCard;
        const expressSale = dataSaleOrder.IsSOAnKhang || dataSaleOrder.IsSOAVA;
        if (expressSale) {
            const { cashPayment } = this.state;
            const moneyCard = dataCard[index]?.MoneyCard ?? 0;
            const newCashVND = Math.max(cashPayment + moneyCard, 0);
            dataModifySaleOrder.CashVND = newCashVND;
            this.setState({ cashPayment: newCashVND });
        }
        // dataModifySaleOrder.CashVND = 0;
        // this.setState({ cashPayment: 0 });
        this.modifySaleOrder(dataModifySaleOrder);
    };

    updateDataLoyalty = (data) => {
        const { dataSaleOrder, actionSaleOrder } = this.props;
        const dataModifySaleOrder = helper.deepCopy(dataSaleOrder);
        dataModifySaleOrder.LoyaltyInfo = data;
        // dataModifySaleOrder.CashVND = 0;
        // this.setState({ cashPayment: 0 });
        const { cashPayment } = this.state;
        const existLoyaltyPoint = data.LoyaltyPoint > 0
        const mainPoint = existLoyaltyPoint ? -data.LoyaltyPoint : dataModifySaleOrder.TotalPointLoyalty;
        const newCashVND = existLoyaltyPoint ? Math.max(cashPayment + mainPoint, 0) : Math.max(cashPayment + mainPoint, 0);
        dataModifySaleOrder.CashVND = newCashVND;
        this.setState({ cashPayment: newCashVND });
        this.modifySaleOrder(dataModifySaleOrder);
        // Reset loyalty transaction status
        actionSaleOrder.setLoyaltyStatus(TRANSACTION_STATUS.UNKNOWN);
        this.modifySaleOrder(dataModifySaleOrder);
    };

    setKeyboardTaps = () => {
        this.setState({ keyboardTaps: 'never', isFocusCash: true });
    };

    onBlurInputCash = (dataSaleOrder, cashPayment) => () => {
        const { CashVND } = dataSaleOrder;
        this.setState({ keyboardTaps: 'always', isFocusCash: false });
        if (cashPayment != CashVND) {
            const dataModifySaleOrder = helper.deepCopy(dataSaleOrder);
            dataModifySaleOrder.CashVND = cashPayment;
            this.updateCashPayment(dataModifySaleOrder, CashVND);
        }
    };

    updateCashPayment = (data, resetValue) => {
        const { isOutput, isPayCash } = this.state;
        showBlockUI();
        this.props.actionSaleOrder
            .modifySaleOrderPayment({
                saleOrder: data,
                isOutput,
                isPayCash
            })
            .then((success) => {
                hideBlockUI();
            })
            .catch((error) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    error.msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: 'default',
                            onPress: () => {
                                hideBlockUI();
                                this.setState({ cashPayment: resetValue });
                            }
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: 'default',
                            onPress: () => {
                                this.updateCashPayment(data, resetValue);
                            }
                        }
                    ]
                );
            });
    };

    onChangOutput = (value) => {
        const { dataSaleOrder } = this.props;
        const { isPayCash } = this.state;
        this.updateOutputPayCash({
            saleOrder: dataSaleOrder,
            isOutput: value,
            isPayCash,
            isCustomer: false
        });
    };

    onChangPayCash = (value) => {
        const { dataSaleOrder } = this.props;
        const { isOutput } = this.state;
        this.updateOutputPayCash({
            saleOrder: dataSaleOrder,
            isPayCash: value,
            isOutput
        });
    };

    updateOutputPayCash = (data) => {
        showBlockUI();
        this.props.actionSaleOrder
            .modifySaleOrderPayment(data)
            .then((success) => {
                hideBlockUI();
                this.setState({
                    isPayCash: data.isPayCash,
                    isOutput: data.isOutput
                });
            })
            .catch((error) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    error.msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: 'default',
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: 'default',
                            onPress: () => {
                                this.updateOutputPayCash(data);
                            }
                        }
                    ]
                );
            });
    };

    onCheckSaleOrder = () => {
        const {
            isFocusCash
        } = this.state;
        if (isFocusCash) {
            this.handleModifyOrder()
        }
        else {
            this.handleSaleOrder()
        }
    };
    handleSaleOrder = () => {
        const {
            reportRetail,
            reportVAT,
            reportCommon,
            contractID,
        } = this.state;
        const { dataSaleOrder } = this.props;
        const { EPOSTransactionID, IsInstallment } = dataSaleOrder;
        const IsAutoCreateEP = helper.IsNonEmptyString(EPOSTransactionID);
        const isRequireContract = IsInstallment && !IsAutoCreateEP;
        const isContract = helper.IsNonEmptyString(contractID);
        const isRetail = !helper.IsEmptyObject(reportRetail);
        const isVat = !helper.IsEmptyObject(reportVAT);
        const isCommon = !helper.IsEmptyObject(reportCommon);
        const isReport = isRetail || isVat || isCommon;
        if (!isReport) {
            Alert.alert(
                '',
                translate('saleOrderPayment.please_choose_printer')
            );
        }
        // else if (isRequireContract && !isContract) {
        //     Alert.alert("", "Vui lòng nhập mã hợp đồng");
        // }
        else {
            this.onCompleteSaleOrder(isRequireContract);
        }
    }

    handleModifyOrder = async () => {
        const {
            isOutput,
            cashPayment,
            isPayCash
        } = this.state;
        const { dataSaleOrder } = this.props;
        showBlockUI();
        try {
            const { CashVND } = dataSaleOrder;
            this.setState({ keyboardTaps: 'always', isFocusCash: false });
            if (cashPayment != CashVND) {
                const dataModifySaleOrder = helper.deepCopy(dataSaleOrder);
                dataModifySaleOrder.CashVND = cashPayment;
                const newdataSaleOrder = await this.props.actionSaleOrder
                    .modifySaleOrderPayment({
                        saleOrder: dataModifySaleOrder,
                        isOutput,
                        isPayCash
                    });
                if (!helper.IsEmptyObject(newdataSaleOrder)) {
                    this.handleSaleOrder()
                }
                else {
                    hideBlockUI()
                }
            }
        } catch (error) {
            Alert.alert(
                translate('common.notification_uppercase'),
                error.msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: 'default',
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: 'default',
                        onPress: () => {
                            this.handleModifyOrder();
                        }
                    }
                ]
            );
        }
    };



    onCompleteSaleOrder = (isRequireContract, packagingBags = []) => {
        const {
            isOutput,
            isPayCash,
            mainProducts,
            saleProducts,
            giftProducts,
            keyGiftRemove,
            saleProgramInfo,
            isCustomer,
            batchesBySOD
        } = this.state;
        this.setState({ packagings: packagingBags });
        const { dataSaleOrder, userInfo } = this.props;
        const { SaleOrderSaleProgramInfo, IsSOAnKhang, StaffUser, IsSOAVA } =
            dataSaleOrder;
        const dataApply = helper.deepCopy(dataSaleOrder);
        const dataGiftProduct = getGiftPromotion(giftProducts, keyGiftRemove);
        const expressSale = IsSOAnKhang || IsSOAVA;

        if (expressSale) {
            mainProducts.forEach((product) => {
                product.cus_SaleOrderDetailInfoBOList =
                    batchesBySOD[product.SaleOrderDetailID] ?? null;
            });
            dataGiftProduct.forEach((product) => {
                product.cus_SaleOrderDetailInfoBOList =
                    batchesBySOD[product.SaleOrderDetailID] ?? null;
            });
            saleProducts.forEach((product) => {
                product.cus_SaleOrderDetailInfoBOList =
                    batchesBySOD[product.SaleOrderDetailID] ?? null;
            });
        }
        dataApply.listMainProduct = [...mainProducts];
        dataApply.listSalePromotion = [...saleProducts];
        dataApply.listGiftPromotion = [...dataGiftProduct];
        if (isRequireContract) {
            const newSaleProgramInfo = {
                ...SaleOrderSaleProgramInfo,
                ...saleProgramInfo
            };
            dataApply.SaleOrderSaleProgramInfo = newSaleProgramInfo;
        }
        if (userInfo.userName != StaffUser) {
            Alert.alert(
                '',
                translate('saleOrderPayment.you_want_finish_order'),
                [
                    {
                        text: translate('saleOrderPayment.btn_skip_uppercase'),
                        style: 'cancel'
                    },
                    {
                        text: translate('saleOrderPayment.btn_continue'),
                        style: 'default',
                        onPress: () =>
                            this.createOuputPaymentVoucher({
                                dataSaleOrder: dataApply,
                                isOutput,
                                isPayCash,
                                isCustomer,
                                packagings: packagingBags
                            })
                    }
                ]
            );
        } else {
            this.createOuputPaymentVoucher({
                dataSaleOrder: dataApply,
                isOutput,
                isPayCash,
                isCustomer,
                packagings: packagingBags
            });
        }
    };

    createOuputPaymentVoucher = (info) => {
        showBlockUI();
        this.props.actionSaleOrder
            .createVoucherOVCM(info)
            .then((data) => {
                this.handleResponse(data);
            })
            .catch((error) => {
                hideBlockUI();
                const { msgError, errorData } = error;
                if (errorData) {
                    this.setState({
                        isVisibleAlert: true,
                        loyaltyTransaction: {
                            status: errorData.status,
                            amount: errorData.loyaltyPoint,
                            createdAt: errorData.createdDate,
                            message: errorData.message
                        }
                    });
                } else {
                    Alert.alert("", msgError, [
                        {
                            text: "OK",
                            style: "default"
                        }
                    ]);
                }
            });
        // showBlockUI();
        // helper.checkWifiInternal().then(isInternal => {
        //     if (isInternal) {
        //         this.props.actionSaleOrder.createVoucherOVCM(info)
        //             .then(data => {
        //                 this.handleResponse(data);
        //             }).catch(error => {
        //                 Alert.alert("", error.msgError, [
        //                     {
        //                         text: "OK",
        //                         style: "default",
        //                         onPress: hideBlockUI
        //                     }
        //                 ]);
        //             });
        //     }
        //     else {
        //         Alert.alert("", translate('saleOrderPayment.internal_wifi_needed'), [
        //             {
        //                 text: "OK",
        //                 style: "default",
        //                 onPress: hideBlockUI
        //             }
        //         ]);
        //     }
        // })
    };

    handleResponse = (data) => {
        const { SaleOrderBO, msgObject } = data;
        const { isOutput } = this.state;
        if (helper.IsNonEmptyArray(msgObject)) {
            const lastIndex = msgObject.length - 1;
            this.showAlertNotify(msgObject, SaleOrderBO, lastIndex);
        } else if (isOutput) {
            checkShowPackagingBagScreen({ "saleOrder": SaleOrderBO, "orderTypeID": SaleOrderBO.orderTypeID }).then((IsShowPackagingScreen) => {
                if (IsShowPackagingScreen) {
                    hideBlockUI();
                    this.props.navigation.navigate('PackagingBag', {
                        saleOrderID: SaleOrderBO.saleOrderID,
                        outputStoreID: SaleOrderBO.outputStoreID,
                        shouldGoBack: true,
                        onFinish: () => this.showAlertContent(data)
                    });
                } else {
                    this.showAlertContent(data);
                }
            });
        } else {
            this.showAlertContent(data);
        }
    };


    showAlertNotify = (arrayMessage, SaleOrderBO, lastIndex, index = 0) => {
        const { content } = arrayMessage[index];
        const { isOutput, isPayCash, isCustomer, packagings } = this.state;
        if (index < lastIndex) {
            const nextIndex = index + 1;
            Alert.alert(translate('common.notification_uppercase'), content, [
                {
                    text: translate('saleOrderPayment.btn_skip_uppercase'),
                    onPress: hideBlockUI
                },
                {
                    text: translate('saleOrderPayment.btn_continue'),
                    onPress: () =>
                        this.showAlertNotify(
                            arrayMessage,
                            SaleOrderBO,
                            lastIndex,
                            nextIndex
                        )
                }
            ]);
        } else {
            Alert.alert(translate('common.notification_uppercase'), content, [
                {
                    text: translate('saleOrderPayment.btn_skip_uppercase'),
                    onPress: hideBlockUI
                },
                {
                    text: translate('saleOrderPayment.btn_continue'),
                    onPress: () =>
                        this.createOuputPaymentVoucher({
                            dataSaleOrder: SaleOrderBO,
                            isOutput,
                            isPayCash,
                            isCustomer,
                            packagings
                        })
                }
            ]);
        }
    };

    showAlertContent = (data) => {
        let content = '';
        const { isPrintDosage, isPrintBatchNo } = this.state;
        const { dataSaleOrder } = this.props;
        const { cus_IsPrintDosageContent, IsSOAnKhang } = dataSaleOrder;
        const {
            SaleOrderBO: {
                NewInOutVoucherID,
                NewOutputVoucherID,
                TypeSendByRefundPoint,
                PointRefund,
                cmOutVoucherDeposit,
                saleOrderID
            },
            eBillContentNumberOfCopy,
            eBillContentIncomeNumberOfCopy,
            giftVCIssueContentNumberOfCopy,
            outTransContentNumberOfCopy,
            MoneyBankTransferRefundInfo
        } = data;
        const reportInfo = [];
        if (helper.IsNonEmptyString(NewInOutVoucherID)) {
            content += `${translate(
                'saleOrderPayment.create_receipts'
            )} ${NewInOutVoucherID}`;
        }
        if (helper.IsNonEmptyString(NewOutputVoucherID)) {
            content += `${content ? '\n' : ''}${translate(
                'saleOrderPayment.create_bill'
            )} ${NewOutputVoucherID}`;
        }
        if (helper.IsNonEmptyString(cmOutVoucherDeposit)) {
            content += `${content ? '\n' : ''}${translate(
                'saleOrderPayment.create_payment'
            )} ${cmOutVoucherDeposit}`;
        }
        if (helper.IsNonEmptyString(MoneyBankTransferRefundInfo)) {
            content += `\n${MoneyBankTransferRefundInfo}`;
        }
        if (TypeSendByRefundPoint == 1) {
            content += `${translate(
                'saleOrderPayment.extra_points'
            )} ${helper.convertNum(PointRefund)} ${translate(
                'saleOrderPayment.pay_vip_account'
            )}`;
        }
        if (TypeSendByRefundPoint == 2) {
            content += `${translate(
                'saleOrderPayment.customers_contact_manager_report_card'
            )} ${helper.convertNum(PointRefund)}`;
        }
        if (eBillContentNumberOfCopy == 1) {
            reportInfo.push({ ReportContent: 'EBillContent' });
        }
        if (eBillContentIncomeNumberOfCopy == 1) {
            reportInfo.push({ ReportContent: 'EBillContentIncome' });
        }
        if (giftVCIssueContentNumberOfCopy == 1) {
            reportInfo.push({ ReportContent: 'GiftVCIssueContentPrint' });
        }
        if (outTransContentNumberOfCopy == 1) {
            reportInfo.push({ ReportContent: 'KeySoftwareContent' });
        }
        if (cus_IsPrintDosageContent && isPrintDosage) {
            reportInfo.push({ ReportContent: 'DosageContent' });
        }
        if (IsSOAnKhang && isPrintBatchNo) {
            reportInfo.push({ ReportContent: 'InfoBatchNOContent' });
        }
        // Alert.alert("", content, [
        //     {
        //         text: translate('saleOrderPayment.btn_skip_uppercase'),
        //         style: "default",
        //         onPress: this.goBack
        //     },
        //     {
        //         text: translate('saleOrderPayment.btn_continue_print'),
        //         style: "default",
        //         onPress: this.onCheckPrintContent({
        //             "saleOrderID": saleOrderID,
        //             "reportContents": reportInfo
        //         })
        //     }
        // ]);
        this.onCheckPrintContent({
            saleOrderID,
            reportContents: reportInfo
        });
    };

    onCheckPrintContent = (data) => {
        data.isFitContent = true;
        data.isGetContentHTML = true;
        data.isPublishEBill = true;
        this.getContentHtml(data);
        this.dataPrint = data;
    };

    getContentBase64PDF = (reportInfo) => {
        this.props.actionSaleOrder
            .getReportContentBase64(reportInfo)
            .then(this.onPrintBill)
            .catch((msgError) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: translate(
                                'saleOrderPayment.btn_skip_uppercase'
                            ),
                            style: 'default',
                            onPress: this.showEbillQTV
                        },
                        {
                            text: translate(
                                'saleOrderPayment.btn_retry_uppercase'
                            ),
                            style: 'default',
                            onPress: () => this.getContentBase64PDF(reportInfo)
                        }
                    ]
                );
            });
    };

    onPrintBill = (data) => {
        const requestAPI = this.getPrintRequestAPI(data);
        if (helper.IsNonEmptyArray(requestAPI)) {
            this.printAllRequest(requestAPI);
        } else {
            this.goBack();
        }
    };

    getPrintRequestAPI = (data) => {
        const {
            dataSO: { SaleOrderID }
        } = this.props;
        const requestAPI = [];
        const {
            EBillContent,
            eBillContentPrinterTypeID,

            GiftVCIssueContentPrint,
            giftVCIssueContentPrinterTypeID,

            VATContentPrint,
            vatContentPrinterTypeID,
            vatContentNumberOfCopy,

            imeiListContentPrint,
            imeiListContentPrinterTypeID,
            imeiListContentNumberOfCopy,

            EBillContentIncome,
            eBillContentIncomePrinterTypeID,

            OutTransContent,
            outTransContentPrinterTypeID,

            DosageContent,
            dosageContentPrinterTypeID,

            infoBatchNOContent,
            infoBatchNOContentPrinterTypeID
        } = data;
        if (helper.IsNonEmptyString(EBillContent)) {
            const report = this.getReport(eBillContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
                const printService = this.getPrintService(report, EBillContent);
                requestAPI.push(printService);
            }
        }
        if (helper.IsNonEmptyString(GiftVCIssueContentPrint)) {
            const report = this.getReport(giftVCIssueContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
                const printService = this.getPrintService(
                    report,
                    GiftVCIssueContentPrint
                );
                requestAPI.push(printService);
                actionSaleOrderCreator.insertLogPrintGift(SaleOrderID);
            }
        }
        if (helper.IsNonEmptyString(VATContentPrint)) {
            const report = this.getReport(vatContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
                const printService = this.getPrintService(
                    report,
                    VATContentPrint
                );
                requestAPI.push(printService);
            }
        }
        if (helper.IsNonEmptyString(imeiListContentPrint)) {
            const report = this.getReport(imeiListContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
                const printService = this.getPrintService(
                    report,
                    imeiListContentPrint
                );
                requestAPI.push(printService);
            }
        }
        if (helper.IsNonEmptyString(EBillContentIncome)) {
            const report = this.getReport(eBillContentIncomePrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
                const printService = this.getPrintService(
                    report,
                    EBillContentIncome
                );
                requestAPI.push(printService);
            }
        }
        if (helper.IsNonEmptyString(OutTransContent)) {
            const report = this.getReport(outTransContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
                const printService = this.getPrintService(
                    report,
                    OutTransContent
                );
                requestAPI.push(printService);
            }
        }
        if (helper.IsNonEmptyString(DosageContent)) {
            const report = this.getReport(dosageContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
                const printService = this.getPrintService(
                    report,
                    DosageContent
                );
                requestAPI.push(printService);
            }
        }
        if (helper.IsNonEmptyString(infoBatchNOContent)) {
            const report = this.getReport(infoBatchNOContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
                const printService = this.getPrintService(
                    report,
                    infoBatchNOContent
                );
                requestAPI.push(printService);
            }
        }
        return requestAPI;
    };

    getPrintService = (report, content) => {
        const printerConfig = {
            strPrinterName: report.PRINTERNAME,
            strPaperSize: report.PAPERSIZE,
            paperwidth: report.PAPERWIDTH,
            parperheight: report.PARPERHEIGHT,
            intCopyCount: 1,
            bolIsDuplex: false,
            bolShrinkToMargin: false,
            strBase64: content
        };
        if (report.REPORTID == 2820) {
            printerConfig.strPaperSize = 'A4 210 x 297 mm';
        }
        let formBody = [];
        for (const property in printerConfig) {
            const encodedKey = encodeURIComponent(property);
            const encodedValue = encodeURIComponent(printerConfig[property]);
            formBody.push(`${encodedKey}=${encodedValue}`);
        }
        formBody = formBody.join('&');
        return new Promise((resolve, reject) => {
            actionSaleOrderCreator
                .printBillVoucher(formBody)
                .then((result) => {
                    resolve(result);
                })
                .catch((msgError) => {
                    reject(msgError);
                });
        });
    };

    printAllRequest = (allPromise) => {
        Promise.all(allPromise)
            .then((result) => {
                console.log('PRINT RSULT', result);
                this.showEbillQTV();
            })
            .catch((msgError) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: 'OK',
                            style: 'default',
                            onPress: this.showEbillQTV
                        }
                    ]
                );
            });
    };

    getReport = (type) => {
        const { reportRetail, reportVAT, reportCommon } = this.state;
        switch (type) {
            case 'InvoiceRetailPrinter':
                return reportRetail;
            case 'VATContentPrint':
                return reportVAT;
            default:
                // CommonPrinter
                return reportCommon;
        }
    };

    showEbillQTV = () => {
        if (this.ebillQTV.size > 0) {
            let content = "";
            this.ebillQTV.forEach(ele => {
                if (helper.IsNonEmptyString(content)) {
                    content += ', ';
                }
                content += REPORT_NAME[ele];
            })
            Alert.alert("Thông báo", `${content} đã được gửi qua app Quà Tặng VIP cho khách hàng.\nVui lòng hướng dẫn khách hàng mở app Quà Tặng VIP để kiểm tra.`, [{
                onPress: this.goBack
            }]);
        }
        else {
            this.goBack();
        }
    }

    goBack = async () => {
        // let medicines = {};
        // if (this.state.isOutput) {
        //     medicines = await this.props.actionAnKhangNew.getMedicineRegularly(this.props.dataSaleOrder);
        // }
        // if (!helper.IsEmptyObject(medicines)) {
        //     this.props.navigation.navigate(SCREENS.BuyMedicineRegularlyScreenName, { screenNameGoBack: "Pharmacy", data: medicines });
        // }
        // else {
        //     this.props.navigation.navigate('Pharmacy');
        // }
        this.props.navigation.navigate('Pharmacy');
        hideBlockUI();
    };

    getContractInfo = (data, TotalPrePaid) => {
        showBlockUI();
        this.props.actionSaleOrder
            .getContractInfo(data)
            .then((info) => {
                const saleProgramInfo = {
                    ContractID: info.contactID,
                    PGProcessUserID: info.pgProcessUserID,
                    PGProcessUserName: info.pgProcessUserName,
                    packageRates: info.packageRates,
                    TotalPrePaid: data.isCardPartner
                        ? TotalPrePaid
                        : info.totalPrePaid,
                    TermLoan: info.termLoan,
                    PaymentAmountMonthly: info.paymentAmountMonthly,
                    CustomerName: info.customerName,
                    CustomerAddress: info.customerAddress,
                    customerPhone: info.customerPhone,
                    customerIDCard: info.customerIDCard
                };
                this.applyContractInfo(saleProgramInfo);
            })
            .catch((msgError) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: translate(
                                'saleOrderPayment.btn_skip_uppercase'
                            ),
                            onPress: () => {
                                hideBlockUI();
                                this.setState({
                                    contractID: '',
                                    keyboardTaps: 'always'
                                });
                            }
                        },
                        {
                            text: translate(
                                'saleOrderPayment.btn_retry_uppercase'
                            ),
                            onPress: () => this.getContractInfo(data)
                        }
                    ]
                );
            });
    };

    applyContractInfo = (saleProgramInfo) => {
        const { isOutput, isPayCash } = this.state;
        const { dataSaleOrder, actionSaleOrder } = this.props;
        const { SaleOrderSaleProgramInfo } = dataSaleOrder;
        const newSaleProgramInfo = {
            ...SaleOrderSaleProgramInfo,
            ...saleProgramInfo
        };
        const dataModifySaleOrder = helper.deepCopy(dataSaleOrder);
        dataModifySaleOrder.SaleOrderSaleProgramInfo = newSaleProgramInfo;
        actionSaleOrder
            .modifySaleOrderPayment({
                saleOrder: dataModifySaleOrder,
                isOutput,
                isPayCash
            })
            .then((success) => {
                hideBlockUI();
                this.setState({
                    saleProgramInfo,
                    keyboardTaps: 'always'
                });
            })
            .catch((error) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    error.msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: 'default',
                            onPress: () => {
                                hideBlockUI();
                                this.setState({
                                    contractID: '',
                                    keyboardTaps: 'always'
                                });
                            }
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: 'default',
                            onPress: () =>
                                this.applyContractInfo(saleProgramInfo)
                        }
                    ]
                );
            });
    };

    applyTransaction = (transaction, dataTransaction) => {
        const { dataSaleOrder, actionSaleOrder } = this.props;
        const dataModifySaleOrder = helper.deepCopy(dataSaleOrder);
        dataModifySaleOrder.PaymentTransactions = [transaction];
        // dataModifySaleOrder.CashVND = 0;
        // this.setState({ cashPayment: 0 });
        actionSaleOrder.updateDataTransaction(dataTransaction);
        this.modifySaleOrder(dataModifySaleOrder);
    };

    createQR = async (data) => {
        const { dataSaleOrder, actionSaleOrder, navigation } = this.props;
        const { isOutput, isPayCash, cashPayment } = this.state;
        const { storeID, languageID, moduleID } = this.props.userInfo;
        const { CustomerPhone, IsSOAnKhang, IsSOAVA } = dataSaleOrder;

        try {
            showBlockUI();

            const baseBody = {
                loginStoreId: storeID,
                languageID,
                moduleID,
            };
            if (!helper.IsNonEmptyString(CustomerPhone) && data.transactionType?.PaymentTransactionTypeID == 13) {
                return Alert.alert("", "Đơn hàng không có SĐT không thể thanh toán BUYNOW PAYLATER. Vui lòng liên hệ NH!", [{ text: "OK", onPress: hideBlockUI }]);
            }
            if (!this.extraDataGenQR.current && helper.IsNonEmptyString(CustomerPhone) && data.transactionType?.PaymentTransactionTypeID == 13) {
                const profile = await actionShoppingCartCreator.getCustomerProfile({ ...baseBody, phoneNumber: CustomerPhone, typeProfile: TYPE_PROFILE.CUSTOMER })
                if (!profile[0]?.profileId) {
                    return Alert.alert("", "Đơn hàng không có profile không được phép thanh toán", [{ text: "OK", onPress: hideBlockUI }]);
                }
                const body = {
                    ...baseBody,
                    "paymentTransactionTypeID": data.transactionType?.PaymentTransactionTypeID,
                    "customerID": profile[0]?.profileId
                }
                const extraData = await actionSaleOrderCreator.checkSendOTPGenQRCode(body);
                if (!helper.IsEmptyObject(extraData)) {
                    hideBlockUI();
                    this.extraDataGenQR.current = extraData;
                    this.dataTempCreateQR.current = data;
                    this.OTPSheetRef.current?.present();
                    return;
                }
            }

            if (!helper.IsEmptyObject(this.extraDataGenQR.current)) {
                data.extraData = this.extraDataGenQR.current;
            }

            const qrInfo = await actionSaleOrder.getDataQRPayment(data);
            const { cus_CountIdentifyID, cus_IdentifyID } = qrInfo;

            if (cus_CountIdentifyID <= 1) {
                hideBlockUI();

                if (IsSOAnKhang || IsSOAVA) {
                    const updatedSaleOrder = {
                        ...dataSaleOrder,
                        CashVND: Math.max(cashPayment - data.amount, 0),
                    };

                    this.setState({ cashPayment: updatedSaleOrder.CashVND });
                    this.modifySaleOrder(updatedSaleOrder, true);
                }

                navigation.navigate("QRPayment", { isOutput, isPayCash });
            } else {
                const newData = {
                    ...data,
                    transactionType: {
                        ...data.transactionType,
                        cus_IdentifyID,
                    },
                };

                Alert.alert(
                    "",
                    `Đơn hàng đang tạo có ${cus_CountIdentifyID} mã khuyến mãi khi quét Qrcode ${newData.transactionType.CustomerDisplayName} nhưng chỉ áp dụng được 1 mã khuyến mãi cho 1 đơn, bạn có muốn tiếp tục tạo?`,
                    [
                        {
                            text: "YES",
                            onPress: () => this.createQR(newData),
                        },
                        {
                            text: "NO",
                            onPress: hideBlockUI,
                        },
                    ]
                );
            }
        } catch (error) {
            hideBlockUI();
            Alert.alert("", error || "Đã có lỗi xảy ra. Vui lòng thử lại!", [
                { text: "OK", onPress: hideBlockUI },
            ]);
        }
    };


    getQRInfo = (info) => {
        const { isOutput, isPayCash } = this.state;
        this.props.actionSaleOrder.getQRInfo(info).then((success) => {
            this.props.navigation.navigate('QRPayment', {
                isOutput,
                isPayCash
            });
        });
    };

    getDataPhoneVoucher = () => {
        const {
            dataSaleOrder: { CustomerPhone, SaleOrderID }
        } = this.props;
        showBlockUI();
        this.props.actionSaleOrder
            .getDataPhoneVoucher({
                customerPhone: CustomerPhone,
                saleOrderID: SaleOrderID
            })
            .then((data) => {
                hideBlockUI();
                this.setState({
                    dataPhoneVoucher: data,
                    isVisibleVoucher: true
                });
            })
            .catch((msgError) => {
                Alert.alert('', msgError, [
                    {
                        text: 'OK',
                        style: 'default',
                        onPress: hideBlockUI
                    }
                ]);
            });
    };

    showBlock = () => {
        this.setState({ isBlockUI: true });
    };

    hideBlock = () => {
        this.setState({ isBlockUI: false });
    };

    setVerify = () => {
        this.setState({ isVerify: true });
    };

    applyEncryptVoucher = (info) => () => {
        const { dataSaleOrder } = this.props;
        const { isOutput, isPayCash } = this.state;
        this.setState({ isBlockUI: true });
        this.props.actionSaleOrder
            .applyGiftVoucher({
                saleOrder: dataSaleOrder,
                imei: '',
                imeiEncrypt: info.IMEIEncrypt,
                isOutput,
                isPayCash
            })
            .then((success) => {
                this.setState({
                    isBlockUI: false,
                    isVisibleVoucher: false
                });
            })
            .catch((msgError) => {
                Alert.alert('', msgError, [
                    {
                        text: 'OK',
                        style: 'default',
                        onPress: () => {
                            this.setState({ isBlockUI: false });
                        }
                    }
                ]);
            });
    };

    /*  */
    printAllRequestFW = async (allPromise) => {
        try {
            for (const body of allPromise) {
                await actionSaleOrderCreator.printBillVoucherBit(body);
                await helper.sleep(1000);
            }
            this.goBack();
        } catch (msgError) {
            Alert.alert(translate('common.notification'), msgError, [
                {
                    text: 'OK',
                    style: 'default',
                    onPress: this.showEbillQTV
                }
            ]);
        }
    };

    printAllRequestSocket = async (allPromise) => {
        try {
            for (const { data, ip, delay } of allPromise) {
                await printSocket(data, ip);
                if (delay > 0) {
                    await helper.sleep(delay);
                }
            }
            this.goBack();
        } catch (msgError) {
            Alert.alert(translate('common.notification'), msgError, [
                {
                    text: 'OK',
                    style: 'default',
                    onPress: this.showEbillQTV
                }
            ]);
        }
    };

    getContentHtml = (info) => {
        showBlockUI();
        this.props.actionSaleOrder
            .getReportContentBase64(info)
            .then((data) => {
                this.ebillQTV = new Set(data?.reportSendedLoyalty);
                if (this.ebillQTV.size > 0) {
                    info.reportContents = info.reportContents.filter(ele => !this.ebillQTV.has(ele.ReportContent))
                }
                this.onConvertHTML(
                    data,
                    info.reportContents,
                    info.isFitContent
                );
            })
            .catch((msgError) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: translate(
                                'saleOrderPayment.btn_skip_uppercase'
                            ),
                            style: 'default',
                            onPress: this.goBack
                        },
                        {
                            text: translate(
                                'saleOrderPayment.btn_retry_uppercase'
                            ),
                            style: 'default',
                            onPress: () => this.getContentHtml(info)
                        }
                    ]
                );
            });
    };

    requestConvertHtml = async (data, reportContents) => {
        try {
            const requestConvert = [];
            const {
                EBillContentHTML,
                GiftVCIssueContentPrintHTML,
                EBillContentIncomeHTML,
                OutTransContentHTML,
                DosageContentHTML,
                InfoBatchNOContentHTML
            } = data;
            for (const ele of reportContents) {
                const { ReportContent } = ele;
                switch (ReportContent) {
                    case 'EBillContent':
                        if (helper.IsNonEmptyString(EBillContentHTML)) {
                            const eBillContent = await convertHtml2Image(
                                EBillContentHTML,
                                H_BILL
                            );
                            requestConvert.push([eBillContent]);
                        }
                        break;
                    case 'EBillContentIncome':
                        if (helper.IsNonEmptyString(EBillContentIncomeHTML)) {
                            const eBillContentIncome = await convertHtml2Image(
                                EBillContentIncomeHTML,
                                H_BILL
                            );
                            requestConvert.push([eBillContentIncome]);
                        }
                        break;
                    case 'GiftVCIssueContentPrint':
                        if (
                            helper.IsNonEmptyString(GiftVCIssueContentPrintHTML)
                        ) {
                            const giftData =
                                GiftVCIssueContentPrintHTML.split(
                                    '<br /><br />'
                                );
                            const giftVCIssueContentPrint = [];
                            for (const giftHtml of giftData) {
                                const giftContent = await convertHtml2Image(
                                    giftHtml,
                                    H_VOUCHER
                                );
                                giftVCIssueContentPrint.push(giftContent);
                            }
                            requestConvert.push(giftVCIssueContentPrint);
                        }
                        break;
                    case 'KeySoftwareContent':
                        if (helper.IsNonEmptyString(OutTransContentHTML)) {
                            const keySoftwareContent = await convertHtml2Image(
                                OutTransContentHTML,
                                H_KEY
                            );
                            requestConvert.push([keySoftwareContent]);
                        }
                        break;
                    case 'DosageContent':
                        if (helper.IsNonEmptyString(DosageContentHTML)) {
                            const dosageContent = await convertHtml2Image(
                                DosageContentHTML,
                                H_VOUCHER
                            );
                            requestConvert.push([dosageContent]);
                        }
                        break;
                    case 'InfoBatchNOContent':
                        if (helper.IsNonEmptyString(InfoBatchNOContentHTML)) {
                            const batchContent = await convertHtml2Image(
                                InfoBatchNOContentHTML,
                                H_VOUCHER
                            );
                            requestConvert.push([batchContent]);
                        }
                        break;
                    default:
                        console.log(ele);
                        break;
                }
            }
            return requestConvert;
        } catch (error) {
            console.log('convertHtml2Image', error);
            Alert.alert('', 'Quá trình in lỗi. Vui lòng vào chức năng "In lại" để thao tác tiếp.', [
                {
                    text: 'OK',
                    style: 'default',
                    onPress: this.showEbillQTV
                }
            ]);
        }
    };

    onConvertHTML = async (data, reportContents, isAnKhang) => {
        const dataBit = await this.requestConvertHtml(data, reportContents);
        if (helper.IsNonEmptyArray(dataBit)) {
            this.onPrintBillHTML(data, reportContents, dataBit, isAnKhang);
        } else {
            this.showEbillQTV();
        }
    };

    getPrintServiceHTML = (report, info, type) => {
        const {
            userInfo: { userName }
        } = this.props;
        const body = {
            Printer: report.PRINTERSHORTNAME,
            Value: info,
            Type: type,
            User: userName,
            Status: 'Payment'
        };
        return body;
    };

    getPrintServiceSocket = (report, info) => {
        const body = {
            ip: report.IPPRINTER,
            delay: report.DELAY,
            data: info
        };
        return body;
    };

    getPrintHTMLRequestAPI = (data, reportContents, dataBit, isAnKhang) => {
        const {
            dataSO: { SaleOrderID }
        } = this.props;
        const { reportRetail } = this.state;
        const requestAPI = [];
        const {
            eBillContentPrinterTypeID,
            eBillContentIncomePrinterTypeID,
            giftVCIssueContentPrinterTypeID,
            outTransContentPrinterTypeID,
            GiftVCIssueContentPrintHTML,
            dosageContentPrinterTypeID,
            infoBatchNOContentPrinterTypeID
        } = data;
        reportContents.forEach((ele, index) => {
            const { ReportContent } = ele;
            const dataConvert = dataBit[index];
            let report = {};
            switch (ReportContent) {
                case 'EBillContent':
                    report = this.getReport(eBillContentPrinterTypeID);
                    break;
                case 'EBillContentIncome':
                    report = this.getReport(eBillContentIncomePrinterTypeID);
                    break;
                case 'GiftVCIssueContentPrint':
                    report = this.getReport(giftVCIssueContentPrinterTypeID);
                    break;
                case 'KeySoftwareContent':
                    report = this.getReport(outTransContentPrinterTypeID);
                    break;
                case 'DosageContent':
                    report = this.getReport(dosageContentPrinterTypeID);
                    break;
                case 'InfoBatchNOContent':
                    report = this.getReport(infoBatchNOContentPrinterTypeID);
                    break;
                default:
                    report = reportRetail;
                    break;
            }
            if (helper.IsNonEmptyArray(dataConvert)) {
                dataConvert.forEach((info) => {
                    if (isAnKhang) {
                        if (!report.IPPRINTER) {
                            report.IPPRINTER = '*************';
                            report.DELAY = 500;
                        }
                        const printService = this.getPrintServiceSocket(
                            report,
                            info,
                            ReportContent
                        );
                        requestAPI.push(printService);
                    } else {
                        const printService = this.getPrintServiceHTML(
                            report,
                            info,
                            ReportContent
                        );
                        requestAPI.push(printService);
                    }
                });
                const isPMH = ReportContent == 'GiftVCIssueContentPrint';
                if (
                    isPMH &&
                    helper.IsNonEmptyString(GiftVCIssueContentPrintHTML)
                ) {
                    actionSaleOrderCreator.insertLogPrintGift(SaleOrderID);
                }
            }
        });
        return requestAPI;
    };

    onPrintBillHTML = (data, reportContents, dataBit, isAnKhang) => {
        const requestAPI = this.getPrintHTMLRequestAPI(
            data,
            reportContents,
            dataBit,
            isAnKhang
        );
        if (helper.IsNonEmptyArray(requestAPI)) {
            if (isAnKhang) {
                this.printAllRequestSocket(requestAPI);
            } else {
                this.printAllRequestFW(requestAPI);
            }
        } else {
            this.showEbillQTV();
        }
    };

    /*  */
    printAllRequestBHX = async (allPromise) => {
        try {
            for (const body of allPromise) {
                await actionSaleOrderCreator.printBillVoucherBHX(body);
                await helper.sleep(1000);
            }
            this.showEbillQTV();
        } catch (msgError) {
            Alert.alert(translate('common.notification'), msgError, [
                {
                    text: 'OK',
                    style: 'default',
                    onPress: this.showEbillQTV
                }
            ]);
        }
    };

    getContentHtmlBHX = (info) => {
        showBlockUI();
        this.props.actionSaleOrder
            .getReportContentBase64(info)
            .then((data) => {
                this.onConvertHTMLBHX(data, info.reportContents);
            })
            .catch((msgError) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: translate(
                                'saleOrderPayment.btn_skip_uppercase'
                            ),
                            style: 'default',
                            onPress: hideBlockUI
                        },
                        {
                            text: translate(
                                'saleOrderPayment.btn_retry_uppercase'
                            ),
                            style: 'default',
                            onPress: () => this.getContentHtmlBHX(info)
                        }
                    ]
                );
            });
    };

    requestConvertHtmlBHX = (data, reportContents) => {
        try {
            const requestConvert = [];
            const {
                EBillContentHTML,
                GiftVCIssueContentPrintHTML,
                EBillContentIncomeHTML,
                OutTransContentHTML,
                DosageContentHTML,
                InfoBatchNOContentHTML
            } = data;
            for (const ele of reportContents) {
                const { ReportContent } = ele;
                switch (ReportContent) {
                    case 'EBillContent':
                        if (helper.IsNonEmptyString(EBillContentHTML)) {
                            const eBillContent =
                                convertToBase64(EBillContentHTML);
                            requestConvert.push(eBillContent);
                        }
                        break;
                    case 'EBillContentIncome':
                        if (helper.IsNonEmptyString(EBillContentIncomeHTML)) {
                            const eBillContentIncome = convertToBase64(
                                EBillContentIncomeHTML
                            );
                            requestConvert.push(eBillContentIncome);
                        }
                        break;
                    case 'GiftVCIssueContentPrint':
                        if (
                            helper.IsNonEmptyString(GiftVCIssueContentPrintHTML)
                        ) {
                            const giftContent = convertToBase64(
                                GiftVCIssueContentPrintHTML
                            );
                            requestConvert.push(giftContent);
                        }
                        break;
                    case 'KeySoftwareContent':
                        if (helper.IsNonEmptyString(OutTransContentHTML)) {
                            const keySoftwareContent =
                                convertHtml2Image(OutTransContentHTML);
                            requestConvert.push(keySoftwareContent);
                        }
                        break;
                    case 'DosageContent':
                        if (helper.IsNonEmptyString(DosageContentHTML)) {
                            const dosageContent =
                                convertToBase64(DosageContentHTML);
                            requestConvert.push(dosageContent);
                        }
                        break;
                    case 'InfoBatchNOContent':
                        if (helper.IsNonEmptyString(InfoBatchNOContentHTML)) {
                            const batchContent = convertToBase64(
                                InfoBatchNOContentHTML
                            );
                            requestConvert.push(batchContent);
                        }
                        break;
                    default:
                        console.log(ele);
                        break;
                }
            }
            return requestConvert;
        } catch (error) {
            console.log('convertHtml2Image', error);
            Alert.alert('', 'Quá trình in lỗi. Vui lòng vào chức năng "In lại" để thao tác tiếp.', [
                {
                    text: 'OK',
                    style: 'default',
                    onPress: this.showEbillQTV
                }
            ]);
        }
    };

    onConvertHTMLBHX = (data, reportContents) => {
        const dataContent = this.requestConvertHtmlBHX(data, reportContents);
        if (helper.IsNonEmptyArray(dataContent)) {
            this.onPrintBillHTMLBHX(data, reportContents, dataContent);
        } else {
            hideBlockUI();
        }
    };

    getPrintServiceHTMLBHX = (report, info, type) => {
        const body = {
            printerName: report.PRINTERNAME,
            docData: info
        };
        return body;
    };

    getPrintHTMLRequestAPIBHX = (data, reportContents, dataBit) => {
        const { reportRetail } = this.state;
        const requestAPI = [];
        const {
            eBillContentPrinterTypeID,
            eBillContentIncomePrinterTypeID,
            giftVCIssueContentPrinterTypeID,
            outTransContentPrinterTypeID,
            dosageContentPrinterTypeID,
            infoBatchNOContentPrinterTypeID
        } = data;
        reportContents.forEach((ele, index) => {
            const { ReportContent } = ele;
            const dataConvert = dataBit[index];
            let report = {};
            switch (ReportContent) {
                case 'EBillContent':
                    report = this.getReport(eBillContentPrinterTypeID);
                    break;
                case 'EBillContentIncome':
                    report = this.getReport(eBillContentIncomePrinterTypeID);
                    break;
                case 'GiftVCIssueContentPrint':
                    report = this.getReport(giftVCIssueContentPrinterTypeID);
                    break;
                case 'KeySoftwareContent':
                    report = this.getReport(outTransContentPrinterTypeID);
                    break;
                case 'DosageContent':
                    report = this.getReport(dosageContentPrinterTypeID);
                    break;
                case 'InfoBatchNOContent':
                    report = this.getReport(infoBatchNOContentPrinterTypeID);
                    break;
                default:
                    report = reportRetail;
                    break;
            }
            if (helper.IsNonEmptyString(dataConvert)) {
                const printService = this.getPrintServiceHTMLBHX(
                    report,
                    dataConvert,
                    ReportContent
                );
                requestAPI.push(printService);
            }
        });
        return requestAPI;
    };

    onPrintBillHTMLBHX = (data, reportContents, dataBit) => {
        const requestAPI = this.getPrintHTMLRequestAPIBHX(
            data,
            reportContents,
            dataBit
        );
        if (helper.IsNonEmptyArray(requestAPI)) {
            this.printAllRequestBHX(requestAPI);
        } else {
            hideBlockUI();
        }
    };

    switchPrintToPDF = () => {
        this.dataPrint.isFitContent = false;
        this.dataPrint.isGetContentHTML = false;
        this.getContentBase64PDF(this.dataPrint);
    };
}

const mapStateToProps = function (state) {
    return {
        dataSaleOrder: state.saleOrderPaymentReducer.dataSaleOrder,
        dataSO: state.saleOrderPaymentReducer.dataSO,
        printerRetail: state.saleOrderPaymentReducer.printerRetail,
        printerVAT: state.saleOrderPaymentReducer.printerVAT,
        printerCommon: state.saleOrderPaymentReducer.printerCommon,
        defaultReport: state.saleOrderPaymentReducer.defaultReport,
        stateSaleOrder: state.saleOrderPaymentReducer.stateSaleOrder,
        statePrinter: state.saleOrderPaymentReducer.statePrinter,
        dataQRTransaction: state.saleOrderPaymentReducer.dataQRTransaction,
        stateQRTransaction: state.saleOrderPaymentReducer.stateQRTransaction,
        paramFilter: state.managerSOReducer.paramFilter,
        userInfo: state.userReducer
    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),
        actionManagerSO: bindActionCreators(actionManagerSOCreator, dispatch),
        actionCard: bindActionCreators(actionCardCreator, dispatch),
        actionAnKhangNew: bindActionCreators(actionAnKhangNewCreator, dispatch)

    };
};

const PaymentScreenWithHooks = (props) => {
    const theme = useTheme();
    const COLOR = theme.colors;
    return <PaymentScreen {...props} COLOR={COLOR} />
}


export default connect(mapStateToProps, mapDispatchToProps)(PaymentScreenWithHooks);

const getGiftPromotion = (giftProducts, keyGiftRemove) => {
    return giftProducts.map((ele) => {
        const { SaleOrderDetailID } = ele;
        if (keyGiftRemove.has(SaleOrderDetailID)) {
            ele.IsOutput = false;
        } else {
            ele.IsOutput = true;
        }
        return ele;
    });
};

const getKeyImeiProduct = (mainProducts, saleProducts, giftProducts) => {
    const dataProducts = [...mainProducts, ...saleProducts, ...giftProducts];
    const keyImeiProduct = new Set();
    dataProducts.forEach((ele) => {
        const { IMEI } = ele;
        if (helper.IsNonEmptyString(IMEI)) {
            const imei = IMEI.trim();
            keyImeiProduct.add(imei);
        }
    });
    return keyImeiProduct;
};


