/* eslint-disable react/jsx-fragments */
import {
    View,
    Image,
    TouchableOpacity,
    StyleSheet,
    BackHandler,
    FlatList,
    Alert,
    KeyboardAvoidingView,
    Platform,
    SafeAreaView,
    Pressable,
    Keyboard,
    Animated
} from 'react-native';
import React, {
    useState,
    useEffect,
    useRef,
    useMemo,
    useLayoutEffect,
    useCallback
} from 'react';
import LottieView from 'lottie-react-native';
import { useSelector, useDispatch, batch as rBatch } from 'react-redux';
import { useIsFocused } from '@react-navigation/core';
import { v4 as uuidv4 } from 'uuid';
import Toast from 'react-native-toast-message';

import { COLORS } from '@styles';
import { MyText, Icon, BaseLoading, BarcodeCamera } from '@components';
import { translate, keys } from '@translate';
import { material_wave_loading, sale_christmas } from '@animation';
import { CONFIG, constants, ENUM } from '@constants';
import { helper, debounce, storageHelper } from '@common';
import { ScrollView } from 'react-native-gesture-handler';
import Lot<PERSON> from 'lottie-react-native';

import {
    useAudio,
    useProvisionalCarts,
    useBrandCheck,
    useCrossSellingProducts
} from './hooks';
import {
    searchProduct,
    searchProductAnKhang,
    getPromotion,
    applyPromotion,
    createCart,
    getCartPromotion,
    applyCartPromotion,
    applyCoupon,
    resetCartInfo,
    getBatchNoInfo,
    setBatchNoByCart,
    resetBatchNoByCart,
    setCartLength,
    getProductStock,
    setActiveProducts,
    getProductPrice,
    setActiveIndex,
    updateCustomerInfo,
    setTempPhoneNumber,
    getPromotionProfit,
    setActiveTab,
    checkInventoryStatus,
    setActiveCrossSelling,
    parseProductInfos,
    uploadCombo,
    getDeliveryFree,
    updateDeliveryInfoToCart,
    getInfoProductLock,
    reset_map_prescriptions,
    getRandomPromotion
} from './action';
import { pharmacyState } from './state';
import { set_init_screen } from '../SaleOrderCart/action';
import {
    INITIAL_PROPS,
    SCREENS,
    FETCHING_STATE,
    PRICE_AREA,
    ACTION_AREA,
    ITEM,
    TYPE_ID,
    SUFFIX_COUPON,
    PERSISTENT_ID,
    TYPE_DELIVERY,
    TYPE_PROMOTION
} from './constants';
import {
    ProductSearchItem,
    Button,
    PriceText,
    SearchBar,
    ProductItem,
    SaleModal,
    ModalConfirm,
    BarcodeLimitScope,
    CouponField,
    CapsuleButton,
    CartCount,
    Counter
} from './components';
import {
    PromotionSheet,
    CartPromotionSheet,
    PrescriptionSheet,
    MiscSheet,
    StatusSheet,
    CrossSellingSheet
} from './frames';
import { MiscProvider } from './context/MiscContext';
import {
    validatePromotions,
    getProductName,
    isAcceptedPromotionType,
    useItemByBrandID,
    generateIdPromotionFetch,
    getCounterQuantity,
    checkHasCrossSelling,
    getDosageList,
    findKeyByValueFromMap
} from './helpers';
import ModalUsageGuide from '../Detail/component/Modal/ModalProductOtherInfo';
import BatchInfo from './components/BatchInfo';
import useTheme from './useTheme';
import UnitSheet from './frames/UnitSheet';
import FabWrapper from './components/FabWrapper';
import DeliverySheet from './frames/DeliverySheet';
import { DeliveryProvider } from './context/DeliveryProvider';
import LockProductSheet from './frames/LockProductSheet';
import CartRandomPromotionSheet from './frames/CartRandomPromotionSheet';
import PopupPhone from '../ShoppingCart/component/PopupPhone';

const { CartScreen, Pharmacy } = SCREENS;
const { FULFILLED, PENDING, REJECTED, READY } = FETCHING_STATE;
const { saleExpress, common } = keys;
const TIMEOUT = 650;
const TIMEOUT_SEARCH = 1400;
const ANDROID_AUDIO = {
    type: 'directory',
    path: require('../../../assets/assets_beep.mp3'),
    name: 'Beep'
};
const IOS_AUDIO = {
    type: 'app-bundle',
    path: 'beep.mp3',
    name: 'Beep'
};

const MainScreen = ({ navigation, route }) => {
    const {
        batchNoByCart,
        cartLength,
        tempPhoneNumber,
        customerInfo: { CustomerPhone, TempCartContactPhone },
        activeTab,
        activeCrossSell,
        typeDelivery,
        isActiveSheet,
        electricalPrescriptionBO,
        isClearCart
    } = useSelector((state) => state._pharmacyReducer);

    const [cartPromotions, setCartPromotions] = useState({
        giftPromotion: [],
        csPromotion: [],
        bundlePromotion: []
    });
    const [randomPromotions, setRandomPromotions] = useState([]);
    const [products, setProducts] = useState([]);
    const [productHistorys, setProductHistorys] = useState([]);
    const [techSpec, setTechSpec] = useState([]);
    const [techSpecSelected, setTechSpecSelected] = useState({});
    // To map promotion by product
    const [promotionGroups, setPromotionGroups] = useState({});
    /**
     * id: {
     *   promotionGroups: [123],
     *   promoByGroup: {
     *     123: [1, 2, 3]
     *   }
     * }
     */
    // To map promotion into sheet
    const [selectedProductId, setSelectedProductId] = useState({
        listId: null,
        prodId: null,
        promoGroupId: null,
        shouldCreateCart: false
    });

    const [selectedUnitId, setSelectedUnitId] = useState({
        listId: null,
        prodId: null
    });
    // Determine which promotion fetching
    const [isPromotionFetching, setIsPromotionFetching] = useState({});
    const [isCrossSellFetching, setIsCrossSellFetching] = useState({});
    // [id]: ['uuid', 'uuid',...]
    const [appliedCrossSelling, setAppliedCrossSelling] = useState({});
    const [dosageById, setDosageById] = useState({});
    // Be used to prevent computing unnecessary `appliedCrossSelling`
    const [isChangeQuantityFired, setIsChangeQuantityFired] = useState(false);

    // Determine which promotion should fetch
    const [fetchPromotion, setFetchPromotion] = useState({
        id: 0
    });
    // Promotion response corresponding to product
    const [responsePromo, setResponsePromo] = useState({});
    const [responseCart, setResponseCart] = useState({});
    const [stateSearchProducts, setStateSearchProducts] = useState({
        isError: false,
        description: '',
        isEmpty: false,
        data: []
    });
    const [applyPromotions, setApplyPromotions] = useState([]);
    const [webInfo, setWebInfo] = useState({});
    // Determine which product open to delete
    const [openedItemID, setOpenedItemID] = useState();

    const [coupon, setCoupon] = useState('');
    const [keyword, setKeyword] = useState('');

    // coupon
    const [isRequirePhone, setIsRequirePhone] = useState(false);
    const customerPhone = useRef('');
    const isAppliedPhonePromotion = useRef(false);

    // useEffect(() => {
    //     handleReadBarcode({
    //         code: "1063024000002",
    //         productRequest: null,
    //         isScan: true
    //     });
    // }, [])

    const [totalQty, setTotalQty] = useState(1);
    // tổng tiền sau khi áp dụng coupon

    const [dataModal, setDataModal] = useState(null);

    const [isNumericKeyword, setIsNumericKeyword] = useState(false);
    const [isShowCamera, setIsShowCamera] = useState(false);
    const [isSheetOpen, setIsSheetOpen] = useState(false);
    // Check người dùng đã bấm vào nút `Khuyến mãi`
    const [isSelectedPromo, setIsSelectedPromo] = useState(false);
    const [showListSearch, setShowListSearch] = useState(false);
    // set không cho edit khi đã nhập coupon thành công
    const [editableCoupon, setEditableCoupon] = useState(true);
    // freeze scanning barcode
    const [freeze, setFreeze] = useState(false);
    // Determine Product is fetching
    const [isWebSearching, setIsWebSearching] = useState(false);
    const [isElasticSearching, setIsElasticSearching] = useState(false);
    const [isShowSaleModal, setIsShowSaleModal] = useState(false);
    const [loadingBlock, setLoadingBlock] = useState(false);
    const [visibleConfirm, setVisibleConfirm] = useState(false);

    // show modal Hướng dẫn sử dụng
    const [isVisibleUsageGuide, setIsVisibleUsageGuide] = useState(false);
    const [isVisibleBatchQRScan, setIsVisibleBatchQRScan] = useState(false);
    const [shouldRenderCamera, setShouldRenderCamera] = useState(false);
    const [isModalLoading, setIsModalLoading] = useState(false);
    const [selectedProductIDRef, setSelectedProductIDRef] = useState(null);

    const [temQuantity, setTemQuantity] = useState(1);
    const [update, setUpdate] = useState(0);
    const [clearHistorySearch, setClearHistorySearch] = useState(0);
    const [listProductLock, setListProductLock] = useState([]);
    const [infoLockProduct, setInfoLockProduct] = useState({});
    const [stateScanCoupon, setStateScanCoupon] = useState({
        isShow: false,
        isScaned: false
    });

    // animation
    const animationRef = useRef(null);
    // lô date
    const [batchModal, setBatchModal] = useState({
        value: [],
        visible: false,
        ProductName: '',
        totalQuantity: 0,
        id: '',
        index: null,
        allowToChangeLess: false
    });

    // List tạm
    const [tempPhonePromoSet, setTempPhonePromoSet] = useState(new Set());
    // List khuyến mãi theo sản phẩm đã áp dụng SĐT
    const [phonePromoSet, setPhonePromoSet] = useState(new Set());

    // To map status into sheet
    const [selectedStatusProduct, setSelectedStatusProduct] = useState({
        ProductName: null,
        InventoryStatusID: null
    });

    const [tempInfoCart, setTempInfoCart] = useState({});
    // danh sách số lượng sản phẩm chính
    const [listAppliedQuantityMainProduct, setListAppliedQuantityMainProduct] =
        useState({});

    const PrevTotalQty = useRef(1);
    const scrollViewRef = useRef(null);
    const sheetRef = useRef(null);
    const cartSheetRef = useRef(null);
    const dosageSheetRef = useRef(null);
    const miscSheetRef = useRef(null);
    const unitSheetRef = useRef(null);
    const crossSellingSheetRef = useRef(null);
    const deliverySheetRef = useRef(null);
    const searchHeight = useRef(0);
    const productRef = useRef(new Map()).current;
    const productLayoutRef = useRef(new Map()).current;
    const scannedCodeMap = useRef(new Map()).current; // key: code; value: uuid
    const statusSheetRef = useRef(null);
    const lockProductSheetRef = useRef(null);
    const randomPromotionSheetRef = useRef(null);

    // Customized hooks
    const audio = useAudio(Platform.OS === 'ios' ? IOS_AUDIO : ANDROID_AUDIO);
    const isFocused = useIsFocused();
    const { drugs, provisionalCartId, isCareCustomer } = route.params ?? {
        drugs: []
    };
    const dispatch = useDispatch();
    const { storeID, languageID, moduleID, brandID, provinceID } = useSelector(
        (state) => state.userReducer
    );
    const { saleScenarioTypeID } = useSelector(
        (state) => state.specialSaleProgramReducer
    );
    const isAva = useBrandCheck(ENUM.BRAND_ID.AVA);
    const isAnKhang = useBrandCheck(ENUM.BRAND_ID.AN_KHANG);

    const requestSearch = useItemByBrandID({ item: ITEM.REQUEST_SEARCH });
    const {
        crossSellingProducts,
        setCrossSellingProductsById,
        setCrossSellingProducts,
        clearCrossSellingProducts
    } = useCrossSellingProducts();

    const {
        provisionalCarts,
        expiredTime,
        removeProvisionalCartById,
        updateProvisionalCartsInfo
    } = useProvisionalCarts();

    const baseRequest = {
        moduleID,
        languageID,
        loginStoreId: storeID,
        deliveryTypeID: typeDelivery
    };

    const handleReadBarcode = ({ code, productRequest = null, isScan }) => {
        Keyboard.dismiss();

        const isMatched = scannedCodeMap.has(code);
        if (!freeze && !isElasticSearching && !isMatched) {
            const body = {
                ...baseRequest,
                inventoryStatusID: null,
                storeID,
                saleProgramID: 0,
                deliveryTypeID: typeDelivery,
                isInstallment: false,
                keyword: code,
                saleOrderID: '',
                productRequest,
                isGetInfoByIMEI: false,
                isGetTechSpecs: isAva
            };
            isScan && audio.play();
            setFreeze(true);
            setIsElasticSearching(true);
            showBlockUI();
            if (!helper.IsEmptyObject(productRequest)) {
                isAva
                    ? storageHelper.updateSearchAvaHistory(keyword)
                    : storageHelper.updateSearchMedicalHistory(keyword);
            }
            searchProduct(body)
                .then((response) => {
                    const splittedKeywords = code?.split('.');
                    const batchId =
                        splittedKeywords?.length === 3
                            ? splittedKeywords[2]
                            : '';
                    const uuid = handleAddProductToCart({
                        product: response,
                        batchId
                    });

                    code && scannedCodeMap.set(code, uuid);
                    setKeyword('');
                    setIsSelectedPromo(false);
                    handleClearCoupon();
                    getDataHistory();
                })
                .catch((msgError) => {
                    const content =
                        msgError || ' Không tìm thấy thông tin sản phẩm.';
                    Alert.alert(translate(common.notification), content, [
                        {
                            text: 'OK',
                            style: 'cancel'
                        }
                    ]);
                })
                .finally(() => {
                    hideBlockUI();
                    setIsElasticSearching(false);
                });
        } else if (isMatched) {
            // isAva ? storageHelper.updateSearchAvaHistory(code) : storageHelper.updateSearchMedicalHistory(code);
            const uuid = scannedCodeMap.get(code);
            const selectedProduct = products.find((prod) => prod.id === uuid);
            setFreeze(true);
            if (selectedProduct) {
                const targetProduct = helper.deepCopy(selectedProduct);
                targetProduct.cus_ProductInfoBOList =
                    targetProduct.cus_ProductInfoBOList.map((prod) => ({
                        ...prod,
                        AppliedQuantity: 1
                    }));
                const splittedKeywords = code?.split('.');
                const batchId =
                    splittedKeywords?.length === 3 ? splittedKeywords[2] : '';

                handleAddProductToCart({
                    product: targetProduct,
                    batchId
                });
            } else {
                scannedCodeMap.delete(code);
                handleReadBarcode({
                    code,
                    productRequest,
                    isScan
                });
            }
        }

        if (keyword === null || keyword.length === 0) {
            setShowListSearch(false);
        }
    };

    const handleApiFetchInfo = ({
        ProductIDRef,
        ImageUrl,
        ProductID,
        Product,
        action = 'search'
    }) => {
        let inventoryStatusID = null;
        if (
            Product &&
            Product.cus_ProductInfoBOList &&
            Product.cus_ProductInfoBOList.length > 0
        ) {
            for (const x of Product.cus_ProductInfoBOList) {
                if (x.IsSelected) {
                    inventoryStatusID = x.InventoryStatusID;
                }
            }
        }
        const body = {
            ...baseRequest,
            inventoryStatusID,
            storeID,
            saleProgramID: 0,
            deliveryTypeID: typeDelivery,
            isInstallment: false,
            keyword: null,
            saleOrderID: '',
            productRequest: {
                ProductIDRef,
                Image: ImageUrl,
                ProductID
            },
            isGetInfoByIMEI: false,
            isGetTechSpecs: isAva
        };
        if (action === 'stock') {
            return getProductStock(body);
        } else if (action === 'price') {
            return getProductPrice(body);
        }
        return searchProduct(body, Product);
    };

    const handleApiGetPromotion = (productInfo, id) => {
        const isUpdate =
            isPromotionFetching[id] !== undefined &&
            isPromotionFetching[id] !== REJECTED;
        const body = {
            ...baseRequest,
            outputStoreID: storeID,
            saleProgramID: 0,
            deliveryTypeID: typeDelivery,
            saleOrderID: '',
            productInfo: {
                ...productInfo,
                cus_ProductListGroupBOList: null
            },
            storeID,
            brandID,
            provinceID,
            saleScenarioTypeID
        };
        setIsPromotionFetching((prevFetchingState) => ({
            ...prevFetchingState,
            [id]: PENDING
        }));
        getPromotion(body)
            .then((response) => {
                setResponsePromo((prevResPromo) => ({
                    ...prevResPromo,
                    [id]: {
                        productInfo,
                        response,
                        isUpdate
                    }
                }));
            })
            .catch((error) => {
                Toast.show({
                    type: 'error',
                    text1: `${productInfo.ProductName}: ${error}`
                });
                setIsPromotionFetching((prevFetchingState) => ({
                    ...prevFetchingState,
                    [id]: REJECTED
                }));
            });
    };

    const handleApiGetCrossSell = (productInfo, id) => {
        // const isUpdate = isPromotionFetching[id] !== undefined;
        const body = {
            ...baseRequest,
            outputStoreID: storeID,
            saleProgramID: 0,
            deliveryTypeID: typeDelivery,
            saleOrderID: '',
            productInfo: {
                ...productInfo,
                cus_ProductListGroupBOList: null
            },
            IsSalePromotion: true,
            storeID,
            brandID,
            provinceID,
            saleScenarioTypeID
        };
        setIsCrossSellFetching((prevFetchingState) => ({
            ...prevFetchingState,
            [id]: PENDING
        }));
        getPromotion(body)
            .then((response) => {
                if (response.cus_ProductListGroupSaleBOList) {
                    setCrossSellingProductsById({
                        products: response.cus_ProductListGroupSaleBOList,
                        id
                    });
                }
                // Clear SP Bán kèm
                const [uuid] = id.split('_');
                const newProducts = clearCSProductsByUuid(uuid, true);
                if (newProducts.length > 0) {
                    setProducts(newProducts);
                }

                setIsCrossSellFetching((prevFetchingState) => ({
                    ...prevFetchingState,
                    [id]: FULFILLED
                }));
            })
            .catch((error) => {
                Toast.show({
                    type: 'error',
                    text1: `${productInfo.ProductName}: ${error}`
                });
                setIsCrossSellFetching((prevFetchingState) => ({
                    ...prevFetchingState,
                    [id]: REJECTED
                }));
            });
    };

    const handleApiGetCartPromotion = (cartRequest) => {
        const body = {
            ...baseRequest,
            outputStoreID: storeID,
            promotionWithSaleOrderRequest: {
                saleOrderTypeID: 1,
                isGetPromotionForSaleProduct: false,
                appliedPromotion: null
            },
            cartRequest,
            storeID,
            brandID,
            provinceID,
            saleScenarioTypeID
        };
        return getCartPromotion(body);
    };

    const handleClearPhonePromo = () => {
        if (!hasSelectedPhonePromo) {
            dispatch(
                updateCustomerInfo({
                    CustomerPhone: '',
                    ContactPhone: ''
                })
            );
        }
    };

    const handleApiPromotionProfit = (cartRequest) => {
        const body = {
            ...baseRequest,
            saleScenarioTypeID,
            promotionWithSaleOrderRequest: {
                saleOrderTypeID: 1,
                isGetPromotionForSaleProduct: false,
                appliedPromotion: null
            },
            discountCode: '',
            cartRequest,
            giftCode: '',
            promotionGroups: []
        };

        return getPromotionProfit(body);
    };

    const handleApiPromotionRandom = async (cartRequest, infoAddToCart) => {
        const body = {
            ...baseRequest,
            outputStoreID: storeID,
            promotionWithSaleOrderRequest: {
                saleOrderTypeID: 1,
                isGetPromotionForSaleProduct: false,
                appliedPromotion: null
            },
            cartRequest,
            storeID,
            brandID,
            provinceID,
            saleScenarioTypeID
        };
        let isAllowSetCart = true;
        try {
            showBlockUI();
            randomPromotionSheetRef.current.snapToIndex(0);
            const { GiftProductListGroups } = await getRandomPromotion(body);
            if (GiftProductListGroups.length > 0) {
                setRandomPromotions(GiftProductListGroups);
            } else {
                if (isAva && typeDelivery !== TYPE_DELIVERY.STORE) {
                    setTempInfoCart(infoAddToCart);
                    deliverySheetRef.current.snapToIndex(0);
                } else {
                    randomPromotionSheetRef.current.close();
                    handleNavigateToCart({
                        ...infoAddToCart
                    });
                    setResponseCart({});
                    isAllowSetCart = false;
                }
            }
        } catch (errorMsg) {
            Alert.alert(translate(common.notification), errorMsg, [
                {
                    text: translate(common.btn_skip),
                    style: 'cancel',
                    onPress: () => {
                        if (errorMsg?.includes(SUFFIX_COUPON)) {
                            setCoupon('');
                        }
                        randomPromotionSheetRef.current.close();
                    }
                },
                {
                    text: translate(common.btn_notify_try_again),
                    style: 'default',
                    onPress: () =>
                        handleApiPromotionRandom(cartRequest, infoAddToCart)
                }
            ]);
        } finally {
            hideBlockUI();
            if (isAllowSetCart) {
                setResponseCart(cartRequest);
            }
        }
    };

    const getSheetRef = (type) => {
        const sheetRefGroup = {
            [TYPE_PROMOTION.TOTAL_PROMOTION]: cartSheetRef,
            [TYPE_PROMOTION.RANDOM_PROMOTION]: randomPromotionSheetRef
        };
        return sheetRefGroup[type] ?? null;
    };

    const handleApiApplyCartPromotion = ({
        cartPromo,
        cart,
        campaignPhone,
        loyaltyInfo,
        newProductInfos,
        typePromotion
    }) => {
        // Remove barcode promotions that aren't required and don't have any products
        const newCartPromo = cartPromo.filter(
            (promotion) =>
                !(
                    promotion.ViewBarcode &&
                    !promotion.IsRequired &&
                    promotion.ProductLists?.length === 0
                )
        );
        if (!helper.IsEmptyObject(cart)) {
            cart.SaleOrderDetails.forEach((item, index) => {
                const newDeliveryInfoRequest = {
                    ...item.DeliveryInfoRequest,
                    DeliveryTypeID: typeDelivery
                };
                cart.SaleOrderDetails[index].DeliveryInfoRequest =
                    newDeliveryInfoRequest;
            });
        }

        const body = {
            ...baseRequest,
            shoppingCartRequest: cart,
            productListGroups: newCartPromo,
            productInfoBOList: newProductInfos
        };
        newCartPromo.forEach((promotion, index) => {
            if (promotion.InvisibleByCustomerPhone) {
                if (!promotion.isVisibleCartPromotion && promotion.IsRequired) {
                    newCartPromo.splice(index, 1);
                }
            }
        });
        const listPromotion = validatePromotions(newCartPromo);
        if (!helper.IsNonEmptyArray(listPromotion)) {
            showBlockUI();
            const sheetRef = getSheetRef(typePromotion);
            sheetRef?.current?.close();
            applyCartPromotion(body)
                .then((newCart) => {
                    const hasSelectedCampaignPromotion = newCartPromo.some(
                        (promotion) =>
                            promotion.InvisibleByCustomerPhone &&
                            promotion.IsSelected
                    );
                    const isAppliedCouponWithPhoneNumber = helper.hasProperty(
                        newCart.CustomerInfo,
                        'ApplyPhoneNumberByDiscountCode'
                    );
                    if (!CustomerPhone && hasSelectedCampaignPromotion) {
                        dispatch(
                            updateCustomerInfo({
                                CustomerPhone: campaignPhone,
                                ContactPhone: campaignPhone
                            })
                        );
                    }
                    const dataDosages = getDosageList({
                        dosageById,
                        cart: products
                    });
                    if (typePromotion === TYPE_PROMOTION.TOTAL_PROMOTION) {
                        const infoAddToCart = {
                            dataCart: newCart,
                            dataDosages,
                            loyaltyInfo,
                            onGoBack: handleClearPhonePromo,
                            isDisableCustomerPhone:
                                hasSelectedPhonePromo ||
                                hasSelectedCampaignPromotion ||
                                isAppliedCouponWithPhoneNumber ||
                                isAppliedPhonePromotion.current
                        };

                        handleApiPromotionRandom(newCart, infoAddToCart);
                    } else if (
                        typePromotion === TYPE_PROMOTION.RANDOM_PROMOTION
                    ) {
                        if (isAva && typeDelivery !== TYPE_DELIVERY.STORE) {
                            setResponseCart(newCart);
                            setTempInfoCart({
                                dataCart: newCart,
                                dataDosages,
                                loyaltyInfo,
                                onGoBack: handleClearPhonePromo,
                                isDisableCustomerPhone:
                                    hasSelectedPhonePromo ||
                                    hasSelectedCampaignPromotion ||
                                    isAppliedCouponWithPhoneNumber ||
                                    isAppliedPhonePromotion.current
                            });
                            deliverySheetRef.current.snapToIndex(0);
                        } else {
                            handleNavigateToCart({
                                dataCart: newCart,
                                dataDosages,
                                loyaltyInfo,
                                onGoBack: handleClearPhonePromo,
                                isDisableCustomerPhone:
                                    hasSelectedPhonePromo ||
                                    hasSelectedCampaignPromotion ||
                                    isAppliedCouponWithPhoneNumber ||
                                    isAppliedPhonePromotion.current
                            });
                        }
                    }
                    // if (isAva && typeDelivery !== TYPE_DELIVERY.STORE) {
                    //     setResponseCart(newCart);
                    //     setTempInfoCart({
                    //         dataCart: newCart,
                    //         dataDosages,
                    //         loyaltyInfo,
                    //         onGoBack: handleClearPhonePromo,
                    //         isDisableCustomerPhone:
                    //             hasSelectedPhonePromo ||
                    //             hasSelectedCampaignPromotion
                    //     });
                    //     deliverySheetRef.current.snapToIndex(0);
                    // } else {
                    //     handleNavigateToCart({
                    //         dataCart: newCart,
                    //         dataDosages,
                    //         loyaltyInfo,
                    //         onGoBack: handleClearPhonePromo,
                    //         isDisableCustomerPhone:
                    //             hasSelectedPhonePromo ||
                    //             hasSelectedCampaignPromotion
                    //     });
                    // }
                })
                .catch((error) => {
                    Alert.alert(translate(common.notification), error);
                })
                .finally(hideBlockUI);
        } else {
            const productNames = listPromotion.join('\n');
            Alert.alert(
                translate(common.notification),
                translate(saleExpress.warning_required_promotions_with_params, {
                    productNames
                })
            );
        }
    };

    const handleApiBatchNo = ({ uuid, product, index }) => {
        const hasBatch = !helper.IsEmptyObject(products[index].BatchLot);

        if (!hasBatch) {
            Toast.show({
                type: 'error',
                text1: `${product.ProductName}: Bắt buộc quét QR.`
            });
        } else {
            const id = `${uuid}_${product.ProductID}_${product.InventoryStatusID}`;
            const batches = batchNoByCart[id];
            if (batches) {
                setBatchModal({
                    visible: true,
                    value: batches ?? [],
                    ProductName: product.ProductName,
                    id: product.ProductID,
                    totalQuantity: product.AppliedQuantity,
                    index,
                    allowToChangeLess:
                        !product.cus_BatchNOBO.IsStoreGetCurrentInstock
                });
            } else {
                showBlockUI();
                const body = {
                    outputStoreID: storeID,
                    productInfo: {
                        ItemID: product.ItemID,
                        cus_BatchNOBO: {
                            ...product.cus_BatchNOBO,
                            cus_BatchNO: products[index].BatchLot.BatchNO
                        },
                        IMEI: product.IMEI,
                        cus_SaleOrderDetailInfoBOList:
                            product.cus_SaleOrderDetailInfoBOList,
                        cus_ItemExchangeQuantityUnitBO:
                            product.cus_ItemExchangeQuantityUnitBO,
                        AppliedQuantity: product.AppliedQuantity,
                        InventoryStatusID: product.InventoryStatusID,
                        ProductID: product.ProductID,
                        ProductName: product.ProductName,
                        IsCheckStockQuantity: product.IsCheckStockQuantity
                    }
                };

                getBatchNoInfo(body)
                    .then((response) => {
                        if (helper.IsNonEmptyArray(response)) {
                            setBatchModal({
                                visible: true,
                                value: response,
                                ProductName: response[0].ProductName,
                                id: response[0].ProductID,
                                totalQuantity: product.AppliedQuantity,
                                index,
                                allowToChangeLess:
                                    !product.cus_BatchNOBO
                                        .IsStoreGetCurrentInstock
                            });
                            dispatch(
                                setBatchNoByCart({
                                    id,
                                    batchNo: response
                                })
                            );
                        }
                    })
                    .catch((msgError) => {
                        Alert.alert(
                            translate('common.notification_uppercase'),
                            msgError,
                            [{ text: 'OK' }]
                        );
                    })
                    .finally(hideBlockUI);
            }
        }
    };

    // const handleApiApplyRanDomPromotion = ({
    //     cartPromo,
    //     cart,
    //     campaignPhone,
    //     loyaltyInfo,
    //     newProductInfos,
    //     typePromotion
    // }) => {
    //     // Remove barcode promotions that aren't required and don't have any products
    //     const newCartPromo = cartPromo.filter(
    //         (promotion) =>
    //             !(
    //                 promotion.ViewBarcode &&
    //                 !promotion.IsRequired &&
    //                 promotion.ProductLists?.length === 0
    //             )
    //     );
    //     if (!helper.IsEmptyObject(cart)) {
    //         cart.SaleOrderDetails.forEach((item, index) => {
    //             const newDeliveryInfoRequest = {
    //                 ...item.DeliveryInfoRequest,
    //                 DeliveryTypeID: typeDelivery
    //             };
    //             cart.SaleOrderDetails[index].DeliveryInfoRequest =
    //                 newDeliveryInfoRequest;
    //         });
    //     }

    //     const body = {
    //         ...baseRequest,
    //         shoppingCartRequest: cart,
    //         productListGroups: newCartPromo,
    //         productInfoBOList: newProductInfos
    //     };
    //     newCartPromo.forEach((promotion, index) => {
    //         if (promotion.InvisibleByCustomerPhone) {
    //             if (!promotion.isVisibleCartPromotion && promotion.IsRequired) {
    //                 newCartPromo.splice(index, 1);
    //             }
    //         }
    //     });
    //     const listPromotion = validatePromotions(newCartPromo);
    //     if (!helper.IsNonEmptyArray(listPromotion)) {
    //         showBlockUI();
    //         randomPromotionSheetRef.current.close();
    //         applyCartPromotion(body)
    //             .then((newCart) => {
    //                 const hasSelectedCampaignPromotion = newCartPromo.some(
    //                     (promotion) =>
    //                         promotion.InvisibleByCustomerPhone &&
    //                         promotion.IsSelected
    //                 );
    //                 if (!CustomerPhone && hasSelectedCampaignPromotion) {
    //                     dispatch(
    //                         updateCustomerInfo({
    //                             CustomerPhone: campaignPhone,
    //                             ContactPhone: campaignPhone
    //                         })
    //                     );
    //                 }
    //                 const dataDosages = getDosageList({
    //                     dosageById,
    //                     cart: products
    //                 });
    //                 if (isAva && typeDelivery !== TYPE_DELIVERY.STORE) {
    //                     setResponseCart(newCart);
    //                     setTempInfoCart({
    //                         dataCart: newCart,
    //                         dataDosages,
    //                         loyaltyInfo,
    //                         onGoBack: handleClearPhonePromo,
    //                         isDisableCustomerPhone:
    //                             hasSelectedPhonePromo ||
    //                             hasSelectedCampaignPromotion
    //                     });
    //                     deliverySheetRef.current.snapToIndex(0);
    //                 } else {
    //                     handleNavigateToCart({
    //                         dataCart: newCart,
    //                         dataDosages,
    //                         loyaltyInfo,
    //                         onGoBack: handleClearPhonePromo,
    //                         isDisableCustomerPhone:
    //                             hasSelectedPhonePromo ||
    //                             hasSelectedCampaignPromotion
    //                     });
    //                 }
    //             })
    //             .catch((error) => {
    //                 Alert.alert(translate(common.notification), error);
    //             })
    //             .finally(hideBlockUI);
    //     } else {
    //         const productNames = listPromotion.join('\n');
    //         Alert.alert(
    //             translate(common.notification),
    //             translate(saleExpress.warning_required_promotions_with_params, {
    //                 productNames
    //             })
    //         );
    //     }
    // };

    const clearAllProducts = () => {
        setProducts([]);
        setPromotionGroups({});
        setIsPromotionFetching({});
        setSelectedProductId({
            listId: null,
            prodId: null,
            promoGroupId: null,
            shouldCreateCart: false
        });
        setDosageById({});
        setResponsePromo({});
        setResponseCart({});
        setIsSheetOpen(false);
        setEditableCoupon(true);
        setCoupon('');
        setSelectedStatusProduct({
            ProductName: null,
            InventoryStatusID: null
        });

        rBatch(() => {
            dispatch(setActiveProducts(pharmacyState.activeProducts));
            dispatch(setActiveIndex(pharmacyState.activeIndex));
            // set all object cs products -> id === null
            clearCrossSellingProducts();
        });
        productRef.clear();
        productLayoutRef.clear();
        scannedCodeMap.clear();

        sheetRef.current.close();
        cartSheetRef.current.close();
        dosageSheetRef.current.close();
        unitSheetRef.current.close();
        statusSheetRef.current.close();
        lockProductSheetRef.current.close();
        // deliverySheetRef.current.close();
        randomPromotionSheetRef.current.close();
    };

    const confirmClearProducts = () => {
        statusSheetRef.current.close();
        Alert.alert(
            translate(common.notification),
            translate(saleExpress.confirm_clear_all_products),
            [
                {
                    text: translate(common.btn_skip)
                },
                {
                    text: translate(common.btn_continue),
                    onPress: () => {
                        dispatch(reset_map_prescriptions());
                        clearAllProducts();
                    }
                }
            ]
        );
    };

    const debounceSearchWebProducts = useCallback(
        debounce((text) => handleOnSearchProduct(text), TIMEOUT_SEARCH),
        []
    );

    const clearCSProductsByUuid = (uuid, isDelete) => {
        const promotionGroupIds = products
            .filter(
                (product) =>
                    product.crossSelling?.appliedCrossSellingById === uuid
            )
            .map((product) => product.crossSelling.promotionGroupId);
        if (promotionGroupIds.length > 0) {
            handleClearCrossSellingProduct({
                newProducts: products,
                target: {
                    crossSelling: {
                        appliedCrossSellingById: uuid,
                        promotionGroupId: [...promotionGroupIds]
                    }
                },
                isDelete
            });
            const newProducts = products.filter(
                (product) =>
                    product.crossSelling?.appliedCrossSellingById !== uuid
            );
            return newProducts;
        }
        return [...products];
    };

    const handleOnSearchProduct = (text) => {
        if (text.length > 0) {
            setShowListSearch(true);
            const { siteID, maingroupIDs } = requestSearch;
            const body = {
                keyword: text,
                pageSize: 20,
                pageIndex: 0,
                groupType: 1,
                siteID,
                maingroupIDs
            };
            setIsWebSearching(true);

            setStateSearchProducts({
                isError: false,
                description: '',
                isEmpty: false,
                data: []
            });
            searchProductAnKhang(body)
                .then((response) => {
                    if (response.length > 0) {
                        setStateSearchProducts({
                            isError: false,
                            description: '',
                            isEmpty: false,
                            data: response
                        });
                    } else {
                        const isOnlyDigit = /^\d+$/;
                        if (isOnlyDigit.test(text)) {
                            setStateSearchProducts({
                                isError: false,
                                description:
                                    'Không tìm thấy thông tin sản phẩm.',
                                isEmpty: true,
                                data: []
                            });
                            // setIsNumericKeyword(true);
                        } else {
                            setStateSearchProducts({
                                isError: false,
                                description:
                                    'Không tìm thấy thông tin sản phẩm.',
                                isEmpty: true,
                                data: []
                            });
                        }
                    }
                })
                .catch((error) => {
                    setStateSearchProducts({
                        isError: true,
                        description: error,
                        isEmpty: true,
                        data: []
                    });
                })
                .finally(() => setIsWebSearching(false));
        }
    };

    const handleChangeUnit = (index, id) => {
        const selectedProduct = products[index];
        const uuid = selectedProduct.id;
        const newProducts = clearCSProductsByUuid(uuid, false);
        const code = findKeyByValueFromMap(scannedCodeMap, uuid);
        code && scannedCodeMap.delete(code);
        handleGetPriceStock(newProducts, index, id, selectedProduct);
    };

    const handleChangeTechSpec = (productID) => {
        const { listId: uuid } = selectedUnitId;
        const index = products.findIndex((prod) => prod.id === uuid);
        const selectedProduct = products[index];
        const isProductSize = true;

        const newProducts = clearCSProductsByUuid(uuid, false);
        handleGetPriceStock(
            newProducts,
            index,
            productID,
            selectedProduct,
            isProductSize
        );

        unitSheetRef.current.close();
    };

    const updateDeliveryInfo = (dataCart) => {
        showBlockUI();
        dispatch(updateDeliveryInfoToCart(dataCart))
            .then((newCart) => {
                hideBlockUI();
                handleNavigateToCart({
                    ...tempInfoCart,
                    dataCart: newCart
                });
                deliverySheetRef.current.close();
            })
            .catch((errorMsg) => {
                Alert.alert(translate(common.notification), errorMsg, [
                    {
                        text: translate(common.btn_skip),
                        style: 'cancel',
                        onPress: () => {
                            hideBlockUI();
                            deliverySheetRef.current.close();
                        }
                    },
                    {
                        text: translate(common.btn_notify_try_again),
                        style: 'default',
                        onPress: () => updateDeliveryInfo(dataCart)
                    }
                ]);
            });
    };

    const handleGetPriceStock = (
        currentProducts,
        index,
        id,
        selectedProduct,
        isProductSize,
        instockQuantity
    ) => {
        // const newProducts = [...products];
        const unitIdx = selectedProduct.cus_ProductInfoBOList.findIndex(
            (product) => product.ProductID === id
        );
        const { length } = selectedProduct.cus_ProductInfoBOList;
        const nextIdx = isProductSize ? unitIdx : (unitIdx + 1) % length;
        const newProductsInfo = selectedProduct.cus_ProductInfoBOList.map(
            (product, idx) => ({
                ...product,
                IsSelected: idx === nextIdx,
                ...INITIAL_PROPS
            })
        );

        currentProducts[index].cus_ProductInfoBOList = newProductsInfo;
        if (selectedProduct.cus_ProductInfoBOList[nextIdx].IsFetched) {
            currentProducts[index].cus_ProductInfoBOList = newProductsInfo;
            setProducts(currentProducts);
        } else {
            showBlockUI();
            setIsElasticSearching(true);
            handleApiFetchInfo({
                ProductID:
                    selectedProduct.cus_ProductInfoBOList[nextIdx].ProductID,
                ProductIDRef: selectedProduct.ProductIDRef,
                ImageUrl: selectedProduct.Image,
                Product: currentProducts[index]
            })
                .then((response) => {
                    const uuid = selectedProduct.id;
                    currentProducts[index] = response;
                    if (instockQuantity) {
                        currentProducts[index].cus_ProductInfoBOList[
                            nextIdx
                        ].InstockQuantity = instockQuantity;
                    }
                    if (
                        currentProducts[index].cus_ProductInfoBOList[nextIdx]
                            .cus_MessageWarning
                    ) {
                        currentProducts[index].cus_ProductInfoBOList[nextIdx] =
                        {
                            ...currentProducts[index].cus_ProductInfoBOList[
                            nextIdx
                            ],
                            CostPrice: 0,
                            PriceAppliedPromo: 0,
                            RetailPrice: 0,
                            RetailPriceVAT: 0,
                            SalePrice: 0,
                            SalePriceVAT: 0,
                            StandardPriceAreaSalePrice: 0,
                            StandardSalePrice: 0
                        };
                    }
                    setProducts(currentProducts);
                    const isMadePrice =
                        response.cus_ProductInfoBOList[nextIdx].IsMadePrice;
                    setFetchPromotion((prevFetch) => ({
                        ...prevFetch,
                        [uuid]: isMadePrice ? 1 : 0 // refetch promotion when product is made price
                    }));
                    setIsSelectedPromo(false);
                    handleClearCoupon();
                })
                .catch((error) => {
                    Alert.alert(translate(common.notification), error);
                })
                .finally(() => {
                    hideBlockUI();
                    setIsElasticSearching(false);
                });
        }
    };
    const handleChangeQuantity = (quantity, index, id) => {
        const idx = products[index].cus_ProductInfoBOList.findIndex(
            (item) => item.ProductID === id
        );
        const generatedId = `${products[index].id}_${id}_${products[index].cus_ProductInfoBOList[idx].InventoryStatusID}`;
        if (idx !== -1) {
            const batches = batchNoByCart[generatedId];
            const newProducts = [...products];
            const selectedProduct =
                newProducts[index].cus_ProductInfoBOList[idx];
            selectedProduct.AppliedQuantity = quantity;
            if (newProducts[index].BatchLot) {
                newProducts[index].BatchLot.Quantity = quantity;
            }
            newProducts[index].StoreChange = null;
            setProducts(newProducts);
            if (!editableCoupon) {
                handleClearCoupon();
            }
            if (batches) {
                dispatch(
                    setBatchNoByCart({
                        id: generatedId,
                        batchNo: null
                    })
                );
            }
        }
    };

    const handleChangeTotalQuantity = (quantity) => {
        const newProducts = helper.deepCopy(products);
        let isValid = true;
        for (let i = 0; i < newProducts.length; i++) {
            const product = newProducts[i];
            if (
                !product.crossSelling &&
                helper.IsEmptyObject(product.StoreChange)
            ) {
                const selectedProduct = product.cus_ProductInfoBOList.find(
                    (item) => item.IsSelected && !item.IsRequestIMEI
                );
                if (selectedProduct) {
                    const newQty =
                        Math.ceil(
                            selectedProduct.AppliedQuantity /
                            PrevTotalQty.current
                        ) * quantity;
                    if (newQty > selectedProduct.InstockQuantity) {
                        toastSpecificItem({
                            message: translate(
                                saleExpress.product_lack_of_in_stock_with_params,
                                {
                                    productName: selectedProduct.ProductName
                                }
                            ),
                            index: i
                        });
                        setUpdate((prev) => prev + 1);
                        isValid = false;
                        break;
                    }
                    selectedProduct.AppliedQuantity = newQty;
                    if (newProducts[i].BatchLot) {
                        newProducts[i].BatchLot.Quantity = newQty;
                    }
                }
            }
        }
        if (isValid) {
            setIsChangeQuantityFired(true);
            setProducts(newProducts);
            setTotalQty(quantity);
            if (!editableCoupon) {
                handleClearCoupon();
            }
            dispatch(resetBatchNoByCart());
        }
    };

    const handleChangePrescription = (newDosages) => {
        dosageSheetRef.current.close();

        setDosageById(newDosages);
    };

    const handleChangePromotion = ({
        promotions,
        shouldCreateCart,
        isDiscountPromoSelected
    }) => {
        const newProds = [...products];
        let currentPromotionGroups = { ...promotionGroups };

        const nonSelectedRequiredPromotionNames = [];
        // Khi người dùng bấm vào nút `Khuyến mãi` -> kiểm tra KM bắt buộc
        if (selectedProductId.prodId === null) {
            promotions.forEach((promo) => {
                const { data } = promo;
                nonSelectedRequiredPromotionNames.push(
                    ...validatePromotions(data)
                );
            });
        }

        if (nonSelectedRequiredPromotionNames.length > 0) {
            const productNames = nonSelectedRequiredPromotionNames.join('\n');
            setIsSelectedPromo(false);
            Alert.alert(
                translate(common.notification),
                translate(saleExpress.warning_required_promotions_with_params, {
                    productNames
                })
            );
        } else {
            promotions.forEach((promo) => {
                const { listId, prodId, data, inventoryStatusID } = promo;
                const listIndex = newProds.findIndex(
                    (prod) => prod.id === listId
                );
                if (listIndex !== -1) {
                    const prodIdx = newProds[
                        listIndex
                    ].cus_ProductInfoBOList.findIndex(
                        (prod) => prod.ProductID === prodId
                    );
                    if (prodIdx !== -1) {
                        const newProduct =
                            newProds[listIndex].cus_ProductInfoBOList[prodIdx];
                        newProduct.cus_ProductListGroupBOList = data;

                        const hasDiscountPromo = data.some((group) => {
                            return group.ProductLists.some(
                                (prod) => prod.IsDiscount
                            );
                        });
                        const id = `${listId}_${prodId}_${inventoryStatusID}`;

                        if (isDiscountPromoSelected[id] && hasDiscountPromo) {
                            setIsPromotionFetching((prevState) => ({
                                ...prevState,
                                [id]: PENDING
                            }));

                            applyPromotion({ productInfo: newProduct })
                                .then((response) => {
                                    setResponsePromo((prevResPromo) => ({
                                        ...prevResPromo,
                                        [id]: {
                                            productInfo: newProduct,
                                            response,
                                            isUpdate: true
                                        }
                                    }));
                                })
                                .catch((error) => {
                                    Alert.alert(
                                        translate(common.notification),
                                        error,
                                        [
                                            {
                                                text: 'OK',
                                                style: 'cancel'
                                            }
                                        ]
                                    );
                                    setIsPromotionFetching((prevState) => ({
                                        ...prevState,
                                        [id]: FULFILLED
                                    }));
                                });
                        } else {
                            currentPromotionGroups = generatePromotionGroups({
                                newProduct,
                                id: `${listId}_${prodId}_${inventoryStatusID}`,
                                currentPromotionGroups,
                                isUpdate: true
                            });
                        }
                    }
                }
            });

            setProducts(newProds);
            setPromotionGroups(currentPromotionGroups);
            sheetRef.current.close();
            if (shouldCreateCart) {
                setIsSelectedPromo(true);
                handleCart({ shouldCreateCart });
            }
        }
        // set km check SĐT
        setPhonePromoSet(tempPhonePromoSet);
        dispatch(
            updateCustomerInfo({
                CustomerPhone: tempPhoneNumber,
                ContactPhone: tempPhoneNumber
            })
        );
    };

    const handleChangeCrossSellingPromotion = ({ promotions, phoneKeyMap }) => {
        const newProducts = [...products];
        // make sure promotions has 1 element
        // promotions.forEach((promo) => {
        let shouldUpdateCS = false;
        const {
            listId,
            prodId,
            data,
            inventoryStatusID,
            promotionsByData,
            isPromotionChange,
            productInfoID,
            outputStoreID,
            outputTypeID,
            IsApplyTotalPromotion
        } = promotions[0];

        // index: -1 -> unshift | index: positive -> reassign
        const request = [];
        data.forEach((promoGroup) => {
            const {
                PromotionListGroupID,
                PromotionID,
                PromotionListGroupName,
                IsSelected,
                ProductLists
            } = promoGroup;
            const idx = newProducts.findIndex((prod) => {
                if (prod.crossSelling) {
                    const { promotionGroupId, appliedCrossSellingById } =
                        prod.crossSelling;
                    return (
                        promotionGroupId === PromotionListGroupID &&
                        listId === appliedCrossSellingById
                    );
                }
                return false;
            });
            if (IsSelected) {
                shouldUpdateCS = true;
                const selectedPromo = ProductLists.find(
                    (prod) => prod.IsSelected
                );
                const requestItem = {
                    value: {
                        ...promoGroup,
                        ProductLists: ProductLists.filter(
                            (item) => item.IsSelected
                        )
                    },
                    index: -1,
                    crossSelling: {
                        appliedCrossSellingById: listId,
                        promotionGroupId: PromotionListGroupID,
                        productId: prodId,
                        PromotionListGroupName,
                        PromotionID,
                        inventoryStatusID,
                        originQuantity: selectedPromo.Quantity,
                        isPriceFetch: READY
                    }
                };
                // existedPromotionGroup
                if (idx !== -1) {
                    const existedPromotionGroup = newProducts[idx];
                    const isExistedProduct =
                        existedPromotionGroup.cus_ProductInfoBOList[0]
                            .ProductID === selectedPromo.ProductID;
                    // Chọn SP khác trong cùng Chương trình KM -> Replace
                    if (
                        !isExistedProduct ||
                        isPromotionChange[PromotionListGroupID]
                    ) {
                        requestItem.index = idx;
                        request.push(requestItem);
                    }
                } else {
                    request.push(requestItem);
                }
            } else if (idx !== -1) {
                shouldUpdateCS = true;
                // clear in list if found
                // mutate
                newProducts.splice(idx, 1);
            }
        });

        if (request.length > 0) {
            showBlockUI();
            parseProductInfos({
                ...baseRequest,
                promotionGroups: request.map((item) => ({ ...item.value })),
                brandID // Xác định để lấy thông tin Dosage nếu An Khang
            })
                .then((response) => {
                    const applyCSOfPromotions = [];
                    const newPhonePromotionSet = new Set(phonePromoSet);
                    response.forEach((product, idx) => {
                        const { index, crossSelling } = request[idx];
                        product.ApplyProductInfoID = productInfoID;
                        product.IsSelected = true;
                        product.IsFetched = true;
                        product.IsMadePrice = true;
                        product.OutputStoreID = outputStoreID;
                        product.OutputTypeID = outputTypeID;
                        product.IsApplyTotalPromotion = IsApplyTotalPromotion;
                        if (!helper.IsEmptyObject(crossSelling)) {
                            product.DeliveryTypeID = 1;
                        }
                        const { promotionGroupId } = crossSelling;
                        if (promotionsByData?.[promotionGroupId]) {
                            product.cus_ProductListGroupBOList =
                                helper.deepCopy(
                                    promotionsByData[promotionGroupId]
                                );
                        }

                        const newProduct = {
                            id: uuidv4(),
                            ProductIDRef: 0,
                            Image: null,
                            batchId: '',
                            cus_ProductInfoBOList: [{ ...product }],
                            crossSelling: {
                                ...crossSelling
                            },
                            ui_ItemID: product.ItemID
                        };
                        const key = phoneKeyMap.get(promotionGroupId);
                        const hasDiscountPromotion =
                            product.cus_ProductListGroupBOList?.some(
                                (group) => {
                                    return (
                                        group.IsSelected &&
                                        group.ProductLists.some(
                                            (prod) =>
                                                prod.IsDiscount &&
                                                prod.IsSelected
                                        )
                                    );
                                }
                            );
                        const applyPromo = {
                            id: newProduct.id,
                            hasDiscountPromotion
                        };
                        if (index !== -1) {
                            if (key) {
                                newPhonePromotionSet.add(
                                    `${newProduct.id}${key}`
                                );
                            }
                            newProducts[index] = {
                                ...newProduct
                            };
                        } else {
                            const mainProductIndex = newProducts.findIndex(
                                (prod) =>
                                    prod.id ===
                                    crossSelling.appliedCrossSellingById
                            );
                            if (mainProductIndex !== -1) {
                                if (key) {
                                    newPhonePromotionSet.add(
                                        `${newProduct.id}${key}`
                                    );
                                }
                                newProducts.splice(
                                    mainProductIndex + 1,
                                    0,
                                    newProduct
                                );
                            } else {
                                if (key) {
                                    newPhonePromotionSet.add(
                                        `${newProduct.id}${key}`
                                    );
                                }
                                newProducts.unshift(newProduct);
                            }
                        }
                        applyCSOfPromotions.push(applyPromo);
                    });
                    setProducts(newProducts);
                    setPhonePromoSet(newPhonePromotionSet);
                    setApplyPromotions(applyCSOfPromotions);
                })
                .finally(hideBlockUI);
        } else if (shouldUpdateCS) {
            setProducts(newProducts);
        }

        if (shouldUpdateCS) {
            const id = `${listId}_${prodId}_${inventoryStatusID}`;
            setCrossSellingProductsById({
                products: data,
                id
            });
        }
        // set km check SĐT
        setPhonePromoSet(tempPhonePromoSet);
        dispatch(
            updateCustomerInfo({
                CustomerPhone: tempPhoneNumber,
                ContactPhone: tempPhoneNumber
            })
        );
        // });
        crossSellingSheetRef.current.close();
    };

    const handleAddProductToCart = ({ product, batchId = '' }) => {
        const existedProduct = products.find(
            (prod) =>
                prod.ui_ItemID === product.ui_ItemID &&
                prod.batchId === batchId &&
                helper.IsEmptyObject(prod.StoreChange) &&
                helper.IsEmptyObject(prod.crossSelling)
        );
        let listProductCart = [];
        products.forEach((pro) => {
            listProductCart = [
                ...listProductCart,
                ...(pro.cus_ProductInfoBOList ?? [])
            ];
        });
        const prodKey = 'cus_ProductInfoBOList';
        let idx = product[prodKey].findIndex((prod) => prod.IsSelected);
        idx = idx !== -1 ? idx : 0;
        const {
            ProductID,
            IsRequestIMEI,
            IsMadePrice,
            InventoryStatusID,
            IMEI
        } = product[prodKey][idx];
        let isExisted = false;
        const hasCrossSelling = checkHasCrossSelling({
            appliedCrossSelling,
            existedProduct
        });
        if (existedProduct && existedProduct.batchId === batchId) {
            const existedSelectedPro = existedProduct[prodKey].find(
                (prod) => prod.IsSelected
            );
            const isHomeDelivery = existedSelectedPro.DeliveryTypeID === 2;
            isExisted =
                existedSelectedPro &&
                existedSelectedPro.ProductID === ProductID &&
                existedSelectedPro.InventoryStatusID === InventoryStatusID &&
                !isHomeDelivery;
        }
        const fetchPromoId = {};

        if (!IsRequestIMEI && isExisted && !hasCrossSelling) {
            const existedProductBO = existedProduct[prodKey];
            const id = `${existedProduct.id}_${ProductID}_${InventoryStatusID}`;
            if (existedProductBO.BatchLot) {
                existedProductBO.BatchLot.Quantity +=
                    product[prodKey][idx].AppliedQuantity;
            }
            existedProductBO[idx].AppliedQuantity +=
                product[prodKey][idx].AppliedQuantity;
            if (existedProductBO[idx].IsMadePrice) {
                fetchPromoId[existedProduct.id] = 1;
            }

            if (batchNoByCart[id]) {
                const batchIdx = batchNoByCart[id].findIndex(
                    (b) => b.cus_MFBatchNO === batchId
                );
                if (batchIdx !== -1) {
                    const newBatch = [...batchNoByCart[id]];
                    const isOverStock =
                        existedProductBO[idx].AppliedQuantity >
                        newBatch[batchIdx].cus_InstockBatchQuantity;

                    newBatch[batchIdx].Quantity = isOverStock
                        ? newBatch[batchIdx].cus_InstockBatchQuantity
                        : existedProductBO[idx].AppliedQuantity;

                    dispatch(
                        setBatchNoByCart({
                            id,
                            batchNo: newBatch
                        })
                    );
                }
            }

            setPromotionGroups((prevPromoGroups) => ({
                ...prevPromoGroups,
                [id]: {}
            }));
            setProducts(products);
        } else if (
            !!IMEI &&
            listProductCart.some(
                (prod) => prod.IsSelected && prod.IMEI === IMEI
            )
        ) {
            Toast.show({
                type: 'error',
                text1: 'IMEI đã tồn tại trong giỏ hàng.',
                visibilityTime: 2000,
                position: 'top'
            });
        } else {
            product.id = uuidv4();
            product.batchId = batchId;
            products.unshift(product);
            // Đã làm giá và không phải là SP bán kèm
            if (IsMadePrice && !product.crossSelling) {
                // Gọi KM -> getPromotion()
                fetchPromoId[product.id] = 1;
            }
            setProducts(products);
        }

        setFetchPromotion((prevFetch) => ({
            ...prevFetch,
            ...fetchPromoId
        }));

        return product.id ?? '';
    };

    const handleClosePromotionSheet = (promotions) => {
        // Khi người dùng bấm vào nút `Khuyến mãi` -> kiểm tra KM bắt buộc
        if (isSelectedPromo) {
            if (selectedProductId.prodId === null) {
                const hasRequiredPromotionNonSelected = promotions.some(
                    (promo) => {
                        const { data } = promo;
                        const productNames = validatePromotions(data);
                        return productNames.length > 0;
                    }
                );
                if (hasRequiredPromotionNonSelected) {
                    setIsSelectedPromo(false);
                    Toast.show({
                        type: 'error',
                        text1: translate(
                            saleExpress.warning_required_promotions
                        ),
                        visibilityTime: 2000
                    });
                }
            }
        }
    };

    const handleDelete = (product) => {
        if (products.length === 1) {
            dispatch(reset_map_prescriptions());
        }
        let newProducts = products.filter((prod) => prod.id !== product.id);
        // handle clear cross selling product
        newProducts = handleClearCrossSellingProduct({
            newProducts,
            target: product,
            isDelete: true
        });
        const code = findKeyByValueFromMap(scannedCodeMap, product.id);
        code && scannedCodeMap.delete(code);
        setProducts(newProducts);
        // Clear phone promotion map
        const newSet = new Set(phonePromoSet);
        const arrKey = Array.from(phonePromoSet).filter((key) =>
            key.includes(product.id)
        );
        arrKey.forEach((key) => newSet.delete(key));
        setPhonePromoSet(newSet);
        if (productRef.has(product.id)) {
            productRef.delete(product.id);
        }
        if (newSet.size === 0) {
            dispatch(
                updateCustomerInfo({
                    CustomerPhone: '',
                    ContactPhone: ''
                })
            );
        }
        unitSheetRef.current.close();
    };

    const handleMutateClearSelectedCSProducts = (csProducts, index) => {
        const newProductLists = csProducts[index].ProductLists.map(
            (product) => ({
                ...product,
                IsSelected: false
            })
        );
        csProducts[index].IsSelected = false;
        csProducts[index].ProductLists = [...newProductLists];
    };

    const handleClearCrossSellingProduct = ({
        newProducts,
        target,
        isDelete
    }) => {
        let relatedCSProducts = [...newProducts];
        if (target.crossSelling) {
            const { appliedCrossSellingById, promotionGroupId } =
                target.crossSelling;
            const mainProduct = newProducts.find(
                (product) => product.id === appliedCrossSellingById
            );
            const { ProductID: mainProductId, InventoryStatusID } =
                mainProduct.cus_ProductInfoBOList.find(
                    (product) => product.IsSelected
                );
            const id = `${appliedCrossSellingById}_${mainProductId}_${InventoryStatusID}`;
            if (crossSellingProducts[id]) {
                const newCSProducts = [...crossSellingProducts[id]];
                newCSProducts.forEach((promotion, index) => {
                    if (helper.isArray(promotionGroupId)) {
                        promotionGroupId.forEach((groupId) => {
                            if (promotion.PromotionListGroupID === groupId) {
                                handleMutateClearSelectedCSProducts(
                                    newCSProducts,
                                    index
                                );
                            }
                        });
                    } else if (
                        promotion.PromotionListGroupID === promotionGroupId
                    ) {
                        handleMutateClearSelectedCSProducts(
                            newCSProducts,
                            index
                        );
                    }
                });

                setCrossSellingProductsById({
                    products: newCSProducts,
                    id
                });
            }
        } else {
            // Clear all cross-selling products of main product
            relatedCSProducts = newProducts.filter((product) => {
                if (product.crossSelling) {
                    const { appliedCrossSellingById } = product.crossSelling;
                    return appliedCrossSellingById !== target.id;
                }
                return true;
            });
            if (isDelete) {
                const { ProductID, InventoryStatusID } =
                    target.cus_ProductInfoBOList.find(
                        (prod) => prod.IsSelected
                    );
                const id = `${target.id}_${ProductID}_${InventoryStatusID}`;
                const newCS = { ...crossSellingProducts };
                delete newCS[id];

                setCrossSellingProducts({ productsObject: newCS });
            }

            if (relatedCSProducts.length !== newProducts.length) {
                // dispatch(
                //     setCrossSellingProducts({
                //         products: [],
                //         id: deletedProduct.id
                //     })
                // );
            }
        }

        return relatedCSProducts;
    };

    const handleNavigateToCart = ({ dataCart, dataDosages, ...data }) => {
        if (!helper.IsEmptyObject(electricalPrescriptionBO)) {
            const indexPrescriptions = products.findIndex(
                (item) => item.isPrescription
            );
            if (indexPrescriptions == -1) {
                dispatch(reset_map_prescriptions());
            }
        }
        handleClearProvisionalCart(provisionalCartId, TYPE_ID.PROVISIONAL);
        navigation.navigate(CartScreen, {
            data: dataCart,
            dosages: dataDosages,
            totalQuantity,
            ...data
        });
        scannedCodeMap.clear();
    };

    const isValidProducts = () => {
        for (let i = 0; i < products.length; i++) {
            const selectedProduct = products[i].cus_ProductInfoBOList.find(
                (item) => item.IsSelected
            );
            if (selectedProduct) {
                const {
                    InstockQuantity,
                    AppliedQuantity,
                    ProductName,
                    SalePriceVAT,
                    cus_BatchNOBO,
                    ProductID,
                    DeliveryTypeID,
                    WarningMessage
                } = selectedProduct;
                const isHomeDelivery = DeliveryTypeID === 2;
                const isNotEnoughQty =
                    (InstockQuantity === 0 ||
                        AppliedQuantity > InstockQuantity) &&
                    helper.IsEmptyObject(products[i].StoreChange ?? {}) && // Bỏ qua check tồn khi có xin hàng `StoreChange`
                    !isHomeDelivery; // Bỏ qua check tồn khi Giao tại nhà
                const isNotMadePrice = SalePriceVAT === -1;
                const id = `${products[i].id}_${ProductID}`;

                if (isNotEnoughQty || isNotMadePrice) {
                    const msgText = isNotEnoughQty
                        ? translate(saleExpress.not_enough_in_stock)
                        : WarningMessage ||
                        translate(saleExpress.not_made_price);

                    toastSpecificItem({
                        message: translate(
                            saleExpress.warning_validation_product_with_params,
                            {
                                ProductName,
                                msgText
                            }
                        ),
                        index: i
                    });
                    return false;
                }
                // Validation product required Batch but it's not scanned QR
                const hasBatch =
                    !helper.IsEmptyObject(products[i].BatchLot) ||
                    batchNoByCart[id];
                const isStoreChange = !helper.IsEmptyObject(
                    products[i].StoreChange
                );
                // cus_IsBatchManagement -> Có bắt buộc quét QRCode không? (Confused)
                const failQR =
                    cus_BatchNOBO?.cus_IsBatchManagement && !hasBatch;
                if (failQR && !isStoreChange && !isHomeDelivery) {
                    toastSpecificItem({
                        message: 'Sản phẩm bắt buộc quét QR.',
                        index: i
                    });
                    return false;
                }
                const promotionId = `${id}_${selectedProduct.InventoryStatusID}`;
                if (isPromotionFetching[promotionId] === REJECTED) {
                    const msgText = 'Vui lòng lấy lại khuyến mãi';
                    toastSpecificItem({
                        message: translate(
                            saleExpress.warning_validation_product_with_params,
                            {
                                ProductName,
                                msgText
                            }
                        ),
                        index: i
                    });
                    return false;
                }
            }
        }
        return true;
    };

    const handleApiApplyCoupon = (cart, discountCode) => {
        const body = {
            ...baseRequest,
            discountCode,
            shoppingCartRequest: {
                ...cart,
                CustomerInfo: {
                    CustomerPhone: helper.IsNonEmptyString(TempCartContactPhone)
                        ? TempCartContactPhone
                        : customerPhone.current
                }
            }
        };
        return applyCoupon(body);
    };

    const handleApiCreateCart = (productInfoBOList) => {
        const body = {
            ...baseRequest,
            productInfoBOList,
            storeID,
            saleProgramID: 0,
            deliveryTypeID: typeDelivery,
            isInstallment: false,
            saleOrderID: '',
            outputStoreID: storeID
        };
        return createCart(body);
    };

    const handleAddToCart = async (productInfoBOList, shouldCallCoupon) => {
        let cartRes = { ...responseCart };
        try {
            showBlockUI();
            if (helper.IsEmptyObject(cartRes)) {
                cartRes = await handleApiCreateCart(productInfoBOList);
            }
            const isAppliedCouponWithPhoneNumber = helper.hasProperty(
                cartRes.CustomerInfo,
                'ApplyPhoneNumberByDiscountCode'
            );
            if (shouldCallCoupon) {
                cartRes = await handleApiApplyCoupon(cartRes, coupon);
                setEditableCoupon(false);
                dispatch(
                    updateCustomerInfo({
                        CustomerPhone: customerPhone.current,
                        ContactPhone: customerPhone.current
                    })
                );
            } else {
                const hasPromotionProfit = !helper.IsEmptyObject(
                    cartRes.cus_PromDiscountExtraMaster
                );
                if (!hasPromotionProfit) {
                    cartRes = await handleApiPromotionProfit(cartRes);
                    hideBlockUI();
                }

                cartSheetRef.current.snapToIndex(0);
                const {
                    GiftProductListGroups,
                    BundleProductListGroups,
                    ProductCSListGroups
                } = await handleApiGetCartPromotion(cartRes);
                if (
                    GiftProductListGroups.length > 0 ||
                    ProductCSListGroups.length > 0 ||
                    BundleProductListGroups.length > 0
                ) {
                    setCartPromotions({
                        ...cartPromotions,
                        giftPromotion: GiftProductListGroups,
                        csPromotion: ProductCSListGroups,
                        bundlePromotion: BundleProductListGroups
                    });
                } else {
                    const dataDosages = getDosageList({
                        dosageById,
                        cart: products
                    });
                    cartSheetRef.current.close();
                    // if (isAva && typeDelivery !== TYPE_DELIVERY.STORE) {
                    //     setTempInfoCart({
                    //         dataCart: cartRes,
                    //         dataDosages,
                    //         onGoBack: handleClearPhonePromo,
                    //         isDisableCustomerPhone: hasSelectedPhonePromo
                    //     });
                    //     deliverySheetRef.current.snapToIndex(0);
                    // } else {
                    //     handleNavigateToCart({
                    //         dataCart: cartRes,
                    //         dataDosages,
                    //         onGoBack: handleClearPhonePromo,
                    //         isDisableCustomerPhone: hasSelectedPhonePromo
                    //     });
                    // }
                    const infoAddToCart = {
                        dataCart: cartRes,
                        dataDosages,
                        onGoBack: handleClearPhonePromo,
                        isDisableCustomerPhone:
                            hasSelectedPhonePromo ||
                            isAppliedCouponWithPhoneNumber ||
                            isAppliedPhonePromotion.current
                    };
                    setTempInfoCart(infoAddToCart);
                    handleApiPromotionRandom(cartRes, infoAddToCart);
                }
            }
        } catch (errorMsg) {
            if (editableCoupon) {
                customerPhone.current = '';
            }
            if (errorMsg.includes('IsRequirePhone')) {
                hideBlockUI();
                setIsRequirePhone(true);
            } else {
                Alert.alert(translate(common.notification), errorMsg, [
                    {
                        text: translate(common.btn_skip),
                        style: 'cancel',
                        onPress: () => {
                            if (errorMsg?.includes(SUFFIX_COUPON)) {
                                setCoupon('');
                            }
                        }
                    },
                    {
                        text: translate(common.btn_notify_try_again),
                        style: 'default',
                        onPress: () =>
                            handleAddToCart(productInfoBOList, shouldCallCoupon)
                    }
                ]);
            }
        } finally {
            hideBlockUI();
            setResponseCart(cartRes);
        }
    };

    const getRequestAddToCart = (shouldCallCoupon) => {
        const productInfoBOList = [];
        let errorIndex = -1;
        const newProducts = helper.deepCopy(products);
        newProducts.every((product, index) => {
            const selectedProduct = product.cus_ProductInfoBOList.find(
                (prod) =>
                    prod.IsSelected &&
                    !prod.cus_MessageWarning &&
                    !prod.WarningMessage &&
                    prod.SalePriceVAT !== -1 &&
                    prod.AppliedQuantity > 0
            );
            if (selectedProduct) {
                const generatedId = `${product.id}_${selectedProduct.ProductID}`;
                selectedProduct.InventoryStatusID =
                    selectedProduct.InventoryStatusID ?? 1;
                if (batchNoByCart[generatedId]) {
                    selectedProduct.cus_SaleOrderDetailInfoBOList =
                        batchNoByCart[generatedId];
                } else {
                    const { ProductID } = selectedProduct;
                    const batches = [
                        {
                            ...product.BatchLot,
                            ProductID,
                            OutputStoreID: storeID
                        }
                    ];
                    selectedProduct.cus_SaleOrderDetailInfoBOList =
                        product.BatchLot ? batches : null;
                }

                if (product.StoreChange) {
                    selectedProduct.StoreChangeRequests = [
                        { ...product.StoreChange }
                    ];
                }
                // Gửi Km được chọn, giảm size request
                if (selectedProduct.cus_ProductListGroupBOList) {
                    selectedProduct.cus_ProductListGroupBOList =
                        selectedProduct.cus_ProductListGroupBOList.filter(
                            (promoGroup) => {
                                if (promoGroup.IsSelected) {
                                    promoGroup.ProductLists =
                                        promoGroup.ProductLists.filter(
                                            (prod) => prod.IsSelected
                                        );
                                }
                                return promoGroup.IsSelected;
                            }
                        );
                }
                if (isAva) {
                    selectedProduct.DeliveryTypeID = typeDelivery;
                }
                productInfoBOList.push(selectedProduct);
            } else {
                errorIndex = index;
            }
            return errorIndex === -1;
        });
        if (productInfoBOList.length > 0 && errorIndex < 0) {
            handleAddToCart(productInfoBOList, shouldCallCoupon);
        } else {
            toastSpecificItem({
                message: translate(saleExpress.error_can_not_create_cart),
                index: errorIndex
            });
        }
    };
    const handleCart = ({ shouldCreateCart }) => {
        if (isValidProducts()) {
            // Check whether all promotions have been selected
            const isSelectedPromotion = products.every((product) => {
                const selectedProduct = product.cus_ProductInfoBOList.find(
                    (prod) => prod.IsSelected
                );
                if (selectedProduct) {
                    if (selectedProduct.cus_ProductListGroupBOList) {
                        return selectedProduct.cus_ProductListGroupBOList.every(
                            (prod) => prod.IsSelected
                        );
                    }
                    // skip the product having no promotions
                    return true;
                }
                // skip the not found product
                return true;
            });

            if (isSelectedPromo || isSelectedPromotion || shouldCreateCart) {
                getRequestAddToCart();
            } else {
                setSelectedProductId({
                    listId: null,
                    prodId: null,
                    promoGroupId: null,
                    shouldCreateCart: true
                });
                sheetRef.current.snapToIndex(0);
            }
        }
    };

    const getRequestApplyCoupon = () => {
        const validCoupon = helper.IsNonEmptyString(coupon);
        Keyboard.dismiss();
        if (validCoupon) {
            const shouldCallCoupon = true;
            if (editableCoupon) {
                // getRequestAddToCart(shouldCallCoupon);
                if (
                    (hasDiscountPromotion && isSelectedPromo) ||
                    !hasDiscountPromotion
                ) {
                    getRequestAddToCart(shouldCallCoupon);
                } else {
                    Toast.show({
                        type: 'error',
                        text1: translate(saleExpress.warning_apply_coupon),
                        visibilityTime: 2000,
                        position: 'top'
                    });
                    handleOpenPromotionSheet();
                }
            } else {
                handleClearCoupon();
            }
        } else {
            Toast.show({
                type: 'error',
                text1: translate(saleExpress.warning_empty_coupon),
                visibilityTime: 2000,
                position: 'top'
            });
        }
    };

    const handleClearCoupon = () => {
        customerPhone.current = '';
        setResponseCart({});
        setCoupon('');
        setEditableCoupon(true);
    };

    const handleOpenPromotionSheet = () => {
        const hasPromotion = products.some((product) => {
            const selectedProduct = product.cus_ProductInfoBOList.find(
                (prod) => prod.IsSelected
            );
            return selectedProduct?.cus_ProductListGroupBOList;
        });

        setIsSelectedPromo(true);
        if (hasPromotion) {
            setSelectedProductId({
                listId: null,
                prodId: null,
                promoGroupId: null,
                shouldCreateCart: false
            });
            sheetRef.current.snapToIndex(0);
        } else {
            Alert.alert(
                translate(common.notification),
                translate(saleExpress.warning_not_found_promotions),
                [
                    {
                        text: 'OK',
                        style: 'cancel'
                    }
                ]
            );
        }
    };

    const generatePromotionGroups = ({
        newProduct,
        currentPromotionGroups,
        id,
        isUpdate
    }) => {
        const _currentPromotionGroups = { ...currentPromotionGroups };
        if (isUpdate && _currentPromotionGroups[id]) {
            _currentPromotionGroups[id].promotionGroups = [];
            _currentPromotionGroups[id].promoByGroup = {};
        }
        if (newProduct.cus_ProductListGroupBOList) {
            newProduct.cus_ProductListGroupBOList.forEach((prodGroup) => {
                const { PromotionListGroupID, ProductLists } = prodGroup;
                const prodGroups = [
                    ...(_currentPromotionGroups[id]?.promotionGroups ?? [])
                ];
                const promoByGroup = {
                    ...(_currentPromotionGroups[id]?.promoByGroup ?? {})
                };
                const promotionIds = ProductLists.filter(
                    (prod) => prod.IsSelected
                ).map((prod) => prod.ProductID);
                // Dùng nhiều chỗ cùng điều kiện `acceptedPromotionType`
                const acceptedPromotionType =
                    isAcceptedPromotionType(prodGroup);
                if (promotionIds.length > 0) {
                    if (!prodGroups.includes(PromotionListGroupID)) {
                        prodGroups.push(PromotionListGroupID);
                    }
                    _currentPromotionGroups[id] = {
                        promotionGroups: prodGroups,
                        promoByGroup: {
                            ...promoByGroup,
                            [PromotionListGroupID]: promotionIds
                        }
                    };
                } else if (
                    acceptedPromotionType &&
                    !_currentPromotionGroups[id]
                ) {
                    // Khuyến mãi vừa tặng quà vừa giảm tiền
                    _currentPromotionGroups[id] = {
                        promotionGroups: [],
                        promoByGroup: {}
                    };
                }
            });
        } else {
            _currentPromotionGroups[id] = {};
        }
        return _currentPromotionGroups;
    };

    const getCurrentIndex = (index) => {
        setIsSheetOpen(index !== -1);
        if (index === -1) {
            rBatch(() => {
                dispatch(setActiveTab(null));
                dispatch(setActiveCrossSelling(null));
            });
        }
    };

    const handleOnPressProductName = (productIDRef) => {
        setIsVisibleUsageGuide(true);
        setSelectedProductIDRef(productIDRef);
        setWebInfo({
            link: 'tskt',
            title: translate(saleExpress.title_usage_guide)
        });
        sheetRef.current.close();
    };

    const handleSubmitBatch = (objBatch, id) => {
        const { index } = batchModal;
        const generatedId = `${products[index].id}_${id}`;
        const { batch: batchNo } = objBatch;
        dispatch(
            setBatchNoByCart({
                id: generatedId,
                batchNo
            })
        );
    };

    // check sl SP theo inventoryStatusID đang có trong giỏ hàng
    const checkAppliedQuantity = (
        appliedQuantityCurrent,
        ItemID,
        productId,
        inventoryStatusID
    ) => {
        // Tìm SP đó (có id === selectedProductId.proID && === inventoryStatusID)
        let quantity = appliedQuantityCurrent;
        if (products && products.length > 0) {
            for (const item of products) {
                if (
                    item.ui_ItemID === ItemID &&
                    item.cus_ProductInfoBOList.length > 0
                ) {
                    for (const x of item.cus_ProductInfoBOList) {
                        if (
                            x.IsSelected &&
                            x.ProductID === productId &&
                            x.InventoryStatusID === inventoryStatusID
                        ) {
                            quantity += x.AppliedQuantity;
                        }
                    }
                }
            }
        }

        return quantity;
    };

    // check tồn kho khi thay đổi InventoryStatusID
    const handleChangeStatus = (statusId) => {
        const index = products.findIndex(
            (x) => x.id === selectedProductId.listId
        );
        const indexProductSelected = products[
            index
        ].cus_ProductInfoBOList.findIndex(
            (x) => x.ProductID === selectedProductId.prodId
        );
        const productSelected =
            products[index].cus_ProductInfoBOList[indexProductSelected];
        const ItemID = products[index].ui_ItemID;

        if (productSelected.InventoryStatusID !== statusId) {
            // api check
            const body = {
                ...baseRequest,
                productID: productSelected.ProductID,
                inventoryStatusID: statusId,
                appliedQuantity: checkAppliedQuantity(
                    productSelected.AppliedQuantity,
                    ItemID,
                    selectedProductId.prodId,
                    statusId
                ),
                mainGroupID: productSelected.MainGroupID,
                barcode: productSelected.Barcode,
                isRequestIMEI: productSelected.IsRequestIMEI,
                outputStoreID: productSelected.OutputStoreID,
                iMEI: productSelected.IMEI,
                isGetTechSpecs: isAva
            };
            showBlockUI();
            checkInventoryStatus(body)
                .then((response) => {
                    const newProducts = [...products];
                    newProducts[index].cus_ProductInfoBOList = newProducts[
                        index
                    ].cus_ProductInfoBOList.map((product) => ({
                        ...product,
                        InventoryStatusID: statusId,
                        IsFetched: false
                    }));
                    newProducts[index].cus_ProductInfoBOList[
                        indexProductSelected
                    ].InstockQuantity = response.InstockQuantity;

                    handleGetPriceStock(
                        newProducts,
                        index,
                        productSelected.ProductID,
                        newProducts[index],
                        true,
                        response.InstockQuantity
                    );

                    const promotionFetchingId = generateIdPromotionFetch({
                        uuid: products[index].id,
                        productId: productSelected.ProductID,
                        statusId
                    });
                    setFetchPromotion((prevFetch) => ({
                        ...prevFetch,
                        ...promotionFetchingId
                    }));
                    const id = `${products[index].id}_${productSelected.ProductID}_${statusId}`;
                    delete promotionFetchingId[id];
                    setIsPromotionFetching((prevFetch) => ({
                        ...prevFetch,
                        ...promotionFetchingId
                    }));
                })
                .catch((error) => {
                    Alert.alert(translate(common.notification), error, [
                        {
                            text: 'OK',
                            style: 'cancel'
                        }
                    ]);
                    hideBlockUI();
                });

            // có thì đóng
            statusSheetRef.current.close();
        }
    };

    const handleSetProvisionalCarts = async ({
        name,
        time,
        expiredTimeDev,
        typeId,
        isPersistent = false,
        category,
        isUpload
    }) => {
        const productNames = [];
        const productImages = [];
        const isExpired = Date.now() > expiredTime;
        let isResetOrderNumber = false;
        let newExpiredTime;

        const productsWithoutCrossSelling = products
            .filter((product) => !product.crossSelling)
            .map((product) => ({
                ...product,
                cus_ProductInfoBOList: product.cus_ProductInfoBOList.map(
                    (prod) => ({
                        ...prod,
                        cus_ProductListGroupBOList: null,
                        PriceAppliedPromo: prod.SalePriceVAT,
                        InstockQuantity: 0
                    })
                )
            }));

        productsWithoutCrossSelling.forEach((product) => {
            if (!product.crossSelling) {
                const productName = getProductName({
                    product,
                    withBatch: true
                });
                productNames.push(productName);
                product.Image &&
                    !productImages.includes(product.Image) &&
                    productImages.length <= 5 &&
                    productImages.push(product.Image);
            }
        });

        if (isUpload) {
            try {
                const combo = {
                    id: '',
                    name,
                    productImages,
                    quantity: productsWithoutCrossSelling.length,
                    productNames: productNames.join(', '),
                    data: productsWithoutCrossSelling,
                    isPersistent: false,
                    time: Date.now()
                };
                setIsModalLoading(true);
                await uploadCombo({
                    combo,
                    loginStoreId: storeID
                });
                setVisibleConfirm(false);
                clearAllProducts();
                Toast.show({
                    type: 'success',
                    text1: `Tạo combo ${name} thành công!`
                });
            } catch (error) {
                Alert.alert(translate(common.notification), error, [
                    {
                        text: translate(common.btn_skip),
                        style: 'cancel'
                    },
                    {
                        text: translate(common.btn_notify_try_again),
                        style: 'default',
                        onPress: () =>
                            handleSetProvisionalCarts({
                                name,
                                time,
                                expiredTimeDev,
                                typeId,
                                isPersistent,
                                category,
                                isUpload
                            })
                    }
                ]);
            } finally {
                setIsModalLoading(false);
            }
        } else {
            const currentCarts = {
                id: isPersistent ? PERSISTENT_ID : `${typeId}.${uuidv4()}`,
                name,
                time,
                quantity: productsWithoutCrossSelling.length,
                productNames: productNames.join(', '),
                productImages,
                data: productsWithoutCrossSelling,
                isPersistent
            };
            const newStoredCarts = [currentCarts];
            if (!isExpired) {
                provisionalCarts.forEach((cart) => {
                    if (!cart.isPersistent) {
                        newStoredCarts.push(cart);
                    }
                });
            } else {
                const combo = provisionalCarts.filter((cart) =>
                    cart.id.includes(TYPE_ID.COMBO)
                );
                newStoredCarts.push(...combo);
            }

            if (
                !CONFIG.isPRODUCTION &&
                expiredTimeDev > 0 &&
                expiredTimeDev !== expiredTime
            ) {
                newExpiredTime = expiredTimeDev;
            } else if (isExpired) {
                // Reset expired time to next day
                newExpiredTime = 0;
                isResetOrderNumber = true;
            }

            updateProvisionalCartsInfo({
                carts: newStoredCarts,
                time: newExpiredTime,
                isReset: isResetOrderNumber,
                isPersistent
            });
            setVisibleConfirm(false);
            !isPersistent && clearAllProducts();
        }
    };

    const handleQRBatch = (id) => (qr) => {
        const arr = qr.split('.');
        let message = '';
        const [productId, date, batch] = arr;
        if (arr.length > 1 && date?.length === 8) {
            const idx = products.findIndex((prod) => prod.id === id);
            if (idx !== -1) {
                // Kiểm tra mã lô date hợp lệ
                const existedProduct = products[idx].cus_ProductInfoBOList.find(
                    (prod) => prod.ProductID === productId
                );
                const selectedProduct = products[
                    idx
                ].cus_ProductInfoBOList.find((prod) => prod.IsSelected);
                if (existedProduct && selectedProduct) {
                    const newProducts = [...products];
                    const dd = date.slice(0, 2);
                    const mm = date.slice(2, 4);
                    const yyyy = date.slice(-4);
                    const data = {
                        SaleOrderDetailInfoID: null,
                        SaleOrderDetailID: null,
                        SaleOrderID: null,
                        ProductID: productId,
                        ProductName: null,
                        Quantity: selectedProduct.AppliedQuantity,
                        cus_BasicQuantity: 0,
                        InventoryStatusID: selectedProduct.InventoryStatusID,
                        Imei: null,
                        BatchNO: qr,
                        ExpirationDate: `${yyyy}-${mm}-${dd}T00:00:00`,
                        OutputStoreID: 0,
                        CreatedUser: null,
                        CreatedDate: null,
                        UpdatedUser: null,
                        UpdatedDate: null,
                        IsDeleted: false,
                        DeletedUser: null,
                        DeletedDate: null,
                        cus_InstockBatchQuantity: 0,
                        cus_MFBatchNO: batch,
                        cus_Selected: true
                    };
                    newProducts[idx].BatchLot = data;
                    newProducts[idx].batchId = batch;

                    setProducts(newProducts);
                } else {
                    const prod = products[idx].cus_ProductInfoBOList.find(
                        (p) => p.IsSelected
                    );
                    const name = prod?.ProductName || 'sản phẩm được chọn';
                    message = `Mã QR không khớp với ${name}.`;
                }
            }
        } else {
            message = 'Mã QR không đúng định dạng';
        }
        !!message &&
            Toast.show({
                type: 'error',
                text1: message
            });
        closeCameraQR();
    };

    const handleApplyAlter = ({ product, index }) => {
        const newProducts = [...products];
        newProducts[index] = { ...product };

        setPromotionGroups((prevPromoGroups) => ({
            ...prevPromoGroups,
            [product.id]: {}
        }));
        setFetchPromotion((prevFetch) => ({
            ...prevFetch,
            [product.id]: 1
        }));
        setProducts(newProducts);
    };

    const handleMiscDataSheet = (id) => (data) => {
        const index = products.findIndex((prod) => prod.id === id);
        if (index !== -1 && !helper.IsEmptyObject(data)) {
            if (activeTab === SCREENS.AlternativeProducts) {
                handleApplyAlter({ product: data, index });
            } else {
                const newProducts = [...products];
                const newProdBOList = newProducts[index].cus_ProductInfoBOList
                    .filter((_, idx) => idx === data.activeIndex)
                    .map((prod) => ({
                        ...prod,
                        IsSelected: true
                    }));
                const newProd = helper.deepCopy(newProducts[index]);

                newProd.id = uuidv4();
                newProd.BatchLot = null;
                newProd.cus_ProductInfoBOList = helper.deepCopy(newProdBOList);
                const appliedQuantity =
                    data.storeChangeQuantity || // Xin lân cận
                    data.quantity || // Giao tại nhà
                    newProdBOList[0].AppliedQuantity; // Thêm SP mới
                newProd.cus_ProductInfoBOList[0].AppliedQuantity =
                    appliedQuantity;
                newProd.StoreChange = null;
                // turn applied quantity of main product into sellable
                newProducts[index].cus_ProductInfoBOList[
                    data.activeIndex
                ].AppliedQuantity =
                    newProducts[index].cus_ProductInfoBOList[
                        data.activeIndex
                    ].InstockQuantity;
                // Update thông tin giao tại nhà
                if (!helper.IsEmptyObject(data.contactInfo)) {
                    newProd.cus_ProductInfoBOList[0].DeliveryInfo = {
                        DeliveryTypeID: data.deliveryTypeID,
                        DeliveryVehicles: data.vehicleTypeID,
                        DeliveryDistrictID: data.district,
                        DeliveryProvinceID: data.province,
                        DeliveryWardID: data.ward,
                        DeliveryDistance: data.distance,
                        DeliveryStoreID: data.storeID || data.storeId,
                        DeliveryAddress: data.contactInfo.contactAddress,
                        DeliveryTime: data.deliveryTime,
                        ContactPhone: data.contactInfo.contactPhone,
                        ContactName: data.contactInfo.contactName,
                        CustomerNote: data.contactInfo.customerNote,
                        ShippingCost: data.shippingCost,
                        ContactGender: data.contactInfo.gender
                    };
                    newProd.cus_ProductInfoBOList[0].OutputStoreID =
                        data.storeID || data.storeId;
                    newProd.cus_ProductInfoBOList[0].OutputStoreName =
                        data.storeName ?? '';
                    newProd.cus_ProductInfoBOList[0].DeliveryTypeID = 2; // 2 - Giao tại nhà
                    if (
                        data.storeRequests &&
                        !helper.IsEmptyArray(data.storeRequests)
                    ) {
                        // Tồn tại CO
                        newProd.StoreChange = {
                            StockStoreID: data.storeRequests[0].storeID,
                            StoreChangeQuantity: appliedQuantity,
                            ShippingCost: data.storeRequests[0].shippingCost,
                            TransportTypeID:
                                data.storeRequests[0].transportTypeID,
                            StockStoreName: data.storeName,
                            StockStoreAddress: null,
                            GetStockType: data.storeRequests[0].getStockType
                        };
                    }
                } else {
                    // Xin hàng lân cận
                    newProd.StoreChange = {
                        StockStoreID: data.storeID,
                        StoreChangeQuantity: appliedQuantity,
                        ShippingCost: data.shippingCost,
                        TransportTypeID: data.transportTypeID,
                        StockStoreName: data.storeName,
                        StockStoreAddress: null,
                        GetStockType: data.getStockType
                    };
                }
                // TODO: clean this messy
                if (!newProd.cus_ProductInfoBOList[0].IsFetched) {
                    setIsElasticSearching(true);
                    showBlockUI();
                    handleApiFetchInfo({
                        ProductID: newProd.cus_ProductInfoBOList[0].ProductID,
                        ProductIDRef: newProd.ProductIDRef,
                        ImageUrl: newProd.Image,
                        Product: {},
                        action: 'price'
                    })
                        .then((price) => {
                            newProd.cus_ProductInfoBOList[0] = {
                                ...newProd.cus_ProductInfoBOList[0],
                                ...price
                            };
                            newProducts.unshift(newProd);
                            setPromotionGroups((prevPromoGroups) => ({
                                ...prevPromoGroups,
                                [newProd.id]: {}
                            }));
                            setFetchPromotion((prevFetch) => ({
                                ...prevFetch,
                                [newProd.id]: 1
                            }));
                            setProducts(newProducts);
                        })
                        .catch((error) => {
                            Alert.alert(translate(common.notification), error);
                        })
                        .finally(() => {
                            hideBlockUI();
                            setIsElasticSearching(false);
                        });
                } else {
                    newProducts.unshift(newProd);
                    setPromotionGroups((prevPromoGroups) => ({
                        ...prevPromoGroups,
                        [newProd.id]: {}
                    }));
                    setFetchPromotion((prevFetch) => ({
                        ...prevFetch,
                        [newProd.id]: 1
                    }));
                    setProducts(newProducts);
                }
            }
        }
        miscSheetRef.current.close();
    };

    const closeCameraQR = () => {
        setIsVisibleBatchQRScan(false);
        setTimeout(() => {
            setShouldRenderCamera(false);
        }, 700);
    };

    const openCameraQR = () => {
        setIsVisibleBatchQRScan(true);
        setShouldRenderCamera(true);
    };

    const toastSpecificItem = ({ message, index, id, duration = 2000 }) => {
        id = id ?? products[index].id;
        const position = productLayoutRef.get(id) ?? { x: 0, y: 0 };
        Toast.show({
            type: 'error',
            text1: message,
            visibilityTime: duration
        });
        scrollViewRef.current.scrollTo({
            animated: true,
            ...position
        });
        productRef.get(id).highlight();
    };

    const renderProduct = ({ item, index }) => {
        const data =
            item.cus_ProductInfoBOList?.find((product) => product.IsSelected) ??
            item.cus_ProductInfoBOList[0];
        const productSize = item.cus_ProductInfoBOList?.some(
            (product) => product.cus_TechSpecsBO !== null
        );
        const unitsLength = item.cus_ProductInfoBOList?.length ?? 1;
        const backgroundColor = index % 2 === 0 ? COLOR.white : COLOR.bgCart;
        const promotionId = `${item.id}_${data.ProductID}_${data.InventoryStatusID}`;
        const hasBatch =
            !helper.IsEmptyObject(item.BatchLot) || batchNoByCart[promotionId];

        const { maxCount, minCount, step, appliedQuantity } =
            getCounterQuantity({
                crossSelling: item.crossSelling,
                mainProductQuantities: listAppliedQuantityMainProduct,
                appliedQuantity: data.AppliedQuantity,
                inStockQuantity: data.InstockQuantity
            });

        data.AppliedQuantity = appliedQuantity;
        data.IsSelected = true;

        return (
            <ProductItem
                key={item.id}
                ref={(ref) => {
                    if (ref && !productRef.get(item.id)) {
                        productRef.set(item.id, ref);
                    }
                }}
                onLayout={(event) => {
                    productLayoutRef.set(item.id, {
                        x: event.nativeEvent.layout.x,
                        y: event.nativeEvent.layout.y
                    });
                }}
                index={index}
                backgroundColor={backgroundColor}
                shouldCloseSwipeable={openedItemID !== item.id}
                data={data}
                image={item.Image}
                storeChange={item.StoreChange}
                crossSelling={item.crossSelling}
                hasBatch={hasBatch}
                isPromotionFetching={isPromotionFetching[promotionId]}
                isCrossSellFetching={isCrossSellFetching[promotionId]}
                promotionGroup={promotionGroups[promotionId]}
                disabledUnit={unitsLength <= 1 || data.IsRequestIMEI}
                onGetCSPrice={() => onGetCSPrice(item)}
                onDelete={() => handleDelete(item)}
                setOpenedItemID={() => setOpenedItemID(item.id)}
                onOpenQRScan={openCameraQR}
                onChangeQuantity={(quantity, id) => {
                    let newQuantity = quantity;
                    if (item.crossSelling) {
                        const num =
                            quantity -
                            (quantity % item.crossSelling.originQuantity);
                        newQuantity = num > maxCount ? maxCount : num;
                    }
                    setIsChangeQuantityFired(true);
                    handleChangeQuantity(newQuantity, index, id);
                }}
                maxCount={maxCount}
                minCount={minCount}
                step={step}
                onChangeUnit={(id) => {
                    if (productSize) {
                        unitSheetRef.current.snapToIndex(0);
                        setSelectedUnitId({
                            listId: item.id,
                            prodId: data.ProductID
                        });
                    } else {
                        handleChangeUnit(index, id);
                    }
                    statusSheetRef.current.close();
                }}
                onChangePromotion={(promoGroupId) => {
                    setSelectedProductId({
                        listId: item.id,
                        prodId: data.ProductID,
                        promoGroupId,
                        shouldCreateCart: false
                    });
                    sheetRef.current.snapToIndex(0);
                    statusSheetRef.current.close();
                }}
                onRetryGetPromotion={() => {
                    handleApiGetPromotion(data, promotionId);
                }}
                onRetryGetCrossSelling={() => {
                    handleApiGetCrossSell(data, promotionId);
                }}
                onShowModal={() => setIsShowSaleModal(true)}
                setDataModal={setDataModal}
                onPressProductName={() =>
                    handleOnPressProductName(data.ProductID)
                }
                onPressBatchNo={() => {
                    handleApiBatchNo({
                        uuid: item.id,
                        product: data,
                        index
                    });
                }}
                onGetNearest={(tab) => {
                    rBatch(() => {
                        const idx = item.cus_ProductInfoBOList.findIndex(
                            (e) => e.IsSelected
                        );
                        statusSheetRef.current.close();
                        dispatch(setActiveProducts(item.cus_ProductInfoBOList));
                        dispatch(setActiveIndex(idx));
                        dispatch(setActiveTab(tab));
                    });
                }}
                onOpenStatusSheet={() => {
                    setSelectedProductId({
                        listId: item.id,
                        prodId: data.ProductID,
                        promoGroupId: null,
                        shouldCreateCart: false
                    });
                    setSelectedStatusProduct({
                        ProductName: data.ProductName,
                        InventoryStatusID: data.InventoryStatusID
                    });
                    statusSheetRef.current.snapToIndex(0);
                }}
                onNoticeProduct={() => {
                    toastSpecificItem({
                        message: `Đây là sản phẩm chính của ${data.ProductName}`,
                        id: item.crossSelling.appliedCrossSellingById,
                        duration: 3000
                    });
                }}
                onGetProductLock={() => {
                    handleGetLockProductInfo(data);
                }}
            />
        );
    };

    useEffect(() => {
        if (helper.IsNonEmptyArray(listProductLock)) {
            lockProductSheetRef.current.snapToIndex(0);
        }
    }, [listProductLock]);

    const handleGetLockProductInfo = (productInfo) => {
        showBlockUI();
        const body = {
            loginStoreId: storeID,
            languageID,
            moduleID,
            storeID,
            imei: productInfo.IMEI,
            productID: productInfo.ProductID,
            productIDRef: productInfo.ProductIDRef,
            inventoryStatusID: productInfo.InventoryStatusID,
            saleProgramID: 0,
            isInstalment: false
        };
        getInfoProductLock(body)
            .then((listProduct) => {
                hideBlockUI();
                setInfoLockProduct({
                    instockQuantity: productInfo.InstockQuantity,
                    productName: productInfo.ProductName,
                    quantity: productInfo.Quantity,
                    lockQuantity: productInfo.LockQuantity
                });
                setListProductLock(listProduct);
            })
            .catch((msgError) => {
                setInfoLockProduct({});
                Alert.alert(translate(common.notification), msgError, [
                    {
                        text: translate(common.btn_skip),
                        style: 'cancel',
                        onPress: hideBlockUI
                    },
                    {
                        text: translate(common.btn_notify_try_again),
                        style: 'default',
                        onPress: () => handleGetLockProductInfo(productInfo)
                    }
                ]);
            });
    };

    const handlePersistNewProduct = () => {
        handleSetProvisionalCarts({
            name: 'Giỏ hàng đang thao tác',
            time: Date.now(),
            expiredTimeDev: 0,
            isPersistent: true
        });
    };

    const handleInitPhonePromotion = () => {
        setTempPhonePromoSet(phonePromoSet);
        const promotionPhoneNumber = helper.IsNonEmptyString(tempPhoneNumber)
            ? tempPhoneNumber
            : TempCartContactPhone;
        dispatch(setTempPhoneNumber(promotionPhoneNumber));
    };
    const handleSelectPhonePromotion = ({ key, phoneNumber }) => {
        const newSet = new Set(tempPhonePromoSet);
        const isValidPhone = helper.isValidatePhone(phoneNumber);
        if (isValidPhone) {
            newSet.add(key);
            // isAppliedPhonePromotion.current = true;
            dispatch(
                updateCustomerInfo({
                    ContactPhone: phoneNumber,
                    TempCartContactPhone: phoneNumber
                })
            );
            dispatch(setTempPhoneNumber(phoneNumber));
            customerPhone.current = phoneNumber;
        } else {
            newSet.delete(key);
            dispatch(setTempPhoneNumber(''));
            customerPhone.current = '';
            // isAppliedPhonePromotion.current = false;
        }
        // Clear phone if map is empty or store phone if that's first update
        // if (
        //     (!isValidPhone && newSet.size === 0) ||
        //     (isValidPhone && newSet.size === 1)
        // ) {
        //     dispatch(setTempPhoneNumber(phoneNumber));
        // }

        setTempPhonePromoSet(newSet);
    };
    const handleClearProvisionalCart = (id, type) => {
        if (id && id.includes(type)) {
            removeProvisionalCartById(id);
        }
    };

    const handleEffectProductsParams = (productsParams) => {
        showBlockUI();
        const fetchPromoId = {};
        const newProductsParams = productsParams.map((product) => {
            const prod = {
                ...product,
                id: uuidv4()
            };
            fetchPromoId[prod.id] = 1;
            return prod;
        });
        const newProducts = [...newProductsParams, ...products];
        setProducts(newProducts);
        const productInfoPromises = newProductsParams.map((product) => {
            const { ProductID } = product.cus_ProductInfoBOList.find(
                (prod) => prod.IsSelected
            );
            const body = {
                ...baseRequest,
                inventoryStatusID: null,
                storeID,
                saleProgramID: 0,
                deliveryTypeID: typeDelivery,
                isInstallment: false,
                keyword: null,
                saleOrderID: '',
                productRequest: {
                    ProductIDRef: product.ProductIDRef,
                    Image: product.Image,
                    ProductID
                },
                isGetInfoByIMEI: false,
                isGetTechSpecs: isAva
            };
            const Product = isCareCustomer ? product : {}
            return searchProduct(body, Product);
        });
        Promise.all(productInfoPromises)
            .then((result) => {
                const newFetchProducts = newProductsParams.map(
                    (product, index) => {
                        const productSelected =
                            product.cus_ProductInfoBOList.find(
                                (pro) => pro.IsSelected
                            );
                        const indexSelected = result[
                            index
                        ].cus_ProductInfoBOList.findIndex(
                            (element) => element.IsSelected
                        );
                        result[index].cus_ProductInfoBOList[
                            indexSelected
                        ].AppliedQuantity =
                            productSelected.AppliedQuantity ?? 1;
                        return {
                            ...product,
                            ...result[index]
                        };
                    }
                );
                setProducts([...newFetchProducts, ...products]);
                setFetchPromotion((prevFetch) => ({
                    ...prevFetch,
                    ...fetchPromoId
                }));
            })
            .catch(() => {
                setProducts([...products]);
                Alert.alert(
                    translate(common.notification),
                    'Lỗi không lấy được tồn kho và giá của sản phẩm.'
                );
            })
            .finally(hideBlockUI);
    };

    const showBlockUI = () => {
        setLoadingBlock(true);
    };
    const hideBlockUI = () => {
        setLoadingBlock(false);
    };

    useEffect(() => {
        if (isNumericKeyword && helper.IsEmptyArray(stateSearchProducts.data)) {
            setIsNumericKeyword(false);
            handleReadBarcode({
                code: keyword,
                productRequest: null,
                isScan: false
            });
        }
    }, [isNumericKeyword, stateSearchProducts, keyword]);

    useEffect(() => {
        if (drugs?.length > 0) {
            if (provisionalCartId) {
                handleEffectProductsParams(drugs);
            } else {
                // Thuốc cắt liều
                for (const drug of drugs) {
                    handleAddProductToCart({
                        product: drug,
                        batchId: drug.batchId
                    });
                }
            }

            setIsElasticSearching(false);
            setIsSelectedPromo(false);
            handleClearCoupon();
        }
    }, [drugs, provisionalCartId]);
    const onGetCSPrice = (product) => {
        if (product.crossSelling) {
            const newProducts = [...products];
            const indexPro = newProducts.findIndex(
                (item) => item.id === product.id
            );
            newProducts[indexPro].crossSelling = {
                ...product.crossSelling,
                isPriceFetch: PENDING
            };
            setProducts(newProducts);
            const body = {
                ...baseRequest,
                inventoryStatusID: 0,
                storeID,
                saleProgramID: 0,
                deliveryTypeID: typeDelivery,
                isInstallment: false,
                keyword: null,
                saleOrderID: '',
                productRequest: {
                    ProductIDRef: product.ProductIDRef,
                    Image: product.ImageUrl,
                    ProductID: product.cus_ProductInfoBOList[0].ProductID
                },
                isGetInfoByIMEI: false,
                isGetTechSpecs: isAva
            };
            getProductPrice(body)
                .then((result) => {
                    const {
                        StandardPriceAreaSalePrice,
                        StandardSalePrice,
                        CostPrice
                    } = result;
                    const newNewProducts = [...newProducts];
                    newNewProducts[indexPro].crossSelling = {
                        ...newNewProducts[indexPro].crossSelling,
                        isPriceFetch: FULFILLED
                    };
                    newNewProducts[indexPro].cus_ProductInfoBOList[0] = {
                        ...newNewProducts[indexPro].cus_ProductInfoBOList[0],
                        StandardPriceAreaSalePrice,
                        StandardSalePrice,
                        CostPrice
                    };

                    setProducts(newNewProducts);
                })
                .catch(() => {
                    const newNewProducts = [...newProducts];
                    newNewProducts[indexPro].crossSelling = {
                        ...newNewProducts[indexPro].crossSelling,
                        isPriceFetch: REJECTED
                    };

                    setProducts(newNewProducts);
                });
        }
    };

    useEffect(() => {
        // eslint-disable-next-line guard-for-in
        for (const id in responsePromo) {
            const uuid = id.split('_')[0];
            if (Object.prototype.hasOwnProperty.call(responsePromo, id)) {
                if (
                    responsePromo[id] &&
                    products.find((prod) => prod.id === uuid)
                ) {
                    const { productInfo, response, isUpdate } =
                        responsePromo[id];

                    productInfo.PriceAppliedPromo = response.PriceAppliedPromo;
                    if (!isUpdate) {
                        productInfo.cus_ProductListGroupBOList =
                            response.cus_ProductListGroupBOList;
                    }
                    const idx = products.findIndex((prod) => prod.id === uuid);
                    if (idx !== -1) {
                        const prodIdx = products[
                            idx
                        ].cus_ProductInfoBOList.findIndex(
                            (prod) => prod.ProductID === productInfo.ProductID
                        );
                        if (prodIdx !== -1) {
                            products[idx].cus_ProductInfoBOList[prodIdx] =
                                productInfo;
                            setIsPromotionFetching((prevState) => ({
                                ...prevState,
                                [id]: FULFILLED
                            }));

                            setProducts(products);

                            //
                            const newPromoGroups = generatePromotionGroups({
                                newProduct: response,
                                currentPromotionGroups: { ...promotionGroups },
                                id,
                                isUpdate
                            });
                            setPromotionGroups(newPromoGroups);
                        }
                    }

                    setResponsePromo((prevResponsePromo) => ({
                        ...prevResponsePromo,
                        [id]: null
                    }));
                }
            }
        }
    }, [responsePromo, products, promotionGroups]);

    useEffect(() => {
        PrevTotalQty.current = totalQty;
    }, [totalQty]);

    useEffect(() => {
        if (freeze) {
            setTimeout(() => {
                setFreeze(false);
            }, TIMEOUT);
        }
    }, [freeze]);

    useEffect(() => {
        if (keyword === null || keyword.length === 0) {
            setShowListSearch(false);
        }
    }, [keyword]);

    useEffect(() => {
        for (const uuid in fetchPromotion) {
            if (Object.prototype.hasOwnProperty.call(fetchPromotion, uuid)) {
                if (fetchPromotion[uuid] > 0 && products.length > 0) {
                    const latestProduct = products.find(
                        (product) => product.id === uuid
                    );
                    if (latestProduct) {
                        const selectedProduct =
                            latestProduct.cus_ProductInfoBOList.find(
                                (product) => product.IsSelected
                            ) ?? latestProduct.cus_ProductInfoBOList[0];

                        // promotionsId
                        const id = `${uuid}_${selectedProduct.ProductID}_${selectedProduct.InventoryStatusID}`;
                        if (!isPromotionFetching[id]) {
                            setFetchPromotion((prevFetch) => ({
                                ...prevFetch,
                                [uuid]: 0
                            }));
                            handleApiGetPromotion(selectedProduct, id);
                            handleApiGetCrossSell(selectedProduct, id);
                        }
                    }
                }
            }
        }
    }, [fetchPromotion, products, promotionGroups]);

    useEffect(() => {
        const backHandler = BackHandler.addEventListener(
            'hardwareBackPress',
            () => {
                if (showListSearch) {
                    setKeyword('');
                    return true;
                }
            }
        );
        return () => backHandler.remove();
    }, [showListSearch]);

    useEffect(() => {
        if (showListSearch) {
            sheetRef.current.close();
            cartSheetRef.current.close();
            statusSheetRef.current.close();
        }
    }, [showListSearch]);

    useEffect(() => {
        if (
            !isWebSearching &&
            !isElasticSearching &&
            !showListSearch &&
            products.length > 0
        ) {
            // Lưu tạm giỏ hàng đang thao tác
            // Điều kiện: Tải dữ liệu xong -> sản phẩm mới được thêm vào lưới
            // hoặc thay đổi SL bán hoặc thay đổi đơn vị bán
            handlePersistNewProduct();
        }
    }, [products, isWebSearching, isElasticSearching, showListSearch]);

    useEffect(() => {
        if (products.length === 0) {
            setTotalQty(1);
            dispatch(resetCartInfo());
            setResponseCart({});
            statusSheetRef.current.close();
        } else {
            setEditableCoupon(true);
        }
    }, [products]);

    useEffect(() => {
        // Reset cart response when promotions changed or products changed
        if (products.length > 0) {
            const { length } = products;
            length !== cartLength && dispatch(setCartLength({ length }));
            setResponseCart({});
        }
    }, [products, isElasticSearching, isPromotionFetching]);

    // Products là những sản phẩm size màu
    useEffect(() => {
        if (selectedUnitId.listId) {
            const { listId, prodId } = selectedUnitId;
            const selectedList = products.find((prod) => prod.id === listId);
            if (selectedList) {
                const selectedProduct = selectedList.cus_ProductInfoBOList.find(
                    (prod) => prod.ProductID === prodId
                );
                const newSelectedList = selectedList.cus_ProductInfoBOList.map(
                    (item) => {
                        if (item.cus_TechSpecsBO) {
                            return {
                                ProductID: item.ProductID,
                                ProductColorID: item.ProductColorID,
                                cus_TechSpecsBO: [
                                    {
                                        ...item.cus_TechSpecsBO,
                                        ProductID: item.ProductID,
                                        ProductName: item.ProductName
                                    }
                                ]
                            };
                        } else {
                            return {
                                ProductID: item.ProductID,
                                ProductColorID: item.ProductColorID,
                                cus_TechSpecsBO: []
                            };
                        }
                    }
                );
                const listProductColor = newSelectedList.reduce(
                    (curListColor, curProduct) => {
                        const index = curListColor.findIndex(
                            (v) =>
                                v.ProductColorID === curProduct.ProductColorID
                        );
                        if (index === -1) {
                            curListColor.push(curProduct);
                        } else {
                            curListColor[index].cus_TechSpecsBO.push(
                                ...curProduct.cus_TechSpecsBO
                            );
                        }
                        return curListColor;
                    },
                    []
                );

                setTechSpecSelected(selectedProduct);
                setTechSpec(listProductColor);
            }
        }
    }, [selectedUnitId]);

    useEffect(() => {
        if (activeTab) {
            miscSheetRef.current.snapToIndex(0);
            statusSheetRef.current.close();
        }
    }, [activeTab, miscSheetRef]);

    useEffect(() => {
        if (activeCrossSell) {
            crossSellingSheetRef.current.snapToIndex(0);
        }
    }, [activeCrossSell, crossSellingSheetRef]);

    useEffect(() => {
        if (applyPromotions.length > 0) {
            let currentPromotionGroups = { ...promotionGroups };
            applyPromotions.forEach((item) => {
                const index = products.findIndex(
                    (product) => product.id === item.id
                );
                if (index !== -1) {
                    const productInfo = products[index];
                    const newProduct = products[index].cus_ProductInfoBOList[0];
                    const id = `${productInfo.id}_${newProduct.ProductID}_${newProduct.InventoryStatusID}`;
                    if (item.hasDiscountPromotion) {
                        setIsPromotionFetching((prevState) => ({
                            ...prevState,
                            [id]: PENDING
                        }));

                        applyPromotion({ productInfo: newProduct })
                            .then((response) => {
                                setResponsePromo((prevResPromo) => ({
                                    ...prevResPromo,
                                    [id]: {
                                        productInfo: newProduct,
                                        response,
                                        isUpdate: true
                                    }
                                }));
                            })
                            .catch((error) => {
                                Alert.alert(
                                    translate(common.notification),
                                    error,
                                    [
                                        {
                                            text: 'OK',
                                            style: 'cancel'
                                        }
                                    ]
                                );
                                setIsPromotionFetching((prevState) => ({
                                    ...prevState,
                                    [id]: FULFILLED
                                }));
                            });
                    } else {
                        currentPromotionGroups = generatePromotionGroups({
                            newProduct,
                            id,
                            currentPromotionGroups,
                            isUpdate: true
                        });
                    }
                }
            });

            setPromotionGroups(currentPromotionGroups);
            setApplyPromotions([]);
        }
    }, [applyPromotions, products]);

    useEffect(() => {
        dispatch(set_init_screen(Pharmacy));
    }, []);

    useEffect(() => {
        if (products.length > 0) {
            const newProducts = [];
            const newAppliedCS = {};
            const newDosageById = {};
            // const newProducts = products.filter((item) => !item.crossSelling);
            products.forEach((product) => {
                if (!product.crossSelling) {
                    newProducts.push(product);
                } else if (!isChangeQuantityFired) {
                    const { crossSelling, id } = product;
                    // handle cross selling
                    const mainId = crossSelling.appliedCrossSellingById;
                    if (newAppliedCS[mainId]) {
                        newAppliedCS[mainId] = [...newAppliedCS[mainId], id];
                    } else {
                        newAppliedCS[mainId] = [id];
                    }
                }
                // handle dosage
                if (isAnKhang) {
                    const { ui_ItemID, cus_ProductInfoBOList } = product;
                    if (!dosageById[ui_ItemID]) {
                        const selectedProduct =
                            cus_ProductInfoBOList.find(
                                (item) => item.IsSelected
                            ) ?? cus_ProductInfoBOList[0];
                        if (selectedProduct.SaleOrderDoSageBO) {
                            const { ProductID, ProductName, ItemID } =
                                selectedProduct;
                            newDosageById[ui_ItemID] = {
                                ProductID,
                                ProductName,
                                ItemID,
                                ...INITIAL_PROPS
                            };
                        }
                    } else {
                        newDosageById[ui_ItemID] = {
                            ...dosageById[ui_ItemID]
                        };
                    }
                }
            });

            let listApplyQuantity = {};
            const originDosageLength = Object.values(dosageById).length;
            const newDosageLength = Object.values(newDosageById).length;
            if (helper.IsNonEmptyArray(newProducts)) {
                listApplyQuantity = newProducts.reduce((obj, cur) => {
                    const productSelected = cur.cus_ProductInfoBOList.find(
                        (pro) => pro.IsSelected
                    );
                    const quantityID = `${cur.id}_${productSelected.ProductID}_${productSelected.InventoryStatusID}`;
                    return {
                        ...obj,
                        [quantityID]: productSelected.AppliedQuantity
                    };
                }, {});
            }
            setListAppliedQuantityMainProduct(listApplyQuantity);
            !helper.IsEmptyObject(newAppliedCS) &&
                setAppliedCrossSelling(newAppliedCS);
            if (originDosageLength !== newDosageLength) {
                setDosageById(newDosageById);
            }
        }

        if (isChangeQuantityFired) {
            setIsChangeQuantityFired(false);
        }
    }, [products]);

    const getDataHistory = async () => {
        let dataSearchHistory = [];
        if (isAva) {
            dataSearchHistory = await storageHelper.getDataAvaHistory();
        } else {
            dataSearchHistory = await storageHelper.getDataMedicalHistory();
        }
        setProductHistorys(dataSearchHistory);
    };

    useEffect(() => {
        getDataHistory();
    }, [keyword]);

    useEffect(() => {
        if (productHistorys.length === 0 && clearHistorySearch > 0) {
            isAva
                ? storageHelper.resetDataAvaHistory()
                : storageHelper.resetDataMedicalHistory();
        }
    }, [clearHistorySearch, productHistorys]);

    useLayoutEffect(() => {
        if (!isFocused) {
            setShowListSearch(false);
            sheetRef.current.close();
            cartSheetRef.current.close();
            dosageSheetRef.current.close();
            miscSheetRef.current.close();
            unitSheetRef.current.close();
            statusSheetRef.current.close();
            lockProductSheetRef.current.close();

            // deliverySheetRef.current.close();
            randomPromotionSheetRef.current.close();
        }
    }, [
        isFocused,
        setShowListSearch,
        sheetRef,
        cartSheetRef,
        dosageSheetRef,
        miscSheetRef,
        unitSheetRef,
        statusSheetRef,
        deliverySheetRef,
        lockProductSheetRef,
        randomPromotionSheetRef
    ]);

    useEffect(() => {
        if (helper.IsNonEmptyString(coupon) && stateScanCoupon.isScaned) {
            getRequestApplyCoupon();
            setStateScanCoupon({ ...stateScanCoupon, isScaned: false });
        }
    }, [coupon, stateScanCoupon.isScaned]);

    useEffect(() => {
        if (isClearCart) {
            clearAllProducts();
        }
    }, [isClearCart]);

    const { totalAmount, totalQuantity } = useMemo(() => {
        const initialValue = {
            totalAmount: 0,
            totalQuantity: 0
        };
        return products.reduce((currentValue, product) => {
            const selectedProduct = product.cus_ProductInfoBOList.find(
                (item) => item.IsSelected
            );
            const { PriceAppliedPromo, SalePriceVAT, AppliedQuantity } =
                selectedProduct;
            const finalPrice =
                PriceAppliedPromo >= 0
                    ? PriceAppliedPromo
                    : Math.max(SalePriceVAT, 0);
            return {
                totalAmount:
                    finalPrice * AppliedQuantity + currentValue.totalAmount,
                totalQuantity: AppliedQuantity + currentValue.totalQuantity
            };
        }, initialValue);
    }, [products, isElasticSearching, isPromotionFetching]);

    const hasDiscountPromotion = useMemo(() => {
        const hasPromotion = products.some((product) => {
            const selectedProduct = product.cus_ProductInfoBOList.find(
                (prod) => prod.IsSelected
            );
            if (selectedProduct?.cus_ProductListGroupBOList) {
                return selectedProduct.cus_ProductListGroupBOList.some(
                    (promotionGroup) => {
                        // Dùng nhiều chỗ cùng điều kiện `acceptedPromotionType`
                        return isAcceptedPromotionType(promotionGroup);
                    }
                );
            }
            return selectedProduct?.cus_ProductListGroupBOList;
        });
        return hasPromotion;
    }, [products, isPromotionFetching]);

    const isEmptyCrossSelling = helper.IsEmptyObject(crossSellingProducts);

    const disabledNext = useMemo(() => {
        let isLoading = false;
        for (const id in isPromotionFetching) {
            if (isPromotionFetching[id] === PENDING) {
                isLoading = true;
                break;
            }
        }
        return isLoading;
    }, [isPromotionFetching]);

    const theme = useTheme();
    const COLOR = theme.colors;

    const placeholderSearch = isAva
        ? translate(keys.saleExpress.placeholder_search_ava)
        : translate(keys.saleExpress.placeholder_search_drugs);

    const hasSelectedPhonePromo = phonePromoSet.size > 0;
    // Render here
    return (
        <FabWrapper products={products}>
            {/* <SafeAreaView style={styles.container}> */}
            <KeyboardAvoidingView
                style={{
                    position: 'absolute',
                    height: '100%',
                    width: '100%'
                }}
                behavior="height"
                keyboardVerticalOffset={
                    Platform.OS === 'ios'
                        ? PRICE_AREA + ACTION_AREA + 20
                        : PRICE_AREA
                }>
                {!isShowCamera && (
                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            marginHorizontal: 18,
                            marginVertical: 10
                        }}
                        onLayout={({ nativeEvent }) => {
                            searchHeight.current = nativeEvent.layout.height;
                        }}>
                        <SearchBar
                            onFocus={() => {
                                unitSheetRef.current.close();
                                cartSheetRef.current.close();
                                sheetRef.current.close();
                                statusSheetRef.current.close();
                            }}
                            setShowListSearch={setShowListSearch}
                            value={keyword}
                            placeholder={placeholderSearch}
                            onChangeText={(text) => {
                                setKeyword(text);
                                debounceSearchWebProducts(text);
                            }}
                            onPressBarcode={() =>
                                setIsShowCamera((prev) => !prev)
                            }
                            onSearch={() => {
                                debounceSearchWebProducts.stop();
                                handleOnSearchProduct(keyword);
                            }}
                        />
                    </View>
                )}
                {isShowCamera && isFocused && (
                    <BarcodeLimitScope
                        onClose={() => setIsShowCamera(false)}
                        expandHeight={searchHeight.current}
                        onReadCode={(code) => {
                            !showListSearch &&
                                !isSheetOpen &&
                                !isVisibleUsageGuide &&
                                handleReadBarcode({
                                    code,
                                    productRequest: null,
                                    isScan: true
                                });
                        }}
                    />
                )}
                {helper.IsNonEmptyArray(productHistorys) && !isShowCamera && (
                    <ProductHistory
                        ids={productHistorys}
                        callAction={(product) => {
                            handleOnSearchProduct(product.code);
                            setKeyword(product.code);
                        }}
                        onDelete={() => {
                            setProductHistorys([]);
                            setClearHistorySearch((prev) => prev + 1);
                        }}
                    />
                )}

                {/* <ProductSearch
                    ids={[
                        '1314741000177',
                        '1314741000177',
                        'HIEN43242',
                        '1063024000002'
                    ]}
                    callAction={handleReadBarcode}
                /> */}
                <SaleModal
                    isShowModal={isShowSaleModal}
                    onClose={() => setIsShowSaleModal(false)}
                    data={dataModal}
                />
                {products && products.length > 0 ? (
                    <View style={{ flex: 1 }}>
                        <View
                            style={[
                                styles.bar,
                                { borderTopColor: COLOR.gray }
                            ]}>
                            <View style={styles.cartShadow}>
                                <CartCount
                                    rowCount={products.length}
                                    quantity={totalQuantity}
                                />
                                <TouchableOpacity
                                    style={{ marginRight: 15 }}
                                    onPress={() => {
                                        setVisibleConfirm(true);
                                    }}>
                                    <Icon
                                        iconSet="MaterialIcons"
                                        name="save"
                                        color={COLOR.primary}
                                        size={24}
                                    />
                                </TouchableOpacity>
                                {!isAva && (
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            alignItems: 'center'
                                        }}>
                                        <MyText
                                            style={{
                                                fontWeight: 'bold',
                                                textAlign: 'left',
                                                fontSize: 14,
                                                marginRight: 8
                                            }}>
                                            {`×${totalQty}`}
                                        </MyText>
                                        {/* <CapsuleButton
                                            disabled={totalQty <= 1}
                                            onPress={() => {
                                                handleChangeTotalQuantity(
                                                    totalQty - 1
                                                );
                                            }}
                                            direction="left"
                                        />
                                        <CapsuleButton
                                            value={totalQty}
                                            onPress={() => {
                                                handleChangeTotalQuantity(
                                                    totalQty + 1
                                                );
                                            }}
                                            direction="right"
                                        /> */}
                                        <Counter
                                            key={update}
                                            withoutRange
                                            quantity={totalQty}
                                            onIncrease={() => {
                                                handleChangeTotalQuantity(
                                                    totalQty + 1
                                                );
                                                setTemQuantity(totalQty + 1);
                                            }}
                                            onDecrease={() => {
                                                handleChangeTotalQuantity(
                                                    totalQty - 1
                                                );
                                                setTemQuantity(totalQty - 1);
                                            }}
                                            height={30}
                                            width={constants.width / 4 - 10}
                                            onChangeQuantity={(quantity) => {
                                                setTemQuantity(quantity);
                                            }}
                                            onBlur={() => {
                                                if (totalQty !== temQuantity) {
                                                    handleChangeTotalQuantity(
                                                        temQuantity
                                                    );
                                                }
                                            }}
                                            nonAllowZero
                                        />
                                    </View>
                                )}
                                <TouchableOpacity
                                    style={{ marginLeft: 35 }}
                                    onPress={confirmClearProducts}>
                                    <Icon
                                        iconSet="AntDesign"
                                        name="delete"
                                        color={COLOR.red}
                                        size={30}
                                    />
                                </TouchableOpacity>
                            </View>
                        </View>
                        {/* <Animated.FlatList
                            ref={scrollViewRef}
                            data={products}
                            renderItem={renderProduct}
                            layout={Layout.springify()}
                            itemLayoutAnimation={Layout.springify()}
                            keyExtractor={(item) => item.id}
                            keyboardDismissMode="on-drag"
                            keyboardShouldPersistTaps="never"
                        /> */}
                        <Animated.ScrollView
                            ref={scrollViewRef}
                            keyboardDismissMode="on-drag"
                            keyboardShouldPersistTaps="never">
                            {products.map((product, index) =>
                                renderProduct({ item: product, index })
                            )}
                        </Animated.ScrollView>
                        {/* ACTIONS AREA */}
                        {!showListSearch && (
                            <View
                                style={[
                                    styles.bar,
                                    styles.barShadow,
                                    {
                                        backgroundColor: COLOR.gray,
                                        borderTopColor: COLOR.gray
                                    }
                                ]}>
                                <CouponField
                                    value={coupon}
                                    onChangeValue={setCoupon}
                                    onPress={getRequestApplyCoupon}
                                    editable={editableCoupon}
                                    openCamera={() =>
                                        setStateScanCoupon({
                                            ...stateScanCoupon,
                                            isShow: true
                                        })
                                    }
                                />
                                <ScrollView
                                    style={{ flex: 1 }}
                                    bounces={false}
                                    showsHorizontalScrollIndicator={false}
                                    horizontal>
                                    <Button
                                        onPress={handleOpenPromotionSheet}
                                        outline={!isSelectedPromo}
                                        disabled={!hasDiscountPromotion}
                                        text={translate(
                                            saleExpress.title_promotions
                                        )}
                                        color={isAva ? COLOR.primary : null}
                                    />
                                    <Button
                                        style={{ marginHorizontal: 5 }}
                                        text={translate(
                                            saleExpress.title_sale_promotions
                                        )}
                                        disabled={isEmptyCrossSelling}
                                        color={isAva ? COLOR.primary : null}
                                        onPress={() => {
                                            dispatch(
                                                setActiveCrossSelling(true)
                                            );
                                        }}
                                    />
                                    {!isAva && (
                                        <View style={{ flexDirection: 'row' }}>
                                            <Button
                                                onPress={() => {
                                                    if (
                                                        !helper.IsEmptyObject(
                                                            dosageById
                                                        )
                                                    ) {
                                                        dosageSheetRef.current.snapToIndex(
                                                            0
                                                        );
                                                    } else {
                                                        Toast.show({
                                                            type: 'error',
                                                            text1: translate(
                                                                saleExpress.warning_no_dosage
                                                            ),
                                                            visibilityTime: 2000
                                                        });
                                                    }
                                                }}
                                                text={translate(
                                                    saleExpress.title_dosage
                                                )}
                                            />
                                        </View>
                                    )}
                                </ScrollView>
                            </View>
                        )}
                    </View>
                ) : (
                    <Pressable style={{ flex: 1 }} onPress={Keyboard.dismiss}>
                        {isAva ? (
                            <View
                                style={{
                                    flex: 1,
                                    alignItems: 'center',
                                    justifyContent: 'center'
                                }}>
                                <View
                                    style={{
                                        width: '80%',
                                        height: '60%'
                                    }}>
                                    <Lottie
                                        ref={animationRef}
                                        source={
                                            typeDelivery === TYPE_DELIVERY.STORE
                                                ? require('../../../assets/animation_Store.json')
                                                : require('../../../assets/animation_Truck.json')
                                        }
                                        // source={sale_christmas}
                                        loop
                                        autoPlay
                                    />
                                </View>
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        justifyContent: 'center'
                                    }}>
                                    {/* <Image
                                        style={{
                                            width: 40,
                                            height: 40,
                                            resizeMode: 'contain'
                                        }}
                                        source={
                                            typeDelivery === TYPE_DELIVERY.STORE
                                                ? require('../../../assets/store.png')
                                                : require('../../../assets/truck.png')
                                        }
                                    /> */}
                                    <MyText
                                        style={{
                                            fontWeight: 'bold',
                                            marginLeft: 15
                                        }}>
                                        {typeDelivery === TYPE_DELIVERY.STORE
                                            ? 'Bạn đang tạo đơn hàng nhận tại siêu thị.'
                                            : 'Bạn đang tạo đơn hàng giao tận nơi.'}
                                    </MyText>
                                </View>
                            </View>
                        ) : (
                            <View
                                style={{
                                    flex: 1,
                                    alignItems: 'center',
                                    justifyContent: 'center'
                                }}>
                                <Image
                                    style={styles.barcodeImage}
                                    source={require('../../../assets/scanbarcode.jpg')}
                                />
                                <MyText
                                    style={{
                                        fontWeight: 'bold',
                                        marginTop: 15
                                    }}>
                                    {translate(
                                        saleExpress.scan_to_find_product
                                    )}
                                </MyText>
                            </View>
                        )}

                        <View
                            style={[
                                styles.bar,
                                { borderTopColor: COLOR.gray }
                            ]}>
                            <View
                                style={[
                                    styles.barShadow,
                                    {
                                        height: 40,
                                        backgroundColor: COLOR.gray
                                    }
                                ]}>
                                <MyText
                                    style={{
                                        color: COLOR.lightBlue,
                                        fontSize: 16,
                                        fontWeight: 'bold'
                                    }}>
                                    {translate(saleExpress.payment_info)}
                                </MyText>
                            </View>
                        </View>
                    </Pressable>
                )}
                {/* PRICE AREA */}
                <View
                    style={[
                        styles.bottomContainer,
                        styles.totalPriceContainer,
                        {
                            marginBottom: constants.heightBottomSafe > 0 ? 0 : 8
                        }
                    ]}>
                    <View style={{ flex: 1 }}>
                        {responseCart.SHCouponDiscountAmount > 0 && (
                            <PriceText
                                fontSize={18}
                                label="Coupon"
                                value={responseCart.SHCouponDiscountAmount}
                            />
                        )}
                        <PriceText
                            fontSize={18}
                            label={translate(saleExpress.provisional_price)}
                            value={
                                responseCart.SHAmount > 0
                                    ? responseCart.SHAmount
                                    : totalAmount || 0
                            }
                            valueColor={COLOR.lightRed}
                        />
                    </View>
                    {products.length > 0 && (
                        <Button
                            style={{
                                ...styles.btnNext,
                                opacity: disabledNext ? 0.6 : 1,
                                backgroundColor: COLOR.primary
                            }}
                            color={COLOR.primary}
                            disabled={disabledNext}
                            onPress={() =>
                                handleCart({ shouldCreateCart: false })
                            }
                            text={translate(common.btn_continue)}
                            outline={false}
                            isLoading={disabledNext}
                        />
                    )}
                </View>
            </KeyboardAvoidingView>

            {showListSearch && (
                <View style={styles.listSearchContainer}>
                    <BaseLoading
                        isLoading={isWebSearching}
                        isEmpty={stateSearchProducts.isEmpty}
                        textLoadingError={stateSearchProducts.description}
                        isError={stateSearchProducts.isError}
                        colorText={COLOR.primary}
                        onPressTryAgains={() => handleOnSearchProduct(keyword)}
                        content={
                            <FlatList
                                data={stateSearchProducts.data}
                                renderItem={({ item }) => (
                                    <ProductSearchItem
                                        data={item}
                                        onPress={handleReadBarcode}
                                    />
                                )}
                                keyboardShouldPersistTaps="never"
                                keyboardDismissMode="on-drag"
                                keyExtractor={(item) => item.ProductID}
                            />
                        }
                    />
                </View>
            )}

            <PromotionSheet
                ref={sheetRef}
                productId={selectedProductId}
                products={products}
                onGoNext={handleChangePromotion}
                onCloseSheet={handleClosePromotionSheet}
                getCurrentIndex={getCurrentIndex}
                onInitPhonePromotion={handleInitPhonePromotion}
                onSelectPhonePromotion={handleSelectPhonePromotion}
                enablePanDownToClose={false}
            />
            <CrossSellingSheet
                ref={crossSellingSheetRef}
                products={products}
                snapPoints={['99.99%']}
                onGoNext={handleChangeCrossSellingPromotion}
                onInitPhonePromotion={handleInitPhonePromotion}
                onSelectPhonePromotion={handleSelectPhonePromotion}
                getCurrentIndex={getCurrentIndex}
            />
            <CartPromotionSheet
                ref={cartSheetRef}
                data={cartPromotions}
                getCurrentIndex={getCurrentIndex}
                onGoNext={(
                    promotions,
                    campaignPhone,
                    loyaltyInfo,
                    newProductInfos
                ) => {
                    handleApiApplyCartPromotion({
                        cartPromo: promotions,
                        cart: responseCart,
                        campaignPhone,
                        loyaltyInfo,
                        newProductInfos,
                        typePromotion: TYPE_PROMOTION.TOTAL_PROMOTION
                    });
                }}
                hasSelectedPhonePromo={hasSelectedPhonePromo}
                shopCart={responseCart}
            />
            <CartRandomPromotionSheet
                ref={randomPromotionSheetRef}
                data={randomPromotions}
                getCurrentIndex={getCurrentIndex}
                onGoNext={(promotions, campaignPhone, loyaltyInfo) => {
                    handleApiApplyCartPromotion({
                        cartPromo: promotions,
                        cart: responseCart,
                        campaignPhone,
                        loyaltyInfo,
                        newProductInfos: [],
                        typePromotion: TYPE_PROMOTION.RANDOM_PROMOTION
                    });
                }}
                onCloseSheet={(closeSheet) => {
                    setResponseCart({});
                }}
                hasSelectedPhonePromo={hasSelectedPhonePromo}
                shopCart={responseCart}
            />
            <DeliveryProvider>
                <DeliverySheet
                    products={products}
                    ref={deliverySheetRef}
                    shopCart={responseCart}
                    onGoNext={(dataCart) => {
                        updateDeliveryInfo(dataCart);
                    }}
                    onCloseSheet={(closeSheet) => {
                        setResponseCart({});
                        if (coupon) {
                            setEditableCoupon(true);
                        }
                        randomPromotionSheetRef.current.close();
                    }}
                    getCurrentIndex={getCurrentIndex}
                    snapPoints={['99.99%']}
                    isDisableCustomerPhone={helper.IsNonEmptyString(
                        tempInfoCart?.dataCart?.CustomerInfo
                            ?.ApplyPhoneNumberByDiscountCode
                    )}
                />
            </DeliveryProvider>

            {batchModal.visible && (
                <BatchInfo
                    data={batchModal.value}
                    isShowModal={batchModal.visible}
                    productName={batchModal.ProductName}
                    totalQuantity={batchModal.totalQuantity}
                    allowToChangeLess={batchModal.allowToChangeLess}
                    editable={false}
                    totalInStock={10}
                    onSubmit={(objBatch) => {
                        handleSubmitBatch(objBatch, batchModal.id);
                    }}
                    onClose={() =>
                        setBatchModal({
                            visible: false,
                            value: [],
                            ProductName: '',
                            totalQuantity: 0,
                            id: '',
                            index: null,
                            allowToChangeLess: false
                        })
                    }
                />
            )}
            <PrescriptionSheet
                ref={dosageSheetRef}
                dosageById={dosageById}
                onGoNext={handleChangePrescription}
                getCurrentIndex={getCurrentIndex}
                disabled={false}
            />
            <MiscProvider>
                <MiscSheet
                    ref={miscSheetRef}
                    scrollWrap={false}
                    snapPoints={['99.99%']}
                    getCurrentIndex={getCurrentIndex}
                    onGoNext={handleMiscDataSheet(openedItemID)}
                    products={products}
                />
            </MiscProvider>
            <ModalUsageGuide
                isVisible={isVisibleUsageGuide}
                hideModal={() => setIsVisibleUsageGuide(false)}
                webInfo={webInfo}
                productInfo={{ productIDRef: selectedProductIDRef }}
                brandID={brandID}
                backgroundColor={isAva ? '#ED1164' : COLORS.bg2FB47C}
            />
            {shouldRenderCamera && (
                <BarcodeCamera
                    modalProps={{
                        animationIn: 'slideInLeft',
                        animationOut: 'slideOutRight',
                        swipeDirection: ['right', 'down']
                    }}
                    isVisible={isVisibleBatchQRScan}
                    closeCamera={closeCameraQR}
                    resultScanBarcode={handleQRBatch(openedItemID)}
                />
            )}
            <UnitSheet
                ref={unitSheetRef}
                snapPoints={['50%']}
                onGoNext={(data) => {
                    const { ProductID } = data;
                    handleChangeTechSpec(ProductID);
                }}
                techSpec={techSpec}
                techSpecSelected={techSpecSelected}
                getCurrentIndex={getCurrentIndex}
                disabled={false}
            />
            <ModalConfirm
                visible={visibleConfirm}
                isModalLoading={isModalLoading}
                onClose={() => setVisibleConfirm(false)}
                onSubmit={handleSetProvisionalCarts}
            />
            <Indicator visible={loadingBlock} />
            <StatusSheet
                ref={statusSheetRef}
                product={selectedStatusProduct}
                onGoNext={(statusID) => {
                    handleChangeStatus(statusID);
                }}
            />
            <LockProductSheet
                snapPoints={['99.99999%']}
                ref={lockProductSheetRef}
                products={listProductLock}
                infoLockProduct={infoLockProduct}
                onGoNext={(statusID) => {
                    handleChangeStatus(statusID);
                }}
                onclose={() => {
                    setListProductLock([]);
                }}
            />
            {stateScanCoupon.isShow && (
                <BarcodeCamera
                    modalProps={{
                        animationIn: 'slideInLeft',
                        animationOut: 'slideOutRight',
                        swipeDirection: ['right', 'down']
                    }}
                    isVisible={stateScanCoupon.isShow}
                    closeCamera={() => {
                        setStateScanCoupon({
                            ...stateScanCoupon,
                            isShow: false
                        });
                    }}
                    resultScanBarcode={(code) => {
                        if (helper.IsNonEmptyString(code)) {
                            setCoupon(code);
                            setStateScanCoupon({
                                ...stateScanCoupon,
                                isShow: false,
                                isScaned: true
                            });
                        }
                    }}
                />
            )}
            {isRequirePhone && (
                <PopupPhone
                    isVisible={isRequirePhone}
                    title={translate(
                        'shoppingCart.popup_phone_coupon_requires_phone'
                    )}
                    onCancel={() => {
                        setIsRequirePhone(false);
                    }}
                    onConFirm={(phone) => {
                        customerPhone.current = phone;
                        setIsRequirePhone(false);
                        setTimeout(getRequestApplyCoupon, 300);
                    }}
                />
            )}
        </FabWrapper>
    );
};

const styles = StyleSheet.create({
    bar: {
        borderTopWidth: 1,
        overflow: 'hidden',
        paddingBottom: 3
    },
    barShadow: {
        alignItems: 'center',
        // backgroundColor: COLOR.gray,
        elevation: 5,
        flexDirection: 'row',
        height: ACTION_AREA + 8,
        paddingHorizontal: 8,
        shadowColor: COLORS.bg000000,
        shadowOffset: { width: 1, height: 1 },
        shadowOpacity: 0.4,
        shadowRadius: 3,
        width: '100%'
    },
    barcodeImage: {
        height: '60%',
        width: '60%'
    },
    bottomContainer: {
        backgroundColor: COLORS.bgFFFFFF,
        paddingHorizontal: 18
    },
    btnNext: {
        alignItems: 'center',
        borderRadius: 5,
        marginLeft: 20
    },
    cartShadow: {
        alignItems: 'center',
        backgroundColor: COLORS.bgFFFFFF,
        elevation: 3,
        flexDirection: 'row',
        height: ACTION_AREA + 8,
        paddingHorizontal: 16,
        shadowColor: COLORS.bg000000,
        shadowOffset: { width: 1, height: 1 },
        shadowOpacity: 0.4,
        shadowRadius: 3,
        width: '100%'
    },
    container: {
        backgroundColor: COLORS.bgFFFFFF,
        flex: 1,
        position: 'relative'
    },
    listSearchContainer: {
        backgroundColor: COLORS.bgFFFFFF,
        height: '100%',
        left: 0,
        paddingBottom: 50,
        position: 'absolute',
        top: 58,
        width: '100%',
        zIndex: 100
    },
    totalPriceContainer: {
        alignItems: 'center',
        flexDirection: 'row',
        paddingTop: 8
    }
});

export const Indicator = ({ visible }) => (
    <View
        // eslint-disable-next-line react-native/no-color-literals
        style={{
            flex: 1,
            backgroundColor: '#00000033',
            zIndex: visible ? 999999 : -999999,
            position: 'absolute',
            left: 0,
            top: 0,
            width: visible ? '100%' : 0,
            height: visible ? constants.height : 0
        }}>
        {!visible ? null : (
            <View
                style={{
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center'
                }}>
                <LottieView
                    autoPlay
                    source={material_wave_loading}
                    style={{
                        height: 100,
                        width: 100
                    }}
                />
            </View>
        )}
    </View>
);

const ProductSearch = ({ ids, callAction }) => (
    <View
        style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            padding: 10
        }}>
        {ids.map((id, idx) => (
            <Button
                key={id}
                onPress={() =>
                    callAction({
                        code: id,
                        productRequest: null,
                        isScan: true
                    })
                }
                outline
                text={idx.toString()}
                style={{ marginRight: 5 }}
            />
        ))}
    </View>
);
const ProductHistory = ({ ids, callAction, onDelete }) => (
    <View style={{ paddingBottom: 12 }}>
        <View
            style={{
                justifyContent: 'space-between',
                flexDirection: 'row',
                marginBottom: 5,
                paddingHorizontal: 20
            }}>
            <MyText
                style={{
                    fontWeight: 'bold',
                    fontSize: 12,
                    color: COLORS.txt333333
                }}
                text="Lịch sử tìm kiếm"
            />
            <TouchableOpacity onPress={onDelete}>
                <MyText
                    style={{
                        fontWeight: 'bold',
                        fontSize: 12,
                        color: COLORS.txtDA0000
                    }}
                    text="Xoá"
                />
            </TouchableOpacity>
        </View>
        <View style={{ paddingHorizontal: 10 }}>
            <ScrollView
                showsHorizontalScrollIndicator={false}
                horizontal
                contentContainerStyle={{
                    flexGrow: 1,
                    alignItems: 'center'
                }}>
                {ids.map((id, idx) => (
                    <Button
                        styleTextButton={{ fontSize: 11 }}
                        key={id}
                        onPress={() =>
                            callAction({
                                code: id,
                                productRequest: null,
                                isScan: true
                            })
                        }
                        outline
                        text={id.toString()}
                        style={{ marginRight: 5, fontSize: 10 }}
                    />
                ))}
            </ScrollView>
        </View>
    </View>
);

export default MainScreen;
