import React, { useMemo, useRef, forwardRef, useImperativeHandle, useCallback } from 'react';
import { StyleSheet, View } from 'react-native';
import BottomSheet, { BottomSheetBackdrop, BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { COLORS } from '@styles';
import { constants } from '@constants';
import { Button } from './';
import useTheme from '../useTheme';

const Sheet = forwardRef(
    (
        {
            children,
            onGoNext,
            contentContainerStyle,
            isLoading,
            isError,
            snapPoints: snapPointsProp = ['80%'],
            scrollWrap = true,
            buttonText = 'Tiếp tục',
            isShowButton = true,
            enablePanDownToClose = true,
            ...props
        },
        ref
    ) => {
        // ref
        const bottomSheetRef = useRef(null);

        // variables
        const snapPoints = useMemo(() => [...snapPointsProp], [snapPointsProp]);

        useImperativeHandle(ref, () => ({
            snapToIndex: (index) => {
                bottomSheetRef.current.snapToIndex(index);
            },
            close: () => {
                bottomSheetRef.current.close();
            }
        }));
        const theme = useTheme() ?? {};
        const COLOR = theme?.colors ?? {};

        const renderBackdrop = useCallback(
            props => (
                <BottomSheetBackdrop
                    {...props}
                    disappearsOnIndex={-1}
                    appearsOnIndex={0}
                    pressBehavior={"none"}
                />
            ),
            []
        );
        // renders
        return (
            <BottomSheet
                enableContentPanningGesture={false}
                ref={bottomSheetRef}
                index={-1}
                snapPoints={snapPoints}
                enablePanDownToClose={enablePanDownToClose}
                keyboardBlurBehavior="restore"
                style={styles.bottomSheet}
                backdropComponent={renderBackdrop}
                {...props}>
                <View style={[styles.contentContainer, contentContainerStyle]}>
                    {props.Header}
                    {scrollWrap ? (
                        <BottomSheetScrollView keyboardShouldPersistTaps="handled" contentContainerStyle={{ flexGrow: 1 }}>
                            {children}
                        </BottomSheetScrollView>
                    ) : (
                        children
                    )}
                </View>
                {!isLoading && !isError && isShowButton && (
                    <View
                        style={{
                            flexDirection: 'row',
                            justifyContent: 'center',
                            paddingHorizontal: 16
                        }}>
                        <Button
                            onPress={onGoNext}
                            style={{
                                borderRadius: 5,
                                marginBottom: constants.heightBottomSafe || 10,
                                width: '100%',
                                alignItems: 'center',
                                marginTop: 8
                            }}
                            color={COLOR.primary ?? COLORS.bg2FB47C}
                            text={buttonText}
                            outline={false}
                        />
                    </View>
                )}
            </BottomSheet>
        );
    }
);

const styles = StyleSheet.create({
    bottomSheet: {
        backgroundColor: COLORS.bgFFFFFF,
        borderRadius: 20,
        elevation: 5,
        shadowColor: COLORS.bg000000,
        shadowOffset: {
            width: 0,
            height: 2
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84
    },
    contentContainer: {
        flex: 1
    }
});

export default Sheet;
