import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    Image,
    Animated
} from 'react-native';
import React, {
    useEffect,
    useRef,
    useMemo,
    useState,
    useCallback,
    forwardRef,
    useImperativeHandle
} from 'react';
import { DotIndicator } from 'react-native-indicators';
import { useSelector, useDispatch } from 'react-redux';
import { Icon, MyText } from '@components';
import { v4 as uuidv4 } from 'uuid';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import { COLORS } from '@styles';
import { helper, dateHelper } from '@common';
import { constants, ENUM } from '@constants';
import { keys, translate } from '@translate';
import {
    FETCHING_STATE,
    SCREENS,
    ITEM,
    INVENTORY_STATUS_ID,
    INVENTORY_STATUS_NAME_KEY
} from '../constants';
import { getKFactor, getStandardPoint, setStandardPointById } from '../action';
import { styles as style } from '../styles';
import Counter from './Counter';
import useTheme from '../useTheme';
import { useBrandCheck } from '../hooks';
import { useItemByBrandID } from '../helpers';
import TooltipWrapper from '../../ShoppingCart/component/TooltipWrapper';

const { PENDING, REJECTED, READY } = FETCHING_STATE;
const { saleExpress, common } = keys;
const { NEW, CLOSER_TO_DATE } = INVENTORY_STATUS_ID;

const renderRightActions = ({ progress, actions }) => {
    const baseSize = 64;
    const { length } = actions;
    return (
        <View
            style={{
                width: baseSize * length,
                flexDirection: 'row'
            }}>
            {actions.map((action, index) => (
                <RightAction
                    key={action.icon}
                    {...action}
                    x={baseSize * (length - index)}
                    progress={progress}
                />
            ))}
        </View>
    );
};

const renderLeftActions =
    (onOpenQRScan, COLOR, disabledBatchNo, hasBatch, onGetProductLock, isAva) =>
        (_progress, dragX) => {
            const scale = dragX.interpolate({
                inputRange: [0, 80],
                outputRange: [0, 1],
                extrapolate: 'clamp'
            });
            return (
                // eslint-disable-next-line react/jsx-fragments
                <>
                    {!disabledBatchNo && !hasBatch && (
                        <TouchableOpacity
                            onPress={() => {
                                onOpenQRScan();
                            }}
                            style={[
                                styles.swipeBtnContainer,
                                {
                                    backgroundColor: COLOR.lightOrange
                                }
                            ]}>
                            <Animated.View
                                style={[
                                    styles.swipeBtn,
                                    { transform: [{ scale }] }
                                ]}>
                                <Icon
                                    iconSet="MaterialCommunityIcons"
                                    name="qrcode-scan"
                                    color={COLOR.lightBlack}
                                    size={48}
                                />
                            </Animated.View>
                        </TouchableOpacity>
                    )}
                    <TouchableOpacity
                        onPress={onGetProductLock}
                        style={[
                            styles.swipeBtnContainer,
                            {
                                backgroundColor: COLOR.primary
                            }
                        ]}>
                        <Animated.View
                            style={[styles.swipeBtn, { transform: [{ scale }] }]}>
                            <Icon
                                iconSet="MaterialCommunityIcons"
                                name="lock"
                                color={COLOR.white}
                                size={35}
                            />
                            <MyText
                                addSize={-3}
                                style={{ color: COLOR.white, marginTop: 5 }}
                                text="Xem lock hàng"
                            />
                        </Animated.View>
                    </TouchableOpacity>
                </>
            );
        };

const ProductItem = forwardRef(
    (
        {
            backgroundColor,
            data,
            image,
            hasBatch,
            storeChange,
            crossSelling,
            onDelete,
            setOpenedItemID,
            onChangeQuantity,
            onChangeUnit,
            onChangePromotion,
            onRetryGetPromotion,
            onRetryGetCrossSelling,
            onOpenQRScan,
            onGetNearest,
            promotionGroup,
            isPromotionFetching,
            isCrossSellFetching,
            disabledUnit,
            onShowModal,
            setDataModal,
            onPressProductName,
            onPressBatchNo,
            onNoticeProduct,
            shouldCloseSwipeable,
            index,
            maxCount,
            minCount,
            step,
            onOpenStatusSheet,
            onGetCSPrice,
            onGetProductLock,
            ...props
        },
        ref
    ) => {
        const theme = useTheme();
        const COLOR = theme.colors;
        const {
            IMEI,
            ProductName,
            SalePriceVAT,
            QuantityUnitName,
            InstockQuantity,
            AppliedQuantity,
            ProductID,
            cus_ProductListGroupBOList,
            cus_MessageWarning,
            cus_BatchNOBO,
            IsMadePrice,
            InventoryStatusID,
            cus_TechSpecsBO,
            DeliveryTypeID,
            DeliveryInfo,
            PriceAppliedPromo,
            OutputStoreID,
            OutputStoreName,
            IsRequestIMEI,
            InventoryStatusName,
            WarningMessage
        } = data;
        const [visibleTooltip, setVisibleTooltip] = useState(false)
        const animatedBackground = useRef(new Animated.Value(0)).current;
        const { appliedCrossSellingById } = crossSelling ?? {};
        const [temQuantity, setTemQuantity] = useState(1);
        const [update, setUpdate] = useState(0);
        const [coefficient, setCoefficient] = useState(0)
        const shakeAnimation = useRef(new Animated.Value(0)).current;
        const id = `${ProductID}_${InventoryStatusID}`;
        const refSwipe = useRef(null);
        const dispatch = useDispatch();
        const { standardPointById } = useSelector(
            (state) => state._pharmacyReducer
        );
        const { storeID, moduleID, languageID } = useSelector((state) => state.userReducer);
        const isAva = useBrandCheck(ENUM.BRAND_ID.AVA);
        const handleApiGetStandardPoint = ({
            productID,
            inventoryStatusID
        }) => {
            const body = { productID, inventoryStatusID };
            dispatch(
                setStandardPointById({
                    id: `${productID}_${inventoryStatusID}`,
                    standardPoint: 0,
                    toDate: ''
                })
            );
            dispatch(getStandardPoint(body));
            // ignore error
        };

        const isShowStatus = !IMEI
        const isCrossSellingProduct = !helper.IsEmptyObject(crossSelling);
        const isStoreChange = !helper.IsEmptyObject(storeChange);
        const isHomeDelivery = DeliveryTypeID === 2;
        const isOtherDeliveryType = isStoreChange || isHomeDelivery;
        const isDisableCounter = isOtherDeliveryType || IsRequestIMEI;
        const isPriceDiscounted = SalePriceVAT - PriceAppliedPromo > 0;
        const finalPrice = isPriceDiscounted ? PriceAppliedPromo : SalePriceVAT;
        const totalFinalPrice = finalPrice * AppliedQuantity;
        // SalePrice > 0 => Có giá thì xem như là đã làm giá => Bỏ qua warning message
        const isPriceHandled =
            (IsMadePrice && !cus_MessageWarning) || SalePriceVAT > 0;
        // cus_IsBatchManagement -> Có bắt buộc quét QRCode không? (Confused)
        const disabledBatchNo =
            !cus_BatchNOBO?.cus_IsBatchManagement ||
            !isPriceHandled ||
            isOtherDeliveryType;
        const colorUnit = disabledUnit ? COLOR.darkGray : COLOR.primary;
        const isPriceFetching = !SalePriceVAT && !cus_MessageWarning;
        const isAllowedChangeStatus =
            !isCrossSellingProduct && !isOtherDeliveryType;

        const { promoByGroup, promotionGroups } = promotionGroup ?? {};
        const valueUnit = cus_TechSpecsBO
            ? `${cus_TechSpecsBO.TechSpecsValue ?? cus_TechSpecsBO.ColorName}`
            : QuantityUnitName;
        const shouldRenderPriceFetching =
            crossSelling?.isPriceFetch === PENDING ||
            crossSelling?.isPriceFetch === REJECTED;

        useEffect(() => {
            if (shouldCloseSwipeable) {
                refSwipe.current.close();
            }
        }, [shouldCloseSwipeable]);

        useEffect(() => {
            if (standardPointById[id] === undefined && !isCrossSellingProduct) {
                handleApiGetStandardPoint({
                    productID: ProductID,
                    inventoryStatusID: InventoryStatusID
                });
            }
        }, [standardPointById, ProductID, InventoryStatusID]);

        useEffect(() => {
            if (
                !helper.IsEmptyObject(crossSelling) &&
                crossSelling.isPriceFetch === READY
            ) {
                onGetCSPrice();
            }
            !isAva && getBonusConversionCoefficient()
        }, [data]);

        const promotions = useMemo(() => {
            if (promoByGroup && promotionGroups && cus_ProductListGroupBOList) {
                const products = [];
                promotionGroups.forEach((groupId) => {
                    promoByGroup[groupId].forEach((prodId) => {
                        const groupIndex = cus_ProductListGroupBOList.findIndex(
                            (group) => group.PromotionListGroupID === groupId
                        );
                        if (groupIndex !== -1) {
                            const product = cus_ProductListGroupBOList[
                                groupIndex
                            ].ProductLists.find(
                                (prod) => prod.ProductID === prodId
                            );
                            product && products.push({ ...product, groupId });
                        }
                    });
                });
                if (products.length > 0) {
                    // Filter những con SP giảm tiền
                    return products.filter(
                        (product) => product.ProductID !== null
                    );
                } else {
                    // Khuyến mãi loại 3 (PromotionProgramType) Vừa tặng quà Vừa KM
                    // Thêm prop `Default` để phần biệt
                    // products.push({
                    //     ProductID: uuidv4(),
                    //     ProductName: 'Khuyến mãi chưa được chọn.',
                    //     Default: true
                    // });
                    // return products;
                }
            }
            return [];
        }, [promotionGroups, cus_ProductListGroupBOList]);

        useImperativeHandle(ref, () => ({ highlight }));

        const getListDiscountPrice = () => {
            const discountPrices = [];

            if (!helper.IsEmptyObject(crossSelling)) {
                discountPrices.push({
                    PromotionID: crossSelling.PromotionID,
                    PromotionListGroupName: crossSelling.PromotionListGroupName,
                    IsSalePromotionListGroup: true
                });
            }

            data.cus_ProductListGroupBOList?.forEach((productListGroup) => {
                const selectedPromo = productListGroup.ProductLists.filter(
                    (promo) => promo.IsSelected && promo.IsDiscount
                );
                if (selectedPromo && selectedPromo.length > 0) {
                    discountPrices.push({
                        PromotionID: productListGroup.PromotionID,
                        PromotionListGroupName:
                            productListGroup.PromotionListGroupName,
                        PromotionList: selectedPromo
                    });
                }
            });
            return discountPrices;
        };

        const getBonusConversionCoefficient = () => {
            const body = {
                "loginStoreId": storeID,
                "languageID": languageID,
                "moduleID": moduleID,
                "brandId": 8,
                "inventoryStatusId": InventoryStatusID,
                "productId": ProductID
            }
            getKFactor(body).then((k_factor) => {
                setCoefficient(k_factor)
            })
        }

        const imageDefault = useItemByBrandID({
            item: ITEM.DEFAULT_IMAGE
        });
        const source = image ? { uri: image } : imageDefault;
        let standardPointText = '';
        let storeChangeInfoText = '';
        if (
            !helper.IsEmptyObject(standardPointById[id]) &&
            standardPointById[id].value > 0
        ) {
            const { value, toDate } = standardPointById[id];
            const text = translate(saleExpress.hot_bonus);
            standardPointText = `${text}: ${helper.convertMaskString(
                value
            )} (${dateHelper.formatStrDateDDMM(toDate)})`;
        }
        if (isHomeDelivery && !helper.IsEmptyObject(DeliveryInfo)) {
            storeChangeInfoText = DeliveryInfo.DeliveryAddress
                ? translate(saleExpress.shipping_info, {
                    address: DeliveryInfo.DeliveryAddress
                })
                : `Hàng xuất đi giao từ siêu thị: ${OutputStoreID} - ${OutputStoreName}`;
        } else if (isStoreChange) {
            const { StockStoreID, StockStoreName } = storeChange;
            storeChangeInfoText = translate(saleExpress.store_change_info, {
                id: StockStoreID,
                name: StockStoreName
            });
        }

        const remountKey = useMemo(() => {
            if (disabledBatchNo || hasBatch) {
                return uuidv4();
            }
            return uuidv4();
        }, [disabledBatchNo, hasBatch]);

        const rightActions = useMemo(() => {
            const baseActions = [
                {
                    text: translate(keys.common.delete),
                    icon: 'delete',
                    color: COLOR.ERROR_500,
                    onPress: onDelete
                }
            ];
            const isAllowedMoreActions =
                statusVicinity.has(`${InventoryStatusID}`) &&
                // statusAllowVicinty &&
                !isOtherDeliveryType &&
                !isCrossSellingProduct &&
                !IMEI;
            if (isAllowedMoreActions) {
                baseActions.unshift({
                    text: translate(saleExpress.title_vicinity),
                    icon: 'map-marker-radius',
                    color: COLOR.PRIMARY_500,
                    onPress: () => {
                        onGetNearest(SCREENS.NearestStore);
                        refSwipe.current.close();
                    }
                });
                if (!isAva) {
                    baseActions.unshift({
                        text: translate(saleExpress.title_alternative),
                        icon: 'dots-horizontal',
                        color: COLOR.WARNING_500,
                        onPress: () => {
                            onGetNearest(SCREENS.AlternativeProducts);
                            refSwipe.current.close();
                        }
                    });
                }
            }
            return baseActions;
        }, [isOtherDeliveryType, onDelete, isPriceHandled]);

        const boxInterpolation = animatedBackground.interpolate({
            inputRange: [0, 1],
            outputRange: [backgroundColor, COLOR.PRIMARY_RGBA_8]
        });

        const animatedStyle = {
            backgroundColor: boxInterpolation
        };

        const animationColor = useCallback(() => {
            Animated.sequence([
                Animated.timing(animatedBackground, {
                    toValue: 1,
                    duration: 400,
                    useNativeDriver: false
                }),
                Animated.timing(animatedBackground, {
                    toValue: 0,
                    duration: 400,
                    useNativeDriver: false
                })
            ]).start();
        }, []);
        const shake = useCallback(() => {
            Animated.loop(
                // runs the animation array in sequence
                Animated.sequence([
                    // shift element to the left by 2 units
                    Animated.timing(shakeAnimation, {
                        toValue: -3,
                        duration: 50,
                        useNativeDriver: false
                    }),
                    // shift element to the right by 2 units
                    Animated.timing(shakeAnimation, {
                        toValue: 3,
                        duration: 50,
                        useNativeDriver: false
                    }),
                    // bring the element back to its original position
                    Animated.timing(shakeAnimation, {
                        toValue: 0,
                        duration: 50,
                        useNativeDriver: false
                    })
                ]),
                // loops the above animation config 2 times
                { iterations: 3 }
            ).start();
        }, []);
        const highlight = useCallback(() => {
            animationColor();
            shake();
        }, []);

        const handleBlurCounter = () => {
            if (isCrossSellingProduct) {
                if (temQuantity < maxCount) {
                    onChangeQuantity(temQuantity, ProductID);
                    const num =
                        temQuantity -
                        (temQuantity % crossSelling.originQuantity);
                    const newTemQuantity = num > maxCount ? maxCount : num;

                    setTemQuantity(newTemQuantity);
                    setUpdate((prev) => prev + 1);
                } else if (temQuantity >= maxCount) {
                    setTemQuantity(maxCount);
                    onChangeQuantity(maxCount, ProductID);
                    setUpdate((prev) => prev + 1);
                }
            } else if (AppliedQuantity !== temQuantity) {
                onChangeQuantity(temQuantity, ProductID);
            }
        };

        const inventoryStatusName = translate(
            INVENTORY_STATUS_NAME_KEY[InventoryStatusID]
        );

        const RGBA_GRAY_8_5 = helper.hexToRgbA(COLOR.GRAY_8, 0.5);

        return (
            <Animated.View
                key={remountKey}
                {...props}>
                <Swipeable
                    renderRightActions={(progress) =>
                        renderRightActions({
                            progress,
                            actions: rightActions
                        })
                    }
                    renderLeftActions={renderLeftActions(
                        onOpenQRScan,
                        COLOR,
                        disabledBatchNo,
                        hasBatch,
                        onGetProductLock,
                        isAva
                    )}
                    // onSwipeableLeftWillOpen={() => {
                    //     if (!disabledBatchNo && !hasBatch) {
                    //         onOpenQRScan();
                    //         refSwipe.current.close();
                    //     }
                    // }}
                    overshootRight={false}
                    overshootLeft={false}
                    overshootFriction={24}
                    onSwipeableWillOpen={() => {
                        shouldCloseSwipeable && setOpenedItemID();
                    }}
                    ref={refSwipe}>
                    <Animated.View
                        style={[
                            styles.productItemContainer,
                            // styles.shadowBox,
                            {
                                transform: [{ translateX: shakeAnimation }],
                                ...animatedStyle
                            }
                        ]}>
                        {(isOtherDeliveryType || isCrossSellingProduct) && (
                            <StoreChangeIndicator />
                        )}
                        <View style={styles.colContainer}>
                            <View
                                style={[
                                    styles.imageContainer,
                                    styles.shadowBox,
                                    {
                                        shadowColor: COLOR.primary,
                                    }
                                ]}>
                                <Image
                                    style={styles.image}
                                    source={source}
                                    resizeMode="contain"
                                />
                                {isShowStatus && (
                                    <View
                                        style={[
                                            styles.viewStatus,
                                            {
                                                backgroundColor:
                                                    isAllowedChangeStatus
                                                        ? COLOR.PRIMARY_RGBA_9
                                                        : RGBA_GRAY_8_5
                                            }
                                        ]}>
                                        <TouchableOpacity
                                            disabled={!isAllowedChangeStatus || !!IMEI}
                                            onPress={onOpenStatusSheet}>
                                            <Text
                                                style={{
                                                    color: COLOR.GRAY_1,
                                                    fontWeight: '500',
                                                    textAlign: 'center',
                                                    paddingHorizontal: 1
                                                }}
                                                adjustsFontSizeToFit>
                                                {inventoryStatusName}
                                            </Text>
                                        </TouchableOpacity>
                                    </View>
                                )}
                            </View>
                            <View style={styles.detailContainer}>
                                <TouchableOpacity onPress={onPressProductName}>
                                    <Text
                                        selectTextOnFocus={false}
                                        numberOfLines={IMEI ? 1 : 2}
                                        ellipsizeMode="tail"
                                        style={[
                                            styles.productName,
                                            { height: IMEI ? 18 : 35 }
                                        ]}>
                                        {ProductName.toUpperCase()}
                                    </Text>
                                </TouchableOpacity>
                                {!!IMEI && (
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            alignItems: 'center'
                                        }}>
                                        <Text
                                            style={{
                                                fontSize: 11,
                                                color: COLOR.primary,
                                                fontStyle: 'italic',
                                                fontWeight: 'bold'
                                            }}
                                            selectTextOnFocus={false}
                                            numberOfLines={1}
                                            ellipsizeMode="tail">
                                            {`IMEI: ${encryptIMEI(IMEI)} (${InventoryStatusName})`}
                                        </Text>
                                    </View>
                                )}

                                <View style={styles.subDetailContainer}>
                                    <View>
                                        {shouldRenderPriceFetching ? (
                                            <PriceCrossSelling
                                                onGetCSPrice={onGetCSPrice}
                                                isPriceFetch={
                                                    crossSelling.isPriceFetch
                                                }
                                            />
                                        ) : (
                                            <View style={{ flex: 0.8 }}>
                                                {!isPriceFetching &&
                                                    isPriceHandled && (
                                                        <MyText
                                                            selectable={false}
                                                            style={
                                                                styles.totalPrice
                                                            }>
                                                            {totalFinalPrice > 0 && helper.convertNum(
                                                                totalFinalPrice
                                                            )}
                                                        </MyText>
                                                    )}
                                                {isPriceFetching && <Loading />}
                                            </View>
                                        )}
                                        {!!coefficient && (
                                            <TooltipWrapper
                                                text={"Hệ số quy đổi doanh thu"}
                                                isVisible={visibleTooltip}
                                                onClose={() => { setVisibleTooltip(false) }}
                                                disabled
                                                wrapper={
                                                    <TouchableOpacity onPress={() => { setVisibleTooltip(true) }}
                                                        style={{
                                                            flexDirection: "row",
                                                            alignItems: "center",
                                                            paddingBottom: 5
                                                        }}>
                                                        <Image
                                                            style={{ height: 20, width: 25 }}
                                                            source={require("../../../../assets/exchange.png")}
                                                            resizeMode="contain"
                                                        />
                                                        <Text
                                                            style={{
                                                                color: COLOR.primary,
                                                                fontStyle: 'italic',
                                                                fontWeight: 'bold',
                                                                paddingLeft: 5
                                                            }}
                                                            selectTextOnFocus={false}
                                                            numberOfLines={1}
                                                            ellipsizeMode="tail">
                                                            {coefficient}
                                                        </Text>
                                                    </TouchableOpacity>
                                                }
                                            />

                                        )}
                                    </View>


                                    <View
                                        style={{
                                            alignItems: 'center',
                                            justifyContent: 'flex-end',
                                            flex: 1
                                        }}>
                                        <TouchableOpacity
                                            style={[
                                                styles.unitBtn,
                                                {
                                                    borderColor: colorUnit,
                                                    shadowColor: colorUnit
                                                }
                                            ]}
                                            disabled={disabledUnit}
                                            onPress={() => {
                                                if (!isPromotionFetching) {
                                                    onChangeUnit(ProductID);
                                                }
                                            }}>
                                            <MyText
                                                selectable={false}
                                                style={[
                                                    styles.unitName,
                                                    {
                                                        color: colorUnit
                                                    }
                                                ]}>
                                                {valueUnit}
                                            </MyText>
                                        </TouchableOpacity>
                                        {isOtherDeliveryType ? (
                                            <View style={{ height: 15 }} />
                                        ) : (
                                            <InStockQuantityContainer
                                                value={InstockQuantity}
                                                crossSellingQuantity={
                                                    isCrossSellingProduct
                                                        ? maxCount
                                                        : 0
                                                }
                                            />
                                        )}
                                    </View>

                                </View>


                            </View>
                            <View style={styles.counterContainer}>
                                {!disabledBatchNo ? (
                                    <TouchableOpacity
                                        style={[
                                            style.shadowBox,
                                            {
                                                flexDirection: 'row',
                                                marginBottom: 20,
                                                shadowColor: COLOR.primary
                                            }
                                        ]}
                                        onPress={onPressBatchNo}>
                                        <MyText
                                            style={{
                                                fontSize: 15,
                                                fontWeight: 'bold',
                                                color: COLOR.primary,
                                                marginRight: 4
                                            }}
                                            text={translate(
                                                saleExpress.lot_date
                                            )}
                                        />
                                        {!hasBatch && (
                                            <Icon
                                                iconSet="MaterialCommunityIcons"
                                                name="qrcode-scan"
                                                color={COLOR.lightBlack}
                                                size={15}
                                            />
                                        )}
                                    </TouchableOpacity>
                                ) : (
                                    <View
                                        style={{
                                            height: 15,
                                            marginBottom: 20
                                        }}
                                    />
                                )}
                                {/*
                                    TODO: Replace this component
                                    Workaround: create state `update` in order to remount Counter
                                */}
                                <Counter
                                    key={update}
                                    disabled={isDisableCounter}
                                    withoutRange
                                    quantity={AppliedQuantity}
                                    max={maxCount}
                                    min={minCount}
                                    onIncrease={() => {
                                        const newQuantity =
                                            AppliedQuantity + step;
                                        onChangeQuantity(
                                            newQuantity,
                                            ProductID
                                        );
                                        setTemQuantity(newQuantity);
                                    }}
                                    onDecrease={() => {
                                        const newQuantity =
                                            AppliedQuantity - step;
                                        onChangeQuantity(
                                            newQuantity,
                                            ProductID
                                        );
                                        setTemQuantity(newQuantity);
                                    }}
                                    height={30}
                                    width={constants.width / 4 - 10}
                                    onChangeQuantity={(quantity) => {
                                        setTemQuantity(quantity);
                                    }}
                                    onBlur={handleBlurCounter}
                                    nonAllowZero
                                />
                            </View>
                        </View>
                        {isPriceDiscounted && (
                            <View style={styles.saleIconBackground} />
                        )}
                        {isPriceDiscounted && (
                            <TouchableOpacity
                                style={styles.saleIcon}
                                onPress={() => {
                                    onShowModal();
                                    setDataModal(getListDiscountPrice());
                                }}>
                                <Icon
                                    iconSet="Foundation"
                                    name="burst-sale"
                                    color="red"
                                    size={35}
                                />
                            </TouchableOpacity>
                        )}
                        <View
                            style={[
                                styles.priceWrapper,
                                {
                                    width: 100,
                                    left: 0
                                }
                            ]}>
                            {
                                !!WarningMessage ? <MyText
                                    addSize={-4.5}
                                    style={[
                                        {
                                            fontWeight: '700',
                                            textAlign: 'center',
                                            color: COLOR.red
                                        }
                                    ]}
                                    text={WarningMessage}
                                    selectable={false}
                                />
                                    : (!isPriceFetching && isPriceHandled ? (
                                        <MyText
                                            style={{
                                                fontWeight: '700',
                                                textAlign: 'center'
                                            }}
                                            selectable={false}>
                                            {helper.convertNum(finalPrice)}{' '}
                                            {isPriceDiscounted && (
                                                <MyText style={styles.originalPrice}>
                                                    {helper.convertNum(SalePriceVAT)}
                                                </MyText>
                                            )}
                                        </MyText>
                                    ) : (
                                        <MyText
                                            style={[
                                                styles.smallText,
                                                {
                                                    fontWeight: '700',
                                                    textAlign: 'center',
                                                    color: COLOR.red
                                                }
                                            ]}
                                            text={cus_MessageWarning}
                                            selectable={false}
                                        />
                                    ))
                            }
                        </View>

                        {!!appliedCrossSellingById && (
                            <TouchableOpacity onPress={onNoticeProduct}>
                                <Info
                                    bold
                                    icon="sale"
                                    color={COLOR.PRIMARY_500}
                                    content="SP Bán kèm"
                                />
                            </TouchableOpacity>
                        )}
                        <Info
                            icon="map-marker-radius-outline"
                            color={COLOR.PRIMARY_500}
                            content={storeChangeInfoText}
                        />
                        <Info
                            icon="star-circle"
                            color={COLOR.yellow}
                            content={standardPointText}
                        />

                        {promotions.length > 0 && (
                            <Promotions
                                onPress={onChangePromotion}
                                data={promotions}
                                appliedQuantity={AppliedQuantity}
                            />
                        )}
                        {isPromotionFetching === PENDING && (
                            <LoadingPromotion />
                        )}
                        {isCrossSellFetching === PENDING && (
                            <LoadingPromotion iconName="sale" />
                        )}
                        {isCrossSellFetching === REJECTED && (
                            <RetryLoadingPromotion
                                onRetryGetPromotion={onRetryGetCrossSelling}
                                iconName="sale"
                                iconSet="MaterialCommunityIcons"
                            />
                        )}
                        {isPromotionFetching === REJECTED && (
                            <RetryLoadingPromotion
                                onRetryGetPromotion={onRetryGetPromotion}
                                iconName="gift"
                                iconSet="MaterialCommunityIcons"
                            />
                        )}
                    </Animated.View>
                </Swipeable>
            </Animated.View>
        );
    }
);

const LoadingPromotion = ({ color = '#315FDA', iconName = 'gift' }) => (
    <View style={styles.giftContainer}>
        <Icon
            iconSet="MaterialCommunityIcons"
            name={iconName}
            color={color}
            size={18}
            style={styles.giftIcon}
        />
        <Loading />
    </View>
);

const RetryLoadingPromotion = ({ onRetryGetPromotion, iconName, iconSet }) => {
    const theme = useTheme();
    const COLOR = theme.colors;
    return (
        <View style={[styles.giftContainer, { alignItems: 'center' }]}>
            <Icon iconSet={iconSet} name={iconName} color="#315FDA" size={18} />
            <TouchableOpacity
                onPress={onRetryGetPromotion}
                style={{
                    flexDirection: 'row',
                    marginLeft: 8,
                    alignItems: 'center'
                }}>
                <Text
                    style={{
                        color: COLOR.red,
                        marginRight: 8,
                        fontWeight: '700'
                    }}>
                    {translate(common.btn_notify_try_again)}
                </Text>
                <Icon
                    iconSet="MaterialCommunityIcons"
                    name="reload"
                    color={COLOR.red}
                    size={14}
                />
            </TouchableOpacity>
        </View>
    );
};

const Promotions = ({ data, onPress, appliedQuantity }) => {
    const theme = useTheme();
    const COLOR = theme.colors;
    return (
        <View style={styles.giftContainer}>
            <Icon
                iconSet="MaterialCommunityIcons"
                name="gift"
                color={COLOR.blue}
                size={18}
                style={styles.giftIcon}
            />
            <View style={{ flex: 1, marginTop: 2 }}>
                {data.map((promo) => {
                    const {
                        ProductName,
                        Quantity,
                        QuantityUnit,
                        ProductID,
                        InstockQuantity,
                        groupId
                    } = promo;
                    const totalQuantity = Quantity * appliedQuantity;
                    // TODO: translate
                    const name = `Tặng: ${ProductName} (${totalQuantity} ${QuantityUnit} - Tồn: ${InstockQuantity})`;
                    const key = `${ProductID}_${groupId} `;
                    return (
                        <TouchableOpacity
                            onPress={() => onPress(groupId)}
                            key={key}>
                            <View
                                style={{
                                    marginBottom: 2,
                                    flex: 1
                                }}>
                                <Text style={styles.smallText}>{name}</Text>
                            </View>
                        </TouchableOpacity>
                    );
                })}
            </View>
        </View>
    );
};

const Loading = () => (
    <View>
        <DotIndicator size={8} color="#315FDA" />
    </View>
);

const RightAction = ({ text, color, x, progress, onPress, icon }) => {
    const theme = useTheme();
    const COLOR = theme.colors;
    const trans = progress.interpolate({
        inputRange: [0, 1],
        outputRange: [x, 0]
    });
    // const opacity = progress.interpolate({
    //     inputRange: [0, 0.25, 0.5, 0.75, 1],
    //     outputRange: [0, 0.25, 0.5, 0.5, 1]
    // });

    return (
        <Animated.View
            style={{
                flex: 1,
                transform: [{ translateX: trans }]
            }}>
            <TouchableOpacity
                style={{
                    alignItems: 'center',
                    flex: 1,
                    justifyContent: 'center',
                    backgroundColor: color
                }}
                onPress={onPress}>
                <View
                    style={{
                        flex: 0.5,
                        justifyContent: 'flex-end',
                        marginBottom: 4
                    }}>
                    <Icon
                        iconSet="MaterialCommunityIcons"
                        name={icon}
                        color={COLOR.GRAY_2}
                        size={25}
                    />
                </View>
                <MyText
                    style={{
                        color: COLOR.GRAY_2,
                        paddingHorizontal: 4,
                        textAlign: 'center',
                        flex: 0.5
                    }}
                    text={text}
                />
            </TouchableOpacity>
        </Animated.View>
    );
};

export const Info = ({ content, icon, color, bold }) => {
    if (!content) {
        return null;
    }
    return (
        <View
            style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginVertical: 5
            }}>
            <Icon
                iconSet="MaterialCommunityIcons"
                name={icon}
                color={color}
                size={18}
                style={styles.giftIcon}
            />
            <Text
                style={[
                    styles.smallText,
                    { fontWeight: bold ? '700' : '400' }
                ]}>
                {content}
            </Text>
        </View>
    );
};

const StoreChangeIndicator = () => {
    const theme = useTheme();
    const COLOR = theme.colors;
    return (
        <View
            style={{
                height: '100%',
                backgroundColor: COLOR.WARNING_500,
                width: 4,
                position: 'absolute',
                top: 0,
                left: 0
            }}
        />
    );
};

const PriceCrossSelling = ({ onGetCSPrice, isPriceFetch }) => {
    return (
        <View style={{ flex: 0.8 }}>
            {isPriceFetch === PENDING && <Loading />}
            {isPriceFetch === REJECTED && (
                <RetryLoadingPromotion
                    onRetryGetPromotion={onGetCSPrice}
                    iconName=""
                    iconSet=""
                />
            )}
        </View>
    );
};

const InStockQuantityContainer = ({ value, crossSellingQuantity }) => {
    const COLOR = useTheme().colors;
    const [layoutHeight, setHeight] = useState(0);
    const [layoutWidth, setWidth] = useState(0);
    return (
        <View>
            <MyText
                onLayout={(e) => {
                    const { width, height } = e.nativeEvent.layout;

                    setHeight(height);
                    setWidth(width + 20);
                }}
                style={{
                    fontWeight: '500',
                    color: COLOR.primary,
                    marginTop: 2
                }}>
                {value}
            </MyText>
            {/* Số lượng bán kèm tối đa */}
            {crossSellingQuantity ? (
                <View
                    style={{
                        position: 'absolute',
                        top: layoutHeight,
                        left: -10,
                        width: layoutWidth
                    }}>
                    <MyText
                        style={{
                            fontWeight: '500',
                            textAlign: 'center',
                            color: COLOR.GRAY_9
                        }}>
                        ({crossSellingQuantity})
                    </MyText>
                </View>
            ) : null}
        </View>
    );
};

const styles = StyleSheet.create({
    colContainer: {
        alignItems: 'center',
        flexDirection: 'row',
        zIndex: -100,
    },
    counterContainer: {
        alignItems: 'center',
        alignSelf: 'flex-start'
    },
    detailContainer: {
        flex: 1,
    },
    giftContainer: {
        flexDirection: 'row',
        marginTop: 5
    },
    giftIcon: {
        alignSelf: 'flex-start',
        marginRight: 8
    },
    image: {
        height: 55,
        width: 65
    },
    imageContainer: {
        alignSelf: 'flex-start',
        backgroundColor: COLORS.bgFFFFFF,
        borderRadius: 4,
        marginRight: 10,
        paddingHorizontal: 6,
        paddingVertical: 2
    },
    originalPrice: {
        color: COLORS.bdB9B9B9,
        fontSize: 10,
        fontWeight: 'bold',
        textDecorationLine: 'line-through'
    },
    priceWrapper: {
        marginTop: 5,
        position: 'absolute',
        top: 65
    },
    productItemContainer: {
        paddingHorizontal: 10,
        paddingVertical: 8
    },
    productName: {
        color: COLORS.bg000000,
        fontWeight: 'bold',
        height: 35
    },
    saleIcon: {
        left: 63,
        position: 'absolute',
        top: 0
    },
    saleIconBackground: {
        backgroundColor: COLORS.bgFFFFFF,
        borderRadius: 10,
        height: 20,
        left: 67,
        position: 'absolute',
        top: 5,
        width: 20
    },
    shadowBox: {
        elevation: 5,
        shadowOffset: {
            width: 0,
            height: 2
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84
    },
    smallText: {
        fontSize: 12
    },
    subDetailContainer: {
        flexDirection: 'row',
        marginTop: 4,
    },
    swipeBtn: {
        alignItems: 'center',
        justifyContent: 'center'
    },
    swipeBtnContainer: {
        alignItems: 'center',
        backgroundColor: COLORS.bgDB0606A3,
        flex: 0.2,
        justifyContent: 'center'
    },
    totalPrice: {
        fontWeight: 'bold'
    },
    unitBtn: {
        alignItems: 'center',
        backgroundColor: COLORS.bgFFFFFF,
        borderRadius: 15,
        borderWidth: 1,
        elevation: 10,
        justifyContent: 'center',
        paddingHorizontal: 10,
        paddingVertical: 5,
        shadowOffset: { width: -2, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 3
    },
    unitName: {
        fontWeight: 'bold'
    },
    viewStatus: {
        borderBottomLeftRadius: 4,
        borderBottomRightRadius: 4,
        bottom: 0,
        height: 16,
        left: 0,
        position: 'absolute',
        width: 65 + 12
    }
});

export default ProductItem;

const statusVicinity = new Set(`${NEW}, ${CLOSER_TO_DATE}`)


const encryptIMEI = (imei) => {
    if (imei.length < 15) return imei
    imei = imei.replace(/[-,\s]/g, '');
    imei = `*****${imei.substring(imei.length - 10, imei.length)}`;
    return imei
};