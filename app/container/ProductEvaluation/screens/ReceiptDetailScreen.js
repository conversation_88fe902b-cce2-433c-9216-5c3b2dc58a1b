import { ScrollView, View, Modal, TouchableOpacity } from 'react-native';
import React, { useState } from 'react';
import { MyText, ImageURI, Icon } from '@components';
import { COLORS } from '@styles';
import { helper } from '@common';
import ImageViewer from 'react-native-image-zoom-viewer';
import { constants } from '@constants';
import { Title, Section, ReturnProductsSection } from '../components';
import { ImageCDN } from '../../../components';

const ReceiptDetailScreen = ({ route }) => {
    const { data } = route.params;
    const {
        ExchangeProgramName,
        CustomerName,
        CustomerPhone,
        CustomerIDCard,
        CustomerAddress,
        cus_ExchangeReceiptDetailBOList,
        MaxQuantityOfApplyProduct,
        ReviewedUserFullname,
        ReviewedUser,
        cus_ExchangeReceiptAttachmentBOList
    } = data;

    const totalMoney = cus_ExchangeReceiptDetailBOList.reduce(
        (accumulator, currentValue) => accumulator + currentValue.DiscountValue,
        0
    );

    const toUrlFile = ({ FilePath, Description }) => ({
        url: FilePath,
        description: Description
    });

    const urlFiles = cus_ExchangeReceiptAttachmentBOList
        ? cus_ExchangeReceiptAttachmentBOList.map(toUrlFile)
        : [];

    const { ExchangeProductID, ExchangeProductName } =
        cus_ExchangeReceiptDetailBOList[0];

    const [isShowImageViewer, setIsShowImageViewer] = useState(false);
    const [imgIndex, setImgIndex] = useState(0);

    return (
        <ScrollView
            contentContainerStyle={{
                flexGrow: 1,
                backgroundColor: COLORS.bgFFFFFF
            }}>
            <Title name={ExchangeProgramName} />
            <Section title="Thông tin khách hàng">
                <TextField name="Họ và tên" value={CustomerName} />
                <TextField name="Số điện thoại" value={CustomerPhone} />
                <TextField name="CMND" value={CustomerIDCard} />
                <TextField name="Địa chỉ" value={CustomerAddress} />
            </Section>
            <Section title="Sản phẩm đổi sang">
                <MyText
                    style={{ fontWeight: 'bold' }}
                    text={`${ExchangeProductID} - ${ExchangeProductName}`}
                    addSize={1}
                />
            </Section>
            <Section title="Danh sách sản phẩm thu cũ">
                <ReturnProductsSection
                    data={cus_ExchangeReceiptDetailBOList}
                    maxQuantity={MaxQuantityOfApplyProduct}
                    totalMoney={totalMoney}
                />
            </Section>
            <Section title="Nhân viên duyệt">
                <MyText
                    style={{ fontWeight: 'bold' }}
                    text={`${ReviewedUser} - ${ReviewedUserFullname}`}
                />
            </Section>
            <Section title="Hình ảnh">
                {helper.IsNonEmptyArray(urlFiles) && (
                    <View style={{ flexDirection: 'row' }}>
                        {urlFiles.map(({ url, description }, index) => (
                            <View style={{ alignItems: 'center', flex: 1 }}>
                                <TouchableOpacity
                                    onPress={() => {
                                        setIsShowImageViewer(true);
                                        setImgIndex(index);
                                    }}>
                                    <ImageCDN
                                        uri={url}
                                        style={{
                                            width: 60,
                                            height: 60
                                        }}
                                    />
                                </TouchableOpacity>
                                <MyText text={description} />
                            </View>
                        ))}
                    </View>
                )}
            </Section>
            <Modal visible={isShowImageViewer} transparent>
                <ImageViewer
                    renderHeader={() => (
                        <TouchableOpacity
                            style={{
                                position: 'absolute',
                                right: 5,
                                top: constants.heightTopSafe + 5,
                                zIndex: 100
                            }}
                            onPress={() => setIsShowImageViewer(false)}>
                            <Icon
                                iconSet="Ionicons"
                                name="close"
                                color={COLORS.bgFFFFFF}
                                size={40}
                            />
                        </TouchableOpacity>
                    )}
                    imageUrls={urlFiles}
                    index={imgIndex}
                    enableSwipeDown
                    onCancel={() => setIsShowImageViewer(false)}
                />
            </Modal>
        </ScrollView>
    );
};

const TextField = ({ name, value }) => {
    return (
        <MyText
            text={`${name}: `}
            style={{
                color: COLORS.txt8E8E93,
                marginBottom: 5
            }}>
            <MyText
                text={value}
                style={{
                    color: COLORS.txt333333
                }}
            />
        </MyText>
    );
};

export default ReceiptDetailScreen;
