import {
    View,
    Image,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    SafeAreaView,
    Alert,
    Keyboard
} from 'react-native';
import React, { useState } from 'react';
import { COLORS } from '@styles';
import { MyText } from '@components';
import { helper } from '@common';
import { useDispatch, useSelector } from 'react-redux';
import { COLOR } from '../constants';
import { deleteReturnProductByIMEISet } from '../actions';
import { IconField } from '../components/ExchangeReceiptItem';
import { ExchangeProductModal, InputSearch } from '../components';

const Item = ({ data, onSelect, onViewDetail }) => {
    const { ExchangeProductName, ExchangeProductImage, TotalDiscountValue, InstockQuantity, QuantityUnitName, IsDisabled } =
        data;

    const [isError, setIsError] = useState(false);

    const imgSource =
        isError || !ExchangeProductImage
            ? require('../../../../assets/smartphone.png')
            : { uri: ExchangeProductImage };
    return (
        <View
            style={{
                backgroundColor: COLORS.bgFFFFFF,
                padding: 10,
                margin: 5,
                borderRadius: 5,
                elevation: 5,
                shadowColor: COLORS.bg000000,
                shadowOffset: {
                    width: 0,
                    height: 2
                },
                shadowOpacity: 0.25,
                shadowRadius: 3.84
            }}>
            <View style={{ flexDirection: 'row', marginBottom: 5 }}>
                <View
                    style={{
                        borderWidth: StyleSheet.hairlineWidth,
                        borderColor: COLORS.bg8E8E93,
                        borderRadius: 5,
                        padding: 10,
                        alignItems: 'center',
                        justifyContent: 'center'
                    }}>
                    <Image
                        style={{
                            height: 70,
                            width: 70
                        }}
                        resizeMode="contain"
                        source={imgSource}
                        onError={() => setIsError(true)}
                    />
                </View>
                <View
                    style={{
                        flex: 1,
                        marginLeft: 10
                    }}>
                    <MyText
                        text={ExchangeProductName}
                        style={{
                            color: COLOR.BLUE_2,
                            fontWeight: 'bold'
                        }}
                    />

                    <View>
                        <MyText text="Tổng tiền hỗ trợ và xác máy" />
                        <MyText
                            text={helper.convertNum(TotalDiscountValue)}
                            addSize={4}
                            style={{
                                color: COLOR.RED_4,
                                fontWeight: 'bold'
                            }}
                        />
                    </View>
                    <MyText
                        text={`SL tồn:  `}
                        style={{
                            color: COLORS.txt999999
                        }}
                    >
                        <MyText
                            text={`${InstockQuantity} ${QuantityUnitName}`}
                            style={{
                                color: COLORS.txt008000,
                                fontWeight: 'bold'
                            }}
                        />
                    </MyText>
                </View>
            </View>
            <View
                style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between'
                }}>
                <TouchableOpacity onPress={onViewDetail}>
                    <IconField
                        title="Xem chi tiết"
                        iconSet="Feather"
                        name="external-link"
                        color={COLORS.ic5B8767}
                    />
                </TouchableOpacity>
                <TouchableOpacity disabled={IsDisabled} onPress={onSelect} style={{ opacity: IsDisabled ? 0.6 : 1 }}>
                    <IconField
                        title="Chọn"
                        iconSet="MaterialCommunityIcons"
                        name="cart-plus"
                        color={COLOR.ORANGE_1}

                    />
                </TouchableOpacity>

            </View>
        </View>
    );
};

const ExchangeProductScreen = ({ route, navigation }) => {
    const { data } = route.params;
    const dispatch = useDispatch();
    const { returnProducts, exchangeProgram } = useSelector(
        (state) => state.productEvaluationReducer
    );

    const [isVisibleModal, setIsVisibleModal] = useState(false);
    const [modalData, setModalData] = useState({});
    const [keyword, setKeyword] = useState('');

    const renderData = helper.IsEmptyString(keyword) ? data : data.filter(product => helper.removeAccent(product.ExchangeProductName).toUpperCase().includes(helper.removeAccent(keyword).toUpperCase()))

    const handleViewDetail = (item) => () => {
        setModalData(item);
        setIsVisibleModal(true);
    };
    const handleSelectItem = (item) => () => {
        Keyboard.dismiss();
        const { cus_ExchangeReceiptDetailBOList, ExchangeProductName } = item;
        if (returnProducts.length !== cus_ExchangeReceiptDetailBOList.length) {
            const IMEISet = new Set();
            cus_ExchangeReceiptDetailBOList.forEach(({ ReturnIMEI }) =>
                IMEISet.add(ReturnIMEI)
            );
            const deletedIMEI = new Set();
            let warningProducts = `Thao tác này sẽ xoá khỏi danh sách sản phẩm thu cũ những sản phẩm sau (do không được khai báo ở chương trình với sản phẩm đổi sang ${ExchangeProductName} đang chọn):\n`;
            returnProducts.forEach(({ IMEI, productName }) => {
                if (!IMEISet.has(IMEI)) {
                    deletedIMEI.add(IMEI);
                    warningProducts += `- ${productName} (IMEI/SN ${IMEI})\n`;
                }
            });
            warningProducts += 'Bạn có muốn tiếp tục không?';
            Alert.alert('Thông báo', warningProducts, [
                {
                    text: 'BỎ QUA'
                },
                {
                    text: 'TIẾP TỤC',
                    onPress: () => {
                        dispatch(deleteReturnProductByIMEISet(deletedIMEI));
                        addToCart(item, false);
                    }
                }
            ]);
        } else {
            addToCart(item, true);
        }
    };
    const addToCart = (item, shouldGoBack) => {
        const { cus_ExchangeReceiptDetailBOList, ...exchangeProduct } = item;
        const {
            ExchangeProgramID,
            ExchangeProgramName,
            MaxQuantityOfApplyProduct,
            ExchangeProgramType
        } = exchangeProgram;

        const stateMap = new Map();
        returnProducts.forEach(({ IMEI, states }) =>
            stateMap.set(
                IMEI,
                states.filter((state) => state.isChecked)
            )
        );
        cus_ExchangeReceiptDetailBOList.forEach((detail) => {
            detail.cus_ExchangeReceiptDetailStateBOList = stateMap.get(
                detail.ReturnIMEI
            );
            detail.ReturnQuantity = 1;
        });

        const exchangeReceiptBO = {
            ExchangeProgramID,
            ExchangeProgramName,
            MaxQuantityOfApplyProduct,
            cus_ExchangeReceiptDetailBOList,
            ExchangeProgramType
        };
        setKeyword('');
        navigation.navigate('CartScreen', {
            exchangeReceiptBO,
            exchangeProduct,
            shouldGoBack
        });
    };

    return (
        <SafeAreaView style={{ flex: 1 }}>
            <InputSearch
                value={keyword}
                onChangeText={setKeyword}
                clearText={() => {
                    setKeyword('')
                }}
                isClose={true}
            />
            <ScrollView keyboardShouldPersistTaps='always'>
                {renderData.map((item) => (
                    <Item
                        data={item}
                        onSelect={handleSelectItem(item)}
                        onViewDetail={handleViewDetail(item)}
                    />
                ))}
            </ScrollView>
            {isVisibleModal && (
                <ExchangeProductModal
                    data={modalData}
                    isShowModal={isVisibleModal}
                    handleOnClose={() => {
                        setIsVisibleModal(false);
                    }}
                />
            )}
        </SafeAreaView>
    );
};

export default ExchangeProductScreen;
