import {
    <PERSON>,
    <PERSON><PERSON><PERSON>iew,
    Safe<PERSON>rea<PERSON><PERSON>w,
    Alert,
    TouchableOpacity
} from 'react-native';
import React, { useState } from 'react';
import {
    MyText,
    Button,
    BarcodeCamera,
    showBlockUI,
    hideBlockUI,
    Icon
} from '@components';
import { COLORS } from '@styles';
import { constants } from '@constants';
import { useDispatch, useSelector } from 'react-redux';
import { helper } from '@common';
import Toast from 'react-native-toast-message';
import { translate } from '@translate';
import {
    Title,
    SearchBar,
    ProductStateModal,
    ProductDetail
} from '../components';
import { COLOR } from '../constants';
import {
    deleteReturnProductByIMEISet,
    getReturnProductStates,
    getReturnProductInfoByIMEI,
    getExchangeProductInfo,
    addReturnProduct
} from '../actions';
import { IconField } from '../components/ExchangeReceiptItem';
import { useEffect } from 'react';
import { hasValidPartnerEco } from '../helper';

const ProductItem = ({ data, onDelete, onEditState }) => {
    const { dataFromPartner } = useSelector(
        (state) => state.productEvaluationReducer
    );
    const selectedItem = dataFromPartner?.data?.find((item) => item.IsSelected);

    const {
        IMEI,
        productImage,
        productName,
        saleOrderID,
        customerName,
        customerPhone,
        states
    } = data;

    return (
        <View
            style={{
                elevation: 5,
                borderRadius: 5,
                padding: 5,
                margin: 5,
                backgroundColor: COLORS.bgFFFFFF,
                shadowColor: COLORS.bg000000,
                shadowOffset: {
                    width: 0,
                    height: 2
                },
                shadowOpacity: 0.25,
                shadowRadius: 3.84
            }}>
            <ProductDetail
                imageURL={productImage}
                name={productName}
                saleOrderID={saleOrderID}
                customerName={customerName}
                customerPhone={customerPhone}
                IMEI={IMEI}
            />

            <TouchableOpacity
                style={{
                    position: 'absolute',
                    top: 0,
                    right: 0,
                    paddingRight: 10,
                    backgroundColor: COLORS.btnDCE1E1,
                    paddingBottom: 1,
                    paddingLeft: 5,
                    borderBottomLeftRadius: 5,
                    zIndex: 100
                }}
                onPress={onDelete}>
                <IconField
                    title="Xoá"
                    iconSet="Ionicons"
                    name="trash"
                    color={COLORS.icFF0000}
                />
            </TouchableOpacity>
            {!helper.IsEmptyObject(selectedItem) ? (
                <View
                    style={{
                        flexDirection: 'row',
                        marginBottom: 2,
                        alignItems: 'center'
                    }}>
                    <MyText
                        style={{ fontWeight: 'bold' }}
                        text="Tình trạng: "
                    />
                    <MyText
                        style={{
                            fontWeight: 'bold'
                        }}
                        text={selectedItem.PartnerReceiptBO?.StateGroupID}>
                        <MyText
                            text={` - ${selectedItem.PartnerReceiptBO?.StateGroupName}`}
                        />
                    </MyText>
                </View>
            ) : (
                <>
                    <View
                        style={{
                            flexDirection: 'row',
                            marginBottom: 2,
                            alignItems: 'center'
                        }}>
                        <MyText
                            style={{ fontWeight: 'bold' }}
                            text="Tình trạng: "
                        />
                        <TouchableOpacity
                            style={{ padding: 5 }}
                            onPress={onEditState}>
                            <Icon
                                iconSet="FontAwesome"
                                name="edit"
                                color={COLOR.ORANGE_1}
                                size={16}
                            />
                        </TouchableOpacity>
                    </View>
                    {states
                        .filter(({ isChecked }) => isChecked)
                        .map(
                            ({
                                ExchangeProductStateID: id,
                                ExchangeProductStateName: stateName
                            }) => (
                                <MyText
                                    style={{
                                        marginBottom: 5,
                                        fontWeight: 'bold'
                                    }}
                                    text={id}>
                                    <MyText text={` - ${stateName}`} />
                                </MyText>
                            )
                        )}
                </>
            )}
        </View>
    );
};

const ReturnProductScreen = ({ navigation }) => {
    const dispatch = useDispatch();
    const { exchangeProgram, returnProducts } = useSelector(
        (state) => state.productEvaluationReducer
    );
    const {
        ExchangeProgramID,
        ExchangeProgramType,
        ExchangeProgramName,
        ExchangeProductType,
        MaxQuantityOfApplyProduct,
        FromOutputDate,
        ToOutputDate
    } = exchangeProgram;

    const {
        storeID: loginStoreId,
        moduleID,
        languageID
    } = useSelector((state) => state.userReducer);

    const { dataFromPartner } = useSelector(
        (state) => state.productEvaluationReducer
    );

    const [IMEI, setIMEI] = useState('');
    const [productInfo, setProductInfo] = useState({});
    const [productStates, setProductStates] = useState([]);
    const [isEditing, setIsEditing] = useState(false);

    const [isVisibleBarcodeScanner, setIsVisibleBarcodeScanner] =
        useState(false);

    const [isVisibleModal, setIsVisibleModal] = useState(false);

    useEffect(() => {
        if (hasValidPartnerEco(dataFromPartner)) {
            const partnerData = dataFromPartner.data;
            const selectedItem = partnerData.find((item) => item.IsSelected);
            const imei = selectedItem?.PartnerReceiptBO?.ReturningProductIMEI;
            if (imei) {
                setIMEI(imei);
                validateIMEI(imei);
            }
        }
    }, [dataFromPartner]);

    const validateIMEI = (text) => {
        if (helper.IsEmptyString(text)) {
            Toast.show({
                type: 'error',
                text1: 'Vui lòng nhập IMEI/SN'
            });
        } else if (returnProducts.length === MaxQuantityOfApplyProduct) {
            Toast.show({
                type: 'error',
                text1: 'Số lượng máy thu cũ vượt quá số lượng sản phẩm cho phép. Bạn không thể tiếp tục thu cũ sản phẩm.'
            });
        } else {
            const index = returnProducts.findIndex(
                (item) => item.IMEI.trim() === text.trim()
            );
            if (index !== -1) {
                Toast.show({
                    type: 'error',
                    text1: 'IMEI/SN này đã có trong danh sách sản phẩm thu cũ, vui lòng nhập IMEI/SN khác'
                });
            } else {
                handleOnSearch(text);
            }
        }
    };

    const handleOnSearch = async (text) => {
        if (hasValidPartnerEco(dataFromPartner)) {
            setIMEI('');
            navigation.navigate('SearchReturnProductScreen', {
                ExchangeProgramID,
                ExchangeProgramType,
                IMEI: text
            });
        }
        showBlockUI();
        try {
            const prodInfo = await getReturnProductInfoByIMEI({
                loginStoreId,
                languageID,
                moduleID,
                exchangeProgramID: ExchangeProgramID,
                exchangeProductType: ExchangeProductType,
                exchangeProgramType: ExchangeProgramType,
                fromOutputDate: FromOutputDate,
                toOutputDate: ToOutputDate,
                imei: text
            });
            console.log('prodInfo', prodInfo);
            if (prodInfo !== null) {
                if (hasValidPartnerEco(dataFromPartner)) {
                    const partnerData = dataFromPartner.data;
                    const selectedItem = partnerData.find(
                        (item) => item.IsSelected
                    );
                    const stateGroupID =
                        selectedItem?.PartnerReceiptBO?.StateGroupID;
                    dispatch(
                        addReturnProduct({
                            IMEI,
                            productID: prodInfo.cus_ReturnProductID,
                            productImage: prodInfo.cus_ReturnProductImage,
                            productName: prodInfo.cus_ReturnProductName,
                            saleOrderID: prodInfo.SaleOrderID,
                            saleOrderDetailID: prodInfo.SaleOrderDetailID,
                            customerName: prodInfo.CustomerName,
                            customerPhone: prodInfo.CustomerPhone,
                            states: [],
                            StateGroupID: stateGroupID
                        })
                    );
                } else {
                    const prodStates = await getReturnProductStates({
                        loginStoreId,
                        languageID,
                        moduleID,
                        exchangeProgramID: ExchangeProgramID,
                        exchangeProgramType: ExchangeProgramType,
                        returnProductID: prodInfo.cus_ReturnProductID,
                        imeiReturnProduct: text
                    });
                    // TODO: open modal
                    setIsEditing(false);
                    setProductStates(prodStates);
                    setProductInfo({
                        productName: prodInfo.cus_ReturnProductName,
                        productImage: prodInfo.cus_ReturnProductImage,
                        productID: prodInfo.cus_ReturnProductID,
                        saleOrderID: prodInfo.SaleOrderID,
                        saleOrderDetailID: prodInfo.SaleOrderDetailID,
                        customerName: prodInfo.CustomerName,
                        customerPhone: prodInfo.CustomerPhone
                    });
                    setIsVisibleModal(true);
                }
            } else {
                // TODO: navigate to ProductSelection Screen
                setIMEI('');
                navigation.navigate('SearchReturnProductScreen', {
                    ExchangeProgramID,
                    ExchangeProgramType,
                    IMEI: text
                });
            }
            hideBlockUI();
        } catch (error) {
            hideBlockUI();
            Alert.alert(translate('common.notification_uppercase'), error, [
                {
                    text: 'OK',
                    style: 'default',
                    onPress: () => {
                        setProductInfo({});
                        setProductStates([]);
                    }
                }
            ]);
        }
    };
    const handleOnContinue = () => {
        if (returnProducts.length === 0) {
            Toast.show({
                type: 'error',
                text1: 'Vui lòng thêm sản phẩm thu cũ trước khi tiếp tục'
            });
        } else {
            showBlockUI();
            const ReturnProductList = returnProducts.map(
                ({
                    productID,
                    productName,
                    productImage,
                    IMEI: ReturnIMEI,
                    StateGroupID,
                    saleOrderID,
                    saleOrderDetailID
                }) => ({
                    ExchangeProgramID,
                    ReturnProductID: productID,
                    ReturnProductName: productName,
                    ReturnProductImage: productImage,
                    ReturnIMEI,
                    StateGroupID,
                    SaleOrderID: saleOrderID,
                    SaleOrderDetailID: saleOrderDetailID
                })
            );
            getExchangeProductInfo({
                moduleID,
                languageID,
                loginStoreId,
                exchangeProgramType: ExchangeProgramType,
                returnProductList: ReturnProductList
            })
                .then((response) => {
                    hideBlockUI();
                    navigation.navigate('ExchangeProductScreen', {
                        data: response
                    });
                })
                .catch((error) => {
                    hideBlockUI();
                    Alert.alert(
                        translate('common.notification_uppercase'),
                        error,
                        [
                            {
                                text: 'OK',
                                style: 'default'
                            }
                        ]
                    );
                });
        }
    };

    const handleOnAddToCart = () => {
        const partnerData = dataFromPartner?.data;
        const {
            ExchangeProgramID,
            ExchangeProgramName,
            MaxQuantityOfApplyProduct,
            ExchangeProgramType
        } = exchangeProgram;

        const selectedItem = partnerData?.find(item => item.IsSelected);

        const {
            PartnerReceiptBO: receipt,
            PartnerReceiptBOs = []
        } = selectedItem || {};

        const productReturn =
            PartnerReceiptBOs.find(
                _item => _item.ReturningProductID === returnProducts?.[0]?.productID
            ) || receipt;



        if (!productReturn) return;

        const detail = {
            ...productReturn,
            ReturnProductID: productReturn.ReturningProductID,
            ReturnProductName: productReturn.ReturningProductName,
            ReturnIMEI: productReturn.ReturningProductIMEI,
            ReturnQuantity: 1,
            cus_ExchangeReceiptDetailStateBOList: [],
        };

        const exchangeReceiptBO = {
            ExchangeProgramID,
            ExchangeProgramName,
            MaxQuantityOfApplyProduct,
            cus_ExchangeReceiptDetailBOList: [detail],
            ExchangeProgramType

        };

        navigation.navigate('CartScreen', {
            exchangeReceiptBO,
            exchangeProduct: {
                TotalDiscountValue: productReturn.DiscountValue,
            },
        });
    };

    const handleWorkFlow = () => {
        const isEcoWithPartner = hasValidPartnerEco(dataFromPartner) && ExchangeProgramType === 3;
        isEcoWithPartner ? handleOnAddToCart() : handleOnContinue();
    };


    const handleEditState = (item) => () => {
        const { states, ...info } = item;
        setIMEI(info.IMEI);
        setProductInfo(info);
        setProductStates(states);
        setIsEditing(true);
        setIsVisibleModal(true);
    };
    return (
        <SafeAreaView style={{ flex: 1 }}>
            <Title name={ExchangeProgramName} />
            <View
                style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    paddingHorizontal: 10,
                    alignItems: 'center'
                }}>
                <SearchBar
                    hasBarcodeScanner
                    onOpenBarcodeScanner={() =>
                        setIsVisibleBarcodeScanner(true)
                    }
                    placeholder="Nhập IMEI/SN máy cũ"
                    value={IMEI}
                    onChangeText={(text) => {
                        setIMEI(text);
                    }}
                    onSearch={() => validateIMEI(IMEI)}
                    onDelete={() => {
                        setIMEI('');
                    }}
                />
            </View>
            <View
                style={{
                    backgroundColor: COLOR.GREEN_3,
                    padding: 10
                }}>
                <MyText
                    style={{ color: COLORS.bgFFFFFF, fontWeight: 'bold' }}
                    text={`Danh sách sản phẩm thu cũ (${returnProducts.length}/${MaxQuantityOfApplyProduct})`}
                />
            </View>
            <ScrollView
                contentContainerStyle={{
                    flexGrow: 1
                }}>
                {returnProducts.map((item) => (
                    <ProductItem
                        data={item}
                        onDelete={() => {
                            dispatch(
                                deleteReturnProductByIMEISet(
                                    new Set().add(item.IMEI)
                                )
                            );
                        }}
                        onEditState={handleEditState(item)}
                    />
                ))}
            </ScrollView>
            {isVisibleModal && (
                <ProductStateModal
                    isEditing={isEditing}
                    isShowModal={isVisibleModal}
                    handleOnClose={() => {
                        setIsVisibleModal(false);
                        setIMEI('');
                    }}
                    productStates={productStates}
                    productInfo={productInfo}
                    IMEI={IMEI}
                />
            )}
            <View
                style={{
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'flex-end'
                }}>
                <Button
                    text="BỎ QUA"
                    styleContainer={{
                        alignSelf: 'center',
                        backgroundColor: COLOR.GREEN_2,
                        borderRadius: constants.getSize(10),
                        height: constants.getSize(40),
                        marginVertical: 5,
                        width: constants.width / 2 - 30,
                        marginRight: 20
                    }}
                    styleText={{
                        color: COLORS.txtFFFFFF,
                        fontSize: 14,
                        fontWeight: 'bold'
                    }}
                    onPress={navigation.goBack}
                />
                <Button
                    text="TIẾP TỤC"
                    styleContainer={{
                        alignSelf: 'center',
                        backgroundColor: COLOR.ORANGE_2,
                        borderRadius: constants.getSize(10),
                        height: constants.getSize(40),
                        marginVertical: 5,
                        width: constants.width / 2 - 30
                    }}
                    styleText={{
                        color: COLORS.txtFFFFFF,
                        fontSize: 14,
                        fontWeight: 'bold'
                    }}
                    onPress={handleWorkFlow}
                />
            </View>
            {isVisibleBarcodeScanner && (
                <BarcodeCamera
                    isVisible={isVisibleBarcodeScanner}
                    closeCamera={() => {
                        setIsVisibleBarcodeScanner(false);
                    }}
                    resultScanBarcode={(text) => {
                        setIsVisibleBarcodeScanner(false);
                        setIMEI(text?.trim());
                        validateIMEI(text?.trim());
                    }}
                />
            )}
        </SafeAreaView>
    );
};

export default ReturnProductScreen;
