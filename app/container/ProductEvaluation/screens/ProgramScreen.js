import { View, SafeAreaView, FlatList } from 'react-native';
import React, { useState, useEffect } from 'react';
import { BaseLoading } from '@components';
import { useSelector } from 'react-redux';
import { SearchBar, ProgramItem } from '../components';
import { searchExchangeProgram } from '../actions';

const ProgramScreen = () => {
    const {
        storeID: loginStoreId,
        moduleID,
        languageID
    } = useSelector((state) => state.userReducer);

    const [keyword, setKeyword] = useState('');
    const [stateSearch, setStateSearch] = useState({
        isFetching: false,
        isError: false,
        description: '',
        isEmpty: false,
        data: []
    });

    useEffect(() => {
        handleOnSearch();
    }, []);

    const handleOnSearch = () => {
        setStateSearch({
            isFetching: true,
            isError: false,
            description: '',
            isEmpty: false,
            data: []
        });
        const body = {
            loginStoreId,
            languageID,
            moduleID,
            keyword
        };
        searchExchangeProgram(body)
            .then((response) => {
                if (response.length > 0) {
                    setStateSearch({
                        isFetching: false,
                        isError: false,
                        description: '',
                        isEmpty: false,
                        data: response
                    });
                } else {
                    setStateSearch({
                        isFetching: false,
                        isError: false,
                        description:
                            'Không tìm thấy thông tin chương trình thu cũ đổi mới',
                        isEmpty: true,
                        data: []
                    });
                }
            })
            .catch((error) => {
                setStateSearch({
                    isFetching: false,
                    isError: true,
                    description: error,
                    isEmpty: true,
                    data: []
                });
            });
    };
    return (
        <SafeAreaView style={{ flex: 1 }}>
            <View style={{ marginHorizontal: 10, flexDirection: 'row' }}>
                <SearchBar
                    value={keyword}
                    placeholder="Nhập từ khoá để tìm chương trình"
                    onChangeText={(text) => {
                        setKeyword(text);
                    }}
                    onSearch={handleOnSearch}
                    onDelete={() => {
                        setKeyword('');
                    }}
                    disabled={stateSearch.isFetching}
                />
            </View>
            <BaseLoading
                isLoading={stateSearch.isFetching}
                isError={stateSearch.isError}
                isEmpty={stateSearch.isEmpty}
                textLoadingError={stateSearch.description}
                onPressTryAgains={handleOnSearch}
                content={
                    <View>
                        <FlatList
                            data={stateSearch.data}
                            renderItem={({ item }) => (
                                <ProgramItem data={item} />
                            )}
                        />
                    </View>
                }
            />
        </SafeAreaView>
    );
};

export default ProgramScreen;
