import { apiBase, EMP<PERSON>, METHOD } from '@config';
import { API_CONST } from '@constants';
import { helper } from '@common';
import { translate } from '@translate';

const {
    API_SEARCH_EX_PROGRAM,
    API_GET_RETURN_PRODUCT_INFO_BY_IMEI,
    API_GET_RETURN_PRODUCT_STATES,
    API_SEARCH_EX_RECEIPT,
    API_GET_EX_RECEIPT_DETAIL,
    API_DELETE_RECEIPT,
    API_PRINT_EX_RECEIPT,
    API_GET_EXCHANGE_PRODUCT_INFO,
    API_CHECK_RETURN_PRODUCT_STATES,
    API_INSERT_EXCHANGE_RECEIPT,
    API_SEARCH_PRODUCT_NEW
} = API_CONST;

export const actions = {
    RESET_PRODUCT_EVALUATION: 'RESET_PRODUCT_EVALUATION',
    SET_EXCHANGE_PROGRAM: 'SET_EXCHANGE_PROGRAM',
    ADD_RETURN_PRODUCT: 'ADD_RETURN_PRODUCT',
    DELETE_RETURN_PRODUCT: 'DELETE_RETURN_PRODUCT',
    UPDATE_PRODUCT_STATES: 'UPDATE_PRODUCT_STATES'
};

export const setExchangeProgram = (payload) => ({
    type: actions.SET_EXCHANGE_PROGRAM,
    payload
});

export const addReturnProduct = (payload) => ({
    type: actions.ADD_RETURN_PRODUCT,
    payload
});

export const deleteReturnProductByIMEISet = (set) => ({
    type: actions.DELETE_RETURN_PRODUCT,
    payload: set
});

export const updateProductStates = (payload) => ({
    type: actions.UPDATE_PRODUCT_STATES,
    payload
});

export const searchExchangeProgram = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_SEARCH_EX_PROGRAM, METHOD.POST, body)
            .then((res) => {
                console.log('searchExchangeProgram success', res.object);
                if (
                    helper.hasProperty(res, 'object') &&
                    helper.isArray(res.object) &&
                    res.object.length > 0
                ) {
                    resolve(res.object);
                } else {
                    resolve([]);
                }
            })
            .catch((err) => {
                console.log('searchExchangeProgram error', err);
                reject(err.msgError);
            });
    });
};

export const getReturnProductInfoByIMEI = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_GET_RETURN_PRODUCT_INFO_BY_IMEI, METHOD.POST, body)
            .then((res) => {
                console.log('getReturnProductInfoByIMEI success', res.object);
                if (helper.hasProperty(res, 'object')) {
                    resolve(res.object);
                } else {
                    resolve(null);
                }
            })
            .catch((err) => {
                console.log('getReturnProductInfoByIMEI error', err);
                reject(err.msgError);
            });
    });
};

export const getReturnProductStates = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_GET_RETURN_PRODUCT_STATES, METHOD.POST, body)
            .then((res) => {
                console.log('getReturnProductStates success', res.object);
                if (
                    helper.hasProperty(res, 'object') &&
                    helper.isArray(res.object) &&
                    res.object.length > 0
                ) {
                    resolve(res.object);
                } else {
                    resolve([]);
                }
            })
            .catch((err) => {
                console.log('getReturnProductStates error', err);
                reject(err.msgError);
            });
    });
};

export const getReceiptDetail = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_GET_EX_RECEIPT_DETAIL, METHOD.POST, body)
            .then((res) => {
                console.log('getReceiptDetail success', res.object);
                if (
                    helper.hasProperty(res, 'object') &&
                    helper.isObject(res.object) &&
                    !helper.IsEmptyObject(res.object)
                ) {
                    resolve(res.object);
                } else {
                    const content = 'Lỗi lấy thông tin chi tiết.';
                    reject(content);
                }
            })
            .catch((err) => {
                console.log('getReceiptDetail error', err);
                reject(err.msgError);
            });
    });
};

export const searchExchangeReceipt = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_SEARCH_EX_RECEIPT, METHOD.POST, body)
            .then((res) => {
                console.log('searchExchangeReceipt success', res.object);
                if (
                    helper.hasProperty(res, 'object') &&
                    helper.isArray(res.object) &&
                    res.object.length > 0
                ) {
                    resolve(res.object);
                } else {
                    resolve([]);
                }
            })
            .catch((err) => {
                console.log('searchExchangeReceipt error', err);
                reject(err.msgError);
            });
    });
};

export const deleteReceipt = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_DELETE_RECEIPT, METHOD.POST, body)
            .then((res) => {
                console.log('deleteReceipt success', res.object);
                const content = 'Xóa thành công.';
                resolve(content);
            })
            .catch((err) => {
                console.log('deleteReceipt error', err);
                reject(err.msgError);
            });
    });
};

export const printExchangeReceipt = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_PRINT_EX_RECEIPT, METHOD.POST, body)
            .then((res) => {
                console.log('printExchangeReceipt success', res.object);
                if (
                    helper.hasProperty(res, 'object') &&
                    helper.isObject(res.object) &&
                    !helper.IsEmptyObject(res.object) &&
                    helper.IsNonEmptyString(
                        res.object.ExchangeReceiptHTMLContent
                    )
                ) {
                    resolve(res.object.ExchangeReceiptHTMLContent);
                } else {
                    const content = 'Lỗi lấy thông tin HTML.';
                    reject(content);
                }
            })
            .catch((err) => {
                console.log('printExchangeReceipt error', err);
                reject(err.msgError);
            });
    });
};

export const getExchangeProductInfo = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_GET_EXCHANGE_PRODUCT_INFO, METHOD.POST, body)
            .then((res) => {
                console.log('getExchangeProductInfo success', res.object);
                if (
                    helper.hasProperty(res, 'object') &&
                    helper.isArray(res.object) &&
                    res.object.length > 0
                ) {
                    resolve(res.object);
                } else {
                    const error = 'Không tìm thấy thông tin máy được đổi sang';
                    reject(error);
                }
            })
            .catch((err) => {
                console.log('getExchangeProductInfo error', err);
                reject(err.msgError);
            });
    });
};

export const checkReturnProductStates = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_CHECK_RETURN_PRODUCT_STATES, METHOD.POST, body)
            .then((res) => {
                console.log('checkReturnProductStates success', res.object);
                if (
                    helper.hasProperty(res, 'object') &&
                    helper.isObject(res.object) &&
                    !helper.IsEmptyObject(res.object)
                ) {
                    resolve(res.object);
                } else {
                    const content = 'Lỗi kiểm tra tình trạng sản phẩm.';
                    reject(content);
                }
            })
            .catch((err) => {
                console.log('checkReturnProductStates error', err);
                reject(err.msgError);
            });
    });
};
export const getUser = function (body) {
    return new Promise((resolve, reject) => {
        apiBase(API_CONST.API_SEARCHUSER, METHOD.POST, body)
            .then((response) => {
                const { object } = response;
                if (
                    helper.hasProperty(response, 'object') &&
                    helper.isArray(response.object) &&
                    object.length > 0
                ) {
                    resolve(response.object);
                } else {
                    reject(
                        translate(
                            'instalmentManager.no_agent_information_found'
                        )
                    );
                }
                console.log('response getUser: ', response);
            })
            .catch((error) => {
                reject(
                    translate('instalmentManager.error_get_agent_information')
                );
                console.log('error getUser: ', error);
            });
    });
};

export const insertExchangeReceipt = (body) => {
    return new Promise((resolve, reject) => {
        apiBase(API_INSERT_EXCHANGE_RECEIPT, METHOD.POST, body)
            .then((res) => {
                console.log('insertExchangeReceipt success', res.object);
                if (
                    helper.hasProperty(res, 'object') &&
                    helper.isObject(res.object) &&
                    !helper.IsEmptyObject(res.object)
                ) {
                    resolve(res.object);
                } else {
                    const content = 'Lỗi tạo phiếu thẩm định máy cũ';
                    reject(content);
                }
            })
            .catch((err) => {
                console.log('insertExchangeReceipt error', err);
                reject(err.msgError);
            });
    });
};

export const searchProduct = function (keyword, saleScenarioTypeID, exchangeProgramID) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "saleScenarioTypeID": saleScenarioTypeID,
                "exchangeProgramID": exchangeProgramID,
                "keyword": keyword
            };
            apiBase(API_SEARCH_PRODUCT_NEW, METHOD.POST, body, { setTimeOut: 5000 }).then((response) => {
                console.log('searchProduct success', response);
                const { object } = response;
                if (helper.isArray(object) && object.length > 0) {
                    resolve({
                        isEmpty: !EMPTY,
                        description: "",
                        data: object
                    })
                }
                else {
                    resolve({
                        isEmpty: EMPTY,
                        description: translate("inventory_share.not_found_product_info"),
                        data: []
                    })
                }
            }).catch(error => {
                console.log("searchProduct error", error);
                reject(error.msgError)
            })
        })
    }
}