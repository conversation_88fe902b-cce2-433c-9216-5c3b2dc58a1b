import { View, Image, StyleSheet } from 'react-native';
import React from 'react';
import { COLORS } from '@styles';
import { MyText } from '@components';
import { COLOR } from '../constants';

const TextField = ({ name, value, valueColor = COLORS.txt333333 }) => {
    return (
        <MyText
            text={`${name}: `}
            style={{
                color: COLORS.txt8E8E93
            }}>
            <MyText
                text={value}
                style={{
                    color: valueColor
                }}
            />
        </MyText>
    );
};
export const ProductDetail = ({
    imageURL,
    IMEI,
    name,
    saleOrderID,
    customerName,
    customerPhone
}) => {
    return (
        <View style={{ flexDirection: 'row', marginBottom: 5 }}>
            <View
                style={{
                    borderWidth: StyleSheet.hairlineWidth,
                    borderColor: COLORS.bg8E8E93,
                    borderRadius: 5,
                    padding: 10,
                    alignItems: 'center',
                    justifyContent: 'center'
                }}>
                <Image
                    style={{
                        height: 65,
                        width: 65
                    }}
                    resizeMode="contain"
                    source={
                        imageURL
                            ? { uri: imageURL }
                            : require('../../../../assets/smartphone.png')
                    }
                />
            </View>
            <View style={{ flex: 1, marginLeft: 10 }}>
                <MyText
                    text={`IMEI/SN: ${IMEI}`}
                    style={{
                        color: COLOR.RED_4,
                        fontWeight: 'bold'
                    }}
                />
                <MyText
                    text={name}
                    style={{ color: COLOR.BLUE_2, fontWeight: 'bold' }}
                />
                <TextField
                    name="Đơn hàng"
                    value={saleOrderID ?? 'Bên ngoài hệ thống'}
                    valueColor={saleOrderID ? COLORS.bg000000 : COLOR.RED_4}
                />
                <TextField
                    name="Khách hàng"
                    value={customerName ?? 'Không có'}
                />
                <TextField
                    name="Số điện thoại"
                    value={customerPhone ?? 'Không có'}
                />
            </View>
        </View>
    );
};

export default ProductDetail;
