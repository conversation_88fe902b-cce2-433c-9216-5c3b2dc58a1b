import {
    View,
    Modal,
    Text,
    TouchableOpacity,
    ScrollView,
    Alert
} from 'react-native';
import React, { useEffect, useState } from 'react';
import { constants } from '@constants';
import { COLORS } from '@styles';
import { Icon, Button, MyText } from '@components';
import { translate } from '@translate';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/core';
import { helper } from '@common';
import Toast, { BaseToast, ErrorToast } from 'react-native-toast-message';
import { ProductDetail, CheckBox } from './';
import { COLOR } from '../constants';
import {
    addReturnProduct,
    checkReturnProductStates,
    updateProductStates
} from '../actions';

const TOAST_CONFIG = {
    success: (props) => (
        <BaseToast {...props} text1NumberOfLines={2} text2NumberOfLines={2} />
    ),
    error: (props) => (
        <ErrorToast {...props} text1NumberOfLines={2} text2NumberOfLines={2} />
    )
};

const Title = ({ text }) => {
    return (
        <View style={{ overflow: 'hidden', paddingBottom: 5 }}>
            <View
                style={{
                    backgroundColor: COLORS.bgFFFFFF,
                    width: '100%',
                    height: 30,
                    shadowColor: COLORS.bg000000,
                    shadowOffset: { width: 1, height: 1 },
                    shadowOpacity: 0.4,
                    shadowRadius: 3,
                    elevation: 3
                }}>
                <Text
                    style={{
                        color: COLOR.BLUE_1,
                        fontWeight: 'bold',
                        fontSize: 16,
                        textAlign: 'center'
                    }}>
                    {text}
                </Text>
            </View>
        </View>
    );
};

const ButtonClose = ({ onPress, isDisabled }) => {
    return (
        <TouchableOpacity
            disabled={isDisabled}
            style={{
                opacity: isDisabled ? 0.6 : 1,
                width: 30,
                height: 30,
                position: 'absolute',
                right: 1,
                top: 11
            }}
            onPress={onPress}>
            <Icon
                iconSet="AntDesign"
                name="close"
                size={22}
                color={COLOR.RED_3}
            />
        </TouchableOpacity>
    );
};

const ButtonAction = ({ onClose, onSubmit, isDisabled }) => (
    <View
        style={{
            flexDirection: 'row',
            justifyContent: 'center',
            marginTop: 10
        }}>
        <Button
            disabled={isDisabled}
            text={translate('common.btn_skip').toUpperCase()}
            onPress={onClose}
            styleContainer={{
                opacity: isDisabled ? 0.6 : 1,
                borderRadius: 4,
                borderColor: COLOR.ORANGE_2,
                height: 40,
                marginRight: 10,
                borderWidth: 1,
                width: '40%'
            }}
            styleText={{
                color: COLOR.ORANGE_2,
                fontSize: 14,
                fontWeight: 'bold'
            }}
        />

        <Button
            isLoading={isDisabled}
            disabled={isDisabled}
            text={translate('common.btn_confirm').toUpperCase()}
            onPress={onSubmit}
            styleContainer={{
                opacity: isDisabled ? 0.6 : 1,
                borderRadius: 4,
                backgroundColor: COLOR.ORANGE_2,
                borderColor: COLOR.ORANGE_2,
                marginLeft: 10,
                height: 40,
                borderWidth: 1,
                width: '40%'
            }}
            styleText={{
                color: COLORS.txtFFFFFF,
                fontSize: 14,
                fontWeight: 'bold',
                marginRight: 5
            }}
        />
    </View>
);
const ProductStateModal = ({
    isEditing,
    isShowModal,
    handleOnClose,
    productStates,
    productInfo,
    IMEI,
    shouldGoBack
}) => {
    const dispatch = useDispatch();
    const {
        storeID: loginStoreId,
        moduleID,
        languageID
    } = useSelector((state) => state.userReducer);

    const {
        exchangeProgram: { ExchangeProgramID }
    } = useSelector((state) => state.productEvaluationReducer);
    const navigation = useNavigation();
    const {
        productID,
        productImage,
        productName,
        saleOrderID,
        saleOrderDetailID,
        customerName,
        customerPhone
    } = productInfo;
    const [states, setStates] = useState([]);
    const [isFetching, setIsFetching] = useState(false);
    useEffect(() => {
        if (productStates) {
            setStates(helper.deepCopy(productStates));
        }
    }, [productStates]);

    const handleCheckStates = (id) => () => {
        setStates(prevStates =>
            prevStates.map(item => ({
                ...item,
                isChecked: item.ExchangeProductStateID === id ? !item.isChecked : false
            }))
        );
    };



    const handleOnSubmit = () => {
        const ExchangeStateIDList = states
            .filter((item) => item.isChecked)
            .map((state) => state.ExchangeProductStateID)
            .join();
        if (helper.IsEmptyString(ExchangeStateIDList)) {
            Toast.show({
                type: 'error',
                text1: 'Vui lòng chọn tình trạng máy của khách.'
            });
        } else {
            setIsFetching(true);
            checkReturnProductStates({
                moduleID,
                languageID,
                loginStoreId,
                ExchangeProgramID,
                ExchangeStateIDList,
                ReturnProductID: productID
            })
                .then(({ StateGroupID }) => {
                    setIsFetching(false);
                    if (isEditing) {
                        dispatch(
                            updateProductStates({
                                IMEI,
                                states,
                                StateGroupID
                            })
                        );
                    } else {
                        dispatch(
                            addReturnProduct({
                                IMEI,
                                productID,
                                productImage,
                                productName,
                                saleOrderID,
                                saleOrderDetailID,
                                customerName,
                                customerPhone,
                                states,
                                StateGroupID
                            })
                        );
                    }
                    handleOnClose();
                    shouldGoBack && navigation.goBack();
                })
                .catch((error) => {
                    Alert.alert(
                        translate('common.notification_uppercase'),
                        error,
                        [
                            {
                                text: 'OK',
                                style: 'default',
                                onPress: () => setIsFetching(false)
                            }
                        ]
                    );
                });
        }
    };
    return (
        <View
            style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center'
            }}>
            <Modal animationType="fade" transparent visible={isShowModal}>
                <View
                    style={{
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center',
                        backgroundColor: COLORS.bg0000004
                    }}>
                    <View
                        style={{
                            width: constants.width - 20,
                            backgroundColor: COLORS.bgFFFFFF,
                            paddingVertical: 10,
                            borderRadius: 5
                        }}>
                        <Title text="Chọn tình trạng máy" />
                        <ButtonClose
                            onPress={handleOnClose}
                            isDisabled={isFetching}
                        />
                        <View style={{ margin: 10, marginBottom: 0 }}>
                            <ProductDetail
                                imageURL={productImage}
                                name={productName}
                                saleOrderID={saleOrderID}
                                customerName={customerName}
                                customerPhone={customerPhone}
                                IMEI={IMEI}
                            />
                            <MyText
                                text="Chọn tình trạng máy của khách:"
                                style={{ fontWeight: 'bold' }}
                            />
                            <View
                                style={{
                                    maxHeight: '70%'
                                }}>
                                <ScrollView style={{ flexGrow: 0 }}>
                                    {states.map(
                                        ({
                                            ExchangeProductStateID,
                                            ExchangeProductStateName,
                                            isChecked
                                        }) => (
                                            <CheckBox
                                                isDisabled={isFetching}
                                                isCheck={isChecked}
                                                title={ExchangeProductStateName}
                                                marginVertical={2}
                                                onCheck={handleCheckStates(
                                                    ExchangeProductStateID
                                                )}
                                            />
                                        )
                                    )}
                                </ScrollView>
                            </View>
                        </View>
                        <ButtonAction
                            onClose={handleOnClose}
                            onSubmit={handleOnSubmit}
                            isDisabled={isFetching}
                        />
                    </View>
                </View>
                <Toast
                    config={TOAST_CONFIG}
                    position="bottom"
                    visibilityTime={2000}
                />
            </Modal>
        </View>
    );
};

export default ProductStateModal;
