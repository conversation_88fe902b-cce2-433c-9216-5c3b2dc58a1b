import { View, TouchableOpacity } from 'react-native';
import React, { useState } from 'react';
import { Icon, MyText } from '@components';
import { COLORS } from '@styles';
import { helper } from '@common';
import { COLOR } from '../constants';
import { useSelector } from 'react-redux';

const ReturnProduct = ({ data }) => {
    const {
        ReturnProductID,
        cus_ReturnProductName,
        cus_ExchangeReceiptDetailStateBOList,
        SupplierDiscountMoney,
        ProductValue,
        DiscountValue,
        ReturnProductName,
        ReturnIMEI,
        MWGTradeInBonusValue,
    } = data;

    const productName = cus_ReturnProductName || ReturnProductName;

    const [isViewDetailMoney, setIsViewDetailMoney] = useState(false);
    const { dataFromPartner } = useSelector(
        (state) => state.productEvaluationReducer
    );
    const selectedItem = dataFromPartner?.data?.find((item) => item.IsSelected);

    return (
        <View style={{ marginVertical: 3 }}>
            <MyText
                style={{
                    color: COLORS.txt0000FF,
                    fontWeight: 'bold'
                }}
                addSize={1}
                text={`${ReturnProductID} - ${productName}`}
            />
            <MyText
                text={`IMEI/SN: ${ReturnIMEI}`}
                style={{ fontWeight: 'bold' }}
            />
            <MyText style={{ fontWeight: 'bold' }} text="Tình trạng: " />
            {!helper.IsEmptyObject(selectedItem) ? <MyText
                style={{
                    marginBottom: 5,
                    fontWeight: 'bold'
                }}
                text={selectedItem.PartnerReceiptBO?.StateGroupID}>
                <MyText text={` - ${selectedItem.PartnerReceiptBO?.StateGroupName}`} />
            </MyText> : cus_ExchangeReceiptDetailStateBOList?.map(
                ({
                    ExchangeProductStateID: id,
                    ExchangeProductStateName: stateName
                }) => (
                    <MyText
                        style={{
                            marginBottom: 5,
                            fontWeight: 'bold'
                        }}
                        text={id}>
                        <MyText text={` - ${stateName}`} />
                    </MyText>
                )
            )}
            <TouchableOpacity
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingVertical: 5,
                    backgroundColor: COLORS.bgFDF9E5,
                    marginHorizontal: -10,
                    paddingHorizontal: 10,
                }}
                activeOpacity={0.8}
                onPress={() => {
                    setIsViewDetailMoney(!isViewDetailMoney)
                }}
            >
                <MyText
                    style={{
                        fontWeight: 'bold',
                        color: COLORS.txt147EFB,
                        flex: 1,
                        marginRight: 5
                    }}
                    text="Tiền hỗ trợ và xác máy ">
                    <MyText
                        style={{ fontStyle: 'italic' }}
                        addSize={-2}
                        text="(Đã làm tròn)"
                    />
                </MyText>
                <MyText
                    style={{ fontWeight: 'bold', color: COLOR.RED_3 }}
                    text={helper.convertNum(DiscountValue)}
                />
                <Icon
                    iconSet="Ionicons"
                    name={isViewDetailMoney ? 'chevron-up' : 'chevron-down'}
                    color={COLORS.icFC3D39}
                    size={22}
                />
            </TouchableOpacity>
            {isViewDetailMoney && <View
                style={{
                    paddingVertical: 5
                }}>
                {
                    SupplierDiscountMoney > 0 && <MyText
                        style={{ color: COLORS.txt555555, flex: 1 }}
                        text="Tiền hãng hỗ trợ: ">
                        <MyText
                            style={{ fontWeight: 'bold', color: COLORS.txtFF0000 }}
                            text={helper.convertNum(SupplierDiscountMoney)}
                        />
                    </MyText>
                }
                <MyText
                    style={{
                        color: COLORS.txt555555, paddingVertical: 5
                    }}
                    text="Tiền MWG hỗ trợ: ">
                    <MyText
                        style={{ fontWeight: 'bold', color: COLORS.txtFF0000 }}
                        text={helper.convertNum(MWGTradeInBonusValue)}
                    />
                </MyText>

                <MyText style={{ color: COLORS.txt555555 }} text="Tiền xác máy: ">
                    <MyText
                        style={{ fontWeight: 'bold', color: COLORS.txtFF0000 }}
                        text={helper.convertNum(ProductValue)}
                    />
                </MyText>
            </View>}
        </View>
    );
};

const ReturnProductsSection = ({ data, totalMoney }) => {

    const [isViewDetailMoney, setIsViewDetailMoney] = useState(false);

    const totalSupplierMoney = data.reduce(
        (accumulator, currentValue) =>
            accumulator + currentValue.SupplierDiscountMoney + currentValue.MWGTradeInBonusValue,
        0
    );
    return (
        <View>
            {data.map((item) => (
                <ReturnProduct data={item} />
            ))}
            <TouchableOpacity
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingVertical: 5,
                    backgroundColor: COLOR.YELLOW_2,
                    marginHorizontal: -10,
                    paddingHorizontal: 10,
                }}
                activeOpacity={0.8}
                onPress={() => {
                    setIsViewDetailMoney(!isViewDetailMoney)
                }}>
                <MyText
                    style={{
                        fontWeight: 'bold',
                        color: COLOR.RED_2,
                        flex: 1,
                        marginRight: 5
                    }}
                    text="Tổng tiền hỗ trợ và xác máy ">
                    <MyText
                        style={{ fontStyle: 'italic' }}
                        addSize={-2}
                        text="(Đã làm tròn)"
                    />
                </MyText>

                <MyText
                    style={{ fontWeight: 'bold', color: COLORS.txtFF0000 }}
                    text={helper.convertNum(totalMoney)}
                />
                <Icon
                    iconSet="Ionicons"
                    name={isViewDetailMoney ? 'chevron-up' : 'chevron-down'}
                    color={COLORS.icFC3D39}
                    size={22}
                />
            </TouchableOpacity>
            {isViewDetailMoney && <View
                style={{
                    flexDirection: 'row',
                    paddingVertical: 5
                }}>
                <MyText
                    style={{ color: COLORS.txt555555, flex: 1 }}
                    text="Tổng tiền hỗ trợ: ">
                    <MyText
                        style={{ fontWeight: 'bold', color: COLORS.txtFF0000 }}
                        text={helper.convertNum(totalSupplierMoney)}
                    />
                </MyText>
                <MyText style={{ color: COLORS.txt555555 }} text="Tổng tiền xác máy: ">
                    <MyText
                        style={{ fontWeight: 'bold', color: COLORS.txtFF0000 }}
                        text={helper.convertNum(totalMoney - totalSupplierMoney)}
                    />
                </MyText>
            </View>}
        </View>
    );
};

export default ReturnProductsSection;
