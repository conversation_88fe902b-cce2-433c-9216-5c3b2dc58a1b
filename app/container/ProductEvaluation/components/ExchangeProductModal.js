import {
    View,
    Modal,
    Text,
    TouchableOpacity,
    Image,
    StyleSheet
} from 'react-native';
import React from 'react';
import { constants } from '@constants';
import { COLORS } from '@styles';
import { Icon, MyText } from '@components';
import { helper } from '@common';
import { COLOR } from '../constants';

const Title = ({ text }) => {
    return (
        <View style={{ overflow: 'hidden', paddingBottom: 5 }}>
            <View
                style={{
                    backgroundColor: COLORS.bgFFFFFF,
                    width: '100%',
                    height: 30,
                    shadowColor: COLORS.bg000000,
                    shadowOffset: { width: 1, height: 1 },
                    shadowOpacity: 0.4,
                    shadowRadius: 3,
                    elevation: 3
                }}>
                <Text
                    style={{
                        color: COLOR.BLUE_1,
                        fontWeight: 'bold',
                        fontSize: 16,
                        textAlign: 'center'
                    }}>
                    {text}
                </Text>
            </View>
        </View>
    );
};

const ButtonClose = ({ onPress }) => {
    return (
        <TouchableOpacity
            style={{
                width: 30,
                height: 30,
                position: 'absolute',
                right: 1,
                top: 11
            }}
            onPress={onPress}>
            <Icon
                iconSet="AntDesign"
                name="close"
                size={22}
                color={COLOR.RED_3}
            />
        </TouchableOpacity>
    );
};

const MoneyField = ({ title, value, valueColor = COLORS.bg000000 }) => (
    <View
        style={{
            flexDirection: 'row',
            justifyContent: 'space-between'
        }}>
        <MyText text={title} />
        <MyText
            style={{ fontWeight: 'bold', color: valueColor }}
            text={helper.convertNum(value)}
        />
    </View>
);

const Item = ({ data }) => {
    const {
        ReturnProductName,
        cus_ReturnImageProduct,
        ReturnIMEI,
        SupplierDiscountMoney,
        DiscountValue,
        ProductValue,
        MWGTradeInBonusValue
    } = data;
    return (
        <View
            style={{
                flexDirection: 'row',
                marginBottom: 15
            }}>
            <View
                style={{
                    borderWidth: StyleSheet.hairlineWidth,
                    borderColor: COLORS.bg8E8E93,
                    borderRadius: 5,
                    padding: 10,
                    alignItems: 'center',
                    justifyContent: 'center'
                }}>
                <Image
                    style={{
                        height: 50,
                        width: 50
                    }}
                    resizeMode="contain"
                    source={
                        cus_ReturnImageProduct
                            ? { uri: cus_ReturnImageProduct }
                            : require('../../../../assets/smartphone.png')
                    }
                />
            </View>
            <View
                style={{
                    flex: 1,
                    marginLeft: 10
                }}>
                <MyText
                    text={`${ReturnProductName}`}
                    style={{
                        color: COLOR.BLUE_2,
                        fontWeight: 'bold'
                    }}
                />
                <MyText
                    text={`IMEI/SN: ${ReturnIMEI}`}
                    style={{ fontWeight: 'bold' }}
                />
                {
                    SupplierDiscountMoney > 0 && <MoneyField
                        title="Tiền hãng hỗ trợ:"
                        value={SupplierDiscountMoney}
                    />
                }

                <MoneyField
                    title="Tiền MWG hỗ trợ:"
                    value={MWGTradeInBonusValue}
                />
                <MoneyField title="Tiền xác máy:" value={ProductValue} />
                <MoneyField
                    valueColor="red"
                    title="Tiền hỗ trợ và xác máy:"
                    value={DiscountValue}
                />
            </View>
        </View>
    );
};

const ExchangeProductModal = ({ data, isShowModal, handleOnClose }) => {
    console.log('data', data);
    const { cus_ExchangeReceiptDetailBOList } = data;
    return (
        <View
            style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center'
            }}>
            <Modal animationType="fade" transparent visible={isShowModal}>
                <View
                    style={{
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center',
                        backgroundColor: COLORS.bg0000004
                    }}>
                    <View
                        style={{
                            width: constants.width - 20,
                            backgroundColor: COLORS.bgFFFFFF,
                            paddingVertical: 10,
                            borderRadius: 5
                        }}>
                        <Title text="Danh sách sản phẩm thu cũ" />
                        <ButtonClose onPress={handleOnClose} />
                        <View style={{ margin: 10, marginBottom: 0 }}>
                            {cus_ExchangeReceiptDetailBOList.map((item) => (
                                <Item data={item} />
                            ))}
                        </View>
                    </View>
                </View>
            </Modal>
        </View>
    );
};

export default ExchangeProductModal;
