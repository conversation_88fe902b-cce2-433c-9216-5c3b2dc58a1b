import { View, TextInput, Keyboard, TouchableOpacity } from 'react-native';
import React, { useEffect, useRef } from 'react';

import { constants } from '@constants';
import { COLORS } from '@styles';
import { Icon } from '@components';

const InputSearch = ({
    value,
    onChangeText,
    onFocus,
    onBlur,
    clearText,
    openBarcode,
    isClose,
    editable
}) => {
    const inputRef = useRef(null);
    let timeFocus = null;
    const onAutoFocus = () => {
        if (inputRef) {
            inputRef.current.focus();
        }
    };
    const onClearTimer = () => {
        if (timeFocus) {
            clearTimeout(timeFocus);
        }
    };
    const didMount = () => {
        timeFocus = setTimeout(onAutoFocus, 50);
        return onClearTimer;
    };

    useEffect(didMount, []);

    return (
        <View
            style={{
                flexDirection: 'row',
                width: constants.width - 20,
                marginBottom: 8,
                borderWidth: 1,
                borderColor: COLORS.bdE4E4E4,
                height: 40,
                backgroundColor: COLORS.bgFFFFFF,
                borderRadius: 20,
                justifyContent: 'space-between',
                alignItems: 'center',
                alignSelf: 'center',
                marginTop: 10
            }}>
            <TextInput
                editable={editable}
                ref={inputRef}
                onFocus={onFocus}
                onBlur={onBlur}
                autoFocus
                value={value}
                onChangeText={onChangeText}
                placeholder="Nhập tên sản phẩm cần trả / thu mua"
                placeholderTextColor="gray"
                onSubmitEditing={Keyboard.dismiss}
                style={{
                    height: 40,
                    width: constants.width - 68,
                    paddingLeft: 20
                }}
                maxLength={150}
            />
            {isClose ? (
                <TouchableOpacity
                    style={{
                        height: 40,
                        width: 48,
                        justifyContent: 'center',
                        alignItems: 'flex-end',
                        paddingRight: 20
                    }}
                    onPress={clearText}>
                    <Icon
                        iconSet="Ionicons"
                        name="close"
                        color={COLORS.ic848A8C}
                        size={22}
                    />
                </TouchableOpacity>
            ) : (
                <TouchableOpacity
                    style={{
                        height: 40,
                        width: 48,
                        justifyContent: 'center',
                        alignItems: 'flex-end',
                        paddingRight: 20
                    }}
                    onPress={openBarcode}>
                    <Icon
                        iconSet="MaterialCommunityIcons"
                        name="barcode-scan"
                        color={COLORS.icFFD400}
                        size={22}
                    />
                </TouchableOpacity>
            )}
        </View>
    );
};

export default InputSearch;
