import {
    View,
    TouchableOpacity,
    SafeAreaView,
    FlatList,
    Alert,
    KeyboardAvoidingView,
    Platform
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import React, { useCallback, useEffect, useState } from 'react';
import { COLORS } from '@styles';
import {
    Icon,
    BaseLoading,
    Button,
    showBlockUI,
    hideBlockUI,
    HOOKSearchInput,
    BarcodeCamera
} from '@components';
import { constants } from '@constants';
import moment from 'moment';
import { convertHtml2Pdf } from '@common';
import { translate } from '@translate';
import {
    SearchBar,
    ModalFilterSearch,
    ExchangeReceiptItem
} from './components';
import {
    deleteReceipt,
    getDataFromPartner,
    printExchangeReceipt,
    resetDataFromPartner,
    searchExchangeReceipt
} from './actions';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';
import { hasValidPartnerEco } from "./helper"

const ProductEvaluation = ({ navigation }) => {

    const { dataFromPartner } = useSelector(
        (state) => state.productEvaluationReducer
    );

    const [isShowModal, setIsShowModal] = useState(false);
    const [isReload, setIsReload] = useState(false);
    const [stateSearch, setStateSearch] = useState({
        isFetching: false,
        isError: false,
        description: '',
        isEmpty: false,
        data: []
    });
    const [filter, setFilter] = useState({
        keyword: '',
        fromDate: new Date(),
        toDate: new Date(),
        searchType: -1,
        isDelete: false
    });

    const [isVisibleScan, setIsVisibleScan] = useState(false)
    const textInput = React.useRef(null)

    const dispatch = useDispatch()

    const {
        storeID: loginStoreId,
        moduleID,
        languageID
    } = useSelector((state) => state.userReducer);
    const { saleScenarioTypeID } = useSelector(
        (state) => state.specialSaleProgramReducer
    );
    const handleOnSearch = (keyWord = "") => {
        setStateSearch({
            isFetching: true,
            isError: false,
            description: '',
            isEmpty: false,
            data: []
        });
        setIsReload(false);
        const body = {
            loginStoreId,
            languageID,
            moduleID,
            Keyword: keyWord || filter.keyword,
            FromDate: moment(filter.fromDate).format('YYYY-MM-DD'),
            ToDate: moment(filter.toDate).format('YYYY-MM-DD'),
            IsDeleted: filter.isDelete,
            SearchBy: filter.searchType
        };
        searchExchangeReceipt(body)
            .then((response) => {
                if (response.length > 0) {
                    setStateSearch({
                        isFetching: false,
                        isError: false,
                        description: '',
                        isEmpty: false,
                        data: response
                    });
                } else {
                    setStateSearch({
                        isFetching: false,
                        isError: false,
                        description: 'Không tìm thấy phiếu thẩm định mãy cũ',
                        isEmpty: true,
                        data: []
                    });
                }
            })
            .catch((error) => {
                setStateSearch({
                    isFetching: false,
                    isError: true,
                    description: error,
                    isEmpty: true,
                    data: []
                });
            });
    };
    const handleOnSearchEco = (keyWord = "") => {
        showBlockUI()
        dispatch(getDataFromPartner({ keyword: keyWord })).then(() => {
            hideBlockUI()
            navigation.navigate('ProgramScreen');
        }).catch((msgError) => {
            Alert.alert(
                translate('common.notification_uppercase'),
                msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: 'cancel',
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: 'default',
                        onPress: () => handleOnSearchEco(keyWord)
                    }
                ]
            );
        })

    };

    const deleteReceiptItem = (id) => {
        const body = {
            loginStoreId,
            languageID,
            moduleID,
            ExchangeReceiptID: id
        };
        showBlockUI();
        deleteReceipt(body)
            .then((response) => {
                hideBlockUI();
                Alert.alert(
                    'Thông báo',
                    response,
                    [
                        {
                            text: 'OK',
                            onPress: () => {
                                setFilter({
                                    keyword: '',
                                    fromDate: new Date(),
                                    toDate: new Date(),
                                    searchType: -1,
                                    isDelete: false
                                });
                                setIsReload(true);
                            }
                        }
                    ],
                    { cancelable: false }
                );
            })
            .catch((msgError) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: 'cancel',
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: 'default',
                            onPress: () => deleteReceiptItem(id)
                        }
                    ]
                );
            });
    };

    const getBase64Reprint = (ID) => {
        showBlockUI();
        const body = {
            loginStoreId,
            languageID,
            moduleID,
            saleScenarioTypeID,
            exchangeReceiptID: ID
        };
        printExchangeReceipt(body)
            .then(async (html) => {
                const base64PDF = await convertHtml2Pdf(html);
                hideBlockUI();
                navigation.navigate('PrintScreen', { base64: base64PDF });
            })
            .catch((msgError) => {
                Alert.alert('Thông báo', msgError, [
                    {
                        text: 'Bỏ qua',
                        onPress: hideBlockUI
                    },
                    {
                        text: 'Thử lại',
                        onPress: () => getBase64Reprint(ID)
                    }
                ]);
            });
    };

    const processInput = (input) => {
        const value = input?.toString().toUpperCase() ?? "";
        if (value.startsWith("ESVN")) {
            handleOnSearchEco(value);
        } else {
            handleOnSearch(value);
        }
    };

    const handleBarcode = (barcode) => {
        setIsVisibleScan(false);
        processInput(barcode);
    };

    const handleSubmitEditing = (event) => {
        processInput(event?.nativeEvent?.text);
    };

    useEffect(() => {
        if (isReload) {
            handleOnSearch();
        }
    }, [isReload]);

    useEffect(() => {
        textInput.current?.focus()
    }, [])

    useFocusEffect(
        useCallback(() => {
            if (hasValidPartnerEco(dataFromPartner)) {
                dispatch(resetDataFromPartner())
            }
        }, [navigation])
    );
    function useKeyboardVerticalOffset() {
        const { top } = useSafeAreaInsets()
        if (!Platform.OS) return 0
        return top + 10
    }

    const keyboardVerticalOffset = useKeyboardVerticalOffset()


    return (
        <KeyboardAvoidingView keyboardVerticalOffset={keyboardVerticalOffset} behavior={Platform.OS === 'ios' ? 'padding' : "height"} style={{ flex: 1, backgroundColor: COLORS.bgFFFFFF }}>
            <View style={{ flexDirection: 'row', margin: 10 }}>
                {/* <SearchBar
                    value={filter.keyword}
                    placeholder="Nhập Mã phiếu thẩm định/NV tạo/Tên KH/SĐT"
                    onChangeText={(text) => {
                        setFilter((prev) => ({
                            ...prev,
                            keyword: text
                        }));
                    }}
                    onSearch={handleOnSearch}
                    onDelete={() => {
                        setFilter((prev) => ({
                            ...prev,
                            keyword: ''
                        }));
                    }}
                    disabled={stateSearch.isFetching}
                    hasBarcodeScanner
                /> */}
                <HOOKSearchInput
                    style={{ flex: 9 }}
                    ref={textInput}
                    value={filter.keyword}
                    onFocus={() => { }}
                    onChangeText={(text) => {
                        setFilter((prev) => ({
                            ...prev,
                            keyword: text
                        }));
                    }}
                    onClearText={() => {
                        setFilter((prev) => ({
                            ...prev,
                            keyword: ""
                        }));
                        textInput.current?.focus()
                    }}
                    onSubmitEditing={handleSubmitEditing}
                    placeholder="Nhập Mã phiếu thẩm định/NV tạo/Tên KH/SĐT"
                    showBarcode={true}
                    onPressBarcode={() => {
                        setIsVisibleScan(true)
                        textInput.current?.blur()
                    }}
                />

                <TouchableOpacity
                    style={{
                        marginRight: 5,
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginLeft: 10,
                        flex: 1
                    }}
                    onPress={() => {
                        setIsShowModal(true);
                        textInput.current?.blur()
                    }}
                    activeOpacity={0.7}>
                    <Icon
                        iconSet="FontAwesome"
                        name="filter"
                        style={{
                            color: COLORS.ic2C8BD7,
                            fontSize: 18
                        }}
                    />
                </TouchableOpacity>
            </View>
            <BaseLoading
                isLoading={stateSearch.isFetching}
                isError={stateSearch.isError}
                isEmpty={stateSearch.isEmpty}
                textLoadingError={stateSearch.description}
                onPressTryAgains={handleOnSearch}
                content={
                    <View>
                        <FlatList
                            data={stateSearch.data}
                            removeClippedSubviews={false}
                            renderItem={({ item }) => (
                                <ExchangeReceiptItem
                                    data={item}
                                    handleRemoveItem={(id) => {
                                        Alert.alert(
                                            'Thông báo',
                                            'Bạn có chắc chắn muốn xóa?',
                                            [
                                                {
                                                    text: 'Bỏ qua',
                                                    onPress: hideBlockUI
                                                },
                                                {
                                                    text: 'Đồng ý',
                                                    onPress: () =>
                                                        deleteReceiptItem(id)
                                                }
                                            ]
                                        );
                                    }}
                                    handlePrintReceipt={(id) => {
                                        getBase64Reprint(id);
                                    }}
                                />
                            )}
                        />
                    </View>
                }
            />
            <View
                style={{
                    backgroundColor: COLORS.bgFFFFFF,
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginBottom: 30
                }}>
                <Button
                    text="Tạo phiếu thẩm định máy cũ"
                    styleContainer={{
                        alignSelf: 'center',
                        backgroundColor: COLORS.bg147EFB,
                        borderRadius: constants.getSize(10),
                        height: constants.getSize(40),
                        marginVertical: 5,
                        width: constants.getSize(210)
                    }}
                    styleText={{
                        color: COLORS.txtFFFFFF,
                        fontSize: 14
                    }}
                    onPress={() => {
                        setStateSearch({
                            isFetching: false,
                            isError: false,
                            description: '',
                            isEmpty: false,
                            data: []
                        });
                        if (hasValidPartnerEco(dataFromPartner)) {
                            dispatch(resetDataFromPartner())
                        }
                        navigation.navigate('ProgramScreen');
                    }}
                />
            </View>
            <ModalFilterSearch
                isVisible={isShowModal}
                hideModal={() => {
                    setIsShowModal(false);
                }}
                filter={filter}
                setFilter={setFilter}
                onSearch={handleOnSearch}
            />
            <BarcodeCamera
                isVisible={isVisibleScan}
                closeCamera={() => {
                    setIsVisibleScan(false);
                }}
                resultScanBarcode={(barcode) => {
                    handleBarcode(barcode)
                }}
            />
        </KeyboardAvoidingView>
    );
};

export default ProductEvaluation;
