import { productEvaluationState } from './state';
import { actions } from './actions';

export const productEvaluationReducer = (
    state = productEvaluationState,
    action
) => {
    const { type, payload } = action;
    const { returnProducts } = state;
    switch (type) {
        case actions.SET_EXCHANGE_PROGRAM:
            return {
                ...productEvaluationState,
                exchangeProgram: payload,
                dataFromPartner: state.dataFromPartner
            };
        case actions.ADD_RETURN_PRODUCT: {
            const newReturnProducts = [...returnProducts];
            newReturnProducts.push(payload);
            return {
                ...state,
                returnProducts: newReturnProducts
            };
        }
        case actions.DELETE_RETURN_PRODUCT:
            return {
                ...state,
                returnProducts: returnProducts.filter(
                    ({ IMEI }) => !payload.has(IMEI)
                )
            };
        case actions.UPDATE_PRODUCT_STATES: {
            const { IMEI, states, StateGroupID } = payload;
            const newReturnProducts = [...returnProducts];
            const index = newReturnProducts.findIndex(
                (item) => item.IMEI === IMEI
            );
            if (index !== -1) {
                newReturnProducts[index].states = states;
                newReturnProducts[index].StateGroupID = StateGroupID;
            }
            return {
                ...state,
                returnProducts: newReturnProducts
            };
        }
        case actions.SET_DATA_FROM_PARTNER:
            return {
                ...state,
                dataFromPartner: payload
            }
        case actions.RESET_DATA_FROM_PARTNER:
            return {
                ...state,
                dataFromPartner: {}
            }
        default:
            return state;
    }
};
