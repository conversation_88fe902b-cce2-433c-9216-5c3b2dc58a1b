import { API_CONST } from "@constants";
import { helper, dateHelper } from "@common";
import { apiBase, METHOD, ERROR, EMPTY, SUCCESS } from "@config";
import { translate } from "@translate";

const {
    API_GET_SERVICE_LIST,
    API_VALIDATE_DATA_SERVICE_REQUEST,
    API_GET_PRICE_SERVICE,
    API_GET_CREATE_SERVICE_REQUEST,
    API_GET_DATA_COLLECTION_MANAGER_NEW,
    API_GET_PROMOTION_SERVICE,
    API_GET_INFO_REFUND,
    API_CREATE_AIRTIME_REFUND,
    API_CHECK_STATUS_TICKET_SERVICE,
    API_GET_SERVICE_LIST_HISTORY_REFUND,
    API_GET_PROCESSOUT_VOUCHER,
    API_GET_CANCEL_AND_CREATE_AIRTIME,
    API_GET_QUERY_STATUS,
    API_GET_DATA_INFO,
    API_VALIDATE_IMEI,
    API_CREATE_AIRTIME_EDIT_REQUEST,
    API_QUERY_STATUS_EDIT_TRANSACTION,
    API_SEARCH_AIRTIME_EDIT_REQUEST,
    API_VALIDATE_EDIT_DATA,
    API_GET_TRANSACTION_DETAIL,
    API_GET_SEARCH_SO,
    API_GET_DETAIL_SO,
    API_GET_PROVINCE,
    API_GET_DISTRICT,
    API_GET_WARD,
    API_GET_INPUT_FORM_TEMPLATE,
    API_GET_HEALTH_INSURANCE_FEE,
    API_CREATE_SERVICE_REQUEST,
    API_GET_PRINT_SERVICE_REPORT,
    API_GET_PROCESS_SERVICE_REQUEST,
    API_GET_CASH_RETURN_DETAIL
} = API_CONST;

const START_GET_SERVICE_LIST = "START_GET_SERVICE_LIST";
const STOP_GET_SERVICE_LIST = "STOP_GET_SERVICE_LIST";
const START_VALIDATE_DATA_SERVICE_REQUEST = "START_VALIDATE_DATA_SERVICE_REQUEST";
const STOP_VALIDATE_DATA_SERVICE_REQUEST = "STOP_VALIDATE_DATA_SERVICE_REQUEST"
const START_ADD_TO_SALE_ORDER_CART = "START_ADD_TO_SALE_ORDER_CART";
const STOP_ADD_TO_SALE_ORDER_CART = "STOP_ADD_TO_SALE_ORDER_CART";
const CLEAR_DATA_VALIDATE_SERVICE_REQUEST = 'CLEAR_DATA_VALIDATE_SERVICE_REQUEST';
const START_SEARCH_HISTORY_INSURANCE = "START_SEARCH_HISTORY_INSURANCE";
const STOP_SEARCH_HISTORY_INSURANCE = "STOP_SEARCH_HISTORY_INSURANCE";
const START_GET_CREATE_AIRTIME_REFUND = "START_GET_CREATE_AIRTIME_REFUND";
const STOP_GET_CREATE_AIRTIME_REFUND = "STOP_GET_CREATE_AIRTIME_REFUND";
const START_GET_QUERY_STATUS_EDIT_EMEI_SERVICE_REQUEST = 'START_GET_QUERY_STATUS_EDIT_EMEI_SERVICE_REQUEST';
const STOP_GET_QUERY_STATUS_EDIT_EMEI_SERVICE_REQUEST = 'STOP_GET_QUERY_STATUS_EDIT_EMEI_SERVICE_REQUEST';
const START_SEARCH_HISTORY_EDIT_EMEI = 'START_SEARCH_HISTORY_EDIT_EMEI';
const STOP_SEARCH_HISTORY_EDIT_EMEI = 'STOP_SEARCH_HISTORY_EDIT_EMEI';
const START_GET_DATA_INFO = 'START_GET_DATA_INFO';
const STOP_GET_DATA_INFO = 'STOP_GET_DATA_INFO';
const UPDATE_HEADER_AIRTIME = 'UPDATE_HEADER_AIRTIME';
const CLEAR_DATA_INFO = 'CLEAR_DATA_INFO';
const START_GET_SEARCH_SO = 'START_GET_SEARCH_SO';
const STOP_GET_SEARCH_SO = "STOP_GET_SEARCH_SO";
const CLEAR_DATA_SEARCH_SO = "CLEAR_DATA_SEARCH_SO";
const START_GET_DETAIL_SO = "START_GET_DETAIL_SO";
const STOP_GET_DETAIL_SO = "STOP_GET_DETAIL_SO";
const START_GET_PROVINCE = "START_GET_PROVINCE";
const STOP_GET_PROVINCE = "STOP_GET_PROVINCE";
const START_GET_DISTRICT = "START_GET_DISTRICT";
const STOP_GET_DISTRICT = "STOP_GET_DISTRICT";
const START_GET_PROMOTION_SERVICE = "START_GET_PROMOTION_SERVICE";
const STOP_GET_PROMOTION_SERVICE = "STOP_GET_PROMOTION_SERVICE";
const START_GET_PRICE_SERVICE = "START_GET_PRICE_SERVICE";
const STOP_GET_PRICE_SERVICE = "STOP_GET_PRICE_SERVICE";
//
const START_GET_LIST_HEALTH_INSURANCE_PARTNER = "START_GET_LIST_HEALTH_INSURANCE_PARTNER";
const STOP_GET_LIST_HEALTH_INSURANCE_PARTNER = "STOP_GET_LIST_HEALTH_INSURANCE_PARTNER";
const SET_HEALTH_INSURANCE_PARTNER_INFO = "SET_HEALTH_INSURANCE_PARTNER_INFO";
const SET_INPUT_FORM_TEMPLATE = "SET_INPUT_FORM_TEMPLATE";
const START_GET_HEALTH_INSURANCE_FEE = "START_GET_HEALTH_INSURANCE_FEE";
const STOP_GET_HEALTH_INSURANCE_FEE = "STOP_GET_HEALTH_INSURANCE_FEE";
const UPDATE_LOCAL_SIDE_STATE = "UPDATE_LOCAL_SIDE_STATE";
const SET_HEALTH_INSURANCE_SALE_ORDER_ID = "SET_HEALTH_INSURANCE_SALE_ORDER_ID";
const UPDATE_ITEM_DETAIL_ORDER_DETAIL = 'UPDATE_ITEM_DETAIL_ORDER_DETAIL';
const START_GET_SEND_OTP_PROCESS = "START_GET_SEND_OTP_PROCESS";
const STOP_GET_SEND_OTP_PROCESS = "STOP_GET_SEND_OTP_PROCESS";

export const actionInsuranceAirtimeService = {
    START_GET_SERVICE_LIST,
    STOP_GET_SERVICE_LIST,
    START_ADD_TO_SALE_ORDER_CART,
    STOP_ADD_TO_SALE_ORDER_CART,
    START_VALIDATE_DATA_SERVICE_REQUEST,
    STOP_VALIDATE_DATA_SERVICE_REQUEST,
    CLEAR_DATA_VALIDATE_SERVICE_REQUEST,
    START_SEARCH_HISTORY_INSURANCE,
    STOP_SEARCH_HISTORY_INSURANCE,
    START_GET_CREATE_AIRTIME_REFUND,
    STOP_GET_CREATE_AIRTIME_REFUND,
    START_GET_QUERY_STATUS_EDIT_EMEI_SERVICE_REQUEST,
    STOP_GET_QUERY_STATUS_EDIT_EMEI_SERVICE_REQUEST,
    START_SEARCH_HISTORY_EDIT_EMEI,
    STOP_SEARCH_HISTORY_EDIT_EMEI,
    START_GET_DATA_INFO,
    STOP_GET_DATA_INFO,
    UPDATE_HEADER_AIRTIME,
    CLEAR_DATA_INFO,
    START_GET_SEARCH_SO,
    STOP_GET_SEARCH_SO,
    CLEAR_DATA_SEARCH_SO,
    START_GET_DETAIL_SO,
    STOP_GET_DETAIL_SO,
    START_GET_PROVINCE,
    STOP_GET_PROVINCE,
    START_GET_DISTRICT,
    STOP_GET_DISTRICT,
    START_GET_PROMOTION_SERVICE,
    STOP_GET_PROMOTION_SERVICE,
    START_GET_PRICE_SERVICE,
    STOP_GET_PRICE_SERVICE,
    START_GET_LIST_HEALTH_INSURANCE_PARTNER,
    STOP_GET_LIST_HEALTH_INSURANCE_PARTNER,
    SET_HEALTH_INSURANCE_PARTNER_INFO,
    SET_INPUT_FORM_TEMPLATE,
    START_GET_HEALTH_INSURANCE_FEE,
    STOP_GET_HEALTH_INSURANCE_FEE,
    UPDATE_LOCAL_SIDE_STATE,
    SET_HEALTH_INSURANCE_SALE_ORDER_ID,
    UPDATE_ITEM_DETAIL_ORDER_DETAIL,
    START_GET_SEND_OTP_PROCESS,
    STOP_GET_SEND_OTP_PROCESS,
};

export const getServiceList = function (data) {
    return (dispatch, getState) => {
        const body = {
            loginStoreId: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            moduleID: getState().userReducer.moduleID,
            catalogID: data.catalogID,
            serviceGroupID: data.serviceGroupID,
            isSearchByQuery: data.isSearchByQuery,
            isLoadSearchBy: data.isLoadSearchBy
        }
        if (data.catalogID == 3 && data.serviceGroupID == 30) {
            body.isLoadSearchBy = false;
            body.isSearchByEdit = false;
        }
        dispatch(start_get_service_list())
        apiBase(API_GET_SERVICE_LIST, METHOD.POST, body)
            .then((response) => {
                console.log("getServiceList success", response);
                if (helper.IsNonEmptyArray(response.object)) {
                    dispatch(stop_get_service_list(response.object, false, '', false));
                } else {
                    dispatch(stop_get_service_list([], true, 'Không lấy được dữ liệu', false));
                }
            }).catch(error => {
                dispatch(stop_get_service_list([], false, error.msgError, true))
                console.log("getServiceList error", error);
            })
    }
};

export const validateDataServiceRequest = function (data) {
    return (dispatch, getState) => {
        const body = {
            loginStoreId: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            moduleID: getState().userReducer.moduleID,
            catalogID: data.catalogID,
            serviceGroupID: data.serviceGroupID,
            airtimeTransactionTypeID: data.airtimeTransactionTypeID,
            isLoadProductInfo: 1,
            groupBys: "ProductID,ProductName",
            insuranceID: data.insuranceID,
            insCustomerID: data.insCustomerID,
            imei: data.imei,
            keyword: data.keyword,
            isCheckDetail: data.isCheckDetail
        }
        if(data.isCheckDetail){
           body.validateType = data.validateType,
           body.FromDate = dateHelper.formatDateYYYYMMDD(data.fromDate) + "T00:00:00",
           body.ToDate= dateHelper.formatDateYYYYMMDD(data.toDate) + "T23:59:59"
        }
        dispatch(start_validate_data_service_request())
        apiBase(API_VALIDATE_DATA_SERVICE_REQUEST, METHOD.POST, body)
            .then((response) => {
                console.log("validateDataServiceRequest success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    dispatch(stop_validate_data_service_request(response.object, false, '', false));
                } else {
                    dispatch(stop_validate_data_service_request({}, true, 'Không lấy được dữ liệu', false));
                }
            }).catch(error => {
                dispatch(stop_validate_data_service_request({}, false, error.msgError, true))
                console.log("validateDataServiceRequest error", error);
            })
    }
};

export const getPromotionService = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                productID: data.productID,
                phoneNumber: data.phoneNumber,
                insProgramID: data.insProgramID,
                mainProductID: data.mainProductID,
            };
            dispatch(start_get_promotion_service());
            apiBase(API_GET_PROMOTION_SERVICE, METHOD.POST, body)
                .then((response) => {
                    console.log("getPromotionService success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        dispatch(stop_get_promotion_service(response.object));
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("getPromotionService error", error);
                    reject(error.msgError);
                });
        });
    };
};

export const getPriceService = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                productID: data.productID,
                retailPriceVAT: data.retailPriceVAT,
                outputDate: data.outputDate,
                insuranceMonth: data.insuranceMonth,
                imei: data.imei,
                mainProductID: data.mainProductID,
                productPromotion: data.productPromotion,
                promotionID: data.promotionID,
                mainSaleOrderID: data.mainSaleOrderID,
                extraData: data.extraData,
                insuranceApplyID: data.insuranceApplyID,
                termLoan: data.termLoan,
                loanAmount: data.loanAmount,
                insMoneyScreen: data.insMoneyScreen
            };
            dispatch(start_get_price_service())
            apiBase(API_GET_PRICE_SERVICE, METHOD.POST, body)
                .then((response) => {
                    console.log("getPriceService success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        dispatch(stop_get_price_service(response.object));
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("getPriceService error", error);
                    reject(error.msgError);
                });
        });
    };
};

export const addToSaleOrderCart = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                airTimeTransactionBO: data.airTimeTransactionBO,
                insuranceID: data.insuranceID,
                imei: data.imei,
                insCustomerID: data.insCustomerID,
                insProgramID: data.insProgramID,
                insuranceMonth: data.insuranceMonth,
                insuranceApplyID: data.insuranceApplyID,
                promotionID: data.promotionID,
                productPromotion: data.productPromotion,
                partnerData: data.partnerData,
                retailPriceVAT: data.retailPriceVAT,
                mainSaleOrderID: data.mainSaleOrderID,
                mainProductID: data.mainProductID,
                mainSaleOrderDetailID: data.mainSaleOrderDetailID,
                districtID: data.districtID,
                provinceID: data.provinceID,
                wardID: data.wardID,
                apartmentNumberAddress: data.apartmentNumberAddress,
                profileID: data.profileID,
                startDate: data.startDate,
                termLoan: data.termLoan,
                totalPrePaid: data.totalPrePaid,
                voucherConcern: data.voucherConcern,
                paymentAmountMonthly: data.paymentAmountMonthly,
                customerBirthday: data.customerBirthday,
                endDate: data.endDate,
                customerIDCard: data.customerIDCard,
                loanAmount: data.loanAmount,
                promotionGift: data.promotionGift,
                insMoneyScreen: data.insMoneyScreen,
            };
            dispatch(start_add_to_sale_order_cart());
            apiBase(API_GET_CREATE_SERVICE_REQUEST, METHOD.POST, body)
                .then((response) => {
                    console.log("addToSaleOrderCart success", response);
                    const { object } = response;
                    if (!helper.IsEmptyObject(object)) {
                        dispatch(stop_add_to_sale_order_cart(object));
                        resolve(response);
                    } else {
                        dispatch(stop_add_to_sale_order_cart({}));
                        reject({ msgError: translate("saleOrder.error_create_order") });
                    }
                })
                .catch((error) => {
                    dispatch(stop_add_to_sale_order_cart(!SUCCESS, {}));
                    const { msgError } = error;
                    console.log("addToSaleOrderCart error", error);
                    reject(msgError);
                });
        });
    };
};

export const getSearchHistoryInsurance = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                Keyword: data.keyword.length > 0 ? data.keyword : '',
                ProcessUser: getState().userReducer.userName,
                FromDate: dateHelper.formatDateYYYYMMDD(data.fromDate) + "T00:00:00",
                ToDate: dateHelper.formatDateYYYYMMDD(data.toDate) + "T23:59:59",
                IsDeleted: data.isDelete ? true : false,
                airtimetrsTypeIDList: data.airtimetrsTypeIDList ? data.airtimetrsTypeIDList : '',
                searchType: data.searchType ? data.searchType : ''
            }
            dispatch(start_search_history_insurance())
            apiBase(API_GET_DATA_COLLECTION_MANAGER_NEW, METHOD.POST, body)
                .then((response) => {
                    console.log("getSearchHistoryInsurance BODY", response.object);
                    if (
                        helper.hasProperty(response, 'object') &&
                        helper.isArray(response.object) &&
                        response.object.length > 0
                    ) {
                        console.log("getSearchHistoryInsurance success", response);
                        dispatch(stop_search_history_insurance(response.object));
                        resolve(response.object);
                    } else {
                        dispatch(stop_search_history_insurance([], false, 'Không tìm thấy đơn hàng', true));
                        reject("Lỗi lấy thông tin dữ liệu");
                    }
                })
                .catch((error) => {
                    console.log('getSearchHistoryInsurance err', error);
                    dispatch(stop_search_history_insurance([], !EMPTY, error.msgError, ERROR));
                    reject("Lỗi lấy thông tin dữ liệu");
                });
        })
    }
}

export const getServiceListHistory = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                isLoadSearchBy: data.isLoadSearchBy,
                isSearchByEdit: data.isSearchByEdit,
                isSearchByQuery: data.isSearchByQuery,
            }
            apiBase(API_GET_SERVICE_LIST, METHOD.POST, body)
                .then((response) => {
                    console.log("getServiceListHistory success", response);
                    if (helper.IsNonEmptyArray(response.object)) {
                        resolve(response.object)
                    } else {
                        reject('Không lấy được dữ liệu')
                    }
                }).catch(error => {
                    reject(error.msgError)
                    console.log("getServiceListHistory error", error);
                })
        }
        )

    }
};

export const getInfoRefund = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                productID: data.productID,
                inputTime: data.inputTime,
                serviceVoucherID: data.serviceVoucherID
            };
            apiBase(API_GET_INFO_REFUND, METHOD.POST, body)
                .then((response) => {
                    console.log("getInfoRefund success", response);
                    if (!helper.IsEmptyObject(response)) {
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("getInfoRefund error", error);
                    reject(error.msgError);
                });
        });
    };
};

export const createAirtimeRefund = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                airtimeTransactionID: data.airtimeTransactionID,
                totalRefundAmount: data.totalRefundAmount,
                rqRefundTypeID: data.rqRefundTypeID,
                reasonID: data.reasonID,
                refundNote: data.refundNote,
                approvedUser: data.approvedUser,
                approvedDate: data.approvedDate
            };
            dispatch(start_get_create_airtime_refund());
            apiBase(API_CREATE_AIRTIME_REFUND, METHOD.POST, body)
                .then((response) => {
                    console.log("createAirtimeRefund success", response);
                    if (!helper.IsEmptyObject(response)) {
                        resolve(response.object);
                        dispatch(stop_get_create_airtime_refund(response.object, false, '', false));
                    }
                })
                .catch((error) => {
                    console.log("createAirtimeRefund error", error);
                    dispatch(stop_get_create_airtime_refund({}, false, error.msgError, true))
                    reject(error.msgError);
                });
        });
    };
};

export const checksSatusTicketService = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                ticketID: data.TICKETID,
                airtimeTransactionID: data.AIRTIMETRANSACTIONID
            };
            apiBase(API_CHECK_STATUS_TICKET_SERVICE, METHOD.POST, body).then((response) => {
                console.log("checksSatusTicketService success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                }
                else {
                    resolve("Lỗi lấy thông tin dữ liệu");
                }
            }).catch(error => {
                console.log("checksSatusTicketService error", error);
                reject(error.msgError)
            })
        });
    }
}

export const getQuerysTatusServiceRequest = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                airtimeTransactionID: data.airtimeTransactionID,
                airtimeTransactionRFID: data.airtimeTransactionRFID
            };
            apiBase(API_GET_CANCEL_AND_CREATE_AIRTIME, METHOD.POST, body)
                .then((response) => {
                    console.log("getQuerysTatusServiceRequest success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("getQuerysTatusServiceRequest error", error);
                    reject(error.msgError);
                });
        });
    };
};

export const getServiceListHistoryRefund = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                Keyword: data.keyword.length > 0 ? data.keyword : '',
                ProcessUser: getState().userReducer.userName,
                FromDate: dateHelper.formatDateYYYYMMDD(data.fromDate) + "T00:00:00",
                ToDate: dateHelper.formatDateYYYYMMDD(data.toDate) + "T23:59:59",
                airtimetrsTypeIDList: data.airtimetrsTypeIDList ? data.airtimetrsTypeIDList : '',
                searchType: data.searchType ? data.searchType : '',
                approvalStatus: data.approvalStatus
            }
            dispatch(start_search_history_insurance())
            apiBase(API_GET_SERVICE_LIST_HISTORY_REFUND, METHOD.POST, body)
                .then((response) => {
                    console.log("getSearchHistoryInsurance BODY", response.object);
                    if (
                        helper.hasProperty(response, 'object') &&
                        helper.isArray(response.object) &&
                        response.object.length > 0
                    ) {
                        console.log("getSearchHistoryInsurance success", response);
                        dispatch(stop_search_history_insurance(response.object));
                        resolve(response.object);
                    } else {
                        dispatch(stop_search_history_insurance([], false, 'Không tìm thấy đơn hàng', true));
                        reject("Lỗi lấy thông tin dữ liệu");
                    }
                })
                .catch((error) => {
                    console.log('getSearchHistoryInsurance err', error);
                    dispatch(stop_search_history_insurance([], !EMPTY, error.msgError, ERROR));
                    reject("Lỗi lấy thông tin dữ liệu");
                });
        })
    }
}

export const getServiceListRefund = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                isLoadSearchBy: data.isLoadSearchBy,
                "isSearchByRefund": data.isLoadSearchBy,
            }
            apiBase(API_GET_SERVICE_LIST, METHOD.POST, body)
                .then((response) => {
                    console.log("getServiceListRefund success", response);
                    if (helper.IsNonEmptyArray(response.object)) {
                        resolve(response.object)
                    } else {
                        reject('Không lấy được dữ liệu')
                    }
                }).catch(error => {
                    reject(error.msgError)
                    console.log("getServiceListRefund error", error);
                })
        }
        )

    }
};
export const getStatusHistory = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                airtimeTransactionID: data.airtimeTransactionID,
                airtimeTransactionRFID: data.airtimeTransactionRFID
            }
            apiBase(API_GET_PROCESSOUT_VOUCHER, METHOD.POST, body)
                .then((response) => {
                    console.log('get statushistory success', response);
                    if (!helper.IsEmptyObject(response.object)) {
                        resolve(response.object);
                    }
                }).catch((error) => {
                    console.log('get statushistory error', error);
                    reject(error);
                })
        })
    }
}

export const queryTransactionPartner = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                productID: data.productID,
                saleOrderID: data.saleOrderID
            };
            apiBase(API_GET_QUERY_STATUS, METHOD.POST, body).then((response) => {
                console.log("queryTransactionPartner success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                }
                else {
                    reject("Lỗi lấy thông tin dữ liệu");
                }
            }).catch(error => {
                console.log("queryTransactionPartner error", error);
                reject(error.msgError)
            })
        });
    }
}

export const getDataInfo = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                serviceVoucherID: data.serviceVoucherID,
                saleOrderID: data.saleOrderID
            };
            apiBase(API_GET_DATA_INFO, METHOD.POST, body).then((response) => {
                console.log("getDataInfo success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                    dispatch(stop_get_data_info(response.object));
                }
                else {
                    reject("Lỗi lấy thông tin dữ liệu");
                    dispatch(stop_get_data_info({}, false, 'Không tìm thấy đơn hàng', true));
                }
            })
                .catch((error) => {
                    console.log("getDataInfo error", error);
                    dispatch(stop_get_data_info({}, !EMPTY, error.msgError, ERROR));
                    reject(error)
                });
        });
    };
};

export const validateImeiAirtimeService = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                airtimeTransactionID: data.airtimeTransactionID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                imei: data.imei
            };
            apiBase(API_VALIDATE_IMEI, METHOD.POST, body)
                .then((response) => {
                    console.log("validateImeiAirtimeService success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("validateImeiAirtimeService error", error);
                    reject(error.msgError);
                });
        });
    };
};

export const createAirtimeEditRequest = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                airtimeTransactionID: data.airtimeTransactionID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                imei: data.imei
            };
            dispatch(start_get_query_status_edit_emei_service_request())
            apiBase(API_CREATE_AIRTIME_EDIT_REQUEST, METHOD.POST, body)
                .then((response) => {
                    console.log("createAirtimeEditRequest success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        resolve(response.object);
                        dispatch(stop_get_query_status_edit_emei_service_request(response.object));
                    }
                })
                .catch((error) => {
                    console.log("createAirtimeEditRequest error", error);
                    reject(error.msgError);
                    dispatch(stop_get_query_status_edit_emei_service_request({}, !EMPTY, error.msgError, ERROR));
                });
        });
    };
};

export const getQuerysTatusEditEmeiServiceRequest = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                airtimeTransactionID: data.airtimeTransactionID,
                editRequestID: data.editRequestID
            };
            apiBase(API_QUERY_STATUS_EDIT_TRANSACTION, METHOD.POST, body)
                .then((response) => {
                    console.log("getQuerysTatusEditEmeiServiceRequest success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("getQuerysTatusEditEmeiServiceRequest error", error);
                    reject(error.msgError);

                });
        });
    };
};

export const getSearchHistoryEditEmei = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                Keyword: data.keyword.length > 0 ? data.keyword : '',
                ProcessUser: getState().userReducer.userName,
                FromDate: dateHelper.formatDateYYYYMMDD(data.fromDate) + "T00:00:00",
                ToDate: dateHelper.formatDateYYYYMMDD(data.toDate) + "T23:59:59",
                IsDeleted: false,
                airtimetrsTypeIDList: data.airtimetrsTypeIDList ? data.airtimetrsTypeIDList : '',
                searchType: data.searchType ? data.searchType : ''
            }
            dispatch(start_search_history_edit_emei())
            apiBase(API_SEARCH_AIRTIME_EDIT_REQUEST, METHOD.POST, body)
                .then((response) => {
                    console.log("getSearchHistoryEditEmei BODY", response.object);
                    if (
                        helper.hasProperty(response, 'object') &&
                        helper.isArray(response.object) &&
                        response.object.length > 0
                    ) {
                        console.log("getSearchHistoryEditEmei success", response);
                        dispatch(stop_search_history_edit_emei(response.object));
                        resolve(response.object);
                    } else {
                        dispatch(stop_search_history_edit_emei([], false, 'Không tìm thấy đơn hàng', true));
                        reject("Lỗi lấy thông tin dữ liệu");
                    }
                })
                .catch((error) => {
                    console.log('getSearchHistoryEditEmei err', error);
                    dispatch(stop_search_history_edit_emei([], !EMPTY, error.msgError, ERROR));
                    reject("Lỗi lấy thông tin dữ liệu");
                });
        })
    }
}

export const getDataTransactionDetail = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                serviceVoucherID: data.serviceVoucherID,
                airtimeTransactionID: data.airtimeTransactionID
            };
            apiBase(API_GET_TRANSACTION_DETAIL, METHOD.POST, body)
                .then((response) => {
                    console.log("getDataTransactionDetail success", response);
                    if (!helper.IsEmptyObject(response)) {
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("getDataTransactionDetail error", error);
                    reject(error.msgError);
                });
        });
    };
};

export const validateEditData = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                airtimeTransactionID: data.airtimeTransactionID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID
            };
            apiBase(API_VALIDATE_EDIT_DATA, METHOD.POST, body)
                .then((response) => {
                    console.log("validateEditData success", response);
                    if (!helper.IsEmptyObject(response)) {
                        resolve(response);
                    }
                })
                .catch((error) => {
                    console.log("validateEditData error", error);
                    reject(error.msgError);
                });
        });
    };
};

export const getDataSearchSO = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                validateType: data.validateType,
                searchType: data.searchType ? data.searchType : '',
                PartnerInstallID: data.PartnerInstallID,
                keyword: data.keyword.length > 0 ? data.keyword : '',
                fromDate: dateHelper.formatDateYYYYMMDD(data.fromDate) + "T00:00:00",
                toDate: dateHelper.formatDateYYYYMMDD(data.toDate) + "T23:59:59",
                extraData: data.extraData
            }
            dispatch(start_search_so())
            apiBase(API_GET_SEARCH_SO, METHOD.POST, body)
                .then((response) => {
                    console.log('getDataSearchSO BODY', response?.object?.SOINFO);
                    if (
                        helper.IsNonEmptyArray(response?.object?.SOINFO)
                    ) {
                        console.log('getDataSearchSO success', response);
                        dispatch(stop_search_so(response?.object?.SOINFO));
                        resolve(response?.object?.SOINFO);
                    } else {
                        dispatch(stop_search_so([], false, 'Không tìm thấy đơn hàng', true));
                        reject("Lỗi lấy thông tin dữ liệu")
                    }
                })
                .catch((error) => {
                    console.log('getDataSearchSO error: ', error);
                    dispatch(stop_search_so([], !EMPTY, error.msgError, ERROR))
                    reject("Lỗi lấy thông tin dữ liệu")
                })
        })
    }
}

export const getDetailSO = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                validateType: data.validateType,
                mainSaleOrderID: data.mainSaleOrderID,
                extraData: data.extraData,
                keyword: data.keyword,
                fromDate: dateHelper.formatDateYYYYMMDD(data.fromDate) + "T00:00:00",
                toDate: dateHelper.formatDateYYYYMMDD(data.toDate) + "T23:59:59",
                imei: data.imei,
            }
            dispatch(start_get_detail_so())
            apiBase(API_GET_DETAIL_SO, METHOD.POST, body)
                .then((response) => {
                    if (
                        !helper.IsEmptyObject(response?.object)
                    ) {
                        console.log('getDetailSO success', response);
                        dispatch(stop_get_detail_so(response.object));
                        resolve(response.object);
                    } else {
                        dispatch(stop_get_detail_so({}, false, 'Không tìm thấy đơn hàng', true));
                        reject("Lỗi lấy thông tin dữ liệu")
                    }
                })
                .catch((error) => {
                    console.log('getDetailSO error: ', error);
                    dispatch(stop_search_so({}, !EMPTY, error.msgError, ERROR))
                    reject("Lỗi lấy thông tin dữ liệu")
                })
        })
    }
}

export const geDataBuyInsurance = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                validateType: "CHECKBUY",
                extraData: data.extraData
            }
            dispatch(start_validate_data_service_request())
            apiBase(API_GET_DETAIL_SO, METHOD.POST, body)
                .then((response) => {
                    console.log("geDataBuyInsurance success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        dispatch(stop_validate_data_service_request(response.object));
                        resolve(response.object);
                    }
                }).catch(error => {
                    console.log("geDataBuyInsurance error", error.msgError);
                    dispatch(stop_validate_data_service_request({}, !EMPTY, error.msgError, ERROR))
                    reject(error.msgError)
                })
        })
    }
}

export const getDataProvince = function () {
    return function (dispatch, getState) {
        let body = {
            "loginStoreId": getState().userReducer.storeID,
            "languageID": getState().userReducer.languageID,
            "moduleID": getState().userReducer.moduleID,
            "keyWord": "",
            "top": 64
        };
        dispatch(start_get_province());
        apiBase(API_GET_PROVINCE, METHOD.POST, body).then((response) => {
            console.log("getDataProvince success", response);
            const { object } = response;
            if (helper.isArray(object) && object.length > 0) {
                dispatch(stop_get_province(object));
            }
            else {
                dispatch(stop_get_province([], !EMPTY, translate("provincemain.mess_error"), ERROR));
            }
        }).catch(error => {
            console.log("getDataProvince error", error);
            dispatch(stop_get_province([], !EMPTY, error.msgError, ERROR));
        })
    }
}

export const getDataDistrict = function (provinceID) {
    return function (dispatch, getState) {
        let body = {
            "loginStoreId": getState().userReducer.storeID,
            "languageID": getState().userReducer.languageID,
            "moduleID": getState().userReducer.moduleID,
            "keyWord": "",
            "provinceID": provinceID,
            "top": 64
        };
        dispatch(start_get_district());
        apiBase(API_GET_DISTRICT, METHOD.POST, body).then((response) => {
            console.log("getDataDistrict success", response);
            const { object } = response;
            if (helper.isArray(object) && object.length > 0) {
                dispatch(stop_get_district(object));
            }
            else {
                dispatch(stop_get_district([], !EMPTY, translate("provincemain.mess_error1"), ERROR));
            }
        }).catch(error => {
            console.log("getDataDistrict error", error);
            dispatch(stop_get_district([], !EMPTY, error.msgError, ERROR));
        })
    }
}

export const getDistrict = function (provinceID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                "keyWord": "",
                "provinceID": provinceID,
                "top": 64
            };
            apiBase(API_GET_DISTRICT, METHOD.POST, body).then((response) => {
                console.log("getDistrict success", response);
                const { object } = response;
                if (helper.isArray(object) && object.length > 0) {
                    resolve(object)
                }
                else {
                    reject(translate("provincemain.mess_error1"));
                }
            }).catch(error => {
                console.log("getDistrict error", error);
                reject(error.msgError);
            })
        })
    };
}

export const getWard = function (provinceID, districtID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                "provinceID": provinceID,
                "districtID": districtID,
                "top": 64
            };
            apiBase(API_GET_WARD, METHOD.POST, body).then((response) => {
                console.log("getWard success", response);
                const { object } = response;
                if (helper.isArray(object) && object.length > 0) {
                    resolve(object)
                }
                else {
                    reject(translate("provincemain.mess_error2"));
                }
            }).catch(error => {
                console.log("getWard error", error);
                reject(error.msgError);
            })
        })
    };
}

export const getAllLocation = function (provinceID, districtID) {
    return new Promise((resolve, reject) => {
        console.log("getAllLocation");
        const allPromise = [
            getDistrict(provinceID),
            getWard(provinceID, districtID)
        ];
        Promise.all(allPromise).then(result => {
            console.log("getAllLocation success", result);
            resolve({
                dataDistrict: result[0],
                dataWard: result[1]
            })
        }).catch(msgError => {
            console.log("getAllLocation error", msgError);
            reject(msgError);
        })
    })
}

export const setHealthInsurancePartnerInfo = (partnerInfo) => {
    return (dispatch, getState) => {
        dispatch(set_health_insurance_partner_info(partnerInfo))
    }
}

export const getInputFormTemplate = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: data.catalogId,
                serviceGroupID: data.serviceGroupId,
                airtimeTransactionTypeID: data.airtimeTransactionTypeId,
                insCustomerID: data.partnerId
            }
            // const insuranceProperty = FORM_TEMPLATE_MOCK_DATA.filter(formGroup => formGroup.Step == 1);
            // const customerProperty = FORM_TEMPLATE_MOCK_DATA.filter(formGroup => formGroup.Step == 2);
            // dispatch(set_input_form_template({ insuranceProperty, customerProperty }))
            // resolve({ insuranceProperty, customerProperty });

            dispatch(stop_get_health_insurance_fee({}))
            dispatch(updateLocalSideState({
                isInvalidHealthStatus: false,
                isSelectedYesOnSecondHealthStatusSubQuestion: false,
                isSelectedNoOnSecondHealthStatusQuestion: false,
                extraPolicyFee: [],
                isResign: false,
                isBoughtForChildren: false
            }));
            apiBase(API_GET_INPUT_FORM_TEMPLATE, METHOD.POST, body)
                .then((response) => {
                    console.log("getInputFormTemplate success", response);
                    const { object } = response;
                    if (helper.IsNonEmptyArray(object)) {
                        const insuranceProperty = object.filter(formGroup => formGroup.Step == 1);
                        const customerProperty = object.filter(formGroup => formGroup.Step == 2);
                        dispatch(set_input_form_template({ insuranceProperty, customerProperty }))
                        resolve({ insuranceProperty, customerProperty })
                    } else {
                        reject('Không có thông tin nhập liệu đã khai báo!')
                    }
                }).catch(error => {
                    console.log("getInputFormTemplate error", error);
                    reject(error.msgError)
                })
        })
    }
};

export const getHealthInsuranceFee = function (data) {
    return (dispatch, getState) => {
        dispatch(start_get_health_insurance_fee())
        const { catalogId, serviceGroupId, airtimeTransactionTypeId, productPromotion, listProperty, extraPolicyFee } = data
        const body = {
            loginStoreId: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            moduleID: getState().userReducer.moduleID,
            catalogID: catalogId,
            serviceGroupID: serviceGroupId,
            airtimeTransactionTypeID: airtimeTransactionTypeId,
            productPromotion: productPromotion,
            extraData: {
                "ListProperty": listProperty
            }
        }
        apiBase(API_GET_HEALTH_INSURANCE_FEE, METHOD.POST, body)
            .then((response) => {
                console.log("getHealthInsuranceFee success", response);
                const { object } = response;
                if (helper.IsEmptyObject(object)) {
                    dispatch(stop_get_health_insurance_fee({}, EMPTY, 'Không có dữ liệu phí bảo hiểm sức khoẻ!'))
                } else {
                    // dispatch(stop_get_health_insurance_fee({ ...object, extraPolicyFee }))
                    dispatch(getHealthInsurancePromotion(data, object))
                }
            }).catch(error => {
                console.log("getHealthInsuranceFee error", error);
                dispatch(stop_get_health_insurance_fee({}, EMPTY, error.msgError))
            })
    }
};

export const getHealthInsurancePromotion = function (data, feeResponse) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const { catalogId, serviceGroupId, airtimeTransactionTypeId, extraPolicyFee } = data
            const promotionBody = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: catalogId,
                serviceGroupID: serviceGroupId,
                airtimeTransactionTypeID: airtimeTransactionTypeId,
                extraData: feeResponse
            }
            apiBase(API_GET_PROMOTION_SERVICE, METHOD.POST, promotionBody)
                .then((response) => {
                    console.log("getHealthInsurancePromotion success", response);
                    const { object } = response;
                    if (helper.IsEmptyObject(object)) {
                        dispatch(stop_get_health_insurance_fee({ ...feeResponse, extraPolicyFee }, EMPTY, 'Không có thông tin khuyến mãi khi lấy phí bảo hiểm sức khoẻ!'))
                        reject('Không có thông tin khuyến mãi khi lấy phí bảo hiểm sức khoẻ!')
                    } else {
                        const { PromotionID, ExtraData } = object;
                        const feeBody = {
                            ...promotionBody,
                            productPromotion: ExtraData?.ProductPromotion,
                        }
                        apiBase(API_GET_HEALTH_INSURANCE_FEE, METHOD.POST, feeBody)
                            .then((response) => {
                                console.log("recalculateInsuranceFee success", response);
                                const { object } = response;
                                if (helper.IsEmptyObject(object)) {
                                    dispatch(stop_get_health_insurance_fee({ ...feeResponse, extraPolicyFee, productPromotion: ExtraData?.ProductPromotion }, EMPTY, 'Không lấy được dữ liệu tính phí bảo hiểm sức khoẻ sau khi áp dụng khuyến mãi!'))
                                    reject('Không lấy được dữ liệu tính phí bảo hiểm sức khoẻ sau khi áp dụng khuyến mãi!')
                                } else {
                                    resolve();
                                    dispatch(stop_get_health_insurance_fee({ ...object, extraPolicyFee, promotionId: PromotionID, productPromotion: ExtraData?.ProductPromotion }))
                                }
                            }).catch(error => {
                                console.log("recalculateInsuranceFee error", error);
                                dispatch(stop_get_health_insurance_fee({ ...feeResponse, extraPolicyFee, productPromotion: ExtraData?.ProductPromotion }, EMPTY, error.msgError))
                                reject(error.msgError)
                            })
                    }
                }).catch(error => {
                    console.log("getHealthInsurancePromotion error", error);
                    dispatch(stop_get_health_insurance_fee({ ...feeResponse, extraPolicyFee }, EMPTY, error.msgError))
                    reject(error.msgError)
                })

        })
    }
};

export const getCarInsuranceFee = function (data) {
    return (dispatch, getState) => {
        dispatch(start_get_health_insurance_fee())
        const { catalogId, serviceGroupId, airtimeTransactionTypeId, productPromotion, listProperty, extraPolicyFee } = data
        const body = {
            loginStoreId: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            moduleID: getState().userReducer.moduleID,
            catalogID: catalogId,
            serviceGroupID: serviceGroupId,
            airtimeTransactionTypeID: airtimeTransactionTypeId,
            productPromotion: productPromotion,
            extraData: {
                "ListProperty": listProperty
            }
        }
        apiBase(API_GET_HEALTH_INSURANCE_FEE, METHOD.POST, body)
            .then((response) => {
                console.log("getCarInsuranceFee success", response);
                const { object } = response;
                if (helper.IsEmptyObject(object)) {
                    dispatch(stop_get_health_insurance_fee({}, EMPTY, 'Không có dữ liệu phí bảo ô tô!'))
                } else {
                    dispatch(stop_get_health_insurance_fee({ ...object, extraPolicyFee }))
                    dispatch(getCarInsurancePromotion(data, object))
                }
            }).catch(error => {
                console.log("getCarInsuranceFee error", error);
                dispatch(stop_get_health_insurance_fee({}, EMPTY, error.msgError))
            })
    }
};

export const getCarInsurancePromotion = function (data, feeResponse) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const { catalogId, serviceGroupId, airtimeTransactionTypeId, extraPolicyFee } = data
            const promotionBody = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: catalogId,
                serviceGroupID: serviceGroupId,
                airtimeTransactionTypeID: airtimeTransactionTypeId,
                extraData: feeResponse
            }
            apiBase(API_GET_PROMOTION_SERVICE, METHOD.POST, promotionBody)
                .then((response) => {
                    console.log("getCarInsurancePromotion success", response);
                    const { object } = response;
                    if (helper.IsEmptyObject(object)) {
                        dispatch(stop_get_promotion_service({ ...feeResponse, extraPolicyFee }, EMPTY, 'Không có thông tin khuyến mãi khi lấy phí bảo hiểm ô tô!'))
                        reject('Không có thông tin khuyến mãi khi lấy phí bảo hiểm ô tô!')
                    } else {
                        const { PromotionID, ExtraData } = object;

                        if (helper.IsEmptyObject(object)) {
                            dispatch(stop_get_promotion_service({ ...feeResponse, extraPolicyFee, productPromotion: ExtraData?.ProductPromotion }, EMPTY, 'Không lấy được dữ liệu tính phí bảo hiểm ô tô sau khi áp dụng khuyến mãi!'))
                            reject('Không lấy được dữ liệu tính phí bảo hiểm ô tô sau khi áp dụng khuyến mãi!')
                        } else {
                            resolve();
                            dispatch(stop_get_promotion_service({ ...object, extraPolicyFee, promotionId: PromotionID, productPromotion: ExtraData?.ProductPromotion }))
                        }
                    }
                }).catch(error => {
                    console.log("getCarInsurancePromotion error", error);
                    dispatch(stop_get_promotion_service({ ...feeResponse, extraPolicyFee }, EMPTY, error.msgError))
                    reject(error.msgError)
                })

        })
    }
};

export const getSendOTPProcessServicePrequest = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleOrderID: data.saleOrderID,
                isRequestOTP: data.isRequestOTP,
                extraData: data.extraData,
                serviceVoucherID: data.serviceVoucherID
            };
            dispatch(start_get_send_otp_processs());
            apiBase(API_GET_PROCESS_SERVICE_REQUEST, METHOD.POST, body)
                .then((response) => {
                    console.log("getSendOTPProcessServicePrequest success", response);
                    const { object } = response;
                    if (!helper.IsEmptyObject(object)) {
                        dispatch(stop_get_send_otp_processs(object));
                        resolve(response);
                    } else {
                        dispatch(stop_get_send_otp_processs({}));
                        reject({ msgError: "Không tìm thấy thông tin gửi lại OTP" });
                    }
                })
                .catch((error) => {
                    dispatch(stop_add_to_sale_order_cart(!SUCCESS, {}));
                    const { msgError } = error;
                    console.log("getSendOTPProcessServicePrequest error", error);
                    reject(msgError);
                });
        });
    };
};

export const getGetTransactionDetail = function (saleOrderID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleOrderID: saleOrderID,
            };
            dispatch(start_get_send_otp_processs());
            apiBase(API_GET_TRANSACTION_DETAIL, METHOD.POST, body)
                .then((response) => {
                    console.log("getGetTransactionDetail success", response);
                    const { object } = response;
                    if (!helper.IsEmptyObject(object)) {
                        dispatch(stop_get_send_otp_processs(object));
                        resolve(response);
                    } else {
                        dispatch(stop_get_send_otp_processs({}));
                        reject({ msgError: "Không tìm thấy thông tin gửi lại OTP" });
                    }
                })
                .catch((error) => {
                    dispatch(stop_add_to_sale_order_cart(!SUCCESS, {}));
                    const { msgError } = error;
                    console.log("getGetTransactionDetail error", error);
                    reject(msgError);
                });
        });
    };
};

export const getConfirmOTPProcessServicePrequest = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleOrderID: data.saleOrderID,
                otp: data.otp
            };
            apiBase(API_GET_PROCESS_SERVICE_REQUEST, METHOD.POST, body)
                .then((response) => {
                    console.log("getConfirmOTPProcessServicePrequest success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("getConfirmOTPProcessServicePrequest error", error);
                    reject(error.msgError);
                });
        });
    };
};


export const updateHealthInsuranceFee = (feeInfo) => {
    return (dispatch, getState) => {
        dispatch(stop_get_health_insurance_fee(feeInfo))
    }
}

export const updateLocalSideState = (localSideState) => {
    return (dispatch, getState) => {
        dispatch({
            type: UPDATE_LOCAL_SIDE_STATE,
            localSideState
        })
    }
}

export const createHealthInsuranceServiceRequest = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const {
                catalogId,
                serviceGroupId,
                airtimeTransactionTypeId,
                productPromotion,
                airTimeTransactionBO,
                listProperty,
                partnerData,
                promotionId
            } = data
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: catalogId,
                serviceGroupID: serviceGroupId,
                airtimeTransactionTypeID: airtimeTransactionTypeId,
                productPromotion: productPromotion,
                airTimeTransactionBO: airTimeTransactionBO,
                extraData: {
                    "ListProperty": listProperty
                },
                partnerData: partnerData,
                promotionID: promotionId
            }
            apiBase(API_CREATE_SERVICE_REQUEST, METHOD.POST, body)
                .then((response) => {
                    console.log("createHealthInsuranceServiceRequest success", response.object);
                    const { object } = response;
                    if (helper.IsEmptyObject(object)) {
                        reject("Không lấy được thông tin tạo phiếu yêu cầu dịch vụ!");
                    } else {
                        resolve(object);
                    }
                })
                .catch((error) => {
                    console.log('createHealthInsuranceServiceRequest error', error);
                    reject(error.msgError);
                });
        })
    }
};

export const getParentInsuranceContractInfo = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const { catalogId, serviceGroupId, airtimeTransactionTypeId, productPromotion, listProperty } = data
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                catalogID: catalogId,
                serviceGroupID: serviceGroupId,
                airtimeTransactionTypeID: airtimeTransactionTypeId,
                productPromotion: productPromotion,
                extraData: {
                    "ListProperty": listProperty
                }
            }
            apiBase(API_VALIDATE_DATA_SERVICE_REQUEST, METHOD.POST, body)
                .then((response) => {
                    console.log("getParentInsuranceContractInfo success", response);
                    const { object } = response;
                    if (helper.IsEmptyObject(object)) {
                        reject('Không có thông tin hợp đồng của bố mẹ!')
                    } else {
                        resolve(object);
                    }
                }).catch(error => {
                    console.log("getParentInsuranceContractInfo error", error);
                    reject(error.msgError)
                })
        })
    }
};


export const getPrintServiceReport = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "airtimeTransactionID": data.airtimeTransactionID
            };
            apiBase(API_GET_PRINT_SERVICE_REPORT, METHOD.POST, body).then((response) => {
                console.log("getPrintServiceReport success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                }
                else {
                    reject("Lỗi lấy thông tin dữ liệu");
                }
            })
                .catch((error) => {
                    console.log("getPrintServiceReport error", error);
                    reject(error)
                });
        });
    };
};

export const getRefundDetails = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "airtimeTransactionID": data.airtimeTransactionID
            };
            apiBase(API_GET_CASH_RETURN_DETAIL, METHOD.POST, body).then((response) => {
                console.log("getRefundDetails success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                }
                else {
                    reject("Lỗi lấy thông tin dữ liệu");
                }
            })
                .catch((error) => {
                    console.log("getRefundDetails error", error);
                    reject(error)
                });
        });
    };
};



export const setHealthInsuranceSaleOrderId = (healthInsuranceSaleOrderId) => {
    return (dispatch, getState) => {
        dispatch({
            type: SET_HEALTH_INSURANCE_SALE_ORDER_ID,
            healthInsuranceSaleOrderId
        })
    }
}

const start_get_list_health_insurance_partner = () => ({ type: START_GET_LIST_HEALTH_INSURANCE_PARTNER });

const stop_get_list_health_insurance_partner = (
    listHealthInsurancePartner,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_LIST_HEALTH_INSURANCE_PARTNER,
    listHealthInsurancePartner,
    isEmpty,
    description,
    isError
});

const set_health_insurance_partner_info = (healthInsurancePartnerInfo = {}) => ({
    type: SET_HEALTH_INSURANCE_PARTNER_INFO,
    healthInsurancePartnerInfo
});

const set_input_form_template = (inputFormTemplate) => ({
    type: SET_INPUT_FORM_TEMPLATE,
    inputFormTemplate
});

export const start_search_history_insurance = () => ({ type: START_SEARCH_HISTORY_INSURANCE });

export const stop_search_history_insurance = (data, isEmpty = false, description = '', isError = false) => ({
    type: STOP_SEARCH_HISTORY_INSURANCE,
    data,
    isEmpty,
    description,
    isError
});

const start_get_health_insurance_fee = () => ({ type: START_GET_HEALTH_INSURANCE_FEE });

const stop_get_health_insurance_fee = (
    healthInsuranceFee,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_HEALTH_INSURANCE_FEE,
    healthInsuranceFee,
    isEmpty,
    description,
    isError
});


export const start_add_to_sale_order_cart = () => {
    return {
        type: START_ADD_TO_SALE_ORDER_CART,
    };
};

export const stop_add_to_sale_order_cart = (dataSaleOrderCart = {}) => {
    return {
        type: STOP_ADD_TO_SALE_ORDER_CART,
        dataSaleOrderCart,
    };
};

export const start_get_service_list = () => {
    return {
        type: START_GET_SERVICE_LIST,
    };
};

export const stop_get_service_list = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_SERVICE_LIST,
    data,
    isEmpty,
    description,
    isError
});

export const start_validate_data_service_request = () => {
    return {
        type: START_VALIDATE_DATA_SERVICE_REQUEST,
    };
};

export const stop_validate_data_service_request = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_VALIDATE_DATA_SERVICE_REQUEST,
    data,
    isEmpty,
    description,
    isError
});

export const clear_data_validate_service_request = () => ({
    type: CLEAR_DATA_VALIDATE_SERVICE_REQUEST
});

export const start_get_create_airtime_refund = () => {
    return {
        type: START_GET_CREATE_AIRTIME_REFUND,
    };
};

export const stop_get_create_airtime_refund = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_CREATE_AIRTIME_REFUND,
    data,
    isEmpty,
    description,
    isError
});

const start_get_query_status_edit_emei_service_request = () => {
    return {
        type: START_GET_QUERY_STATUS_EDIT_EMEI_SERVICE_REQUEST,
    };
};

const stop_get_query_status_edit_emei_service_request = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_QUERY_STATUS_EDIT_EMEI_SERVICE_REQUEST,
    data,
    isEmpty,
    description,
    isError
});

export const start_search_history_edit_emei = () => {
    return {
        type: START_SEARCH_HISTORY_EDIT_EMEI,
    };
};

export const stop_search_history_edit_emei = (data, isEmpty = false, description = '', isError = false) => ({
    type: STOP_SEARCH_HISTORY_EDIT_EMEI,
    data,
    isEmpty,
    description,
    isError
});

export const start_get_data_info = () => {
    return {
        type: START_GET_DATA_INFO,
    };
};

export const stop_get_data_info = (data, isEmpty = false, description = '', isError = false) => ({
    type: STOP_GET_DATA_INFO,
    data,
    isEmpty,
    description,
    isError
});

const update_header_airtime = (
    data
) => ({
    type: UPDATE_HEADER_AIRTIME,
    data
});

export const updateHeaderAirtime = (data) => {
    return function (dispatch, getState) {
        dispatch(update_header_airtime(data));
    }
}

export const clear_data_info = () => ({
    type: CLEAR_DATA_INFO
});

export const start_search_so = () => {
    return {
        type: START_GET_SEARCH_SO,
    };
};
export const stop_search_so = (data, isEmpty = false, description = '', isError = false) => ({
    type: STOP_GET_SEARCH_SO,
    data,
    isEmpty,
    description,
    isError
});

export const clear_data_search_so = () => ({
    type: CLEAR_DATA_SEARCH_SO
});

export const start_get_detail_so = () => {
    return {
        type: START_GET_DETAIL_SO,
    };
};
export const stop_get_detail_so = (data, isEmpty = false, description = '', isError = false) => ({
    type: STOP_GET_DETAIL_SO,
    data,
    isEmpty,
    description,
    isError
});

const start_get_province = () => {
    return ({
        type: START_GET_PROVINCE
    });
}

const stop_get_province = (
    dataProvince,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_PROVINCE,
        dataProvince,
        isEmpty,
        description,
        isError,
    });
}

const start_get_district = () => {
    return ({
        type: START_GET_DISTRICT
    });
}

const stop_get_district = (
    dataDistrict,
    isEmpty = false,
    description = "",
    isError = false,
) => {
    return ({
        type: STOP_GET_DISTRICT,
        dataDistrict,
        isEmpty,
        description,
        isError,
    });
}

const start_get_promotion_service = () => {
    return ({
        type: START_GET_PROMOTION_SERVICE
    });
}

const stop_get_promotion_service = (
    data,
) => {
    return ({
        type: STOP_GET_PROMOTION_SERVICE,
        data,
    });
}

export const start_get_price_service = () => {
    return {
        type: START_GET_PRICE_SERVICE,
    };
};

export const stop_get_price_service = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_PRICE_SERVICE,
    data,
    isEmpty,
    description,
    isError
});

const update_item_order_detail = (
    data
) => ({
    type: UPDATE_ITEM_DETAIL_ORDER_DETAIL,
    data
});

export const updateItemOrderDetail = (data) => {
    return function (dispatch, getState) {
        dispatch(update_item_order_detail(data));
    }
}

const start_get_send_otp_processs = () => ({
    type: START_GET_SEND_OTP_PROCESS
});
const stop_get_send_otp_processs = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_SEND_OTP_PROCESS,
    data,
    isEmpty,
    description,
    isError
});


const FORM_TEMPLATE_MOCK_DATA = [
    {
        "InsInforGroupID": 1,
        "GroupName": "Phạm vi bảo hiểm",
        "Step": 1,
        "OrderIndex": 0,
        "ParentGroupID": 0,
        "AirtimeTransactionTypeID": 1792,
        "InsPackageID": 0,
        "CreatedUser": "165065",
        "CreatedDate": "2024-04-01T16:08:18",
        "PropertyList": [
            {
                "ParentPropertyID": null,
                "PropertyID": 64,
                "Label": "Loại hợp đồng",
                "DataType": 3,
                "Data": [
                    {
                        "PropertyValueID": 3101,
                        "InsPackagePropertyID": 2920,
                        "Value": "Mới",
                        "PartnerValue": "G",
                        "IsDefault": false,
                        "OrderIndex": 0,
                        "cus_PropertyID": 64,
                        "cus_InsPackageID": 345,
                        "IsSelected": false
                    },
                    {
                        "PropertyValueID": 3102,
                        "InsPackagePropertyID": 2920,
                        "Value": "Tái ký",
                        "PartnerValue": "T",
                        "IsDefault": false,
                        "OrderIndex": 0,
                        "cus_PropertyID": 64,
                        "cus_InsPackageID": 345,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "PACKAGE",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 65,
                "Label": "Số hợp đồng tái ký",
                "DataType": 0,
                "Data": [
                    {
                        "PropertyValueID": 0,
                        "InsPackagePropertyID": 0,
                        "Value": "",
                        "PartnerValue": "",
                        "IsDefault": false,
                        "OrderIndex": 0,
                        "cus_PropertyID": 65,
                        "cus_InsPackageID": 345,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "PACKAGE",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 66,
                "Label": "Chương trình bảo hiểm",
                "DataType": 4,
                "Data": [
                    {
                        "PropertyValueID": 3103,
                        "InsPackagePropertyID": 2922,
                        "Value": "Đồng",
                        "PartnerValue": "1",
                        "IsDefault": false,
                        "OrderIndex": 0,
                        "cus_PropertyID": 66,
                        "cus_InsPackageID": 345,
                        "IsSelected": false
                    },
                    {
                        "PropertyValueID": 3104,
                        "InsPackagePropertyID": 2922,
                        "Value": "Bạc",
                        "PartnerValue": "2",
                        "IsDefault": false,
                        "OrderIndex": 0,
                        "cus_PropertyID": 66,
                        "cus_InsPackageID": 345,
                        "IsSelected": false
                    },
                    {
                        "PropertyValueID": 3105,
                        "InsPackagePropertyID": 2922,
                        "Value": "Vàng",
                        "PartnerValue": "3",
                        "IsDefault": false,
                        "OrderIndex": 0,
                        "cus_PropertyID": 66,
                        "cus_InsPackageID": 345,
                        "IsSelected": false
                    },
                    {
                        "PropertyValueID": 3106,
                        "InsPackagePropertyID": 2922,
                        "Value": "Bạch Kim",
                        "PartnerValue": "4",
                        "IsDefault": false,
                        "OrderIndex": 0,
                        "cus_PropertyID": 66,
                        "cus_InsPackageID": 345,
                        "IsSelected": false
                    },
                    {
                        "PropertyValueID": 3107,
                        "InsPackagePropertyID": 2922,
                        "Value": "Kim Cương",
                        "PartnerValue": "5",
                        "IsDefault": false,
                        "OrderIndex": 0,
                        "cus_PropertyID": 66,
                        "cus_InsPackageID": 345,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "PACKAGE",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 112,
                "Label": "Ngày sinh người được bảo hiểm",
                "DataType": 2,
                "Data": [
                    {
                        "BusinesTypePropertyValueID": 0,
                        "BusinesTypePropertyID": 0,
                        "Value": "",
                        "PartnerValue": "",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 112,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "BUSINESS",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 4,
                "Label": "Ngày hiệu lực",
                "DataType": 2,
                "Data": [
                    {
                        "BusinesTypePropertyValueID": 0,
                        "BusinesTypePropertyID": 0,
                        "Value": "",
                        "PartnerValue": "",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 4,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "BUSINESS",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 5,
                "Label": "Ngày kết thúc",
                "DataType": 2,
                "Data": [
                    {
                        "BusinesTypePropertyValueID": 0,
                        "BusinesTypePropertyID": 0,
                        "Value": "",
                        "PartnerValue": "",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 5,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "BUSINESS",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            }
        ],
        "ChildProperty": null
    },
    {
        "InsInforGroupID": 6,
        "GroupName": "Thông tin đối tượng mua bảo hiểm",
        "Step": 2,
        "OrderIndex": 0,
        "ParentGroupID": 0,
        "AirtimeTransactionTypeID": 1792,
        "InsPackageID": 0,
        "CreatedUser": "165065",
        "CreatedDate": "2024-04-01T16:23:22",
        "PropertyList": [
            {
                "ParentPropertyID": null,
                "PropertyID": 107,
                "Label": "Loại Khách hàng",
                "DataType": 0,
                "Data": [
                    {
                        "BusinesTypePropertyValueID": 46475,
                        "BusinesTypePropertyID": 108,
                        "Value": "Cá nhân",
                        "PartnerValue": "C",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 107,
                        "IsSelected": false
                    },
                    {
                        "BusinesTypePropertyValueID": 46476,
                        "BusinesTypePropertyID": 108,
                        "Value": "Doanh Nghiệp",
                        "PartnerValue": "T",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 107,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "BUSINESS",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 35,
                "Label": "Mối quan hệ với người mua",
                "DataType": 0,
                "Data": [
                    {
                        "BusinesTypePropertyValueID": 46481,
                        "BusinesTypePropertyID": 120,
                        "Value": "Bố/Mẹ",
                        "PartnerValue": "2",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 35,
                        "IsSelected": false
                    },
                    {
                        "BusinesTypePropertyValueID": 46482,
                        "BusinesTypePropertyID": 120,
                        "Value": "Vợ/Chồng",
                        "PartnerValue": "3",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 35,
                        "IsSelected": false
                    },
                    {
                        "BusinesTypePropertyValueID": 46483,
                        "BusinesTypePropertyID": 120,
                        "Value": "Con cái",
                        "PartnerValue": "4",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 35,
                        "IsSelected": false
                    },
                    {
                        "BusinesTypePropertyValueID": 46484,
                        "BusinesTypePropertyID": 120,
                        "Value": "Bản thân",
                        "PartnerValue": "1",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 35,
                        "IsSelected": false
                    },
                    {
                        "BusinesTypePropertyValueID": 46485,
                        "BusinesTypePropertyID": 120,
                        "Value": "Anh/Chị/Em ruột",
                        "PartnerValue": "5",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 35,
                        "IsSelected": false
                    },
                    {
                        "BusinesTypePropertyValueID": 46486,
                        "BusinesTypePropertyID": 120,
                        "Value": "Khác",
                        "PartnerValue": "6",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 35,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "BUSINESS",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            }
        ],
        "ChildProperty": null
    },
    {
        "InsInforGroupID": 7,
        "GroupName": "Thông tin người mua bảo hiểm",
        "Step": 2,
        "OrderIndex": 1,
        "ParentGroupID": 0,
        "AirtimeTransactionTypeID": 1792,
        "InsPackageID": 0,
        "CreatedUser": "165065",
        "CreatedDate": "2024-04-01T16:23:55",
        "PropertyList": [
            {
                "ParentPropertyID": null,
                "PropertyID": 108,
                "Label": "Số CMND/CCCD/ Mã số thuế",
                "DataType": 0,
                "Data": [
                    {
                        "BusinesTypePropertyValueID": 0,
                        "BusinesTypePropertyID": 0,
                        "Value": "",
                        "PartnerValue": "",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 108,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "BUSINESS",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 109,
                "Label": "Giới tính người mua bảo hiểm",
                "DataType": 3,
                "Data": [
                    {
                        "BusinesTypePropertyValueID": 46477,
                        "BusinesTypePropertyID": 110,
                        "Value": "Nam",
                        "PartnerValue": "1",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 109,
                        "IsSelected": false
                    },
                    {
                        "BusinesTypePropertyValueID": 46478,
                        "BusinesTypePropertyID": 110,
                        "Value": "Nữ",
                        "PartnerValue": "2",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 109,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "BUSINESS",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 36,
                "Label": "Ngày sinh",
                "DataType": 2,
                "Data": [
                    {
                        "BusinesTypePropertyValueID": 0,
                        "BusinesTypePropertyID": 0,
                        "Value": "",
                        "PartnerValue": "",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 36,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "BUSINESS",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 38,
                "Label": "Họ tên người mua",
                "DataType": 0,
                "Data": [
                    {
                        "BusinesTypePropertyValueID": 0,
                        "BusinesTypePropertyID": 0,
                        "Value": "",
                        "PartnerValue": "",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 38,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "BUSINESS",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 12,
                "Label": "Địa chỉ",
                "DataType": 3,
                "Data": [
                    {
                        "BusinesTypePropertyValueID": 0,
                        "BusinesTypePropertyID": 0,
                        "Value": "",
                        "PartnerValue": "",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 12,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "BUSINESS",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 11,
                "Label": "Số nhà",
                "DataType": 0,
                "Data": [
                    {
                        "BusinesTypePropertyValueID": 0,
                        "BusinesTypePropertyID": 0,
                        "Value": "",
                        "PartnerValue": "",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 11,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "BUSINESS",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 37,
                "Label": "Số điện thoại người mua",
                "DataType": 0,
                "Data": [
                    {
                        "BusinesTypePropertyValueID": 0,
                        "BusinesTypePropertyID": 0,
                        "Value": "",
                        "PartnerValue": "",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 37,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "BUSINESS",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 16,
                "Label": "Email",
                "DataType": 0,
                "Data": [
                    {
                        "BusinesTypePropertyValueID": 0,
                        "BusinesTypePropertyID": 0,
                        "Value": "",
                        "PartnerValue": "",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 16,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "BUSINESS",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            }
        ],
        "ChildProperty": null
    },
    {
        "InsInforGroupID": 2,
        "GroupName": "Thông tin bố mẹ",
        "Step": 1,
        "OrderIndex": 1,
        "ParentGroupID": 0,
        "AirtimeTransactionTypeID": 1792,
        "InsPackageID": 0,
        "CreatedUser": "165065",
        "CreatedDate": "2024-04-01T16:16:20",
        "PropertyList": [
            {
                "ParentPropertyID": null,
                "PropertyID": 68,
                "Label": "Số hợp đồng (bố/mẹ) cho trường hợp trẻ em mua cùng người lớn",
                "DataType": 0,
                "Data": [
                    {
                        "PropertyValueID": 0,
                        "InsPackagePropertyID": 0,
                        "Value": "",
                        "PartnerValue": "",
                        "IsDefault": false,
                        "OrderIndex": 0,
                        "cus_PropertyID": 68,
                        "cus_InsPackageID": 345,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "PACKAGE",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            }
        ],
        "ChildProperty": null
    },
    {
        "InsInforGroupID": 3,
        "GroupName": "Thông tin tình trạng sức khỏe",
        "Step": 1,
        "OrderIndex": 2,
        "ParentGroupID": 0,
        "AirtimeTransactionTypeID": 1792,
        "InsPackageID": 0,
        "CreatedUser": "165065",
        "CreatedDate": "2024-04-01T16:17:51",
        "PropertyList": [
            {
                "ParentPropertyID": null,
                "PropertyID": 75,
                "Label": "Câu 1:\nNgười được bảo hiểm có thuộc các trường hợp dưới đây hay không?\n- Những người bị bệnh tâm thần, bệnh phong, hội chứng down, tự kỷ;\n- Những người bị thương tật vĩnh viễn từ 50% trở lên;\n- Những người đang trong thời gian điều trị bệnh hoặc thương tật hoặc bị ung thư.\nĐiều này chỉ áp dụng đối với các trường hợp tham gia bảo hiểm năm đầu tiên.",
                "DataType": 3,
                "Data": [
                    {
                        "PropertyValueID": 3108,
                        "InsPackagePropertyID": 2925,
                        "Value": "Có",
                        "PartnerValue": "C",
                        "IsDefault": false,
                        "OrderIndex": 0,
                        "cus_PropertyID": 75,
                        "cus_InsPackageID": 345,
                        "IsSelected": false
                    },
                    {
                        "PropertyValueID": 3109,
                        "InsPackagePropertyID": 2925,
                        "Value": "Không",
                        "PartnerValue": "K",
                        "IsDefault": false,
                        "OrderIndex": 1,
                        "cus_PropertyID": 75,
                        "cus_InsPackageID": 345,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "PACKAGE",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 76,
                "Label": "Câu 2: Trong vòng 3 năm qua, Người được bảo hiểm đã từng được chẩn đoán, xuất hiện triệu chứng phải đi khám, điều trị hay đã được chuyên gia y tế khuyên Người được bảo hiểm phải điều trị hay không?\nLưu ý:\nNgười được bảo hiểm không trả lời ‘CÓ” đối với các bệnh/ tình trạng y tế dưới đây:\n- Phụ nữ sinh con (sinh thường, sinh mổ) mà không có biến chứng thai sản;\n- Cúm và cảm lạnh theo mùa thông thường, viêm dạ dày cấp tính, viêm ruột thừa cấp tính, viêm amidan cấp tính, nhiễm trùng đường tiết niệu, bệnh tả, thương hàn, sốt xuất huyết mà Người được bảo hiểm đã được điều trị và đã hồi phục hoàn toàn hoặc nếu Người được bảo hiểm sử dụng bất kỳ loại thực phẩm bổ sung sức khỏe tổng quát nào.",
                "DataType": 3,
                "Data": [
                    {
                        "PropertyValueID": 3111,
                        "InsPackagePropertyID": 2926,
                        "Value": "Có",
                        "PartnerValue": "C",
                        "IsDefault": false,
                        "OrderIndex": 0,
                        "cus_PropertyID": 76,
                        "cus_InsPackageID": 345,
                        "IsSelected": false
                    },
                    {
                        "PropertyValueID": 3110,
                        "InsPackagePropertyID": 2926,
                        "Value": "Không",
                        "PartnerValue": "K",
                        "IsDefault": false,
                        "OrderIndex": 1,
                        "cus_PropertyID": 76,
                        "cus_InsPackageID": 345,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "PACKAGE",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 77,
                "Label": "Câu 2.1. NĐBH có bệnh về thân kinh, bệnh về máu, da và các mô liên kết không?",
                "DataType": 3,
                "Data": [
                    {
                        "PropertyValueID": 3113,
                        "InsPackagePropertyID": 2927,
                        "Value": "Có",
                        "PartnerValue": "C",
                        "IsDefault": false,
                        "OrderIndex": 0,
                        "cus_PropertyID": 77,
                        "cus_InsPackageID": 345,
                        "IsSelected": false
                    },
                    {
                        "PropertyValueID": 3112,
                        "InsPackagePropertyID": 2927,
                        "Value": "Không",
                        "PartnerValue": "K",
                        "IsDefault": false,
                        "OrderIndex": 1,
                        "cus_PropertyID": 77,
                        "cus_InsPackageID": 345,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "PACKAGE",
                "ChildProperty": [
                    {
                        "ParentPropertyID": 77,
                        "PropertyID": 82,
                        "Label": "Danh sách nhóm bệnh 1",
                        "DataType": 3,
                        "Data": [
                            {
                                "PropertyValueID": 3122,
                                "InsPackagePropertyID": 2932,
                                "Value": "Viêm hệ thần kinh trung ương (Não)",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 82,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3123,
                                "InsPackagePropertyID": 2932,
                                "Value": "Parkinson, Alzheimer",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 82,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3124,
                                "InsPackagePropertyID": 2932,
                                "Value": "Thoái hóa khác của hệ thần kinh",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 82,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3125,
                                "InsPackagePropertyID": 2932,
                                "Value": "Mất trí nhớ, hôn mê, bại não, liệt",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 82,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3126,
                                "InsPackagePropertyID": 2932,
                                "Value": "Bỏng nặng từ độ III trở lên",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 82,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3127,
                                "InsPackagePropertyID": 2932,
                                "Value": "Hội chứng Apallic",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 82,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3128,
                                "InsPackagePropertyID": 2932,
                                "Value": "Phẫu thuật não",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 82,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3129,
                                "InsPackagePropertyID": 2932,
                                "Value": "Viêm màng não, viêm não do virus",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 82,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3130,
                                "InsPackagePropertyID": 2932,
                                "Value": "Bệnh tế bào thần kinh vận động",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 82,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3131,
                                "InsPackagePropertyID": 2932,
                                "Value": "Xơ cứng rải rác (đa xơ cứng)",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 82,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3132,
                                "InsPackagePropertyID": 2932,
                                "Value": "Loạn dưỡng cơ",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 82,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3133,
                                "InsPackagePropertyID": 2932,
                                "Value": "Nhược cơ",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 82,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3134,
                                "InsPackagePropertyID": 2932,
                                "Value": "Thiếu máu bất sản, thiếu máu tán huyết, thiếu máu do suy tủy",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 82,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3135,
                                "InsPackagePropertyID": 2932,
                                "Value": "Ghép tủy",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 82,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3136,
                                "InsPackagePropertyID": 2932,
                                "Value": "Suy tủy",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 82,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3137,
                                "InsPackagePropertyID": 2932,
                                "Value": "Lupus ban đỏ",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 82,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3138,
                                "InsPackagePropertyID": 2932,
                                "Value": "Teo cơ",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 82,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    }
                ],
                "ChildLabel": "Danh sách nhóm bệnh 1",
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 78,
                "Label": "Câu 2.2. NĐBH có bị bệnh về hệ hô hấp và hệ tuần hoàn, hệ tiêu hóa, hệ tiết niệu không?",
                "DataType": 3,
                "Data": [
                    {
                        "PropertyValueID": 3114,
                        "InsPackagePropertyID": 2928,
                        "Value": "Có",
                        "PartnerValue": "C",
                        "IsDefault": false,
                        "OrderIndex": 0,
                        "cus_PropertyID": 78,
                        "cus_InsPackageID": 345,
                        "IsSelected": false
                    },
                    {
                        "PropertyValueID": 3115,
                        "InsPackagePropertyID": 2928,
                        "Value": "Không",
                        "PartnerValue": "K",
                        "IsDefault": false,
                        "OrderIndex": 1,
                        "cus_PropertyID": 78,
                        "cus_InsPackageID": 345,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "PACKAGE",
                "ChildProperty": [
                    {
                        "ParentPropertyID": 78,
                        "PropertyID": 83,
                        "Label": "Danh sách nhóm bệnh2",
                        "DataType": 3,
                        "Data": [
                            {
                                "PropertyValueID": 3139,
                                "InsPackagePropertyID": 2933,
                                "Value": "Suy phổi, tràn khí phổi, suy hô hấp mãn tính",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 83,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3140,
                                "InsPackagePropertyID": 2933,
                                "Value": "Phẫu thuật cắt bỏ 1 bên phổi",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 83,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3141,
                                "InsPackagePropertyID": 2933,
                                "Value": "Tăng áp động mạch phổi",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 83,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3142,
                                "InsPackagePropertyID": 2933,
                                "Value": "Bệnh phổi giai đoạn cuối",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 83,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3143,
                                "InsPackagePropertyID": 2933,
                                "Value": "Mạch máu não/đột quỵ (xuất huyết não, xơ cứng động mạch)",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 83,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3144,
                                "InsPackagePropertyID": 2933,
                                "Value": "Nhồi máu cơ tim, suy tim mất bù, bệnh tim giai đoạn cuối",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 83,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3145,
                                "InsPackagePropertyID": 2933,
                                "Value": "Phẫu thuật động mạch chủ/van tim, ghép tim",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 83,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3146,
                                "InsPackagePropertyID": 2933,
                                "Value": "Phẫu thuật nối tắt động mạch vành",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 83,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3147,
                                "InsPackagePropertyID": 2933,
                                "Value": "Viêm gan siêu vi tối cấp",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 83,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3148,
                                "InsPackagePropertyID": 2933,
                                "Value": "Xơ gan",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 83,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3149,
                                "InsPackagePropertyID": 2933,
                                "Value": "Bệnh Crohn",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 83,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3150,
                                "InsPackagePropertyID": 2933,
                                "Value": "Phẫu thuật gan",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 83,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3151,
                                "InsPackagePropertyID": 2933,
                                "Value": "Suy gan (bệnh gan giai đoạn cuối)",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 83,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3152,
                                "InsPackagePropertyID": 2933,
                                "Value": "Suy thận, teo thận, sỏi thận cả 2 bên",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 83,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3153,
                                "InsPackagePropertyID": 2933,
                                "Value": "Chạy thận nhân tạo",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 83,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3154,
                                "InsPackagePropertyID": 2933,
                                "Value": "Suy tuyến thượng thận mãn tính (Addison)",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 83,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    }
                ],
                "ChildLabel": "Danh sách nhóm bệnh2",
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 79,
                "Label": "Câu 2.3. NĐBH có bị bệnh về cơ xương khớp, hệ nội tiết, dinh dưỡng, chuyển hóa, khối u và các bệnh khác không?",
                "DataType": 3,
                "Data": [
                    {
                        "PropertyValueID": 3117,
                        "InsPackagePropertyID": 2929,
                        "Value": "Có",
                        "PartnerValue": "C",
                        "IsDefault": false,
                        "OrderIndex": 0,
                        "cus_PropertyID": 79,
                        "cus_InsPackageID": 345,
                        "IsSelected": false
                    },
                    {
                        "PropertyValueID": 3116,
                        "InsPackagePropertyID": 2929,
                        "Value": "Không",
                        "PartnerValue": "K",
                        "IsDefault": false,
                        "OrderIndex": 1,
                        "cus_PropertyID": 79,
                        "cus_InsPackageID": 345,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "PACKAGE",
                "ChildProperty": [
                    {
                        "ParentPropertyID": 79,
                        "PropertyID": 84,
                        "Label": "Danh sách nhóm bệnh 3",
                        "DataType": 3,
                        "Data": [
                            {
                                "PropertyValueID": 3155,
                                "InsPackagePropertyID": 2934,
                                "Value": "Viêm đa khớp dạng thấp nặng",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 84,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3156,
                                "InsPackagePropertyID": 2934,
                                "Value": "Loãng xương mức độ nặng",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 84,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3157,
                                "InsPackagePropertyID": 2934,
                                "Value": "Ghép tủy xương",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 84,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3158,
                                "InsPackagePropertyID": 2934,
                                "Value": "Tiểu đường chỉ số trên 11mmol/l\nTiểu đường đã gây biến chứng",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 84,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3159,
                                "InsPackagePropertyID": 2934,
                                "Value": "U thượng thận trái (đã cắt hoặc chưa cắt)",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 84,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3160,
                                "InsPackagePropertyID": 2934,
                                "Value": "Ung thư các loại",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 84,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3161,
                                "InsPackagePropertyID": 2934,
                                "Value": "Lao các loại",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 84,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3162,
                                "InsPackagePropertyID": 2934,
                                "Value": "Phong",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 84,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3163,
                                "InsPackagePropertyID": 2934,
                                "Value": "Bạch cầu",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 84,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3164,
                                "InsPackagePropertyID": 2934,
                                "Value": "Các bệnh lây qua đường tình dục: Giang mai, lậu, hội chứng suy giảm miễn dịch",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 84,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3165,
                                "InsPackagePropertyID": 2934,
                                "Value": "Bệnh bẩm sinh, di truyền, dị dạng về gen",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 84,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3166,
                                "InsPackagePropertyID": 2934,
                                "Value": "Nang ở tủy thận",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 84,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3167,
                                "InsPackagePropertyID": 2934,
                                "Value": "Hội chứng Đao (Down)",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 84,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3168,
                                "InsPackagePropertyID": 2934,
                                "Value": "Mù 1 mắt trở lên",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 84,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3169,
                                "InsPackagePropertyID": 2934,
                                "Value": "Gai đôi cột sống",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 84,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3170,
                                "InsPackagePropertyID": 2934,
                                "Value": "Suy đa tạng",
                                "PartnerValue": "null",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 84,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    }
                ],
                "ChildLabel": "Danh sách nhóm bệnh 3",
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 80,
                "Label": "Câu 2.4. NĐBH có các bệnh lý ví dụ như: Bỏng dưới độ III, Tim, Viêm gan A….không?",
                "DataType": 3,
                "Data": [
                    {
                        "PropertyValueID": 3119,
                        "InsPackagePropertyID": 2930,
                        "Value": "Có",
                        "PartnerValue": "C",
                        "IsDefault": false,
                        "OrderIndex": 0,
                        "cus_PropertyID": 80,
                        "cus_InsPackageID": 345,
                        "IsSelected": false
                    },
                    {
                        "PropertyValueID": 3118,
                        "InsPackagePropertyID": 2930,
                        "Value": "Không",
                        "PartnerValue": "K",
                        "IsDefault": false,
                        "OrderIndex": 1,
                        "cus_PropertyID": 80,
                        "cus_InsPackageID": 345,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "PACKAGE",
                "ChildProperty": [
                    {
                        "ParentPropertyID": 80,
                        "PropertyID": 85,
                        "Label": "Bỏng dưới độ III",
                        "DataType": 3,
                        "Data": [
                            {
                                "PropertyValueID": 3171,
                                "InsPackagePropertyID": 2935,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 85,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3172,
                                "InsPackagePropertyID": 2935,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 85,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    },
                    {
                        "ParentPropertyID": 80,
                        "PropertyID": 86,
                        "Label": "Tim",
                        "DataType": 3,
                        "Data": [
                            {
                                "PropertyValueID": 3173,
                                "InsPackagePropertyID": 2936,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 86,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3174,
                                "InsPackagePropertyID": 2936,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 86,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    },
                    {
                        "ParentPropertyID": 80,
                        "PropertyID": 87,
                        "Label": "Tăng áp lực động mạch vành vô căn",
                        "DataType": 3,
                        "Data": [
                            {
                                "PropertyValueID": 3175,
                                "InsPackagePropertyID": 2937,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 87,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3176,
                                "InsPackagePropertyID": 2937,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 87,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    },
                    {
                        "ParentPropertyID": 80,
                        "PropertyID": 88,
                        "Label": "Viêm gan A",
                        "DataType": 3,
                        "Data": [
                            {
                                "PropertyValueID": 3177,
                                "InsPackagePropertyID": 2938,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 88,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3178,
                                "InsPackagePropertyID": 2938,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 88,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    },
                    {
                        "ParentPropertyID": 80,
                        "PropertyID": 89,
                        "Label": "Viêm gan B",
                        "DataType": 3,
                        "Data": [
                            {
                                "PropertyValueID": 3179,
                                "InsPackagePropertyID": 2939,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 89,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3180,
                                "InsPackagePropertyID": 2939,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 89,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    },
                    {
                        "ParentPropertyID": 80,
                        "PropertyID": 90,
                        "Label": "Viêm gan C",
                        "DataType": 3,
                        "Data": [
                            {
                                "PropertyValueID": 3181,
                                "InsPackagePropertyID": 2940,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 90,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3182,
                                "InsPackagePropertyID": 2940,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 90,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    },
                    {
                        "ParentPropertyID": 80,
                        "PropertyID": 91,
                        "Label": "Rối loạn tuyến giáp",
                        "DataType": 3,
                        "Data": [
                            {
                                "PropertyValueID": 3183,
                                "InsPackagePropertyID": 2941,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 91,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3184,
                                "InsPackagePropertyID": 2941,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 91,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    },
                    {
                        "ParentPropertyID": 80,
                        "PropertyID": 92,
                        "Label": "Cường giáp",
                        "DataType": 3,
                        "Data": [
                            {
                                "PropertyValueID": 3185,
                                "InsPackagePropertyID": 2942,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 92,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3186,
                                "InsPackagePropertyID": 2942,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 92,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    },
                    {
                        "ParentPropertyID": 80,
                        "PropertyID": 93,
                        "Label": "Suy giáp",
                        "DataType": 3,
                        "Data": [
                            {
                                "PropertyValueID": 3187,
                                "InsPackagePropertyID": 2943,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 93,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3188,
                                "InsPackagePropertyID": 2943,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 93,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    },
                    {
                        "ParentPropertyID": 80,
                        "PropertyID": 94,
                        "Label": "Basedow (Bướu cổ)",
                        "DataType": 3,
                        "Data": [
                            {
                                "PropertyValueID": 3190,
                                "InsPackagePropertyID": 2944,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 94,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3189,
                                "InsPackagePropertyID": 2944,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 94,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    },
                    {
                        "ParentPropertyID": 80,
                        "PropertyID": 95,
                        "Label": "Tiểu đường chỉ số từ 8 - 10mmol/l",
                        "DataType": 3,
                        "Data": [
                            {
                                "PropertyValueID": 3191,
                                "InsPackagePropertyID": 2945,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 95,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3192,
                                "InsPackagePropertyID": 2945,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 95,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    },
                    {
                        "ParentPropertyID": 80,
                        "PropertyID": 96,
                        "Label": "U xơ tử cung",
                        "DataType": 3,
                        "Data": [
                            {
                                "PropertyValueID": 3193,
                                "InsPackagePropertyID": 2946,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 96,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3194,
                                "InsPackagePropertyID": 2946,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 96,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    },
                    {
                        "ParentPropertyID": 80,
                        "PropertyID": 97,
                        "Label": "U nang buồng trứng",
                        "DataType": 3,
                        "Data": [
                            {
                                "PropertyValueID": 3195,
                                "InsPackagePropertyID": 2947,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 97,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3196,
                                "InsPackagePropertyID": 2947,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 97,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    },
                    {
                        "ParentPropertyID": 80,
                        "PropertyID": 98,
                        "Label": "U xơ tiền liệt",
                        "DataType": 3,
                        "Data": [
                            {
                                "PropertyValueID": 3197,
                                "InsPackagePropertyID": 2948,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 98,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3198,
                                "InsPackagePropertyID": 2948,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 98,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    },
                    {
                        "ParentPropertyID": 80,
                        "PropertyID": 99,
                        "Label": "Rối loạn đông máu",
                        "DataType": 3,
                        "Data": [
                            {
                                "PropertyValueID": 3199,
                                "InsPackagePropertyID": 2949,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 99,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3200,
                                "InsPackagePropertyID": 2949,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 99,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    },
                    {
                        "ParentPropertyID": 80,
                        "PropertyID": 100,
                        "Label": "Xơ cứng bì toàn thân",
                        "DataType": 3,
                        "Data": [
                            {
                                "PropertyValueID": 3201,
                                "InsPackagePropertyID": 2950,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 100,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3202,
                                "InsPackagePropertyID": 2950,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 100,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    },
                    {
                        "ParentPropertyID": 80,
                        "PropertyID": 101,
                        "Label": "Loãng xương",
                        "DataType": 3,
                        "Data": [
                            {
                                "PropertyValueID": 3203,
                                "InsPackagePropertyID": 2951,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 101,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3204,
                                "InsPackagePropertyID": 2951,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 101,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    },
                    {
                        "ParentPropertyID": 80,
                        "PropertyID": 102,
                        "Label": "Điếc",
                        "DataType": 3,
                        "Data": [
                            {
                                "PropertyValueID": 3205,
                                "InsPackagePropertyID": 2952,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 102,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3206,
                                "InsPackagePropertyID": 2952,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 102,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    },
                    {
                        "ParentPropertyID": 80,
                        "PropertyID": 103,
                        "Label": "Bệnh hệ thống tạo keo (Collageno)",
                        "DataType": 3,
                        "Data": [
                            {
                                "PropertyValueID": 3207,
                                "InsPackagePropertyID": 2953,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 103,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3208,
                                "InsPackagePropertyID": 2953,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 103,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    },
                    {
                        "ParentPropertyID": 80,
                        "PropertyID": 104,
                        "Label": "Hội chứng khô mắt",
                        "DataType": 3,
                        "Data": [
                            {
                                "PropertyValueID": 0,
                                "InsPackagePropertyID": 0,
                                "Value": "",
                                "PartnerValue": "",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 104,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    },
                    {
                        "ParentPropertyID": 80,
                        "PropertyID": 105,
                        "Label": "Đục thủy tinh thể",
                        "DataType": 3,
                        "Data": [
                            {
                                "PropertyValueID": 3209,
                                "InsPackagePropertyID": 2955,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 105,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3210,
                                "InsPackagePropertyID": 2955,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 105,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    }
                ],
                "ChildLabel": "Danh sách nhóm bệnh 4",
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 81,
                "Label": "Câu 2.5. NĐBH bị bệnh nhưng không có trong danh sách bệnh từ câu 1 đến câu 4",
                "DataType": 3,
                "Data": [
                    {
                        "PropertyValueID": 3120,
                        "InsPackagePropertyID": 2931,
                        "Value": "Có",
                        "PartnerValue": "C",
                        "IsDefault": false,
                        "OrderIndex": 0,
                        "cus_PropertyID": 81,
                        "cus_InsPackageID": 345,
                        "IsSelected": false
                    },
                    {
                        "PropertyValueID": 3121,
                        "InsPackagePropertyID": 2931,
                        "Value": "Không",
                        "PartnerValue": "K",
                        "IsDefault": false,
                        "OrderIndex": 1,
                        "cus_PropertyID": 81,
                        "cus_InsPackageID": 345,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "PACKAGE",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            }
        ],
        "ChildProperty": null
    },
    {
        "InsInforGroupID": 8,
        "GroupName": "Thông tin người được bảo hiểm",
        "Step": 2,
        "OrderIndex": 2,
        "ParentGroupID": 0,
        "AirtimeTransactionTypeID": 1792,
        "InsPackageID": 0,
        "CreatedUser": "165065",
        "CreatedDate": "2024-04-01T16:24:06",
        "PropertyList": [
            {
                "ParentPropertyID": null,
                "PropertyID": 110,
                "Label": "Giới tính người được bảo hiểm",
                "DataType": 3,
                "Data": [
                    {
                        "BusinesTypePropertyValueID": 46479,
                        "BusinesTypePropertyID": 111,
                        "Value": "Nam",
                        "PartnerValue": "1",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 110,
                        "IsSelected": false
                    },
                    {
                        "BusinesTypePropertyValueID": 46480,
                        "BusinesTypePropertyID": 111,
                        "Value": "Nữ",
                        "PartnerValue": "2",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 110,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "BUSINESS",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 111,
                "Label": "Số CMND/ CCCD",
                "DataType": 0,
                "Data": [
                    {
                        "BusinesTypePropertyValueID": 0,
                        "BusinesTypePropertyID": 0,
                        "Value": "",
                        "PartnerValue": "",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 111,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "BUSINESS",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 112,
                "Label": "Ngày sinh người được bảo hiểm",
                "DataType": 2,
                "Data": [
                    {
                        "BusinesTypePropertyValueID": 0,
                        "BusinesTypePropertyID": 0,
                        "Value": "",
                        "PartnerValue": "",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 112,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "BUSINESS",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 113,
                "Label": "Địa chỉ người được bảo hiểm",
                "DataType": 4,
                "Data": [
                    {
                        "BusinesTypePropertyValueID": 0,
                        "BusinesTypePropertyID": 0,
                        "Value": "",
                        "PartnerValue": "",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 113,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "BUSINESS",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 114,
                "Label": "Số nhà người được bảo hiểm",
                "DataType": 0,
                "Data": [
                    {
                        "BusinesTypePropertyValueID": 0,
                        "BusinesTypePropertyID": 0,
                        "Value": "",
                        "PartnerValue": "",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 114,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "BUSINESS",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 115,
                "Label": "Email người được bảo hiểm",
                "DataType": 0,
                "Data": [
                    {
                        "BusinesTypePropertyValueID": 0,
                        "BusinesTypePropertyID": 0,
                        "Value": "",
                        "PartnerValue": "",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 115,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "BUSINESS",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 9,
                "Label": "Họ tên khách hàng",
                "DataType": 0,
                "Data": [
                    {
                        "BusinesTypePropertyValueID": 0,
                        "BusinesTypePropertyID": 0,
                        "Value": "",
                        "PartnerValue": "",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 9,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "BUSINESS",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 10,
                "Label": "Số điện thoại",
                "DataType": 0,
                "Data": [
                    {
                        "BusinesTypePropertyValueID": 0,
                        "BusinesTypePropertyID": 0,
                        "Value": "",
                        "PartnerValue": "",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 10,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "BUSINESS",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            }
        ],
        "ChildProperty": null
    },
    {
        "InsInforGroupID": 4,
        "GroupName": "Điều khoản chính",
        "Step": 1,
        "OrderIndex": 3,
        "ParentGroupID": 0,
        "AirtimeTransactionTypeID": 1792,
        "InsPackageID": 0,
        "CreatedUser": "165065",
        "CreatedDate": "2024-04-01T16:19:27",
        "PropertyList": [
            {
                "ParentPropertyID": null,
                "PropertyID": 73,
                "Label": "Phí điều khoản chính",
                "DataType": 1,
                "Data": [
                    {
                        "BusinesTypePropertyValueID": 0,
                        "BusinesTypePropertyID": 0,
                        "Value": "",
                        "PartnerValue": "",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 73,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "BUSINESS",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            }
        ],
        "ChildProperty": null
    },
    {
        "InsInforGroupID": 5,
        "GroupName": "Điều khoản bổ sung",
        "Step": 1,
        "OrderIndex": 4,
        "ParentGroupID": 0,
        "AirtimeTransactionTypeID": 1792,
        "InsPackageID": 0,
        "CreatedUser": "165065",
        "CreatedDate": "2024-04-01T16:20:07",
        "PropertyList": [
            {
                "ParentPropertyID": null,
                "PropertyID": 69,
                "Label": "Điều trị ngoại trú do ốm đau, bệnh tật",
                "DataType": 1,
                "Data": [
                    {
                        "BusinesTypePropertyValueID": 0,
                        "BusinesTypePropertyID": 0,
                        "Value": "",
                        "PartnerValue": "",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 69,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "BUSINESS",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 70,
                "Label": "Quyền lợi nha khoa",
                "DataType": 1,
                "Data": [
                    {
                        "BusinesTypePropertyValueID": 0,
                        "BusinesTypePropertyID": 0,
                        "Value": "",
                        "PartnerValue": "",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 70,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "BUSINESS",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 71,
                "Label": "Quyền lợi thai sản",
                "DataType": 1,
                "Data": [
                    {
                        "BusinesTypePropertyValueID": 0,
                        "BusinesTypePropertyID": 0,
                        "Value": "",
                        "PartnerValue": "",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 71,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "BUSINESS",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            },
            {
                "ParentPropertyID": null,
                "PropertyID": 72,
                "Label": "Tử vong, thương tật toàn bộ vĩnh viễn không do nguyên nhân tai nạn",
                "DataType": 1,
                "Data": [
                    {
                        "BusinesTypePropertyValueID": 0,
                        "BusinesTypePropertyID": 0,
                        "Value": "",
                        "PartnerValue": "",
                        "AirTimeTransactionTypeID": 0,
                        "OrderIndex": 0,
                        "ParentBizTypePropValueID": null,
                        "cus_PropertyID": 72,
                        "IsSelected": false
                    }
                ],
                "PropertyType": "BUSINESS",
                "ChildProperty": null,
                "ChildLabel": null,
                "Value": null
            }
        ],
        "ChildProperty": null
    }
]

const sampleGetFeeBody = {
    "loginStoreId": 888,
    "languageID": 2,
    "moduleID": 1,
    "catalogID": 3,
    "serviceGroupID": 30,
    "airtimeTransactionTypeID": 1792,
    "productPromotion": {},
    "extraData": {
        "ListProperty": [
            {
                "InsInforGroupID": 1,
                "GroupName": "Phạm vi bảo hiểm",
                "Step": 1,
                "OrderIndex": 0,
                "ParentGroupID": 0,
                "AirtimeTransactionTypeID": 1792,
                "InsPackageID": 0,
                "CreatedUser": "165065",
                "CreatedDate": "2024-04-01T16:08:18",
                "PropertyList": [
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 64,
                        "Label": "Loại hợp đồng",
                        "DataType": 3,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "PropertyValueID": 3101,
                                "InsPackagePropertyID": 2920,
                                "Value": "Mới",
                                "PartnerValue": "G",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 64,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3102,
                                "InsPackagePropertyID": 2920,
                                "Value": "Tái ký",
                                "PartnerValue": "T",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 64,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": 3101
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 65,
                        "Label": "Số hợp đồng tái ký",
                        "DataType": 0,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "PropertyValueID": 0,
                                "InsPackagePropertyID": 2921,
                                "Value": "",
                                "PartnerValue": "",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 65,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": "131312"
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 66,
                        "Label": "Chương trình bảo hiểm",
                        "DataType": 4,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "PropertyValueID": 3103,
                                "InsPackagePropertyID": 2922,
                                "Value": "Đồng",
                                "PartnerValue": "1",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 66,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3104,
                                "InsPackagePropertyID": 2922,
                                "Value": "Bạc",
                                "PartnerValue": "2",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 66,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3105,
                                "InsPackagePropertyID": 2922,
                                "Value": "Vàng",
                                "PartnerValue": "3",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 66,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3106,
                                "InsPackagePropertyID": 2922,
                                "Value": "Bạch Kim",
                                "PartnerValue": "4",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 66,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3107,
                                "InsPackagePropertyID": 2922,
                                "Value": "Kim Cương",
                                "PartnerValue": "5",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 66,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": 3105
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 112,
                        "Label": "Ngày sinh người được bảo hiểm",
                        "DataType": 2,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "BusinesTypePropertyValueID": 0,
                                "BusinesTypePropertyID": 113,
                                "Value": "",
                                "PartnerValue": "",
                                "AirTimeTransactionTypeID": 0,
                                "OrderIndex": 0,
                                "ParentBizTypePropValueID": null,
                                "cus_PropertyID": 112,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "BUSINESS",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": "12/11/2000"
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 4,
                        "Label": "Ngày hiệu lực",
                        "DataType": 2,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "BusinesTypePropertyValueID": 0,
                                "BusinesTypePropertyID": 118,
                                "Value": "",
                                "PartnerValue": "",
                                "AirTimeTransactionTypeID": 0,
                                "OrderIndex": 0,
                                "ParentBizTypePropValueID": null,
                                "cus_PropertyID": 4,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "BUSINESS",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": "04/05/2024"
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 5,
                        "Label": "Ngày kết thúc",
                        "DataType": 2,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "BusinesTypePropertyValueID": 0,
                                "BusinesTypePropertyID": 119,
                                "Value": "",
                                "PartnerValue": "",
                                "AirTimeTransactionTypeID": 0,
                                "OrderIndex": 0,
                                "ParentBizTypePropValueID": null,
                                "cus_PropertyID": 5,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "BUSINESS",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": "04/05/2025"
                    }
                ],
                "ChildProperty": null,
                "groupIndex": 0,
                "title": "Phạm vi bảo hiểm",
                "data": [
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 64,
                        "Label": "Loại hợp đồng",
                        "DataType": 3,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "PropertyValueID": 3101,
                                "InsPackagePropertyID": 2920,
                                "Value": "Mới",
                                "PartnerValue": "G",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 64,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3102,
                                "InsPackagePropertyID": 2920,
                                "Value": "Tái ký",
                                "PartnerValue": "T",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 64,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": 3101
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 65,
                        "Label": "Số hợp đồng tái ký",
                        "DataType": 0,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "PropertyValueID": 0,
                                "InsPackagePropertyID": 2921,
                                "Value": "",
                                "PartnerValue": "",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 65,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": "131312"
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 66,
                        "Label": "Chương trình bảo hiểm",
                        "DataType": 4,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "PropertyValueID": 3103,
                                "InsPackagePropertyID": 2922,
                                "Value": "Đồng",
                                "PartnerValue": "1",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 66,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3104,
                                "InsPackagePropertyID": 2922,
                                "Value": "Bạc",
                                "PartnerValue": "2",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 66,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3105,
                                "InsPackagePropertyID": 2922,
                                "Value": "Vàng",
                                "PartnerValue": "3",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 66,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3106,
                                "InsPackagePropertyID": 2922,
                                "Value": "Bạch Kim",
                                "PartnerValue": "4",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 66,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3107,
                                "InsPackagePropertyID": 2922,
                                "Value": "Kim Cương",
                                "PartnerValue": "5",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 66,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": 3105
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 112,
                        "Label": "Ngày sinh người được bảo hiểm",
                        "DataType": 2,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "BusinesTypePropertyValueID": 0,
                                "BusinesTypePropertyID": 113,
                                "Value": "",
                                "PartnerValue": "",
                                "AirTimeTransactionTypeID": 0,
                                "OrderIndex": 0,
                                "ParentBizTypePropValueID": null,
                                "cus_PropertyID": 112,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "BUSINESS",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": "12/11/2000"
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 4,
                        "Label": "Ngày hiệu lực",
                        "DataType": 2,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "BusinesTypePropertyValueID": 0,
                                "BusinesTypePropertyID": 118,
                                "Value": "",
                                "PartnerValue": "",
                                "AirTimeTransactionTypeID": 0,
                                "OrderIndex": 0,
                                "ParentBizTypePropValueID": null,
                                "cus_PropertyID": 4,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "BUSINESS",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": "04/05/2024"
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 5,
                        "Label": "Ngày kết thúc",
                        "DataType": 2,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "BusinesTypePropertyValueID": 0,
                                "BusinesTypePropertyID": 119,
                                "Value": "",
                                "PartnerValue": "",
                                "AirTimeTransactionTypeID": 0,
                                "OrderIndex": 0,
                                "ParentBizTypePropValueID": null,
                                "cus_PropertyID": 5,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "BUSINESS",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": "04/05/2025"
                    }
                ]
            },
            {
                "InsInforGroupID": 2,
                "GroupName": "Thông tin bố mẹ",
                "Step": 1,
                "OrderIndex": 1,
                "ParentGroupID": 0,
                "AirtimeTransactionTypeID": 1792,
                "InsPackageID": 0,
                "CreatedUser": "165065",
                "CreatedDate": "2024-04-01T16:16:20",
                "PropertyList": [
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 68,
                        "Label": "Số hợp đồng (bố/mẹ) cho trường hợp trẻ em mua cùng người lớn",
                        "DataType": 0,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "PropertyValueID": 0,
                                "InsPackagePropertyID": 2924,
                                "Value": "",
                                "PartnerValue": "",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 68,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": "123"
                    }
                ],
                "ChildProperty": null,
                "groupIndex": 1,
                "title": "Thông tin bố mẹ",
                "data": [
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 68,
                        "Label": "Số hợp đồng (bố/mẹ) cho trường hợp trẻ em mua cùng người lớn",
                        "DataType": 0,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "PropertyValueID": 0,
                                "InsPackagePropertyID": 2924,
                                "Value": "",
                                "PartnerValue": "",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 68,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": "123"
                    }
                ]
            },
            {
                "InsInforGroupID": 3,
                "GroupName": "Thông tin tình trạng sức khỏe",
                "Step": 1,
                "OrderIndex": 2,
                "ParentGroupID": 0,
                "AirtimeTransactionTypeID": 1792,
                "InsPackageID": 0,
                "CreatedUser": "165065",
                "CreatedDate": "2024-04-01T16:17:51",
                "PropertyList": [
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 75,
                        "Label": "Câu 1:\nNgười được bảo hiểm có thuộc các trường hợp dưới đây hay không?\n- Những người bị bệnh tâm thần, bệnh phong, hội chứng down, tự kỷ;\n- Những người bị thương tật vĩnh viễn từ 50% trở lên;\n- Những người đang trong thời gian điều trị bệnh hoặc thương tật hoặc bị ung thư.\nĐiều này chỉ áp dụng đối với các trường hợp tham gia bảo hiểm năm đầu tiên.",
                        "DataType": 3,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "PropertyValueID": 3108,
                                "InsPackagePropertyID": 2925,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 75,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3109,
                                "InsPackagePropertyID": 2925,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 1,
                                "cus_PropertyID": 75,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": 3109
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 76,
                        "Label": "Câu 2: Trong vòng 3 năm qua, Người được bảo hiểm đã từng được chẩn đoán, xuất hiện triệu chứng phải đi khám, điều trị hay đã được chuyên gia y tế khuyên Người được bảo hiểm phải điều trị hay không?\nLưu ý:\nNgười được bảo hiểm không trả lời ‘CÓ” đối với các bệnh/ tình trạng y tế dưới đây:\n- Phụ nữ sinh con (sinh thường, sinh mổ) mà không có biến chứng thai sản;\n- Cúm và cảm lạnh theo mùa thông thường, viêm dạ dày cấp tính, viêm ruột thừa cấp tính, viêm amidan cấp tính, nhiễm trùng đường tiết niệu, bệnh tả, thương hàn, sốt xuất huyết mà Người được bảo hiểm đã được điều trị và đã hồi phục hoàn toàn hoặc nếu Người được bảo hiểm sử dụng bất kỳ loại thực phẩm bổ sung sức khỏe tổng quát nào.",
                        "DataType": 3,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "PropertyValueID": 3111,
                                "InsPackagePropertyID": 2926,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 76,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3110,
                                "InsPackagePropertyID": 2926,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 1,
                                "cus_PropertyID": 76,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": 3110
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 77,
                        "Label": "Câu 2.1. NĐBH có bệnh về thân kinh, bệnh về máu, da và các mô liên kết không?",
                        "DataType": 3,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "PropertyValueID": 3113,
                                "InsPackagePropertyID": 2927,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 77,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3112,
                                "InsPackagePropertyID": 2927,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 1,
                                "cus_PropertyID": 77,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": [
                            {
                                "ParentPropertyID": 77,
                                "PropertyID": 82,
                                "Label": "Danh sách nhóm bệnh 1",
                                "DataType": 3,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3122,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Viêm hệ thần kinh trung ương (Não)",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3123,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Parkinson, Alzheimer",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3124,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Thoái hóa khác của hệ thần kinh",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3125,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Mất trí nhớ, hôn mê, bại não, liệt",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3126,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Bỏng nặng từ độ III trở lên",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3127,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Hội chứng Apallic",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3128,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Phẫu thuật não",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3129,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Viêm màng não, viêm não do virus",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3130,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Bệnh tế bào thần kinh vận động",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3131,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Xơ cứng rải rác (đa xơ cứng)",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3132,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Loạn dưỡng cơ",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3133,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Nhược cơ",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3134,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Thiếu máu bất sản, thiếu máu tán huyết, thiếu máu do suy tủy",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3135,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Ghép tủy",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3136,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Suy tủy",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3137,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Lupus ban đỏ",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3138,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Teo cơ",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            }
                        ],
                        "ChildLabel": "Danh sách nhóm bệnh 1",
                        "Value": 3112
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 78,
                        "Label": "Câu 2.2. NĐBH có bị bệnh về hệ hô hấp và hệ tuần hoàn, hệ tiêu hóa, hệ tiết niệu không?",
                        "DataType": 3,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "PropertyValueID": 3114,
                                "InsPackagePropertyID": 2928,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 78,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3115,
                                "InsPackagePropertyID": 2928,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 1,
                                "cus_PropertyID": 78,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": [
                            {
                                "ParentPropertyID": 78,
                                "PropertyID": 83,
                                "Label": "Danh sách nhóm bệnh2",
                                "DataType": 3,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3139,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Suy phổi, tràn khí phổi, suy hô hấp mãn tính",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3140,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Phẫu thuật cắt bỏ 1 bên phổi",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3141,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Tăng áp động mạch phổi",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3142,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Bệnh phổi giai đoạn cuối",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3143,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Mạch máu não/đột quỵ (xuất huyết não, xơ cứng động mạch)",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3144,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Nhồi máu cơ tim, suy tim mất bù, bệnh tim giai đoạn cuối",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3145,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Phẫu thuật động mạch chủ/van tim, ghép tim",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3146,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Phẫu thuật nối tắt động mạch vành",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3147,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Viêm gan siêu vi tối cấp",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3148,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Xơ gan",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3149,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Bệnh Crohn",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3150,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Phẫu thuật gan",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3151,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Suy gan (bệnh gan giai đoạn cuối)",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3152,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Suy thận, teo thận, sỏi thận cả 2 bên",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3153,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Chạy thận nhân tạo",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3154,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Suy tuyến thượng thận mãn tính (Addison)",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            }
                        ],
                        "ChildLabel": "Danh sách nhóm bệnh2",
                        "Value": 3115
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 79,
                        "Label": "Câu 2.3. NĐBH có bị bệnh về cơ xương khớp, hệ nội tiết, dinh dưỡng, chuyển hóa, khối u và các bệnh khác không?",
                        "DataType": 3,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "PropertyValueID": 3117,
                                "InsPackagePropertyID": 2929,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 79,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3116,
                                "InsPackagePropertyID": 2929,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 1,
                                "cus_PropertyID": 79,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": [
                            {
                                "ParentPropertyID": 79,
                                "PropertyID": 84,
                                "Label": "Danh sách nhóm bệnh 3",
                                "DataType": 3,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3155,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Viêm đa khớp dạng thấp nặng",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3156,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Loãng xương mức độ nặng",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3157,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Ghép tủy xương",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3158,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Tiểu đường chỉ số trên 11mmol/l\nTiểu đường đã gây biến chứng",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3159,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "U thượng thận trái (đã cắt hoặc chưa cắt)",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3160,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Ung thư các loại",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3161,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Lao các loại",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3162,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Phong",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3163,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Bạch cầu",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3164,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Các bệnh lây qua đường tình dục: Giang mai, lậu, hội chứng suy giảm miễn dịch",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3165,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Bệnh bẩm sinh, di truyền, dị dạng về gen",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3166,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Nang ở tủy thận",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3167,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Hội chứng Đao (Down)",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3168,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Mù 1 mắt trở lên",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3169,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Gai đôi cột sống",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3170,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Suy đa tạng",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            }
                        ],
                        "ChildLabel": "Danh sách nhóm bệnh 3",
                        "Value": 3116
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 80,
                        "Label": "Câu 2.4. NĐBH có các bệnh lý ví dụ như: Bỏng dưới độ III, Tim, Viêm gan A….không?",
                        "DataType": 3,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "PropertyValueID": 3119,
                                "InsPackagePropertyID": 2930,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 80,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3118,
                                "InsPackagePropertyID": 2930,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 1,
                                "cus_PropertyID": 80,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": [
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 85,
                                "Label": "Bỏng dưới độ III",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3171,
                                        "InsPackagePropertyID": 2935,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 85,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3172,
                                        "InsPackagePropertyID": 2935,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 85,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 86,
                                "Label": "Tim",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3173,
                                        "InsPackagePropertyID": 2936,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 86,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3174,
                                        "InsPackagePropertyID": 2936,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 86,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 87,
                                "Label": "Tăng áp lực động mạch vành vô căn",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3175,
                                        "InsPackagePropertyID": 2937,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 87,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3176,
                                        "InsPackagePropertyID": 2937,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 87,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 88,
                                "Label": "Viêm gan A",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3177,
                                        "InsPackagePropertyID": 2938,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 88,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3178,
                                        "InsPackagePropertyID": 2938,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 88,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 89,
                                "Label": "Viêm gan B",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3179,
                                        "InsPackagePropertyID": 2939,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 89,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3180,
                                        "InsPackagePropertyID": 2939,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 89,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 90,
                                "Label": "Viêm gan C",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3181,
                                        "InsPackagePropertyID": 2940,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 90,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3182,
                                        "InsPackagePropertyID": 2940,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 90,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 91,
                                "Label": "Rối loạn tuyến giáp",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3183,
                                        "InsPackagePropertyID": 2941,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 91,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3184,
                                        "InsPackagePropertyID": 2941,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 91,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 92,
                                "Label": "Cường giáp",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3185,
                                        "InsPackagePropertyID": 2942,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 92,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3186,
                                        "InsPackagePropertyID": 2942,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 92,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 93,
                                "Label": "Suy giáp",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3187,
                                        "InsPackagePropertyID": 2943,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 93,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3188,
                                        "InsPackagePropertyID": 2943,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 93,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 94,
                                "Label": "Basedow (Bướu cổ)",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3190,
                                        "InsPackagePropertyID": 2944,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 94,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3189,
                                        "InsPackagePropertyID": 2944,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 94,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 95,
                                "Label": "Tiểu đường chỉ số từ 8 - 10mmol/l",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3191,
                                        "InsPackagePropertyID": 2945,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 95,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3192,
                                        "InsPackagePropertyID": 2945,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 95,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 96,
                                "Label": "U xơ tử cung",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3193,
                                        "InsPackagePropertyID": 2946,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 96,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3194,
                                        "InsPackagePropertyID": 2946,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 96,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 97,
                                "Label": "U nang buồng trứng",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3195,
                                        "InsPackagePropertyID": 2947,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 97,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3196,
                                        "InsPackagePropertyID": 2947,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 97,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 98,
                                "Label": "U xơ tiền liệt",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3197,
                                        "InsPackagePropertyID": 2948,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 98,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3198,
                                        "InsPackagePropertyID": 2948,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 98,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 99,
                                "Label": "Rối loạn đông máu",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3199,
                                        "InsPackagePropertyID": 2949,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 99,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3200,
                                        "InsPackagePropertyID": 2949,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 99,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 100,
                                "Label": "Xơ cứng bì toàn thân",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3201,
                                        "InsPackagePropertyID": 2950,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 100,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3202,
                                        "InsPackagePropertyID": 2950,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 100,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 101,
                                "Label": "Loãng xương",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3203,
                                        "InsPackagePropertyID": 2951,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 101,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3204,
                                        "InsPackagePropertyID": 2951,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 101,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 102,
                                "Label": "Điếc",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3205,
                                        "InsPackagePropertyID": 2952,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 102,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3206,
                                        "InsPackagePropertyID": 2952,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 102,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 103,
                                "Label": "Bệnh hệ thống tạo keo (Collageno)",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3207,
                                        "InsPackagePropertyID": 2953,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 103,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3208,
                                        "InsPackagePropertyID": 2953,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 103,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 104,
                                "Label": "Hội chứng khô mắt",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3213,
                                        "InsPackagePropertyID": 2954,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 104,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3214,
                                        "InsPackagePropertyID": 2954,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 1,
                                        "cus_PropertyID": 104,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 105,
                                "Label": "Đục thủy tinh thể",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3209,
                                        "InsPackagePropertyID": 2955,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 105,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3210,
                                        "InsPackagePropertyID": 2955,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 105,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            }
                        ],
                        "ChildLabel": "Danh sách nhóm bệnh 4",
                        "Value": 3118
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 106,
                        "Label": "Câu 2.5. NĐBH bị bệnh nhưng không có trong danh sách bệnh từ câu 1 đến câu 4",
                        "DataType": 0,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "PropertyValueID": 0,
                                "InsPackagePropertyID": 2956,
                                "Value": "",
                                "PartnerValue": "",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 106,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": "123"
                    }
                ],
                "ChildProperty": null,
                "groupIndex": 2,
                "title": "Thông tin tình trạng sức khỏe",
                "data": [
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 75,
                        "Label": "Câu 1:\nNgười được bảo hiểm có thuộc các trường hợp dưới đây hay không?\n- Những người bị bệnh tâm thần, bệnh phong, hội chứng down, tự kỷ;\n- Những người bị thương tật vĩnh viễn từ 50% trở lên;\n- Những người đang trong thời gian điều trị bệnh hoặc thương tật hoặc bị ung thư.\nĐiều này chỉ áp dụng đối với các trường hợp tham gia bảo hiểm năm đầu tiên.",
                        "DataType": 3,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "PropertyValueID": 3108,
                                "InsPackagePropertyID": 2925,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 75,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3109,
                                "InsPackagePropertyID": 2925,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 1,
                                "cus_PropertyID": 75,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": 3109
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 76,
                        "Label": "Câu 2: Trong vòng 3 năm qua, Người được bảo hiểm đã từng được chẩn đoán, xuất hiện triệu chứng phải đi khám, điều trị hay đã được chuyên gia y tế khuyên Người được bảo hiểm phải điều trị hay không?\nLưu ý:\nNgười được bảo hiểm không trả lời ‘CÓ” đối với các bệnh/ tình trạng y tế dưới đây:\n- Phụ nữ sinh con (sinh thường, sinh mổ) mà không có biến chứng thai sản;\n- Cúm và cảm lạnh theo mùa thông thường, viêm dạ dày cấp tính, viêm ruột thừa cấp tính, viêm amidan cấp tính, nhiễm trùng đường tiết niệu, bệnh tả, thương hàn, sốt xuất huyết mà Người được bảo hiểm đã được điều trị và đã hồi phục hoàn toàn hoặc nếu Người được bảo hiểm sử dụng bất kỳ loại thực phẩm bổ sung sức khỏe tổng quát nào.",
                        "DataType": 3,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "PropertyValueID": 3111,
                                "InsPackagePropertyID": 2926,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 76,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3110,
                                "InsPackagePropertyID": 2926,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 1,
                                "cus_PropertyID": 76,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": 3110
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 77,
                        "Label": "Câu 2.1. NĐBH có bệnh về thân kinh, bệnh về máu, da và các mô liên kết không?",
                        "DataType": 3,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "PropertyValueID": 3113,
                                "InsPackagePropertyID": 2927,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 77,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3112,
                                "InsPackagePropertyID": 2927,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 1,
                                "cus_PropertyID": 77,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": [
                            {
                                "ParentPropertyID": 77,
                                "PropertyID": 82,
                                "Label": "Danh sách nhóm bệnh 1",
                                "DataType": 3,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3122,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Viêm hệ thần kinh trung ương (Não)",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3123,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Parkinson, Alzheimer",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3124,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Thoái hóa khác của hệ thần kinh",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3125,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Mất trí nhớ, hôn mê, bại não, liệt",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3126,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Bỏng nặng từ độ III trở lên",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3127,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Hội chứng Apallic",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3128,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Phẫu thuật não",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3129,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Viêm màng não, viêm não do virus",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3130,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Bệnh tế bào thần kinh vận động",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3131,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Xơ cứng rải rác (đa xơ cứng)",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3132,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Loạn dưỡng cơ",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3133,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Nhược cơ",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3134,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Thiếu máu bất sản, thiếu máu tán huyết, thiếu máu do suy tủy",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3135,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Ghép tủy",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3136,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Suy tủy",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3137,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Lupus ban đỏ",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3138,
                                        "InsPackagePropertyID": 2932,
                                        "Value": "Teo cơ",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 82,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            }
                        ],
                        "ChildLabel": "Danh sách nhóm bệnh 1",
                        "Value": 3112
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 78,
                        "Label": "Câu 2.2. NĐBH có bị bệnh về hệ hô hấp và hệ tuần hoàn, hệ tiêu hóa, hệ tiết niệu không?",
                        "DataType": 3,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "PropertyValueID": 3114,
                                "InsPackagePropertyID": 2928,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 78,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3115,
                                "InsPackagePropertyID": 2928,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 1,
                                "cus_PropertyID": 78,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": [
                            {
                                "ParentPropertyID": 78,
                                "PropertyID": 83,
                                "Label": "Danh sách nhóm bệnh2",
                                "DataType": 3,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3139,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Suy phổi, tràn khí phổi, suy hô hấp mãn tính",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3140,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Phẫu thuật cắt bỏ 1 bên phổi",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3141,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Tăng áp động mạch phổi",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3142,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Bệnh phổi giai đoạn cuối",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3143,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Mạch máu não/đột quỵ (xuất huyết não, xơ cứng động mạch)",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3144,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Nhồi máu cơ tim, suy tim mất bù, bệnh tim giai đoạn cuối",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3145,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Phẫu thuật động mạch chủ/van tim, ghép tim",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3146,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Phẫu thuật nối tắt động mạch vành",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3147,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Viêm gan siêu vi tối cấp",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3148,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Xơ gan",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3149,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Bệnh Crohn",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3150,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Phẫu thuật gan",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3151,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Suy gan (bệnh gan giai đoạn cuối)",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3152,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Suy thận, teo thận, sỏi thận cả 2 bên",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3153,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Chạy thận nhân tạo",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3154,
                                        "InsPackagePropertyID": 2933,
                                        "Value": "Suy tuyến thượng thận mãn tính (Addison)",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 83,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            }
                        ],
                        "ChildLabel": "Danh sách nhóm bệnh2",
                        "Value": 3115
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 79,
                        "Label": "Câu 2.3. NĐBH có bị bệnh về cơ xương khớp, hệ nội tiết, dinh dưỡng, chuyển hóa, khối u và các bệnh khác không?",
                        "DataType": 3,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "PropertyValueID": 3117,
                                "InsPackagePropertyID": 2929,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 79,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3116,
                                "InsPackagePropertyID": 2929,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 1,
                                "cus_PropertyID": 79,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": [
                            {
                                "ParentPropertyID": 79,
                                "PropertyID": 84,
                                "Label": "Danh sách nhóm bệnh 3",
                                "DataType": 3,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3155,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Viêm đa khớp dạng thấp nặng",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3156,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Loãng xương mức độ nặng",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3157,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Ghép tủy xương",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3158,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Tiểu đường chỉ số trên 11mmol/l\nTiểu đường đã gây biến chứng",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3159,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "U thượng thận trái (đã cắt hoặc chưa cắt)",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3160,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Ung thư các loại",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3161,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Lao các loại",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3162,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Phong",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3163,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Bạch cầu",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3164,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Các bệnh lây qua đường tình dục: Giang mai, lậu, hội chứng suy giảm miễn dịch",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3165,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Bệnh bẩm sinh, di truyền, dị dạng về gen",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3166,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Nang ở tủy thận",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3167,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Hội chứng Đao (Down)",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3168,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Mù 1 mắt trở lên",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3169,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Gai đôi cột sống",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3170,
                                        "InsPackagePropertyID": 2934,
                                        "Value": "Suy đa tạng",
                                        "PartnerValue": "null",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 84,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            }
                        ],
                        "ChildLabel": "Danh sách nhóm bệnh 3",
                        "Value": 3116
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 80,
                        "Label": "Câu 2.4. NĐBH có các bệnh lý ví dụ như: Bỏng dưới độ III, Tim, Viêm gan A….không?",
                        "DataType": 3,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "PropertyValueID": 3119,
                                "InsPackagePropertyID": 2930,
                                "Value": "Có",
                                "PartnerValue": "C",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 80,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            },
                            {
                                "PropertyValueID": 3118,
                                "InsPackagePropertyID": 2930,
                                "Value": "Không",
                                "PartnerValue": "K",
                                "IsDefault": false,
                                "OrderIndex": 1,
                                "cus_PropertyID": 80,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": [
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 85,
                                "Label": "Bỏng dưới độ III",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3171,
                                        "InsPackagePropertyID": 2935,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 85,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3172,
                                        "InsPackagePropertyID": 2935,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 85,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 86,
                                "Label": "Tim",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3173,
                                        "InsPackagePropertyID": 2936,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 86,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3174,
                                        "InsPackagePropertyID": 2936,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 86,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 87,
                                "Label": "Tăng áp lực động mạch vành vô căn",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3175,
                                        "InsPackagePropertyID": 2937,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 87,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3176,
                                        "InsPackagePropertyID": 2937,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 87,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 88,
                                "Label": "Viêm gan A",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3177,
                                        "InsPackagePropertyID": 2938,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 88,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3178,
                                        "InsPackagePropertyID": 2938,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 88,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 89,
                                "Label": "Viêm gan B",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3179,
                                        "InsPackagePropertyID": 2939,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 89,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3180,
                                        "InsPackagePropertyID": 2939,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 89,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 90,
                                "Label": "Viêm gan C",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3181,
                                        "InsPackagePropertyID": 2940,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 90,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3182,
                                        "InsPackagePropertyID": 2940,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 90,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 91,
                                "Label": "Rối loạn tuyến giáp",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3183,
                                        "InsPackagePropertyID": 2941,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 91,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3184,
                                        "InsPackagePropertyID": 2941,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 91,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 92,
                                "Label": "Cường giáp",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3185,
                                        "InsPackagePropertyID": 2942,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 92,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3186,
                                        "InsPackagePropertyID": 2942,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 92,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 93,
                                "Label": "Suy giáp",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3187,
                                        "InsPackagePropertyID": 2943,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 93,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3188,
                                        "InsPackagePropertyID": 2943,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 93,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 94,
                                "Label": "Basedow (Bướu cổ)",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3190,
                                        "InsPackagePropertyID": 2944,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 94,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3189,
                                        "InsPackagePropertyID": 2944,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 94,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 95,
                                "Label": "Tiểu đường chỉ số từ 8 - 10mmol/l",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3191,
                                        "InsPackagePropertyID": 2945,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 95,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3192,
                                        "InsPackagePropertyID": 2945,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 95,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 96,
                                "Label": "U xơ tử cung",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3193,
                                        "InsPackagePropertyID": 2946,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 96,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3194,
                                        "InsPackagePropertyID": 2946,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 96,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 97,
                                "Label": "U nang buồng trứng",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3195,
                                        "InsPackagePropertyID": 2947,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 97,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3196,
                                        "InsPackagePropertyID": 2947,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 97,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 98,
                                "Label": "U xơ tiền liệt",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3197,
                                        "InsPackagePropertyID": 2948,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 98,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3198,
                                        "InsPackagePropertyID": 2948,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 98,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 99,
                                "Label": "Rối loạn đông máu",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3199,
                                        "InsPackagePropertyID": 2949,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 99,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3200,
                                        "InsPackagePropertyID": 2949,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 99,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 100,
                                "Label": "Xơ cứng bì toàn thân",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3201,
                                        "InsPackagePropertyID": 2950,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 100,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3202,
                                        "InsPackagePropertyID": 2950,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 100,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 101,
                                "Label": "Loãng xương",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3203,
                                        "InsPackagePropertyID": 2951,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 101,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3204,
                                        "InsPackagePropertyID": 2951,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 101,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 102,
                                "Label": "Điếc",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3205,
                                        "InsPackagePropertyID": 2952,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 102,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3206,
                                        "InsPackagePropertyID": 2952,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 102,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 103,
                                "Label": "Bệnh hệ thống tạo keo (Collageno)",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3207,
                                        "InsPackagePropertyID": 2953,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 103,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3208,
                                        "InsPackagePropertyID": 2953,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 103,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 104,
                                "Label": "Hội chứng khô mắt",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3213,
                                        "InsPackagePropertyID": 2954,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 104,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3214,
                                        "InsPackagePropertyID": 2954,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 1,
                                        "cus_PropertyID": 104,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            },
                            {
                                "ParentPropertyID": 80,
                                "PropertyID": 105,
                                "Label": "Đục thủy tinh thể",
                                "DataType": 5,
                                "IsObliged": 1,
                                "Data": [
                                    {
                                        "PropertyValueID": 3209,
                                        "InsPackagePropertyID": 2955,
                                        "Value": "Có",
                                        "PartnerValue": "C",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 105,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    },
                                    {
                                        "PropertyValueID": 3210,
                                        "InsPackagePropertyID": 2955,
                                        "Value": "Không",
                                        "PartnerValue": "K",
                                        "IsDefault": false,
                                        "OrderIndex": 0,
                                        "cus_PropertyID": 105,
                                        "cus_InsPackageID": 345,
                                        "IsSelected": false
                                    }
                                ],
                                "PropertyType": "PACKAGE",
                                "ChildProperty": null,
                                "ChildLabel": null,
                                "Value": null
                            }
                        ],
                        "ChildLabel": "Danh sách nhóm bệnh 4",
                        "Value": 3118
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 106,
                        "Label": "Câu 2.5. NĐBH bị bệnh nhưng không có trong danh sách bệnh từ câu 1 đến câu 4",
                        "DataType": 0,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "PropertyValueID": 0,
                                "InsPackagePropertyID": 2956,
                                "Value": "",
                                "PartnerValue": "",
                                "IsDefault": false,
                                "OrderIndex": 0,
                                "cus_PropertyID": 106,
                                "cus_InsPackageID": 345,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "PACKAGE",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": "123"
                    }
                ]
            },
            {
                "InsInforGroupID": 4,
                "GroupName": "Điều khoản chính",
                "Step": 1,
                "OrderIndex": 3,
                "ParentGroupID": 0,
                "AirtimeTransactionTypeID": 1792,
                "InsPackageID": 0,
                "CreatedUser": "165065",
                "CreatedDate": "2024-04-01T16:19:27",
                "PropertyList": [
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 73,
                        "Label": "Phí điều khoản chính",
                        "DataType": 1,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "BusinesTypePropertyValueID": 0,
                                "BusinesTypePropertyID": 132,
                                "Value": "",
                                "PartnerValue": "",
                                "AirTimeTransactionTypeID": 0,
                                "OrderIndex": 0,
                                "ParentBizTypePropValueID": null,
                                "cus_PropertyID": 73,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "BUSINESS",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    }
                ],
                "ChildProperty": null,
                "groupIndex": 3,
                "title": "Điều khoản chính",
                "data": [
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 73,
                        "Label": "Phí điều khoản chính",
                        "DataType": 1,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "BusinesTypePropertyValueID": 0,
                                "BusinesTypePropertyID": 132,
                                "Value": "",
                                "PartnerValue": "",
                                "AirTimeTransactionTypeID": 0,
                                "OrderIndex": 0,
                                "ParentBizTypePropValueID": null,
                                "cus_PropertyID": 73,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "BUSINESS",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    }
                ]
            },
            {
                "InsInforGroupID": 5,
                "GroupName": "Điều khoản bổ sung",
                "Step": 1,
                "OrderIndex": 4,
                "ParentGroupID": 0,
                "AirtimeTransactionTypeID": 1792,
                "InsPackageID": 0,
                "CreatedUser": "165065",
                "CreatedDate": "2024-04-01T16:20:07",
                "PropertyList": [
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 69,
                        "Label": "Điều trị ngoại trú do ốm đau, bệnh tật",
                        "DataType": 5,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "BusinesTypePropertyValueID": 46488,
                                "BusinesTypePropertyID": 128,
                                "Value": "0",
                                "PartnerValue": "C",
                                "AirTimeTransactionTypeID": 0,
                                "OrderIndex": 0,
                                "ParentBizTypePropValueID": null,
                                "cus_PropertyID": 69,
                                "IsSelected": false
                            },
                            {
                                "BusinesTypePropertyValueID": 46489,
                                "BusinesTypePropertyID": 128,
                                "Value": "0",
                                "PartnerValue": "K",
                                "AirTimeTransactionTypeID": 0,
                                "OrderIndex": 0,
                                "ParentBizTypePropValueID": null,
                                "cus_PropertyID": 69,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "BUSINESS",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 70,
                        "Label": "Quyền lợi nha khoa",
                        "DataType": 5,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "BusinesTypePropertyValueID": 46491,
                                "BusinesTypePropertyID": 129,
                                "Value": "0",
                                "PartnerValue": "K",
                                "AirTimeTransactionTypeID": 0,
                                "OrderIndex": 0,
                                "ParentBizTypePropValueID": null,
                                "cus_PropertyID": 70,
                                "IsSelected": false
                            },
                            {
                                "BusinesTypePropertyValueID": 46490,
                                "BusinesTypePropertyID": 129,
                                "Value": "0",
                                "PartnerValue": "C",
                                "AirTimeTransactionTypeID": 0,
                                "OrderIndex": 0,
                                "ParentBizTypePropValueID": null,
                                "cus_PropertyID": 70,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "BUSINESS",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 71,
                        "Label": "Quyền lợi thai sản",
                        "DataType": 5,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "BusinesTypePropertyValueID": 46492,
                                "BusinesTypePropertyID": 130,
                                "Value": "0",
                                "PartnerValue": "C",
                                "AirTimeTransactionTypeID": 0,
                                "OrderIndex": 0,
                                "ParentBizTypePropValueID": null,
                                "cus_PropertyID": 71,
                                "IsSelected": false
                            },
                            {
                                "BusinesTypePropertyValueID": 46493,
                                "BusinesTypePropertyID": 130,
                                "Value": "0",
                                "PartnerValue": "K",
                                "AirTimeTransactionTypeID": 0,
                                "OrderIndex": 0,
                                "ParentBizTypePropValueID": null,
                                "cus_PropertyID": 71,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "BUSINESS",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 72,
                        "Label": "Tử vong, thương tật toàn bộ vĩnh viễn không do nguyên nhân tai nạn",
                        "DataType": 5,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "BusinesTypePropertyValueID": 46494,
                                "BusinesTypePropertyID": 131,
                                "Value": "0",
                                "PartnerValue": "C",
                                "AirTimeTransactionTypeID": 0,
                                "OrderIndex": 0,
                                "ParentBizTypePropValueID": null,
                                "cus_PropertyID": 72,
                                "IsSelected": false
                            },
                            {
                                "BusinesTypePropertyValueID": 46495,
                                "BusinesTypePropertyID": 131,
                                "Value": "0",
                                "PartnerValue": "K",
                                "AirTimeTransactionTypeID": 0,
                                "OrderIndex": 0,
                                "ParentBizTypePropValueID": null,
                                "cus_PropertyID": 72,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "BUSINESS",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    }
                ],
                "ChildProperty": null,
                "groupIndex": 4,
                "title": "Điều khoản bổ sung",
                "data": [
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 69,
                        "Label": "Điều trị ngoại trú do ốm đau, bệnh tật",
                        "DataType": 5,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "BusinesTypePropertyValueID": 46488,
                                "BusinesTypePropertyID": 128,
                                "Value": "0",
                                "PartnerValue": "C",
                                "AirTimeTransactionTypeID": 0,
                                "OrderIndex": 0,
                                "ParentBizTypePropValueID": null,
                                "cus_PropertyID": 69,
                                "IsSelected": false
                            },
                            {
                                "BusinesTypePropertyValueID": 46489,
                                "BusinesTypePropertyID": 128,
                                "Value": "0",
                                "PartnerValue": "K",
                                "AirTimeTransactionTypeID": 0,
                                "OrderIndex": 0,
                                "ParentBizTypePropValueID": null,
                                "cus_PropertyID": 69,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "BUSINESS",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 70,
                        "Label": "Quyền lợi nha khoa",
                        "DataType": 5,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "BusinesTypePropertyValueID": 46491,
                                "BusinesTypePropertyID": 129,
                                "Value": "0",
                                "PartnerValue": "K",
                                "AirTimeTransactionTypeID": 0,
                                "OrderIndex": 0,
                                "ParentBizTypePropValueID": null,
                                "cus_PropertyID": 70,
                                "IsSelected": false
                            },
                            {
                                "BusinesTypePropertyValueID": 46490,
                                "BusinesTypePropertyID": 129,
                                "Value": "0",
                                "PartnerValue": "C",
                                "AirTimeTransactionTypeID": 0,
                                "OrderIndex": 0,
                                "ParentBizTypePropValueID": null,
                                "cus_PropertyID": 70,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "BUSINESS",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 71,
                        "Label": "Quyền lợi thai sản",
                        "DataType": 5,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "BusinesTypePropertyValueID": 46492,
                                "BusinesTypePropertyID": 130,
                                "Value": "0",
                                "PartnerValue": "C",
                                "AirTimeTransactionTypeID": 0,
                                "OrderIndex": 0,
                                "ParentBizTypePropValueID": null,
                                "cus_PropertyID": 71,
                                "IsSelected": false
                            },
                            {
                                "BusinesTypePropertyValueID": 46493,
                                "BusinesTypePropertyID": 130,
                                "Value": "0",
                                "PartnerValue": "K",
                                "AirTimeTransactionTypeID": 0,
                                "OrderIndex": 0,
                                "ParentBizTypePropValueID": null,
                                "cus_PropertyID": 71,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "BUSINESS",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    },
                    {
                        "ParentPropertyID": null,
                        "PropertyID": 72,
                        "Label": "Tử vong, thương tật toàn bộ vĩnh viễn không do nguyên nhân tai nạn",
                        "DataType": 5,
                        "IsObliged": 1,
                        "Data": [
                            {
                                "BusinesTypePropertyValueID": 46494,
                                "BusinesTypePropertyID": 131,
                                "Value": "0",
                                "PartnerValue": "C",
                                "AirTimeTransactionTypeID": 0,
                                "OrderIndex": 0,
                                "ParentBizTypePropValueID": null,
                                "cus_PropertyID": 72,
                                "IsSelected": false
                            },
                            {
                                "BusinesTypePropertyValueID": 46495,
                                "BusinesTypePropertyID": 131,
                                "Value": "0",
                                "PartnerValue": "K",
                                "AirTimeTransactionTypeID": 0,
                                "OrderIndex": 0,
                                "ParentBizTypePropValueID": null,
                                "cus_PropertyID": 72,
                                "IsSelected": false
                            }
                        ],
                        "PropertyType": "BUSINESS",
                        "ChildProperty": null,
                        "ChildLabel": null,
                        "Value": null
                    }
                ]
            }
        ]
    }
}


