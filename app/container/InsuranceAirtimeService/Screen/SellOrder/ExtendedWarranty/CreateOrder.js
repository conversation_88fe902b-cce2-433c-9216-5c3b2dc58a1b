import {
  <PERSON><PERSON>,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import React from "react";
import * as actionPaymentOrderCreator from "../../../../SaleOrderPayment/action";
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import * as actionInsuranceAirtimeServiceCreator from "../../../action";
import Accordion from "../../../component/Button/Accordion";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { COLORS } from "../../../../../styles";
import { constants } from "../../../../../constants";
import TextField from "../../../component/Text/TextField";
import { helper } from "../../../../../common";
import { MyText, hideBlockUI, showBlockUI } from "../../../../../components";
import { translate } from "../../../../../translations";

const CreateOrder = ({
  route,
  navigation,
  itemCatalog,
  dataValidateService,
  updateHeaderAirtime,
  dataGetPrice,
  dataSO,
  actionPaymentOrder,
  actionInsuranceAirtimeService,
  itemOrderDetail,
  dataGetPromotion
}) => {
  const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog ?? "";
  const {
    customerName,
    customerPhone,
    customerHouseNumber,
    wardName,
    districName,
    provinceName,
    customer,
    updateInsMonth,
    updateInsProductID,
  } = route.params ?? "";
  const { DistrictID, ProvinceID, WardID } = customer ?? "";
  const { SaleOrderID } = dataSO ?? {};
  const {
    Imei,
    MainProductName,
    ListInsProgram,
    InsCustomerID,
    MainProductID,
    RetailPriceVAT,
  } = dataValidateService ?? {};
  const {
    SALEORDERDETAILID
  } = itemOrderDetail ?? ''

  const { AirTimeTransactionTypeName, AirTimeTransactionTypeID } =
    updateHeaderAirtime ?? "";
  const { FeeInfo, PriceInfo, TotalAmount } = dataGetPrice ?? {};
  const { PriceVAT, Amount, SalePrice, InputPrice, ExtraData, PartnerData } = PriceInfo ?? "";
  const { Fee } = FeeInfo ?? "";
  const { ListInsProgramDetail } = ListInsProgram?.[0] ?? {};
  const { InsuranceID, InsProgramID, InsuranceApplyID } = ListInsProgramDetail?.[0];
  const {
    CMD,
    SendContent,
    ResponseContent,
    SendTime,
    ResponseMessage,
    ResponseTime,
  } = PartnerData ?? "";
  const isCheckOneForOne = updateHeaderAirtime?.AirTimeTransactionTypeID == 1872

  const handleAddCart = () => {
    const productPromotion = dataGetPromotion?.ExtraData?.ProductPromotion;
    const promotionID = dataGetPromotion?.PromotionID;
    showBlockUI();
    const data = {
      catalogID: ServiceCategoryID,
      serviceGroupID: AirtimeServiceGroupID,
      airtimeTransactionTypeID: AirTimeTransactionTypeID,
      airTimeTransactionBO: {
        productid: updateInsProductID,
        amount: Amount,
        fee: Fee,
        phonenumber: Imei,
        inputPrice: InputPrice,
        salePrice: SalePrice,
        customerName: customerName,
        customerAddress: customerHouseNumber,
        customerPhone: customerPhone,
      },
      retailPriceVAT: PriceVAT,
      insuranceID: InsuranceID,
      insCustomerID: InsCustomerID,
      insProgramID: InsProgramID,
      insuranceMonth: updateInsMonth,
      insuranceApplyID: InsuranceApplyID,
      mainSaleOrderID: SaleOrderID,
      mainProductID: MainProductID,
      productPromotion: productPromotion,
      promotionID: promotionID,
      mainSaleOrderDetailID: SALEORDERDETAILID,
      partnerData: {
        CMD: CMD,
        SendContent: SendContent,
        ResponseContent: ResponseContent,
        SendTime: SendTime,
        ResponseMessage: ResponseMessage,
        ResponseTime: ResponseTime,
      },
      districtID: DistrictID,
      provinceID: ProvinceID,
      wardID: WardID,
      apartmentNumberAddress: customerHouseNumber,
    };
    actionInsuranceAirtimeService
      .addToSaleOrderCart(data)
      .then((reponse) => {
        hideBlockUI();
        goToPaymentSO(reponse);
      })
      .catch((error) => {
        Alert.alert(translate("common.notification_uppercase"), error, [
          {
            text: translate("common.btn_close"),
            onPress: hideBlockUI,
          },
        ]);
      });
  };

  const goToPaymentSO = (rpSaleOrderCart) => {
    const dataSaleOrderCart = rpSaleOrderCart.object;
    const SaleOrders = dataSaleOrderCart.SaleOrders[0];
    const { SaleOrderID } = SaleOrders;
    actionPaymentOrder
      .setDataSO({
        SaleOrderID: SaleOrderID,
        SaleOrderTypeID: 1000,
      })
      .then((success) => {
        hideBlockUI();
        navigation.replace("SaleOrderPayment");
        actionPaymentOrder.getSaleOrderPayment(SaleOrderID);
        actionPaymentOrder.getReportPrinterSocket(100);
        actionPaymentOrder.getDataQRTransaction(SaleOrderID);
      });
  };

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: COLORS.bgFFFFFF,
      }}
    >
      <KeyboardAwareScrollView
        style={{
          flex: 1,
        }}
        enableResetScrollToCoords={false}
        keyboardShouldPersistTaps={"always"}
        bounces={false}
        overScrollMode="always"
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        extraScrollHeight={60}
      >
        <SafeAreaView
          style={{
            flex: 1,
            alignItems: "center",
            justifyContent: "center",
            height: "100%",
          }}
        >
          <Accordion
            status={true}
            title={"Thông tin người mua bảo hiểm"}
            Children={
              <View
                style={{
                  backgroundColor: COLORS.bgF0F0F0,
                  width: constants.width - 10,
                  paddingHorizontal: 10,
                  paddingBottom: 10,
                }}
              >
                <View
                  style={{
                    width: "100%",
                    borderWidth: 1,
                    alignSelf: "center",
                    borderColor: "gray",
                  }}
                />
                <TextField name={"Họ và Tên"} value={customerName} />
                <TextField name={"Số điện thoại"} value={customerPhone} />
                <TextField
                  name={"Địa chỉ"}
                  value={`${customerHouseNumber}, ${wardName}, ${districName}, ${provinceName}`}
                />
              </View>
            }
          />
          <Accordion
            status={true}
            title={isCheckOneForOne ? "Thông tin gói bảo hiểm" : "Thông tin gói bảo hiểm mở rộng"}
            Children={
              <View
                style={{
                  backgroundColor: COLORS.bgF0F0F0,
                  width: constants.width - 10,
                  paddingHorizontal: 10,
                  paddingBottom: 10,
                }}
              >
                <View
                  style={{
                    width: "100%",
                    borderWidth: 1,
                    alignSelf: "center",
                    borderColor: "gray",
                  }}
                />
                <TextField
                  name={"Gói bảo hiểm"}
                  value={AirTimeTransactionTypeName}
                />
                <TextField
                  name={"Thời gian bảo hiểm"}
                  value={`${updateInsMonth} tháng`}
                />
                {ExtraData?.StartDateInsExtend == null ? null : (
                  <TextField
                    name={"Ngày bắt đầu hiệu lực"}
                    value={ExtraData?.StartDateInsExtend}
                  />
                )}
                {ExtraData?.EndDateInsExtend == null ? null : (
                  <TextField
                    name={"Ngày hết hạn"}
                    value={ExtraData?.EndDateInsExtend}
                  />
                )}
              </View>
            }
          />
          <Accordion
            status={true}
            title={"Thông tin thiết bị bảo hiểm"}
            Children={
              <View
                style={{
                  backgroundColor: COLORS.bgF0F0F0,
                  width: constants.width - 10,
                  paddingHorizontal: 10,
                  paddingBottom: 10,
                }}
              >
                <View
                  style={{
                    width: "100%",
                    borderWidth: 1,
                    alignSelf: "center",
                    borderColor: "gray",
                  }}
                />
                <TextField name={"IMEI"} value={Imei} />
                <TextField name={"Tên sản phẩm"} value={MainProductName} />
                <TextField
                  name={"Số tiền bảo hiểm"}
                  value={helper.formatMoney(RetailPriceVAT)}
                />
              </View>
            }
          />
          <Accordion
            status={true}
            titleColor="white"
            iconColor="white"
            title={"Thông tin thanh toán bảo hiểm"}
            backgroundColor={COLORS.bg2FB47C}
            Children={
              <View
                style={{
                  backgroundColor: COLORS.bg2FB47C,
                  width: constants.width - 10,
                  paddingBottom: 10,
                }}
              >
                <View
                  style={{
                    width: "100%",
                    borderWidth: 1,
                    alignSelf: "center",
                    borderColor: "white",
                  }}
                />
                <View
                  style={{
                    padding: 5,
                  }}
                >
                  <TextField
                    name={"Phí bảo hiểm"}
                    value={helper.formatMoney(TotalAmount)}
                    color={"white"}
                    titleColor={COLORS.bgFFFFFF}
                  />
                  <TextField
                    name={"Tổng cộng"}
                    color={"white"}
                    value={helper.formatMoney(TotalAmount)}
                    titleColor={COLORS.bgFFFFFF}
                  />
                </View>
              </View>
            }
          />
          <View
            style={{
              width: constants.width,
              height: 50,
              flexDirection: "row",
              paddingHorizontal: 10,
              marginTop: 10,
            }}
          >
            <TouchableOpacity
              onPress={() => navigation.replace("SaleOrderDetail")}
              style={{
                width: 120,
                height: 50,
                borderRadius: 18,
                borderWidth: 2,
                borderColor: COLORS.bg00A98F,
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: "white",
              }}
            >
              <MyText
                text={"QUAY LẠI"}
                style={{
                  fontWeight: "bold",
                  color: COLORS.bg00A98F,
                }}
              />
            </TouchableOpacity>
            <View
              style={{
                width: 10,
              }}
            />
            <TouchableOpacity
              onPress={() => handleAddCart()}
              style={{
                backgroundColor: "pink",
                flex: 1,
                height: 50,
                borderRadius: 18,
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: COLORS.bgF49B0C,
              }}
            >
              <MyText
                text={"TẠO ĐƠN"}
                style={{
                  fontWeight: "bold",
                  color: COLORS.bgFFFFFF,
                }}
              />
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

const mapStateToProps = function (state) {
  return {
    itemCatalog: state.collectionReducer.itemCatalog,
    dataValidateService:
      state.insuranceAirtimeServiceReducer.dataValidateService,
    updateHeaderAirtime:
      state.insuranceAirtimeServiceReducer.updateHeaderAirtime,
    dataGetPrice: state.insuranceAirtimeServiceReducer.dataGetPrice,
    dataGetPromotion: state.insuranceAirtimeServiceReducer.dataGetPromotion,
    dataSO: state.saleOrderPaymentReducer.dataSO,
    dataDetailSO: state.insuranceAirtimeServiceReducer.dataDetailSO,
    itemOrderDetail: state.insuranceAirtimeServiceReducer.itemOrderDetail
  };
};

const mapDispatchToProps = function (dispatch) {
  return {
    actionInsuranceAirtimeService: bindActionCreators(
      actionInsuranceAirtimeServiceCreator,
      dispatch
    ),
    actionPaymentOrder: bindActionCreators(actionPaymentOrderCreator, dispatch),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(CreateOrder);

const styles = StyleSheet.create({});
