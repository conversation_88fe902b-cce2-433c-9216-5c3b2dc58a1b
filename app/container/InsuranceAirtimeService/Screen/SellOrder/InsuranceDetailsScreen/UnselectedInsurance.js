import { <PERSON><PERSON>, FlatList, Pressable, StyleSheet, View, Animated } from "react-native";
import React, { useEffect, useState, useRef } from "react";
import { COLORS } from "@styles";
import { constants } from "@constants";
import {
    BaseLoading,
    MyText,
    PickerSearch,
    TitleInput,
    hideBlockUI,
    showBlockUI,
} from "@components";
import { helper } from "@common";
import { translate } from "@translate";
import ButtonAction from "../../../component/Button/ButtonAction";
import { PickerLocation } from "../../../../../components";
import LinearGradient from "react-native-linear-gradient";
import ParticipationInformation from "../../../component/Button/ParticipationInformation";
import PaymentInformation from "../../../component/Button/PaymentInformation";
import Promotion from "../../../component/Coupon/Promotion";

const UnselectedInsurance = ({
    stateValidateService,
    dataValidateService,
    actionInsuranceAirtimeService,
    itemCatalog,
    updateHeaderAirtime,
    actionPaymentOrder,
    navigation,
    handleSearch,
    keyword,
    dataProvince,
}) => {
    const { isFetching, isError, isEmpty, description } =
        stateValidateService ?? "";
    const {
        Imei,
        ProductID,
        ProductName,
        SalePriceVAT,
        CustomerName,
        CustomerPhone,
        OutputDate,
        MainSaleOrderID,
        ListMoneyInsuranceScreen
    } = dataValidateService ?? {};
    const [chooseInsurance, setChooseInsurance] = useState("");
    const [updateMonth, setUpdateMonth] = useState([]);
    const [listInsProgramDetail, setListInsProgramDetail] = useState([]);
    const [updatePromotion, setUpdatePromotion] = useState({});
    const [updateItem, setUpdateItem] = useState({});
    const [customerName, setCustomerName] = useState("");
    const [customerPhone, setCustomerPhone] = useState("");
    const [updatePrice, setUpdatePrice] = useState({});
    const [customerHouseNumber, setCustomerHouseNumber] = useState("");
    const [provinceID, setProvinceID] = useState(0);
    const [district, setDistrict] = useState([]);
    const [districtID, setDistrictID] = useState(0);
    const [ward, setWard] = useState([]);
    const [wardID, setWardID] = useState(0);
    const [indexPager, setIndexPager] = useState(0);
    const [isShowIndicator, setIsShowIndicator] = useState(false);
    const [customer, setCustomer] = useState({
        DistrictID: 0,
        WardID: 0,
        ProvinceID: 0,
    });
    const [listInsurancePremium, setListInsurancePremium] = useState([]);
    const [pressedItemId, setPressedItemId] = useState(null);
    const [updateItemInsurancePremium, setUpdateItemInsurancePremium] = useState(
        {}
    );
    const { PromotionID, PromotionGift } = updatePromotion?.ExtraData ?? {};
    const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog ?? "";
    const { AirTimeTransactionTypeID, InsuranceID, InsCustomerID } =
        updateHeaderAirtime ?? "";
    const { PartnerData, ExtraData } = updatePrice?.PriceInfo ?? {};
    const { InsuranceValue } = ExtraData ?? "";
    const { Amount, PriceVAT } = updatePrice?.PriceInfo ?? "";
    const isShowInsurancePremium = AirTimeTransactionTypeID === 2132;


    const scaleAnim = useRef(new Animated.Value(1)).current;
    const opacityAnim = useRef(new Animated.Value(1)).current;

    useEffect(() => {
        if (pressedItemId) {
            Animated.sequence([
                Animated.timing(scaleAnim, {
                    toValue: 0.92,
                    duration: 100,
                    useNativeDriver: true,
                }),
                Animated.timing(scaleAnim, {
                    toValue: 1,
                    duration: 150,
                    useNativeDriver: true,
                })
            ]).start();

            Animated.sequence([
                Animated.timing(opacityAnim, {
                    toValue: 0.8,
                    duration: 100,
                    useNativeDriver: true,
                }),
                Animated.timing(opacityAnim, {
                    toValue: 1,
                    duration: 150,
                    useNativeDriver: true,
                })
            ]).start();
        }
    }, [pressedItemId]);

    useEffect(() => {
        if (listInsProgramDetail.length > 0) {
            setUpdateMonth(listInsProgramDetail);
        }
    }, [listInsProgramDetail]);

    useEffect(() => {
        if (!!CustomerName || !!CustomerPhone) {
            setCustomerName(CustomerName);
            setCustomerPhone(CustomerPhone);
        }
    }, [CustomerName, CustomerPhone]);


    useEffect(() => {
        setListInsurancePremium(ListMoneyInsuranceScreen);
    }, [dataValidateService]);

    useEffect(() => {
        setChooseInsurance("");
        setUpdateMonth([]);
        setListInsProgramDetail([]);
        setUpdatePromotion({});
        setUpdateItem({});
        setUpdatePrice({});
        setUpdateItemInsurancePremium({});
    }, [keyword]);

    const getDataPromotionService = (item) => {
        showBlockUI();
        const { InsProgramID, InsuranceProductID } = item;
        const data = {
            catalogID: ServiceCategoryID,
            serviceGroupID: AirtimeServiceGroupID,
            airtimeTransactionTypeID: AirTimeTransactionTypeID,
            productID: InsuranceProductID,
            phoneNumber: Imei,
            insProgramID: InsProgramID,
            mainProductID: ProductID,
        };
        actionInsuranceAirtimeService
            .getPromotionService(data)
            .then((reponsePromotion) => {
                setUpdatePromotion(reponsePromotion);
                handleGetPriceService(reponsePromotion, item, updateItemInsurancePremium);
            })
            .catch((msgError) => {
                hideBlockUI();
                Alert.alert("", msgError, [
                    {
                        text: "OK",
                        onPress: () => {
                            hideBlockUI();
                        },
                    },
                ]);
            });
    };

    const renderInsurancePremium = ({ item, index }) => {
        const selected = item.selected || listInsurancePremium?.length == 1;
        const isPressed = pressedItemId === item.id;
        const shouldAnimate = selected && isPressed;

        return (
            <Pressable
                onPress={() => onClickItemInsurancePremium(item, index)}
                key={item.id}
                disabled={false}
                onPressIn={() => setPressedItemId(item.id)}
                onPressOut={() => setPressedItemId(null)}
                style={[
                    {
                        justifyContent: "center",
                        alignItems: "center",
                        width: constants.width / 3 - constants.getSize(10),
                        paddingVertical: 15,
                        paddingHorizontal: 5,
                        borderWidth: selected ? (shouldAnimate ? 2.5 : 2) : 1,
                        borderRadius: 40,
                        margin: 2,
                        borderColor: selected
                            ? shouldAnimate
                                ? "#2E8B57"
                                : "#3CB371"
                            : "#FFFFFF",
                        backgroundColor: selected
                            ? shouldAnimate
                                ? "#5CAE40"
                                : "#6BC750"
                            : "#FFEFD5",
                        shadowColor: "#000",
                        shadowOffset: {
                            width: 0,
                            height: shouldAnimate ? 2 : 3,
                        },
                        shadowOpacity: shouldAnimate ? 0.25 : 0.3,
                        shadowRadius: shouldAnimate ? 3 : 4,
                        elevation: shouldAnimate ? 3 : 4,
                        height: 50,
                        marginBottom: 5,
                    },
                ]}
            >
                <Animated.View
                    style={{
                        flexDirection: "column",
                        alignItems: "center",
                        borderRadius: 20,
                        width: "100%",
                        transform: [{ scale: scaleAnim }],
                        opacity: opacityAnim,
                    }}
                >
                    <View style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                    }}>
                        {selected && (
                            <Animated.View style={{
                                width: 6,
                                height: 6,
                                borderRadius: 3,
                                backgroundColor: '#FFFFFF',
                                marginRight: 6,
                                opacity: opacityAnim,
                                transform: [{ scale: scaleAnim }]
                            }} />
                        )}
                        <Animated.Text
                            style={{
                                color: selected ? COLORS.bgFFFFFF : COLORS.txt555555,
                                fontSize: 15,
                                fontWeight: selected ? "bold" : "500",
                                transform: [{ scale: scaleAnim }],
                                opacity: opacityAnim,
                            }}
                        >
                            {helper.formatMoney(item.InsMoneyScreen)}
                        </Animated.Text>
                    </View>
                </Animated.View>
            </Pressable>
        );
    };

    const renderItemMonth = ({ item, index }) => {
        const isCheckSelect = item.selected || listInsProgramDetail?.length == 1;
        return (
            <Pressable
                onPress={() => onClickItem(item, index)}
                key={item.id}
                disabled={listInsProgramDetail?.length == 1}
                style={({ pressed }) => [
                    {
                        margin: 8,
                        backgroundColor: isCheckSelect ? "#75C2F6" : "#F5EFE7",
                        borderRadius: 15,
                        padding: 12,
                        width: "46%",
                        height: item?.Description == "" ? 42 : "auto",
                        alignItems: "center",
                        shadowColor: "#000",
                        shadowOffset: {
                            width: 0,
                            height: 1,
                        },
                        shadowOpacity: 0.15,
                        shadowRadius: 3,
                        elevation: 3,
                    },
                ]}
            >
                <View
                    style={{
                        flexDirection: "column",
                        alignItems: "center",
                        borderRadius: 20,
                        width: "100%",
                    }}
                >
                    <View
                        style={{
                            backgroundColor: isCheckSelect
                                ? item?.Description != ""
                                    ? "#7BC9FF"
                                    : "#75C2F6"
                                : "#F5EFE7",
                            width: "100%",
                            justifyContent: "center",
                            alignItems: "center",
                            borderRadius: 16,
                        }}
                    >
                        <MyText
                            style={{
                                color: isCheckSelect ? "#008DDA" : "#B6BBC4",
                                fontSize: 15,
                                fontWeight: isCheckSelect ? "bold" : "500",
                            }}
                            text={`${item.InsuranceMonth} tháng`}
                        />
                    </View>
                    {item?.Description && (
                        <View
                            style={{
                                padding: 8,
                            }}
                        >
                            <MyText
                                style={{
                                    color: isCheckSelect ? "#FFFFFF" : "#B6BBC4",
                                    fontSize: 14,
                                    fontWeight: isCheckSelect ? "bold" : "400",
                                    textAlign: "center",
                                }}
                                text={`${item.Description}`}
                            />
                        </View>
                    )}
                </View>
            </Pressable>
        );
    };

    const onClickItemInsurancePremium = (item) => {
        const newDataInsurancePremium = ListMoneyInsuranceScreen?.map((r) => ({
            ...r,
            selected: r.InsMoneyScreen === item.InsMoneyScreen,
        }));
        setListInsurancePremium(newDataInsurancePremium);
        setUpdateItemInsurancePremium(item);
        setChooseInsurance("");
        setUpdatePrice({});
        const newDataMonth = updateMonth?.map((r) => ({
            ...r,
            selected: false,
        }));
        setUpdateMonth(newDataMonth);
    };

    const onClickItem = (item) => {
        const newDataMonth = listInsProgramDetail?.map((r) => ({
            ...r,
            selected: r.InsuranceProductID === item.InsuranceProductID,
        }));
        setUpdateMonth(newDataMonth);
        getDataPromotionService(item);
        setUpdateItem(item);
    };

    const handleGetPriceService = (reponsePromotion, item, updateItemInsurancePremium) => {
        const { RetailPriceVAT, OutputDate, Imei, ProductID } =
            dataValidateService ?? {};
        const { InsuranceProductID, InsuranceMonth, InsuranceApplyID } = item ?? {};
        const { ProductPromotion } = reponsePromotion?.ExtraData;
        const { InsMoneyScreen } = updateItemInsurancePremium ?? {};
        const data = {
            catalogID: ServiceCategoryID,
            serviceGroupID: AirtimeServiceGroupID,
            airtimeTransactionTypeID: AirTimeTransactionTypeID,
            productID: InsuranceProductID,
            retailPriceVAT: RetailPriceVAT,
            outputDate: OutputDate,
            insuranceMonth: InsuranceMonth,
            imei: Imei,
            mainProductID: ProductID,
            productPromotion: ProductPromotion,
            insuranceApplyID: InsuranceApplyID,
            mainSaleOrderID: MainSaleOrderID,
            insMoneyScreen: InsMoneyScreen
        };
        setUpdatePrice({});
        actionInsuranceAirtimeService
            .getPriceService(data)
            .then((reponse) => {
                hideBlockUI();
                setUpdatePrice(reponse);
            })
            .catch((msgError) => {
                Alert.alert("", msgError, [
                    {
                        text: "Thử lại",
                        onPress: () => getDataPromotionService(item),
                    },
                    {
                        text: "OK",
                        onPress: () => {
                            hideBlockUI();
                            actionInsuranceAirtimeService.clear_data_validate_service_request();
                            setUpdateMonth([]);
                            setUpdateItem({});
                            setChooseInsurance("");
                        },
                    },
                ]);
            });
    };

    const handleAddCart = () => {
        const {
            Amount,
            SalePrice,
            InputPrice,
            ProductInfo: { ProductID },
        } = updatePrice?.PriceInfo;
        const { Fee } = updatePrice?.FeeInfo ?? {};
        const { Imei } = dataValidateService ?? {};
        const { InsuranceMonth, InsuranceApplyID } = updateItem;
        const { PromotionID, ProductPromotion } = updatePromotion?.ExtraData ?? {};
        const {
            CMD,
            SendContent,
            ResponseContent,
            SendTime,
            ResponseMessage,
            ResponseTime,
        } = PartnerData ?? "";
        const { InsMoneyScreen } = updateItemInsurancePremium ?? {};
        const data = {
            catalogID: ServiceCategoryID,
            serviceGroupID: AirtimeServiceGroupID,
            airtimeTransactionTypeID: AirTimeTransactionTypeID,
            airTimeTransactionBO: {
                productid: ProductID,
                amount: Amount,
                fee: Fee,
                phonenumber: Imei,
                inputPrice: InputPrice,
                salePrice: SalePrice,
                customerName: customerName,
                customerPhone: customerPhone,
                customerAddress: customerHouseNumber,
            },
            insuranceID: InsuranceID,
            imei: Imei,
            insCustomerID: InsCustomerID,
            insProgramID: chooseInsurance,
            insuranceMonth: InsuranceMonth,
            insuranceApplyID: InsuranceApplyID,
            promotionID: PromotionID,
            productPromotion: ProductPromotion,
            partnerData: {
                CMD: CMD,
                SendContent: SendContent,
                ResponseContent: ResponseContent,
                SendTime: SendTime,
                ResponseMessage: ResponseMessage,
                ResponseTime: ResponseTime,
            },
            districtID: districtID,
            provinceID: provinceID,
            wardID: wardID,
            apartmentNumberAddress: customerHouseNumber,
            mainSaleOrderID: MainSaleOrderID,
            promotionGift: PromotionGift,
            insMoneyScreen: InsMoneyScreen,
        };
        if (customerName == "") {
            Alert.alert("", "Vui lòng nhập tên khách hàng!");
            return hideBlockUI();
        } else if (customerPhone == "") {
            Alert.alert("", "Vui lòng nhập số điện thoại khách hàng!");
            return hideBlockUI();
        } else if (customerPhone.length < 10) {
            Alert.alert("", "Vui lòng nhập số điện thoại đúng 10 số!");
            return hideBlockUI();
        }
        if (customerName == "") {
            Alert.alert("", "Vui lòng nhập tên khách hàng!");
            return;
        } else if (customerPhone == "") {
            Alert.alert("", "Vui lòng nhập số điện thoại khách hàng!");
            return;
        } else if (customerPhone.length < 10) {
            Alert.alert("", "Vui lòng nhập số điện thoại đủ 10 số!");
            return;
        } else if (AirTimeTransactionTypeID == 1872) {
            if (customerHouseNumber == "") {
                Alert.alert("", "Vui lòng nhập số nhà !");
                return;
            } else if (
                helper.IsEmptyObject(customer) ||
                customer.DistrictID == 0 ||
                customer.ProvinceID == 0 ||
                customer.WardID == 0
            ) {
                Alert.alert("", "Vui lòng chọn Tỉnh/Thành - Quận/Huyện - Phường/Xã");
                return;
            }
        }
        showBlockUI();
        actionInsuranceAirtimeService
            .addToSaleOrderCart(data)
            .then((reponse) => {
                goToPaymentSO(reponse);
            })
            .catch((error) => {
                Alert.alert(translate("common.notification_uppercase"), error, [
                    {
                        text: translate("common.btn_close"),
                        onPress: hideBlockUI,
                    },
                ]);
            });
    };

    const goToPaymentSO = (rpSaleOrderCart) => {
        const dataSaleOrderCart = rpSaleOrderCart.object;
        const SaleOrders = dataSaleOrderCart.SaleOrders[0];
        const { SaleOrderID } = SaleOrders;
        actionPaymentOrder
            .setDataSO({
                SaleOrderID: SaleOrderID,
                SaleOrderTypeID: 1000,
            })
            .then((success) => {
                hideBlockUI();
                setUpdateMonth([]);
                setUpdatePrice({});
                actionInsuranceAirtimeService.clear_data_validate_service_request();
                navigation.navigate("SaleOrderPayment");
                actionPaymentOrder.getSaleOrderPayment(SaleOrderID);
                actionPaymentOrder.getReportPrinterSocket(1000);
                actionPaymentOrder.getDataQRTransaction(SaleOrderID);
            });
    };

    useEffect(() => {
        getProvince();
    }, [actionInsuranceAirtimeService]);

    const effectChangeProvince = () => {
        if (provinceID > 0) {
            getDataDistrict(provinceID);
        }
    };

    const effectChangeDistrict = () => {
        if (districtID > 0) {
            getDataWard(provinceID, districtID);
        }
    };

    useEffect(effectChangeProvince, [provinceID]);

    useEffect(effectChangeDistrict, [districtID]);

    const getProvince = () => {
        actionInsuranceAirtimeService.getDataProvince();
    };

    const getDataDistrict = (provinceID) => {
        setIsShowIndicator(true);
        actionInsuranceAirtimeService
            .getDistrict(provinceID)
            .then((data) => {
                setDistrict(data);
                setIndexPager(1);
                setIsShowIndicator(false);
            })
            .catch((msgError) => {
                Alert.alert(translate("common.notification_uppercase"), msgError, [
                    {
                        text: translate("common.btn_skip"),
                        style: "cancel",
                        onPress: () => setIsShowIndicator(false),
                    },
                    {
                        text: translate("common.btn_notify_try_again"),
                        style: "default",
                        onPress: () => getDataDistrict(provinceID),
                    },
                ]);
            });
    };

    const getDataWard = (provinceID, districtID) => {
        setIsShowIndicator(true);
        actionInsuranceAirtimeService
            .getWard(provinceID, districtID)
            .then((data) => {
                setWard(data);
                setIndexPager(2);
                setIsShowIndicator(false);
            })
            .catch((msgError) => {
                Alert.alert(translate("common.notification_uppercase"), msgError, [
                    {
                        text: translate("common.btn_skip"),
                        style: "cancel",
                        onPress: () => setIsShowIndicator(false),
                    },
                    {
                        text: translate("common.btn_notify_try_again"),
                        style: "default",
                        onPress: () => getDataWard(provinceID, districtID),
                    },
                ]);
            });
    };

    return (
        <View
            style={{
                width: "100%",
                height: "100%",
                justifyContent: "center",
                alignItems: "center",
                flex: 1,
            }}
        >
            <BaseLoading
                isLoading={isFetching}
                isError={isError}
                isEmpty={isEmpty}
                textLoadingError={description}
                onPressTryAgains={() => {
                    handleSearch(keyword);
                }}
                content={
                    !helper.IsEmptyObject(dataValidateService) && (
                        <ParticipationInformation
                            title={"Thông tin sản phẩm tham gia bảo hiểm"}
                            SaleOrderID={MainSaleOrderID}
                            ProductName={ProductName}
                            Imei={Imei}
                            RetailPriceVAT={SalePriceVAT}
                            CUSTOMERNAME={CustomerName}
                            CUSTOMERPHONE={CustomerPhone}
                            CREATEDATE={OutputDate}
                        />
                    )
                }
            />
            <View
                style={{
                    width: "100%",
                    height: "100%",
                    flex: 1,
                }}
            >
                {helper.IsNonEmptyArray(dataValidateService?.ListInsProgram) &&
                    isShowInsurancePremium && (
                        <View
                            style={{
                                justifyContent: "center",
                                marginTop: 5,
                                marginBottom: 5,
                            }}
                        >
                            <MyText
                                style={{
                                    color: COLORS.bg000000,
                                    fontSize: 15,
                                    fontWeight: "bold",
                                    paddingHorizontal: 5,
                                }}
                                text={"Số tiền tham gia bảo hiểm"}
                            />
                            <FlatList
                                data={listInsurancePremium}
                                keyExtractor={(item, index) => `${index}`}
                                renderItem={renderInsurancePremium}
                                bounces={false}
                                numColumns={3}
                            />
                        </View>
                    )}

                {helper.IsNonEmptyArray(dataValidateService?.ListInsProgram) &&
                    ((isShowInsurancePremium &&
                        !helper.IsEmptyObject(updateItemInsurancePremium)) ||
                        !isShowInsurancePremium) && (
                        <>
                            <View
                                style={{
                                    paddingTop: 10,
                                }}
                            >
                                <MyText
                                    style={{
                                        color: COLORS.bg000000,
                                        fontSize: 15,
                                        fontWeight: "bold",
                                        paddingHorizontal: 5,
                                    }}
                                    text={"Thông tin bảo hiểm"}
                                />
                                <PickerSearch
                                    label={"InsProgramName"}
                                    value={"InsProgramID"}
                                    valueSelected={chooseInsurance}
                                    data={
                                        helper.IsNonEmptyArray(dataValidateService?.ListInsProgram)
                                            ? dataValidateService?.ListInsProgram
                                            : []
                                    }
                                    onChange={(item) => {
                                        setChooseInsurance(item?.InsProgramID);
                                        if (item.ListInsProgramDetail?.length > 1) {
                                            setListInsProgramDetail(item?.ListInsProgramDetail);
                                        } else {
                                            setListInsProgramDetail(item?.ListInsProgramDetail);
                                            getDataPromotionService(item.ListInsProgramDetail?.[0]);
                                            setUpdateItem(item.ListInsProgramDetail?.[0]);
                                        }
                                        setUpdatePrice({});
                                    }}
                                    style={{
                                        alignSelf: "center",
                                        flexDirection: "row",
                                        justifyContent: "center",
                                        alignItems: "center",
                                        height: 40,
                                        borderRadius: 4,
                                        borderWidth: 1,
                                        borderColor: COLORS.bdE4E4E4,
                                        width: constants.width - 10,
                                        backgroundColor: COLORS.btnFFFFFF,
                                        marginBottom: 10,
                                        marginTop: 5,
                                    }}
                                    defaultLabel={"Chọn gói bảo hành"}
                                />
                                {!!chooseInsurance && (
                                    <View
                                        style={{
                                            justifyContent: "center",
                                            alignItems: "center",
                                            width: "100%",
                                        }}
                                    >
                                        <FlatList
                                            data={updateMonth}
                                            keyExtractor={(item, index) => `${index}`}
                                            renderItem={renderItemMonth}
                                            stickySectionHeadersEnabled={true}
                                            alwaysBounceVertical={false}
                                            bounces={false}
                                            numColumns={2}
                                            scrollEventThrottle={16}
                                            horizontal={false}
                                            contentContainerStyle={{
                                                width:
                                                    updateMonth.length == 1
                                                        ? constants.width - 20
                                                        : "auto",
                                            }}
                                        />
                                    </View>
                                )}

                                {!helper.IsEmptyObject(updatePrice) && (
                                    <View
                                        style={{
                                            width: "100%",
                                            alignItems: "center",
                                        }}
                                    >
                                        <PaymentInformation
                                            RetailPriceVAT={InsuranceValue}
                                            IMEI={Imei}
                                            Amount={Amount}
                                            PriceVAT={PriceVAT}
                                            promotionID={PromotionID}
                                            updatePrice={updatePrice}
                                            EffectiveTime={ExtraData?.EffectiveTime}
                                            isCheckDate={ExtraData?.EffectiveTime != null}
                                        />
                                        <View
                                            style={{
                                                marginTop: 5,
                                            }}
                                        >
                                            <TitleInput
                                                title={"Họ tên khách hàng"}
                                                styleInput={{
                                                    borderWidth: 1,
                                                    borderRadius: 4,
                                                    borderColor: COLORS.bdCCCCCC,
                                                    marginBottom: 5,
                                                    paddingHorizontal: 10,
                                                    backgroundColor: COLORS.bgFFFFFF,
                                                    paddingVertical: 8,
                                                }}
                                                placeholder={"Vui lòng nhập họ tên khách hàng: "}
                                                value={customerName}
                                                onChangeText={(text) => {
                                                    setCustomerName(text);
                                                }}
                                                keyboardType="default"
                                                returnKeyType="done"
                                                blurOnSubmit
                                                width={constants.width - 20}
                                                height={40}
                                                clearText={() => {
                                                    setCustomerName("");
                                                }}
                                                key="customerName"
                                                isRequired={true}
                                            />
                                            <TitleInput
                                                title={"Số điện thoại khách hàng"}
                                                styleInput={{
                                                    borderWidth: 1,
                                                    borderRadius: 4,
                                                    borderColor: COLORS.bdCCCCCC,
                                                    marginBottom: 5,
                                                    paddingHorizontal: 10,
                                                    backgroundColor: COLORS.bgFFFFFF,
                                                    paddingVertical: 8,
                                                }}
                                                placeholder={"Vui lòng nhập số điện thoại khách hàng: "}
                                                value={customerPhone}
                                                onChangeText={(text) => {
                                                    const regExpPhone = new RegExp(/^[0]\d{0,9}$/);
                                                    const isValidate =
                                                        regExpPhone.test(text) || text == "";
                                                    if (isValidate) {
                                                        setCustomerPhone(text);
                                                    }
                                                }}
                                                keyboardType="numeric"
                                                returnKeyType="done"
                                                blurOnSubmit
                                                width={constants.width - 20}
                                                height={40}
                                                clearText={() => {
                                                    setCustomerPhone("");
                                                }}
                                                key="customerPhoneNumber"
                                                isRequired={true}
                                            />
                                        </View>
                                        {AirTimeTransactionTypeID == 1872 && (
                                            <View>
                                                <TitleInput
                                                    title={"Số nhà"}
                                                    styleInput={{
                                                        borderWidth: 1,
                                                        borderRadius: 4,
                                                        borderColor: COLORS.bdCCCCCC,
                                                        marginBottom: 5,
                                                        paddingHorizontal: 10,
                                                        backgroundColor: COLORS.bgFFFFFF,
                                                        paddingVertical: 8,
                                                        width: "100%",
                                                    }}
                                                    placeholder={"Nhập số nhà: "}
                                                    value={customerHouseNumber}
                                                    onChangeText={(text) => {
                                                        setCustomerHouseNumber(text);
                                                    }}
                                                    keyboardType="default"
                                                    returnKeyType="done"
                                                    blurOnSubmit
                                                    width={constants.width - 20}
                                                    height={40}
                                                    clearText={() => {
                                                        setCustomerHouseNumber("");
                                                    }}
                                                    key="customerName"
                                                    isRequired={true}
                                                />
                                                <View style={{ marginTop: 2 }}>
                                                    <MyText
                                                        style={{
                                                            fontWeight: "700",
                                                            fontStyle: "italic",
                                                            fontSize: 13,
                                                            marginBottom: 5,
                                                        }}
                                                        text={"Địa chỉ thường trú"}
                                                        children={
                                                            <MyText
                                                                text={"*"}
                                                                style={{
                                                                    color: COLORS.txtFF0000,
                                                                    fontSize: 16,
                                                                }}
                                                            />
                                                        }
                                                    />

                                                    <PickerLocation
                                                        dataProvince={{
                                                            data: dataProvince,
                                                            id: "provinceID",
                                                            value: "provinceName",
                                                        }}
                                                        dataDistrict={{
                                                            data: district,
                                                            id: "districtID",
                                                            value: "districtName",
                                                        }}
                                                        dataWard={{
                                                            data: ward,
                                                            id: "wardID",
                                                            value: "wardName",
                                                        }}
                                                        wardID={wardID}
                                                        districtID={districtID}
                                                        provinceID={provinceID}
                                                        onSelectProvince={(item) => {
                                                            setProvinceID(item.provinceID);
                                                            setDistrictID(0);
                                                            setWardID(0);
                                                        }}
                                                        onSelectDistrict={(item) => {
                                                            setDistrictID(item.districtID);
                                                            setWardID(0);
                                                        }}
                                                        onSelectWard={(item) => {
                                                            if (item.wardID != wardID) {
                                                                setWardID(item.wardID);
                                                                setCustomer({
                                                                    ...customer,
                                                                    ProvinceID: provinceID,
                                                                    DistrictID: districtID,
                                                                    WardID: item.wardID,
                                                                });
                                                            }
                                                        }}
                                                        indexPager={indexPager}
                                                        onShowPicker={(index) => {
                                                            setIndexPager(index);
                                                        }}
                                                        updatePager={(index) => {
                                                            setIndexPager(index);
                                                        }}
                                                        isShowIndicator={isShowIndicator}
                                                    />
                                                </View>
                                            </View>
                                        )}
                                        {helper.IsNonEmptyArray(PromotionGift) && (
                                            <View style={{ width: "100%", marginTop: 10 }}>
                                                <FlatList
                                                    data={PromotionGift}
                                                    keyExtractor={(item, index) => `${index}`}
                                                    renderItem={({ item }) => (
                                                        <Promotion message={item.PromotionMessage} />
                                                    )}
                                                    contentContainerStyle={{ paddingVertical: 10 }}
                                                    showsVerticalScrollIndicator={false}
                                                />
                                            </View>
                                        )}

                                        <View
                                            style={{
                                                marginTop: 10,
                                                padding: 3,
                                                width: "98%",
                                            }}
                                        >
                                            <LinearGradient
                                                colors={["#f8f8f8", "#e0e0e0"]}
                                                start={{ x: 0, y: 0 }}
                                                end={{ x: 1, y: 1 }}
                                                style={{
                                                    flexDirection: "row",
                                                    justifyContent: "space-between",
                                                    alignItems: "center",
                                                    padding: 10,
                                                    borderRadius: 8,
                                                    marginBottom: 5,
                                                    shadowColor: "#000",
                                                    shadowOffset: { width: 0, height: 2 },
                                                    shadowOpacity: 0.1,
                                                    shadowRadius: 4,
                                                    elevation: 2,
                                                }}
                                            >
                                                <MyText
                                                    style={{
                                                        color: COLORS.bg000000,
                                                        fontSize: 14,
                                                        lineHeight: 20,
                                                    }}
                                                    text={"Tổng tiền đơn hàng:"}
                                                />
                                                <MyText
                                                    style={{
                                                        color: COLORS.bg000000,
                                                        fontWeight: "bold",
                                                        fontSize: 16,
                                                    }}
                                                    text={helper.formatMoney(updatePrice?.TotalAmount)}
                                                />
                                            </LinearGradient>
                                            <LinearGradient
                                                colors={["#fdeeee", "#f9dcdc"]}
                                                start={{ x: 0, y: 0 }}
                                                end={{ x: 1, y: 1 }}
                                                style={{
                                                    flexDirection: "row",
                                                    justifyContent: "space-between",
                                                    alignItems: "center",
                                                    padding: 10,
                                                    borderRadius: 8,
                                                    shadowColor: "#000",
                                                    shadowOffset: { width: 0, height: 2 },
                                                    shadowOpacity: 0.1,
                                                    shadowRadius: 4,
                                                    elevation: 2,
                                                }}
                                            >
                                                <MyText
                                                    style={{
                                                        color: COLORS.bg000000,
                                                        fontSize: 14,
                                                        lineHeight: 20,
                                                    }}
                                                    text={"Phải thu của khách:"}
                                                />
                                                <MyText
                                                    style={{
                                                        color: COLORS.bgEA1D5D,
                                                        fontWeight: "bold",
                                                        fontSize: 16,
                                                    }}
                                                    text={helper.formatMoney(updatePrice?.TotalAmount)}
                                                />
                                            </LinearGradient>
                                        </View>
                                        <View
                                            style={{
                                                justifyContent: "center",
                                                alignItems: "center",
                                                flexDirection: "row",
                                                marginTop: 20,
                                                width: constants.width,
                                                paddingHorizontal: 5,
                                                marginHorizontal: 500,
                                            }}
                                        >
                                            <ButtonAction
                                                onPress={() => navigation.goBack()}
                                                title={"ĐÓNG"}
                                                style={{
                                                    borderWidth: 1,
                                                    borderColor: COLORS.bg00A98F,
                                                }}
                                                styleText={{
                                                    color: COLORS.bg00A98F,
                                                }}
                                                opacity={1}
                                                disabled={false}
                                            />
                                            <View style={{ flex: 1 }} />
                                            <ButtonAction
                                                onPress={() => handleAddCart()}
                                                title={"XÁC NHẬN"}
                                                style={{
                                                    backgroundColor: COLORS.bg00A98F,
                                                }}
                                                styleText={{
                                                    color: COLORS.bgFFFFFF,
                                                }}
                                                opacity={1}
                                                disabled={false}
                                            />
                                        </View>
                                    </View>
                                )}
                            </View>
                        </>
                    )}
            </View>
        </View>
    );
};

export default UnselectedInsurance;

const styles = StyleSheet.create({});
