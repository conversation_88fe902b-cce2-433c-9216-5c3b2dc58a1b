import React, { useState } from 'react';
import { View, SafeAreaView, SectionList, Alert } from 'react-native';
import { COLORS } from '@styles';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import * as actionInsuranceAirtimeServiceCreator from "../../../action";
import { MyText } from '@components';
import { hideBlockUI, showBlockUI } from '@components';
import {
    ButtonDirectionStep,
    FormDatePicker,
    FormPicker,
    FormLocationPicker,
    FormRadio,
    FormTextInput,
    FormCheckbox
} from './components'
import { helper } from '@common'
import moment from 'moment'

//  6 => Thông tin đối tượng mua bảo hiểm
//  7 => Thông tin người mua bảo hiểm
//  8 => Thông tin người được bảo hiểm

const CustomerInformation = ({
    customerInfo,
    onBack,
    onNextStep,
    isSamePerson,
    setIsSamePerson,
    itemCatalog,
    updateHeaderAirtime,
    actionInsuranceAirtimeService
}) => {

    const [customerInfoState, setCustomerInfoState] = useState(customerInfo.map((formGroup, groupIndex) => ({
        ...formGroup,
        groupIndex,
        title: formGroup.GroupName,
        data: formGroup.PropertyList
    })));
    const [selectedBuiseness, setSelectedBuiseness] = useState(false)

    const assignInsuredPersonInfo = () => {
        let newCustomerInfoState = [...customerInfoState];
        const customerInfoGroupIndex = newCustomerInfoState.findIndex(group => group.InsInforGroupID == 7)
        const insuredPersonInfoGroupIndex = newCustomerInfoState.findIndex(group => group.InsInforGroupID == 8)
        if (insuredPersonInfoGroupIndex > -1 && customerInfoGroupIndex > -1) {
            const customerPropertyIdIndex = newCustomerInfoState[customerInfoGroupIndex].PropertyList.findIndex(form => form.PropertyID == 36)
            const insuredPersonPropertyIdIndex = newCustomerInfoState[insuredPersonInfoGroupIndex].PropertyList.findIndex(form => form.PropertyID == 112)
            if (customerPropertyIdIndex > -1 && insuredPersonPropertyIdIndex > -1) {
                newCustomerInfoState[customerInfoGroupIndex].PropertyList[customerPropertyIdIndex].Value = newCustomerInfoState[insuredPersonInfoGroupIndex].PropertyList[insuredPersonPropertyIdIndex].Value
                newCustomerInfoState[customerInfoGroupIndex].PropertyList[customerPropertyIdIndex].Value = newCustomerInfoState[insuredPersonInfoGroupIndex].data[insuredPersonPropertyIdIndex].Value
            }
        }
        setCustomerInfoState(newCustomerInfoState);
    }

    const resetInsuredPersonInfo = () => {
        let newCustomerInfoState = [...customerInfoState];
        const insuredPersonInfoGroupIndex = newCustomerInfoState.findIndex(group => group.InsInforGroupID == 8)
        if (insuredPersonInfoGroupIndex > -1) {
            newCustomerInfoState[insuredPersonInfoGroupIndex].PropertyList.forEach(property => {
                if (property.PropertyID != 112) {
                    property.Value = null
                }
            })
            newCustomerInfoState[insuredPersonInfoGroupIndex].data = newCustomerInfoState[insuredPersonInfoGroupIndex].PropertyList
        }
        setCustomerInfoState(newCustomerInfoState)
    }

    const onCheckRadio = (groupIndex, formIndex) => (value) => {
        let newCustomerInfoState = [...customerInfoState];
        newCustomerInfoState[groupIndex].PropertyList[formIndex].Value = value;
        newCustomerInfoState[groupIndex].data[formIndex].Value = value;
        setCustomerInfoState(newCustomerInfoState);
    }

    const onSubmitText = (groupIndex, formIndex) => (text) => {
        let newCustomerInfoState = [...customerInfoState];
        newCustomerInfoState[groupIndex].PropertyList[formIndex].Value = text;
        newCustomerInfoState[groupIndex].data[formIndex].Value = text;
        setCustomerInfoState(newCustomerInfoState);
    }

    const onSelectDate = (groupIndex, formIndex) => (date, propertyId) => {
        let newCustomerInfoState = [...customerInfoState];
        newCustomerInfoState[groupIndex].PropertyList[formIndex].Value = date;
        newCustomerInfoState[groupIndex].data[formIndex].Value = date;
        if (propertyId == 4) {
            const expiredDateFormIndex = newCustomerInfoState[groupIndex].PropertyList.findIndex(form => form.PropertyID == 5)
            if (expiredDateFormIndex > -1) {
                const [dd, mm, yyyy] = date.split('/')
                newCustomerInfoState[groupIndex].data[expiredDateFormIndex].Value = `${dd}/${mm}/${+yyyy + 1}`
            }
        }
        setCustomerInfoState(newCustomerInfoState);
    }

    const getDataCarModel = (form, groupIndex, value) => {
        return new Promise((resolve, reject) => {
            const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog;
            const { AirTimeTransactionTypeID, CustomerID } = updateHeaderAirtime;
            showBlockUI();
            const data = {
                catalogId: ServiceCategoryID,
                serviceGroupId: AirtimeServiceGroupID,
                airtimeTransactionTypeId: AirTimeTransactionTypeID,
                partnerId: CustomerID,
                searchType: 1,
                extraData: form
            }
            actionInsuranceAirtimeService.getDataCarModel(data)
                .then((responseStatus) => {
                    hideBlockUI();
                    // Cập nhật state ngay sau khi có data
                    let newCustomerInfoState = [...customerInfoState];
                    for (let gIndex = 0; gIndex < newCustomerInfoState.length; gIndex++) {
                        const form27Index = newCustomerInfoState[gIndex].PropertyList.findIndex(f => f.PropertyID === 27);
                        if (form27Index > -1) {
                            const dataDongXe = responseStatus?.Data || [];
                            newCustomerInfoState[gIndex].PropertyList[form27Index].Data = dataDongXe;
                            newCustomerInfoState[gIndex].data[form27Index].Data = dataDongXe;
                            break;
                        }
                    }
                    setCustomerInfoState(newCustomerInfoState);
                    resolve(responseStatus);
                })
                .catch((msgError) => {
                    hideBlockUI();
                    updateCarModelData([]);
                    Alert.alert("", msgError, [
                        {
                            text: "OK",
                            onPress: () => { },
                        },
                    ]);
                    reject(msgError);
                });
        });
    };

    const updateCarModelData = (dataDongXe) => {
        let newCustomerInfoState = [...customerInfoState];
        for (let gIndex = 0; gIndex < newCustomerInfoState.length; gIndex++) {
            const form27Index = newCustomerInfoState[gIndex].PropertyList.findIndex(f => f.PropertyID === 27);
            if (form27Index > -1) {
                newCustomerInfoState[gIndex].PropertyList[form27Index].Data = dataDongXe;
                newCustomerInfoState[gIndex].data[form27Index].Data = dataDongXe;
                break;
            }
        }
        setCustomerInfoState(newCustomerInfoState);
    };

    const onSelectItem = (groupIndex, formIndex, form) => (value, propertyId, partnerValue) => {
        let newCustomerInfoState = [...customerInfoState];
        newCustomerInfoState[groupIndex].PropertyList[formIndex].Value = value;
        newCustomerInfoState[groupIndex].data[formIndex].Value = value;
        // logic khi mua bảo hiểm cho bản thân
        if (propertyId == 35) {
            setIsSamePerson(partnerValue == "1")
            if (partnerValue == "1") {
                const isValidAge = validateCustomerAge();
                if (isValidAge) {
                    assignInsuredPersonInfo();
                } else {
                    newCustomerInfoState[groupIndex].PropertyList[formIndex].Value = null;
                    newCustomerInfoState[groupIndex].data[formIndex].Value = null;
                }
            } else {
                resetInsuredPersonInfo();
            }
        }
        if (propertyId == 26 && form.IsGetDataFilter == true) {
            getDataCarModel(form, groupIndex, value);
        }
        setCustomerInfoState(newCustomerInfoState);
    }

    const onSelectProvince = (groupIndex, formIndex) => (provinceId, provinceName) => {
        let newCustomerInfoState = [...customerInfoState];
        newCustomerInfoState[groupIndex].PropertyList[formIndex].LocationValue = { ...newCustomerInfoState[groupIndex].PropertyList[formIndex].LocationValue, provinceId, provinceName };
        newCustomerInfoState[groupIndex].data[formIndex].LocationValue = { ...newCustomerInfoState[groupIndex].PropertyList[formIndex].LocationValue, provinceId, provinceName };
        setCustomerInfoState(newCustomerInfoState);
    }

    const onSelectDistrict = (groupIndex, formIndex) => (districtId, districtName) => {
        let newCustomerInfoState = [...customerInfoState];
        newCustomerInfoState[groupIndex].PropertyList[formIndex].LocationValue = { ...newCustomerInfoState[groupIndex].PropertyList[formIndex].LocationValue, districtId, districtName };
        newCustomerInfoState[groupIndex].data[formIndex].LocationValue = { ...newCustomerInfoState[groupIndex].PropertyList[formIndex].LocationValue, districtId, districtName };
        setCustomerInfoState(newCustomerInfoState);
    }

    const onSelectWard = (groupIndex, formIndex) => (wardId, wardName) => {
        let newCustomerInfoState = [...customerInfoState];
        newCustomerInfoState[groupIndex].PropertyList[formIndex].LocationValue = { ...newCustomerInfoState[groupIndex].PropertyList[formIndex].LocationValue, wardId, wardName };
        newCustomerInfoState[groupIndex].data[formIndex].LocationValue = { ...newCustomerInfoState[groupIndex].PropertyList[formIndex].LocationValue, wardId, wardName };
        const { provinceName, districtName } = newCustomerInfoState[groupIndex].PropertyList[formIndex].LocationValue
        newCustomerInfoState[groupIndex].PropertyList[formIndex].Value = ` ${wardName}, ${districtName},${provinceName}`
        setCustomerInfoState(newCustomerInfoState);
    }

    const onSelectCheckbox = (groupIndex, formIndex) => (value) => {
        let newCustomerInfoState = [...customerInfoState];
        newCustomerInfoState[groupIndex].PropertyList[formIndex].Value = isSelected;
        newCustomerInfoState[groupIndex].data[formIndex].Value = isSelected;
        setCustomerInfoState(newCustomerInfoState);
    }

    const nextStep = () => {
        // map thông tin người mua - người được bảo hiểm
        // giới tính 109 - 110
        // CMND/CCCD 108 - 111
        // ngày sinh 36 - 112
        // họ tên 38 - 9
        // địa chỉ 12 - 113
        // số nhà 11 - 114
        // số điện thoại 37 - 10
        // email 16 - 115
        let newCustomerInfoState = [...customerInfoState];
        if (isSamePerson) {
            const customerInfoGroupIndex = newCustomerInfoState.findIndex(group => group.InsInforGroupID == 7)
            const insuredPersonInfoGroupIndex = newCustomerInfoState.findIndex(group => group.InsInforGroupID == 8)
            if (insuredPersonInfoGroupIndex > -1 && customerInfoGroupIndex > -1) {
                const propertyIdMap = [[109, 110], [108, 111], [38, 9], [12, 113], [11, 114], [37, 10], [16, 115]]
                propertyIdMap.forEach(propertyPair => {
                    const [customerPropertyId, insuredPersonPropertyId] = propertyPair;
                    const customerPropertyIdIndex = newCustomerInfoState[customerInfoGroupIndex].PropertyList.findIndex(form => form.PropertyID == customerPropertyId)
                    const insuredPersonPropertyIdIndex = newCustomerInfoState[insuredPersonInfoGroupIndex].PropertyList.findIndex(form => form.PropertyID == insuredPersonPropertyId)
                    if (customerPropertyIdIndex > -1 && insuredPersonPropertyIdIndex > -1) {
                        if (customerPropertyId == 109) {
                            const customerSelectedObject = newCustomerInfoState[customerInfoGroupIndex].PropertyList[customerPropertyIdIndex].Data.find(item => item.BusinesTypePropertyValueID == newCustomerInfoState[customerInfoGroupIndex].PropertyList[customerPropertyIdIndex].Value)
                            if (!helper.IsEmptyObject(customerSelectedObject)) {
                                const insuredPersonMapObject = newCustomerInfoState[insuredPersonInfoGroupIndex].PropertyList[insuredPersonPropertyIdIndex].Data.find(item => item.PartnerValue == customerSelectedObject.PartnerValue)
                                if (!helper.IsEmptyObject(insuredPersonMapObject)) {
                                    newCustomerInfoState[insuredPersonInfoGroupIndex].data[insuredPersonPropertyIdIndex].Value = insuredPersonMapObject.BusinesTypePropertyValueID
                                }
                            }
                        } else {
                            newCustomerInfoState[insuredPersonInfoGroupIndex].PropertyList[insuredPersonPropertyIdIndex].Value = newCustomerInfoState[customerInfoGroupIndex].PropertyList[customerPropertyIdIndex].Value
                            newCustomerInfoState[insuredPersonInfoGroupIndex].data[insuredPersonPropertyIdIndex].Value = newCustomerInfoState[customerInfoGroupIndex].PropertyList[customerPropertyIdIndex].Value
                        }
                    }
                });
            }
            setCustomerInfoState(newCustomerInfoState);
        }
        const isValidForm = validateInsuranceInfo(newCustomerInfoState);
        if (isValidForm) {
            onNextStep(customerInfoState)
        }
    }

    const validateCustomerAge = () => {
        const insuredPersonInfoGroupIndex = customerInfoState.findIndex(group => group.InsInforGroupID == 8)
        if (insuredPersonInfoGroupIndex > -1) {
            const insuredPersonBirthdayForm = customerInfoState[insuredPersonInfoGroupIndex].PropertyList.find(form => form.PropertyID == 112)
            if (!helper.IsEmptyObject(insuredPersonBirthdayForm)) {
                if (moment().diff(moment(insuredPersonBirthdayForm.Value, "DD/MM/YYYY"), 'years') < 18) {
                    Alert.alert("", "Tuổi người mua bảo hiểm phải lớn hơn hoặc bằng 18 tuổi!")
                    return false;
                }
            }
        }
        return true;
    }

    const validateInsuranceInfo = (formState) => {
        const invalidForm = formState.reduce((forms, formGroup) => [...forms, ...formGroup.PropertyList], []).find(form => form.IsObliged == 1 && (!helper.IsNonEmptyString(form.Value) && !helper.isNumber(form.Value)))
        console.log(JSON.stringify(invalidForm));
        if (!helper.IsEmptyObject(invalidForm)) {
            const { Label } = invalidForm;
            Alert.alert("", `${Label.split(':')[0]} không hợp lệ, bạn vui lòng kiểm tra lại!`)
            return false;
        } else {
            return true;
        }
    }

    const renderSectionHeader = ({ section: { title, InsInforGroupID } }) => {
        if (isSamePerson && InsInforGroupID == 8) return null
        if (selectedBuiseness == false && InsInforGroupID == 53) return null
        return (
            <MyText
                text={title}
                style={{
                    fontWeight: 'bold',
                    padding: 5
                }}
                addSize={1}
            />
        );
    }

    const renderSectionFooter = ({ section: { InsInforGroupID } }) => {
        return null
    }


    const handleHiddenPropertyForm = (form, groupIndex) => {
        if (!form) return null;

        const { PropertyID } = form;
        const hiddenPropertyIDs = [19, 45, 24, 123, 124, 125];

        if (hiddenPropertyIDs.includes(PropertyID)) {

            const radioForm119 = customerInfoState[groupIndex]?.PropertyList?.find(f => f.PropertyID === 119);
            const radioForm7 = customerInfoState[0]?.PropertyList?.find(f => f.PropertyID === 7);

            let isDoanhNghiepSelected = false;
            let isCongTySelected = false;

            if (radioForm119) {
                const selectedOption119 = radioForm119.Data?.find(item => item.BusinesTypePropertyValueID === radioForm119.Value);
                isDoanhNghiepSelected = selectedOption119 && selectedOption119.Value === "Doanh Nghiệp";
            }

            if (radioForm7) {
                const selectedOption7 = radioForm7.Data?.find(item => item.BusinesTypePropertyValueID === radioForm7.Value);
                isCongTySelected = selectedOption7 && selectedOption7.Value === "Công ty";
                setSelectedBuiseness(isCongTySelected)
            } else {
                setSelectedBuiseness(false)
            }

            const shouldShowHiddenForms = isDoanhNghiepSelected || isCongTySelected;

            if (shouldShowHiddenForms) {
                form.IsObliged = 1;
                return form;
            } else {
                form.IsObliged = 0;
                return null;
            }
        }
        return form;
    }

    const renderForm = ({ item: form, index, section }) => {
        const { groupIndex, InsInforGroupID } = section
        if (isSamePerson && InsInforGroupID == 8) return null
        const { DataType, PropertyID } = form;

        const processedForm = handleHiddenPropertyForm(form, groupIndex);
        if (!processedForm) return null;

        switch (DataType) {
            case 0:
                return <FormTextInput
                    key={`${index}`}
                    textInputData={processedForm}
                    onSubmit={onSubmitText(groupIndex, index)}
                />
            case 2:
                return <FormDatePicker
                    key={`${index}`}
                    datePickerData={processedForm}
                    onSelectDate={onSelectDate(groupIndex, index)}
                    isDisabled={(InsInforGroupID == 8 && PropertyID == 112) || (isSamePerson && PropertyID == 36)}
                />
            case 3:
                return <FormRadio
                    key={`${index}`}
                    radioData={processedForm}
                    onCheck={onCheckRadio(groupIndex, index)}
                />
            case 4:
                return <FormPicker
                    key={`${index}`}
                    pickerData={processedForm}
                    onSelect={onSelectItem(groupIndex, index, form)}
                />
            case 5:
                return <FormCheckbox
                    key={`${index}`}
                    checkboxData={processedForm}
                    onSelectCheckbox={onCheckRadio(groupIndex, index)}
                />
            case 6:
                return <FormLocationPicker
                    key={`${index}`}
                    locationPickerData={processedForm}
                    onSelectProvince={onSelectProvince(groupIndex, index)}
                    onSelectDistrict={onSelectDistrict(groupIndex, index)}
                    onSelectWard={onSelectWard(groupIndex, index)}
                />
            default:
                return <></>
        }
    }

    return (
        <KeyboardAwareScrollView
            style={{
                flex: 1,
                backgroundColor: COLORS.bgFFFFFF
            }}
            enableResetScrollToCoords={true}
            keyboardShouldPersistTaps="always"
            bounces={false}
            overScrollMode="always"
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            extraScrollHeight={150}
        >
            <SectionList
                sections={customerInfoState}
                keyExtractor={(item, index) => `${index}`}
                renderItem={renderForm}
                renderSectionHeader={renderSectionHeader}
                renderSectionFooter={renderSectionFooter}
            />
            <View style={{
                marginTop: 10
            }}>
                <ButtonDirectionStep
                    onBack={onBack}
                    onNextStep={nextStep}
                />
            </View>
        </KeyboardAwareScrollView>
    )
}

const mapStateToProps = function (state) {
    return {
        itemCatalog: state.collectionReducer.itemCatalog,
        updateHeaderAirtime: state.insuranceAirtimeServiceReducer.updateHeaderAirtime,
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionInsuranceAirtimeService: bindActionCreators(actionInsuranceAirtimeServiceCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(CustomerInformation);