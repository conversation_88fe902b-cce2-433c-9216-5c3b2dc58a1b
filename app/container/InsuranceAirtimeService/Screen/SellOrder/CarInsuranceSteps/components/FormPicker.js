import { View, StyleSheet } from 'react-native'
import React from 'react'
import { MyText, PickerSearch } from '@components'
import { COLORS } from '@styles';
import { constants } from '@constants';
import { helper } from '@common';

export default function FormPicker({ pickerData, onSelect }) {

    const { Label, Data, Value, IsObliged, PropertyID } = pickerData;
    const isRequired = IsObliged == 1;

    const valueKey = helper.isNumber(Data?.[0]?.PropertyValueID) ? "PropertyValueID" : "BusinesTypePropertyValueID"

    return (
        <View style={{
            width: constants.width,
            paddingVertical: 5,
            paddingHorizontal: 10
        }}>
            <MyText
                text={Label}
                style={{
                    color: COLORS.txt999999,
                    fontWeight: 'bold'
                }}
                addSize={-1}
            >
                {isRequired && <MyText
                    text={' *'}
                    style={{
                        fontWeight: 'bold',
                        color: COLORS.txtFF0000
                    }}
                    addSize={1.5}
                />}
            </MyText>
            <PickerSearch
                label={"Value"}
                value={valueKey}
                defaultLabel={Label}
                data={Data}
                valueSelected={Value}
                onChange={(item) => {
                    console.log(item);
                    if (item[valueKey] != Value) {
                        onSelect(item[valueKey], PropertyID, item.PartnerValue)
                    }
                }}
                style={{
                    width: constants.width - 40,
                    borderWidth: StyleSheet.hairlineWidth,
                    paddingLeft: 10,
                    paddingVertical: 10,
                    marginVertical: 5,
                    marginHorizontal: 10,
                    borderColor: COLORS.bd808080,
                    borderRadius: 5,
                    flexDirection: 'row',
                    alignItems: 'center'
                }}
            />
        </View>
    )
}