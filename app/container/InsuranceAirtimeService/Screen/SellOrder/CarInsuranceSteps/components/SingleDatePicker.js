import React, { useEffect, useState } from 'react';
import {
    View,
    TouchableOpacity,
    FlatList,
    Text,
    StyleSheet
} from 'react-native';
import moment from 'moment';
import { Calendar, LocaleConfig } from 'react-native-calendars';
import KModal from 'react-native-modal';
import { constants } from '@constants';
import { COLORS } from '@styles';
import { Icon, MyText } from '@components';
import { translate } from '@translate';

function SingleDatePicker({
    isVisible,
    hideModal,
    selectedDate,
    onSubmit,
    isAllowSelectYear = false,
    isBirthday = false,
    isAllowSelectPrevious = true
}) {
    const [date, setDate] = useState(selectedDate);
    const [markedDates, setMarkedDates] = useState({});
    const [isSelectYear, setIsSelectYear] = useState(isAllowSelectYear);
    const [isSelectMonth, setIsSelectMonth] = useState(false);
    const [selectedYear, setSelectedYear] = useState(
        selectedDate.getFullYear()
    );
    const [years, setYears] = useState(
        generateYear(new Date().getFullYear() - (isBirthday ? 30 : 0))
    );

    useEffect(() => {
        if (isVisible) {
            setDate(selectedDate);
            setIsSelectYear(isAllowSelectYear);
            setIsSelectMonth(false);
        }
    }, [isVisible]);

    LocaleConfig.locales.vi = {
        monthNames: [
            'Tháng 1',
            'Tháng 2',
            'Tháng 3',
            'Tháng 4',
            'Tháng 5',
            'Tháng 6',
            'Tháng 7',
            'Tháng 8',
            'Tháng 9',
            'Tháng 10',
            'Tháng 11',
            'Tháng 12'
        ],
        monthNamesShort: [
            'Tháng 1',
            'Tháng 2',
            'Tháng 3',
            'Tháng 4',
            'Tháng 5',
            'Tháng 6',
            'Tháng 7',
            'Tháng 8',
            'Tháng 9',
            'Tháng 10',
            'Tháng 11',
            'Tháng 12'
        ],
        dayNames: [
            'Chủ Nhật',
            'Thứ Hai',
            'Thứ Ba',
            'Thứ Tư',
            'Thứ Năm',
            'Thứ Sáu',
            'Thứ Bảy'
        ],
        dayNamesShort: ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'],
        today: "Aujourd'hui"
    };
    LocaleConfig.defaultLocale = 'vi';

    const onDayPress = (date) => {
        setDate(new Date(date.timestamp));
        setMarkedDates({
            [date.dateString]: {
                selected: true,
                color: COLORS.txt50CEBB,
                marked: true,
                selectedColor: COLORS.txt50CEBB
            }
        });
    };

    useEffect(() => {
        setMarkedDates({
            [moment(date).format('YYYY-MM-DD')]: {
                selected: true,
                color: COLORS.txt50CEBB,
                marked: true,
                selectedColor: COLORS.txt50CEBB
            }
        });
        setSelectedYear(selectedDate.getFullYear());
    }, [selectedDate, date]);

    const renderHeader = (date) => {
        return (
            <TouchableOpacity
                onPress={() => {
                    setIsSelectYear(true);
                }}
                style={{
                    borderBottomWidth: StyleSheet.hairlineWidth,
                    borderBottomColor: COLORS.bdDDDDDD
                }}
            >
                <MyText
                    text={`${LocaleConfig.locales.vi.monthNames[date.getMonth()]} ${date.getFullYear()}`}
                    style={{
                        fontSize: 16,
                        color: COLORS.txt288AD6,
                        fontWeight: 'bold'
                    }}
                />
            </TouchableOpacity>
        );
    };

    const renderYearItem = ({ item, index }) => (
        <TouchableOpacity
            style={{
                width: (constants.width - 20) / 3,
                height: 65,
                alignItems: 'center',
                justifyContent: 'center'
            }}
            onPress={() => {
                setSelectedYear(item);
                setIsSelectMonth(true);
            }}
            activeOpacity={0.8}
            key={`${index}`}
        >
            <MyText
                text={item}
                style={{
                    fontSize: 20,
                    fontWeight: 'bold',
                    color:
                        item == date.getFullYear()
                            ? COLORS.txt50CEBB
                            : COLORS.txt555555
                }}
            />
        </TouchableOpacity>
    );

    const renderYearSelector = () => {
        return (
            <View style={{
                width: constants.width - 20,
                height: 320,
                alignItems: 'center'
            }}>
                <View style={{
                    width: constants.width - 20,
                    height: 50,
                    paddingHorizontal: 12,
                    paddingVertical: 8,
                    alignItems: 'center',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    borderBottomWidth: StyleSheet.hairlineWidth,
                    borderBottomColor: COLORS.bg8E8E93,
                    backgroundColor: COLORS.bgF0F0F0
                }}>
                    <TouchableOpacity
                        onPress={() => setYears(generateYear(years[0] - 1))}
                    >
                        <Icon
                            iconSet={'Feather'}
                            name={'chevrons-left'}
                            color={COLORS.txt50CEBB}
                            size={25}
                        />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => setIsSelectYear(false)}>
                        <MyText
                            text={`${years[0]} - ${years[11]}`}
                            style={{
                                fontSize: 16,
                                color: COLORS.txt288AD6,
                                fontWeight: '600'
                            }}
                        />
                    </TouchableOpacity>
                    <TouchableOpacity
                        onPress={() => setYears(generateYear(years[11] + 1))}
                    >
                        <Icon
                            iconSet={'Feather'}
                            name={'chevrons-right'}
                            color={COLORS.txt50CEBB}
                            size={25}
                        />
                    </TouchableOpacity>
                </View>

                <FlatList
                    data={years}
                    renderItem={renderYearItem}
                    keyExtractor={(item, index) => index.toString()}
                    horizontal={false}
                    numColumns={3}
                />
            </View>
        );
    };

    const renderMonthItem = ({ item, index }) => (
        <TouchableOpacity
            style={{
                width: (constants.width - 20) / 3,
                height: 65,
                alignItems: 'center',
                justifyContent: 'center'
            }}
            onPress={() => {
                setDate(new Date(selectedYear, index, date.getDate()));
                setIsSelectMonth(false);
                setIsSelectYear(false);
            }}
            activeOpacity={0.8}
        >
            <MyText
                text={item}
                style={{
                    fontSize: 20,
                    color: COLORS.txt288AD6,
                    fontWeight: '600',
                    color:
                        index == date.getMonth()
                            ? COLORS.txt50CEBB
                            : COLORS.txt555555
                }}
            />
        </TouchableOpacity>
    );

    const renderMonthSelector = () => {
        const months = LocaleConfig.locales.vi.monthNames;
        return (
            <View style={{
                width: constants.width - 20,
                height: 320,
                alignItems: 'center'
            }}>
                <View style={{
                    width: constants.width - 20,
                    height: 50,
                    paddingVertical: 12,
                    alignItems: 'center',
                    borderBottomWidth: StyleSheet.hairlineWidth,
                    borderBottomColor: COLORS.bg8E8E93
                }}>
                    <TouchableOpacity onPress={() => setIsSelectMonth(false)}>
                        <MyText
                            text={`Năm ${selectedYear}`}
                            style={{
                                fontSize: 20,
                                color: COLORS.txt288AD6,
                                fontWeight: '600'
                            }}
                        />
                    </TouchableOpacity>
                </View>

                <FlatList
                    data={months}
                    renderItem={renderMonthItem}
                    keyExtractor={(item, index) => index.toString()}
                    horizontal={false}
                    numColumns={3}
                />
            </View>
        );
    };
    return (
        <KModal
            isVisible={isVisible}
            style={{
                borderRadius: 4,
                justifyContent: 'center',
                alignItems: 'center'
            }}
            deviceWidth={constants.width}
            deviceHeight={constants.height}
            backdropTransitionOutTiming={0}
            animationIn={'slideInUp'}
            animationOut={'slideOutDown'}
            useNativeDriver={true}
            hideModalContentWhileAnimating={true}
        >
            <View style={{
                backgroundColor: COLORS.bgF0F0F0,
                width: constants.width - 20
            }}>
                {isSelectYear ? (
                    isSelectMonth ? (
                        renderMonthSelector()
                    ) : (
                        renderYearSelector()
                    )
                ) : (
                    <Calendar
                        initialDate={moment(date).format('YYYY-MM-DD')}
                        markedDates={markedDates}
                        onDayPress={onDayPress}
                        renderHeader={renderHeader}
                        minDate={isAllowSelectPrevious ? '' : moment(new Date()).format('YYYY-MM-DD')}
                        maxDate={isBirthday ? moment(new Date()).format('YYYY-MM-DD') : ''}
                        theme={{
                            // calendarBackground: colors.background10,
                            // textSectionTitleColor: colors.success,
                            // selectedDayBackgroundColor: colors.outlineVariant,
                            // selectedDayTextColor: colors.onPrimary,
                            // todayTextColor: COLORS.txt50CEBB,
                            // dayTextColor: colors.text,
                            // dotColor: colors.white,
                            // selectedDotColor: colors.white,
                            // arrowColor: COLORS.txt50CEBB,
                            // monthTextColor: colors.success,
                            // textMonthFontWeight: 'bold',
                            // textDayHeaderFontWeight: 'bold',
                            // textDisabledColor: colors.outline
                        }}
                    />
                )}
                <View style={{
                    paddingHorizontal: 20,
                    marginVertical: 10,
                    flexDirection: 'row',
                    justifyContent: 'center'
                }}>
                    {!isSelectYear && !isSelectMonth && (
                        <TouchableOpacity
                            style={{
                                width: 100,
                                height: 'auto',
                                borderRadius: 4,
                                padding: 10,
                                backgroundColor: COLORS.txt50CEBB,
                                alignItems: 'center',
                                justifyContent: 'center'
                            }}
                            activeOpacity={0.7}
                            onPress={() => {
                                hideModal();
                                onSubmit(date);
                            }}
                        >
                            <MyText
                                text={translate('common.btn_confirm')}
                                style={{
                                    fontWeight: 'bold',
                                    color: COLORS.btnFFFFFF
                                }}
                            />
                        </TouchableOpacity>
                    )}
                    {(isSelectYear || isSelectMonth) && (
                        <TouchableOpacity
                            style={{
                                width: 100,
                                height: 'auto',
                                borderRadius: 4,
                                padding: 10,
                                backgroundColor: COLORS.txt50CEBB,
                                alignItems: 'center',
                                justifyContent: 'center'
                            }}
                            activeOpacity={0.7}
                            onPress={hideModal}
                        >
                            <MyText
                                text={translate('common.btn_close')}
                                style={{
                                    fontWeight: 'bold',
                                    color: COLORS.btnFFFFFF
                                }}
                            />
                        </TouchableOpacity>
                    )}
                </View>
            </View>
        </KModal>
    );
}

export default SingleDatePicker;

const generateYear = (year) =>
    Array.from({ length: 12 }, (_, i) => i - (year % 12) + year);

// ------------------------ Usage -----------------//

/**
this.state = {
  singleSelectedDate: new Date()
}

<SingleDatePicker
  width={200}
  selectedDate={singleSelectedDate}
  onDateSelected={(date) => {
    this.setState({ singleSelectedDate: new Date(date) })
  }}
/>
*/
