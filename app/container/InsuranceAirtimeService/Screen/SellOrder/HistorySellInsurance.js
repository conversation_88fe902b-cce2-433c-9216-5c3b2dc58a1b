import { <PERSON><PERSON>ist, SafeAreaView, StyleSheet, Animated, TouchableOpacity, View, Alert } from 'react-native'
import React, { useState, useEffect, useCallback } from 'react'
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'
import { constants } from '@constants'
import { BaseLoading, Icon, MyText, PickerSearch, hideBlockUI, showBlockUI } from '@components'
import { COLORS } from '@styles';
import moment from 'moment';
import ModalCalendar from '../../../InstallmentManager/components/ModalCalendar';
import SearchInput from '../../component/SearchInput/SearchInput'
import ItemCollection from '../../component/ItemFiled/ItemCollection'
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux';
import * as actionInsuranceAirtimeServiceCreator from "../../action";
import * as actionSaleOrderCreator from "../../../SaleOrderPayment/action";
import * as actionManagerSOCreator from "../../../SaleOrderManager/action";
import { translate } from '@translate';
import { useFocusEffect } from '@react-navigation/native';
import { helper } from '@common'
import CheckFilter from '../../component/CheckFilter/CheckFilter'

const MAX_HEIGHT = 117;
const MIN_HEIGHT = 40;
const DISTANCE = MAX_HEIGHT - MIN_HEIGHT;

const HistorySellInsurance = ({
  stateSearchListHistory,
  dataSearchListHistory,
  actionInsuranceAirtimeService,
  actionManagerSO,
  actionSaleOrder,
  navigation,
  route,
  itemCatalog
}) => {
  const {
    ServiceCategoryID,
    AirtimeServiceGroupID
  } = itemCatalog ?? {};
  const [filter, setFilter] = useState({
    scrollY: new Animated.Value(0),
    isIncome: 0,
    isCompleteInfo: 0,
    isDelivery: 0,
  });
  const { SaleOrderID, CatalogID, ServiceGroupID } = route?.params ?? {};
  const [fromDate, setFromDate] = useState(new Date())
  const [toDate, setToDate] = useState(new Date())
  const [isShowCalendar, setIsShowCalendar] = useState(false);
  const [searchByInsurance, setSearchByInsurance] = useState('');
  const [providerInsurance, setProviderInsurance] = useState('');
  const [keyword, setkeyWork] = useState('');
  const [dataInsurance, setDataInsurance] = useState([])
  const [dataProvider, setProvider] = useState([])
  const [dataHistoryInsurance, setDataHistoryInsurance] = useState();
  const [isDelete, setIsDelete] = useState(false);
  const diffClamp = Animated.diffClamp(filter.scrollY, 0, DISTANCE);

  const translateY = diffClamp.interpolate({
    inputRange: [0, DISTANCE],
    outputRange: [0, -DISTANCE],
  });

  useFocusEffect(
    useCallback(() => {
      const data = {
        catalogID: ServiceCategoryID,
        serviceGroupID: AirtimeServiceGroupID,
        isLoadSearchBy: true
      };
      if (ServiceCategoryID == 3 && AirtimeServiceGroupID == 29) {
        data.isSearchByQuery = false;
      }
      actionInsuranceAirtimeService.getServiceListHistory(data).then((response) => {
        setDataInsurance(response[0].SearchByList)
        setProvider(response[1].ProviderList)
      }).catch((err) => console.log(err));
    }, [actionInsuranceAirtimeService])
  );

  useFocusEffect(
    useCallback(() => {
      if (helper.IsNonEmptyString(SaleOrderID)) {
        setkeyWork(SaleOrderID)
        getSearchHistoryInsurance({
          keyword: SaleOrderID,
          fromDate: fromDate,
          toDate: toDate,
          isDelete: isDelete,
          searchType: searchByInsurance,
          catalogID: ServiceCategoryID,
          serviceGroupID: AirtimeServiceGroupID,
          airtimetrsTypeIDList: providerInsurance,
        });
      } else {
        getSearchHistoryInsurance({
          keyword: keyword,
          fromDate: fromDate,
          toDate: toDate,
          isDelete: isDelete,
          searchType: searchByInsurance,
          catalogID: ServiceCategoryID,
          serviceGroupID: AirtimeServiceGroupID,
          airtimetrsTypeIDList: providerInsurance,
        });
      }
    }, [actionInsuranceAirtimeService])
  );

  const getSearchHistoryInsurance = ({
    keyword: keyword,
    fromDate: fromDate,
    toDate: toDate,
    isDelete: isDelete,
    searchType: searchByInsurance,
    catalogID: ServiceCategoryID,
    serviceGroupID: AirtimeServiceGroupID,
    airtimetrsTypeIDList: providerInsurance
  }) => {
    const data = {
      keyword: keyword,
      fromDate: fromDate,
      toDate: toDate,
      isDelete: isDelete,
      searchType: searchByInsurance,
      catalogID: ServiceCategoryID,
      serviceGroupID: AirtimeServiceGroupID,
      airtimetrsTypeIDList: providerInsurance,
    }
    actionInsuranceAirtimeService.getSearchHistoryInsurance(data)
  }

  useEffect(() => {
    setDataHistoryInsurance(dataSearchListHistory)
  }, [dataSearchListHistory]);

  const onSubmit = (keyword) => {
    const data = {
      catalogID: ServiceCategoryID,
      serviceGroupID: AirtimeServiceGroupID,
      keyword: keyword,
      fromDate: new Date(fromDate),
      toDate: new Date(toDate),
      searchType: searchByInsurance,
      airtimetrsTypeIDList: providerInsurance,
    }
    actionInsuranceAirtimeService.getSearchHistoryInsurance(data)
  }

  useEffect(() => {
    setDataHistoryInsurance(dataSearchListHistory)
  }, [dataSearchListHistory])

  const onAskStatusInsurance = (item) => {
    showBlockUI();
    const {
      SALEORDERID
    } = item;
    const data = {
      saleOrderID: SALEORDERID
    }
    actionInsuranceAirtimeService.queryTransactionPartner(data).then((reponse) => {
      hideBlockUI();
      if (reponse) {
        Alert.alert("", reponse.airtimestatusname, [
          {
            text: "OK",
            onPress: () => {
              hideBlockUI();
              setkeyWork(SALEORDERID);
              getSearchHistoryInsurance({
                keyword: SALEORDERID,
                fromDate: new Date(fromDate),
                toDate: new Date(toDate),
                searchType: searchByInsurance,
                catalogID: ServiceCategoryID,
                serviceGroupID: AirtimeServiceGroupID,
                airtimetrsTypeIDList: providerInsurance,
              })
            }
          }
        ])
      }

    }).catch(msgError => {
      Alert.alert("", msgError, [
        {
          text: translate('common.btn_skip'),
          onPress: hideBlockUI
        },
        {
          text: translate('common.btn_notify_try_again'),
          onPress: () =>
            onAskStatusInsurance(item),

        }
      ])
    });
  }

  const handleSaleOrderPayment = async (SOInfo) => {
    const { SALEORDERID, ORDERTYPEID } = SOInfo;
    try {
      showBlockUI();
      actionSaleOrder.setDataSO({
        SaleOrderID: SALEORDERID,
        SaleOrderTypeID: 100
      })
      const dataSaleOrder = await actionSaleOrder.getSaleOrderPayment(SALEORDERID);
      hideBlockUI();
      if (dataSaleOrder.cus_WarningMessage) {
        Alert.alert(
          translate("common.notification"),
          dataSaleOrder.cus_WarningMessage,
          [
            {
              text: translate("common.customer_decline"),
              onPress: () => {}
            },
            {
              text: translate("common.customer_accept"),
              onPress: hideBlockUI,
              style: "cancel"
            }
          ]
        );
      } else {
        navigation.navigate("SaleOrderPayment", { shouldOriginUpdate: true });
        actionSaleOrder.getReportPrinterSocket(ORDERTYPEID);
        actionSaleOrder.getDataQRTransaction(SALEORDERID);
        actionSaleOrder.getDataSCTransaction(SALEORDERID);
      }
    } catch (error) {
      Alert.alert(translate('common.notification_uppercase'), error, [
        {
          text: translate('common.btn_skip'),
          onPress: hideBlockUI
        },
        {
          text: translate('common.btn_notify_try_again'),
          onPress: () => handleSaleOrderPayment(SOInfo),
        }
      ]);
    }
  }

  const onPrint = (item) => {
    const { SALEORDERID, ORDERTYPEID } = item;
    actionSaleOrder.setDataSO({
      SaleOrderID: SALEORDERID,
      SaleOrderTypeID: 100
    }).then(success => {
      actionManagerSO.getContentTypeReport(SALEORDERID);
      actionSaleOrder.getReportPrinterSocket(100);
      navigation.navigate("ReprintSaleOrder");
    })
  }

  const handleDeleteSOInsurance = (info) => {
    const { SALEORDERID, ORDERTYPEID, TICKETID, AIRTIMETRANSACTIONID, SERVICEVOUCHERID } = info;
    showBlockUI();
    actionManagerSO.getInfoSODelete(SALEORDERID).then(saleOrder => {
      const { SaleOrderID, OrderTypeID } = saleOrder;
      hideBlockUI();
      actionSaleOrder.setDataSO({
        SaleOrderID: SaleOrderID,
        SaleOrderTypeID: OrderTypeID
      }).then(success => {
        const { TotalPaid, IsIncome, OrderTypeID } = saleOrder;
        const isCancel = IsIncome && (TotalPaid > 0);
        if (isCancel) {
          navigation.navigate("CancelSO", {
            TICKETID,
            AIRTIMETRANSACTIONID,
            SERVICEVOUCHERID
          });
          actionSaleOrder.getReportPrinterSocket(OrderTypeID);
        }
        else {
          navigation.navigate("DeleteSO");
        }

      })
    }).catch(msgError => {
      Alert.alert(translate('common.notification_uppercase'), msgError, [
        {
          text: translate('common.btn_skip'),
          onPress: hideBlockUI,
        },
        {
          text: translate('common.btn_notify_try_again'),
          onPress: () => handleDeleteSOInsurance(info),
        },
      ]);
    })
  }

  const handlePreEnterNavigatorOTPAirtime = (item) => {
    showBlockUI();
    const { SALEORDERID } = item ?? {};
    const isCalled = false
    actionSaleOrder.setDataSO({
      SaleOrderID: SALEORDERID,
      SaleOrderTypeID: 100
    }).then(success => {
      hideBlockUI();
      actionManagerSO.getContentTypeReport(SALEORDERID);
      actionSaleOrder.getReportPrinterSocket(100);
      navigation.navigate("OTPAirtimeService", {
        SaleOrderID: SALEORDERID,
        isCalled
      });
    }).catch((msgError) => {
      Alert.alert("", msgError, [
        {
          text: "OK",
          onPress: () => {
            hideBlockUI();
          },
        },
      ]);
    });
  }

  const renderFooter = () => {
    return (
      <View
        style={{
          height: 300,
        }}
      />
    );
  };

  const printPromotionCollection = (item) => {
    const { PROMOTIONSALEORDERID } = item;
    actionSaleOrder.setDataSO({
      SaleOrderID: PROMOTIONSALEORDERID,
      SaleOrderTypeID: 100
    }).then(success => {
      actionManagerSO.getContentTypeReport(PROMOTIONSALEORDERID);
      actionSaleOrder.getReportPrinterSocket(100);
      navigation.navigate("ReprintSaleOrder");
    })
  }

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: "white",
      }}
    >
      <KeyboardAwareScrollView
        style={{
          flex: 1,
        }}
        enableResetScrollToCoords={false}
        keyboardShouldPersistTaps="always"
        bounces={false}
        overScrollMode="always"
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        extraScrollHeight={60}
      >
        <SafeAreaView
          style={{
            flex: 1,
            alignItems: "center",
            justifyContent: 'center'
          }}
        >
          <Animated.View style={{
            transform: [{ translateY: translateY }],
            backgroundColor: COLORS.bgF5F5F5,
            position: 'relative',
            top: 0, left: 0, right: 0, zIndex: 1,
          }}>
            <View
              style={{
                width: constants.width - 20,
                marginTop: 5
              }}
            >
              <View
                style={{
                  width: constants.width,
                }}
              >
                <CheckFilter
                  isDelete={isDelete}
                  onChangeParam={(value) => {
                    setIsDelete(value);
                    getSearchHistoryInsurance({
                      keyword: keyword,
                      fromDate: new Date(fromDate),
                      toDate: new Date(toDate),
                      isDelete: value,
                      searchType: searchByInsurance,
                      catalogID: ServiceCategoryID,
                      serviceGroupID: AirtimeServiceGroupID,
                      airtimetrsTypeIDList: providerInsurance,
                    })
                  }
                  }
                  disabled={false}
                />
              </View>
              <TouchableOpacity style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                backgroundColor: COLORS.bgFFFFFF,
                borderRadius: 5,
                width: '100%',
                paddingHorizontal: 5,
                borderWidth: 1,
                borderColor: COLORS.bdDDDDDD,
                height: 44,
                alignSelf: 'center'
              }}
                onPress={() => setIsShowCalendar(true)}
              >
                <MyText
                  style={{
                    width: '87%',
                    paddingHorizontal: 5
                  }}
                  text={`${moment(fromDate).format(
                    'DD/MM/YYYY'
                  )
                    } - ${moment(toDate).format(
                      'DD/MM/YYYY'
                    )
                    } `}
                />
                <Icon
                  iconSet="Feather"
                  name="calendar"
                  style={{
                    fontSize: 30,
                    color: COLORS.ic2C8BD7
                  }}
                />
              </TouchableOpacity>
              <PickerSearch
                label={"AirTimeTransactionTypeName"}
                value={"AirTimeTransactionTypeID"}
                valueSelected={providerInsurance}
                data={dataProvider}
                onChange={(item) => {
                  setProviderInsurance(item.AirTimeTransactionTypeID)
                  getSearchHistoryInsurance({
                    keyword: keyword,
                    fromDate: new Date(fromDate),
                    toDate: new Date(toDate),
                    isDelete: isDelete,
                    searchType: searchByInsurance,
                    catalogID: ServiceCategoryID,
                    serviceGroupID: AirtimeServiceGroupID,
                    airtimetrsTypeIDList: item.AirTimeTransactionTypeID,
                  })
                }}
                style={{
                  flex: 1,
                  flexDirection: "row",
                  justifyContent: "center",
                  alignItems: "center",
                  height: 40,
                  borderRadius: 4,
                  borderWidth: 1,
                  borderColor: COLORS.bdE4E4E4,
                  width: constants.width - 20,
                  backgroundColor: COLORS.btnFFFFFF,
                  marginTop: 5
                }}
                defaultLabel={"Nhà cung cấp"}
              />
              <PickerSearch
                label={"Label"}
                value={"SearchTypeID"}
                valueSelected={searchByInsurance}
                data={dataInsurance}
                onChange={(item) => {
                  setSearchByInsurance(item.SearchTypeID)
                  getSearchHistoryInsurance({
                    keyword: keyword,
                    fromDate: new Date(fromDate),
                    toDate: new Date(toDate),
                    isDelete: isDelete,
                    searchType: item.SearchTypeID,
                    catalogID: ServiceCategoryID,
                    serviceGroupID: AirtimeServiceGroupID,
                    airtimetrsTypeIDList: providerInsurance,
                  })
                }}
                style={{
                  flex: 1,
                  flexDirection: "row",
                  justifyContent: "center",
                  alignItems: "center",
                  height: 40,
                  borderRadius: 4,
                  borderWidth: 1,
                  borderColor: COLORS.bdE4E4E4,
                  width: constants.width - 20,
                  backgroundColor: COLORS.btnFFFFFF,
                  marginBottom: 5,
                  marginTop: 5
                }}
                defaultLabel={"Tìm theo"}
              />
            </View>
          </Animated.View>
          <SearchInput
            onSubmit={() => onSubmit(keyword)}
            inputText={keyword}
            onChangeText={(text) => {
              setkeyWork(text)
            }}
            onClearText={() => {
              setkeyWork('');
            }}
            placeholder={'Từ khoá'}
          />
          <BaseLoading
            isLoading={stateSearchListHistory.isFetching}
            isError={stateSearchListHistory.isError}
            isEmpty={stateSearchListHistory.isEmpty}
            textLoadingError={stateSearchListHistory.description}
            onPressTryAgains={() => {
              getSearchHistoryInsurance({
                keyword: keyword,
                fromDate: new Date(fromDate),
                toDate: new Date(toDate),
                isDelete: isDelete,
                searchType: searchByInsurance,
                catalogID: ServiceCategoryID,
                serviceGroupID: AirtimeServiceGroupID,
                airtimetrsTypeIDList: providerInsurance,
              })
            }}
            content={
              <FlatList
                style={{ marginTop: 5 }}
                data={dataHistoryInsurance}
                keyExtractor={(item, index) => `${index} `}
                renderItem={({ item, index }) => (
                  <ItemCollection
                    item={item}
                    index={index}
                    ServiceCategoryID={ServiceCategoryID}
                    AirtimeServiceGroupID={AirtimeServiceGroupID}
                    onPressDetail={() => { }}
                    onPrint={() => onPrint(item)}
                    onEdit={() => handleSaleOrderPayment(item)}
                    onAskStatus={() => onAskStatusInsurance(item)}
                    onActiveCollection={() => { }}
                    onDelete={() => handleDeleteSOInsurance(item)}
                    onActionList={() => onActionList(item)}
                    onPreEnterOTP={() => handlePreEnterNavigatorOTPAirtime(item)}
                    printPromotion={() => printPromotionCollection(item)}
                  />)
                }
                stickySectionHeadersEnabled={false}
                alwaysBounceVertical={false}
                bounces={false}
                scrollEventThrottle={16}
                ListFooterComponent={renderFooter}
              />
            }
          />
          <ModalCalendar
            isVisible={isShowCalendar}
            hideModal={() => {
              setIsShowCalendar(false);
            }}
            startDate={fromDate}
            endDate={toDate}
            setDate={(day) => {
              setFromDate(day.startDate)
              setToDate(day.endDate)
              getSearchHistoryInsurance({
                keyword: keyword,
                fromDate: new Date(day.startDate),
                toDate: new Date(day.endDate),
                isDelete: isDelete,
                searchType: searchByInsurance,
                catalogID: ServiceCategoryID,
                serviceGroupID: AirtimeServiceGroupID,
                airtimetrsTypeIDList: providerInsurance,
              })
            }}
          />
        </SafeAreaView>
      </KeyboardAwareScrollView>
    </View>
  )
}

const mapStateToProps = function (state) {
  return {
    itemCatalog: state.collectionReducer.itemCatalog,
    dataSearchListHistory: state.insuranceAirtimeServiceReducer.dataSearchListHistory,
    stateSearchListHistory: state.insuranceAirtimeServiceReducer.stateSearchListHistory,
  }
}

const mapDispatchToProps = function (dispatch) {
  return {
    actionInsuranceAirtimeService: bindActionCreators(actionInsuranceAirtimeServiceCreator, dispatch),
    actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),
    actionManagerSO: bindActionCreators(actionManagerSOCreator, dispatch),
  }
}
export default connect(mapStateToProps, mapDispatchToProps)(HistorySellInsurance);

const styles = StyleSheet.create({})