import {
  <PERSON><PERSON>,
  <PERSON><PERSON>View,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import React from "react";
import Accordion from "../../../component/Button/Accordion";
import { COLORS } from "@styles";
import TextField from "../../../component/Text/TextField";
import { constants } from "@constants";
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import * as actionInsuranceAirtimeServiceCreator from "../../../action";
import { helper } from "@common";
import { MyText, hideBlockUI, showBlockUI } from "@components";
import * as actionPaymentOrderCreator from "../../../../SaleOrderPayment/action";
import { translate } from "@translate";

const CreateOrderLoan = ({
  route,
  navigation,
  itemCatalog,
  dataValidateService,
  actionInsuranceAirtimeService,
  updateHeaderAirtime,
  dataGetPrice,
  dataGetPromotion,
  actionPaymentOrder,
}) => {
  const {
    customerName,
    customerPhone,
    customerHouseNumber,
    wardName,
    districName,
    provinceName,
    updateItem,
    customer,
    customerIdCard,
    birthday
  } = route?.params ?? "";
  const { InsProgramName, InsuranceID, InsProgramID, InsuranceApplyID } =
    updateItem ?? "";
  const {
    StartDateInsLoan,
    EndDateInsLoan,
    ProfileID,
    Imei,
    InsCustomerID,
    MainProductID,
    PaymentAmountMonthly,
    TermLoan,
    TotalPrePaid,
    LoanAmount,
    MainSaleOrderID,
    VoucherConcern
  } = dataValidateService ?? "";
  const { FeeInfo, PriceInfo, TotalAmount } = dataGetPrice ?? "";
  const {
    PriceVAT,
    Amount,
    SalePrice,
    InputPrice,
    PartnerData,
    ProductInfo,
  } = PriceInfo ?? "";
  const { Fee } = FeeInfo ?? "";
  const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog ?? "";
  const { AirTimeTransactionTypeID } = updateHeaderAirtime ?? "";
  const { DistrictID, ProvinceID, WardID } = customer ?? "";
  const { ProductID } = ProductInfo ?? "";
  const {
    CMD,
    SendContent,
    ResponseContent,
    SendTime,
    ResponseMessage,
    ResponseTime,
  } = PartnerData ?? "";
  const handleAddCart = () => {
    showBlockUI();
    const productPromotion = dataGetPromotion?.ExtraData?.ProductPromotion;
    const promotionID = dataGetPromotion?.PromotionID;
    const data = {
      catalogID: ServiceCategoryID,
      serviceGroupID: AirtimeServiceGroupID,
      airtimeTransactionTypeID: AirTimeTransactionTypeID,
      airTimeTransactionBO: {
        productid: ProductID,
        amount: Amount,
        fee: Fee,
        phonenumber: Imei,
        inputPrice: InputPrice,
        salePrice: SalePrice,
        customerName: customerName,
        customerAddress: customerHouseNumber,
        customerPhone: customerPhone,
        districtID: DistrictID,
        provinceID: ProvinceID,
        wardID: WardID,
        apartmentNumberAddress: customerHouseNumber,
      },
      retailPriceVAT: PriceVAT,
      insuranceID: InsuranceID,
      insCustomerID: InsCustomerID,
      insProgramID: InsProgramID,
      insuranceMonth: TermLoan,
      insuranceApplyID: InsuranceApplyID,
      mainSaleOrderID: MainSaleOrderID,
      mainProductID: MainProductID,
      productPromotion: productPromotion,
      promotionID: promotionID,
      partnerData: {
        CMD: CMD,
        SendContent: SendContent,
        ResponseContent: ResponseContent,
        SendTime: SendTime,
        ResponseMessage: ResponseMessage,
        ResponseTime: ResponseTime,
      },
      districtID: DistrictID,
      provinceID: ProvinceID,
      wardID: WardID,
      apartmentNumberAddress: customerHouseNumber,
      profileID: ProfileID,
      startDate: StartDateInsLoan,
      termLoan: TermLoan,
      totalPrePaid: TotalPrePaid,
      voucherConcern: VoucherConcern,
      paymentAmountMonthly: PaymentAmountMonthly,
      customerBirthday: birthday,
      endDate: EndDateInsLoan,
      customerIDCard: customerIdCard,
      loanAmount: LoanAmount
    };
    actionInsuranceAirtimeService
      .addToSaleOrderCart(data)
      .then((reponse) => {
        hideBlockUI();
        goToPaymentSO(reponse);
      })
      .catch((error) => {
        Alert.alert(translate("common.notification_uppercase"), error, [
          {
            text: translate("common.btn_close"),
            onPress: hideBlockUI,
          },
        ]);
      });
  };

  const goToPaymentSO = (rpSaleOrderCart) => {
    const dataSaleOrderCart = rpSaleOrderCart.object;
    const SaleOrders = dataSaleOrderCart.SaleOrders[0];
    const { SaleOrderID } = SaleOrders;
    actionPaymentOrder
      .setDataSO({
        SaleOrderID: SaleOrderID,
        SaleOrderTypeID: 1000,
      })
      .then((success) => {
        hideBlockUI();
        navigation.replace("SaleOrderPayment");
        actionPaymentOrder.getSaleOrderPayment(SaleOrderID);
        actionPaymentOrder.getReportPrinterSocket(100);
        actionPaymentOrder.getDataQRTransaction(SaleOrderID);
      });
  };

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: COLORS.bgFFFFFF,
      }}
    >
      <ScrollView
        contentContainerStyle={{
          alignItems: "center",
        }}
      >
        <Accordion
          status={true}
          title={"Thông tin người mua bảo hiểm"}
          Children={
            <View
              style={{
                backgroundColor: COLORS.bgF0F0F0,
                width: constants.width - 10,
                paddingHorizontal: 10,
                paddingBottom: 10,
              }}
            >
              <View
                style={{
                  width: "100%",
                  borderWidth: 1,
                  alignSelf: "center",
                  borderColor: "gray",
                }}
              />
              <TextField name={"Họ và Tên"} value={customerName} />
              <TextField name={"Số điện thoại"} value={customerPhone} />
              <TextField
                name={"Địa chỉ"}
                value={`${customerHouseNumber}, ${wardName}, ${districName}, ${provinceName}`}
              />
            </View>
          }
        />
        <Accordion
          status={true}
          title={"Thông tin gói bảo hiểm"}
          Children={
            <View
              style={{
                backgroundColor: COLORS.bgF0F0F0,
                width: constants.width - 10,
                paddingHorizontal: 10,
                paddingBottom: 10,
              }}
            >
              <View
                style={{
                  width: "100%",
                  borderWidth: 1,
                  alignSelf: "center",
                  borderColor: "gray",
                }}
              />
              <TextField name={"Gói bảo hiểm"} value={InsProgramName} />
              <TextField
                name={"Thời gian bảo hiểm"}
                value={`${TermLoan} tháng`}
              />
              <TextField
                name={"Ngày bắt đầu hiệu lực"}
                value={StartDateInsLoan}
              />
              <TextField name={"Ngày hết hạn"} value={EndDateInsLoan} />
            </View>
          }
        />
        <Accordion
          status={true}
          title={"Thông tin hợp đồng bảo hiểm"}
          Children={
            <View
              style={{
                backgroundColor: COLORS.bgF0F0F0,
                width: constants.width - 10,
                paddingHorizontal: 10,
                paddingBottom: 10,
              }}
            >
              <View
                style={{
                  width: "100%",
                  borderWidth: 1,
                  alignSelf: "center",
                  borderColor: "gray",
                }}
              />
              <TextField name={"Hợp đồng bảo hiểm"} value={ProfileID} />
              <TextField
                name={"Số tiền bảo hiểm"}
                value={helper.formatMoney(LoanAmount)}
              />
            </View>
          }
        />
        <Accordion
          status={true}
          titleColor="white"
          iconColor="white"
          title={"Thông tin thanh toán bảo hiểm"}
          backgroundColor={COLORS.bg2FB47C}
          Children={
            <View
              style={{
                backgroundColor: COLORS.bgF0F0F0,
                width: constants.width - 10,
                paddingHorizontal: 10,
                paddingBottom: 10,
              }}
            >
              <View
                style={{
                  width: "100%",
                  borderWidth: 1,
                  alignSelf: "center",
                  borderColor: "white",
                }}
              />
              <TextField
                name={"Phí bảo hiểm"}
                value={helper.formatMoney(TotalAmount)}
                color={COLORS.bgFF0000}
              />
              <TextField
                name={"Tổng cộng"}
                color={COLORS.bgFF0000}
                value={helper.formatMoney(TotalAmount)}
              />
            </View>
          }
        />
        <View
          style={{
            height: 50,
            flexDirection: "row",
            marginTop: 10,
            justifyContent: "center",
            alignItems: "center",
            padding: 10,
          }}
        >
          <TouchableOpacity
            onPress={() => navigation.navigate("InsuranceLoan")}
            style={{
              width: 120,
              height: 50,
              borderRadius: 18,
              borderWidth: 2,
              borderColor: COLORS.bg00A98F,
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "white",
            }}
          >
            <MyText
              text={"QUAY LẠI"}
              style={{
                fontWeight: "bold",
                color: COLORS.bg00A98F,
              }}
            />
          </TouchableOpacity>
          <View
            style={{
              width: 10,
            }}
          />
          <TouchableOpacity
            onPress={() => handleAddCart()}
            style={{
              backgroundColor: "pink",
              flex: 1,
              height: 50,
              borderRadius: 18,
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: COLORS.bgF49B0C,
            }}
          >
            <MyText
              text={"TẠO ĐƠN"}
              style={{
                fontWeight: "bold",
                color: COLORS.bgFFFFFF,
              }}
            />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

const mapStateToProps = function (state) {
  return {
    itemCatalog: state.collectionReducer.itemCatalog,
    updateHeaderAirtime:
      state.insuranceAirtimeServiceReducer.updateHeaderAirtime,
    dataValidateService:
      state.insuranceAirtimeServiceReducer.dataValidateService,
    dataGetPrice: state.insuranceAirtimeServiceReducer.dataGetPrice,
    dataGetPromotion: state.insuranceAirtimeServiceReducer.dataGetPromotion,
  };
};

const mapDispatchToProps = function (dispatch) {
  return {
    actionInsuranceAirtimeService: bindActionCreators(
      actionInsuranceAirtimeServiceCreator,
      dispatch
    ),
    actionPaymentOrder: bindActionCreators(actionPaymentOrderCreator, dispatch),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(CreateOrderLoan);

const styles = StyleSheet.create({});
