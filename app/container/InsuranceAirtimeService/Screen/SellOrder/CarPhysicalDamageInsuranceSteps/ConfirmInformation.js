import React from "react";
import { View, SafeAreaView } from "react-native";
import ButtonDirectionStep from "./components/ButtonDirectionStep";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { helper } from "@common";
import { bindActionCreators } from "redux";
import * as actionInsuranceAirtimeServiceCreator from "../../../action";
import { connect } from "react-redux";
import {
  PaymentInfo,
  ReviewCustomerInfo,
  ReviewInsuranceInfo,
} from "./components";

const ConfirmInformation = ({
  onBack,
  onNextStep,
  healthInsuranceFee,
  insuranceProperty,
  customerProperty,
  isSamePerson,
  onEditInsuranceInfo,
  onEditCustomerInfo,
}) => {
  const customerInfo = customerProperty.find(
    (formGroup) => formGroup.InsInforGroupID == 7
  );
  const insuredPersonInfo = customerProperty.find(
    (formGroup) => formGroup.InsInforGroupID == 8
  );
  const insuranceInfo = insuranceProperty.find(
    (formGroup) => formGroup.InsInforGroupID == 1
  );
  const healthStatusInfo = insuranceProperty.find(
    (formGroup) => formGroup.InsInforGroupID == 3
  );
  const priceInfo = insuranceProperty.find(formGroup => formGroup.InsInforGroupID == 58);
  const filterPropertyList = priceInfo?.PropertyList?.filter(p => p.Value != null);

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: "white",
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <KeyboardAwareScrollView
        style={{
          flex: 1,
        }}
        enableResetScrollToCoords={false}
        keyboardShouldPersistTaps={"always"}
        bounces={false}
        overScrollMode="always"
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        extraScrollHeight={60}
      >
        {!helper.IsEmptyObject(customerInfo) && (
          <ReviewCustomerInfo
            customerInfo={customerInfo}
            onEditInfo={onEditCustomerInfo}
          />
        )}
        {!helper.IsEmptyObject(insuredPersonInfo) && !isSamePerson && (
          <ReviewCustomerInfo
            customerInfo={insuredPersonInfo}
            onEditInfo={onEditCustomerInfo}
          />
        )}
        {!helper.IsEmptyObject(insuranceInfo) && (
          <ReviewInsuranceInfo
            insuranceInfo={insuranceInfo}
            healthInsuranceFee={healthInsuranceFee}
            healthStatusInfo={healthStatusInfo}
            onEditInfo={onEditInsuranceInfo}
          />
        )}
        <PaymentInfo
          healthInsuranceFee={healthInsuranceFee}
          PropertyList={filterPropertyList}
        />
        <View
          style={{
            marginTop: 10,
          }}
        >
          <ButtonDirectionStep
            onBack={onBack}
            onNextStep={onNextStep}
            nextStepTitle={"TẠO ĐƠN HÀNG"}
          />
        </View>
      </KeyboardAwareScrollView>
    </View>
  );
};

const mapStateToProps = function (state) {
  return {
    itemCatalog: state.collectionReducer.itemCatalog,
    updateHeaderAirtime:
      state.insuranceAirtimeServiceReducer.updateHeaderAirtime,
    healthInsuranceFee: state.insuranceAirtimeServiceReducer.healthInsuranceFee,
  };
};

const mapDispatchToProps = function (dispatch) {
  return {
    actionInsuranceAirtimeService: bindActionCreators(
      actionInsuranceAirtimeServiceCreator,
      dispatch
    ),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(ConfirmInformation);
