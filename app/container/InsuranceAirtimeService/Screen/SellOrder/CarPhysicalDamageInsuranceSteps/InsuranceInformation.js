import React, { useEffect, useState } from 'react'
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'
import { MyText, hideBlockUI, showBlockUI } from '@components'
import { Alert, SectionList, TouchableOpacity, View } from 'react-native'
import { bindActionCreators } from 'redux'
import * as actionInsuranceAirtimeServiceCreator from "../../../action";
import { connect } from 'react-redux'
import { helper } from '@common'
import {
    ButtonDirectionStep,
    FormContractNumber,
    FormDatePicker,
    FormPicker,
    InsuranceFee,
    InsuranceScopeNote,
    FormLocationPicker,
    FormRadio,
    FormTextInput,
    FormCheckbox,
    FormTextField,
    InsuranceFeeNote,
    FormTextInputMoney
} from './components'
import { COLORS } from '@styles'
import { translate } from '@translate'
import moment from 'moment'

//  1 => Phạm vi bảo hiểm
//  2 => Thông tin bố mẹ
//  3 => Thông tin tình trạng sức khỏe
//  4 => <PERSON>i<PERSON><PERSON> khoản chính
//  5 => <PERSON>i<PERSON><PERSON> kho<PERSON><PERSON> bổ sung

const InsuranceInformation = ({
    insuranceInfo,
    onBack,
    onNextStep,
    //redux
    itemCatalog,
    updateHeaderAirtime,
    healthInsuranceFee,
    stateHealthInsuranceFee,
    localSideState,
    actionInsuranceAirtimeService
}) => {

    const [insuranceInfoState, setInsuranceInfoState] = useState(insuranceInfo?.map((formGroup, groupIndex) => ({
        ...formGroup,
        groupIndex,
        title: formGroup.GroupName,
        data: formGroup.PropertyList
    })));

    const [isCarModelApiCalled, setIsCarModelApiCalled] = useState(false);
    const completedApiCalls = localSideState.completedApiCalls || new Set();

    // Mapping PropertyID và PropertyID tiếp theo trong chuỗi
    const apiChainMapping = {
        26: { targetPropertyId: 134, nextPropertyId: 134 },
        134: { targetPropertyId: 27, nextPropertyId: 27 },
        27: { targetPropertyId: 135, nextPropertyId: 135 },
        135: { targetPropertyId: 133, nextPropertyId: 133 },
        133: { targetPropertyId: 43, nextPropertyId: 43 },
        43: { targetPropertyId: 43, nextPropertyId: null }
    };

    const apiChainMappingMic = {
        26: { targetPropertyId: 27, nextPropertyId: 27 },
        27: { targetPropertyId: 43, nextPropertyId: 43 },
        43: { targetPropertyId: 43, nextPropertyId: null }
    };

    const {
        isInvalidHealthStatus,
        isSelectedYesOnSecondHealthStatusSubQuestion,
        isSelectedNoOnSecondHealthStatusQuestion,
        extraPolicyFee,
        isResign,
        isBoughtForChildren
    } = localSideState;

    const setIsInvalidHealthStatus = (value) => {
        actionInsuranceAirtimeService.updateLocalSideState({
            ...localSideState,
            isInvalidHealthStatus: value
        })
    }

    const setIsSelectedYesOnSecondHealthStatusSubQuestion = (value) => {
        actionInsuranceAirtimeService.updateLocalSideState({
            ...localSideState,
            isSelectedYesOnSecondHealthStatusSubQuestion: value
        })
    }

    const setIsSelectedNoOnSecondHealthStatusQuestion = (value) => {
        actionInsuranceAirtimeService.updateLocalSideState({
            ...localSideState,
            isSelectedNoOnSecondHealthStatusQuestion: value
        })
    }

    const setExtraPolicyFee = (value) => {
        actionInsuranceAirtimeService.updateLocalSideState({
            ...localSideState,
            extraPolicyFee: value
        })
    }

    const setIsResign = (value) => {
        actionInsuranceAirtimeService.updateLocalSideState({
            ...localSideState,
            isResign: value
        })
    }

    const setIsBoughtForChildren = (value) => {
        actionInsuranceAirtimeService.updateLocalSideState({
            ...localSideState,
            isBoughtForChildren: value
        })
    }

    const updateCompletedApiCalls = (propertyId, action = 'add') => {
        const currentSet = new Set(localSideState.completedApiCalls || []);
        if (action === 'add') {
            currentSet.add(propertyId);
        } else if (action === 'remove') {
            currentSet.delete(propertyId);
        } else if (action === 'clear') {
            propertyId.forEach(id => currentSet.delete(id));
        }

        actionInsuranceAirtimeService.updateLocalSideState({
            ...localSideState,
            completedApiCalls: Array.from(currentSet)
        });
    }

    const isGotFee = !helper.IsEmptyObject(healthInsuranceFee);

    useEffect(() => {
        if (isInvalidHealthStatus || isSelectedYesOnSecondHealthStatusSubQuestion) {
            Alert.alert("", "Tình trạng sức khoẻ không đủ điều kiện tham gia bảo hiểm sức khoẻ MIC!", [
                {
                    text: "OK",
                    onPress: () => { }
                },
            ]);
        }
    }, [isInvalidHealthStatus, isSelectedYesOnSecondHealthStatusSubQuestion])

    // Khôi phục trạng thái completedApiCalls khi component mount
    useEffect(() => {
        console.log("Component mounted, completedApiCalls:", completedApiCalls);
    }, []);

    useEffect(() => {
        const healthStatusFormGroup = insuranceInfo.find(formGroup => formGroup.InsInforGroupID == 3);
        if (!helper.IsEmptyObject(healthStatusFormGroup)) {
            const { PropertyList } = healthStatusFormGroup;
            const healthStatusForm = PropertyList.find(form => form.PropertyID == 76)
            if (!helper.IsEmptyObject(healthStatusForm)) {
                const { Value, Data } = healthStatusForm;
                if (helper.IsNonEmptyArray(Data) && Value != null) {
                    try {
                        const selectedPackage = Data.find(item => item && item.PropertyValueID == Value)
                        if (!helper.IsEmptyObject(selectedPackage) && selectedPackage.PartnerValue) {
                            if (selectedPackage.PartnerValue == 'K') {
                                setIsSelectedNoOnSecondHealthStatusQuestion(true);
                            }
                        }
                    } catch (error) {
                        console.error('Lỗi lấy thông tin dữ liệu PropertyID theo chuỗi: 26 → 134 → 27 → 135 → 133 → 43');
                    }
                }
            }
        }
    }, [])


    useEffect(() => {
        if (!helper.IsEmptyObject(healthInsuranceFee.PriceInfo)) {
            const { PriceInfo: { ExtraData, ListPriceInfoDetailBO }, TotalAmount } = healthInsuranceFee;
            let newInsuranceInfoState = [...insuranceInfoState];
            const mainFeeGroupIndex = newInsuranceInfoState.findIndex(group => group.InsInforGroupID == 4);
            if (mainFeeGroupIndex > -1) {
                const mainFeePropertyIndex = newInsuranceInfoState[mainFeeGroupIndex].PropertyList.findIndex(form => form.PropertyID == 73)
                if (mainFeePropertyIndex > -1) {
                    newInsuranceInfoState[mainFeeGroupIndex].PropertyList[mainFeePropertyIndex].Value = helper.IsNonEmptyArray(ListPriceInfoDetailBO) ? ListPriceInfoDetailBO[0]?.ExtraData?.InsAmount : null;
                }
                const totalFeePropertyIndex = newInsuranceInfoState[mainFeeGroupIndex].PropertyList.findIndex(form => form.PropertyID == 74)
                if (totalFeePropertyIndex > -1) {
                    newInsuranceInfoState[mainFeeGroupIndex].PropertyList[totalFeePropertyIndex].Value = TotalAmount
                }
            }
            const feeGroupIndex = newInsuranceInfoState.findIndex(group => group.InsInforGroupID == 51 || group.InsInforGroupID == 58);
            if (feeGroupIndex > -1) {
                newInsuranceInfoState[feeGroupIndex].PropertyList = ExtraData;
                newInsuranceInfoState[feeGroupIndex].data = ExtraData;
            }
            setInsuranceInfoState(newInsuranceInfoState);
        }
    }, [healthInsuranceFee])

    const onCheckRadio = (groupIndex, formIndex) => (value, propertyId, partnerValue) => {
        if (isGotFee) {
            actionInsuranceAirtimeService.updateHealthInsuranceFee({});
        }
        let newInsuranceInfoState = [...insuranceInfoState];
        newInsuranceInfoState[groupIndex].PropertyList[formIndex].Value = value;
        newInsuranceInfoState[groupIndex].data[formIndex].Value = value;

        // xử lý nghiệp vụ case tái ký
        if (propertyId == 64) {
            const oldContractFormIndex = newInsuranceInfoState[groupIndex].PropertyList.findIndex(form => form.PropertyID == 65)
            if (oldContractFormIndex > -1) {
                newInsuranceInfoState[groupIndex].PropertyList[oldContractFormIndex].IsObliged = partnerValue == 'T' ? 1 : 0;
                newInsuranceInfoState[groupIndex].PropertyList[oldContractFormIndex].Value = '';
                newInsuranceInfoState[groupIndex].data[oldContractFormIndex].IsObliged = partnerValue == 'T' ? 1 : 0;
                newInsuranceInfoState[groupIndex].data[oldContractFormIndex].Value = '';
            }
            setIsResign(partnerValue == 'T');
        }


        if (newInsuranceInfoState[groupIndex].PropertyList[formIndex].PropertyID == 75) {
            // validate nếu chọn "Có" ở câu hỏi đầu tiên của tình trạng sức khoẻ thì sẽ không được phép tham gia bảo hiểm sức khởe
            setIsInvalidHealthStatus(partnerValue == 'C');
        } else if (newInsuranceInfoState[groupIndex].PropertyList[formIndex].PropertyID == 76) {
            // logic nếu chọn "Không" ở câu hỏi thứ 2 của tình trạng sức khoẻ thì default mấy câu hỏi con là không
            setIsSelectedNoOnSecondHealthStatusQuestion(partnerValue == 'K');
            if (partnerValue == 'K') {
                // reset danh sách bệnh khi chọn không ở câu hỏi 2
                const selectDiseaseFormIndex = newInsuranceInfoState[groupIndex].PropertyList.findIndex(form => form.PropertyID == 80)
                if (selectDiseaseFormIndex > -1) {
                    newInsuranceInfoState[groupIndex].PropertyList[selectDiseaseFormIndex].ChildProperty = newInsuranceInfoState[groupIndex].PropertyList[selectDiseaseFormIndex].ChildProperty.map(childProperty => ({ ...childProperty, Value: null }))
                    newInsuranceInfoState[groupIndex].data[selectDiseaseFormIndex].ChildProperty = newInsuranceInfoState[groupIndex].data[selectDiseaseFormIndex].ChildProperty.map(childProperty => ({ ...childProperty, Value: null }))
                }
            }
        } else if (
            newInsuranceInfoState[groupIndex].PropertyList[formIndex].PropertyID == 77 ||
            newInsuranceInfoState[groupIndex].PropertyList[formIndex].PropertyID == 78 ||
            newInsuranceInfoState[groupIndex].PropertyList[formIndex].PropertyID == 79
        ) {
            // logic nếu chọn "Có" ở câu hỏi 2.1, 2.2, 2.3 của tình trạng sức khoẻ thì disable
            setIsSelectedYesOnSecondHealthStatusSubQuestion(partnerValue == 'C');
        } else if (newInsuranceInfoState[groupIndex].PropertyList[formIndex].PropertyID == 80 && partnerValue == 'K') {
            // reset danh sách bệnh khi chọn không ở câu hỏi 2.4
            newInsuranceInfoState[groupIndex].PropertyList[formIndex].ChildProperty = newInsuranceInfoState[groupIndex].PropertyList[formIndex].ChildProperty.map(childProperty => ({ ...childProperty, Value: null }))
            newInsuranceInfoState[groupIndex].data[formIndex].ChildProperty = newInsuranceInfoState[groupIndex].data[formIndex].ChildProperty.map(childProperty => ({ ...childProperty, Value: null }))
        }
        setInsuranceInfoState(newInsuranceInfoState);
    }

    const onCheckDisease = (groupIndex, formIndex) => (value, propertyIndex) => {
        if (isGotFee) {
            actionInsuranceAirtimeService.updateHealthInsuranceFee({});
        }
        let newInsuranceInfoState = [...insuranceInfoState];
        if (newInsuranceInfoState[groupIndex].PropertyList[formIndex].ChildProperty[propertyIndex].Value == value) {
            newInsuranceInfoState[groupIndex].PropertyList[formIndex].ChildProperty[propertyIndex].Value = null
            newInsuranceInfoState[groupIndex].data[formIndex].ChildProperty[propertyIndex].Value = null
        } else {
            newInsuranceInfoState[groupIndex].PropertyList[formIndex].ChildProperty[propertyIndex].Value = value
            newInsuranceInfoState[groupIndex].data[formIndex].ChildProperty[propertyIndex].Value = value
        }
        setInsuranceInfoState(newInsuranceInfoState);
    }

    const onSubmitText = (groupIndex, formIndex) => (text) => {
        if (isGotFee) {
            actionInsuranceAirtimeService.updateHealthInsuranceFee({});
        }
        let newInsuranceInfoState = [...insuranceInfoState];
        const propertyId = newInsuranceInfoState[groupIndex].PropertyList[formIndex].PropertyID;
        newInsuranceInfoState[groupIndex].PropertyList[formIndex].Value = text;
        newInsuranceInfoState[groupIndex].data[formIndex].Value = text;
        if (propertyId === 43) {
            for (let gIndex = 0; gIndex < newInsuranceInfoState.length; gIndex++) {
                const idx130 = newInsuranceInfoState[gIndex].PropertyList.findIndex(f => f.PropertyID === 130);
                if (idx130 > -1) {
                    newInsuranceInfoState[gIndex].PropertyList[idx130].Value = text;
                    newInsuranceInfoState[gIndex].data[idx130].Value = text;
                    break;
                }
            }
        }

        setInsuranceInfoState(newInsuranceInfoState);
    }

    const onSubmitContractNumber = (groupIndex, formIndex) => (contractNumber) => {
        if (isGotFee) {
            actionInsuranceAirtimeService.updateHealthInsuranceFee({});
        }
        let newInsuranceInfoState = [...insuranceInfoState];
        newInsuranceInfoState[groupIndex].PropertyList[formIndex].Value = contractNumber;
        newInsuranceInfoState[groupIndex].data[formIndex].Value = contractNumber;

        if (helper.IsEmptyString(contractNumber)) {
            let newInsuranceInfoState = [...insuranceInfoState];
            const parentInsuranceContractInfoIndex = newInsuranceInfoState.findIndex(group => group.InsInforGroupID == 2);
            if (parentInsuranceContractInfoIndex > -1) {
                newInsuranceInfoState[parentInsuranceContractInfoIndex].PropertyList = newInsuranceInfoState[parentInsuranceContractInfoIndex].PropertyList.map(form => ({ ...form, Value: null }));
                newInsuranceInfoState[parentInsuranceContractInfoIndex].data = newInsuranceInfoState[parentInsuranceContractInfoIndex].data.map(form => ({ ...form, Value: null }));
            }
            setInsuranceInfoState(newInsuranceInfoState);
        } else {
            setInsuranceInfoState(newInsuranceInfoState);
            getParentInfo(newInsuranceInfoState)
        }
    }

    const getParentInfo = (listProperty) => {
        showBlockUI();
        const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog;
        const { AirTimeTransactionTypeID } = updateHeaderAirtime;
        const data = {
            catalogId: ServiceCategoryID,
            serviceGroupId: AirtimeServiceGroupID,
            airtimeTransactionTypeId: AirTimeTransactionTypeID,
            productPromotion: {},
            listProperty: listProperty
        }
        actionInsuranceAirtimeService.getParentInsuranceContractInfo(data).then(({ extraData }) => {
            hideBlockUI();
            let newInsuranceInfoState = [...insuranceInfoState];
            const parentInsuranceContractInfoIndex = newInsuranceInfoState.findIndex(group => group.InsInforGroupID == 2);
            if (parentInsuranceContractInfoIndex > -1) {
                newInsuranceInfoState[parentInsuranceContractInfoIndex].PropertyList = extraData;
                newInsuranceInfoState[parentInsuranceContractInfoIndex].data = extraData;
                setInsuranceInfoState(newInsuranceInfoState);
            }
        }).catch(msgError => {
            Alert.alert("", msgError, [
                {
                    text: translate('common.btn_skip'),
                    onPress: hideBlockUI
                },
                {
                    text: translate('common.btn_notify_try_again'),
                    onPress: () => { getParentInfo(listProperty) },
                }
            ])
        });
    }

    const onSelectDate = (groupIndex, formIndex) => (date, propertyId) => {
        if (isGotFee) {
            actionInsuranceAirtimeService.updateHealthInsuranceFee({});
        }
        let newInsuranceInfoState = [...insuranceInfoState];
        newInsuranceInfoState[groupIndex].PropertyList[formIndex].Value = date;
        newInsuranceInfoState[groupIndex].data[formIndex].Value = date;
        if (propertyId == 4) {
            const expiredDateFormIndex = newInsuranceInfoState[groupIndex].PropertyList.findIndex(form => form.PropertyID == 5)
            if (expiredDateFormIndex > -1) {
                const [dd, mm, yyyy] = date.split('/')
                newInsuranceInfoState[groupIndex].data[expiredDateFormIndex].Value = `${dd}/${mm}/${+yyyy + 1}`
            }
        }
        setInsuranceInfoState(newInsuranceInfoState);
    }

    const onSelectItem = (groupIndex, formIndex, form) => (value, propertyId) => {
        console.log(form, insuranceInfoState, "6676878")
        if (isGotFee) {
            actionInsuranceAirtimeService.updateHealthInsuranceFee({});
        }
        let newInsuranceInfoState = [...insuranceInfoState];
        newInsuranceInfoState[groupIndex].PropertyList[formIndex].Value = value;
        newInsuranceInfoState[groupIndex].data[formIndex].Value = value;

        // Lấy AirTimeTransactionTypeID để quyết định sử dụng mapping nào
        const { AirTimeTransactionTypeID } = updateHeaderAirtime;

        // Chọn mapping dựa trên AirTimeTransactionTypeID
        const currentMapping = AirTimeTransactionTypeID === 1533 ? apiChainMappingMic : apiChainMapping;

        console.log(`Using ${AirTimeTransactionTypeID === 1533 ? 'apiChainMappingMic' : 'apiChainMapping'} for AirTimeTransactionTypeID: ${AirTimeTransactionTypeID}`);

        // Clear data cho các PropertyID sau trong chuỗi khi user thay đổi lựa chọn PropertyID theo chuỗi
        if (currentMapping[propertyId]) {
            const clearDataForPropertyIds = [];

            if (AirTimeTransactionTypeID === 1533) {
                // Logic cho apiChainMappingMic
                if (propertyId === 26) {
                    clearDataForPropertyIds.push(27,  43, 130);
                } else if (propertyId === 27) {
                    clearDataForPropertyIds.push(130);
                }
            } else {
                // Logic cho apiChainMapping (mặc định)
                if (propertyId === 26) {
                    clearDataForPropertyIds.push(134, 27, 135, 133, 43, 130);
                } else if (propertyId === 134) {
                    clearDataForPropertyIds.push(27, 135, 133, 43, 130);
                } else if (propertyId === 27) {
                    clearDataForPropertyIds.push(135, 133, 43, 130);
                } else if (propertyId === 135) {
                    clearDataForPropertyIds.push(133, 43, 130);
                } else if (propertyId === 133) {
                    clearDataForPropertyIds.push(43, 130);
                }
            }

            // Clear data cho các PropertyID sau
            if (clearDataForPropertyIds.length > 0) {
                for (let gIndex = 0; gIndex < newInsuranceInfoState.length; gIndex++) {
                    clearDataForPropertyIds.forEach(clearPropertyId => {
                        const clearFormIndex = newInsuranceInfoState[gIndex].PropertyList.findIndex(f => f.PropertyID === clearPropertyId);
                        if (clearFormIndex > -1) {
                            newInsuranceInfoState[gIndex].PropertyList[clearFormIndex].Data = [];
                            newInsuranceInfoState[gIndex].data[clearFormIndex].Data = [];
                            newInsuranceInfoState[gIndex].PropertyList[clearFormIndex].Value = null;
                            newInsuranceInfoState[gIndex].data[clearFormIndex].Value = null;
                            console.log(`Cleared data for PropertyID ${clearPropertyId} when selecting PropertyID ${propertyId}`);
                        }
                    });
                }

                // Remove các PropertyID sau khỏi completedApiCalls
                updateCompletedApiCalls(clearDataForPropertyIds, 'clear');

                console.log(`PropertyID ${propertyId} selected. Cleared data for PropertyIDs [${clearDataForPropertyIds.join(', ')}] using ${AirTimeTransactionTypeID === 1533 ? 'MIC' : 'default'} mapping`);
            }
        }

        // Kiểm tra nếu PropertyID thuộc chuỗi API calls với mapping tương ứng
        if (currentMapping[propertyId] && form.IsGetDataFilter == true) {
            getDataCarModel(form, propertyId, groupIndex, value);
        }

        setInsuranceInfoState(newInsuranceInfoState);
    }

    const onSelectProvince = (groupIndex, formIndex) => (provinceId, provinceName) => {
        if (isGotFee) {
            actionInsuranceAirtimeService.updateHealthInsuranceFee({});
        }
        let newInsuranceInfoState = [...insuranceInfoState];
        newInsuranceInfoState[groupIndex].PropertyList[formIndex].LocationValue = { ...newInsuranceInfoState[groupIndex].PropertyList[formIndex].LocationValue, provinceId, provinceName };
        newInsuranceInfoState[groupIndex].data[formIndex].LocationValue = { ...newInsuranceInfoState[groupIndex].PropertyList[formIndex].LocationValue, provinceId, provinceName };
        setInsuranceInfoState(newInsuranceInfoState);
    }

    const onSelectDistrict = (groupIndex, formIndex) => (districtId, districtName) => {
        if (isGotFee) {
            actionInsuranceAirtimeService.updateHealthInsuranceFee({});
        }
        let newInsuranceInfoState = [...insuranceInfoState];
        newInsuranceInfoState[groupIndex].PropertyList[formIndex].LocationValue = { ...newInsuranceInfoState[groupIndex].PropertyList[formIndex].LocationValue, districtId, districtName };
        newInsuranceInfoState[groupIndex].data[formIndex].LocationValue = { ...newInsuranceInfoState[groupIndex].PropertyList[formIndex].LocationValue, districtId, districtName };
        setInsuranceInfoState(newInsuranceInfoState);
    }

    const onSelectWard = (groupIndex, formIndex) => (wardId, wardName) => {
        if (isGotFee) {
            actionInsuranceAirtimeService.updateHealthInsuranceFee({});
        }
        let newInsuranceInfoState = [...insuranceInfoState];
        newInsuranceInfoState[groupIndex].PropertyList[formIndex].LocationValue = { ...newInsuranceInfoState[groupIndex].PropertyList[formIndex].LocationValue, wardId, wardName };
        newInsuranceInfoState[groupIndex].data[formIndex].LocationValue = { ...newInsuranceInfoState[groupIndex].PropertyList[formIndex].LocationValue, wardId, wardName };
        const { provinceName, districtName } = newInsuranceInfoState[groupIndex].PropertyList[formIndex].LocationValue
        newInsuranceInfoState[groupIndex].PropertyList[formIndex].Value = `${wardName}, ${districtName}, ${provinceName}`
        setInsuranceInfoState(newInsuranceInfoState);
    }

    const onSubmitAddress = (groupIndex, formIndex) => (address) => {
        if (isGotFee) {
            actionInsuranceAirtimeService.updateHealthInsuranceFee({});
        }
        let newInsuranceInfoState = [...insuranceInfoState];
        newInsuranceInfoState[groupIndex].PropertyList[formIndex].Value = { ...newInsuranceInfoState[groupIndex].PropertyList[formIndex].Value, address };
        newInsuranceInfoState[groupIndex].data[formIndex].Value = { ...newInsuranceInfoState[groupIndex].PropertyList[formIndex].Value, address };
        setInsuranceInfoState(newInsuranceInfoState);
    }
    const onSelectCheckbox = (groupIndex, formIndex) => (isSelected, extraFeeInfo = {}) => {

        let newInsuranceInfoState = [...insuranceInfoState];

        if (isGotFee && newInsuranceInfoState[groupIndex].InsInforGroupID != 58) {
            actionInsuranceAirtimeService.updateHealthInsuranceFee({});
        }

        const value = isSelected
            ? (extraFeeInfo?.Value !== undefined ? extraFeeInfo.Value : true)
            : false;
        if (isSelected) {
            newInsuranceInfoState[groupIndex].PropertyList[formIndex].Value = value;
            newInsuranceInfoState[groupIndex].data[formIndex].Value = value;
        } else {
            newInsuranceInfoState[groupIndex].PropertyList[formIndex].Value = null;
            newInsuranceInfoState[groupIndex].data[formIndex].Value = null;
        }
        if (newInsuranceInfoState[groupIndex].InsInforGroupID == 58 && !helper.IsEmptyObject(extraFeeInfo)) {
            checkPromotion(newInsuranceInfoState, groupIndex, value, extraFeeInfo);
        } else {
            setInsuranceInfoState(newInsuranceInfoState);
        }
    }

    const checkPromotion = (insuranceFormState, groupIndex, isSelected, extraFeeInfo) => {
        showBlockUI();
        const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog;
        const { AirTimeTransactionTypeID } = updateHeaderAirtime;
        console.log(JSON.stringify(healthInsuranceFee));
        const data = {
            catalogId: ServiceCategoryID,
            serviceGroupId: AirtimeServiceGroupID,
            airtimeTransactionTypeId: AirTimeTransactionTypeID
        }
        const feeInfo = {
            ...healthInsuranceFee,
            PriceInfo: {
                ...healthInsuranceFee.PriceInfo,
                ExtraData: insuranceFormState[groupIndex].PropertyList
            }
        }
        actionInsuranceAirtimeService.getHealthInsurancePromotion(data, feeInfo).then(() => {
            hideBlockUI();
            setInsuranceInfoState(insuranceFormState);
            if (isSelected) {
                setExtraPolicyFee([...extraPolicyFee, extraFeeInfo])
            } else {
                setExtraPolicyFee(extraPolicyFee.filter(fee => fee.BusinesTypePropertyID != extraFeeInfo.BusinesTypePropertyID))
            }
        }).catch(msgError => {
            Alert.alert("", msgError, [
                {
                    text: translate('common.btn_skip'),
                    onPress: hideBlockUI
                },
                {
                    text: translate('common.btn_notify_try_again'),
                    onPress: () => {
                        checkPromotion(insuranceFormState, groupIndex);
                    }
                }
            ])
        });
    }

    const onSelectBuyForChildren = () => {
        let isAllow = true;
        if (!isBoughtForChildren) {
            const insuranceInfoFormGroup = insuranceInfoState.findIndex(group => group.InsInforGroupID == 1)
            if (insuranceInfoFormGroup > -1) {
                const birthdayForm = insuranceInfoState[insuranceInfoFormGroup].PropertyList.find(form => form.PropertyID == 112)
                if (!helper.IsEmptyObject(birthdayForm) && moment(birthdayForm.Value, "DD/MM/YYYY").isValid()) {
                    const selectedDate = moment(birthdayForm.Value, "DD/MM/YYYY");
                    if (moment().diff(selectedDate, 'days') < 15 || moment().diff(selectedDate, 'years') > 6) {
                        isAllow = false;
                        Alert.alert(
                            translate('common.notification'),
                            'Độ tuổi của người được bảo hiểm không đủ điều kiện tham gia mua kèm bố hoặc mẹ. Vui lòng kiểm tra lại ngày sinh người được bảo hiểm!',
                            [
                                {
                                    text: translate('common.btn_accept'),
                                    onPress: () => { },
                                },
                            ]);
                    }
                }
            }
        }
        if (isAllow) {
            setIsBoughtForChildren(!isBoughtForChildren);
        }
    }

    const getInsuranceFee = () => {
        const isValidForm = validateInsuranceInfo();
        if (isValidForm) {
            const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog;
            const { AirTimeTransactionTypeID } = updateHeaderAirtime;
            // Lọc bở data, loop vào PropertyList có đối tượng Data không và Data đó trùng với Value đã chọn giữ lại data đó
            const filteredArrayInsuranceInfoState = insuranceInfoState.map(({ data, ...group }) => {
                if (group.OrderIndex !== 0) return { ...group }; // giữ nguyên nếu không phải OrderIndex 0

                return {
                    ...group,
                    PropertyList: group.PropertyList.map(prop => {
                        const { Data = [], Value, ...rest } = prop;
                        const filteredData = Data.filter(item =>
                            [item.PropertyValueID, item.BusinesTypePropertyValueID, item.Value].includes(Value)
                        );

                        return {
                            ...rest,
                            Value,
                            Data: filteredData
                        };
                    })
                };
            });

            const data = {
                catalogId: ServiceCategoryID,
                serviceGroupId: AirtimeServiceGroupID,
                airtimeTransactionTypeId: AirTimeTransactionTypeID,
                productPromotion: {},
                listProperty: filteredArrayInsuranceInfoState,
                extraPolicyFee
            }
            actionInsuranceAirtimeService.getCarInsuranceFee(data);
        }
    }

    const validateInsuranceInfo = () => {
        const listInsuranceForm = insuranceInfoState.reduce((forms, formGroup) => [...forms, ...formGroup.PropertyList], []);
        const invalidForm = listInsuranceForm.find(form => form.IsObliged == 1 && (!helper.IsNonEmptyString(form.Value) && !helper.isNumber(form.Value)))
        if (!helper.IsEmptyObject(invalidForm)) {
            const { Label } = invalidForm;
            Alert.alert("", `Thông tin không hợp lệ: \n ${Label.split('?')[0]} \n Bạn vui lòng kiểm tra lại!`)
            return false;
        } else {
            const selectDiseaseForm = listInsuranceForm.find(form => form.PropertyID == 80)
            if (!helper.IsEmptyObject(selectDiseaseForm)) {
                const selectedObject = selectDiseaseForm.Data.find(item => item.PartnerValue == 'C')
                if (!helper.IsEmptyObject(selectedObject) && selectedObject.PropertyValueID == selectDiseaseForm.Value) {
                    const isSelectedDisease = selectDiseaseForm.ChildProperty.findIndex(property => property.Value != null) > -1
                    if (!isSelectedDisease) {
                        Alert.alert("", `Bạn vui lòng chọn bệnh lý ở câu hỏi 2.4!`)
                        return false;
                    }
                }
            }
            return true;
        }
    }

    const nextStep = () => {
        actionInsuranceAirtimeService.updateHealthInsuranceFee({
            ...healthInsuranceFee,
            extraPolicyFee
        });
        onNextStep(insuranceInfoState);
    }

    const renderSectionHeader = ({ section: { title, InsInforGroupID } }) => {
        if (!isGotFee && (InsInforGroupID == 4 || InsInforGroupID == 55 || InsInforGroupID == 58)) return null;
        if (!isBoughtForChildren && InsInforGroupID == 2) return null;
        const extraPolicyFee = helper.IsNonEmptyArray(healthInsuranceFee.PriceInfo?.ListPriceInfoDetailBO) ? healthInsuranceFee.PriceInfo?.ListPriceInfoDetailBO[0]?.ExtraData?.InsAmountAddition : 0;
        const insuranceFee = helper.IsNonEmptyArray(healthInsuranceFee.PriceInfo?.ListPriceInfoDetailBO) ? healthInsuranceFee.PriceInfo?.ListPriceInfoDetailBO[0]?.ExtraData?.InsAmount : 0
        return (
            <View style={{
                flexDirection: 'row',
                justifyContent: 'space-between'
            }}>
                <MyText
                    text={title}
                    style={{
                        fontWeight: 'bold',
                        padding: 5
                    }}
                    addSize={1}
                />
                {InsInforGroupID == 4 && insuranceFee > 0 &&
                    <MyText
                        text={helper.formatMoney(insuranceFee)}
                        style={{
                            fontWeight: 'bold',
                            padding: 5,
                            color: COLORS.txtD0021B
                        }}
                        addSize={1}
                    />
                }
                {InsInforGroupID == 5 && extraPolicyFee > 0 &&
                    <MyText
                        text={helper.formatMoney(extraPolicyFee)}
                        style={{
                            fontWeight: 'bold',
                            padding: 5,
                            color: COLORS.txtD0021B
                        }}
                        addSize={1}
                    />
                }
            </View>
        );
    }

    const getDataCarModel = (form, propertyId, groupIndex, value) => {
        return new Promise((resolve, reject) => {
            const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog;
            const { AirTimeTransactionTypeID, CustomerID } = updateHeaderAirtime;
            showBlockUI();
            // Tạo extraData dựa trên AirTimeTransactionTypeID và PropertyId
            let extraData = form;

            if (AirTimeTransactionTypeID === 1533 && propertyId === 27) {
                const insuranceInfoFormGroup = insuranceInfoState.findIndex(group => group.InsInforGroupID == 82)
                const Model = insuranceInfoState[insuranceInfoFormGroup].PropertyList.find(form => form.PropertyID == 27)
                const Manufacturer = insuranceInfoState[insuranceInfoFormGroup].PropertyList.find(form => form.PropertyID == 26)
                const Year = insuranceInfoState[insuranceInfoFormGroup].PropertyList.find(form => form.PropertyID == 21)
                // Thêm các trường Manufacturer, Model, Year cho MIC khi PropertyId = 27
                extraData = {
                    Manufacturer,
                    Model,
                    Year
                };
                console.log('Added Manufacturer, Model, Year to extraData for MIC (AirTimeTransactionTypeID: 1533 and PropertyId: 27)');
            }

            const data = {
                catalogId: ServiceCategoryID,
                serviceGroupId: AirtimeServiceGroupID,
                airtimeTransactionTypeId: AirTimeTransactionTypeID,
                partnerId: CustomerID,
                searchType: 1,
                extraData: extraData
            }
            console.log(`Calling API getDataCarModel for PropertyID ${propertyId}`);

            actionInsuranceAirtimeService.getDataCarModel(data)
                .then((responseStatus) => {
                    hideBlockUI();

                    // Chọn mapping dựa trên AirTimeTransactionTypeID
                    const currentMapping = AirTimeTransactionTypeID === 1533 ? apiChainMappingMic : apiChainMapping;
                    const mapping = currentMapping[propertyId];

                    console.log(`getDataCarModel using ${AirTimeTransactionTypeID === 1533 ? 'apiChainMappingMic' : 'apiChainMapping'} for PropertyID ${propertyId}`);

                    if (mapping) {
                        const { targetPropertyId, nextPropertyId } = mapping;

                        const matchedObject = Array.isArray(responseStatus)
                            ? responseStatus.find(obj => obj?.PropertyID === targetPropertyId)
                            : null;

                        if (matchedObject) {
                            let newInsuranceInfoState = [...insuranceInfoState];
                            for (let gIndex = 0; gIndex < newInsuranceInfoState.length; gIndex++) {
                                const targetFormIndex = newInsuranceInfoState[gIndex].PropertyList.findIndex(f => f.PropertyID === targetPropertyId);
                                if (targetFormIndex > -1) {
                                    const dataDongXe = matchedObject?.Data || [];

                                    newInsuranceInfoState[gIndex].PropertyList[targetFormIndex].Data = dataDongXe;
                                    newInsuranceInfoState[gIndex].data[targetFormIndex].Data = dataDongXe;
                                    if (targetPropertyId === 43 && helper.IsNonEmptyArray(dataDongXe)) {
                                        const autoFillProperty = (item, propertyId, itemIndex, formatRequire) => {
                                            if (!item) return;
                                            const textValue = item.Data || item.Value || item.Text || item.Label;
                                            if (!textValue) return;

                                            for (let gIdx = 0; gIdx < newInsuranceInfoState.length; gIdx++) {
                                                const propIndex = newInsuranceInfoState[gIdx].PropertyList.findIndex(f => f.PropertyID === propertyId);
                                                if (propIndex > -1) {
                                                    newInsuranceInfoState[gIdx].PropertyList[propIndex].Value = textValue;
                                                    newInsuranceInfoState[gIdx].data[propIndex].Value = textValue;

                                                    if (formatRequire) {
                                                        newInsuranceInfoState[gIdx].PropertyList[propIndex].FormatRequire = formatRequire;
                                                        newInsuranceInfoState[gIdx].data[propIndex].FormatRequire = formatRequire;
                                                    }
                                                    if (propertyId === 130) {
                                                        newInsuranceInfoState[gIdx].PropertyList[propIndex].Data = dataDongXe;
                                                        newInsuranceInfoState[gIdx].data[propIndex].Data = dataDongXe;
                                                    }

                                                    console.log(`Auto-filled PropertyID ${propertyId} with item[${itemIndex}]:`, textValue);
                                                    break;
                                                }
                                            }
                                        };

                                        const autoFillTargets = [43, 130];
                                        autoFillTargets.forEach((propertyId, index) => {
                                            const itemToUse = dataDongXe[index] || dataDongXe[0];
                                            if (itemToUse) {
                                                const formatRequire = matchedObject?.FormatRequire || null;
                                                autoFillProperty(itemToUse, propertyId, index, formatRequire);
                                            }
                                        });
                                        const formatRequireFor43 = responseStatus.find(obj => obj?.PropertyID === 43)?.FormatRequire || null;
                                        const formatRequireFor130 = responseStatus.find(obj => obj?.PropertyID === 130)?.FormatRequire || null;

                                        console.log('FormatRequire for PropertyID 43:', formatRequireFor43);
                                        console.log('FormatRequire for PropertyID 130:', formatRequireFor130);

                                        if (dataDongXe[0]) {
                                            autoFillProperty(dataDongXe[0], 43, 0, formatRequireFor43);
                                        }

                                        if (dataDongXe[1] || dataDongXe[0]) {
                                            const itemFor130 = dataDongXe[1] || dataDongXe[0];
                                            autoFillProperty(itemFor130, 130, 1, formatRequireFor130);
                                        }

                                        setInsuranceInfoState([...newInsuranceInfoState]);
                                    }

                                    console.log(`Updated PropertyID ${targetPropertyId} with data:`, dataDongXe);
                                    break;
                                }
                            }
                            updateCompletedApiCalls(propertyId, 'add');
                            setInsuranceInfoState(newInsuranceInfoState);
                        } else {
                            updateCompletedApiCalls(propertyId, 'add');
                        }
                    }

                    setIsCarModelApiCalled(true);
                    resolve(responseStatus);
                })
                .catch((msgError) => {
                    hideBlockUI();
                    updateCarModelData([]);
                    Alert.alert("", msgError, [
                        {
                            text: "OK",
                            onPress: () => { },
                        },
                    ]);
                    reject(msgError);
                });
        });
    };

    const updateCarModelData = (dataDongXe) => {
        let newInsuranceInfoState = [...insuranceInfoState];
        for (let gIndex = 0; gIndex < newInsuranceInfoState.length; gIndex++) {
            const form134Index = newInsuranceInfoState[gIndex].PropertyList.findIndex(f => f.PropertyID === 27);
            if (form134Index > -1) {
                newInsuranceInfoState[gIndex].PropertyList[form134Index].Data = dataDongXe;
                newInsuranceInfoState[gIndex].data[form134Index].Data = dataDongXe;
                break;
            }
        }
        setInsuranceInfoState(newInsuranceInfoState);
    };

    const renderSectionFooter = ({ section: { InsInforGroupID } }) => {
        const isDisable = isInvalidHealthStatus || isSelectedYesOnSecondHealthStatusSubQuestion || stateHealthInsuranceFee.isFetching;
        if (InsInforGroupID == 55) {
            return (
                <>
                    <TouchableOpacity
                        style={{
                            alignSelf: 'flex-end',
                            marginRight: 10,
                            backgroundColor: COLORS.btnF49B0C,
                            alignItems: 'center',
                            justifyContent: 'center',
                            paddingVertical: 8,
                            borderRadius: 5,
                            paddingHorizontal: 15,
                            opacity: isDisable ? 0.6 : 1
                        }}
                        activeOpacity={0.8}
                        onPress={getInsuranceFee}
                        disabled={isDisable}
                    >
                        <MyText
                            text={'Kiểm tra phí'}
                            style={{ color: COLORS.txtFFFFFF }}
                        />
                    </TouchableOpacity>

                    <InsuranceFee
                        getInsuranceFee={getInsuranceFee}
                        healthInsuranceFee={healthInsuranceFee}
                        stateHealthInsuranceFee={stateHealthInsuranceFee}
                    />
                </>
            );
        }
    };

    const propertyDependencyMapOpes = {
        134: 26,
        27: 134,
        135: 27,
        133: 135,
        43: 133,
        130: 133,
    };

    const renderForm = ({ item: form, index, section }) => {
        const { groupIndex, InsInforGroupID } = section
        const { DataType, PropertyID, Value } = form;
        const { AirTimeTransactionTypeID } = updateHeaderAirtime;

        // Chuyển đổi completedApiCalls từ array sang Set để sử dụng has()
        const completedApiCallsSet = new Set(completedApiCalls);

        // Kiểm tra dependency nếu có
        const dependency = propertyDependencyMapOpes[PropertyID];
        if (dependency && !completedApiCallsSet.has(dependency) && AirTimeTransactionTypeID == 2153) {
            console.log(`Hiding PropertyID ${PropertyID} - PropertyID ${dependency} API not completed yet`);
            return null;
        }
        if (!isGotFee && InsInforGroupID == 51) return null;
        if (!isGotFee && InsInforGroupID == 58) return null;
        if (!isResign && PropertyID == 65) return null;
        if (!isBoughtForChildren && InsInforGroupID == 2) {
            return null
        } else {
            if (DataType == 7 && Value == null)
                return null
        }
        switch (DataType) {
            case 0:
                return <FormContractNumber
                    key={`${index}`}
                    textInputData={form}
                    onSubmit={onSubmitContractNumber(groupIndex, index)}
                />
            case 1:
                return <FormTextInput
                    key={`${index}`}
                    textInputData={form}
                    onSubmit={onSubmitText(groupIndex, index)}
                    isInvalidHealthStatus={isInvalidHealthStatus || isSelectedYesOnSecondHealthStatusSubQuestion}
                />
            case 2:
                return <FormDatePicker
                    key={`${index}`}
                    datePickerData={form}
                    onSelectDate={onSelectDate(groupIndex, index)}
                    isDisabled={PropertyID == 5}
                    isBoughtForChildren={isBoughtForChildren}
                />
            case 3:
                return <FormRadio
                    key={`${index}`}
                    radioData={form}
                    onCheck={onCheckRadio(groupIndex, index)}
                    onCheckDisease={onCheckDisease(groupIndex, index)}
                    isInvalidHealthStatus={isInvalidHealthStatus}
                    isSelectedNoOnSecondHealthStatusQuestion={isSelectedNoOnSecondHealthStatusQuestion}
                    isSelectedYesOnSecondHealthStatusSubQuestion={isSelectedYesOnSecondHealthStatusSubQuestion}
                />
            case 4:
                return <FormPicker
                    key={`${index}`}
                    pickerData={form}
                    onSelect={onSelectItem(groupIndex, index, form)}
                />
            case 5:
                return <FormCheckbox
                    key={`${index}`}
                    checkboxData={form}
                    onSelectCheckbox={onSelectCheckbox(groupIndex, index)}
                    isShowMoney={InsInforGroupID == 5 || InsInforGroupID == 58}
                />
            case 6:
                return <FormLocationPicker
                    key={`${index}`}
                    locationPickerData={form}
                    onSelectProvince={onSelectProvince(groupIndex, index)}
                    onSelectDistrict={onSelectDistrict(groupIndex, index)}
                    onSelectWard={onSelectWard(groupIndex, index)}
                    onSubmitAddress={onSubmitAddress(groupIndex, index)}
                />
            case 7:
                return <FormTextField
                    key={`${index}`}
                    textFieldData={form}
                    isShowMoney={InsInforGroupID == 51}
                />
            case 8:
                return <FormTextInputMoney
                    key={`${index}`}
                    textInputData={form}
                    onSubmit={onSubmitText(groupIndex, index)}
                    isInvalidHealthStatus={isInvalidHealthStatus || isSelectedYesOnSecondHealthStatusSubQuestion}
                />
            default:
                return <></>
        }
    }

    return (
        <KeyboardAwareScrollView
            style={{
                flex: 1,
            }}
            enableResetScrollToCoords={true}
            keyboardShouldPersistTaps="always"
            bounces={false}
            overScrollMode="always"
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            extraScrollHeight={150}
        >
            <SectionList
                sections={insuranceInfoState}
                keyExtractor={(item, index) => `${index}`}
                renderItem={renderForm}
                renderSectionHeader={renderSectionHeader}
                renderSectionFooter={renderSectionFooter}
            />
            {isGotFee && !stateHealthInsuranceFee.isEmpty && <View style={{
                marginTop: 5
            }}>
                <ButtonDirectionStep
                    onBack={onBack}
                    onNextStep={nextStep}
                />
            </View>}
        </KeyboardAwareScrollView>
    )
}

const mapStateToProps = function (state) {
    return {
        itemCatalog: state.collectionReducer.itemCatalog,
        updateHeaderAirtime: state.insuranceAirtimeServiceReducer.updateHeaderAirtime,
        healthInsuranceFee: state.insuranceAirtimeServiceReducer.healthInsuranceFee,
        stateHealthInsuranceFee: state.insuranceAirtimeServiceReducer.stateHealthInsuranceFee,
        localSideState: state.insuranceAirtimeServiceReducer.localSideState
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionInsuranceAirtimeService: bindActionCreators(actionInsuranceAirtimeServiceCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(InsuranceInformation);
