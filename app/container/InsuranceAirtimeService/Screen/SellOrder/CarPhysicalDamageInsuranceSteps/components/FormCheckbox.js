import React from 'react'
import { Icon, MyText } from '@components'
import { TouchableOpacity, View } from 'react-native'
import { constants } from '@constants'
import { COLORS } from '@styles'
import { helper } from '@common'

export default function FormCheckbox({
    checkboxData,
    onSelectCheckbox,
    isShowMoney
}) {

    const { Label, Value, Data } = checkboxData;
    const isCheck = Value !== null && Value !== undefined;

    return (
        <>
            <View style={{
                width: constants.width,
                paddingVertical: 5,
                paddingHorizontal: 10
            }}>
                <View style={{
                    width: constants.width - 20,
                    flexDirection: 'row',
                    alignItems: 'center'
                }}>
                    <TouchableOpacity
                        style={{
                            flex: 1,
                            paddingVertical: 5,
                            paddingHorizontal: 10,
                            flexDirection: 'row'
                        }}
                        activeOpacity={0.6}
                        onPress={() => {
                            onSelectCheckbox(!isCheck, { ...Data[0], Label })
                        }}
                    >
                        <Icon
                            iconSet="Ionicons"
                            name={isCheck ? 'checkbox' : 'square-outline'}
                            color={isCheck ? COLORS.ic00C300 : COLORS.ic333333}
                            size={18}
                        />
                        <MyText
                            text={Label}
                            style={{
                                flex: 1,
                                paddingLeft: 3,
                                color: COLORS.txt333333
                            }}
                        />
                    </TouchableOpacity>
                    {
                        isShowMoney && <MyText
                            text={helper.formatMoney(Data[0].Value)}
                            style={{
                                paddingLeft: 10,
                                color: COLORS.txtD0021B
                            }}
                        />
                    }
                </View>
            </View>
        </>
    )
}