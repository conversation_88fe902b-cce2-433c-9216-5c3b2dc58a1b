import { helper } from '@common'
import { Icon, MyText } from '@components'
import { constants } from '@constants'
import { COLORS } from '@styles'
import { translate } from '@translate'
import React, { useEffect, useState } from 'react'
import { Alert, StyleSheet, TouchableOpacity, View } from 'react-native'
import TooltipWrapper from './TooltipWrapper';
import TitleInputMoney from './TitleInputMoney';

export default function FormTextInputMoney({ textInputData, onSubmit }) {
    const {
        MaxAmount,
        MaxPercent,
        MinAmount,
        MinPercent,
        Note
    } = textInputData?.FormatRequire ?? {}

    const [textValue, setTextValue] = useState('');
    const [IsShowMinMaxPercen, setIsShowMinMaxPercen] = useState(false);
    const [calculatedPercent, setCalculatedPercent] = useState(0);

    const { Label, Value, IsObliged, PropertyID } = textInputData;
    const isRequired = IsObliged == 1;

    // LOG  37 Số điện thoại người mua
    // LOG  16 Email
    // LOG  115 Email người được bảo hiểm
    // LOG  10 Số điện thoại

    const isMultipleLine = PropertyID == 106
    const isPhoneNumber = PropertyID == 37 || PropertyID == 10 || PropertyID == 13

    useEffect(() => {
        if (helper.IsNonEmptyString(Value)) {
            setTextValue(Value)
        } else {
            setTextValue('')
        }
    }, [Value])

    // Tính phần trăm dựa trên số tiền nhập
    const calculatePercent = (amount) => {
        if (!amount || !textInputData?.FormatRequire) {
            setCalculatedPercent(0);
            return 0;
        }

        const numAmount = parseFloat(amount);
        if (isNaN(numAmount) || numAmount <= 0) {
            setCalculatedPercent(0);
            return 0;
        }

        const inputAmount = numAmount;
        const minAmount = parseFloat(MinAmount) || 0;
        const maxAmount = parseFloat(MaxAmount) || 100;
        const minPercent = parseFloat(MinPercent) || 0;
        const maxPercent = parseFloat(MaxPercent) || 100;

        let percent = 0;

        if (inputAmount < minAmount) {
            percent = (inputAmount / minAmount) * minPercent;
        } else if (inputAmount >= maxAmount) {
            const basePercent = maxPercent;
            const additionalAmount = inputAmount - maxAmount;
            const additionalPercent = (additionalAmount / maxAmount) * (maxPercent - minPercent);
            percent = basePercent + additionalPercent;
        } else {
            percent =
                minPercent +
                ((inputAmount - minAmount) / (maxAmount - minAmount)) *
                (maxPercent - minPercent);
        }

        const roundedPercent = Math.round(percent * 100) / 100;
        setCalculatedPercent(roundedPercent);
        return roundedPercent;
    };

    const checkAmountValidation = (amount) => {
        if (!amount || !textInputData?.FormatRequire) {
            setIsShowMinMaxPercen(false);
            return true;
        }

        const numAmount = parseFloat(amount);
        if (isNaN(numAmount)) {
            setIsShowMinMaxPercen(true);
            console.log("Setting IsShowMinMaxPercen to true - invalid number");
            return false;
        }

        let isValid = true;
        if (MinAmount !== undefined && numAmount < MinAmount) {
            isValid = false;
        }
        if (MaxAmount !== undefined && numAmount > MaxAmount) {
            isValid = false;
        }
        const percent = calculatePercent(amount);

        if (MinPercent !== undefined && percent < MinPercent) {
            isValid = false;
        }
        if (MaxPercent !== undefined && percent > MaxPercent) {
            isValid = false;
        }

        console.log("checkAmountValidation result:", isValid, "for amount:", amount);
        setIsShowMinMaxPercen(!isValid);
        return isValid;
    }

    const submitText = ({ nativeEvent: { text } }) => {
        console.log("submitText called, IsShowMinMaxPercen:", IsShowMinMaxPercen);
        let isValid = true;
        if (PropertyID == 111) {
            const idCardNumberRegex = /^(\d{9}|\d{12})$/;
            isValid = idCardNumberRegex.test(text)
        } else if (PropertyID == 108) {
            const idCardNumberRegex = /^[\d|-]{9,14}$/;
            isValid = idCardNumberRegex.test(text)
        } else if (PropertyID == 16 || PropertyID == 115) {
            const emailRegex = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/;
            isValid = text.length <= 1 || emailRegex.test(text)
        } else if (PropertyID == 37 || PropertyID == 10) {
            const phoneRegex = /^[0][\d]{9,10}$/;
            isValid = phoneRegex.test(text)
        } else {
            isValid = checkAmountValidation(text);
        }

        if (IsShowMinMaxPercen === true) {
            console.log("Showing alert because IsShowMinMaxPercen is true");
            setTimeout(() => {
                Alert.alert(
                    translate('common.notification'),
                    `${Label} không hợp lệ!`,
                    [
                        {
                            text: translate('common.btn_accept'),
                            onPress: () => { setTextValue('') },
                        },
                    ]
                );
            }, 100);
        } else {
            if (helper.IsNonEmptyString(text)) {
                onSubmit(text)
            }
        }
    }

    const onChangeText = (text) => {
        let isValid = true;
        if (PropertyID == 111) {
            const idCardNumberRegex = /^\d{0,12}$/;
            isValid = idCardNumberRegex.test(text)
        } else if (PropertyID == 108) {
            const idCardNumberRegex = /^[\d|-]{0,14}$/;
            isValid = idCardNumberRegex.test(text)
        } else if (PropertyID == 38 || PropertyID == 9) {
            const nameRegex = /^([\u0020-\u007E \u00C0-\u00CD\u00D2-\u00DD\u00E0-\u00ED\u00F2-\u00FD\u0102-\u0103\u0110-\u0111\u0128-\u0129\u0168-\u0169\u01A0-\u01A1\u01AF-\u01B0\u1EA0-\u1EF9 \u1780-\u17DD\u17E0-\u17E9\u17F0-\u17F9])*$/u;
            isValid = nameRegex.test(text)
        } else if (PropertyID == 37 || PropertyID == 10) {
            const phoneRegex = /^[0]\d{0,9}$/;
            isValid = phoneRegex.test(text)
        } else {
            // Kiểm tra FormatRequire trong onChangeText để hiển thị ngay lập tức
            checkAmountValidation(text);
        }

        if (isValid) {
            setTextValue(text)
        }
    }

    const clearText = () => {
        setTextValue('');
        setCalculatedPercent(0);
        setIsShowMinMaxPercen(false);
        onSubmit('');
    }

    return (
        <View style={{
            width: constants.width,
            paddingVertical: 5,
            paddingHorizontal: 10
        }}>

            {PropertyID == 111 ?
                <View
                    style={{
                        flexDirection: "row",
                        alignItems: "center",
                        justifyContent: "flex-start"
                    }}
                >
                    <TooltipWrapper
                        placement={"bottom"}
                        content={
                            <MyText style={{
                                color: COLORS.txtFFFFFF,
                            }}
                                text={'Trường hợp trẻ em vui lòng nhập số định danh cá nhân'}
                            />
                        }
                        wrapper={
                            <View style={{ flexDirection: "row" }}>
                                <MyText
                                    text={Label}
                                    style={{
                                        color: COLORS.txt999999,
                                        fontWeight: 'bold'
                                    }}
                                    addSize={-1}
                                >
                                    {isRequired && <MyText
                                        text={' *'}
                                        style={{
                                            fontWeight: 'bold',
                                            color: COLORS.icD0021B
                                        }}
                                        addSize={1.5}
                                    />}
                                </MyText>
                                <Icon
                                    iconSet={"Ionicons"}
                                    name={"information-circle"}
                                    size={14}
                                    color={COLORS.ic2C8BD7}
                                />
                            </View>
                        }
                    />
                </View> :
                <View style={{ flexDirection: "row", alignItems: "center", justifyContent: "flex-start" }}>
                    <MyText
                        text={Label}
                        style={{
                            color: COLORS.txt999999,
                            fontWeight: 'bold'
                        }}
                        addSize={-1}
                    >
                        {isRequired && <MyText
                            text={' *'}
                            style={{
                                fontWeight: 'bold',
                                color: COLORS.txtFF0000
                            }}
                            addSize={1.5}
                        />}
                    </MyText>
                    {helper.IsNonEmptyString(textValue) || isFinite(calculatedPercent) && calculatedPercent > 0 && (
                        <View style={{ paddingHorizontal: 10, marginTop: 5 }}>
                            <MyText
                                text={`${calculatedPercent}%`}
                                style={{
                                    color: COLORS.txtFF0000,
                                    fontSize: 12,
                                    fontStyle: 'italic',
                                    textAlign: 'center'
                                }}
                            />
                        </View>
                    )}
                </View>
            }
            <View
                style={{
                    width: constants.width - 40,
                    borderWidth: StyleSheet.hairlineWidth,
                    paddingHorizontal: 10,
                    paddingVertical: 6,
                    marginVertical: 5,
                    marginHorizontal: 10,
                    borderColor: COLORS.bd808080,
                    borderRadius: 5,
                    flexDirection: 'row',
                    alignItems: isMultipleLine ? 'flex-start' : 'center'
                }}
            >
                <TitleInputMoney
                    style={{
                        flex: 1,
                        paddingVertical: 2,
                        height: isMultipleLine ? 45 : 'auto'
                    }}
                    multiline={true}
                    keyboardType={isPhoneNumber ? "numeric" : "default"}
                    returnKeyType={isMultipleLine ? "default" : "done"}
                    value={textValue}
                    onChange={onChangeText}
                    onSubmit={submitText}
                    onEndEditing={submitText}
                    blurOnSubmit={!isMultipleLine}
                />

            </View>
            <MyText
                text={Note}
                style={{ color: COLORS.txtFF0000, paddingVertical: 5, paddingTop: 0, fontSize: 12, alignSelf: 'flex-end', fontStyle: 'italic' }}
            />

            {IsShowMinMaxPercen ? (
                <MyText
                    text={`${translate('instalmentManager.min_deposit')} ${MinPercent}% - ${translate('instalmentManager.max_deposit')} ${MaxPercent}%)`}
                    style={{ color: COLORS.txtFF0000, paddingVertical: 5, paddingTop: 0, fontSize: 12, alignSelf: 'flex-end', fontStyle: 'italic' }}
                />
            ) : null}
        </View>
    )
}