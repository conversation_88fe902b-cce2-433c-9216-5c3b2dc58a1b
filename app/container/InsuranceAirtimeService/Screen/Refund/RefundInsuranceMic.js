import {
  StyleSheet,
  Text,
  View,
  SafeAreaView,
  ScrollView,
  Alert,
  TextInput,
  Keyboard,
} from "react-native";
import React, { useState, useEffect, useCallback } from "react";
import { COLORS } from "@styles";
import { constants } from "@constants";
import {
  Button,
  Icon,
  MyText,
  PickerSearch,
  TitleInput,
  hideBlockUI,
  showBlockUI,
} from "@components";
import { Color, helper } from "@common";
import { translate } from "@translate";
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import * as actionCollectionCreator from "../../../CollectionTransfer/action";
import * as actionInsuranceAirtimeServiceCreator from "../../action";
import { useFocusEffect } from "@react-navigation/native";
import * as actionManagerSOCreator from "../../../SaleOrderManager/action";
import * as actionSaleOrderCreator from "../../../SaleOrderPayment/action";

const RefundInsuranceMic = ({
  route,
  actionCollection,
  dataInserAndCreateTicket,
  navigation,
  itemCatalog,
  actionInsuranceAirtimeService,
}) => {
  const [requestType, setRequestType] = useState("");
  const [reason, setReason] = useState("");
  const [note, setNote] = useState("");
  const [dataRefund, setDataRefund] = useState({});
  const [defaultRequestType, setDefaultRequestType] = useState({});
  const [defaultRequestReason, setDefaultRequestReason] = useState({});
  const { data } = route.params;
  const {
    SERVICEVOUCHERID,
    PRODUCTNAME,
    CUSTOMERNAME,
    SALEPRICEVAT,
    CUSTOMERPHONE,
    PHONENUMBER,
    AIRTIMETRANSACTIONID,
    PRODUCTID,
    INPUTTIME,
    AIRTIMETRANSACTIONTYPEID,
    PROCESSUSER,
    SALEORDERID,
    TICKETID,
  } = data;
  const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog ?? {};
  //data test 131491211100166
  useEffect(() => {
    getDataInfoRefund();
  }, [actionInsuranceAirtimeService]);

  useFocusEffect(
    useCallback(() => {
      if (dataInserAndCreateTicket) {
        actionCollection.cleardDataTicket();
      }
    }, [dataInserAndCreateTicket.STATUSID, actionCollection])
  );

  const getDataInfoRefund = () => {
    showBlockUI();
    const data = {
      catalogID: ServiceCategoryID,
      serviceGroupID: AirtimeServiceGroupID,
      airtimeTransactionTypeID: AIRTIMETRANSACTIONTYPEID,
      productID: PRODUCTID,
      inputTime: INPUTTIME,
    };
    actionInsuranceAirtimeService
      .getInfoRefund(data)
      .then((reponse) => {
        hideBlockUI();
        setDataRefund(reponse);
        setDefaultRequestType(reponse?.RefundRequestType?.[0]);
        setDefaultRequestReason(reponse?.RefundRequestReason?.[0]);
      })
      .catch((msgError) => {
        Alert.alert("", msgError, [
          {
            text: "OK",
            onPress: () => {
              navigation.goBack();
              hideBlockUI();
            },
          },
        ]);
      });
  };

  const handleReplyTicket = (AIRTIMETRANSACTIONID) => {
    showBlockUI();
    actionCollection
      .createTicletServiceRequest(AIRTIMETRANSACTIONID)
      .then((reponseDataTicket) => {
        hideBlockUI();
      })
      .catch((msgError) => {
        Alert.alert("", msgError, [
          {
            text: "OK",
            onPress: hideBlockUI,
          },
          {
            text: "Thử lại",
            onPress: () => handleReplyTicket(AIRTIMETRANSACTIONID),
          },
        ]);
      });
  };

  const onCheckSaleOrder = () => {
    if (requestType == "" && dataRefund?.RefundRequestType?.length > 1) {
      Alert.alert("", "Vui lòng chọn loại yêu cầu huỷ");
      return false;
    }
    if (reason == "" && dataRefund?.RefundRequestReason?.length > 1) {
      Alert.alert("", "Vui lòng chọn loại lý do huỷ");
      return false;
    }
    if (helper.IsEmptyObject(note)) {
      Alert.alert("", "Vui lòng nhập ghi chú");
      return false;
    }
    return true;
  };

  const onCancelSOAndCreateCM = () => {
    const isValidate = onCheckSaleOrder();
    if (isValidate) {
      Alert.alert(
        "",
        `Bạn có chắc muốn hoàn tiền cho giao dịch "${SERVICEVOUCHERID}" này không?`,
        [
          {
            text: translate("saleOrderManager.btn_skip_uppercase"),
            style: "cancel",
          },
          {
            text: translate("saleOrderManager.btn_continue_uppercase"),
            style: "default",
            onPress: () => completeAndCreateCM(),
          },
        ]
      );
    }
  };

  const completeAndCreateCM = () => {
    showBlockUI();
    const data = {
      catalogID: ServiceCategoryID,
      serviceGroupID: AirtimeServiceGroupID,
      airtimeTransactionTypeID: AIRTIMETRANSACTIONTYPEID,
      airtimeTransactionID: AIRTIMETRANSACTIONID,
      totalRefundAmount: SALEPRICEVAT,
      rqRefundTypeID:
        dataRefund?.RefundRequestReason?.length == 1
          ? defaultRequestType?.RefundRequestTypeID
          : requestType,
      reasonID:
        dataRefund?.RefundRequestReason?.length == 1
          ? defaultRequestReason?.ReasonID
          : reason,
      refundNote: note,
      approvedUser: PROCESSUSER,
      approvedDate: INPUTTIME,
    };
    actionInsuranceAirtimeService
      .createAirtimeRefund(data)
      .then((reponse) => {
        Alert.alert("", reponse.errorReason, [
          {
            text: "OK",
            onPress: () => {
              hideBlockUI();
              navigation.navigate("HistoryRefundInsurance", { SERVICEVOUCHERID });
            },
          },
        ]);
      })
      .catch((msgError) => {
        Alert.alert("", msgError, [
          {
            text: "OK",
            onPress: () => {
              hideBlockUI();
            },
          },
        ]);
      });
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView>
        <View style={styles.itemCollect}>
          <MyText
            text={SERVICEVOUCHERID}
            style={{
              fontWeight: "bold",
              fontSize: 16,
              color: COLORS.bg1E88E5,
            }}
          />
          <TextField title={"Tên sản phẩm :"} value={PRODUCTNAME} />
          <TextField title={"Số điện thoại :"} value={CUSTOMERPHONE} />
          <TextField title={"Tên khách hàng :"} value={CUSTOMERNAME} />
          <TextField
            title={"Số tiền :"}
            value={helper.formatMoney(SALEPRICEVAT)}
          />
          <TextField title={"Ngày giao dịch :"} value={INPUTTIME} />
        </View>
        <View
          style={{
            backgroundColor: COLORS.bg57a7ff,
            height: 35,
            color: COLORS.bg2FB47C,
            justifyContent: "center",
            marginTop: 10,
          }}
        >
          <MyText
            text={"Yêu cầu hoàn tiền"}
            style={{
              fontWeight: "bold",
              fontSize: 16,
              color: COLORS.bgFFFFFF,
              marginLeft: 5,
            }}
          />
        </View>
        <View style={styles.requestRefund}>
          <TitleInput
            title={"Số tiền yêu cầu"}
            styleInput={{
              borderWidth: 1,
              borderRadius: 4,
              borderColor: COLORS.bdCCCCCC,
              marginBottom: 5,
              paddingHorizontal: 10,
              backgroundColor: COLORS.bgFFFFFF,
              paddingVertical: 8,
            }}
            placeholder={translate("collection.enter_sender_phone")}
            value={`${helper.formatMoney(SALEPRICEVAT)}`}
            onChangeText={(text) => { }}
            keyboardType="numeric"
            returnKeyType="done"
            blurOnSubmit
            width={constants.width - 20}
            height={40}
            clearText={() => { }}
            key="rechargerPhoneNumber"
            isRequired={true}
            editable={false}
          />
          <View
            style={{
              marginTop: 10,
            }}
          >
            <PickerSearch
              title={"Loại yêu cầu"}
              label={"RefundRequestTypeName"}
              value={"RefundRequestTypeID"}
              isRequired={true}
              valueSelected={requestType}
              data={
                helper.IsNonEmptyArray(dataRefund?.RefundRequestType)
                  ? dataRefund?.RefundRequestType
                  : []
              }
              onChange={(item) => {
                setRequestType(item?.RefundRequestTypeID);
              }}
              style={{
                flex: 1,
                flexDirection: "row",
                justifyContent: "center",
                alignItems: "center",
                height: 40,
                borderRadius: 4,
                borderWidth: 1,
                borderColor: COLORS.bdE4E4E4,
                width: constants.width - 20,
                backgroundColor: COLORS.btnFFFFFF,
                marginBottom: 10,
              }}
              disabled={
                dataRefund?.RefundRequestType?.length == 1 ? true : false
              }
              defaultLabel={
                dataRefund?.RefundRequestType?.length == 1
                  ? defaultRequestType.RefundRequestTypeName
                  : "Chọn loại yêu cầu"
              }
            />
          </View>
          <PickerSearch
            title={"Lý do"}
            label={"ReasonName"}
            value={"ReasonID"}
            data={
              helper.IsNonEmptyArray(dataRefund?.RefundRequestReason)
                ? dataRefund?.RefundRequestReason
                : []
            }
            valueSelected={reason}
            onChange={(item) => {
              setReason(item?.ReasonID);
            }}
            isRequired={true}
            style={{
              flex: 1,
              flexDirection: "row",
              justifyContent: "center",
              alignItems: "center",
              height: 40,
              borderRadius: 4,
              borderWidth: 1,
              borderColor: COLORS.bdE4E4E4,
              width: constants.width - 20,
              backgroundColor: COLORS.btnFFFFFF,
              marginBottom: 10,
            }}
            disabled={
              dataRefund?.RefundRequestReason?.length == 1 ? true : false
            }
            defaultLabel={
              dataRefund?.RefundRequestReason?.length == 1
                ? defaultRequestReason?.ReasonName
                : translate("saleOrderManager.select_cancel_reason")
            }
            defaultRequestReason
          />
          <View style={styles.viewReason}>
            <MyText text={"Ghi chú"} style={{ fontWeight: "bold" }} >
              <MyText
                text={"*"}
                addSize={-1.5}
                style={{
                  color: COLORS.txtFF0000
                }} />
            </MyText>
            <TextInput
              style={styles.content}
              value={String(note)}
              onChangeText={(text) => {
                setNote(text);
              }}
              placeholder={"Bắt buộc nhập:"}
              numberOfLines={6}
              multiline={true}
              textAlignVertical="top"
              returnKeyType="done"
              onSubmitEditing={Keyboard.dismiss}
            />
          </View>
        </View>
        <View>
          <ButtonAction onPress={onCancelSOAndCreateCM} disabled={false} />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const mapStateToProps = (state) => ({
  saleOrder: state.managerSOReducer.infoSODelete,
  dataInserAndCreateTicket: state.collectionReducer.dataInserAndCreateTicket,
  itemCatalog: state.collectionReducer.itemCatalog,
});

const mapDispatchToProps = (dispatch) => ({
  actionManagerSO: bindActionCreators(actionManagerSOCreator, dispatch),
  actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),
  actionCollection: bindActionCreators(actionCollectionCreator, dispatch),
  actionInsuranceAirtimeService: bindActionCreators(
    actionInsuranceAirtimeServiceCreator,
    dispatch
  ),
});

export default connect(mapStateToProps, mapDispatchToProps)(RefundInsuranceMic);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.bgFFFFFF,
  },
  itemCollect: {
    width: constants.width - 10,
    backgroundColor: COLORS.bgF0F0F0,
    marginTop: 10,
    alignSelf: "center",
    borderRadius: 10,
    paddingVertical: 10,
    paddingHorizontal: 10,
  },
  content: {
    borderWidth: 1,
    height: 100,
    borderColor: Color.hiddenGray,
    padding: 10,
  },
  requestRefund: {
    width: constants.width,
    alignSelf: "center",
    paddingVertical: 10,
    paddingHorizontal: 10,
  },
});

const TextField = ({ title, value }) => {
  return (
    <View
      style={{
        flexDirection: "row",
        justifyContent: "space-between",
        paddingVertical: 10,
      }}
    >
      <MyText
        text={title}
        style={{
          flex: 1,
          color: COLORS.bg8E8E93,
        }}
      />
      <MyText
        text={value}
        style={{
          flex: 1,
          color: COLORS.bg000000,
          textAlign: "right",
        }}
      />
    </View>
  );
};

const ButtonAction = ({ disabled, onPress }) => {
  return (
    <View
      style={{
        alignItems: "center",
        width: constants.width,
        paddingVertical: 10,
      }}
    >
      <Button
        text={"Tạo phiếu chi"}
        styleContainer={{
          backgroundColor: COLORS.btn288AD6,
          borderColor: COLORS.bd288AD6,
          borderWidth: 1,
          borderRadius: 4,
          paddingVertical: 10,
          paddingHorizontal: 20,
          opacity: disabled ? 0.5 : 1,
        }}
        styleText={{
          color: COLORS.txtFFFFFF,
        }}
        onPress={onPress}
        disabled={disabled}
      />
    </View>
  );
};
