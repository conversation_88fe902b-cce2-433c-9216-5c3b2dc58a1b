import {
  StyleSheet,
  View,
  SafeAreaView,
  ScrollView,
  Alert,
  TextInput,
  Keyboard,
} from "react-native";
import React, { useState, useEffect, useCallback } from "react";
import { COLORS } from "@styles";
import { constants } from "@constants";
import {
  Icon,
  MyText,
  PickerSearch,
  TitleInput,
  hideBlockUI,
  showBlockUI,
} from "@components";
import { Color, helper } from "@common";
import { translate } from "@translate";
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import * as actionCollectionCreator from "../../../CollectionTransfer/action";
import { useFocusEffect } from "@react-navigation/native";
import * as actionSaleOrderCreator from "../../../SaleOrderPayment/action";
import * as actionManagerSOCreator from "../../../SaleOrderManager/action";
import * as actionInsuranceAirtimeServiceCreator from "../../action";
import CollectionStatusModal from "../../component/Modal/CollectionStatusModal";
import ButtonRefund from "../../component/Button/ButtonRefund";
import TextField from "../../component/Text/TextField";
import MoneyTable from "../../component/Grid/MoneyTable";
import ButtonCollection from "../../component/Button/ButtonCollection";
import AcceptCancel from "../../component/Button/AcceptCancel";
import { dateHelper } from "@common";

const MAX_HEIGHT = 117;
const MIN_HEIGHT = 40;
const DISTANCE = MAX_HEIGHT - MIN_HEIGHT;

const RefundInsurance = ({
  route,
  actionCollection,
  dataInserAndCreateTicket,
  actionInsuranceAirtimeService,
  navigation,
  itemCatalog,
}) => {
  const [requestType, setRequestType] = useState("");
  const [reason, setReason] = useState("");
  const [statusTicket, setStatusTicket] = useState({});
  const [note, setNote] = useState("");
  const [dataRefund, setDataRefund] = useState({});
  const [airTimetransactionRFID, setAirTimetransactionRFID] = useState("");
  const [isVisibleModalCollection, setIsVisibleModalCollection] =
    useState(false);
  const [defaultRequestType, setDefaultRequestType] = useState({});
  const [defaultRequestReason, setDefaultRequestReason] = useState({});
  const { data } = route.params;
  const getTicketStatus = statusTicket?.STATUSID;
  const getStatusMess = statusTicket?.STATUSMESS;
  const { LISTUSERAPPROVE } = dataInserAndCreateTicket ?? "";
  const {
    SERVICEVOUCHERID,
    PRODUCTNAME,
    CUSTOMERNAME,
    TOTALAMOUNT,
    CUSTOMERPHONE,
    PHONENUMBER,
    AIRTIMETRANSACTIONID,
    PRODUCTID,
    INPUTTIME,
    AIRTIMETRANSACTIONTYPEID,
    PROCESSUSER,
    SALEORDERID,
    TICKETID,
  } = data;
  const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog ?? {};
  const giftVoucherReturnValue = dataRefund?.giftVoucherReturnValue;
  const totalExpenditure = dataRefund?.TOTALAMOUNT ?? '';
  const totalReturnDetail = dataRefund?.TotalReturnDetail ?? [];

  useEffect(() => {
    getDataInfoRefund();
  }, [actionInsuranceAirtimeService]);

  useFocusEffect(
    useCallback(() => {
      if (dataInserAndCreateTicket) {
        actionCollection.cleardDataTicket();
      }
    }, [dataInserAndCreateTicket.STATUSID, actionCollection])
  );

  const getDataInfoRefund = () => {
    showBlockUI();
    const data = {
      catalogID: ServiceCategoryID,
      serviceGroupID: AirtimeServiceGroupID,
      airtimeTransactionTypeID: AIRTIMETRANSACTIONTYPEID,
      productID: PRODUCTID,
      inputTime: INPUTTIME,
      serviceVoucherID: SERVICEVOUCHERID,
    };
    actionInsuranceAirtimeService
      .getInfoRefund(data)
      .then((reponse) => {
        hideBlockUI();
        setDataRefund(reponse);
        setDefaultRequestType(reponse?.RefundRequestType?.[0]);
        setDefaultRequestReason(reponse?.RefundRequestReason?.[0]);
      })
      .catch((msgError) => {
        Alert.alert("", msgError, [
          {
            text: "OK",
            onPress: () => {
              navigation.goBack();
              hideBlockUI();
            },
          },
        ]);
      });
  };

  const handleReplyTicket = (AIRTIMETRANSACTIONID) => {
    const { TOTALAMOUNT } = dataRefund ?? {};
    if (TOTALAMOUNT < 0) {
      Alert.alert(
        "Thông báo",
        "Số tiền chi cho khách không hợp lệ. Liên hệ ngành hàng dịch vụ để được hỗ trợ!.",
        [
          {
            text: "OK",
            onPress: () => {
            }
          },
        ],
      );
    } else {
      showBlockUI();
      actionCollection
        .createTicletServiceRequest(AIRTIMETRANSACTIONID)
        .then((reponseDataTicket) => {
          hideBlockUI();
        })
        .catch((msgError) => {
          Alert.alert("", msgError, [
            {
              text: "OK",
              onPress: hideBlockUI,
            },
            {
              text: "Thử lại",
              onPress: () => handleReplyTicket(AIRTIMETRANSACTIONID),
            },
          ]);
        });
    }
  };

  const handleQueryStatus = () => {
    showBlockUI();
    const { TICKETID, AIRTIMETRANSACTIONID } = dataInserAndCreateTicket ?? {};
    const data = {
      TICKETID: TICKETID,
      AIRTIMETRANSACTIONID: AIRTIMETRANSACTIONID,
    };
    actionInsuranceAirtimeService
      .checksSatusTicketService(data)
      .then((reponseStatus) => {
        hideBlockUI();
        setStatusTicket(reponseStatus);
      })
      .catch((msgError) => {
        Alert.alert("", msgError, [
          {
            text: "OK",
            onPress: hideBlockUI,
          },
          {
            text: "Thử lại",
            onPress: () => {
              handleQueryStatus();
            },
          },
        ]);
      });
  };

  const onCheckSaleOrder = () => {
    if (requestType == "" && dataRefund?.RefundRequestType?.length > 1) {
      Alert.alert("", "Vui lòng chọn loại yêu cầu huỷ");
      return false;
    }
    if (reason == "" && dataRefund?.RefundRequestReason?.length > 1) {
      Alert.alert("", "Vui lòng chọn loại lý do huỷ");
      return false;
    }
    if (helper.IsEmptyObject(note)) {
      Alert.alert("", "Vui lòng nhập ghi chú");
      return false;
    }
    return true;
  };

  const onCancelSOAndCreateCM = () => {
    const isValidate = onCheckSaleOrder();
    if (isValidate) {
      Alert.alert("", "Bạn có chắc muốn huỷ đơn hàng này", [
        {
          text: translate("saleOrderManager.btn_skip_uppercase"),
          style: "cancel",
        },
        {
          text: translate("saleOrderManager.btn_continue_uppercase"),
          style: "default",
          onPress: () => completeAndCreateCM(),
        },
      ]);
    }
  };

  const completeAndCreateCM = () => {
    const data = {
      catalogID: ServiceCategoryID,
      serviceGroupID: AirtimeServiceGroupID,
      airtimeTransactionTypeID: AIRTIMETRANSACTIONTYPEID,
      airtimeTransactionID: AIRTIMETRANSACTIONID,
      totalRefundAmount: TOTALAMOUNT,
      rqRefundTypeID:
        dataRefund?.RefundRequestReason?.length == 1
          ? defaultRequestType?.RefundRequestTypeID
          : requestType,
      reasonID:
        dataRefund?.RefundRequestReason?.length == 1
          ? defaultRequestReason?.ReasonID
          : reason,
      refundNote: note,
      approvedUser: PROCESSUSER,
      approvedDate: INPUTTIME,
    };
    actionInsuranceAirtimeService
      .createAirtimeRefund(data)
      .then((reponse) => {
        hideBlockUI();
        setAirTimetransactionRFID(reponse?.AirTimetransactionRFID);
        setIsVisibleModalCollection(true);
      })
      .catch((msgError) => {
        Alert.alert("", msgError, [
          {
            text: "OK",
            onPress: () => {
              hideBlockUI();
            },
          },
        ]);
      });
  };

  const onNavigationHistoryRefundMoney = () => {
    setIsVisibleModalCollection(false);
    navigation.navigate("HistoryRefundInsurance", { SERVICEVOUCHERID });
  };

  const handleOrderStatusSuccess = () => {
    setIsVisibleModalCollection(false);
    navigation.navigate("HistoryRefundInsurance", { SERVICEVOUCHERID });
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView>
        <View style={styles.itemCollect}>
          <MyText
            text={SERVICEVOUCHERID}
            style={{
              fontWeight: "bold",
              fontSize: 16,
              color: COLORS.bg1E88E5,
            }}
          />
          <TextField name={"Tên sản phẩm :"} value={PRODUCTNAME} />
          {!!PHONENUMBER && <TextField name={"IMEI :"} value={PHONENUMBER} />}
          <TextField name={"Số điện thoại :"} value={CUSTOMERPHONE} />
          <TextField name={"Tên khách hàng :"} value={CUSTOMERNAME} />
          <TextField
            name={"Số tiền :"}
            value={helper.formatMoney(TOTALAMOUNT)}
          />
          <TextField name={"Ngày giao dịch :"} value={dateHelper.formatStrDateFULL(INPUTTIME)} />
        </View>
        <View
          style={{
            backgroundColor: COLORS.bg57a7ff,
            height: 35,
            color: COLORS.bg2FB47C,
            justifyContent: "center",
            marginTop: 10,
          }}
        >
          <MyText
            text={"Yêu cầu hoàn tiền"}
            style={{
              fontWeight: "bold",
              fontSize: 16,
              color: COLORS.bgFFFFFF,
              marginLeft: 5,
            }}
          />
        </View>
        <View style={styles.requestRefund}>
          {helper.IsNonEmptyArray(totalReturnDetail) ? (
            <MoneyTable
              data={totalReturnDetail}
              editable={false}
              totalAmount={TOTALAMOUNT}
              giftVoucherReturnValue={giftVoucherReturnValue}
              totalCost={totalExpenditure}
            />
          ) : (
            <TitleInput
              title={"Số tiền yêu cầu"}
              styleInput={{
                borderWidth: 1,
                borderRadius: 4,
                borderColor: COLORS.bdCCCCCC,
                marginBottom: 5,
                paddingHorizontal: 10,
                backgroundColor: COLORS.bgFFFFFF,
                paddingVertical: 8,
              }}
              placeholder={translate("collection.enter_sender_phone")}
              value={`${helper.formatMoney(TOTALAMOUNT)}`}
              onChangeText={(text) => { }}
              keyboardType="numeric"
              returnKeyType="done"
              blurOnSubmit
              width={constants.width - 20}
              height={40}
              clearText={() => { }}
              key="rechargerPhoneNumber"
              isRequired={true}
              editable={false}
            />
          )}
          <View
            style={{
              marginTop: 10,
            }}
          >
            <PickerSearch
              title={"Loại yêu cầu"}
              label={"RefundRequestTypeName"}
              value={"RefundRequestTypeID"}
              isRequired={true}
              valueSelected={requestType}
              data={
                helper.IsNonEmptyArray(dataRefund?.RefundRequestType)
                  ? dataRefund?.RefundRequestType
                  : []
              }
              onChange={(item) => {
                setRequestType(item?.RefundRequestTypeID);
              }}
              style={{
                flex: 1,
                flexDirection: "row",
                justifyContent: "center",
                alignItems: "center",
                height: 40,
                borderRadius: 4,
                borderWidth: 1,
                borderColor: COLORS.bdE4E4E4,
                width: constants.width - 20,
                backgroundColor: COLORS.btnFFFFFF,
                marginBottom: 10,
              }}
              disabled={
                dataRefund?.RefundRequestType?.length == 1 ? true : false
              }
              defaultLabel={
                dataRefund?.RefundRequestType?.length == 1
                  ? defaultRequestType.RefundRequestTypeName
                  : "Chọn loại yêu cầu"
              }
            />
          </View>
          <PickerSearch
            title={"Lý do"}
            label={"ReasonName"}
            value={"ReasonID"}
            data={
              helper.IsNonEmptyArray(dataRefund?.RefundRequestReason)
                ? dataRefund?.RefundRequestReason
                : []
            }
            valueSelected={reason}
            onChange={(item) => {
              setReason(item?.ReasonID);
            }}
            isRequired={true}
            style={{
              flex: 1,
              flexDirection: "row",
              justifyContent: "center",
              alignItems: "center",
              height: 40,
              borderRadius: 4,
              borderWidth: 1,
              borderColor: COLORS.bdE4E4E4,
              width: constants.width - 20,
              backgroundColor: COLORS.btnFFFFFF,
              marginBottom: 10,
            }}
            disabled={
              dataRefund?.RefundRequestReason?.length == 1 ? true : false
            }
            defaultLabel={
              dataRefund?.RefundRequestReason?.length == 1
                ? defaultRequestReason?.ReasonName
                : translate("saleOrderManager.select_cancel_reason")
            }
            defaultRequestReason
          />
          <View style={styles.viewReason}>
            <MyText text={"Ghi chú"} style={{ fontWeight: "bold" }} >
              <MyText
                text={"*"}
                addSize={-1.5}
                style={{
                  color: COLORS.txtFF0000
                }} />
            </MyText>
            <TextInput
              style={styles.content}
              value={String(note)}
              onChangeText={(text) => {
                setNote(text);
              }}
              placeholder={"Bắt buộc nhập:"}
              numberOfLines={6}
              multiline={true}
              textAlignVertical="top"
              returnKeyType="done"
              onSubmitEditing={Keyboard.dismiss}
            />
          </View>
        </View>
        <View>
          {helper.IsEmptyObject(dataInserAndCreateTicket) ? (
            <View>
              <AcceptCancel
                title="QUẢN LÝ SIÊU THỊ XÁC NHẬN"
                onPress={() => handleReplyTicket(AIRTIMETRANSACTIONID)}
              />
            </View>
          ) : (
            <View>
              <View
                style={{
                  padding: 10,
                }}
              >
                <View
                  style={{
                    backgroundColor: COLORS.bgFFFFFF,
                    borderRadius: 7,
                    padding: 10,
                    shadowColor: COLORS.bg7F7F7F,
                    shadowOffset: {
                      width: 0,
                      height: 0,
                    },
                    shadowOpacity: 0.5,
                    shadowRadius: 1,
                    elevation: 5,
                  }}
                >
                  <MyText
                    text={"Yêu cầu hoàn tiền của giao dịch "}
                    addSize={-1.5}
                    style={{
                      color: COLORS.txt333333,
                      marginBottom: 10,
                      fontSize: 15,
                      marginTop: 10,
                      marginLeft: 5,
                    }}
                  >
                    {
                      <MyText
                        text={`[${SERVICEVOUCHERID}]`}
                        addSize={-1.5}
                        style={{
                          color: COLORS.txtD0021B,
                          fontSize: 15,
                        }}
                      >
                        {
                          <MyText
                            text={` của bạn đã được gửi thông báo đến app X-Work của quản lý siêu thị có chấm công trong ca gồm: ${LISTUSERAPPROVE}. Vui lòng chờ quản lý siêu thị xác nhận trên App X-Work!`}
                            addSize={-1.5}
                            style={{
                              color: COLORS.txt333333,
                              fontSize: 15,
                            }}
                          />
                        }
                      </MyText>
                    }
                  </MyText>
                  <View
                    style={{
                      flexDirection: "row",
                    }}
                  >
                    <Icon
                      iconSet={"MaterialIcons"}
                      name={"info-outline"}
                      color={COLORS.bgFF0000}
                      size={18}
                    />
                    <MyText
                      text={"Ticket có hiệu lực trong 10 phút"}
                      addSize={-1.5}
                      style={{
                        color: COLORS.bgFF0000,
                        fontSize: 15,
                        marginLeft: 5,
                        fontStyle: "italic",
                      }}
                    />
                  </View>
                </View>
                <View
                  style={{
                    flexDirection: "row",
                    backgroundColor: COLORS.bgFFFFFF,
                    borderRadius: 7,
                    padding: 10,
                    alignItems: "center",
                    shadowColor: COLORS.bg7F7F7F,
                    shadowOffset: {
                      width: 0,
                      height: 0,
                    },
                    shadowOpacity: 0.5,
                    shadowRadius: 1,
                    elevation: 5,
                    marginTop: 10,
                  }}
                >
                  <MyText
                    text={"Trạng thái ticket:"}
                    addSize={-1.5}
                    style={{
                      color: COLORS.txt333333,
                      marginBottom: 10,
                      fontSize: 15,
                      marginTop: 10,
                      fontWeight: "bold",
                    }}
                  />
                  <MyText
                    text={
                      getTicketStatus != null
                        ? getStatusMess
                        : "Đã gửi cho QLST"
                    }
                    addSize={-1.5}
                    style={{
                      color:
                        getTicketStatus != "APPROVE" && getTicketStatus != null
                          ? COLORS.bgFF0000
                          : COLORS.bg00AAFF,
                      fontSize: 15,
                      fontWeight: "bold",
                      marginLeft: 5,
                    }}
                  />
                </View>
              </View>
              <View
                style={{
                  padding: 10,
                }}
              >
                {getTicketStatus == "APPROVE" ? (
                  <ButtonRefund
                    onPress={onCancelSOAndCreateCM}
                    disabled={false}
                  />
                ) : (
                  <View
                    style={{
                      justifyContent: "center",
                      alignItems: "center",
                      flexDirection: "row",
                    }}
                  >
                    <ButtonCollection
                      onPress={() => handleReplyTicket(AIRTIMETRANSACTIONID)}
                      disabled={
                        helper.IsEmptyObject(statusTicket) ||
                          getTicketStatus == undefined ||
                          getTicketStatus == "WAITING"
                          ? true
                          : false
                      }
                      title={"GỬI LẠI TICKET"}
                      iconSet={"Ionicons"}
                      nameIcon={"reload"}
                      style={{
                        backgroundColor: COLORS.bg00A98F,
                      }}
                      opacity={
                        helper.IsEmptyObject(statusTicket) ||
                          getTicketStatus == undefined ||
                          getTicketStatus == "WAITING"
                          ? 0.5
                          : 1
                      }
                    />
                    <View style={{ flex: 1 }} />
                    <ButtonCollection
                      onPress={() => handleQueryStatus()}
                      title={"KIỂM TRA KẾT QUẢ"}
                      iconSet={"Ionicons"}
                      nameIcon={"search"}
                      style={{
                        backgroundColor: COLORS.bg1E88E5,
                      }}
                    />
                  </View>
                )}
              </View>
            </View>
          )}
        </View>
        {isVisibleModalCollection && (
          <CollectionStatusModal
            isVisible={isVisibleModalCollection}
            SaleOrderID={SALEORDERID}
            onClose={() => onNavigationHistoryRefundMoney()}
            onSuccess={() => handleOrderStatusSuccess()}
            airTimetransactionRFID={airTimetransactionRFID}
          />
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const mapStateToProps = function (state) {
  return {
    saleOrder: state.managerSOReducer.infoSODelete,
    dataInserAndCreateTicket: state.collectionReducer.dataInserAndCreateTicket,
    itemCatalog: state.collectionReducer.itemCatalog,
  };
};

const mapDispatchToProps = function (dispatch) {
  return {
    actionManagerSO: bindActionCreators(actionManagerSOCreator, dispatch),
    actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),
    actionCollection: bindActionCreators(actionCollectionCreator, dispatch),
    actionInsuranceAirtimeService: bindActionCreators(
      actionInsuranceAirtimeServiceCreator,
      dispatch
    ),
  };
};
export default connect(mapStateToProps, mapDispatchToProps)(RefundInsurance);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.bgFFFFFF,
  },
  itemCollect: {
    width: constants.width - 10,
    backgroundColor: COLORS.bgF0F0F0,
    marginTop: 10,
    alignSelf: "center",
    borderRadius: 10,
    paddingVertical: 10,
    paddingHorizontal: 10,
  },
  content: {
    borderWidth: 1,
    height: 100,
    borderColor: Color.hiddenGray,
    padding: 10,
  },
  requestRefund: {
    width: constants.width,
    alignSelf: "center",
    paddingVertical: 10,
    paddingHorizontal: 10,
  },
});
