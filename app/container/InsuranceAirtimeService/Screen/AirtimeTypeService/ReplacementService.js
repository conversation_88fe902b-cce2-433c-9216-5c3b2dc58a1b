import {
  Safe<PERSON>reaView,
  StyleSheet,
  View,
  <PERSON>List,
  <PERSON><PERSON>,
  Animated,
} from "react-native";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { COLORS } from "@styles";
import { TouchableOpacity } from "react-native";
import { Image } from "react-native";
import { translate } from "@translate";
import {
  BaseLoading,
  Icon,
  MyText,
  hideBlockUI,
  showBlockUI,
} from "@components";
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import * as actionInsuranceAirtimeServiceCreator from "../../action";
import InsuranceBenefits from "../../component/Modal/InsuranceBenefits";
import SupportingInformation from "../../component/Button/SupportingInformation";
import InsuranceBenefitsMic from "../../component/Modal/InsuranceBenefitsMic";
import { helper } from "../../../../common";
import FastImage from 'react-native-fast-image';
import ModalInformationInsurance from "../../component/Modal/ModalInformationInsurance";

const ReplacementService = ({
  navigation,
  dataServiceList,
  stateServiceList,
  actionInsuranceAirtimeService,
  itemCatalog,
}) => {
  const [updateItem, setUpdateItem] = useState({});
  console.log(updateItem, "log updateItem");
  const scaleValue = useRef(new Animated.Value(1)).current;
  const opacityValue = useRef(new Animated.Value(1)).current;
  const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog ?? {};
  const { isFetching, isError, isEmpty, description } = stateServiceList ?? "";
  const [isVisibleModal, setIsVisibleModal] = useState(false);
  const [isViewBenefit, setIsViewBenefit] = useState(true);
  const [onScrollFlatlist, setOnScrollFlatlist] = useState(true);
  const [selectedPartnerInfo, setSelectedPartnerInfo] = useState({});
  const [isSwitchStatus, setIsSwitchStatus] = useState(true);
  const [isVisibleModalCollection, setIsVisibleModalCollection] = useState(false);

  const animatePressIn = () => {
    Animated.parallel([
      Animated.spring(scaleValue, {
        toValue: 0.95,
        friction: 3,
        useNativeDriver: true,
      }),
      Animated.timing(opacityValue, {
        toValue: 0.8,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const animatePressOut = () => {
    Animated.parallel([
      Animated.spring(scaleValue, {
        toValue: 1,
        friction: 3,
        useNativeDriver: true,
      }),
      Animated.timing(opacityValue, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  };

  useEffect(() => {
    if (ServiceCategoryID == 3 && AirtimeServiceGroupID == 30) {
      setIsSwitchStatus(!isSwitchStatus);
    }
  }, [ServiceCategoryID, AirtimeServiceGroupID]);
  useEffect(() => {
    actionInsuranceAirtimeService.setHealthInsuranceSaleOrderId("");
    if (helper.IsNonEmptyArray(dataServiceList)) {
      setSelectedPartnerInfo(dataServiceList[0]);
    }
  }, [dataServiceList]);
  useEffect(() => {
    const data = {
      catalogID: ServiceCategoryID,
      serviceGroupID: AirtimeServiceGroupID,
    };
    actionInsuranceAirtimeService.getServiceList(data);
  }, [actionInsuranceAirtimeService]);

  const onSelectPartner = (item) => {
    showBlockUI();
    const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog;
    const { AirTimeTransactionTypeID, CustomerID } = item;
    const data = {
      catalogId: ServiceCategoryID,
      serviceGroupId: AirtimeServiceGroupID,
      airtimeTransactionTypeId: AirTimeTransactionTypeID,
      partnerId: CustomerID,
    };
    actionInsuranceAirtimeService
      .getInputFormTemplate(data)
      .then(() => {
        hideBlockUI();
        if (AirtimeServiceGroupID === 44) {
          navigation.navigate("CarInsuranceSteps");
        }
        else if (AirtimeServiceGroupID === 45) {
          navigation.navigate("CarPhysicalDamageInsuranceSteps");
        }
        else {
          navigation.navigate("HealthInsuranceSteps");
        }

      })
      .catch((msgError) => {
        Alert.alert("", msgError, [
          {
            text: "OK",
            onPress: hideBlockUI,
          },
        ]);
      });
  };

  const onViewPartnerInfo = (index) => {
    setSelectedPartnerInfo(dataServiceList[index]);
  };
  const viewBenefit = () => {
    setIsViewBenefit(true);
    setIsVisibleModal(true);
  };

  const viewInsuranceFee = () => {
    setIsViewBenefit(false);
    setIsVisibleModal(true);
  };
  const handleItemPress = useCallback(
    (item) => {
      actionInsuranceAirtimeService.updateHeaderAirtime(item);
      actionInsuranceAirtimeService.clear_data_validate_service_request();
      switch (AirtimeServiceGroupID) {
        case 28:
        case 33:
        case 41:
        case 43:
          navigation.navigate("SellInsurance", { item });
          break;
        case 29:
          actionInsuranceAirtimeService.clear_data_search_so();
          navigation.navigate("WarrantyOrders", { item });
          break;
        case 19:
        case 30:
        case 44:
        case 45:
          onSelectPartner(item);
          break;
        case 31:
          actionInsuranceAirtimeService.clear_data_search_so();
          navigation.navigate("SaleOrderInsuraneLoanProtection", { item });
          break;
        default:
          break;
      }
    },
    [
      actionInsuranceAirtimeService,
      ServiceCategoryID,
      AirtimeServiceGroupID,
      navigation,
    ]
  );

  const handleInfoPress = useCallback((item) => {
    if (AirtimeServiceGroupID == 29) {
      setIsVisibleModalCollection(true)
    }
    else if (item?.InsBenefits !== "") {
      setIsVisibleModal(true);
    } else {
      Alert.alert(
        translate("common.notification_uppercase"),
        "Không tìm thấy quyền lợi bảo hiểm!",
        [
          {
            text: translate("collection.btn_accept"),
            onPress: () => { },
          },
        ]
      );
    }
    setUpdateItem(item);
  }, []);

  const renderItem = ({ item, index }) => {
    return (
      <View
        style={{
          padding: 5,
        }}
      >
        <View
          style={{
            flexDirection: "row",
            backgroundColor: COLORS.bgFFFFFF,
            borderRadius: 15,
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 5 },
            shadowOpacity: 0.15,
            shadowRadius: 10,
            elevation: 5,
            padding: 10,
            alignItems: "center",
          }}
        >
          <View
            style={{
              flexDirection: "row",
            }}
          >
            <TouchableOpacity
              onPressIn={animatePressIn}
              onPressOut={() => {
                animatePressOut();
                handleItemPress(item);
              }}
              style={{
                flexDirection: "row",
                alignItems: "center",
                flex: 1,
                height: 90,
                borderRadius: 10,
                backgroundColor: COLORS.bgFFFFFF,
                flexDirection: "row",
                shadowColor: COLORS.bg8E8E93,
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.3,
                shadowRadius: 6,
                elevation: 6,
              }}
            >
              <FastImage
                style={{ width: 90, height: 50, marginLeft: 5 }}
                source={{
                  uri: item.Logo,
                  priority: FastImage.priority.normal,
                }}
                resizeMode={FastImage.resizeMode.contain}
              />
              <MyText
                style={{
                  color: COLORS.txt000000,
                  fontSize: 12,
                  fontWeight: "bold",
                  textAlign: "center",
                  flex: 1,
                }}
                text={item.AirTimeTransactionTypeName}
              />
            </TouchableOpacity>
            {isSwitchStatus == true && (
              <View
                style={{
                  width: "25%",
                  height: 90,
                  justifyContent: "center",
                  alignItems: "center",
                  backgroundColor: COLORS.txt0099E5,
                  borderRadius: 10,
                  marginLeft: 5,
                }}
              >
                <TouchableOpacity
                  onPress={() => handleInfoPress(item)}
                  style={{
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <Icon
                    iconSet={"Ionicons"}
                    name={"information-circle-outline"}
                    color={COLORS.bgFFFFFF}
                    size={35}
                  />
                  <MyText
                    style={{
                      color: COLORS.bgFFFFFF,
                      width: 70,
                      textAlign: "center",
                    }}
                    text={AirtimeServiceGroupID == 29 ? "Thông tin bảo hiểm" : "Quyền lợi bảo hiểm"}
                  />
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
        {isSwitchStatus == false && (
          <SupportingInformation
            viewBenefit={viewBenefit}
            viewInsuranceFee={viewInsuranceFee}
          />
        )}
        {isVisibleModal &&
          (AirtimeServiceGroupID == 30 ? (
            <InsuranceBenefitsMic
              selectedPartnerInfo={selectedPartnerInfo}
              listPartner={dataServiceList.map(
                (partner) => partner.CustomerAlias
              )}
              isVisible={isVisibleModal}
              hideModal={() => {
                setIsVisibleModal(false);
              }}
              onViewPartnerInfo={onViewPartnerInfo}
              isViewBenefit={isViewBenefit}
            />
          ) : (
            <InsuranceBenefits
              selectedPartnerInfo={updateItem}
              listPartner={dataServiceList.map(
                (partner) => partner.CustomerAlias
              )}
              isVisible={isVisibleModal}
              hideModal={() => {
                setIsVisibleModal(false);
              }}
              isViewBenefit={isViewBenefit}
            />
          ))}
        {isVisibleModalCollection && (
          <ModalInformationInsurance
            isVisible={isVisibleModalCollection}
            data={updateItem}
            onClose={() => setIsVisibleModalCollection(false)}
          />
        )}
      </View>
    );
  };

  const preGetServiceList = (ServiceCategoryID, AirtimeServiceGroupID) => {
    const data = {
      catalogID: ServiceCategoryID,
      serviceGroupID: AirtimeServiceGroupID,
    };
    actionInsuranceAirtimeService.getServiceList(data);
  };

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: COLORS.bgFFFFFF,
      }}
    >
      <KeyboardAwareScrollView
        style={{
          flex: 1,
        }}
        enableResetScrollToCoords={false}
        keyboardShouldPersistTaps="always"
        bounces={false}
        overScrollMode="always"
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        extraScrollHeight={60}
      >
        <SafeAreaView
          style={{
            flex: 1,
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <BaseLoading
            isLoading={isFetching}
            isError={isError}
            isEmpty={isEmpty}
            textLoadingError={description}
            onPressTryAgains={() => {
              preGetServiceList(ServiceCategoryID, AirtimeServiceGroupID);
            }}
            content={
              <View>
                <FlatList
                  scrollEnabled={onScrollFlatlist}
                  style={{
                    backgroundColor: COLORS.bgFFFFFF,
                    padding: 5,
                  }}
                  data={dataServiceList}
                  keyExtractor={(item, index) => `${index}`}
                  renderItem={(item) => renderItem(item)}
                />
              </View>
            }
          />
        </SafeAreaView>
      </KeyboardAwareScrollView>
    </View>
  );
};

const mapStateToProps = function (state) {
  return {
    dataServiceList: state.insuranceAirtimeServiceReducer.dataServiceList,
    stateServiceList: state.insuranceAirtimeServiceReducer.stateServiceList,
    itemCatalog: state.collectionReducer.itemCatalog,
  };
};

const mapDispatchToProps = function (dispatch) {
  return {
    actionInsuranceAirtimeService: bindActionCreators(
      actionInsuranceAirtimeServiceCreator,
      dispatch
    ),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(ReplacementService);

const styles = StyleSheet.create({});
