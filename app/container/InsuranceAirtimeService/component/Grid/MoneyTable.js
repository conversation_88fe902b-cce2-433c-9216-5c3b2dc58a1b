import React, { useEffect, useRef } from 'react';
import { Text, TextInput, Animated } from 'react-native';
import { helper } from '@common';

const MoneyTable = ({ data: propsData, editable, totalAmount, giftVoucherReturnValue, totalCost }) => {
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const slideAnim = useRef(new Animated.Value(0)).current;

    useEffect(() => {
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 800,
                useNativeDriver: true,
            }),
            Animated.spring(slideAnim, {
                toValue: 1,
                friction: 8,
                tension: 40,
                useNativeDriver: true,
            })
        ]).start();
    }, []);

    const amountRequested = {
        Label: "Số tiền yêu cầu",
        Value: totalAmount,
    };
    const amountRecovered = {
        Label: "Số tiền thu hồi PMH đã sử dụng",
        Value: giftVoucherReturnValue
    };
    const totalExpenditure = {
        Label: "Tổng số tiền chi cho khách",
        Value: totalCost,
        Color: '#E83F25'
    };

    const mainData = [
        !!totalAmount ? amountRequested : null,
        !!giftVoucherReturnValue ? amountRecovered : null,
        !!totalCost ? totalExpenditure : null
    ].filter(Boolean);

    const renderItem = (item, index, isLast) => {
        const translateY = slideAnim.interpolate({
            inputRange: [0, 1],
            outputRange: [50, 0],
        });

        return (
            <Animated.View
                key={index}
                style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    paddingVertical: 10,
                    paddingHorizontal: 18,
                    backgroundColor: index % 2 === 0 ? '#fafbfc' : '#f1f4f9',
                    borderBottomWidth: (isLast || item === totalExpenditure) ? 0 : 1,
                    borderBottomColor: '#e1e7ee',
                    opacity: fadeAnim,
                    transform: [{ translateY }],
                }}
            >
                <Text
                    style={{
                        fontSize: 15,
                        color: '#2c3e50',
                        fontWeight: '500',
                        flex: 1,
                    }}
                    numberOfLines={1}
                >
                    {item.Label}
                </Text>

                {editable ? (
                    <TextInput
                        value={item.Value?.toString()}
                        onChangeText={(text) => {
                            item.onChange && item.onChange(text);
                        }}
                        keyboardType="numeric"
                        placeholder="0"
                        placeholderTextColor="#aaa"
                        style={{
                            fontSize: 15,
                            fontWeight: '600',
                            color: item.Color || '#1e272e',
                            borderBottomWidth: 1.5,
                            borderBottomColor: '#ccc',
                            minWidth: 100,
                            textAlign: 'right',
                            paddingVertical: 4,
                        }}
                    />
                ) : (
                    <Text
                        style={{
                            fontSize: 15,
                            fontWeight: '600',
                            color: item.Color || '#1e272e',
                            textAlign: 'right',
                            flexShrink: 1,
                            maxWidth: 140,
                        }}
                        numberOfLines={1}
                    >
                        {helper.formatMoney(item.Value)}
                    </Text>
                )}
            </Animated.View>
        );
    };

    const filteredData = React.useMemo(() => 
        propsData?.filter(item => Number(item?.Value) !== 0) || [],
        [propsData]
      );

    return (
        <Animated.View
            style={{
                borderWidth: 1,
                borderColor: '#dce3ed',
                borderRadius: 16,
                overflow: 'hidden',
                marginTop: 16,
                backgroundColor: '#fff',
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: 0.08,
                shadowRadius: 4,
                elevation: 1,
                opacity: fadeAnim,
                transform: [{ scale: fadeAnim }],
            }}
        >
            {mainData.map((item, index) =>
                renderItem(item, index, index === mainData.length - 1 && propsData?.length === 0)
            )}

            {filteredData.length > 0 && (
                <Animated.View
                    style={{
                        margin: 12,
                        borderWidth: 1,
                        borderColor: '#dce3ed',
                        borderRadius: 12,
                        overflow: 'hidden',
                        opacity: fadeAnim,
                        transform: [{ scale: fadeAnim }],
                    }}
                >
                    {filteredData.map((item, index) =>
                        renderItem(item, index, index === filteredData.length - 1)
                    )}
                </Animated.View>
            )}
        </Animated.View>
    );
};

export default MoneyTable;
