import React, { Component } from 'react';
import {
    <PERSON>,
    <PERSON>ert,
    <PERSON>ing,
    BackHandler,
    Keyboard,
    Button,
    TouchableOpacity,
    StyleSheet,
    TextInput
} from 'react-native';
import {
    BaseLoading,
    showBlock<PERSON>,
    hideBlockUI,
    MyText,
    Button as ButtonPayment,
    OTPSheet,
    Icon,
    ImageURI
} from "@components";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import SafeAreaView from "react-native-safe-area-view";
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { useIsFocused } from '@react-navigation/native';
import Toast from 'react-native-toast-message';
import CryptoJS from 'rn-crypto-js';
import {
    helper,
    storageHelper,
    convertHtml2Image,
    convertToBase64,
    printSocket
} from "@common";
import { constants, DEVICE, STORAGE_CONST } from "@constants";
import CustomerInfo from "./component/CustomerInfo";
import ContractInfo from "./component/ContractInfo";
import CheckBox from "./component/CheckBox";
import MainProduct from "./component/ProductOrder/MainProduct";
import GiftProduct from "./component/ProductOrder/GiftProduct";
import ProductOrder from "./component/ProductOrder/index";
import AmountInfo from "./component/AmountInfo";
import DebtRefundInfo from "./component/DebtRefundInfo";
import PaymentVoucher from "./component/PaymentVoucher/index";
import Voucher from "./component/PaymentVoucher/Voucher";
import PaymentCard from "./component/PaymentCard/index";
import MoneyCard from "./component/PaymentCard/MoneyCard";
import PrintReport from "./component/PrintReport/index";
import Report from "./component/PrintReport/Report";
import PaymentLoyalty from "./component/PaymentLoyalty/index";
import ButtonCreate from "./component/ButtonCreate";
import PaymentQRCode from "./component/PaymentQRCode";
import CheckCustomer from "./component/CheckCustomer";
import CheckPrint from "./component/CheckPrint";
import PaymentBankTransfer from './component/PaymentBankTransfer';
import { AlertMessage, Transaction } from '../AnKhangNew/components';
import * as actionShoppingCartCreator from "../ShoppingCart/action";
import * as actionSaleOrderCreator from "./action";
import * as actionManagerSOCreator from "../SaleOrderManager/action";
import * as actionCardCreator from "../Card/action";
import * as actionSaleCreator from '../Detail/action';
import * as actionLoyaltyCreator from '../Loyalty/action';
import { translate, keys } from '@translate';
import PhoneVoucher from "./component/Modal/PhoneVoucher";
import BatchInfo from '../AnKhangPharmacy/components/BatchInfo';
import PaymentVoucherPartner from './component/PaymentVoucherPartner';
import { checkShowPackagingBagScreen } from './action';
import { showMessage } from 'react-native-flash-message';
import ModalPinCode from './component/PinCodeModal';
import { handleApiGetPointsByPayment } from '../SaleOrderCart';
import OTPCustomerVoucher from './component/OTPCustomerVoucher';
import CollectionStatusModal from './component/Modal/CollectionStatusModal';
import * as actionCollectionManagerCreator from '../CollectionTransferManager/action'
import * as actionAnKhangNewCreator from "../AnKhangNew/action"
import { COLORS } from '@styles';
import ConnectSimMobi from './component/Modal/ConnectSimMobi';
import VoucherPartner from './component/Vouchers/VoucherPartner';
import { SCREENS } from '../AnKhangNew/constants';
import * as actionInsuranceBrightsideCreator from "../InsuranceBrightside/action";
import * as actionHealthInsuranceCreator from '../HealthInsurance/action';
import * as actionBankAirtimeServiceCreator from "../BankAirtimeService/action";
import * as actionCollectInstallmentCreator from "../CollectInstallmentPayments/action";
import SaleProgramInfo from './component/SaleProgramInfo';
import PaymentTransferSheet from './Sheet/PaymentTransferSheet';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { getKeyMoneyCard } from '../SaleOrderManager/component/PaymentCard/component/InputCard';
import OTPInner from '../ShoppingCart/component/OTPInner';
import { TYPE_PROFILE } from '../../constants/constants';
import * as actionCollectionCreator from "../CollectionTransfer/action";

const { H_BILL, H_VOUCHER, H_KEY, PAYMENT_TYPE, TRANSACTION_STATUS, REPORT_NAME, PARTNER_ID, MONEY_CARD_ID, PAYMENT_PARTNER_ID } = constants;
const VoucherWarningMessage = 'Khách đang bị mất tiền trên PMH.';
const PreventCompleteSOMessage = 'Bạn vui lòng kiểm tra và điều chỉnh lại thứ tự nhập tiền thanh toán vào đơn hàng!';

class SaleOrderPayment extends Component {

    constructor(props) {
        super(props);
        this.state = {
            isOutput: true,
            isPayCash: true,
            mainProducts: [],
            giftProducts: [],
            saleProducts: [],
            keyGiftRemove: new Set(),
            reportRetail: {},
            reportVAT: {},
            reportCommon: {},
            cashPayment: 0,
            keyboardTaps: "always",
            saleProgramInfo: {},
            contractID: "",
            isCustomer: false,
            isVisibleVoucher: false,
            dataPhoneVoucher: [],
            isBlockUI: false,
            isVerify: false,
            isPrintDosage: true,
            isPrintBatchNo: false,
            batchModal: {
                visible: false,
                value: [],
                name: '',
                totalQuantity: 0,
                allowToChangeLess: false
            },
            batchesBySOD: {},
            dataMoneys: [],
            visiblePinCode: false,
            shouldCallLoyalty: false,
            visibleOTPCusVoucher: false,
            phoneCertifyPMH: '',
            isVisibleModalCollection: false,
            isVisibleAlert: false,
            loyaltyTransaction: {
                status: 0,
                amount: 0,
                createdAt: 0,
                message: ''
            },
            statusOutPut: "",
            isVisibleModalMsale: false,
            isLoading: false,
            isFocus: false,
            isVisibleCollectionTransaction: false,
            contentCollectionTransaction: "",
            isCalled: true,
            transactionIcon: "",
            transactionColor: "",
            dataBankInfo: [],
            bankSelected: {},
            statusTransferPaymentSuccess: {
                type: "INIT",
                message: "GIAO DỊCH ĐANG ĐỢI XỬ LÝ..."
            },

            waitingForPayment: {
                visible: false,
                value: 0,
                message: "",
                buttons: [],
                approvalCode: ""
            },

        };
        this.isModifyPaymentMoney = false;
        this.isFirst = true;
        this.dataPrint = {};
        this.oldImei = "";
        this.shouldOriginUpdate = false;
        this.dataCollection = {};
        this.isMatchOutputStore = false;
        this.ebillQTV = new Set();
        this.isCheckTransfer = false;
        this.transferCreateOV = React.createRef(false)
        this.paymentTransferSheetRef = React.createRef(null)
        this.intervalPaymentId = React.createRef(-1);
        this.dataCardSelected = React.createRef(null);
        this.extraDataGenQR = React.createRef(null);
        this.OTPSheetRef = React.createRef(null)
        this.dataTempCreateQR = React.createRef(null);
    }

    componentDidMount() {
        //addEventListener "addEventListener"
        BackHandler.addEventListener('addEventListener', this.onBackButtonPressed);
        // Linking.addEventListener('url', this.handleOpenURL);
        this.handleGetmoneycardlist();
        this.handleGetStatus();
    }

    onBackButtonPressed = () => {
        return true;
    }

    // EventListener
    handleOpenURL = (event) => {
        let url = event.url
        if (url) {
            let saleOrderID = url.split("=").pop();
            if (this.props.so == saleOrderID) {
                let newDataPayAndOutput = helper.deepCopy(this.props.dataPayAndOutput)
                this.modifyPaymentMoney(newDataPayAndOutput);
            }
            else {
                this.hideUIIndicator();
            }
        }
    }

    componentWillUnmount() {

        // this.backHandler.remove();
        BackHandler.removeEventListener('hardwareBackPress', this.onBackButtonPressed);
        // Linking.removeEventListener('url', this.handleOpenURL);
    }

    componentDidUpdate(preProps, preState) {
        const { shouldOriginUpdate } = this.props.route.params ?? { shouldOriginUpdate: false };
        this.shouldOriginUpdate = shouldOriginUpdate
        if (preProps.defaultReport !== this.props.defaultReport) {
            const { defaultReport } = this.props;
            this.setState({
                reportRetail: defaultReport.retail,
                reportVAT: defaultReport.vat,
                reportCommon: defaultReport.common
            })
        }
        if (this.isFirst && ((preProps.dataSaleOrder !== this.props.dataSaleOrder) || this.shouldOriginUpdate)) {
            const { dataSaleOrder, actionLoyalty, userInfo: { storeID } } = this.props;
            if (!helper.IsEmptyObject(dataSaleOrder)) {
                this.isFirst = false;
                this.shouldOriginUpdate = false;
                const {
                    listMainProduct = [],
                    listGiftPromotion = [],
                    listSalePromotion = [],
                    AllowOutput,
                    AllowPayCash,
                    SaleOrderSaleProgramInfo,
                    Debt,
                    VoucherConcernType,
                    OutputStoreID
                } = dataSaleOrder;
                this.isMatchOutputStore = `${storeID}` == `${OutputStoreID}`;
                const { PartnerInstallmentID, TermLoanList } = SaleOrderSaleProgramInfo ?? {};
                const isNewFollowPartner = helper.isNewFollowPartner(PartnerInstallmentID);

                const saleProgramInfo = SaleOrderSaleProgramInfo || {};
                if (isNewFollowPartner) {
                    saleProgramInfo.TermLoan = TermLoanList?.[0]?.TermLoanNumber || 0
                }
                const keyGiftRemove = new Set();
                listGiftPromotion.forEach(ele => {
                    const {
                        SaleOrderDetailID,
                        IsOutput,
                        IsRequestIMEI,
                        IMEIByLoad
                    } = ele;
                    // const isImeiLoad = IsRequestIMEI && !!IMEIByLoad;
                    if (!IsOutput) {
                        keyGiftRemove.add(SaleOrderDetailID);
                    }
                });

                // Khác Thu hộ Viettel
                VoucherConcernType !== 6 &&
                    handleApiGetPointsByPayment({
                        PaymentType: PAYMENT_TYPE.CASH,
                        PaymentValue: Debt,
                        apiAction: actionLoyalty.getPointsByPayment
                    });
                this.setState({
                    shouldCallLoyalty: true,
                    mainProducts: listMainProduct,
                    giftProducts: listGiftPromotion,
                    saleProducts: listSalePromotion,
                    isOutput: AllowOutput && this.isMatchOutputStore,
                    isPayCash: AllowPayCash,
                    contractID: saleProgramInfo.ContractID || "",
                    saleProgramInfo: {
                        ContractID: saleProgramInfo.ContractID || "",
                        customerIDCard: saleProgramInfo.customerIDCard || "",
                        PGProcessUserID: saleProgramInfo.PGProcessUserID || "",
                        PGProcessUserName: saleProgramInfo.PGProcessUserName || "",
                        packageRates: saleProgramInfo.packageRates || "",
                        TotalPrePaid: saleProgramInfo.TotalPrePaid || 0,
                        TermLoan: saleProgramInfo.TermLoan || 0,
                        PaymentAmountMonthly: saleProgramInfo.PaymentAmountMonthly || 0,
                        CustomerName: saleProgramInfo.CustomerName || "",
                        CustomerAddress: saleProgramInfo.CustomerAddress || "",
                        customerPhone: saleProgramInfo.customerPhone || "",
                        customerIDCard: saleProgramInfo.customerIDCard || ""
                    },
                    keyGiftRemove
                });
            }
        }

    }

    getSaleOrderPayment = () => {
        const {
            dataSO: { SaleOrderID },
            actionSaleOrder
        } = this.props;
        actionSaleOrder.getSaleOrderPayment(SaleOrderID);
    }

    getReportPrinter = () => {
        const {
            dataSO: { SaleOrderTypeID },
            actionSaleOrder,
        } = this.props;
        actionSaleOrder.getReportPrinterSocket(SaleOrderTypeID);
    }

    getDataQRTransaction = () => {
        const { dataSO: {
            SaleOrderTypeID
        } } = this.props;
        this.props.actionSaleOrder.getDataQRTransaction(SaleOrderTypeID);
    }

    getDataSCTransaction = () => {
        const { dataSO: {
            SaleOrderTypeID
        } } = this.props;
        this.props.actionSaleOrder.getDataSCTransaction(SaleOrderTypeID);
    }

    handleGetmoneycardlist = () => {
        this.props.actionCard.getMoneycardlistReponse().then(dataMoneys => {
            this.setState({ dataMoneys })
        }).catch(error => {
            Alert.alert(translate('common.notification_uppercase'), error.msgError, [
                {
                    text: "OK",
                    style: "default",
                }
            ]);
        });
    }
    handleGetStatus = () => {
        const {
            dataSaleOrder: { SaleOrderID, OrderTypeID, CustomerPhone },
            userInfo: { storeID, moduleID, languageID },
            saleScenarioTypeID
        } = this.props;
        const isSOPre = `,${this.props.PM_PreOrder_SaleOrderTypeList},`.includes(`,${OrderTypeID?.toString()},`);
        if (isSOPre) {
            const body = {
                "loginStoreId": storeID,
                "languageID": languageID,
                "moduleID": moduleID,
                "saleScenarioTypeID": saleScenarioTypeID,
                "orderTypeID": OrderTypeID,
                "saleOrderID": SaleOrderID.trim(),
                "customerPhone": CustomerPhone
            }
            actionSaleOrderCreator.getInvitationEvent(body).then(data => {
                let status = getStatus(data.STATUS)
                this.setState({ statusOutPut: status })
            }).catch(error => {
                console.log("🚀 ~ file: index.js:280 ~ SaleOrderPayment ~ actionSaleOrderCreator.getInvitationEvent ~ error:", error)
            });
        }
    }

    handleGetLoyaltyPoint = (dataLoyalty) => {
        const { dataSaleOrder, actionSaleOrder } = this.props
        const dataModifySaleOrder = { ...dataSaleOrder };
        const {
            DisableTextLoyalty,
            DefaultPointUse,
            MaxPointApply,
            AvailablePoint,
            IsAllowParticipationLoyalty,
            IsCustomerCodeByCompany,
            CustomerCode
        } = dataLoyalty
        dataModifySaleOrder.DisableTextLoyalty = DisableTextLoyalty
        dataModifySaleOrder.DefaultPointUse = DefaultPointUse
        dataModifySaleOrder.MaxPointApply = MaxPointApply
        dataModifySaleOrder.AvailablePoint = AvailablePoint
        dataModifySaleOrder.IsAllowParticipationLoyalty = IsAllowParticipationLoyalty
        dataModifySaleOrder.IsCustomerCodeByCompany = IsCustomerCodeByCompany
        dataModifySaleOrder.CustomerCode = CustomerCode
        actionSaleOrder.stop_modify_saleorder_payment(dataModifySaleOrder);
    }

    handleApiLoadBatchNo = (product) => {
        const batches = this.state.batchesBySOD[product.SaleOrderDetailID];
        if (batches) {
            this.setState({
                batchModal: {
                    visible: true,
                    value: batches,
                    name: product.ProductName,
                    id: product.SaleOrderDetailID,
                    totalQuantity: product.Quantity,
                    allowToChangeLess: product.cus_AllowChageQuantity
                }
            });
        } else {
            showBlockUI();
            this.props.actionSaleOrder
                .loadBatchNoBySO({
                    unSaleOrder: this.props.dataSaleOrder,
                    saleOrderDetailID: product.SaleOrderDetailID,
                    CheckOutput: true,
                    CheckIncome: true
                })
                .then((newBatches) => {
                    hideBlockUI();
                    this.setState({
                        batchModal: {
                            visible: true,
                            value: newBatches,
                            name: product.ProductName,
                            id: product.SaleOrderDetailID,
                            totalQuantity: product.Quantity,
                            allowToChangeLess: product.cus_AllowChageQuantity
                        },
                        batchesBySOD: {
                            ...this.state.batchesBySOD,
                            [product.SaleOrderDetailID]: newBatches
                        }
                    });
                })
                .catch((msgError) => {
                    hideBlockUI();
                    Alert.alert(
                        translate('common.notification_uppercase'),
                        msgError,
                        [
                            { text: 'OK' }
                        ]
                    );
                });
        }
    };

    handleBankTransfer = (amount) => {
        showBlockUI()
        const {
            dataSaleOrder: { SaleOrderID },
            actionSaleOrder
        } = this.props;
        actionSaleOrder.getBankInfo({
            saleOrderID: SaleOrderID, paymentAmount: amount
        }).then((result) => {
            hideBlockUI()
            this.setState({
                dataBankInfo: result, bankSelected: result[0], statusTransferPaymentSuccess: {
                    type: "INIT",
                    message: "GIAO DỊCH ĐANG ĐỢI XỬ LÝ..."
                }
            }, () => {
                this.paymentTransferSheetRef.current?.present()
                if (this.state.bankSelected?.QRCodeData?.length > 0) {
                    this.handleIntervalPayment()
                }
            })
        }).catch((error) => {
            Alert.alert(
                translate('common.notification_uppercase'),
                error.msgError,
                [
                    {
                        text: translate('saleExpress.retry'),
                        style: 'cancel',
                        onPress: () => this.handleBankTransfer(amount)
                    },
                    {
                        text: translate('common.btn_skip'),
                        style: 'cancel',
                        onPress: () => hideBlockUI()
                    }

                ]
            );
        })
    }

    handleChangeBank = (bank) => {
        clearInterval(this.intervalPaymentId.current);
        this.setState({ bankSelected: bank }, () => {
            this.handleIntervalPayment()
        })
    }
    handleIntervalPayment = () => {
        if (!helper.configIntervalPayment()) return
        this.intervalPaymentId.current = setInterval(this.intervalPaymentFunction, 5000);
        setTimeout(() => {
            clearInterval(this.intervalPaymentId.current);
        }, 3000000);
    }
    intervalPaymentFunction = async () => {
        const {
            dataSaleOrder: { SaleOrderID }
        } = this.props;
        const { bankSelected: { CreatedDate } } = this.state
        actionSaleOrderCreator.getTransactionTransfer(SaleOrderID, CreatedDate).then(() => {
            this.setState({
                statusTransferPaymentSuccess: {
                    type: "SUCCESS",
                    message: "GIAO DỊCH ĐÃ THỰC HIỆN THÀNH CÔNG"
                }
            })
            clearInterval(this.intervalPaymentId.current);

        }).catch((msgError) => {
            this.setState({
                statusTransferPaymentSuccess: {
                    type: "ERROR",
                    message: msgError
                }
            })
            clearInterval(this.intervalPaymentId.current);
        })
    }

    getNavigationScreen = (IsShowMessageInfo) => {
        const { actionBankAirtimeService, actionCollection } = this.props;
        const screenMap = {
            "THUHO": "PayBillAirtimeService",
            "TAICHINH": () => {
                const data = {
                    catalogID: 12,
                    serviceGroupID: 37,
                };
                actionBankAirtimeService.clear_data_customer();
                actionBankAirtimeService.getServiceList(data);
                return "BankAirtimeService";
            },
            "BAOHIEM": () => {
                let ServiceCatalogID = 3;
                actionCollection.getServiceGroupListCatalog(ServiceCatalogID)
                return "CatalogCollection"
            }
        };

        const result = screenMap[IsShowMessageInfo];

        if (!result) {
            return null;
        }
        return typeof result === 'function' ? result() : result;
    };


    getNavigationScreenManagerment = (IsShowMessageInfo) => {
        const screenMap = {
            "BAOHIEM": "HistorySellInsurance",
            "TAICHINH": "HistorySell",
            "THUHO": "HistorySell"
        };

        const result = screenMap[IsShowMessageInfo];

        if (!result) {
            return null;
        }
        return result;
    };

    render() {
        const {
            isOutput,
            isPayCash,
            mainProducts,
            saleProducts,
            giftProducts,
            keyGiftRemove,
            reportRetail,
            reportVAT,
            reportCommon,
            cashPayment,
            keyboardTaps,
            saleProgramInfo,
            contractID,
            isCustomer,
            isVisibleVoucher,
            dataPhoneVoucher,
            isBlockUI,
            isVerify,
            isPrintDosage,
            batchModal,
            isPrintBatchNo,
            dataMoneys,
            visiblePinCode,
            visibleOTPCusVoucher,
            phoneCertifyPMH,
            isVisibleModalCollection,
            isVisibleAlert,
            loyaltyTransaction,
            statusOutPut,
            isVisibleModalMsale,
            isLoading,
            transactionIcon,
            transactionColor
        } = this.state;
        const {
            dataSaleOrder,
            printerRetail,
            printerVAT,
            printerCommon,
            stateSaleOrder,
            statePrinter,
            actionSaleOrder,
            dataQRTransaction: {
                dataQRType,
                dataTransaction
            },
            stateQRTransaction,
            dataSCTransaction,
            stateSCTransaction,
            userInfo: { brandID, storeID, provinceID },
            loyaltyPercentBonus,
            navigation,
        } = this.props;
        const {
            IsInstallment,
            GiftVoucherIssueRequests,
            TotalGiftVoucherAmount,
            ApplyMoneyCardDetails,
            MoneyCardList,
            MoneyCard: TotalMoneyCard,
            LoyaltyInfo,
            AvailablePoint,
            MaxPointApply,
            PointRefund,
            TotalPointLoyalty,
            CashVND,
            AllowOutput,
            AllowPayCash,
            PayableAmount,
            ErrorMessageOutput,
            ErrorMessagePayCash,
            SaleOrderSaleProgramInfo,
            SaleOrderID,
            EPOSTransactionID,
            CustomerPhone,
            MoneyBank,
            TotalRemainNotCashVND,
            IsCheckExistCustomerInStall,
            DefaultPointUse,
            DisableTextLoyalty,
            cus_IsAddPromotionSO,
            CartID,
            IsAllowParticipationLoyalty,
            cus_IsPrintDosageContent,
            IsSOAnKhang,
            TotalGiftVoucherAmountPartner,
            PaymentTransactionPartners,
            VoucherConcernType,
            PaymentMethod,
            SaleProgramID,
            TotalPrePaid,
            TotalRemain,
            CustomerName
        } = dataSaleOrder;
        const {
            IsAllowBankTransfer,
            IsAllowCash,
            IsAllowGiftVoucher,
            IsAllowGiftVoucherPartner,
            IsAllowLoyaltyPoint,
            IsAllowQR,
            IsAllowMoneyBankTransfer
        } = PaymentMethod ?? {}
        const IsAutoCreateEP = helper.IsNonEmptyString(EPOSTransactionID);
        const dataVoucher = GiftVoucherIssueRequests || [];
        const dataVoucherGotIt = PaymentTransactionPartners ?? [];
        const dataCard = ApplyMoneyCardDetails || [];
        const dataPOS = MoneyCardList || [];
        const dataLoyalty = LoyaltyInfo || {};
        const isVisibleMain = helper.IsNonEmptyArray(mainProducts);
        const isVisibleSale = helper.IsNonEmptyArray(saleProducts);
        const isVisibleGift = helper.IsNonEmptyArray(giftProducts);
        const isVisible = (AllowOutput || AllowPayCash);
        const keyImeiProduct = getKeyImeiProduct(mainProducts, saleProducts, giftProducts);
        const isVisibleCustomer = (isOutput && IsCheckExistCustomerInStall);
        const isLoyalty = IsAllowParticipationLoyalty;
        const isPhoneVoucher = isPayCash && helper.IsNonEmptyString(CustomerPhone);
        const cartID = helper.IsNonEmptyString(CartID) ? CartID.trim() : "";
        const shouldPrintDosage = cus_IsPrintDosageContent && (`${brandID}` == '8');
        const isApplySC = true;
        const { PartnerInstallmentID } = SaleOrderSaleProgramInfo ?? {};
        const isSmartPosIntsallment = PartnerInstallmentID == 23;
        const isNewFollowPartner = helper.isNewFollowPartner(PartnerInstallmentID);

        return (
            <SafeAreaView style={{
                flex: 1
            }}>
                <BottomSheetModalProvider>
                    <BaseLoading
                        isLoading={stateSaleOrder.isFetching}
                        isEmpty={stateSaleOrder.isEmpty}
                        textLoadingError={stateSaleOrder.description}
                        isError={stateSaleOrder.isError}
                        onPressTryAgains={this.getSaleOrderPayment}
                        content={
                            <KeyboardAwareScrollView
                                style={{
                                    flex: 1
                                }}
                                enableResetScrollToCoords={false}
                                keyboardShouldPersistTaps={keyboardTaps}
                                bounces={false}
                                overScrollMode="always"
                                showsHorizontalScrollIndicator={false}
                                showsVerticalScrollIndicator={false}
                                extraScrollHeight={100}
                            >
                                <View style={{
                                    flex: 1,
                                }}>
                                    <CustomerInfo info={dataSaleOrder} />
                                    {
                                        IsInstallment && (
                                            isNewFollowPartner ? (
                                                <SaleProgramInfo
                                                    saleProgramInfo={saleProgramInfo}
                                                    saleOrderSaleProgramInfo={SaleOrderSaleProgramInfo}
                                                    updateData={(data) => this.setState({ saleProgramInfo: data })}
                                                />
                                            ) : (
                                                <ContractInfo
                                                    info={SaleOrderSaleProgramInfo}
                                                    data={saleProgramInfo}
                                                    editable={!IsAutoCreateEP && !isSmartPosIntsallment}
                                                    onFocus={() => this.setState({ keyboardTaps: "never" })}
                                                    onBlur={() => this.setState({ keyboardTaps: "always" })}
                                                    contractID={contractID}
                                                    onChange={(text) => this.setState({ contractID: text })}
                                                    onClear={() => this.setState({ contractID: "" })}
                                                    getData={this.getContractInfo}
                                                    totalAmount={dataSaleOrder.TotalAmount}
                                                    updateData={(data) => this.setState({ saleProgramInfo: data })}
                                                />
                                            )
                                        )
                                    }
                                    {
                                        <View style={{ flexDirection: "row", justifyContent: "space-between", backgroundColor: COLORS.txtFFFFFF }}>
                                            <CheckBox
                                                isCheck={isOutput}
                                                onCheck={this.onChangOutput}
                                                disabled={!AllowOutput || !this.isMatchOutputStore}
                                                title={translate('saleOrderPayment.output')}
                                                message={ErrorMessageOutput}
                                                width={!!statusOutPut ? "30%" : constants.width}
                                            />
                                            {
                                                !!statusOutPut && (
                                                    <View style={{ alignItems: "center", justifyContent: "center", }}>
                                                        <MyText
                                                            style={{
                                                                fontWeight: '700',
                                                                marginRight: 10,
                                                                // fontSize: 11
                                                            }}
                                                            selectable={false}>
                                                            Trạng thái:{"  "}
                                                            <MyText
                                                                style={{
                                                                    color: "#EA5C2B",
                                                                    fontWeight: 'bold',
                                                                }}>
                                                                {statusOutPut}
                                                            </MyText>
                                                        </MyText>
                                                    </View>
                                                )
                                            }

                                        </View>
                                    }

                                    {
                                        isOutput &&
                                        <>
                                            {
                                                isVisibleMain &&
                                                <ProductOrder
                                                    title={translate('saleOrderPayment.products_list')}
                                                    data={mainProducts}
                                                    renderItem={({ item, index }) => (
                                                        <MainProduct
                                                            product={item}
                                                            updateImeiProduct={(value) => {
                                                                const newMainProducts = helper.deepCopy(mainProducts);
                                                                newMainProducts[index].IMEI = value;
                                                                this.setState({ mainProducts: newMainProducts });
                                                            }}
                                                            updateSerialProduct={(value) => {
                                                                const newMainProducts = helper.deepCopy(mainProducts);
                                                                newMainProducts[index] = value;
                                                                this.setState({ mainProducts: newMainProducts });
                                                            }}
                                                            updateImeiSticker={(value) => {
                                                                const newMainProducts = helper.deepCopy(mainProducts);
                                                                newMainProducts[index].InputIMEIList = value;
                                                                this.setState({ mainProducts: newMainProducts });
                                                            }}
                                                            checkImei={actionSaleOrder.checkOutputImei}
                                                            checkSerial={actionSaleOrder.checkOutputSerial}
                                                            keyImeiProduct={keyImeiProduct}
                                                            onPressLotDate={() => {
                                                                this.handleApiLoadBatchNo(item);
                                                            }}
                                                        />
                                                    )}
                                                />
                                            }
                                            {
                                                isVisibleSale &&
                                                <ProductOrder
                                                    title={translate('saleOrderPayment.attached_sale_list')}
                                                    data={saleProducts}
                                                    renderItem={({ item, index }) => (
                                                        <MainProduct
                                                            product={item}
                                                            updateImeiProduct={(value) => {
                                                                const newSaleProducts = helper.deepCopy(saleProducts);
                                                                newSaleProducts[index].IMEI = value;
                                                                this.setState({ saleProducts: newSaleProducts });
                                                            }}
                                                            updateSerialProduct={(value) => {
                                                                const newSaleProducts = helper.deepCopy(saleProducts);
                                                                newSaleProducts[index] = value;
                                                                this.setState({ saleProducts: newSaleProducts });
                                                            }}
                                                            updateImeiSticker={(value) => {
                                                                const newSaleProducts = helper.deepCopy(saleProducts);
                                                                newSaleProducts[index].InputIMEIList = value;
                                                                this.setState({ saleProducts: newSaleProducts });
                                                            }}
                                                            checkImei={actionSaleOrder.checkOutputImei}
                                                            checkSerial={actionSaleOrder.checkOutputSerial}
                                                            keyImeiProduct={keyImeiProduct}
                                                            onPressLotDate={() => {
                                                                this.handleApiLoadBatchNo(item);
                                                            }}
                                                        />
                                                    )}
                                                />
                                            }
                                            {
                                                isVisibleGift &&
                                                <ProductOrder
                                                    title={translate('saleOrderPayment.promotion_list')}
                                                    data={giftProducts}
                                                    renderItem={({ item, index }) => (
                                                        <GiftProduct
                                                            product={item}
                                                            updateImeiProduct={(value) => {
                                                                const newGiftProducts = helper.deepCopy(giftProducts);
                                                                newGiftProducts[index].IMEI = value;
                                                                this.setState({ giftProducts: newGiftProducts });
                                                            }}
                                                            updateSerialProduct={(value) => {
                                                                const newGiftProducts = helper.deepCopy(giftProducts);
                                                                newGiftProducts[index] = value;
                                                                this.setState({ giftProducts: newGiftProducts });
                                                            }}
                                                            updateImeiSticker={(value) => {
                                                                const newGiftProducts = helper.deepCopy(giftProducts);
                                                                newGiftProducts[index].InputIMEIList = value;
                                                                this.setState({ giftProducts: newGiftProducts });
                                                            }}
                                                            keyGiftRemove={keyGiftRemove}
                                                            updateKeyRemove={(keyRemove) => {
                                                                this.setState({ keyGiftRemove: keyRemove });
                                                            }}
                                                            checkImei={actionSaleOrder.checkOutputImei}
                                                            checkSerial={actionSaleOrder.checkOutputSerial}
                                                            keyImeiProduct={keyImeiProduct}
                                                            onPressLotDate={() => {
                                                                this.handleApiLoadBatchNo(item);
                                                            }}
                                                        />
                                                    )}
                                                />
                                            }
                                            <BatchInfo
                                                data={batchModal.value}
                                                isShowModal={batchModal.visible}
                                                onClose={() =>
                                                    this.setState({
                                                        batchModal: {
                                                            value: [],
                                                            visible: false,
                                                            id: "",
                                                            totalQuantity: 0,
                                                            allowToChangeLess: false
                                                        }
                                                    })
                                                }
                                                editable
                                                productName={batchModal.name}
                                                totalQuantity={batchModal.totalQuantity}
                                                allowToChangeLess={batchModal.allowToChangeLess}
                                                onSubmit={({ batch }) => {
                                                    this.setState({
                                                        batchesBySOD: {
                                                            ...this.state.batchesBySOD,
                                                            [batchModal.id]: batch
                                                        }
                                                    });
                                                }}
                                            />
                                        </>
                                    }
                                    <CheckBox
                                        isCheck={isPayCash}
                                        onCheck={this.onChangPayCash}
                                        disabled={!AllowPayCash}
                                        title={translate('saleOrderPayment.charge')}
                                        message={ErrorMessagePayCash}
                                        onGetVoucher={this.getDataPhoneVoucher}
                                        isPhoneVoucher={isPhoneVoucher}
                                    />
                                    {
                                        isPayCash &&
                                        <>
                                            <AmountInfo info={dataSaleOrder} />
                                            {
                                                IsAllowGiftVoucher && <PaymentVoucher
                                                    title={translate('saleOrderPayment.customer_voucher')}
                                                    data={dataVoucher}
                                                    renderItem={({ item, index }) => (
                                                        <Voucher
                                                            key={`PaymentVoucher ${index}`}
                                                            info={item}
                                                            onRemove={this.removeGiftVoucher(dataVoucher, index)}
                                                        />
                                                    )}
                                                    applyVoucher={this.applyGiftVoucher}
                                                    total={TotalGiftVoucherAmount}
                                                />
                                            }
                                            <OTPCustomerVoucher
                                                visible={visibleOTPCusVoucher}
                                                numberPhone={phoneCertifyPMH}
                                                onClose={() => this.closeModalVerify()}
                                                onSubmit={(otpCode, GiftVoucherCertifyType) => this.onCheckOTP(otpCode, GiftVoucherCertifyType)}
                                            />

                                           {/* {
                                            (DefaultPointUse > 0) && isLoyalty && */}
                                            {
                                                IsAllowLoyaltyPoint &&
                                                <PaymentLoyalty
                                                    shouldCallLoyalty={this.state.shouldCallLoyalty}
                                                    onDataLoyaltyChange={this.handleGetLoyaltyPoint}
                                                    title={translate('saleOrderPayment.membership_point')}
                                                    total={TotalPointLoyalty}
                                                    data={dataLoyalty}
                                                    availablePoint={AvailablePoint}
                                                    maxUsePoint={MaxPointApply}
                                                    refundPoint={PointRefund}
                                                    onUpdateDataLoyalty={this.updateDataLoyalty}
                                                    customerPhone={CustomerPhone}
                                                    maxPayment={TotalRemainNotCashVND}
                                                    brandID={brandID}
                                                    defaultPointUse={DefaultPointUse}
                                                    isDefault={helper.IsNonEmptyString(DisableTextLoyalty)}
                                                    cartID={cartID}
                                                />
                                            }
                                            {/* } */}
                                            {
                                                IsAllowMoneyBankTransfer && (
                                                    <PaymentBankTransfer
                                                        saleOrderID={SaleOrderID}
                                                        onReloadSOPayment={() => {
                                                            showBlockUI()
                                                            actionSaleOrder.ReLoadSaleOrderPayment(SaleOrderID).then((reponse) => {
                                                                actionSaleOrder.setSaleOrderPayment(reponse)
                                                                const { VoucherConcernType, TotalRemain } = reponse;
                                                                if (VoucherConcernType == 6 && TotalRemain == 0) {
                                                                    this.setState({ isVisibleModalCollection: true });
                                                                }
                                                                hideBlockUI()
                                                            }).catch(({ msgError, errorType }) => {
                                                                if (errorType == 1106) {
                                                                    hideBlockUI()
                                                                    this.setState({ isVisibleModalCollection: true });
                                                                    this.transferCreateOV.current = true
                                                                }
                                                                else {
                                                                    Alert.alert('', msgError, [
                                                                        {
                                                                            text: 'OK',
                                                                            onPress: hideBlockUI
                                                                        }
                                                                    ]);
                                                                }
                                                            });
                                                            this.isCheckTransfer = true;
                                                        }}
                                                        payableAmount={TotalRemain}
                                                        isCheck={this.isCheckTransfer || helper.IsNonEmptyArray(this.state.dataBankInfo)}
                                                        handleBankTransfer={this.handleBankTransfer}
                                                    />
                                                )
                                            }
                                            {
                                                IsAllowBankTransfer &&
                                                <BaseLoading
                                                    isLoading={stateSCTransaction.isFetching}
                                                    isEmpty={stateSCTransaction.isEmpty}
                                                    textLoadingError={stateSCTransaction.description}
                                                    isError={stateSCTransaction.isError}
                                                    onPressTryAgains={this.getDataSCTransaction}
                                                    content={
                                                        <PaymentCard
                                                            title={translate('saleOrderPayment.customer_pay_card')}
                                                            dataMoney={dataCard}
                                                            renderItem={({ item, index }) => (
                                                                <MoneyCard
                                                                    key={`PaymentCard ${index}`}
                                                                    info={item}
                                                                    onRemove={this.removeMoneyCard(dataCard, index)}
                                                                    isApplySC={isApplySC}
                                                                />
                                                            )}
                                                            dataPOS={dataMoneys}
                                                            applyMoney={(cardSelected) => {
                                                                this.dataCardSelected.current = cardSelected
                                                                this.handleApplyMoneyCard(dataCard, cardSelected)
                                                            }}
                                                            total={TotalMoneyCard}
                                                            maxPayment={TotalRemainNotCashVND}
                                                            onPayMpos={this.getTokenMpos}
                                                            dataTransaction={dataSCTransaction}
                                                            onApplyTransaction={this.applyTransactionSC}
                                                            updateTransaction={(data) => {
                                                                this.props.actionSaleOrder.updateDataTransactionSC(data);
                                                            }}
                                                            createSC={this.createSC}
                                                            onGetSC={this.getSCInfo}
                                                            actionSaleOrder={actionSaleOrder}
                                                            dataSO={dataSaleOrder}
                                                            isApplySC={isApplySC}
                                                        />
                                                    }
                                                />
                                            }
                                            {
                                                !cus_IsAddPromotionSO && IsAllowQR &&
                                                <BaseLoading
                                                    isLoading={stateQRTransaction.isFetching}
                                                    isEmpty={stateQRTransaction.isEmpty}
                                                    textLoadingError={stateQRTransaction.description}
                                                    isError={stateQRTransaction.isError}
                                                    onPressTryAgains={this.getDataQRTransaction}
                                                    content={
                                                        <PaymentQRCode
                                                            title={translate('saleOrderPayment.customer_pay_QR')}
                                                            total={MoneyBank}
                                                            maxPayment={TotalRemainNotCashVND}
                                                            dataSO={dataSaleOrder}
                                                            dataQRType={dataQRType}
                                                            dataTransaction={dataTransaction}
                                                            onApplyTransaction={this.applyTransaction}
                                                            updateTransaction={(data) => {
                                                                this.props.actionSaleOrder.updateDataTransaction(data);
                                                            }}
                                                            createQR={(data) => {
                                                                this.extraDataGenQR.current = null
                                                                this.createQR(data)
                                                            }}
                                                            onGetQR={this.getQRInfo}
                                                            actionSaleOrder={actionSaleOrder}
                                                        />
                                                    }
                                                />
                                            }
                                            {
                                                IsAllowGiftVoucherPartner && <PaymentVoucherPartner
                                                    title={"Phiếu mua hàng đối tác"}
                                                    applyVoucher={this.applyGiftVoucher}
                                                    total={TotalGiftVoucherAmountPartner}
                                                    data={dataVoucherGotIt}
                                                    renderItem={({ item, index }) => (
                                                        <VoucherPartner
                                                            key={`PaymentVoucherGotIt ${index}`}
                                                            info={item}
                                                            onRemove={this.removeVoucherGotIt(dataVoucherGotIt, item)}
                                                            isGotIt
                                                        />
                                                    )}
                                                />
                                            }
                                            {
                                                IsAllowCash && <DebtRefundInfo
                                                    info={dataSaleOrder}
                                                    cash={cashPayment}
                                                    onChange={(value) => {
                                                        this.setState({ cashPayment: value });
                                                    }}
                                                    onFocus={this.setKeyboardTaps}
                                                    onBlur={this.onBlurInputCash(dataSaleOrder, cashPayment)}
                                                    autoFocus={false}
                                                />
                                            }
                                        </>
                                    }
                                    <PrintReport
                                        title={translate('saleOrderPayment.choose_printer')}
                                        statePrinter={statePrinter}
                                        onTryAgains={this.getReportPrinter}
                                        dataRetail={printerRetail}
                                        dataVAT={printerVAT}
                                        dataCommon={printerCommon}
                                        renderItemRetail={({ item, index }) => (<Report
                                            key={`ReportRetail`}
                                            info={item}
                                            report={reportRetail}
                                            onCheck={() => {
                                                storageHelper.setDefaultPrinter(item.STOREPRINTERID, 0);
                                                this.setState({ reportRetail: item });
                                            }}
                                        />)}
                                        renderItemVAT={({ item, index }) => (<Report
                                            key={`ReportVAT`}
                                            info={item}
                                            report={reportVAT}
                                            onCheck={() => {
                                                storageHelper.setDefaultPrinter(item.STOREPRINTERID, 1);
                                                this.setState({ reportVAT: item });
                                            }}
                                        />)}
                                        renderItemCommon={({ item, index }) => (<Report
                                            key={`ReportCommon`}
                                            info={item}
                                            report={reportCommon}
                                            onCheck={() => {
                                                storageHelper.setDefaultPrinter(item.STOREPRINTERID, 2);
                                                this.setState({ reportCommon: item });
                                            }}
                                        />)}
                                    />
                                    {
                                        shouldPrintDosage && (
                                            <CheckPrint
                                                isCheck={isPrintDosage}
                                                onCheck={(value) => {
                                                    this.setState({
                                                        isPrintDosage: value
                                                    });
                                                }}
                                                title="In liều dùng"
                                            />
                                        )
                                    }
                                    {
                                        IsSOAnKhang && (
                                            <CheckPrint
                                                isCheck={isPrintBatchNo}
                                                onCheck={(value) => {
                                                    this.setState({
                                                        isPrintBatchNo: value
                                                    });
                                                }}
                                                title="In chi tiết Lô date"
                                            />
                                        )
                                    }
                                    {
                                        isVisibleCustomer &&
                                        <CheckCustomer
                                            isCheck={isCustomer}
                                            onCheck={(value) => {
                                                this.setState({ isCustomer: value });
                                            }}
                                            title={translate('saleOrderPayment.customer_at_store')}
                                            note={translate('saleOrderPayment.check_here')}
                                        />
                                    }
                                    {
                                        isVisible &&
                                        <ButtonCreate
                                            disabled={!isOutput && !isPayCash}
                                            onPress={() => this.onCheckSaleOrder(VoucherConcernType)}
                                        />
                                    }
                                    <ModalPinCode
                                        visible={visiblePinCode}
                                        onClose={() => {
                                            this.setState({ visiblePinCode: false })
                                        }}
                                        onSubmit={(pinCode) => {
                                            if (pinCode.trim().length > 0) {
                                                if (pinCode.trim() === this.oldImei.trim()) {
                                                    Alert.alert('', "Mã pin code không hợp lệ.", [
                                                        {
                                                            text: 'OK',
                                                            style: 'default',
                                                        }
                                                    ]);
                                                }
                                                else {
                                                    this.setState({ visiblePinCode: false })
                                                    this.applyGiftVoucherMWG(pinCode)
                                                }

                                            }
                                            else {
                                                Alert.alert('', "Vui lòng nhập pin code.", [
                                                    {
                                                        text: 'OK',
                                                        style: 'default',
                                                    }
                                                ]);
                                            }
                                        }}
                                    />
                                </View >
                                {/* <PhoneVoucher
                                isVisible={isVisibleVoucher}
                                data={dataPhoneVoucher}
                                onClose={() => {
                                    this.setState({ isVisibleVoucher: false })
                                }}
                                onApply={this.applyEncryptVoucher}
                                isBlockUI={isBlockUI}
                                showBlock={this.showBlock}
                                hideBlock={this.hideBlock}
                                isVerify={isVerify}
                                setVerify={this.setVerify}
                                dataSaleOrder={dataSaleOrder}
                            /> */}
                                {
                                    isVisibleModalCollection && <CollectionStatusModal
                                        isVisible={isVisibleModalCollection}
                                        SaleOrderID={SaleOrderID}
                                        onClose={() => this.onNavigationCollectionManager()}
                                        onSuccess={() => this.handleOrderStatusSuccess()}
                                        handleIsNotServiceOrder={() => this.handleIsNotServiceOrder()}
                                        onPressOTP={() => this.handleNavigatorOTPAirtimeService(SaleOrderID)}
                                    />
                                }
                                <AlertMessage
                                    title="Chi tiết giao dịch"
                                    visible={isVisibleAlert}
                                    onClose={() => this.setState({ isVisibleAlert: false })}
                                    message={loyaltyTransaction.message}
                                    buttons={loyaltyTransaction.status !== TRANSACTION_STATUS.PROCESSING ? [] : [
                                        {
                                            key: 0,
                                            text:
                                                translate(keys.common.btn_notify_try_again),
                                            outline: true,
                                            onPress: () => {
                                                this.onCheckSaleOrder(VoucherConcernType)
                                                this.setState({ isVisibleAlert: false })
                                            }
                                        },
                                        {
                                            key: 1,
                                            text: 'OK',
                                            outline: false,
                                            onPress: () => this.setState({ isVisibleAlert: false })
                                        }
                                    ]}
                                >
                                    <Transaction
                                        name="Quà tặng VIP"
                                        status={loyaltyTransaction.status}
                                        amount={loyaltyTransaction.amount}
                                        time={loyaltyTransaction.createdAt}
                                    />
                                </AlertMessage>
                                {
                                    isVisibleModalMsale && <ConnectSimMobi
                                        isLoading={isLoading}
                                        isVisible={isVisibleModalMsale}
                                        hideModal={() => {
                                            this.setState({ isVisibleModalMsale: false })
                                            navigation.goBack();
                                        }}
                                        SaleOrderID={SaleOrderID}
                                        dataActionSIM={this.dataCreateVoucherOVCM.lstActionAfterCreateVoucher}
                                    />}
                                <AlertMessage
                                    title="Chi tiết giao dịch"
                                    visible={this.state.isVisibleCollectionTransaction}
                                    onClose={() => this.setState({ isVisibleCollectionTransaction: false, contentCollectionTransaction: "" })}
                                    message={""}
                                    isMaxHeight={true}
                                    buttons={[
                                        {
                                            key: 0,
                                            text: "Về quản lý giao dịch",
                                            outline: true,
                                            onPress: () => {
                                                const data = {
                                                    keyword: SaleOrderID,
                                                    fromDate: new Date(),
                                                    toDate: new Date(),
                                                    isDelete: false,
                                                    isCreate: true,
                                                }
                                                const IsShowMessageInfo = this.dataCreateVoucherOVCM?.SaleOrderBO?.extensionProperty?.IsShowMessageInfo;
                                                this.props.actionCollectionManager.getSearchCollectionManager(data)
                                                const screen = this.getNavigationScreenManagerment(IsShowMessageInfo);
                                                if (screen) {
                                                    this.props.navigation.navigate(screen, {
                                                        SaleOrderID
                                                    });
                                                    this.props.actionBankAirtimeService.clear_data_customer();
                                                    this.setState({ isVisibleCollectionTransaction: false, contentCollectionTransaction: "" })
                                                } else {
                                                    Alert.alert(
                                                        "Thông báo",
                                                        "Không tìm thấy màn hình điều hướng phù hợp.",
                                                        [
                                                            {
                                                                text: "OK",
                                                                onPress: () => {
                                                                    this.props.navigation.goBack();
                                                                    this.setState({ isVisibleCollectionTransaction: false, contentCollectionTransaction: "" })
                                                                }
                                                            },
                                                        ],
                                                    );

                                                }
                                            }
                                        },
                                        {
                                            key: 1,
                                            text: 'Tiếp tục thanh toán',
                                            outline: false,
                                            onPress: () => {
                                                const IsShowMessageInfo = this.dataCreateVoucherOVCM?.SaleOrderBO?.extensionProperty?.IsShowMessageInfo;
                                                const screen = this.getNavigationScreen(IsShowMessageInfo);
                                                if (screen) {
                                                    this.props.navigation.navigate(screen);
                                                    this.setState({ isVisibleCollectionTransaction: false, contentCollectionTransaction: "" })
                                                } else {
                                                    Alert.alert(
                                                        "Thông báo",
                                                        "Không tìm thấy màn hình điều hướng phù hợp.",
                                                        [
                                                            {
                                                                text: "OK",
                                                                onPress: () => {
                                                                    this.props.navigation.goBack();
                                                                    this.setState({ isVisibleCollectionTransaction: false, contentCollectionTransaction: "" })
                                                                }
                                                            },
                                                        ],
                                                    );

                                                }

                                            }
                                        },
                                        ...(this.dataCreateVoucherOVCM?.SaleOrderBO?.extensionProperty?.IsShowMessageInfo == "THUHO" && helper.isCheckQRCodeCollection(storeID)
                                            ? [{
                                                key: 2,
                                                text: 'Bắn QrCode',
                                                outline: true,
                                                onPress: () => {
                                                    const item = {
                                                        ServiceCatalogID: 4,
                                                        isVisibleScanQrCode: true
                                                    };
                                                    this.setState(
                                                        {
                                                            isVisibleCollectionTransaction: false,
                                                            contentCollectionTransaction: ""
                                                        },
                                                        () => {
                                                            setTimeout(() => {
                                                                this.props.navigation.navigate("CatalogCollection", {
                                                                    item
                                                                });
                                                            }, 300);
                                                        }
                                                    );
                                                }
                                            }]
                                            : [])
                                    ]}
                                    style={{ flexDirection: 'column' }}
                                >
                                    <View style={{
                                        alignItems: 'center',
                                        flex: 1,
                                        flexDirection: 'row',
                                        justifyContent: 'space-between',
                                        marginVertical: 4
                                    }}>
                                        <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
                                            <Icon
                                                name={transactionIcon}
                                                iconSet="MaterialCommunityIcons"
                                                color={transactionColor}
                                                size={55}
                                            />
                                            <MyText style={{ fontSize: 18, fontWeight: '700', paddingTop: 10 }} text={this.state.contentCollectionTransaction} />
                                        </View>


                                    </View>
                                </AlertMessage>
                                <PaymentTransferSheet
                                    paymentTransferSheetRef={this.paymentTransferSheetRef}
                                    bankList={this.state.dataBankInfo}
                                    bankSelected={this.state.bankSelected}
                                    saleOrderID={SaleOrderID}
                                    onChangeBank={this.handleChangeBank}
                                    handleIntervalPayment={this.handleIntervalPayment}
                                    onChangeStatusSheet={(position) => {
                                        if (position == -1) {
                                            clearInterval(this.intervalPaymentId.current);
                                            if (this.state.statusTransferPaymentSuccess.type == "SUCCESS") {
                                                showBlockUI()
                                                actionSaleOrder.ReLoadSaleOrderPayment(SaleOrderID).then((reponse) => {
                                                    actionSaleOrder.setSaleOrderPayment(reponse)
                                                    this.isFirst = true
                                                    const { VoucherConcernType, TotalRemain } = reponse;
                                                    if (VoucherConcernType == 6 && TotalRemain == 0) {
                                                        this.setState({ isVisibleModalCollection: true });
                                                    }
                                                    hideBlockUI()
                                                }).catch(({ msgError, errorType }) => {
                                                    if (errorType == 1106) {
                                                        hideBlockUI()
                                                        this.setState({ isVisibleModalCollection: true });
                                                        this.transferCreateOV.current = true
                                                    }
                                                    else {
                                                        Alert.alert('', msgError, [
                                                            {
                                                                text: 'OK',
                                                                onPress: hideBlockUI
                                                            }
                                                        ]);
                                                    }
                                                })
                                                this.isCheckTransfer = true;
                                            }
                                        }
                                    }}
                                    statusTransferPaymentSuccess={this.state.statusTransferPaymentSuccess}
                                />
                                <AlertMessage
                                    title="Đang thực hiện giao dịch..."
                                    visible={this.state.waitingForPayment.visible}
                                    onClose={() => { }}
                                    message={this.state.waitingForPayment.message}
                                    style={{ justifyContent: "space-evenly", flexDirection: 'row', }}
                                    styleText={{ color: 'red', fontWeight: "bold" }}
                                    buttons={this.state.waitingForPayment.buttons}
                                    isShowButton={this.state.waitingForPayment.buttons.length != 0}
                                    maxHeight='100%'
                                >
                                    <View style={{
                                        alignItems: 'center',
                                        flex: 1,
                                        flexDirection: 'row',
                                        justifyContent: 'space-between',
                                        marginVertical: 4
                                    }}>
                                        <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
                                            <View style={{ paddingBottom: 10 }}>
                                                <MyText style={{ color: COLORS.txt808080 }} text={"Vui lòng quét hoặc cà thẻ!!!"} />

                                            </View>
                                            <ImageURI
                                                uri={'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQtFf_xWszB2pidBE_IyQY-ihqvq_zDsE_irQ&s'}
                                                style={{
                                                    width: 90,
                                                    height: 90,
                                                }}
                                            />
                                            <View style={{ paddingVertical: 10 }}>
                                                <MyText addSize={-1} style={{ color: COLORS.txt808080 }} text={"Số tiền thanh toán: "} >
                                                    <MyText style={{ color: COLORS.txtF50537 }} text={helper.convertNum(this.state.waitingForPayment.value || 0)} />
                                                </MyText>

                                            </View>
                                            {
                                                helper.IsNonEmptyString(this.state.waitingForPayment.message) && < View

                                                    style={{
                                                        borderTopWidth: StyleSheet.hairlineWidth,
                                                        paddingTop: 5, flexDirection: "row",
                                                        justifyContent: "space-between", alignItems: "center", width: "100%"
                                                    }}>

                                                    {/* <MyText style={{ fontWeight: "bold" }} addSize={-2} text={" Nhập mã chuẩn chi"} /> */}
                                                    <TextInput
                                                        style={{
                                                            borderColor: COLORS.bdDDDDDD,
                                                            borderWidth: StyleSheet.hairlineWidth,
                                                            height: 35,
                                                            paddingHorizontal: 8,
                                                            justifyContent: "center",
                                                            alignItems: "flex-end",
                                                            textAlign: "right",
                                                            backgroundColor: COLORS.bgFFFFFF,
                                                            marginLeft: 10,
                                                            flex: 5

                                                        }}
                                                        value={this.state.waitingForPayment.approvalCode}
                                                        onChangeText={(text) => {
                                                            if (new RegExp(/^\w{0,6}$/).test(text)) {
                                                                this.setState({
                                                                    waitingForPayment: {
                                                                        ...this.state.waitingForPayment,
                                                                        approvalCode: text
                                                                    }
                                                                });
                                                            }

                                                        }}
                                                        returnKeyType={"done"}
                                                        placeholder={"Nhập mã chuẩn chi"}
                                                    />
                                                    <View style={{
                                                        flex: 2,
                                                        alignItems: "center",
                                                        justifyContent: "center",
                                                        flexDirection: "row",
                                                        paddingVertical: 10,
                                                        height: 35,
                                                        paddingLeft: 10
                                                        // opacity: disabled ? 0.5 : 1
                                                    }}>
                                                        <ButtonPayment
                                                            text={"Xác nhận"}
                                                            styleContainer={{
                                                                borderRadius: 4,
                                                                backgroundColor: COLORS.txt5BB180,
                                                                height: 35,
                                                                width: 100,
                                                                marginLeft: 10
                                                            }}
                                                            styleText={{
                                                                color: COLORS.txtFFFFFF,
                                                                fontSize: 12,
                                                                fontWeight: "bold"
                                                            }}
                                                            onPress={this.confirmApprovalCode}
                                                        />
                                                    </View>
                                                </View>
                                            }


                                        </View>


                                    </View>
                                </AlertMessage>

                                <OTPSheet
                                    bottomSheetRef={this.OTPSheetRef}
                                    onChangeStatusSheet={() => { }}
                                >
                                    <OTPInner
                                        typeOTP={"LATTER"}
                                        onConfirm={() => {
                                            this.OTPSheetRef.current?.dismiss()
                                            this.createQR(this.dataTempCreateQR.current)
                                        }}
                                        customerInfo={{
                                            customerPhone: CustomerPhone,
                                            customerName: CustomerName,
                                        }}
                                    />
                                </OTPSheet>
                            </KeyboardAwareScrollView >
                        }
                    />
                </BottomSheetModalProvider>

            </SafeAreaView >
        );
    }

    modifySaleOrder = (data, isNonBlockUI) => {
        const {
            isOutput,
            isPayCash
        } = this.state;
        !isNonBlockUI && showBlockUI();
        this.props.actionSaleOrder.modifySaleOrderPayment({
            saleOrder: data,
            isOutput,
            isPayCash
        }).then(success => {
            hideBlockUI();
            const { dataSaleOrder } = this.props;
            if (dataSaleOrder.PaymentTransactionPartners) {
                const isWarning =
                    dataSaleOrder.PaymentTransactionPartners.some(
                        (voucher) => voucher.IsShowWarning
                    );

                isWarning &&
                    Toast.show({
                        type: 'error',
                        text1: VoucherWarningMessage,
                        visibilityTime: 4500
                    });
            }
        }).catch(error => {
            Alert.alert(translate('common.notification_uppercase'), error.msgError, [
                {
                    text: translate('common.btn_skip'),
                    style: "default",
                    onPress: hideBlockUI
                },
                {
                    text: translate('common.btn_notify_try_again'),
                    style: "default",
                    onPress: () => { this.modifySaleOrder(data) }
                }
            ]);
        });
    }

    applyGiftVoucher = (value, dataTransaction) => {
        if (dataTransaction?.PaymentTransactionTypeID) {
            this.applyGiftVoucherGOTIT(value, dataTransaction)
        }
        else {
            this.applyGiftVoucherMWG(value)
        }
    }

    applyGiftVoucherGOTIT = (value, dataTransaction) => {
        const { dataSaleOrder } = this.props;
        const data = {
            saleOrderId: dataSaleOrder.SaleOrderID,
            paymentTransactionType: dataTransaction,
            paymentAmount: 0,
            codePartner: value,
            orderTypeID: dataSaleOrder.OrderTypeID
        };
        showBlockUI();
        this.props.actionSaleOrder
            .getVoucherGotIt(data)
            .then((res) => {
                // hideBlockUI();
                const dataModifySaleOrder = { ...dataSaleOrder };
                if (
                    dataModifySaleOrder.PaymentTransactionPartners?.length > 0
                ) {
                    dataModifySaleOrder.PaymentTransactionPartners = [
                        res.PaymentTransactionGiftVoucher,
                        ...dataModifySaleOrder.PaymentTransactionPartners
                    ];
                } else {
                    dataModifySaleOrder.PaymentTransactionPartners = [
                        res.PaymentTransactionGiftVoucher
                    ];
                }
                this.modifySaleOrder(dataModifySaleOrder);
            })
            .catch((msgError) => {
                Alert.alert('', msgError, [
                    {
                        text: 'OK',
                        onPress: hideBlockUI
                    }
                ]);
            });
    };

    validateOTP = (code, type) => {
        const regExpOTP = new RegExp(/^\d{4}$/);
        const isValidate = regExpOTP.test(code);
        if (helper.IsEmptyString(code)) {
            switch (type) {
                case 2:
                    Alert.alert("", translate('editSaleOrder.OTP_alert'));
                    break;
                case 1:
                    Alert.alert("", translate('shoppingCart.please_enter_id_code'));
                    break;
                default:
                    break;
            }
            return false;
        }
        if (!isValidate) {
            switch (type) {
                case 2:
                    Alert.alert("", translate('editSaleOrder.OTP_full_alert'));
                    break;
                case 1:
                    Alert.alert("", translate('shoppingCart.please_enter_4_digits_id_code'));
                    break;
                default:
                    break;
            }
            return false;
        }
        return true;
    }

    onCheckOTP = (otpCode, GiftVoucherCertifyType) => {
        Keyboard.dismiss();
        const isValidate = this.validateOTP(otpCode, GiftVoucherCertifyType);
        if (isValidate) {
            this.checkVerify(otpCode, GiftVoucherCertifyType);
        }
    }

    alertErrorCheckVeryfy = (msgError, value, GiftVoucherCertifyType) => {
        Alert.alert(
            translate('common.notification_uppercase'),
            msgError,
            [
                {
                    text: translate('common.btn_skip'),
                    style: 'cancel',
                    onPress: hideBlockUI
                },
                {
                    text: translate('common.btn_notify_try_again'),
                    style: 'default',
                    onPress: () =>
                        this.checkVerify(value, GiftVoucherCertifyType)
                }
            ]
        );
    }

    checkVerify = (value, GiftVoucherCertifyType) => {
        const { phoneCertifyPMH } = this.state;
        const { userInfo: { brandID } } = this.props
        showBlockUI();
        switch (GiftVoucherCertifyType) {
            case 2: {
                actionShoppingCartCreator.verifyOTP(value, phoneCertifyPMH)
                    .then((data) => {
                        this.applySMSVerify(value, data.customerId, this.oldImei, GiftVoucherCertifyType, true)
                        hideBlockUI();
                    })
                    .catch((msgError) => {
                        this.alertErrorCheckVeryfy(msgError, value, GiftVoucherCertifyType)
                    });
                break;
            }
            case 1: {
                actionShoppingCartCreator.verifyIdentify(value, "", phoneCertifyPMH, brandID)
                    .then(data => {
                        this.applySMSVerify(value, data.customerId, this.oldImei, GiftVoucherCertifyType, true)
                        hideBlockUI();
                    }).catch(msgError => {
                        this.alertErrorCheckVeryfy(msgError, value, GiftVoucherCertifyType)
                    });
                break;
            }
            default: {
                break;
            }

        }
    }

    applySMSVerify = (CertifyOTP, CustomerIDCertify, value, GiftVoucherCertifyType, IsVerifiedGiftVoucher) => {
        const { dataSaleOrder } = this.props;
        const newData = { ...dataSaleOrder }
        newData.GiftVoucherCertifyType = GiftVoucherCertifyType
        newData.IsVerifiedGiftVoucher = IsVerifiedGiftVoucher
        newData.CertifyOTP = CertifyOTP
        newData.CustomerIDCertify = CustomerIDCertify
        const { isOutput, isPayCash } = this.state;
        showBlockUI();
        this.props.actionSaleOrder
            .verifiedGiftVoucherIssue({
                saleOrder: newData,
                imei: value,
                imeiEncrypt: '',
                isOutput,
                isPayCash,
            })
            .then((response) => {
                hideBlockUI();
                this.setState({ visibleOTPCusVoucher: false })
                const { dataSaleOrder: newDataSaleOrder } = this.props;
                if (newDataSaleOrder.GiftVoucherIssueRequests) {
                    const isWarning =
                        newDataSaleOrder.GiftVoucherIssueRequests.some(
                            (voucher) => voucher.IsShowWarning
                        );

                    isWarning &&
                        Toast.show({
                            type: 'error',
                            text1: VoucherWarningMessage,
                            visibilityTime: 4500
                        });
                }
            })
            .catch((error) => {
                const { msgError, errorType } = error
                if (errorType === -999) {
                    this.setState({ visiblePinCode: true })
                    this.oldImei = value
                    hideBlockUI()
                }
                else {
                    Alert.alert(translate('common.notification_uppercase'), msgError,
                        [
                            {
                                text: translate('common.btn_notify_try_again'),
                                style: "default",
                                onPress: () => this.applySMSVerify(CertifyOTP, CustomerIDCertify, value, GiftVoucherCertifyType, IsVerifiedGiftVoucher)
                            }
                        ]
                    )
                }
            });
    };

    closeModalVerify = () => {
        this.setState({ visibleOTPCusVoucher: false, phoneCertifyPMH: '' })
        //delete PMH chưa xác thực
        const newDataSaleOrder = { ...this.props.dataSaleOrder };
        const lstGiftVoucherIssueNew = newDataSaleOrder.GiftVoucherIssueRequests.filter(x => x.IMEI != this.oldImei);
        newDataSaleOrder.GiftVoucherIssueRequests = lstGiftVoucherIssueNew.length == 0 ? null : lstGiftVoucherIssueNew;
        newDataSaleOrder.IsCertifyGiftVoucher = false;
        this.props.actionSaleOrder.stop_modify_saleorder_payment(newDataSaleOrder);
        this.oldImei = "";
    }

    applyGiftVoucherMWG = (value) => {
        const { dataSaleOrder } = this.props;
        const { isOutput, isPayCash } = this.state;
        showBlockUI();
        this.props.actionSaleOrder
            .applyGiftVoucher({
                saleOrder: dataSaleOrder,
                imei: value,
                imeiEncrypt: '',
                isOutput,
                isPayCash,
            })
            .then(async (response) => {
                let isAuthLoyalty = false;
                let phoneNumber = '';
                let isCheckedCertify = false;
                let isCertifyOTP = false;
                let isCertifiedPhone = false;
                if (response.GiftVoucherIssueRequests) {
                    const indexLast = response.GiftVoucherIssueRequests.length - 1 ?? -1;
                    if (indexLast !== -1) {
                        const { CustomerPhoneCertify, IsCheckedCertify, IMEI, IsCertifyOTP } =
                            response.GiftVoucherIssueRequests[indexLast];
                        phoneNumber = CustomerPhoneCertify;
                        isCheckedCertify = IsCheckedCertify;
                        isCertifyOTP = IsCertifyOTP;
                        this.oldImei = IMEI;
                        const phoneIndex = response.GiftVoucherOTPBOList?.findIndex((gv) => gv.CustomerPhone === CustomerPhoneCertify) ?? -1;
                        isCertifiedPhone = phoneIndex !== -1;
                        isAuthLoyalty = await storageHelper.checkVerifyLoyalty(
                            phoneNumber,
                            dataSaleOrder.CartID?.trim()
                        );
                    }
                }
                // PMH loại yêu cầu xác thực Khách hàng và chưa được xác thực
                if (!isCheckedCertify && isCertifyOTP && !isCertifiedPhone) {
                    if (!isAuthLoyalty) {
                        hideBlockUI();
                        this.setState({
                            visibleOTPCusVoucher: true,
                            phoneCertifyPMH: phoneNumber
                        });
                    } else {
                        const OTPType = 2;
                        const emptyCode = '';
                        const emptyCustomerId = '';

                        this.applySMSVerify(
                            emptyCode,
                            emptyCustomerId,
                            this.oldImei,
                            OTPType,
                            true
                        );
                    }
                } else {
                    hideBlockUI();
                    this.setState({ visiblePinCode: false })
                    this.oldImei = ""
                    const { dataSaleOrder: newDataSaleOrder } = this.props;
                    if (newDataSaleOrder.GiftVoucherIssueRequests) {
                        const isWarning =
                            newDataSaleOrder.GiftVoucherIssueRequests.some(
                                (voucher) => voucher.IsShowWarning
                            );

                        isWarning &&
                            Toast.show({
                                type: 'error',
                                text1: VoucherWarningMessage,
                                visibilityTime: 4500
                            });
                    }
                }
            })
            .catch((error) => {
                const { msgError, errorType } = error
                if (errorType === -999) {
                    this.setState({ visiblePinCode: true })
                    this.oldImei = value
                    hideBlockUI()
                }
                else {
                    Alert.alert('', msgError, [
                        {
                            text: 'OK',
                            style: 'default',
                            onPress: hideBlockUI
                        }
                    ]);
                }

            });
    };

    removeGiftVoucher = (dataVoucher, index) => () => {
        const { dataSaleOrder } = this.props;
        const newDataVoucher = dataVoucher.filter((ele, id) => id != index);
        const dataModifySaleOrder = helper.deepCopy(dataSaleOrder);
        dataModifySaleOrder.GiftVoucherIssueRequests = newDataVoucher;
        dataModifySaleOrder.IsVerifiedGiftVoucher = false
        dataModifySaleOrder.CertifyOTP = null
        dataModifySaleOrder.CustomerIDCertify = null
        if (dataSaleOrder.IsSOAnKhang) {
            const { cashPayment } = this.state;
            const amount = dataVoucher[index]?.Amount ?? 0;
            const newCashVND = Math.max(cashPayment + amount, 0);
            dataModifySaleOrder.CashVND = newCashVND;
            this.setState({ cashPayment: newCashVND });
        }
        // dataModifySaleOrder.CashVND = 0;
        // this.setState({ cashPayment: 0 });
        this.modifySaleOrder(dataModifySaleOrder);
    }

    handleApplyMoneyCard = (dataCard, cardSelected) => {
        if (String(cardSelected.MoneyCardID) == MONEY_CARD_ID.VP_BANK) return this.applyVPBankCard(dataCard, cardSelected)
        this.applyMoneyCard(dataCard, cardSelected);
    }

    applyVPBankCard = async (dataCard, cardSelected) => {
        const { dataSaleOrder, userInfo } = this.props;
        const { dataMoneys } = this.state;
        const ip = dataMoneys.find(_item => _item.MoneyCardID == MONEY_CARD_ID.VP_BANK)?.POSInfoList?.[0]?.IP ?? "";
        const data = {
            amount: cardSelected.MoneyCard,
            timeout: 60000,
            ip: ip,
            port: 8800,
            req_id: CryptoJS.MD5(DEVICE.uniqueId).toString(),
            client_id: `${dataSaleOrder.SaleOrderID?.trim()}-${userInfo.userName}-${dataSaleOrder.TotalRemain}VPB${dataSaleOrder.ApplyMoneyCardDetails.length}-${cardSelected.MoneyCard}`,
            "loginStore": userInfo.storeID,
            "loginUser": userInfo.userName,
            "languageID": userInfo.languageID,
            voucherConcern: dataSaleOrder.SaleOrderID
        };

        try {
            this.setState({
                waitingForPayment: {
                    visible: true,
                    value: cardSelected.MoneyCard,
                    message: "",
                    buttons: [],
                    approvalCode: ""
                }
            });
            const result = await actionSaleOrderCreator.vpBankPayment(data);
            this.setState({
                waitingForPayment: {
                    visible: false,
                    value: 0,
                    message: "",
                    buttons: [],
                    approvalCode: ""
                }
            });
            const cardInfo = { ...cardSelected, MoneyCardVoucherID: result.approvalCode }
            this.applyMoneyCard(dataCard, cardInfo)
        } catch (msgError) {
            this.setState({
                waitingForPayment: {
                    ...this.state.waitingForPayment,
                    message: msgError,
                    approvalCode: "",
                    buttons: [
                        {
                            key: 0,
                            text: "Bỏ qua",
                            outline: true,
                            onPress: () => {
                                this.setState({
                                    waitingForPayment: {
                                        visible: false,
                                        value: 0,
                                        message: "",
                                        buttons: [],
                                    }
                                });
                            }
                        }
                        // ,
                        // {
                        //     key: 1,
                        //     text: 'Xác nhận',
                        //     outline: false,
                        //     onPress: () => {
                        //         const keyMoneyCard = getKeyMoneyCard(dataCard || []);
                        //         const key = `${cardSelected?.MoneyCardID}${this.state.waitingForPayment.approvalCode}`;
                        //         if (!helper.IsNonEmptyString(this.state.waitingForPayment.approvalCode)) {
                        //             Alert.alert("", translate('saleOrderPayment.please_enter_authorization_code'));
                        //             return false;
                        //         }
                        //         if (!new RegExp(/^\w{6}$/).test(this.state.waitingForPayment.approvalCode)) {
                        //             Alert.alert("", translate('saleOrderPayment.authorization_code_six_letters'));
                        //             return false;
                        //         }
                        //         if (keyMoneyCard.has(key)) {
                        //             Alert.alert("", translate('saleOrderManager.existed_expense_code'));
                        //             return false;
                        //         }
                        //         const cardInfo = { ...cardSelected, MoneyCardVoucherID: this.state.waitingForPayment.approvalCode }
                        //         this.setState({
                        //             waitingForPayment: {
                        //                 visible: false,
                        //                 value: 0,
                        //                 message: "",
                        //                 buttons: [],
                        //                 approvalCode: "",
                        //             }
                        //         }, () => {
                        //             this.applyMoneyCard(dataCard, cardInfo)
                        //         });

                        //     }
                        // }
                    ]
                }
            });
        }
    };
    confirmApprovalCode = () => {
        const dataCard = this.props.dataSaleOrder?.ApplyMoneyCardDetails || []
        const keyMoneyCard = getKeyMoneyCard(dataCard);
        const approvalCode = this.state.waitingForPayment.approvalCode;

        if (!helper.IsNonEmptyString(approvalCode)) {
            Alert.alert("", translate('saleOrderPayment.please_enter_authorization_code'));
            return;
        }
        if (!/^\w{6}$/.test(approvalCode)) {
            Alert.alert("", translate('saleOrderPayment.authorization_code_six_letters'));
            return;
        }
        if (keyMoneyCard.has(`${this.dataCardSelected.current?.MoneyCardID}${approvalCode}`)) {
            Alert.alert("", translate('saleOrderManager.existed_expense_code'));
            return;
        }

        const cardInfo = { ...this.dataCardSelected.current ?? {}, MoneyCardVoucherID: approvalCode };
        this.setState({
            waitingForPayment: {
                visible: false,
                value: 0,
                message: "",
                buttons: [],
                approvalCode: "",
            }
        }, () => {
            this.applyMoneyCard(dataCard, cardInfo)
        });

    }


    applyMoneyCard = (dataCard, cardSelected) => {
        const { cashPayment } = this.state;
        const { dataSaleOrder } = this.props;
        const newDataCard = helper.deepCopy(dataCard);
        const dataModifySaleOrder = helper.deepCopy(dataSaleOrder);
        newDataCard.push(cardSelected);
        dataModifySaleOrder.ApplyMoneyCardDetails = newDataCard;
        if (cashPayment > 0 && dataSaleOrder.IsSOAnKhang) {
            const newCashVND = Math.max(cashPayment - cardSelected.MoneyCard, 0);
            dataModifySaleOrder.CashVND = newCashVND;
            this.setState({ cashPayment: newCashVND });
        }
        this.modifySaleOrder(dataModifySaleOrder);
    }

    getTokenMpos = (info) => {
        const {
            dataSaleOrder: { SaleOrderID },
            userInfo: { storeID }
        } = this.props;
        const data = {
            "saleOrderID": SaleOrderID,
            "storeId": storeID,
            "amount": info.MoneyCard
        }
        showBlockUI();
        actionSaleOrderCreator.getTokenPaymentMpos(data).then(token => {
            hideBlockUI();
            this.onLinkingXMPOS(token);
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError, [{
                text: "OK",
                onPress: hideBlockUI
            }]);
        })
    }

    onLinkingXMPOS = (token) => {
        const urlXMPOS = "xmpos://xmpos/token/";
        Linking.canOpenURL(urlXMPOS).then(supported => {
            if (supported) {
                Linking.openURL(`${urlXMPOS}${token}`);
            } else {
                Alert.alert("", translate('saleOrderPayment.please_use_XMPOS'));
            }
        });
    }

    removeMoneyCard = (dataCard, index) => () => {
        const { dataSaleOrder } = this.props;
        const newDataCard = dataCard.filter((ele, id) => id != index);
        const dataModifySaleOrder = helper.deepCopy(dataSaleOrder);
        dataModifySaleOrder.ApplyMoneyCardDetails = newDataCard;
        if (dataSaleOrder.IsSOAnKhang) {
            const { cashPayment } = this.state;
            const moneyCard = dataCard[index]?.MoneyCard ?? 0;
            const newCashVND = Math.max(cashPayment + moneyCard, 0);
            dataModifySaleOrder.CashVND = newCashVND;
            this.setState({ cashPayment: newCashVND });
        }
        // dataModifySaleOrder.CashVND = 0;
        // this.setState({ cashPayment: 0 });
        this.modifySaleOrder(dataModifySaleOrder);

    }

    removeVoucherGotIt = (dataGotIt, voucher) => () => {
        const { IMEI, PaymentTransactionID, PaymentTransactionTypeID } = voucher
        const partner = 'gotit';
        const { actionSaleOrder, dataSaleOrder } = this.props;
        showBlockUI();
        actionSaleOrder.deleteQRTransaction({
            transactionID: PaymentTransactionID,
            imei: IMEI,
            partner,
            paymentTransactionTypeId: PaymentTransactionTypeID
        }).then(success => {
            const newDataGotIt = dataGotIt.filter((ele, id) => ele.PaymentTransactionID != PaymentTransactionID);
            const dataModifySaleOrder = helper.deepCopy(dataSaleOrder);
            dataModifySaleOrder.PaymentTransactionPartners = newDataGotIt;
            this.modifySaleOrder(dataModifySaleOrder);
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError, [
                {
                    text: "OK",
                    onPress: hideBlockUI
                },
            ]);
        })
    }

    updateDataLoyalty = (data, infoLoyalty) => {
        const { dataSaleOrder, actionSaleOrder } = this.props;
        const dataModifySaleOrder = helper.deepCopy(dataSaleOrder);
        dataModifySaleOrder.LoyaltyInfo = data;
        dataModifySaleOrder.RequestIDLoyalty = infoLoyalty.requestId;
        dataModifySaleOrder.CustomerIDLoyalty = infoLoyalty.customerId;
        // dataModifySaleOrder.CashVND = 0;
        // this.setState({ cashPayment: 0 });
        if (dataSaleOrder.IsSOAnKhang) {
            const { cashPayment } = this.state;
            const existLoyaltyPoint = data.LoyaltyPoint > 0
            const mainPoint = existLoyaltyPoint ? -data.LoyaltyPoint : dataModifySaleOrder.TotalPointLoyalty;
            const newCashVND = existLoyaltyPoint ? Math.max(cashPayment + mainPoint, 0) : Math.max(cashPayment + mainPoint, 0);
            dataModifySaleOrder.CashVND = newCashVND;
            this.setState({ cashPayment: newCashVND });
        }
        // Reset loyalty transaction status
        actionSaleOrder.setLoyaltyStatus(TRANSACTION_STATUS.UNKNOWN);
        this.modifySaleOrder(dataModifySaleOrder);
    }

    setKeyboardTaps = () => {
        this.setState({ keyboardTaps: "never", isFocus: true });
    }

    onBlurInputCash = (dataSaleOrder, cashPayment) => () => {
        const { CashVND } = dataSaleOrder;
        this.setState({ keyboardTaps: "always", isFocus: false });
        if (cashPayment != CashVND) {
            const dataModifySaleOrder = helper.deepCopy(dataSaleOrder);
            dataModifySaleOrder.CashVND = cashPayment;
            this.updateCashPayment(dataModifySaleOrder, CashVND);
        }
    }

    updateCashPayment = (data, resetValue) => {
        const {
            isOutput,
            isPayCash
        } = this.state;
        showBlockUI();
        this.props.actionSaleOrder.modifySaleOrderPayment({
            saleOrder: data,
            isOutput,
            isPayCash
        }).then(success => {
            hideBlockUI();
        }).catch(error => {
            Alert.alert(translate('common.notification_uppercase'), error.msgError, [
                {
                    text: translate('common.btn_skip'),
                    style: "default",
                    onPress: () => {
                        hideBlockUI();
                        this.setState({ cashPayment: resetValue });
                    }
                },
                {
                    text: translate('common.btn_notify_try_again'),
                    style: "default",
                    onPress: () => { this.updateCashPayment(data, resetValue) }
                }
            ]);
        });
    }

    onChangOutput = (value) => {
        const { dataSaleOrder } = this.props;
        const { isPayCash } = this.state;
        this.updateOutputPayCash({
            saleOrder: dataSaleOrder,
            isOutput: value,
            isPayCash,
            isCustomer: false
        });
    }

    onChangPayCash = (value) => {
        const { dataSaleOrder } = this.props;
        const { isOutput, isPayCash } = this.state;
        const dataModifySaleOrder = helper.deepCopy(dataSaleOrder);
        if (isPayCash) {
            dataModifySaleOrder.GiftVoucherIssueRequests = []
            dataModifySaleOrder.ApplyMoneyCardDetails = [];
            dataModifySaleOrder.CashVND = 0;
            dataModifySaleOrder.LoyaltyInfo = {};
            this.setState({ cashPayment: 0 });
        }
        this.updateOutputPayCash({
            saleOrder: dataModifySaleOrder,
            isPayCash: value,
            isOutput,
        });
    }

    updateOutputPayCash = (data) => {
        showBlockUI();
        this.props.actionSaleOrder.modifySaleOrderPayment(data).then(success => {
            hideBlockUI();
            this.setState({
                isPayCash: data.isPayCash,
                isOutput: data.isOutput,
            })
        }).catch(error => {
            Alert.alert(translate('common.notification_uppercase'), error.msgError, [
                {
                    text: translate('common.btn_skip'),
                    style: "default",
                    onPress: hideBlockUI
                },
                {
                    text: translate('common.btn_notify_try_again'),
                    style: "default",
                    onPress: () => { this.updateOutputPayCash(data) }
                }
            ]);
        });
    }

    onCheckSaleOrder = () => {
        const {
            reportRetail,
            reportVAT,
            reportCommon,
            contractID,
            isFocus,
            saleProgramInfo
        } = this.state;
        if (isFocus) return Keyboard.dismiss();
        const { dataSaleOrder } = this.props;
        const { EPOSTransactionID, IsInstallment, VoucherConcernType, SaleOrderSaleProgramInfo } = dataSaleOrder;
        const IsAutoCreateEP = helper.IsNonEmptyString(EPOSTransactionID);
        const { PartnerInstallmentID } = SaleOrderSaleProgramInfo ?? {};
        const isSmartPosIntsallment = PartnerInstallmentID == 23;
        const isNewFollowPartner = helper.isNewFollowPartner(PartnerInstallmentID);
        const isRequireContract = (IsInstallment && !IsAutoCreateEP && !isSmartPosIntsallment && !isNewFollowPartner);
        const isContract = helper.IsNonEmptyString(contractID);
        const isRetail = !helper.IsEmptyObject(reportRetail);
        const isVat = !helper.IsEmptyObject(reportVAT);
        const isCommon = !helper.IsEmptyObject(reportCommon);
        const isReport = (isRetail || isVat || isCommon);
        if (isNewFollowPartner && saleProgramInfo.TermLoan == 0) {
            return Alert.alert("", "Vui lòng chọn kì hạn vay.");
        }
        if (!isReport) {
            Alert.alert("", translate('saleOrderPayment.please_choose_printer'));
        }
        // else if (isRequireContract && !isContract) {
        //     Alert.alert("", "Vui lòng nhập mã hợp đồng");
        // }
        else {
            this.onCompleteSaleOrder(isRequireContract);
        }
    }

    onCompleteSaleOrder = (isRequireContract) => {
        const {
            isOutput,
            isPayCash,
            mainProducts,
            saleProducts,
            giftProducts,
            keyGiftRemove,
            saleProgramInfo,
            isCustomer,
            batchesBySOD
        } = this.state;
        const { dataSaleOrder, userInfo } = this.props;
        const { SaleOrderSaleProgramInfo, IsSOAnKhang, StaffUser } = dataSaleOrder;
        const dataApply = helper.deepCopy(dataSaleOrder);
        const dataGiftProduct = getGiftPromotion(giftProducts, keyGiftRemove);
        let warningMessage = "";
        if (IsSOAnKhang) {
            mainProducts.forEach(product => {
                product.cus_SaleOrderDetailInfoBOList = batchesBySOD[product.SaleOrderDetailID] ?? null
            });
            dataGiftProduct.forEach(product => {
                product.cus_SaleOrderDetailInfoBOList = batchesBySOD[product.SaleOrderDetailID] ?? null
            });
            saleProducts.forEach(product => {
                product.cus_SaleOrderDetailInfoBOList = batchesBySOD[product.SaleOrderDetailID] ?? null
            });
        }
        dataApply.listMainProduct = [...mainProducts];
        dataApply.listSalePromotion = [...saleProducts];
        dataApply.listGiftPromotion = [...dataGiftProduct];
        if (isRequireContract) {
            const newSaleProgramInfo = { ...SaleOrderSaleProgramInfo, ...saleProgramInfo };
            dataApply.SaleOrderSaleProgramInfo = newSaleProgramInfo;
        }
        if (dataSaleOrder.GiftVoucherIssueRequests) {
            const isWarning =
                dataSaleOrder.GiftVoucherIssueRequests.some(
                    (voucher) => voucher.IsShowWarning
                );
            if (isWarning) {
                if (dataSaleOrder.TotalRemain > 0) {
                    warningMessage = PreventCompleteSOMessage;
                } else {
                    warningMessage = VoucherWarningMessage;
                }
            }
        }
        if (dataSaleOrder.TotalRemain > 0 && warningMessage) {
            // prevent from going on completing SO.
            Alert.alert('', warningMessage);
        } else {
            if (userInfo.userName != StaffUser || warningMessage) {
                Alert.alert(
                    "",
                    warningMessage
                        ? warningMessage
                        : translate('saleOrderPayment.you_want_finish_order'),
                    [
                        {
                            text: translate('saleOrderPayment.btn_skip_uppercase'),
                            style: "cancel"
                        },
                        {
                            text: translate('saleOrderPayment.btn_continue'),
                            style: "default",
                            onPress: () => this.createOuputPaymentVoucher({
                                dataSaleOrder: dataApply,
                                isOutput,
                                isPayCash,
                                isCustomer
                            })
                        }
                    ]
                )
            }
            else {
                this.createOuputPaymentVoucher({
                    dataSaleOrder: dataApply,
                    isOutput,
                    isPayCash,
                    isCustomer
                })
            }
        }
    }

    useCodePartner = (info) => {
        const { dataSaleOrder } = info;
        if (helper.IsNonEmptyArray(dataSaleOrder.PaymentTransactionPartners)) {
            this.props.actionSaleOrder.useCodePartner({
                "totalGiftVoucherAmountPartner": dataSaleOrder.TotalGiftVoucherAmountPartner,
                "paymentTransactionPartnerList": dataSaleOrder.PaymentTransactionPartners
            });
        }
    }

    createOuputPaymentVoucher = (info) => {
        showBlockUI();
        this.props.actionSaleOrder.createVoucherOVCM(info)
            .then(data => {
                this.useCodePartner(info);
                this.handleResponse(data, info);
                this.dataCollection = data;
                this.dataCreateVoucherOVCM = data;
                const message = data.SaleOrderBO.ErrorMessageOutput;
                if (message) {
                    const type = "warning";
                    showMessage({ message, type, duration: 5500 });
                }
            }).catch(error => {
                hideBlockUI();
                const { msgError, errorData } = error;
                if (errorData) {
                    this.setState({
                        isVisibleAlert: true,
                        loyaltyTransaction: {
                            status: errorData.status,
                            amount: errorData.loyaltyPoint,
                            createdAt: errorData.createdDate,
                            message: errorData.message
                        }
                    });
                } else {
                    Alert.alert("", msgError, [
                        {
                            text: "OK",
                            style: "default"
                        }
                    ]);
                }
            });
    }

    handleResponse = (data, info) => {
        const { actionCollectionManager } = this.props;
        const { SaleOrderBO, msgObject } = data;
        const { isOutput } = this.state;
        const isQueryStatusInterval = data?.SaleOrderBO?.extensionProperty?.IsQueryStatusInterval;
        if (helper.IsNonEmptyArray(msgObject)) {
            const lastIndex = msgObject.length - 1;
            this.showAlertNotify(msgObject, SaleOrderBO, lastIndex);
        }
        else if (isQueryStatusInterval) {
            hideBlockUI();
            actionCollectionManager.clear_data_query_status();
            this.setState({ isVisibleModalCollection: true })
        }
        else if (isOutput) {
            checkShowPackagingBagScreen({ "saleOrder": SaleOrderBO, "orderTypeID": SaleOrderBO.orderTypeID }).then((IsShowPackagingScreen) => {
                if (IsShowPackagingScreen) {
                    hideBlockUI();
                    this.props.navigation.navigate('PackagingBag', {
                        saleOrderID: SaleOrderBO.saleOrderID,
                        outputStoreID: SaleOrderBO.outputStoreID,
                        shouldGoBack: true,
                        onFinish: () => this.showAlertContent(data)
                    });
                } else {
                    this.showAlertContent(data);
                }
            });
        } else {
            this.showAlertContent(data);
        }
    };

    onNavigationCollectionManager = () => {
        this.goBack();
    }

    handleOrderStatusSuccess = () => {
        const { dataSaleOrder } = this.props;
        const { VoucherConcernType, TotalRemain, SaleOrderID } = dataSaleOrder;
        this.setState({ isVisibleModalCollection: false }, () => {
            if (VoucherConcernType == 6 && TotalRemain == 0 || this.transferCreateOV.current) {
                const reportInfo = [{ "ReportContent": "AirtimeTransferContent" }]
                this.onCheckPrintContent({
                    "saleOrderID": SaleOrderID,
                    "reportContents": reportInfo
                })
                this.transferCreateOV.current = false
            } else if (!helper.IsEmptyObject(this.dataCollection)) {
                this.showAlertContent(this.dataCollection);
            }
        });
    }

    handleIsNotServiceOrder = () => {
        this.setState({ isVisibleModalCollection: false });
        this.goBack();
    }

    handleNavigatorOTPAirtimeService = (SaleOrderID) => {
        const { isCalled } = this.state;
        const { dataSaleOrder, actionBankAirtimeService } = this.props;
        const { CustomerPhone } = dataSaleOrder ?? "";
        this.setState({ isVisibleModalCollection: false })
        actionBankAirtimeService.updateCustomerPhone(CustomerPhone);
        this.props.navigation.navigate("OTPAirtimeService", {
            SaleOrderID,
            isCalled
        });
    }

    showAlertNotify = (
        arrayMessage,
        SaleOrderBO,
        lastIndex,
        index = 0
    ) => {
        const { content } = arrayMessage[index];
        const { isOutput, isPayCash, isCustomer } = this.state;
        if (index < lastIndex) {
            const nextIndex = index + 1;
            Alert.alert(translate('common.notification_uppercase'), content, [
                {
                    text: translate('saleOrderPayment.btn_skip_uppercase'),
                    onPress: hideBlockUI,
                },
                {
                    text: translate('saleOrderPayment.btn_continue'),
                    onPress: () => this.showAlertNotify(
                        arrayMessage,
                        SaleOrderBO,
                        lastIndex,
                        nextIndex
                    ),
                },
            ]);
        } else {
            Alert.alert(translate('common.notification_uppercase'), content, [
                {
                    text: translate('saleOrderPayment.btn_skip_uppercase'),
                    onPress: hideBlockUI,
                },
                {
                    text: translate('saleOrderPayment.btn_continue'),
                    onPress: () => this.createOuputPaymentVoucher({
                        dataSaleOrder: SaleOrderBO,
                        isOutput,
                        isPayCash,
                        isCustomer
                    }),
                },
            ]);
        }
    };

    showAlertContent = (data) => {
        let content = "";
        const { isPrintDosage, isPrintBatchNo } = this.state;
        const { dataSaleOrder } = this.props;
        const { cus_IsPrintDosageContent, IsSOAnKhang } = dataSaleOrder;
        const {
            SaleOrderBO: {
                NewInOutVoucherID,
                NewOutputVoucherID,
                TypeSendByRefundPoint,
                PointRefund,
                cmOutVoucherDeposit,
                saleOrderID
            },
            eBillContentNumberOfCopy,
            eBillContentIncomeNumberOfCopy,
            giftVCIssueContentNumberOfCopy,
            outTransContentNumberOfCopy,
            MoneyBankTransferRefundInfo,
            airtimeTransferContentNumberOfCopy
        } = data;
        const reportInfo = [];
        if (helper.IsNonEmptyString(NewInOutVoucherID)) {
            content += `${translate('saleOrderPayment.create_receipts')} ${NewInOutVoucherID}`;
        }
        if (helper.IsNonEmptyString(NewOutputVoucherID)) {
            content += (content ? "\n" : "") + `${translate('saleOrderPayment.create_bill')} ${NewOutputVoucherID}`;
        }
        if (helper.IsNonEmptyString(cmOutVoucherDeposit)) {
            content += (content ? "\n" : "") + `${translate('saleOrderPayment.create_payment')} ${cmOutVoucherDeposit}`;
        }
        if (helper.IsNonEmptyString(MoneyBankTransferRefundInfo)) {
            content += `\n${MoneyBankTransferRefundInfo}`;
        }
        if (TypeSendByRefundPoint == 1) {
            content += `${translate('saleOrderPayment.extra_points')} ${helper.convertNum(PointRefund)} ${translate('saleOrderPayment.pay_vip_account')}`;
        }
        if (TypeSendByRefundPoint == 2) {
            content += `${translate('saleOrderPayment.customers_contact_manager_report_card')} ${helper.convertNum(PointRefund)}`;
        }
        if (eBillContentNumberOfCopy == 1) {
            reportInfo.push({ "ReportContent": "EBillContent" });
        }
        if (eBillContentIncomeNumberOfCopy == 1) {
            reportInfo.push({ "ReportContent": "EBillContentIncome" });
        }
        if (giftVCIssueContentNumberOfCopy == 1) {
            reportInfo.push({ "ReportContent": "GiftVCIssueContentPrint" });
        }
        if (outTransContentNumberOfCopy == 1) {
            reportInfo.push({ "ReportContent": "KeySoftwareContent" });
        }
        if (cus_IsPrintDosageContent && isPrintDosage) {
            reportInfo.push({ "ReportContent": "DosageContent" });
        }
        if (IsSOAnKhang && isPrintBatchNo) {
            reportInfo.push({ "ReportContent": "InfoBatchNOContent" });
        }
        if (airtimeTransferContentNumberOfCopy == 1) {
            reportInfo.push({ "ReportContent": "AirtimeTransferContent" });
        }
        // Alert.alert("", content, [
        //     {
        //         text: translate('saleOrderPayment.btn_skip_uppercase'),
        //         style: "default",
        //         onPress: this.goBack
        //     },
        //     {
        //         text: translate('saleOrderPayment.btn_continue_print'),
        //         style: "default",
        //         onPress: this.onCheckPrintContent({
        //             "saleOrderID": saleOrderID,
        //             "reportContents": reportInfo
        //         })
        //     }
        // ]);
        this.onCheckPrintContent({
            "saleOrderID": saleOrderID,
            "reportContents": reportInfo
        })
    }

    onCheckPrintContent = (data) => {
        data.isFitContent = true;
        data.isGetContentHTML = true;
        data.isPublishEBill = true;
        this.getContentHtml(data);
        this.dataPrint = data;
    }

    getContentBase64PDF = (reportInfo) => {
        this.props.actionSaleOrder.getReportContentBase64(reportInfo)
            .then(this.onPrintBill)
            .catch(msgError => {
                Alert.alert(translate('common.notification_uppercase'), msgError, [
                    {
                        text: translate('saleOrderPayment.btn_skip_uppercase'),
                        style: "default",
                        onPress: this.showEbillQTV
                    },
                    {
                        text: translate('saleOrderPayment.btn_retry_uppercase'),
                        style: "default",
                        onPress: () => this.getContentBase64PDF(reportInfo)
                    }
                ]);
            })
    }

    onPrintBill = (data) => {
        const requestAPI = this.getPrintRequestAPI(data);
        if (helper.IsNonEmptyArray(requestAPI)) {
            this.printAllRequest(requestAPI);
        }
        else {
            this.showEbillQTV();
        }
    }

    getPrintRequestAPI = (data) => {
        const { dataSO: { SaleOrderID } } = this.props;
        const requestAPI = [];
        const {
            EBillContent,
            eBillContentPrinterTypeID,

            GiftVCIssueContentPrint,
            giftVCIssueContentPrinterTypeID,

            VATContentPrint,
            vatContentPrinterTypeID,
            vatContentNumberOfCopy,

            imeiListContentPrint,
            imeiListContentPrinterTypeID,
            imeiListContentNumberOfCopy,

            EBillContentIncome,
            eBillContentIncomePrinterTypeID,

            OutTransContent,
            outTransContentPrinterTypeID,

            DosageContent,
            dosageContentPrinterTypeID,

            infoBatchNOContent,
            infoBatchNOContentPrinterTypeID,

            AirtimeTransferContent,
            airtimeTransferContentPrinterTypeID
        } = data;
        if (helper.IsNonEmptyString(EBillContent)) {
            const report = this.getReport(eBillContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
                const printService = this.getPrintService(report, EBillContent);
                requestAPI.push(printService);
            }
        }
        if (helper.IsNonEmptyString(GiftVCIssueContentPrint)) {
            const report = this.getReport(giftVCIssueContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
                const printService = this.getPrintService(report, GiftVCIssueContentPrint);
                requestAPI.push(printService);
                actionSaleOrderCreator.insertLogPrintGift(SaleOrderID);
            }
        }
        if (helper.IsNonEmptyString(VATContentPrint)) {
            const report = this.getReport(vatContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
                const printService = this.getPrintService(report, VATContentPrint);
                requestAPI.push(printService);
            }
        }
        if (helper.IsNonEmptyString(imeiListContentPrint)) {
            const report = this.getReport(imeiListContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
                const printService = this.getPrintService(report, imeiListContentPrint);
                requestAPI.push(printService);
            }
        }
        if (helper.IsNonEmptyString(EBillContentIncome)) {
            const report = this.getReport(eBillContentIncomePrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
                const printService = this.getPrintService(report, EBillContentIncome);
                requestAPI.push(printService);
            }
        }
        if (helper.IsNonEmptyString(OutTransContent)) {
            const report = this.getReport(outTransContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
                const printService = this.getPrintService(report, OutTransContent);
                requestAPI.push(printService);
            }
        }
        if (helper.IsNonEmptyString(DosageContent)) {
            const report = this.getReport(dosageContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
                const printService = this.getPrintService(report, DosageContent);
                requestAPI.push(printService);
            }
        }
        if (helper.IsNonEmptyString(infoBatchNOContent)) {
            const report = this.getReport(infoBatchNOContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
                const printService = this.getPrintService(report, infoBatchNOContent);
                requestAPI.push(printService);
            }
        }
        if (helper.IsNonEmptyString(AirtimeTransferContent)) {
            const report = this.getReport(airtimeTransferContentPrinterTypeID);
            if (!helper.IsEmptyObject(report)) {
                const printService = this.getPrintServicePDF(report, AirtimeTransferContent);
                requestAPI.push(printService);
            }
        }
        return requestAPI;
    }

    getPrintService = (report, content) => {
        let printerConfig = {
            strPrinterName: report.PRINTERNAME,
            strPaperSize: report.PAPERSIZE,
            paperwidth: report.PAPERWIDTH,
            parperheight: report.PARPERHEIGHT,
            intCopyCount: 1,
            bolIsDuplex: false,
            bolShrinkToMargin: false,
            strBase64: content,
        };
        if (report.REPORTID == 2820) {
            printerConfig.strPaperSize = "A4 210 x 297 mm";
        }
        let formBody = [];
        for (const property in printerConfig) {
            const encodedKey = encodeURIComponent(property);
            const encodedValue = encodeURIComponent(printerConfig[property]);
            formBody.push(encodedKey + "=" + encodedValue);
        }
        formBody = formBody.join("&");
        return new Promise((resolve, reject) => {
            actionSaleOrderCreator.printBillVoucher(formBody).then(result => {
                resolve(result);
            }).catch(msgError => {
                reject(msgError);
            });
        });
    }

    printAllRequest = (allPromise) => {
        Promise.all(allPromise).then(result => {
            console.log("PRINT RSULT", result);
            this.showEbillQTV();
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError, [
                {
                    text: "OK",
                    style: "default",
                    onPress: this.showEbillQTV
                }
            ]);
        })
    }

    getReport = (type) => {
        const {
            reportRetail,
            reportVAT,
            reportCommon,
        } = this.state;
        switch (type) {
            case 'InvoiceRetailPrinter':
                return reportRetail;
            case 'VATContentPrint':
                return reportVAT;
            default:
                // CommonPrinter
                return reportCommon;
        }
    }

    showEbillQTV = () => {
        if (this.ebillQTV.size > 0) {
            let content = "";
            this.ebillQTV.forEach(ele => {
                if (helper.IsNonEmptyString(content)) {
                    content += ', ';
                }
                content += REPORT_NAME[ele];
            })
            Alert.alert("Thông báo", `${content} đã được gửi qua app Quà Tặng VIP cho khách hàng.\nVui lòng hướng dẫn khách hàng mở app Quà Tặng VIP để kiểm tra.`, [{
                onPress: this.goBack
            }]);
        }
        else {
            this.goBack();
        }
    }

    getTransactionStatusConfig = (status) => {
        const defaultDescription = "Giao dịch đang được xử lý. Theo dõi kết quả ở mục 'Quản lý GD ngành hàng multicat'. Vẫn thu đủ tiền KH và không thu lại giao dịch mới trước khi GD này có kết quả cuối";
        const config = {
            PENDING: {
                icon: "sync",
                color: "green",
                message: this.props.dataQueryStatus?.cus_AirtimeStatusCacheBO?.Description || defaultDescription,
            },
            SUCCESS: {
                icon: "check-circle-outline",
                color: "#83B4FF",
                message: this.props.dataQueryStatus?.cus_AirtimeStatusCacheBO?.Description || defaultDescription,
            },
            FAIL: {
                icon: "close-circle-outline",
                color: "#F95454",
                message: this.props.dataQueryStatus?.cus_AirtimeStatusCacheBO?.Description || defaultDescription,
            },
        };

        return config[status] || config.PENDING;
    };

    goBack = async () => {
        const {
            actionManagerSO,
            paramFilter,
            navigation,
            dataSaleOrder,
            userInfo,
            actionCollectionManager,
            actionInsuranceBrightside,
            dataQueryStatus,
            actionHealthInsurance,
            actionSaleOrder,
            actionCollectInstallment
        } = this.props;
        const { IsSOAnKhang, VoucherConcernType, SaleOrderID } = dataSaleOrder;
        const { CatalogID, ServiceGroupID, PromotionSaleorderID } = dataQueryStatus ?? "";
        const listDataActionVoucher = this.dataCreateVoucherOVCM?.lstActionAfterCreateVoucher?.[0]?.data?.SIMPROCESSREQUESTTYPEID;
        const { SaleOrderBO } = this.dataCreateVoucherOVCM || {};
        const isQueryStatusInterval = SaleOrderBO?.extensionProperty?.IsQueryStatusInterval;
        const isShowMessageInfoThuHo = SaleOrderBO?.extensionProperty?.IsShowMessageInfo == "THUHO";
        const isShowMessageInfoTaiChinh = SaleOrderBO?.extensionProperty?.IsShowMessageInfo == "TAICHINH";
        const isShowMessageInfoBaoHiem = SaleOrderBO?.extensionProperty?.IsShowMessageInfo == "BAOHIEM";
        const transactionStatus = dataQueryStatus?.cus_AirtimeStatusCacheBO?.Status ?? null;
        actionManagerSO.getDataSearchSO(paramFilter);
        const {
            storeID,
            provinceID
        } = userInfo;
        if (IsSOAnKhang) {
            // let medicines = {}
            // if (this.state.isOutput) {
            //     medicines = await this.props.actionAnKhangNew.getMedicineRegularly(dataSaleOrder)
            // }
            // if (!helper.IsEmptyObject(medicines)) {
            //     navigation.navigate(SCREENS.BuyMedicineRegularlyScreenName, { screenNameGoBack: "OrderManagement", data: medicines });
            // }
            // else {
            //     navigation.navigate("OrderManagement");
            // }
            navigation.navigate("OrderManagement");
        }
        else if (isShowMessageInfoThuHo || isShowMessageInfoTaiChinh || isShowMessageInfoBaoHiem) {
            const { icon, color, message } = this.getTransactionStatusConfig(transactionStatus);
            if (helper.IsNonEmptyString(PromotionSaleorderID) && (isShowMessageInfoThuHo || isShowMessageInfoTaiChinh || isShowMessageInfoBaoHiem)) {
                const { reportRetail, reportVAT, reportCommon } = this.state;
                const success = await actionSaleOrder.setDataSO({
                    SaleOrderID: PromotionSaleorderID,
                    SaleOrderTypeID: 100
                });
                if (success) {
                    this.setState({
                        isVisibleModalCollection: false,
                    });
                    const data = {
                        retail: reportRetail,
                        vat: reportVAT,
                        common: reportCommon,
                    }
                    await actionManagerSO.getContentTypeReport(PromotionSaleorderID);
                    actionSaleOrder.getReportPrinterSocket(100);
                    actionCollectInstallment.updateItemSelectedPrint({
                        ...data,
                        SaleOrderBO: SaleOrderBO
                    })
                    navigation.replace("PrintCoupon");
                }
            } else {
                this.setState({
                    isVisibleModalCollection: false,
                    isVisibleCollectionTransaction: true,
                    contentCollectionTransaction: message,
                    transactionIcon: icon,
                    transactionColor: color
                });
            }
        } else if (isQueryStatusInterval) {
            this.setState({ isVisibleModalCollection: false })
            navigation.navigate("HistorySellInsurance", {
                SaleOrderID,
                CatalogID,
                ServiceGroupID
            });
        }
        else if (listDataActionVoucher === 4) {
            this.setState({ isVisibleModalMsale: true })
        }
        else {
            navigation.goBack();
        }
        hideBlockUI();
    }

    getContractInfo = (data, TotalPrePaid) => {
        showBlockUI();
        this.props.actionSaleOrder.getContractInfo(data).then(info => {
            const saleProgramInfo = {
                ContractID: info.contactID,
                PGProcessUserID: info.pgProcessUserID,
                PGProcessUserName: info.pgProcessUserName,
                packageRates: info.packageRates,
                TotalPrePaid: data.isCardPartner ? TotalPrePaid : info.totalPrePaid,
                TermLoan: info.termLoan,
                PaymentAmountMonthly: info.paymentAmountMonthly,
                CustomerName: info.customerName,
                CustomerAddress: info.customerAddress,
                customerPhone: info.customerPhone,
                customerIDCard: info.customerIDCard,
            };
            this.applyContractInfo(saleProgramInfo);
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError, [
                {
                    text: translate('saleOrderPayment.btn_skip_uppercase'),
                    onPress: () => {
                        hideBlockUI();
                        this.setState({
                            contractID: "",
                            keyboardTaps: "always"
                        });
                    }
                },
                {
                    text: translate('saleOrderPayment.btn_retry_uppercase'),
                    onPress: () => this.getContractInfo(data)
                }
            ]);
        });
    }

    applyContractInfo = (saleProgramInfo) => {
        const {
            isOutput,
            isPayCash,
        } = this.state;
        const { dataSaleOrder, actionSaleOrder } = this.props;
        const { SaleOrderSaleProgramInfo } = dataSaleOrder;
        const newSaleProgramInfo = { ...SaleOrderSaleProgramInfo, ...saleProgramInfo };
        const dataModifySaleOrder = helper.deepCopy(dataSaleOrder);
        dataModifySaleOrder.SaleOrderSaleProgramInfo = newSaleProgramInfo;
        actionSaleOrder.modifySaleOrderPayment({
            saleOrder: dataModifySaleOrder,
            isOutput,
            isPayCash
        }).then(success => {
            hideBlockUI();
            this.setState({
                saleProgramInfo: saleProgramInfo,
                keyboardTaps: "always"
            })
        }).catch(error => {
            Alert.alert(translate('common.notification_uppercase'), error.msgError, [
                {
                    text: translate('common.btn_skip'),
                    style: "default",
                    onPress: () => {
                        hideBlockUI();
                        this.setState({
                            contractID: "",
                            keyboardTaps: "always"
                        });
                    }
                },
                {
                    text: translate('common.btn_notify_try_again'),
                    style: "default",
                    onPress: () => this.applyContractInfo(saleProgramInfo)
                }
            ]);
        });
    }

    applyTransaction = (transaction, dataTransaction) => {
        const { dataSaleOrder, actionSaleOrder } = this.props;
        const { KREDIVO, CAKE } = PARTNER_ID
        const dataModifySaleOrder = helper.deepCopy(dataSaleOrder);
        dataModifySaleOrder.PaymentTransactions = [transaction];
        const isNewFollowPartner = helper.isNewFollowPartner(dataModifySaleOrder?.SaleOrderSaleProgramInfo?.PartnerInstallmentID);
        if (isNewFollowPartner) {
            const partnerCustomerName = JSON.parse(transaction.ResponseMessage)?.installmentData?.userName
            dataModifySaleOrder.SaleOrderSaleProgramInfo.ContractID = dataModifySaleOrder?.SaleOrderSaleProgramInfo?.PartnerInstallmentID == PARTNER_ID.MOMO ? transaction.PartnerTransactionID : transaction.PaymentTransactionID;
            if (`${KREDIVO},${CAKE}`.includes(String(dataModifySaleOrder?.SaleOrderSaleProgramInfo?.PartnerInstallmentID))) {
                dataModifySaleOrder.SaleOrderSaleProgramInfo.TermLoan = -1
            }
            if (!!partnerCustomerName) {
                dataModifySaleOrder.CustomerInfo.CustomerName = partnerCustomerName;
                dataModifySaleOrder.CustomerName = partnerCustomerName;
            }
        }
        // dataModifySaleOrder.CashVND = 0;
        // this.setState({ cashPayment: 0 });
        actionSaleOrder.updateDataTransaction(dataTransaction);
        this.modifySaleOrder(dataModifySaleOrder);
    }

    applyTransactionSC = (transaction, dataTransaction) => {
        const { dataSaleOrder, actionSaleOrder } = this.props;
        const dataModifySaleOrder = helper.deepCopy(dataSaleOrder);
        const moneyCard = dataModifySaleOrder.ApplyMoneyCardDetails || [];
        const cardInfo = {
            "MoneyCardID": 421,
            "MoneyCardVoucherID": transaction.ApprovalCode,
            "MoneyCard": transaction.MoneyCard,
            "PaymentTransactionID": transaction.PaymentTransactionID
        }
        moneyCard.push(cardInfo);
        dataModifySaleOrder.ApplyMoneyCardDetails = moneyCard;
        dataModifySaleOrder.SaleOrderSaleProgramInfo.ContractID = transaction.ReferenceId;
        actionSaleOrder.updateDataTransactionSC(dataTransaction);
        this.modifySaleOrder(dataModifySaleOrder);
    }

    createQR = async (data) => {

        const { dataSaleOrder, actionSaleOrder, navigation } = this.props;
        const { SaleOrderSaleProgramInfo, CustomerPhone, IsSOAnKhang } = dataSaleOrder;
        const { storeID, languageID, moduleID } = this.props.userInfo;
        const { isOutput, isPayCash, cashPayment, saleProgramInfo } = this.state;

        const isHomePayInstallment =
            SaleOrderSaleProgramInfo?.PartnerInstallmentID == PARTNER_ID.HOME_CREDIT &&
            data.transactionType?.PaymentTransactionTypeID == PAYMENT_PARTNER_ID.HOME_PAY_LATER;

        if (isHomePayInstallment) {
            Alert.alert(
                "",
                "Phương thức thanh toán trả góp HomeCredit không hỗ trợ loại thanh toán qua HomePayLater. Vui lòng chọn loại thanh toán khác !!!",
                [{ text: "OK", onPress: hideBlockUI }]
            );
            return;
        }

        try {
            showBlockUI();

            const baseBody = {
                loginStoreId: storeID,
                languageID,
                moduleID,
            };

            if (
                !helper.IsNonEmptyString(CustomerPhone) && isNewFollowPayment(data.transactionType?.PaymentTransactionTypeID)
            ) {
                return Alert.alert("", "Đơn hàng không có SĐT không thể thanh toán BUYNOW PAYLATER. Vui lòng liên hệ NH!", [{ text: "OK", onPress: hideBlockUI }]);
            }
            if (!this.extraDataGenQR.current && helper.IsNonEmptyString(CustomerPhone) && isNewFollowPayment(data.transactionType?.PaymentTransactionTypeID)) {
                const profile = await actionShoppingCartCreator.getCustomerProfile({ ...baseBody, phoneNumber: CustomerPhone, typeProfile: TYPE_PROFILE.CUSTOMER })
                if (!profile[0]?.profileId) {
                    return Alert.alert("", "Đơn hàng không có profile không được phép thanh toán", [{ text: "OK", onPress: hideBlockUI }]);
                }
                const body = {
                    ...baseBody,
                    "paymentTransactionTypeID": data.transactionType?.PaymentTransactionTypeID,
                    "customerID": profile[0]?.profileId
                }
                const extraData = await actionSaleOrderCreator.checkSendOTPGenQRCode(body);
                this.extraDataGenQR.current = {
                    ...(extraData || {}),
                    termLoan: saleProgramInfo?.TermLoan
                };

                if (!helper.IsEmptyObject(extraData)) {
                    hideBlockUI();
                    this.dataTempCreateQR.current = data;
                    this.OTPSheetRef.current?.present();
                    return;
                }
            }
            if (!helper.IsEmptyObject(this.extraDataGenQR.current)) {
                data.extraData = this.extraDataGenQR.current;
            }


            const qrInfo = await actionSaleOrder.getDataQRPayment(data);
            const { cus_CountIdentifyID, cus_IdentifyID } = qrInfo;

            if (cus_CountIdentifyID <= 1) {
                hideBlockUI();

                if (IsSOAnKhang) {
                    const newCashVND = Math.max(cashPayment - data.amount, 0);
                    const updatedSaleOrder = { ...dataSaleOrder, CashVND: newCashVND };

                    this.setState({ cashPayment: newCashVND });
                    this.modifySaleOrder(updatedSaleOrder, true);
                }

                navigation.navigate("QRPayment", { isOutput, isPayCash });
            } else {
                const newData = { ...data, transactionType: { ...data.transactionType, cus_IdentifyID } };
                const { CustomerDisplayName } = newData.transactionType;

                Alert.alert(
                    "",
                    `Đơn hàng đang tạo có ${cus_CountIdentifyID} mã khuyến mãi khi quét Qrcode ${CustomerDisplayName} nhưng chỉ áp dụng được 1 mã khuyến mãi cho 1 đơn, bạn có muốn tiếp tục tạo?`,
                    [
                        { text: "YES", onPress: () => this.createQR(newData) },
                        { text: "NO", onPress: hideBlockUI },
                    ]
                );
            }
        } catch (error) {
            console.log("🚀 ~ SaleOrderPayment ~ createQR= ~ error:", error)
            hideBlockUI();
            Alert.alert("", error || "Đã có lỗi xảy ra. Vui lòng thử lại!", [{ text: "OK", onPress: hideBlockUI }]);
        }
    };


    createSC = (data) => {
        const {
            isOutput,
            isPayCash
        } = this.state;
        showBlockUI();
        this.props.actionSaleOrder.getDataSCPayment(data).then((scInfo) => {
            const { IdentifyCount, IdentifyID } = scInfo;
            if (IdentifyCount <= 1) {
                hideBlockUI();
                this.props.navigation.navigate("SmartCardPayment", { isOutput, isPayCash });
            } else {
                const newData = helper.deepCopy(data);
                newData.identifyID = IdentifyID;
                Alert.alert('', `Đơn hàng đang tạo có ${IdentifyCount} mã khuyến mãi khi cà thẻ SmartPOS nhưng chỉ áp dụng được 1 mã khuyến mãi cho 1 đơn, bạn có muốn tiếp tục tạo?`, [
                    {
                        text: 'YES',
                        onPress: () => { this.createSC(newData) }
                    },
                    {
                        text: 'NO',
                        onPress: hideBlockUI
                    }
                ]);
            }
        }).catch(msgError => {
            Alert.alert("", msgError, [{
                text: "OK",
                onPress: hideBlockUI
            }]);
        })
    }

    getQRInfo = (info) => {
        const {
            isOutput,
            isPayCash,
        } = this.state;
        this.props.actionSaleOrder.getQRInfo(info).then(success => {
            this.props.navigation.navigate("QRPayment", { isOutput, isPayCash });
        })
    }

    getSCInfo = (info) => {
        const {
            isOutput,
            isPayCash,
        } = this.state;
        this.props.actionSaleOrder.getSCInfo(info).then(success => {
            this.props.navigation.navigate("SmartCardPayment", { isOutput, isPayCash });
        })
    }

    getDataPhoneVoucher = () => {
        const { dataSaleOrder: {
            CustomerPhone,
            SaleOrderID
        } } = this.props;
        showBlockUI();
        this.props.actionSaleOrder.getDataPhoneVoucher({
            "customerPhone": CustomerPhone,
            "saleOrderID": SaleOrderID,
        }).then(data => {
            hideBlockUI();
            this.setState({ dataPhoneVoucher: data, isVisibleVoucher: true });
        }).catch(msgError => {
            Alert.alert("", msgError, [
                {
                    text: "OK",
                    style: "default",
                    onPress: hideBlockUI
                }
            ]);
        })
    }

    showBlock = () => {
        this.setState({ isBlockUI: true });
    }

    hideBlock = () => {
        this.setState({ isBlockUI: false });
    }

    setVerify = () => {
        this.setState({ isVerify: true });
    }

    applyEncryptVoucher = (info) => () => {
        const {
            dataSaleOrder,
        } = this.props;
        const {
            isOutput,
            isPayCash
        } = this.state;
        this.setState({ isBlockUI: true });
        this.props.actionSaleOrder.applyGiftVoucher({
            saleOrder: dataSaleOrder,
            imei: "",
            imeiEncrypt: info.IMEIEncrypt,
            isOutput,
            isPayCash
        }).then(success => {
            this.setState({
                isBlockUI: false,
                isVisibleVoucher: false
            });
        }).catch(msgError => {
            Alert.alert("", msgError, [
                {
                    text: "OK",
                    style: "default",
                    onPress: () => {
                        this.setState({ isBlockUI: false });
                    }
                }
            ]);
        });
    }

    /*  */
    printAllRequestFW = async (allPromise) => {
        try {
            for (const body of allPromise) {
                await actionSaleOrderCreator.printBillVoucherBit(body);
                await helper.sleep(1000);
            }
            this.showEbillQTV();
        } catch (msgError) {
            Alert.alert(translate('common.notification'), msgError, [
                {
                    text: "OK",
                    style: "default",
                    onPress: this.showEbillQTV
                }
            ]);
        }
    }

    printAllRequestSocket = async (allPromise) => {
        try {
            for (const { data, ip, delay } of allPromise) {
                await printSocket(data, ip);
                if (delay > 0) {
                    await helper.sleep(delay);
                }
            }
            this.showEbillQTV();
        } catch (msgError) {
            Alert.alert(translate('common.notification'), 'Quá trình in lỗi. Vui lòng vào Tool Quản lý yêu cầu xuất hàng thực hiện chức năng "In lại" để thao tác tiếp.', [
                {
                    text: "OK",
                    style: "default",
                    onPress: this.showEbillQTV
                }
            ]);
        }
    }

    getContentHtml = (info) => {
        const { dataSaleOrder: { VoucherConcernType } } = this.props;
        showBlockUI();
        this.props.actionSaleOrder.getReportContentBase64(info).then(data => {
            this.ebillQTV = new Set(data?.reportSendedLoyalty);
            if (this.ebillQTV.size > 0) {
                info.reportContents = info.reportContents.filter(ele => !this.ebillQTV.has(ele.ReportContent))
            }
            this.onConvertHTML(data, info.reportContents);
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError, [
                {
                    text: translate('saleOrderPayment.btn_skip_uppercase'),
                    style: "default",
                    onPress: () => {
                        hideBlockUI();
                        this.goBack();
                    }
                },
                {
                    text: translate('saleOrderPayment.btn_retry_uppercase'),
                    style: "default",
                    onPress: () => this.getContentHtml(info)
                }
            ]);
        })
    }

    requestConvertHtml = async (data, reportContents) => {
        try {
            const requestConvert = [];
            const {
                EBillContentHTML,
                GiftVCIssueContentPrintHTML,
                EBillContentIncomeHTML,
                OutTransContentHTML,
                DosageContentHTML,
                InfoBatchNOContentHTML,
                AirtimeTransferContentHTML
            } = data;
            for (const ele of reportContents) {
                const { ReportContent } = ele;
                switch (ReportContent) {
                    case 'EBillContent':
                        if (helper.IsNonEmptyString(EBillContentHTML)) {
                            const eBillContent = await convertHtml2Image(EBillContentHTML, H_BILL);
                            requestConvert.push([eBillContent]);
                        }
                        break;
                    case 'EBillContentIncome':
                        if (helper.IsNonEmptyString(EBillContentIncomeHTML)) {
                            const eBillContentIncome = await convertHtml2Image(EBillContentIncomeHTML, H_BILL);
                            requestConvert.push([eBillContentIncome]);
                        }
                        break;
                    case 'GiftVCIssueContentPrint':
                        if (helper.IsNonEmptyString(GiftVCIssueContentPrintHTML)) {
                            const giftData = GiftVCIssueContentPrintHTML.split(`<br /><br />`);
                            let giftVCIssueContentPrint = [];
                            for (const giftHtml of giftData) {
                                const giftContent = await convertHtml2Image(giftHtml, H_VOUCHER);
                                giftVCIssueContentPrint.push(giftContent);
                            }
                            requestConvert.push(giftVCIssueContentPrint);
                        }
                        break;
                    case 'KeySoftwareContent':
                        if (helper.IsNonEmptyString(OutTransContentHTML)) {
                            const keySoftwareContent = await convertHtml2Image(OutTransContentHTML, H_KEY);
                            requestConvert.push([keySoftwareContent]);
                        }
                        break;
                    case 'DosageContent':
                        if (helper.IsNonEmptyString(DosageContentHTML)) {
                            const dosageContent = await convertHtml2Image(DosageContentHTML, H_VOUCHER);
                            requestConvert.push([dosageContent]);
                        }
                        break;
                    case 'InfoBatchNOContent':
                        if (helper.IsNonEmptyString(InfoBatchNOContentHTML)) {
                            const batchContent = await convertHtml2Image(InfoBatchNOContentHTML, H_VOUCHER);
                            requestConvert.push([batchContent]);
                        }
                        break;
                    case 'AirtimeTransferContent':
                        if (helper.IsNonEmptyString(AirtimeTransferContentHTML)) {
                            const airtimeTransferContent = await convertHtml2Image(AirtimeTransferContentHTML, H_BILL);
                            requestConvert.push([airtimeTransferContent]);
                        }
                        break;
                    default:
                        console.log(ele);
                        break;
                }
            }
            return requestConvert;
        } catch (error) {
            console.log('convertHtml2Image', error);
            Alert.alert("", 'Quá trình in lỗi. Vui lòng vào chức năng "In lại" để thao tác tiếp.', [
                {
                    text: "OK",
                    style: "default",
                    onPress: this.showEbillQTV
                }
            ]);
        }
    }

    onConvertHTML = async (data, reportContents) => {
        const dataBit = await this.requestConvertHtml(data, reportContents);
        if (helper.IsNonEmptyArray(dataBit)) {
            this.onPrintBillHTML(data, reportContents, dataBit);
        }
        else {
            this.showEbillQTV();
        }
    }

    getPrintServiceHTML = (report, info, type) => {
        const { userInfo: { userName } } = this.props;
        let body = {
            "Printer": report.PRINTERSHORTNAME,
            "Value": info,
            "Type": type,
            "User": userName,
            "Status": "Payment"
        }
        return body;
    }

    getPrintServiceSocket = (report, info) => {
        let body = {
            "ip": report.IPPRINTER,
            "delay": report.DELAY,
            "data": info
        }
        return body;
    }

    getPrintHTMLRequestAPI = (data, reportContents, dataBit) => {
        const { dataSO: { SaleOrderID } } = this.props;
        const { reportRetail } = this.state;
        const requestAPI = [];
        const {
            eBillContentPrinterTypeID,
            eBillContentIncomePrinterTypeID,
            giftVCIssueContentPrinterTypeID,
            outTransContentPrinterTypeID,
            GiftVCIssueContentPrintHTML,
            dosageContentPrinterTypeID,
            infoBatchNOContentPrinterTypeID,
            airtimeTransferContentPrinterTypeID
        } = data;
        reportContents.forEach((ele, index) => {
            const { ReportContent } = ele;
            const dataConvert = dataBit[index];
            let report = {};
            switch (ReportContent) {
                case 'EBillContent':
                    report = this.getReport(eBillContentPrinterTypeID);
                    break;
                case 'EBillContentIncome':
                    report = this.getReport(eBillContentIncomePrinterTypeID);
                    break;
                case 'GiftVCIssueContentPrint':
                    report = this.getReport(giftVCIssueContentPrinterTypeID);
                    break;
                case 'KeySoftwareContent':
                    report = this.getReport(outTransContentPrinterTypeID);
                    break;
                case 'DosageContent':
                    report = this.getReport(dosageContentPrinterTypeID);
                    break;
                case 'InfoBatchNOContent':
                    report = this.getReport(infoBatchNOContentPrinterTypeID);
                    break;
                case 'AirtimeTransferContent':
                    report = this.getReport(airtimeTransferContentPrinterTypeID);
                    break;
                default:
                    report = reportRetail;
                    break;
            }
            if (helper.IsNonEmptyArray(dataConvert)) {
                dataConvert.forEach(info => {
                    if (!report.IPPRINTER) {
                        report.IPPRINTER = "*************";
                        report.DELAY = 500;
                    }
                    const printService = this.getPrintServiceSocket(report, info, ReportContent);
                    requestAPI.push(printService);
                })
                const isPMH = (ReportContent == 'GiftVCIssueContentPrint');
                if (isPMH && helper.IsNonEmptyString(GiftVCIssueContentPrintHTML)) {
                    actionSaleOrderCreator.insertLogPrintGift(SaleOrderID);
                }
            }
        })
        return requestAPI;
    }

    onPrintBillHTML = (data, reportContents, dataBit) => {
        const requestAPI = this.getPrintHTMLRequestAPI(data, reportContents, dataBit);
        if (helper.IsNonEmptyArray(requestAPI)) {
            this.printAllRequestSocket(requestAPI);
        }
        else {
            this.showEbillQTV();
        }
    }

    /*  */
    printAllRequestBHX = async (allPromise) => {
        try {
            for (const body of allPromise) {
                await actionSaleOrderCreator.printBillVoucherBHX(body);
                await helper.sleep(1000);
            }
            this.showEbillQTV();
        } catch (msgError) {
            Alert.alert(translate('common.notification'), msgError, [
                {
                    text: "OK",
                    style: "default",
                    onPress: this.showEbillQTV
                }
            ]);
        }
    }

    getContentHtmlBHX = (info) => {
        showBlockUI();
        this.props.actionSaleOrder.getReportContentBase64(info).then(data => {
            this.onConvertHTMLBHX(data, info.reportContents);
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError, [
                {
                    text: translate('saleOrderPayment.btn_skip_uppercase'),
                    style: "default",
                    onPress: hideBlockUI
                },
                {
                    text: translate('saleOrderPayment.btn_retry_uppercase'),
                    style: "default",
                    onPress: () => this.getContentHtmlBHX(info)
                }
            ]);
        })
    }

    requestConvertHtmlBHX = (data, reportContents) => {
        try {
            const requestConvert = [];
            const {
                EBillContentHTML,
                GiftVCIssueContentPrintHTML,
                EBillContentIncomeHTML,
                OutTransContentHTML,
                DosageContentHTML,
                InfoBatchNOContentHTML,
                AirtimeTransferContentHTML
            } = data;
            for (const ele of reportContents) {
                const { ReportContent } = ele;
                switch (ReportContent) {
                    case 'EBillContent':
                        if (helper.IsNonEmptyString(EBillContentHTML)) {
                            const eBillContent = convertToBase64(EBillContentHTML);
                            requestConvert.push(eBillContent);
                        }
                        break;
                    case 'EBillContentIncome':
                        if (helper.IsNonEmptyString(EBillContentIncomeHTML)) {
                            const eBillContentIncome = convertToBase64(EBillContentIncomeHTML);
                            requestConvert.push(eBillContentIncome);
                        }
                        break;
                    case 'GiftVCIssueContentPrint':
                        if (helper.IsNonEmptyString(GiftVCIssueContentPrintHTML)) {
                            const giftContent = convertToBase64(GiftVCIssueContentPrintHTML);
                            requestConvert.push(giftContent);
                        }
                        break;
                    case 'KeySoftwareContent':
                        if (helper.IsNonEmptyString(OutTransContentHTML)) {
                            const keySoftwareContent = convertHtml2Image(OutTransContentHTML);
                            requestConvert.push(keySoftwareContent);
                        }
                        break;
                    case 'DosageContent':
                        if (helper.IsNonEmptyString(DosageContentHTML)) {
                            const dosageContent = convertToBase64(DosageContentHTML);
                            requestConvert.push(dosageContent);
                        }
                        break;
                    case 'InfoBatchNOContent':
                        if (helper.IsNonEmptyString(InfoBatchNOContentHTML)) {
                            const batchContent = convertToBase64(InfoBatchNOContentHTML);
                            requestConvert.push(batchContent);
                        }
                        break;
                    case 'AirtimeTransferContent':
                        if (helper.IsNonEmptyString(AirtimeTransferContentHTML)) {
                            const airtimeTransferContent = convertToBase64(AirtimeTransferContentHTML);
                            requestConvert.push(airtimeTransferContent);
                        }
                        break;
                    default:
                        console.log(ele);
                        break;
                }
            }
            return requestConvert;
        } catch (error) {
            console.log('convertHtml2Image', error);
            Alert.alert("", 'Quá trình in lỗi. Vui lòng vào chức năng "In lại" để thao tác tiếp.', [
                {
                    text: "OK",
                    style: "default",
                    onPress: this.showEbillQTV
                }
            ]);
        }
    }

    onConvertHTMLBHX = (data, reportContents) => {
        const dataContent = this.requestConvertHtmlBHX(data, reportContents);
        if (helper.IsNonEmptyArray(dataContent)) {
            this.onPrintBillHTMLBHX(data, reportContents, dataContent);
        }
        else {
            hideBlockUI();
        }
    }

    getPrintServiceHTMLBHX = (report, info, type) => {
        let body = {
            "printerName": report.PRINTERNAME,
            "docData": info
        }
        return body;
    }

    getPrintHTMLRequestAPIBHX = (data, reportContents, dataBit) => {
        const { reportRetail } = this.state;
        const requestAPI = [];
        const {
            eBillContentPrinterTypeID,
            eBillContentIncomePrinterTypeID,
            giftVCIssueContentPrinterTypeID,
            outTransContentPrinterTypeID,
            dosageContentPrinterTypeID,
            infoBatchNOContentPrinterTypeID,
            airtimeTransferContentPrinterTypeID
        } = data;
        reportContents.forEach((ele, index) => {
            const { ReportContent } = ele;
            const dataConvert = dataBit[index];
            let report = {};
            switch (ReportContent) {
                case 'EBillContent':
                    report = this.getReport(eBillContentPrinterTypeID);
                    break;
                case 'EBillContentIncome':
                    report = this.getReport(eBillContentIncomePrinterTypeID);
                    break;
                case 'GiftVCIssueContentPrint':
                    report = this.getReport(giftVCIssueContentPrinterTypeID);
                    break;
                case 'KeySoftwareContent':
                    report = this.getReport(outTransContentPrinterTypeID);
                    break;
                case 'DosageContent':
                    report = this.getReport(dosageContentPrinterTypeID);
                    break;
                case 'InfoBatchNOContent':
                    report = this.getReport(infoBatchNOContentPrinterTypeID);
                    break;
                case 'AirtimeTransferContent':
                    report = this.getReport(airtimeTransferContentPrinterTypeID);
                    break;
                default:
                    report = reportRetail;
                    break;
            }
            if (helper.IsNonEmptyString(dataConvert)) {
                const printService = this.getPrintServiceHTMLBHX(report, dataConvert, ReportContent);
                requestAPI.push(printService);
            }
        })
        return requestAPI;
    }

    onPrintBillHTMLBHX = (data, reportContents, dataBit) => {
        const requestAPI = this.getPrintHTMLRequestAPIBHX(data, reportContents, dataBit);
        if (helper.IsNonEmptyArray(requestAPI)) {
            this.printAllRequestBHX(requestAPI);
        }
        else {
            hideBlockUI();
        }
    }

    switchPrintToPDF = () => {
        this.dataPrint.isFitContent = false;
        this.dataPrint.isGetContentHTML = false;
        this.getContentBase64PDF(this.dataPrint);
    }
}

const mapStateToProps = function (state) {
    return {
        dataSaleOrder: state.saleOrderPaymentReducer.dataSaleOrder,
        dataSO: state.saleOrderPaymentReducer.dataSO,
        printerRetail: state.saleOrderPaymentReducer.printerRetail,
        printerVAT: state.saleOrderPaymentReducer.printerVAT,
        printerCommon: state.saleOrderPaymentReducer.printerCommon,
        defaultReport: state.saleOrderPaymentReducer.defaultReport,
        stateSaleOrder: state.saleOrderPaymentReducer.stateSaleOrder,
        statePrinter: state.saleOrderPaymentReducer.statePrinter,
        dataQRTransaction: state.saleOrderPaymentReducer.dataQRTransaction,
        stateQRTransaction: state.saleOrderPaymentReducer.stateQRTransaction,
        dataSCTransaction: state.saleOrderPaymentReducer.dataSCTransaction,
        stateSCTransaction: state.saleOrderPaymentReducer.stateSCTransaction,
        paramFilter: state.managerSOReducer.paramFilter,
        userInfo: state.userReducer,
        loyaltyPercentBonus: state.loyaltyReducer.loyaltyPercentBonus,
        saleScenarioTypeID: state.specialSaleProgramReducer.saleScenarioTypeID,
        PM_PreOrder_SaleOrderTypeList: state.appSettingReducer.PM_PreOrder_SaleOrderTypeList,
        dataQueryStatus: state.collectionManagerReducer.dataQueryStatus,
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),
        actionManagerSO: bindActionCreators(actionManagerSOCreator, dispatch),
        actionCard: bindActionCreators(actionCardCreator, dispatch),
        actionSale: bindActionCreators(actionSaleCreator, dispatch),
        actionLoyalty: bindActionCreators(actionLoyaltyCreator, dispatch),
        actionCollectionManager: bindActionCreators(actionCollectionManagerCreator, dispatch),
        actionAnKhangNew: bindActionCreators(actionAnKhangNewCreator, dispatch),
        actionInsuranceBrightside: bindActionCreators(actionInsuranceBrightsideCreator, dispatch),
        actionHealthInsurance: bindActionCreators(actionHealthInsuranceCreator, dispatch),
        actionBankAirtimeService: bindActionCreators(actionBankAirtimeServiceCreator, dispatch),
        actionCollectInstallment: bindActionCreators(actionCollectInstallmentCreator, dispatch),
        actionCollection: bindActionCreators(actionCollectionCreator, dispatch),
    }
}

const SaleOrderPaymentWithFocused = (props) => {
    const isFocused = useIsFocused();
    return <SaleOrderPayment {...props} isFocused={isFocused} />
}

export default connect(mapStateToProps, mapDispatchToProps)(SaleOrderPaymentWithFocused);


const getGiftPromotion = (giftProducts, keyGiftRemove) => {
    return giftProducts.map(ele => {
        const { SaleOrderDetailID } = ele;
        if (keyGiftRemove.has(SaleOrderDetailID)) {
            ele.IsOutput = false;
        }
        else {
            ele.IsOutput = true;
        }
        return ele;
    })
}

const getKeyImeiProduct = (mainProducts, saleProducts, giftProducts) => {
    const dataProducts = [
        ...mainProducts,
        ...saleProducts,
        ...giftProducts
    ]
    const keyImeiProduct = new Set();
    dataProducts.forEach(ele => {
        const { IMEI } = ele;
        if (helper.IsNonEmptyString(IMEI)) {
            const imei = IMEI.trim();
            keyImeiProduct.add(imei);
        }
    });
    return keyImeiProduct;
}

const getStatus = (status) => {
    switch (status) {
        case "WAITED":
            return "Đồng ý tham gia"
        case "CANCELED":
            return "Từ chối tham gia"
        case "JOINED":
            return "Đã check-in"
        default:
            return ""
    }
}

const isNewFollowPayment = (transactionTypeID) => {
    const { HOME_PAY_LATER, KREDIVO, CAKE, QTV, TPBanhEVO } = PAYMENT_PARTNER_ID
    return [HOME_PAY_LATER, KREDIVO, CAKE, QTV, TPBanhEVO].includes(Number(transactionTypeID))
}