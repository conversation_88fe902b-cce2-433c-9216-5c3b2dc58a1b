import React, { useState } from 'react';
import {
    View,
    TouchableOpacity,
    TextInput,
    Keyboard
} from 'react-native';
import {
    Icon,
    Button,
    BarcodeCamera,
    MyText,
    showBlockUI,
    hideBlockUI
} from "@components";
import { constants } from '@constants';
import { helper } from "@common";
import { translate } from '@translate';
import { COLORS } from "@styles";

const GiftProduct = ({
    product,
    updateImeiProduct,
    updateSerialProduct,
    updateImeiSticker,
    keyGiftRemove,
    updateKeyRemove,
    checkImei,
    checkSerial,
    keyImeiProduct,
    onPressLotDate
}) => {
    const {
        IsRequestIMEI,
        IMEIByLoad,
        IMEI,
        AllowInputIMEISerial,
        SerialFormatExp1,
        SerialFormatExp2,
        IMEISerialExp1,
        IMEISerialExp2,
        IsInputIMEI,
        InputIMEIList,
        ProductName,
        Quantity,
        QuantityUnitName,
        SaleOrderDetailID,
        OutputStoreID,
        ProductID,
        InventoryStatusID,
        SaleOrderID,
        IsGiftPromotionProductVirtual,
        OutputTypeID,
        isGiftVoucher,
        cus_IsRequiredBatchNO,
        OrderTypeID,
        IsDisableOutput
    } = product;
    const [imei, setImei] = useState("");
    const [errImei, setErrImei] = useState("");
    const [serialExp1, setSerialExp1] = useState("");
    const [errExp1, setErrExp1] = useState("");
    const [serialExp2, setSerialExp2] = useState("");
    const [errExp2, setErrExp2] = useState("");
    const [sticker, setSticker] = useState("");
    const [errSticker, setErrSticker] = useState("");
    const [typeScan, setTypeScan] = useState("");
    const isRemove = keyGiftRemove.has(SaleOrderDetailID);
    const isImeiLoad = IsRequestIMEI && !!IMEIByLoad;
    const isImei = IsRequestIMEI && !IMEIByLoad;
    const isRequireImei = isImei && !IMEI;
    const isInputImei = isImei && !!IMEI
    const isSerial1 = AllowInputIMEISerial && !!SerialFormatExp1;
    const isRequireSerial1 = isSerial1 && !IMEISerialExp1;
    const isInputSerial1 = isSerial1 && !!IMEISerialExp1;
    const isSerial2 = AllowInputIMEISerial && !!SerialFormatExp2;
    const isRequireSerial2 = isSerial2 && !IMEISerialExp2;
    const isInputSerial2 = isSerial2 && !!IMEISerialExp2;
    const isSticker = IsInputIMEI && helper.IsNonEmptyArray(InputIMEIList);
    const isInputSticker = helper.isArray(InputIMEIList) && (InputIMEIList.length < Quantity);
    const isScan = (typeScan != "");
    const disabled = isImeiLoad || IsGiftPromotionProductVirtual;
    const imeiPMH = isGiftVoucher ? "xxx" : IMEIByLoad;

    const onChange = (text) => {
        if (regExpIMEI.test(text)) {
            setImei(text);
            setErrImei("");
        }
    }

    const onChangeExp1 = (text) => {
        if (regExpIMEI.test(text)) {
            setSerialExp1(text);
            setErrExp1("");
        }
    }

    const onChangeExp2 = (text) => {
        if (regExpIMEI.test(text)) {
            setSerialExp2(text);
            setErrExp2("");
        }
    }

    const onChangeSticker = (text) => {
        if (regExpIMEI.test(text)) {
            setSticker(text);
        }
    }

    const onClose = () => {
        setTypeScan("");
    }

    const onOpen = (type) => () => {
        setTypeScan(type);
    }

    const checkInputImei = (value) => {
        if (keyImeiProduct.has(value)) {
            setErrImei(translate('saleOrderPayment.IMEI_exists_order'));
        }
        else {
            showBlockUI();
            checkImei({
                "imei": value,
                "productID": ProductID,
                "inventoryStatusID": InventoryStatusID,
                "storeID": OutputStoreID,
                "saleOrderID": SaleOrderID,
                "saleOrderTypeID": OrderTypeID,
                "isAdditionalPromotion": OutputTypeID === 4, //Khuyến mãi tặng quà
                "outputTypeID": OutputTypeID
            }).then(success => {
                hideBlockUI();
                updateImeiProduct(value);
            }).catch(msgError => {
                hideBlockUI();
                setErrImei(msgError);
            });
        }
    }

    const validateImei = () => {
        const isValidate = helper.IsNonEmptyString(imei);
        if (isValidate) {
            checkInputImei(imei);
        }
        else {
            setErrImei(translate('saleOrderPayment.please_enter_IMEI'));
        }
    }

    const onClearImei = () => {
        setImei("");
        updateImeiProduct("");
    }

    const checkInputSerial1 = (value) => {
        const newProduct = helper.deepCopy(product);
        newProduct.IMEISerialExp1 = value;
        showBlockUI();
        checkSerial(newProduct, TYPE_SCAN.SERIAL1).then(success => {
            hideBlockUI();
            updateSerialProduct(newProduct);
        }).catch(msgError => {
            hideBlockUI();
            setErrExp1(msgError);
        })
    }

    const validateSerial1 = () => {
        const isValidate = helper.IsNonEmptyString(serialExp1);
        if (isValidate) {
            checkInputSerial1(serialExp1);
        }
        else {
            setErrExp1(translate('saleOrderPayment.please_enter_SERIAL'));
        }
    }

    const onClearSerial1 = () => {
        const newProduct = helper.deepCopy(product);
        newProduct.IMEISerialExp1 = "";
        updateSerialProduct(newProduct);
        setSerialExp1("");
        setErrExp1("");
    }

    const checkInputSerial2 = (value) => {
        const newProduct = helper.deepCopy(product);
        newProduct.IMEISerialExp2 = value;
        showBlockUI();
        checkSerial(newProduct, TYPE_SCAN.SERIAL2).then(success => {
            hideBlockUI();
            updateSerialProduct(newProduct);
        }).catch(msgError => {
            hideBlockUI();
            setErrExp2(msgError);
        })
    }

    const validateSerial2 = () => {
        const isValidate = helper.IsNonEmptyString(serialExp2);
        if (isValidate) {
            checkInputSerial2(serialExp2);
        }
        else {
            setErrExp2(translate('saleOrderPayment.please_enter_SERIAL'));
        }
    }

    const onClearSerial2 = () => {
        const newProduct = helper.deepCopy(product);
        newProduct.IMEISerialExp2 = "";
        updateSerialProduct(newProduct);
        setSerialExp2("");
        setErrExp2("");
    }

    const checkInputSticker = (sticker) => {
        const newImeiList = [...InputIMEIList];
        newImeiList.push(sticker);
        updateImeiSticker(newImeiList);
    }

    const validateSticker = () => {
        const isValidate = helper.IsNonEmptyString(sticker);
        if (isValidate) {
            checkInputSticker(sticker);
        }
        else {
            setErrSticker(translate('saleOrderPayment.please_enter_IMEI'));
        }
    }

    const onClearSticker = () => {
        updateImeiSticker([]);
        setSticker("");
        setErrSticker("");
    }

    const onCheckBarcode = (barcode, type) => {
        setTypeScan("");
        switch (type) {
            case TYPE_SCAN.IMEI:
                setImei(barcode);
                setErrImei("");
                checkInputImei(barcode);
                break;
            case TYPE_SCAN.SERIAL1:
                setSerialExp1(barcode);
                setErrExp1("");
                checkInputSerial1(barcode);
                break;
            case TYPE_SCAN.SERIAL2:
                setSerialExp2(barcode);
                setErrExp2("");
                checkInputSerial2(barcode);
                break;
            case TYPE_SCAN.STICKER:
                checkInputSticker(barcode);
                setSticker(barcode);
                setErrSticker("");
                break;
            default:
                console.log(barcode, type);
                break;
        }
    }

    const onPressGift = () => {
        if (isRemove) {
            keyGiftRemove.delete(SaleOrderDetailID);
        }
        else {
            keyGiftRemove.add(SaleOrderDetailID);
        }
        updateKeyRemove(keyGiftRemove);
    }

    return (
        <View style={{
            width: constants.width,
            backgroundColor: COLORS.bgF5F5F5,
            paddingVertical: 5
        }}>
            <View style={{
                flexDirection: "row",
                width: constants.width,
                paddingHorizontal: 10,
                paddingVertical: isRequireImei ? 6 : 0,
                minHeight: 28,
                alignItems: "center",
            }}
            >
                <TouchableOpacity
                    style={{ flexDirection: "row" }}
                    onPress={onPressGift}
                    activeOpacity={1}
                    disabled={disabled || IsDisableOutput}
                >
                    {
                        disabled
                            ? <Icon
                                iconSet={"Ionicons"}
                                name={"checkmark"}
                                color={COLORS.ic147EFB}
                                size={16}
                                style={{ marginTop: 2 }}
                            />
                            : <Icon
                                iconSet={"Ionicons"}
                                name={isRemove ? "square-outline" : "checkbox-outline"}
                                color={isRemove ? COLORS.ic333333 : COLORS.ic147EFB}
                                size={14}
                                style={{ marginTop: 2 }}
                            />
                    }
                    <View style={{
                        width: constants.width - 96,
                        marginLeft: 5
                    }}>
                        <MyText
                            text={ProductName}
                            style={{
                                color: COLORS.txt288AD6,
                                fontWeight: 'bold'
                            }}>
                            <MyText
                                text={` (${translate('saleOrderPayment.quantity')}: ${Quantity})`}
                                style={{
                                    color: COLORS.txt333333,
                                    fontWeight: 'normal'
                                }} />
                        </MyText>
                        {
                            isImeiLoad &&
                            <MyText
                                text={translate('saleOrderPayment.IMEI')}
                                style={{
                                    color: COLORS.txt555555,
                                    fontWeight: 'bold',
                                    marginTop: 4
                                }}>
                                <MyText
                                    text={imeiPMH}
                                    style={{
                                        color: COLORS.txt333333,
                                        fontWeight: 'normal'
                                    }} />
                            </MyText>
                        }
                    </View>
                </TouchableOpacity>
                {cus_IsRequiredBatchNO && (
                    <TouchableOpacity
                        onPress={onPressLotDate}
                        style={{
                            marginHorizontal: 8,
                            justifyContent: 'center'
                        }}>
                        <MyText
                            text={translate('pharmacy.lot_date')}
                            style={{
                                color: COLORS.txt0000FF,
                                textDecorationLine: 'underline'
                            }}
                        />
                    </TouchableOpacity>
                )}
            </View>
            {
                isRequireImei &&
                <InputScan
                    value={imei}
                    onChange={onChange}
                    onScan={onOpen(TYPE_SCAN.IMEI)}
                    onCheck={validateImei}
                    placeholder={translate('saleOrderPayment.placeholder_enter_IMEI')}
                    error={errImei}
                    key={TYPE_SCAN.IMEI}
                />
            }
            {
                isInputImei &&
                <InputImei
                    name={"IMEI"}
                    value={IMEI}
                    onClear={onClearImei}
                />
            }
            {
                isRequireSerial1 &&
                <InputScan
                    value={serialExp1}
                    onChange={onChangeExp1}
                    onScan={onOpen(TYPE_SCAN.SERIAL1)}
                    onCheck={validateSerial1}
                    placeholder={translate('saleOrderPayment.placeholder_enter_serial_one')}
                    error={errExp1}
                    key={TYPE_SCAN.SERIAL1}
                />
            }
            {
                isInputSerial1 &&
                <InputImei
                    name={"SERIAL 1"}
                    value={IMEISerialExp1}
                    onClear={onClearSerial1}
                />
            }
            {
                isRequireSerial2 &&
                <InputScan
                    value={serialExp2}
                    onChange={onChangeExp2}
                    onScan={onOpen(TYPE_SCAN.SERIAL2)}
                    onCheck={validateSerial2}
                    placeholder={translate('saleOrderPayment.placeholder_enter_serial_two')}
                    error={errExp2}
                    key={TYPE_SCAN.SERIAL2}
                />
            }
            {
                isInputSerial2 &&
                <InputImei
                    name={"SERIAL 2"}
                    value={IMEISerialExp2}
                    onClear={onClearSerial2}
                />
            }
            {
                isSticker &&
                <InputImei
                    name={"IMEI"}
                    value={InputIMEIList.join(", ")}
                    onClear={onClearSticker}
                />
            }
            {
                isInputSticker &&
                <InputScan
                    value={sticker}
                    onChange={onChangeSticker}
                    onScan={onOpen(TYPE_SCAN.STICKER)}
                    onCheck={validateSticker}
                    placeholder={"Nhập IMEI bảo hành"}
                    error={errSticker}
                    key={TYPE_SCAN.STICKER}
                />
            }
            {
                isScan &&
                <BarcodeCamera
                    isVisible={isScan}
                    closeCamera={onClose}
                    resultScanBarcode={(barcode) => {
                        if (regExpIMEI.test(barcode)) {
                            onCheckBarcode(barcode, typeScan);
                        }
                        else {
                            const value = barcode.replace(regExpNOTIMEI, "");
                            if (helper.IsNonEmptyString(value)) {
                                onCheckBarcode(value, typeScan);
                            }
                        }
                    }}
                />
            }
        </View>
    );
}

export default GiftProduct;

const InputScan = ({
    value,
    onChange,
    onScan,
    onCheck,
    placeholder,
    error
}) => {
    return (
        <View style={{
            marginTop: 4
        }}>
            <View style={{
                width: constants.width,
                flexDirection: "row",
                justifyContent: "center",
                alignItems: "center",
            }}>
                <View style={{
                    height: 38,
                    width: constants.width - 94,
                    borderWidth: 1,
                    borderRadius: 4,
                    borderColor: COLORS.bdCCCCCC,
                    backgroundColor: COLORS.bgFFFFFF,
                    alignItems: "center",
                    flexDirection: "row",
                    justifyContent: "space-between",
                }}>
                    <TextInput
                        style={{
                            height: 38,
                            color: COLORS.txt333333,
                            width: constants.width - 140,
                            paddingLeft: 10,
                        }}
                        placeholder={placeholder}
                        keyboardType="default"
                        returnKeyType={"done"}
                        value={value}
                        onChangeText={onChange}
                        blurOnSubmit={true}
                        onSubmitEditing={Keyboard.dismiss}
                    />
                    <TouchableOpacity style={{
                        width: 40,
                        height: 38,
                        justifyContent: "center",
                        alignItems: "center"
                    }}
                        onPress={onScan}
                    >
                        <Icon
                            iconSet={"MaterialCommunityIcons"}
                            name={"barcode-scan"}
                            color={COLORS.icFFD400}
                            size={22}
                        />
                    </TouchableOpacity>
                </View>

                <Button
                    text={translate('saleOrderPayment.btn_enter')}
                    styleContainer={{
                        width: 64,
                        height: 38,
                        backgroundColor: COLORS.btn288AD6,
                        borderRadius: 4,
                        marginLeft: 10,
                    }}
                    styleText={{
                        color: COLORS.txtFFFFFF,
                        fontSize: 14,
                        fontWeight: "bold"
                    }}
                    onPress={onCheck}
                />
            </View>
            {
                !!error &&
                <MyText
                    text={error}
                    style={{
                        width: constants.width,
                        paddingHorizontal: 10,
                        marginTop: 2,
                        color: COLORS.txtFF0000
                    }} />
            }
        </View>
    );
}

const InputImei = ({ name, value, onClear }) => {
    return (
        <View style={{
            width: constants.width,
            paddingLeft: 28,
            paddingRight: 10,
            flexDirection: "row",
            marginTop: 4
        }}>
            <MyText
                text={`${name}: `}
                style={{
                    color: COLORS.txt333333,
                    fontWeight: 'bold',
                    width: constants.width - 70
                }}>
                <MyText
                    text={value}
                    style={{
                        color: COLORS.txt333333,
                        fontWeight: 'normal'
                    }} />
            </MyText>
            <TouchableOpacity style={{
                width: 32,
                alignItems: "center",
                height: 24
            }}
                onPress={onClear}
            >
                <Icon
                    iconSet={"MaterialCommunityIcons"}
                    name={"close-box"}
                    size={22}
                    color={COLORS.icFC3158}
                />
            </TouchableOpacity>
        </View>
    );
}

const TYPE_SCAN = {
    IMEI: "0",
    SERIAL1: "1",
    SERIAL2: "2",
    STICKER: "3"
}

const regExpIMEI = new RegExp(/^[a-zA-Z0-9\-\%\+\/\$\.\/:]{0,50}$/);
const regExpNOTIMEI = new RegExp(/[^a-zA-Z0-9\-\%\+\/\$\.\/:]/g);