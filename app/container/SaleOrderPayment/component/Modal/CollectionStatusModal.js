import React from "react";
import { View, ActivityIndicator, Animated, Easing } from "react-native";
import KModal from "react-native-modal";
import { MyText, Icon, Button, hideBlockUI } from "@components";
import { constants } from "@constants";
import { COLORS } from "@styles";
import LinearGradient from 'react-native-linear-gradient';
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import * as actionCollectionManagerCreator from "../../../CollectionTransferManager/action";
import { translate } from "@translate";
import { helper } from "@common";

const CollectionStatusModal = ({
    isVisible,
    SaleOrderID,
    userInfo,
    actionCollectionManager,
    onClose,
    onSuccess,
    handleIsNotServiceOrder,
    onPressOTP
}) => {
    const intervalId = React.useRef(0);
    const [dataStatus, setDataStatus] = React.useState({});
    const [iconsName, setIconName] = React.useState("");
    const [colorStatus, setColorStatus] = React.useState("#F49B0C");
    const opacityAnim = React.useRef(new Animated.Value(0)).current;
    const translateYAnim = React.useRef(new Animated.Value(50)).current;
    const bounceAnim = React.useRef(new Animated.Value(1)).current;

    const status = dataStatus?.cus_AirtimeStatusCacheBO?.Status;
    const checkStatus = status === "SUCCESS" || status === "FAIL";
    const checkStatusSuccess = status === "SUCCESS";
    const checkStatusFainal = status === "SUCCESS" || status === "FAIL";
    const statusNameTransaction =
        dataStatus?.cus_AirtimeStatusCacheBO?.Description;
    const description = dataStatus?.cus_AirtimeStatusCacheBO?.Description;
    const airTimeStatusName = dataStatus?.cus_AirtimeStatusCacheBO?.AirTimeStatusName;

    React.useEffect(() => {
        getQueryTransactionPartner();
        if (checkStatusFainal) {
            clearInterval(intervalId.current);
        } else {
            intervalId.current = setInterval(() => {
                getQueryTransactionPartner();
            }, 5000);
        }

        return () => {
            if (intervalId.current) {
                clearInterval(intervalId.current);
            }
        };
    }, [checkStatusFainal]);

    React.useEffect(() => {
        Animated.parallel([
            Animated.timing(opacityAnim, {
                toValue: 1,
                duration: 500,
                easing: Easing.out(Easing.exp),
                useNativeDriver: true,
            }),
            Animated.timing(translateYAnim, {
                toValue: 0,
                duration: 500,
                easing: Easing.out(Easing.exp),
                useNativeDriver: true,
            }),
        ]).start();
    }, []);

    const animateIconBounce = () => {
        Animated.sequence([
            Animated.timing(bounceAnim, {
                toValue: 1.2,
                duration: 150,
                easing: Easing.inOut(Easing.bounce),
                useNativeDriver: true,
            }),
            Animated.timing(bounceAnim, {
                toValue: 1,
                duration: 150,
                easing: Easing.inOut(Easing.bounce),
                useNativeDriver: true,
            }),
        ]).start();
    };

    React.useEffect(() => {
        if (status) {
            animateIconBounce();
        }
    }, [status]);

    const getQueryTransactionPartner = () => {
        const data ={
            saleOrderID: SaleOrderID
        }
        actionCollectionManager
            .getQuerysTatusServiceRequest(data)
            .then((transaction) => {
                if (transaction?.cus_AirtimeStatusCacheBO) {
                    const { Status } = transaction.cus_AirtimeStatusCacheBO;
                    setDataStatus(transaction);
                    hideBlockUI();
                    switch (Status) {
                        case "PENDING": {
                            setIconName("");
                            setColorStatus("#F49B0C");
                            break;
                        }
                        case "SUCCESS": {
                            setIconName("md-checkmark-circle-outline");
                            setColorStatus("#1E88E5");
                            break;
                        }
                        case "FAIL": {
                            setIconName("ios-close-circle-outline");
                            setColorStatus("#EA1D5D");
                            break;
                        }
                        case 'WAITINGSENDOTP':
                            onPressOTP()
                    }
                } else {
                    handleIsNotServiceOrder();
                }
            })
            .catch((msgError) => {
                console.log("API Error:", msgError);
            });
    };

    return (
        <KModal
            isVisible={isVisible}
            style={{
                margin: 0,
                justifyContent: "center",
                alignItems: "center",
            }}
            deviceWidth={constants.width}
            deviceHeight={constants.height}
            backdropTransitionOutTiming={0}
            animationIn="slideInUp"
            animationOut="slideOutDown"
            useNativeDriver={true}
            hideModalContentWhileAnimating={true}
        >
            <Animated.View
                style={{
                    backgroundColor: COLORS.bgFFFFFF,
                    width: "90%",
                    borderRadius: 15,
                    alignItems: "center",
                    shadowColor: COLORS.bg000000,
                    shadowOffset: { width: 0, height: 10 },
                    shadowOpacity: 0.2,
                    shadowRadius: 20,
                    elevation: 15,
                    opacity: opacityAnim,
                    transform: [{ translateY: translateYAnim }],
                }}
            >
                <LinearGradient
                    colors={["#00c6ff", "#0072ff"]}
                    style={{
                        width: '100%',
                        paddingVertical: 15,
                        borderTopLeftRadius: 15,
                        borderTopRightRadius: 15,
                        alignItems: "center",
                        marginBottom: 15,
                    }}
                >
                    <MyText
                        style={{
                            color: COLORS.txtFFFFFF,
                            fontSize: 20,
                            fontWeight: "bold",
                        }}
                        text={checkStatus ? "Trạng thái giao dịch" : "Giao dịch đang xử lý"}
                    />
                </LinearGradient>

                <View
                    style={{
                        alignItems: "center",
                        marginVertical: 10,
                    }}
                >
                    <Animated.View
                        style={{
                            width: 70,
                            height: 70,
                            backgroundColor: COLORS.bgFFFFFF,
                            borderRadius: 35,
                            alignItems: "center",
                            justifyContent: "center",
                            shadowColor: COLORS.bg000000,
                            shadowOffset: { width: 0, height: 10 },
                            shadowOpacity: 0.25,
                            shadowRadius: 4,
                            elevation: 5,
                            marginBottom: 15,
                            transform: [{ scale: bounceAnim }],
                        }}
                    >
                        <Icon
                            iconSet={"Ionicons"}
                            name={iconsName}
                            size={35}
                            color={colorStatus}
                        />
                        {!checkStatusFainal && (
                            <ActivityIndicator
                                color={COLORS.bg00A98F}
                                style={{ position: "absolute" }}
                            />
                        )}
                    </Animated.View>
                    {
                        <View>
                            {checkStatus ? (
                                <MyText
                                    style={{
                                        color: colorStatus,
                                        fontSize: 18,
                                        textAlign: "center",
                                        fontWeight: "bold",
                                        marginBottom: 10,
                                    }}
                                    text={airTimeStatusName}
                                />
                            ) : (
                                <View style={{
                                    justifyContent: 'center',
                                    alignItems: 'center'
                                }}>
                                    <MyText
                                        style={{
                                            color: '#00bfff',
                                            fontSize: 16,
                                            fontWeight: "bold",
                                            marginBottom: 10,
                                            textAlign: "center",
                                        }}
                                        text={`Giao dịch đang xử lý`}
                                    />
                                    <MyText
                                        style={{
                                            color: '#1e90ff',
                                            fontSize: 14,
                                            marginBottom: 10,
                                            textAlign: "center",
                                            width: 330
                                        }}
                                        text={`Theo dõi kết quả ở mục "Quản lý GD ngành hàng Multicat". Vẫn thu đủ tiền KH và không thu lại giao dịch mới trước khi GD này có kết quả cuối`}
                                    />
                                </View>
                            )}
                        </View>
                    }
                    <MyText
                        style={{
                            color: COLORS.bg000000,
                            fontSize: 14,
                            textAlign: "center",
                        }}
                        text={checkStatus ? description : ""}
                    />
                </View>
                <View style={{ marginTop: 20, marginBottom: 5 }}>
                    {checkStatusSuccess ? (
                        <Button
                            onPress={onSuccess}
                            text={translate("collection.completed")}
                            styleContainer={{
                                borderRadius: 7,
                                backgroundColor: COLORS.bgF49B0C,
                                height: 45,
                                width: constants.getSize(130),
                                marginTop: 20,
                                shadowColor: "#000",
                                shadowOffset: { width: 0, height: 5 },
                                shadowOpacity: 0.3,
                                shadowRadius: 10,
                                elevation: 5,
                            }}
                            styleText={{
                                color: COLORS.txtFFFFFF,
                                fontSize: 15,
                                fontWeight: "bold",
                            }}
                        />
                    ) : (
                        <Button
                            onPress={onClose}
                            text={translate("collection.close")}
                            styleContainer={{
                                borderRadius: 7,
                                backgroundColor: COLORS.bgF49B0C,
                                height: 45,
                                width: constants.getSize(130),
                                marginTop: 20,
                                shadowColor: "#000",
                                shadowOffset: { width: 0, height: 5 },
                                shadowOpacity: 0.3,
                                shadowRadius: 10,
                                elevation: 5,
                            }}
                            styleText={{
                                color: COLORS.txtFFFFFF,
                                fontSize: 15,
                                fontWeight: "bold",
                            }}
                        />
                    )}
                </View>
            </Animated.View>
        </KModal>
    );
};

const mapStateToProps = (state) => ({
    userInfo: state.userReducer,
});

const mapDispatchToProps = (dispatch) => ({
    actionCollectionManager: bindActionCreators(
        actionCollectionManagerCreator,
        dispatch
    ),
});

export default connect(
    mapStateToProps,
    mapDispatchToProps
)(CollectionStatusModal);
