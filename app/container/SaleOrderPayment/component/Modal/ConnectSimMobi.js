import React, { useEffect } from 'react';
import { FlatList, Image, TouchableOpacity, View } from 'react-native';
import KModal from "react-native-modal";
import { MyText, Button, Icon, UIIndicator, showBlockUI, hideBlockUI } from "@components";
import { DEVICE, constants } from "@constants";
import { COLORS } from "@styles";
import { translate } from '@translate';
import { helper } from '@common';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { getQueryString } from '@config';
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import * as actionGetSimCreator from '../../../ActiveSimManager/action';
const ConnectSimMobi = ({
    isVisible,
    hideModal,
    isLoading,
    SaleOrderID,
    dataActionSIM,
    actionGetSim,
    userInfo
}) => {
    const { storeID, userName } = userInfo;
    const [{ data: { PRODUCTID } }] = dataActionSIM;
    const handleDeeplinkSIMMobi = (item, reponseTokenMobi) => {
        const {
            IMEI,
            PACKAGESTYPE,
            SIMPROCESSREQUESTID,
            SIMSERIAL,
            SIMSERIALTYPE,
            PACKAGENAME
        } = item.data;
        const {
            Token,
            XAccessToken
        } = reponseTokenMobi
        const params = {
            appObject: "APP_TGDD",
            isdn: IMEI.trim(),
            simType: SIMSERIALTYPE == 0 ? 1 : 2,
            strSerial: SIMSERIALTYPE == 1 ? '' : SIMSERIAL,
            arrRegProm: PACKAGESTYPE,
            token: Token,
            xAccessToken: XAccessToken,
            referenceTransactionId: SIMPROCESSREQUESTID.trim(),
            appVersion: DEVICE.readableVersion,
            returnDeepLink: `mwgpos://mwgpos/msale`,
            arrRegPromName: PACKAGENAME
        }
        if (helper.addStoreConditionsSimMobi(storeID)) {
            params.type = 2
        }
        const query = getQueryString(params);
        const url = `msale://tgdd?${query}`;
        const data = {
            "sIMProcessRequestID": SIMPROCESSREQUESTID,
            "data": url
        };
        actionGetSimCreator.inserLogDeeplink(data);
        console.log('Deeplink App mSale++', url, params);
        helper.openURL(url);
        helper.LoggerInfo({ "{+MSALE_DEEPLINK_URL+}": url });
    }

    getTokenJWTMobifone = (item) => {
        showBlockUI();
        actionGetSim.getTokenDeeplinkMobi(PRODUCTID)
            .then(reponseTokenMobi => {
                hideBlockUI();
                handleDeeplinkSIMMobi(item, reponseTokenMobi)
                console.log("Đi deeplink qua App mSalePlus đối tác");
            })
            .catch((error) => {
                hideBlockUI();
                Alert.alert(
                    translate('common.notification_uppercase'),
                    error.msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: 'cancel',
                            onPress: () => this.getTokenJWTMobifone()
                        }
                    ]
                );
            });
    }

    const renderItem = ({ item, index }) => {
        return (
            <View style={{
                flexDirection: 'column'
            }}>
                <MyText
                    style={{
                        color: COLORS.bg000000,
                        fontSize: 18
                    }}
                    text={item.action == "DAUNOI" ? `Mobifone - ${item?.data?.IMEI}` : null} />
                <View style={{
                    padding: 5
                }}>
                    {
                        item.action == "DAUNOI" &&
                        <TouchableOpacity
                            onPress={() => getTokenJWTMobifone(item)}
                            style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                marginLeft: 5
                            }}>
                            <Icon
                                iconSet={"MaterialCommunityIcons"}
                                name={"transit-connection-variant"}
                                color={COLORS.bg2FB47C}
                                size={18}
                            />
                            <MyText
                                style={{
                                    color: COLORS.bg2FB47C,
                                    fontSize: 18,
                                    marginLeft: 5
                                }}
                                text="Bấm vào để thực hiện đấu nối Sim" />
                        </TouchableOpacity>
                    }
                </View>
            </View >
        )
    }

    return (
        <KModal
            isVisible={isVisible}
            deviceWidth={constants.width}
            deviceHeight={constants.height}
            animationIn="slideInUp"
            animationOut="slideOutDown"
            useNativeDriver={true}
            hideModalContentWhileAnimating={true}
            style={{ margin: 0 }}
        >
            <View style={{
                flex: 1,
                backgroundColor: COLORS.bgFFFFFF,
            }}>
                <Header
                    onClose={hideModal}
                    epCode={`Mã YCX: ${SaleOrderID}`}
                    titleSystem={`Siêu thị: ${storeID} - User: ${userName}`}
                />
                <KeyboardAwareScrollView
                    style={{
                        flex: 1
                    }}
                    enableResetScrollToCoords={false}
                    keyboardShouldPersistTaps="always"
                    bounces={false}
                    overScrollMode="always"
                    showsHorizontalScrollIndicator={false}
                    showsVerticalScrollIndicator={false}
                    extraScrollHeight={60}
                >
                    <View>
                        <View style={{
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center',
                            backgroundColor: "white",
                            borderRadius: 15,
                            marginTop: 20
                        }}>
                            <View style={{
                                flexDirection: 'column',
                                justifyContent: 'center',
                                alignItems: 'center'
                            }}>
                                <MyText
                                    style={{
                                        color: COLORS.bg2FB47C,
                                        fontSize: 18,
                                        textAlign: 'center',
                                        fontStyle: 'italic',
                                        fontWeight: 'bold',
                                    }}
                                    text={"Danh sách các công việc cần xử lý để hoàn tất đơn hàng: "} />
                            </View>
                            <View style={{
                                height: 1,
                                weight: "60%",
                                borderColor: COLORS.bg8E8E93,
                                borderEndWidth: 180,
                                marginTop: 20
                            }} />
                        </View>

                        <View style={{
                            padding: 20
                        }}>
                            <FlatList
                                data={dataActionSIM}
                                keyExtractor={(item, index) => index.toString()}
                                renderItem={renderItem}
                                bounces={false}
                                scrollEventThrottle={16}
                                nestedScrollEnabled={true}
                            />
                        </View>
                    </View>
                </KeyboardAwareScrollView>
            </View >
            <UIIndicator isVisible={isLoading} />
        </KModal >

    );
}

const mapStateToProps = (state) => ({
    userInfo: state.userReducer
});

const mapDispatchToProps = (dispatch) => ({
    actionGetSim: bindActionCreators(actionGetSimCreator, dispatch),
});

export default connect(mapStateToProps, mapDispatchToProps)(ConnectSimMobi);
const Header = ({ onClose, epCode, titleSystem }) => {
    return (
        <View style={{
            backgroundColor: COLORS.bg2FB47C,
            paddingTop: constants.heightTopSafe,
            borderBottomLeftRadius: 10
        }}>
            <View style={{
                width: constants.width,
                height: 55,
                alignItems: "center",
                justifyContent: 'center',
                backgroundColor: COLORS.bg2FB47C,
            }}>
                <TouchableOpacity style={{
                    width: 50,
                    height: 50,
                    justifyContent: 'center',
                    alignItems: 'center',
                    position: "absolute",
                    top: 0,
                    right: 0
                }}
                    onPress={onClose}
                >
                    <Icon
                        iconSet={"MaterialIcons"}
                        name={"close"}
                        color={COLORS.icFFFFFF}
                        size={24}
                    />
                </TouchableOpacity>

                <MyText
                    adjustsFontSizeToFit={true}
                    numberOfLines={2}
                    text={epCode}
                    addSize={2}
                    style={{
                        fontWeight: 'bold',
                        color: COLORS.txtFFF6AD,
                        width: "85%",
                        marginRight: 15
                    }}
                />
                <View style={{
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'center'
                }}>
                    <Image
                        style={{ width: 11.5, height: 11.5 }}
                        source={{ uri: "logo_tgdd" }}
                    />
                    <MyText
                        adjustsFontSizeToFit={true}
                        numberOfLines={1}
                        text={titleSystem}
                        addSize={2}
                        style={{
                            color: COLORS.bgFFFFFF,
                            width: "85%",
                            fontSize: 13,
                            marginLeft: 5
                        }}
                    />
                </View>
            </View>
        </View>
    );
}


