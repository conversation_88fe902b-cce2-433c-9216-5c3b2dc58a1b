import React from 'react';
import { View, StyleSheet } from 'react-native';
import { constants } from '@constants';
import { helper } from "@common";
import RowMoney from "./Money/RowMoney";
import InputMoney from "./Money/InputMoney";
import RowLabel from "./Money/RowLabel";
import { translate } from '@translate';
import { COLORS } from "@styles";

const DebtRefundInfo = ({
    info,
    onChange,
    onFocus,
    onBlur,
    cash,
    autoFocus,
    percentBonus
}) => {
    const {
        TotalRemain,
        RefundMoney,
        PointRefund
    } = info;
    return (
        <View style={{
            backgroundColor: COLORS.bgCBE5B2,
            width: constants.width,
            borderTopWidth: StyleSheet.hairlineWidth,
            borderTopColor: COLORS.bdFFFFFF,
            marginTop: 2
        }}>
            <InputMoney
                name={translate('saleOrderPayment.cash_customer_pay')}
                value={cash}
                onChange={onChange}
                onFocus={onFocus}
                onBlur={onBlur}
                autoFocus={autoFocus}
                isActiveNumbericIOS={true}
            />
            {percentBonus ? (
                <RowLabel
                    label={`Tặng thêm ${percentBonus}% điểm QTV trên số tiền mặt khách thanh toán.`}
                    textStyle={{
                        color: COLORS.txt037EF3,
                        fontSize: 13,
                        fontStyle: 'italic'
                    }}
                />
            ) : null}
            <RowMoney
                name={translate('saleOrderPayment.in_debt')}
                colorValue={COLORS.txtFF6600}
                value={helper.convertNum(TotalRemain)}
            />
            <RowMoney
                name={translate('saleOrderPayment.customer_change')}
                colorValue={COLORS.txt4A90E2}
                value={helper.convertNum(RefundMoney)}
            />
            {
                PointRefund > 0 &&
                <RowMoney
                    name={translate('saleOrderPayment.change_point')}
                    colorValue={COLORS.txtFF00BF}
                    value={helper.convertNum(PointRefund)}
                />
            }
        </View>
    );
}

export default DebtRefundInfo;