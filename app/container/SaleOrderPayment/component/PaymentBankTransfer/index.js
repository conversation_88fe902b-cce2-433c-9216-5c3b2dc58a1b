import React, { useEffect, useRef, useState } from 'react';
import { View, StyleSheet, Alert, TouchableOpacity } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { COLORS } from '@styles';
import { helper } from '@common';
import { hideBlockUI, showBlockUI, ViewHTML, MyText, Icon, NumberInput } from '@components';
import { translate } from '@translate';
import { getContentBase64View } from '../../../SaleOrderManager/action';
import { constants } from '@constants';

const PaymentBankTransfer = ({ payableAmount, saleOrderID, onReloadSOPayment, isCheck, handleBankTransfer }) => {
    const [htmlData, setHtmlData] = useState('');
    const [isVisible, setIsVisible] = useState(false);
    const [isShow, setIsShow] = useState(false)
    const [amount, setAmount] = useState(0)
    const preAmount = useRef(0)
    const dispatch = useDispatch();
    const hasHtmlData = helper.IsNonEmptyString(htmlData);
    const { PM_PreOrder_SaleOrderTypeList } = useSelector((state) => state.appSettingReducer)
    const { dataSO } = useSelector((state) => state.saleOrderPaymentReducer)
    const isPre = `,${PM_PreOrder_SaleOrderTypeList},`.includes(`,${dataSO.SaleOrderTypeID?.toString()},`);
    const handleApiGetContentHtmlView = (id) => {
        showBlockUI();
        return dispatch(
            getContentBase64View({
                reportContent: 'BankAccountContent',
                saleOrderID: id,
                paymentAmount: amount,
            })
        )
            .then((html) => {
                hideBlockUI();
                preAmount.current = amount
                return html;
            })
            .catch((msgError) => {
                hideBlockUI();
                Alert.alert(translate('common.notification'), msgError);
                return '';
            });
    };
    useEffect(() => {
        if (payableAmount >= 0) {
            setAmount(payableAmount);
        }
    }, [payableAmount])
    return (
        <View
            style={{
                backgroundColor: COLORS.bgF5F5F5,
                borderTopWidth: StyleSheet.hairlineWidth,
                borderTopColor: COLORS.bdFFFFFF,
            }}>
            <View
                style={{
                    paddingHorizontal: 10,
                    backgroundColor: COLORS.btnF9E498,
                    flexDirection: 'row',
                    alignItems: 'center',
                    height: 40,
                    justifyContent: 'space-between'
                }}
                activeOpacity={0.8}>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center'
                    }}>
                    <TouchableOpacity style={{ flexDirection: "row", alignItems: "center", justifyContent: "center" }} onPress={() => setIsShow(!isShow)}>
                        <MyText
                            text={translate(
                                'saleOrderManager.view_transfer_information'
                            )}
                            style={{
                                color: COLORS.txt147EFB,
                                fontWeight: 'bold'
                            }}
                        />
                        <Icon
                            iconSet={"Ionicons"}
                            name={isShow ? "chevron-up" : "chevron-down"}
                            size={22}
                            color={COLORS.ic147EFB}
                        />
                    </TouchableOpacity>
                </View>
                {(hasHtmlData || isCheck) && !isPre && (
                    <View>
                        <TouchableOpacity
                            style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'flex-end'
                            }}
                            onPress={onReloadSOPayment}>
                            <Icon
                                iconSet="MaterialCommunityIcons"
                                name="reload"
                                size={18}
                                color={COLORS.ic147EFB}
                            />
                            <MyText
                                text={translate(
                                    'saleOrderManager.transfer_validation'
                                )}
                                style={{
                                    color: COLORS.txt147EFB,
                                    textAlign: 'right',
                                    fontWeight: 'bold',
                                    marginLeft: 4
                                }}
                            />
                        </TouchableOpacity>
                    </View>
                )}

            </View>
            {
                isShow && <View style={{ alignItems: "center" }}>
                    <View style={{
                        backgroundColor: 'white',
                        borderRadius: 8,
                        paddingBottom: 15,
                        paddingTop: 15,
                        paddingHorizontal: 25,
                        marginVertical: 10,
                        shadowColor: '#171717',
                        shadowOffset: { width: -2, height: 4 },
                        shadowOpacity: 0.2,
                        shadowRadius: 3,
                        width: constants.width - 20
                    }}>
                        <View style={{ paddingBottom: 15 }}>
                            <InputMoney
                                title={'Nhập số tiền khách muốn thanh toán'}
                                value={amount}
                                onChange={(value) => {
                                    setAmount(value)
                                }}
                                maxValue={payableAmount}
                            />
                        </View>

                        <View style={{
                            alignItems: "center",
                        }}>
                            <TouchableOpacity
                                onPress={() => handleBankTransfer(amount)}
                                style={{
                                    backgroundColor: "white",
                                    height: 30,
                                    width: constants.width / 2,
                                    flexDirection: 'row',
                                    borderColor: COLORS.bg288AD6,
                                    borderWidth: 0.5,
                                    borderRadius: 5,
                                    alignItems: 'center',
                                    shadowColor: COLORS.bg288AD6,
                                    shadowOffset: { width: 0.5, height: 0.5 },
                                    shadowOpacity: 0.3,
                                    shadowRadius: 3,
                                    elevation: 1,

                                }}>

                                <View style={{ alignItems: "center", flex: 1 }}>
                                    <MyText
                                        style={{
                                            color: COLORS.bg288AD6,
                                            fontSize: 13,
                                            fontWeight: "500"
                                        }}
                                        text="Xem thông tin chuyển khoản"
                                    />
                                </View>

                            </TouchableOpacity>

                        </View>


                    </View>
                </View>
            }


            <ViewHTML
                isVisible={isVisible}
                source={htmlData}
                hideModal={() => {
                    setIsVisible(false);
                }}
                title="Thông Tin Chuyển Khoản"
            />
        </View>
    );
};

export default PaymentBankTransfer;

const InputMoney = ({
    title,
    value,
    onChange,
    maxValue
}) => {

    return (
        <View style={{
            width: constants.width - 80,
        }}>
            <MyText
                text={title}
                style={{
                    color: COLORS.txt333333,
                    fontWeight: "500",
                    paddingBottom: 2
                }}
                addSize={-1}
            />
            <NumberInput
                style={{
                    height: 40,
                    backgroundColor: COLORS.bgFFFFFF,
                    borderWidth: 1,
                    borderRadius: 4,
                    borderColor: COLORS.bdCCCCCC,
                    paddingHorizontal: 10
                }}
                placeholder={"0"}
                value={value}
                onChangeText={onChange}
                maxValue={maxValue}
            />
        </View>
    );
}

