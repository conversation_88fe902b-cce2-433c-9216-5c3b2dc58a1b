import React from 'react';
import { View, StyleSheet } from 'react-native';
import { MyText, NumberInput } from '@components';
import { constants } from '@constants';
import { COLORS } from '@styles';

const InputMoney = ({
    name,
    value,
    colorName = COLORS.txt333333,
    colorValue = COLORS.txt333333,
    onChange,
    onFocus,
    onBlur,
    autoFocus,
    isActiveNumbericIOS
}) => {
    return (
        <View
            style={{
                flexDirection: 'row',
                borderBottomWidth: StyleSheet.hairlineWidth,
                borderBottomColor: COLORS.bdFFFFFF
            }}>
            <View
                style={{
                    minHeight: 40,
                    justifyContent: 'center',
                    alignItems: 'flex-start',
                    width: constants.width - 152,
                    borderRightWidth: StyleSheet.hairlineWidth,
                    borderRightColor: COLORS.bdFFFFFF,
                    paddingHorizontal: 10,
                    marginVertical: 4
                }}>
                <MyText text={name} style={{ color: colorName }} />
            </View>
            <View
                style={{
                    minHeight: 40,
                    justifyContent: 'center',
                    alignItems: 'flex-end',
                    width: 150,
                    borderWidth: 1,
                    borderColor: COLORS.bdCBE5B2,
                    backgroundColor: COLORS.bgFFFFFF
                }}>
                <NumberInput
                    style={{
                        height: 40,
                        width: 148,
                        backgroundColor: COLORS.bgFFFFFF,
                        textAlign: 'right',
                        paddingHorizontal: 9,
                        color: colorValue
                    }}
                    placeholder="0"
                    value={value}
                    onChangeText={onChange}
                    onFocus={onFocus}
                    onBlur={onBlur}
                    blurOnSubmit
                    autoFocus={autoFocus}
                    isActiveNumbericIOS={isActiveNumbericIOS}
                />
            </View>
        </View>
    );
};

export default InputMoney;
