import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { helper } from '@common';
import { constants } from '@constants';
import { COLORS } from '@styles';
import { BaseLoading, MyText } from '@components';
import { useDispatch, useSelector } from 'react-redux';
import PaymentPoint from './PaymentPoint';
import PaymentSec from './PaymentSec';
import SecApply from './component/SecApply';
import MaxPointApply from './component/MaxPointApply';

import { getLoyaltyPoint } from '../../action';

const PaymentLoyalty = ({
    title,
    total,
    data,
    availablePoint,
    maxUsePoint,
    refundPoint,
    onUpdateDataLoyalty,
    customerPhone,
    maxPayment,
    brandID,
    defaultPointUse,
    cartID,
    onDataLoyaltyChange,
    shouldCallLoyalty
}) => {
    const { LoyaltyPoint = 0, Cheques = [] } = data;
    const isUsePoint = availablePoint > 0;
    const { storeID, languageID, moduleID } = useSelector(
        (state) => state.userReducer
    );
    const { dataSaleOrder } = useSelector(
        (state) => state.saleOrderPaymentReducer
    );
    const [stateLoyalty, setStateLoyalty] = useState({
        isFetching: false,
        isEmpty: false,
        description: '',
        isError: false
    });
    const getDataLoyalty = () => {
        const body = {
            moduleID,
            languageID,
            loginStoreId: storeID,
            unSaleOrder: dataSaleOrder
        };
        setStateLoyalty({
            isFetching: true,
            isEmpty: false,
            description: '',
            isError: false
        });
        getLoyaltyPoint(body)
            .then((dataLoyalty) => {
                if (!helper.IsEmptyObject(dataLoyalty)) {
                    onDataLoyaltyChange(dataLoyalty);
                    setStateLoyalty({
                        isFetching: false,
                        isEmpty: false,
                        description: '',
                        isError: false
                    });
                } else {
                    setStateLoyalty({
                        isFetching: false,
                        isEmpty: true,
                        description: 'Không có thông tin Loyalty.',
                        isError: false
                    });
                }
            })
            .catch((msgError) => {
                setStateLoyalty({
                    isFetching: false,
                    isEmpty: false,
                    description: msgError,
                    isError: true
                });
            });
    };
    useEffect(() => {
        if (shouldCallLoyalty) {
            getDataLoyalty();
        }
    }, [shouldCallLoyalty]);

    const applyPoint = (point, infoLoyalty = {}) => {
        const newData = helper.deepCopy({ LoyaltyPoint, Cheques });
        newData.LoyaltyPoint = point;
        onUpdateDataLoyalty(newData, infoLoyalty);
    };

    const applySec = (cheque) => {
        const newData = helper.deepCopy({ LoyaltyPoint, Cheques });
        newData.Cheques.push(cheque);
        onUpdateDataLoyalty(newData);
    };

    const removeCheque = (index) => () => {
        const newData = helper.deepCopy({ LoyaltyPoint, Cheques });
        newData.Cheques = Cheques.filter((ele, id) => id != index);
        onUpdateDataLoyalty(newData);
    };

    const renderCheque = ({ item, index }) => {
        return (
            <SecApply
                info={item}
                onRemove={removeCheque(index)}
                key={`Cheque${index}`}
            />
        );
    };

    return (
        <View
            style={{
                backgroundColor: COLORS.bgF5F5F5,
                borderTopWidth: StyleSheet.hairlineWidth,
                borderTopColor: COLORS.bdFFFFFF
            }}>
            <PaymentTitle title={title} total={total} />

            <BaseLoading
                isLoading={stateLoyalty.isFetching}
                textLoadingError={stateLoyalty.description}
                isError={stateLoyalty.isError}
                onPressTryAgains={getDataLoyalty}
                content={
                    <View
                        style={{
                            width: constants.width
                        }}>
                        {
                            // isDefault &&
                            <MaxPointApply maxUsePoint={defaultPointUse} />
                        }
                        {isUsePoint && (
                            <PaymentPoint
                                availablePoint={availablePoint}
                                usePoint={LoyaltyPoint}
                                onApplyPoint={applyPoint}
                                refundPoint={refundPoint}
                                maxUsePoint={defaultPointUse}
                                total={total}
                                customerPhone={customerPhone}
                                maxPayment={maxPayment}
                                brandID={brandID}
                                isDefault
                                cartID={cartID}
                            />
                        )}
                        {/* <PaymentSec
                                data={Cheques}
                                renderItem={renderCheque}
                                onApplySec={applySec}
                                total={total}
                                maxUsePoint={maxUsePoint}
                            /> */}
                    </View>
                }
            />
        </View>
    );
};

export default PaymentLoyalty;

const PaymentTitle = ({ title, total }) => {
    const isPayment = total > 0;
    return (
        <View
            style={{
                paddingHorizontal: 10,
                backgroundColor: COLORS.btnF9E498,
                flexDirection: 'row',
                alignItems: 'center',
                height: 40,
                width: constants.width,
                justifyContent: 'space-between'
            }}
            activeOpacity={0.8}>
            <View
                style={{
                    width: constants.width - 120,
                    flexDirection: 'row',
                    alignItems: 'center'
                }}>
                <MyText
                    text={title}
                    style={{
                        color: COLORS.txt147EFB,
                        fontWeight: 'bold'
                    }}
                />
            </View>
            {isPayment && (
                <MyText
                    text={helper.convertNum(total)}
                    style={{
                        color: COLORS.txt147EFB,
                        width: 100,
                        textAlign: 'right'
                    }}
                />
            )}
        </View>
    );
};
