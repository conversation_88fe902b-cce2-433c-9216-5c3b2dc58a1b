
import React, { useState, useEffect } from 'react';
import {
    View,
    StyleSheet,
    Alert
} from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { constants } from '@constants';
import { BarcodeCamera, showBlock<PERSON>, hideBlock<PERSON> } from "@components";
import { dateHelper, helper, storageHelper } from "@common";
import TypeApply from "./component/TypeApply";
import InputPoint from "./component/InputPoint";
import OtpCode from "./component/OtpCode";
import PointApply from "./component/PointApply";
import UsePointInfo from "./component/UsePointInfo";
import RadioVerify from "./component/RadioVerify";
import InputIdentify from "./component/InputIdentify";
import { AlertMessage, Transaction } from '../../../AnKhangNew/components';
import * as actionShoppingCartCreator from "../../../ShoppingCart/action";
import { getLoyaltyTransactionInfo } from '../../action';
import { translate, keys } from '@translate';
import { COLORS } from "@styles";

const PaymentPoint = ({
    availablePoint,
    maxUsePoint,
    usePoint,
    onApplyPoint,
    total,
    customerPhone,
    maxPayment,
    brandID,
    isDefault,
    cartID
}) => {
    const isApply = (usePoint > 0);
    const [point, setPoint] = useState("");
    const [otpCode, setOtpCode] = useState("");
    const [expireTime, setExpireTime] = useState(0);
    const [isOtp, setIsOtp] = useState(false);
    const [identifyCode, setIdentifyCode] = useState("");
    const [isVisible, setIsVisible] = useState(false);
    const [isVerify, setIsVerify] = useState(false);
    const [isVisibleAlert, setIsVisibleAlert] = useState(false);
    const [createdAt, setCreatedAt] = useState(0);
    const { loyaltyStatus } = useSelector(
        (state) => state.saleOrderPaymentReducer
    );
    const { dataSaleOrder } = useSelector(
        (state) => state.saleOrderPaymentReducer
    );
    const dispatch = useDispatch();
    let intervalId = null;
    let second = 0;

    const effectCheckVerify = async () => {
        const value = await storageHelper.checkVerifyLoyalty(customerPhone, cartID);
        setIsVerify(value);
    }

    const willUnmount = () => {
        if (intervalId) {
            clearInterval(intervalId);
        }
    }

    const didMount = () => {
        if (isDefault) {
            setPoint(`${maxUsePoint}`);
        }
        effectCheckVerify();
        return willUnmount;
    }

    useEffect(didMount, [])

    const validatePoint = () => {
        const subPoint = maxUsePoint - total;
        if (!helper.IsNonEmptyString(point)) {
            Alert.alert("", translate('saleOrderPayment.please_enter_point'));
            return false;
        }
        if (point > maxUsePoint) {
            Alert.alert("", `${translate('saleOrderPayment.excessive_point')} ${helper.convertNum(maxUsePoint)}`);
            return false;
        }
        if (point < 500) {
            Alert.alert("", "Điểm sử dụng không nhỏ hơn 500đ");
            return false;
        }
        // if (point > subPoint) {
        //     Alert.alert("", translate('saleOrderPayment.excessive_total_point'));
        //     return false;
        // }
        // if (point > availablePoint) {
        //     Alert.alert("", translate('saleOrderPayment.excessive_valid_point'));
        //     return false;
        // }
        // if (point > maxPayment) {
        //     Alert.alert("", translate('saleOrderPayment.pay_amount_larger'));
        //     return false;
        // }
        return true;
    }

    const countDown = () => {
        second = second - 1;
        if (second > 0) {
            setExpireTime(second);
        }
        else {
            resetCountDown();
        }
    }

    const resetCountDown = () => {
        if (intervalId) {
            clearInterval(intervalId);
        }
        setOtpCode("");
        setExpireTime(0);
    }

    const setCountDown = () => {
        second = 60;
        setExpireTime(second);
        intervalId = setInterval(
            countDown,
            1000
        );
    }

    const validateOTP = () => {
        const isValidate = regExpOTP.test(otpCode);
        if (!helper.IsNonEmptyString(otpCode)) {
            Alert.alert("", translate('saleOrderPayment.please_enter_OTP_code'));
            return false;
        }
        if (!isValidate) {
            Alert.alert("", translate('saleOrderPayment.OTP_code_4_digits'));
            return false;
        }
        return true;
    }

    const onCreateOTP = (type) => {
        const isValidate = validatePoint();
        if (isValidate) {
            showBlockUI();
            actionShoppingCartCreator.createOTP({
                "type": type,
                "phoneNumber": customerPhone,
                "typeContent": "VIP",
                "lenOtp": 4,
                "brandID": brandID
            }).then(success => {
                hideBlockUI();
                setCountDown();
            }).catch(msgError => {
                Alert.alert(translate('common.notification_uppercase'), msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: "cancel",
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: "default",
                            onPress: () => onCreateOTP(type)
                        }
                    ]
                )
            });
        }
    }

    const verifyOTP = (otpCode, customerPhone) => {
        const isValidate = validateOTP();
        if (isValidate) {
            showBlockUI();
            actionShoppingCartCreator.verifyOTP(otpCode, customerPhone)
                .then(data => {
                    const value = convertString2Number(point);
                    onApplyPoint(value);
                    resetCountDown();
                    setPoint("");
                }).catch(msgError => {
                    Alert.alert(translate('common.notification_uppercase'), msgError,
                        [
                            {
                                text: translate('common.btn_skip'),
                                style: "cancel",
                                onPress: hideBlockUI
                            },
                            {
                                text: translate('common.btn_notify_try_again'),
                                style: "default",
                                onPress: () => verifyOTP(otpCode, customerPhone)
                            }
                        ]
                    )
                })
        }
    }

    const onVlidateID = () => {
        const isValidate = regExpID4.test(identifyCode) || regExpID6.test(identifyCode);
        if (!helper.IsNonEmptyString(identifyCode)) {
            Alert.alert("", translate('saleOrderPayment.please_enter_ID_code'));
        }
        else if (!isValidate) {
            Alert.alert("", "Vui lòng nhập mã định danh đúng 4 hoặc 6 chữ số");
        }
        else {
            verifyIdentify(identifyCode, "");
        }
    }

    const verifyIdentify = (codeID, dataID) => {
        const isValidate = validatePoint();
        if (isValidate) {
            showBlockUI();
            actionShoppingCartCreator.verifyIdentify(codeID, dataID, customerPhone, brandID)
                .then(data => {
                    storageHelper.updateVerifyLoyalty(customerPhone, cartID);
                    const value = convertString2Number(point);
                    onApplyPoint(value, data);
                    setPoint("");
                    setIdentifyCode("");
                }).catch(msgError => {
                    Alert.alert(translate('common.notification_uppercase'), msgError,
                        [
                            {
                                text: translate('common.btn_skip'),
                                style: "cancel",
                                onPress: hideBlockUI
                            },
                            {
                                text: translate('common.btn_notify_try_again'),
                                style: "default",
                                onPress: () => verifyIdentify(codeID, dataID)
                            }
                        ]
                    )
                })
        }
    }

    const onClearPoint = () => {
        onApplyPoint(0);
    }

    const onSwitchCamera = (value) => () => {
        setIsVisible(value);
    }

    const onPayment = () => {
        const isValidate = validatePoint();
        if (isValidate) {
            showBlockUI();
            const value = convertString2Number(point);
            onApplyPoint(value);
            setPoint("");
            setIdentifyCode("");
        }
    }

    const handleGetLoyaltyTransactionInfo = () => {
        showBlockUI();
        dispatch(
            getLoyaltyTransactionInfo({
                saleOrderId: dataSaleOrder.SaleOrderID,
                sessionId: dataSaleOrder.cus_SessionID
            })
        )
            .then((response) => {
                setIsVisibleAlert(true);
                setCreatedAt(response.CreatedDate);
            })
            .catch((error) => {
                Alert.alert(translate(keys.common.notification), error);
            })
            .finally(hideBlockUI);
    };

    return (
        <View style={{
            width: constants.width,
        }}>
            {/* <TypeApply
                icon={"looks-one"}
                title={translate('saleOrderPayment.pay_by_point')}
            /> */}
            {
                isApply
                    ?
                    <PointApply
                        point={usePoint}
                        onClear={onClearPoint}
                        status={loyaltyStatus}
                        onCheckStatus={handleGetLoyaltyTransactionInfo}
                    />
                    : <View style={{
                        width: constants.width,
                        borderBottomWidth: StyleSheet.hairlineWidth,
                        borderBottomColor: COLORS.bdE4E4E4
                    }}>
                        {/* <UsePointInfo
                            availablePoint={availablePoint}
                        /> */}
                        <InputPoint
                            value={point}
                            onChange={(text) => {
                                if (isValidatePoint(text)) {
                                    setPoint(text);
                                }
                            }}
                            expireTime={expireTime}
                        />
                        {/* <RadioVerify
                            isOtp={isOtp}
                            onSwitch={(value) => {
                                setIsOtp(value);
                            }}
                        /> */}
                        {
                            isOtp
                                ?
                                <OtpCode
                                    expireTime={expireTime}
                                    code={otpCode}
                                    onChange={(text) => {
                                        if (regExpInputOTP.test(text)) {
                                            setOtpCode(text);
                                        }
                                    }}
                                    onCreate={onCreateOTP}
                                    onVerify={() => {
                                        verifyOTP(otpCode, customerPhone);
                                    }}
                                    customerPhone={customerPhone}
                                />
                                :
                                <InputIdentify
                                    value={identifyCode}
                                    onChange={(text) => {
                                        if (regExpInputID.test(text)) {
                                            setIdentifyCode(text);
                                        }
                                    }}
                                    onScan={onSwitchCamera(true)}
                                    onVerify={onVlidateID}
                                    onPayment={onPayment}
                                    isVerify={isVerify}
                                    placeholder={translate('saleOrderPayment.placeholder_enter_ID_code')}
                                />
                        }
                    </View>
            }
            {
                isVisible &&
                <BarcodeCamera
                    isVisible={isVisible}
                    closeCamera={onSwitchCamera(false)}
                    resultScanBarcode={(barcode) => {
                        setIsVisible(false);
                        verifyIdentify("", barcode);
                    }}
                />
            }
            <AlertMessage
                title="Chi tiết giao dịch"
                visible={isVisibleAlert}
                onClose={() => setIsVisibleAlert(false)}>
                <Transaction
                    name="Quà tặng VIP"
                    status={loyaltyStatus}
                    amount={usePoint}
                    time={createdAt}
                />
            </AlertMessage>
        </View>
    );
}

export default PaymentPoint;

const regExpPoint = new RegExp(/^[1-9][0-9]{0,9}$/);
const regExpOTP = new RegExp(/^\d{4}$/);
const regExpInputOTP = new RegExp(/^\d{0,4}$/);
const regExpID4 = new RegExp(/^\d{4}$/);
const regExpID6 = new RegExp(/^\d{6}$/);
const regExpInputID = new RegExp(/^\d{0,6}$/);

const isValidatePoint = (stringValue) => {
    const isValidateRange = regExpPoint.test(stringValue);
    const isEmpty = (stringValue == "");
    return isEmpty || isValidateRange;
}

const convertString2Number = (stringValue) => {
    return stringValue ? Math.round(parseFloat(stringValue) / 100) * 100 : 0
}
