import React from 'react';
import {
    View,
    Image,
    TouchableOpacity
} from 'react-native';
import { MyText } from "@components";
import { constants } from '@constants';
import { helper } from "@common";
import { translate } from '@translate';
import { COLORS } from "@styles";

const Transaction = ({
    data,
    onGetQR,
    onQueryQR,
    dataQRType
}) => {

    const onPressItem = (ele) => () => {
        const transactionType = dataQRType.find(qrType => ele.PaymentTransactionTypeID == qrType.PaymentTransactionTypeID);
        const info = {
            amount: ele.Amount,
            saleOrderID: ele.VoucherConcern,
            transactionType: transactionType,
            qrValue: ele.QRCodeData,
            transactionID: ele.PaymentTransactionID,
            expried: ele.QRCodeExpriedDate
        }
        onQueryQR(info);
    }

    const renderItem = (ele) => {
        const {
            Amount,
            PaymentTransactionID,
            CustomerDisplayName,
            PaymentStatusID,
            PaymentTransactionTypeID,
            CMD
        } = ele;
        const { OPEN, PENDING } = getStatusType(PaymentTransactionTypeID, PaymentStatusID, CMD);
        const disabled = !OPEN && !PENDING;
        return (
            <TouchableOpacity style={{
                width: constants.width,
                flexDirection: "row",
                justifyContent: "space-between",
                paddingHorizontal: 4,
                paddingVertical: 6,
                alignItems: "center"
            }}
                key={`${PaymentTransactionID}`}
                onPress={onPressItem(ele)}
                activeOpacity={0.8}
                disabled={disabled}
            >
                <View style={{
                    width: constants.width - 280,
                    flexDirection: "row",
                    paddingLeft: 4
                }}>
                    <Image
                        source={{ uri: "qr_code" }}
                        style={{
                            width: 12,
                            height: 12,
                            marginTop: 2,
                            tintColor: "red"
                        }}
                    />
                    <MyText
                        text={CustomerDisplayName}
                        addSize={-1.5}
                        style={{
                            color: COLORS.txt333333,
                            width: constants.width - 292,
                            paddingLeft: 2
                        }} />
                </View>
                <MyText
                    text={helper.convertNum(Amount)}
                    addSize={-1.5}
                    style={{
                        color: COLORS.txt333333,
                        width: 100,
                        textAlign: 'center',
                    }} />
                <MyText
                    text={getStatusName(PaymentStatusID)}
                    addSize={-1.5}
                    style={{
                        color: COLORS.txt333333,
                        width: 100,
                        textAlign: 'center',
                    }} />
                <View style={{
                    width: 64,
                    alignItems: "center"
                }}>
                    <MyText
                        text={"Kiểm tra"}
                        addSize={-2.5}
                        style={{
                            color: COLORS.txt147EFB,
                            paddingHorizontal: 6,
                            paddingVertical: 4,
                            borderRadius: 8,
                            textAlign: "center",
                            borderWidth: 1,
                            borderColor: COLORS.bd147EFB,
                            fontWeight: "bold"
                        }} />
                </View>
            </TouchableOpacity>
        );
    }

    return (
        <View style={{
            width: constants.width,
            backgroundColor: COLORS.bgFFFFFF,
            paddingVertical: 8
        }}>
            <MyText
                text={translate('saleOrderPayment.QR_transaction_ongoing')}
                style={{
                    color: COLORS.txtB52E31,
                    fontWeight: 'bold',
                    paddingHorizontal: 8
                }} />
            <Header />
            {
                data.map((ele, index) => renderItem(ele))
            }
        </View>
    );
}

export default Transaction;

const Header = () => {
    return (
        <View style={{
            width: constants.width,
            flexDirection: "row",
            justifyContent: "space-between",
            paddingHorizontal: 4,
            paddingTop: 8
        }}>
            <MyText
                text={translate('saleOrderPayment.QR_code_type')}
                addSize={-1}
                style={{
                    color: COLORS.txt333333,
                    fontWeight: 'bold',
                    width: constants.width - 280,
                    textAlign: 'left',
                    paddingLeft: 4
                }} />
            <MyText
                text={translate('saleOrderPayment.money_amount')}
                addSize={-1}
                style={{
                    color: COLORS.txt333333,
                    width: 100,
                    textAlign: 'center',
                    fontWeight: 'bold'
                }} />
            <MyText
                text={translate('saleOrderPayment.status')}
                addSize={-1}
                style={{
                    color: COLORS.txt333333,
                    width: 100,
                    textAlign: 'center',
                    fontWeight: 'bold',
                }} />
            <View style={{
                width: 64,
            }} />
        </View>
    );
}

const getStatusName = (status) => {
    switch (status) {
        case "OPEN":
        case "00":
        case "MWGOPEN":
        case "0":
        case "1000":
            return "Khởi tạo";
        case "PROCESSING":
        case "MWGPROCESSING":
        case "MOMO_PROCESSING":
        case "CREATED":
        case "IN_PROGRESS":
        case "400":
        case "422":
        case "500":
        case "MOMOINS_PROCESSING":
        case "KREDIVO_PROCESSING":
        case "CAKE_PROCESSING":
        case "QTV_PROCESSING":
            return "Đang xử lý";
        case "TPBanhEVO_PROCESSING":
            return "Đang xử lý";
        case "SCANNED":
            return "Scaned";
        default:
            return "";
    }
}

const getStatusType = (type, status, cmd) => {
    switch (type) {
        case 1:
            return {
                OPEN: (cmd == "GEN") || (status == "MWGOPEN") || (status == "00"),
                PENDING: false
            };
        case 5:
            return {
                OPEN: (cmd == "GEN") || (status == "OPEN") || (status == "00"),
                PENDING: (status == "PROCESSING") || (status == "SCANNED")
            };
        case 6:
            return {
                OPEN: (cmd == "GEN") || (status == "0") || (status == "00"),
                PENDING: (status == "MWGPROCESSING")
            };
        case 7:
            return {
                OPEN: (cmd == "GEN") || (status == "0") || (status == "00"),
                PENDING: (status == "MOMO_PROCESSING")
            };
        case 10:
            return {
                OPEN: (cmd == "GEN") || (status == "0") || (status == "3") || (status == "00"),
                PENDING: false
            };
        case 13:
            return {
                OPEN: (cmd == "GEN") || (status == "CREATED") || (status == "00") || (status == 'IN_PROGRESS') || (status == "400") || (status == "422") || (status == "500"),
                PENDING: false

            }
        case 14:
            return {
                OPEN: (cmd == "GEN") || (status == "0") || (status == "00"),
                PENDING: (status == "MOMOINS_PROCESSING")
            };
        case 15:
            return {
                OPEN: (cmd == "GEN") || (status == "0") || (status == "00"),
                PENDING: (status == "KREDIVO_PROCESSING")
            };
        case 16:
            return {
                OPEN: (cmd == "GEN") || (status == "0") || (status == "00"),
                PENDING: (status == "CAKE_PROCESSING")
            };
        case 17:
        case 19:
            return {
                OPEN: (cmd == "GEN") || (status == "0") || (status == "00"),
                PENDING: (status == "QTV_PROCESSING")
            };
        case 18:
            return {
                OPEN: (cmd == "GEN") || (status == "0") || (status == "00"),
                PENDING: (status == "TPBanhEVO_PROCESSING")
            };
        default:
            return {
                OPEN: false,
                PENDING: false
            };
    }
}