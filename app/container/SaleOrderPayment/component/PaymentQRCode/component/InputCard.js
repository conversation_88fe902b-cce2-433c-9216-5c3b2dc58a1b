import React, { useState } from 'react';
import {
    View,
    StyleSheet,
    Alert,
} from 'react-native';
import {
    Button,
    MyText,
    NumberInput
} from "@components";
import { constants } from '@constants';
import { helper } from "@common";
import PickerQR from "../../Modal/PickerQR";
import { translate } from '@translate';
import { COLORS } from "@styles";

const InputCard = ({
    maxPayment,
    dataSO,
    data,
    createQR
}) => {
    const [transactionType, setTransactionType] = useState({});
    const [money, setMoney] = useState(0);
    const { SaleOrderID } = dataSO;

    const validateCard = () => {
        if (helper.IsEmptyObject(transactionType)) {
            Alert.alert("", translate('saleOrderPayment.please_choose_QR_type'));
            return false;
        }
        if (money == 0) {
            Alert.alert("", translate('saleOrderPayment.please_enter_pay_amount'));
            return false;
        }
        if (money > maxPayment) {
            Alert.alert("", translate('saleOrderPayment.pay_amount_larger'));
            return false;
        }
        if (transactionType?.PaymentTransactionTypeID == 13 && money < 10000 || transactionType?.PaymentTransactionTypeID == 13 && money > 3000000) {
            Alert.alert("", "Chỉ được thanh toán số tiền từ 10,000vnđ đến 3,000,000vnđ");
            return false;
        }
        if (transactionType?.PaymentTransactionTypeID == 14 && money < 3750000) {
            Alert.alert("", "Chỉ được thanh toán số tiền từ 3,750,000vnđ");
            return false;
        }
        return true;
    }

    const onCreateQR = () => {
        const isValidate = validateCard();
        if (isValidate) {
            const info = {
                "saleOrderID": SaleOrderID,
                "transactionType": transactionType,
                "amount": money,
                "orderTypeID": dataSO.OrderTypeID
            }
            createQR(info);
            setTransactionType({});
            setMoney(0);
        }
    }

    return (
        <View style={{
            backgroundColor: COLORS.bgFFFFFF,
            width: constants.width,
            paddingTop: 0
        }}>
            <View style={{
                flexDirection: "row",
                borderBottomWidth: StyleSheet.hairlineWidth,
                borderBottomColor: COLORS.bdFFFFFF,
                width: constants.width
            }}>
                <View style={{
                    height: 40,
                    justifyContent: "center",
                    alignItems: "flex-start",
                    width: 138,
                    borderRightWidth: 1,
                    borderRightColor: COLORS.bdFFFFFF,
                    paddingHorizontal: 10,
                    backgroundColor: COLORS.bgF0F0F0,
                }}>
                    <MyText
                        text={translate('saleOrderPayment.QR_code_type_colon')}
                        style={{
                            color: COLORS.txt000000
                        }} />
                </View>
                <View style={{
                    height: 40,
                    justifyContent: "center",
                    alignItems: "flex-end",
                    width: constants.width - 140,
                    borderWidth: StyleSheet.hairlineWidth,
                    borderColor: COLORS.bdE4E4E4
                }}>
                    <PickerQR
                        defaultLabel={"Chọn ví"}
                        selectedValue={transactionType}
                        data={data}
                        onValueChange={(item) => {
                            setTransactionType(item);
                        }}
                    />
                </View>
            </View>
            <View style={{
                flexDirection: "row",
                width: constants.width,
            }}>
                <View style={{
                    height: 40,
                    justifyContent: "center",
                    alignItems: "flex-start",
                    width: 138,
                    borderRightWidth: 1,
                    borderRightColor: COLORS.bdFFFFFF,
                    paddingHorizontal: 10,
                    backgroundColor: COLORS.bgF0F0F0,
                }}>
                    <MyText
                        text={translate('saleOrderPayment.money_amount')}
                        style={{
                            color: COLORS.txt000000
                        }} />
                </View>
                <View style={{
                    height: 40,
                    justifyContent: "center",
                    alignItems: "flex-end",
                    width: constants.width - 140,
                    borderWidth: StyleSheet.hairlineWidth,
                    borderColor: COLORS.bdE4E4E4
                }}>
                    <View
                        style={{
                            height: 40,
                            justifyContent: "center",
                            alignItems: "flex-end",
                            width: constants.width - 140,
                        }}
                    >
                        <NumberInput
                            style={{
                                height: 38,
                                width: constants.width - 142,
                                backgroundColor: COLORS.bgFFFFFF,
                                paddingHorizontal: 8,
                                justifyContent: "center",
                                alignItems: "flex-end",
                                textAlign: "right"
                            }}
                            placeholder={""}
                            value={money}
                            onChangeText={(value) => {
                                setMoney(value);
                            }}
                        />
                    </View>
                </View>
            </View>
            <View style={{
                justifyContent: "center",
                alignItems: "center",
                paddingHorizontal: 10,
                paddingVertical: 6,
                flexDirection: "row",
                width: constants.width,
                backgroundColor: COLORS.bgF0F0F0,
                borderTopWidth: StyleSheet.hairlineWidth,
                borderTopColor: COLORS.bdFFFFFF,
            }}>
                <Button
                    text={translate('saleOrderPayment.btn_create_QR_code')}
                    styleContainer={{
                        backgroundColor: COLORS.btn288AD6,
                        borderRadius: 4,
                        justifyContent: "center",
                        alignItems: "center",
                        padding: 8,
                        borderColor: COLORS.bd288AD6,
                        borderWidth: 1,
                        width: 165,
                    }}
                    styleText={{
                        color: COLORS.txtFFFFFF,
                        fontSize: 14,
                        fontWeight: "bold"
                    }}
                    onPress={onCreateQR}
                />
            </View>
        </View>
    );
}

export default InputCard;
