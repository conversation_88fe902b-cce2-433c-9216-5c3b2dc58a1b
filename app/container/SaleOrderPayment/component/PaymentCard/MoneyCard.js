import React from 'react';
import {
    View,
    TouchableOpacity,
    StyleSheet,
} from 'react-native';
import {
    Icon,
    MyText
} from "@components";
import { constants } from '@constants';
import { helper } from "@common";
import { translate } from '@translate';
import { COLORS } from "@styles";

const { MONEY_CARD_ID } = constants;

const MoneyCard = ({ info, onRemove, isApplySC }) => {
    const {
        MoneyCardName,
        MoneyCardVoucherID,
        MoneyCard,
        MoneyCardID
    } = info;
    const isSmartPos = (MoneyCardID == 421) && isApplySC;
    const isVPBank = String(MoneyCardID) === MONEY_CARD_ID.VP_BANK

    return (
        <View style={{
            flexDirection: "row",
            width: constants.width - 4,
            paddingHorizontal: 8,
            paddingVertical: 6,
            justifyContent: "space-between",
            backgroundColor: COLORS.bgFFFFFF,
            borderWidth: StyleSheet.hairlineWidth,
            borderColor: COLORS.bd999999,
            margin: 2,
            borderRadius: 4
        }}>
            <Icon
                iconSet={"Ionicons"}
                name={"checkmark"}
                color={COLORS.ic147EFB}
                size={16}
                style={{ marginTop: 2 }}
            />
            <View style={{
                width: constants.width - 72
            }}>
                <MyText
                    text={MoneyCardName}
                    style={{
                        color: COLORS.txt288AD6,
                        fontWeight: 'bold'
                    }} />
                <View style={{
                    width: constants.width - 38,
                    flexDirection: "row",
                    justifyContent: "space-between",
                    marginTop: 4,
                }}>
                    <MyText
                        text={translate('saleOrderPayment.appv_code')}
                        style={{
                            color: COLORS.txt555555,
                            fontWeight: 'bold',
                            width: constants.width - 140
                        }} />
                    <MyText
                        text={MoneyCardVoucherID}
                        style={{
                            color: COLORS.txt333333,
                            width: 102,
                            textAlign: 'right'
                        }} />
                </View>
                <View style={{
                    width: constants.width - 38,
                    flexDirection: "row",
                    justifyContent: "space-between",
                    marginTop: 4,
                }}>
                    <MyText
                        text={translate('saleOrderPayment.money_amount')}
                        style={{
                            color: COLORS.txt555555,
                            fontWeight: 'bold',
                            width: constants.width - 140
                        }} />
                    <MyText
                        text={helper.convertNum(MoneyCard)}
                        style={{
                            color: COLORS.txt333333,
                            width: 102,
                            textAlign: 'right'
                        }} />
                </View>
            </View>
            <TouchableOpacity style={{
                width: 32,
                alignItems: "center",
                height: 24
            }}
                onPress={onRemove}
                disabled={isSmartPos || isVPBank}
            >
                {
                    !isSmartPos && !isVPBank &&
                    <Icon
                        iconSet={"MaterialCommunityIcons"}
                        name={"close-box"}
                        size={22}
                        color={COLORS.icFC3158}
                    />
                }
            </TouchableOpacity>
        </View>
    );
}

export default MoneyCard;