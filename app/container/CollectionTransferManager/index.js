import React, { useEffect, useRef, useState } from 'react';
import { View, SafeAreaView, Dimensions, StyleSheet, Text, Alert } from 'react-native';
import { COLORS } from '@styles';
import { MyText } from '@components';
import {
    PagerTitleIndicator
} from 'react-native-best-viewpager';
import { connect, useSelector } from 'react-redux';
import { helper } from '@common';
import ButtonTabs from './component/ButtonTabs'; import HistorySell from './Screen/HistorySell';
import RefundHistory from './Screen/RefundHistory';
;

const SearchCollection = ({ navigation, route }) => {

    const userInfo = useSelector((state) => state.userReducer);
    if (!helper.isWithinSaleHours(userInfo)) {
        return Alert.alert("", "Bạn chỉ được phép thao tác chức năng này trong giờ bán hàng.", [
            {
                text: "Ok",
                onPress: navigation.goBack
            },
        ]);
    }

    const [activeTab, setActiveTab] = useState(0)
    const {
        SaleOrderID,
    } = route.params ?? {};

    const [data, setData] = useState({
        fromDate: '',
        toDate: ''
    })
    useEffect(() => {
        setData(data)
    }, [data]);

    return (
        <SafeAreaView
            style={{
                flex: 1,
                backgroundColor: COLORS.bgFFFFFF,
            }}>
            <ButtonTabs
                TabsOne={'Xem lịch sử giao dịch'}
                TabsTwo={'Xem lịch sử hoàn tiền'}
                activeTab={activeTab}
                setActiveTab={setActiveTab} />
            {activeTab === 0 && (
                <HistorySell
                    navigation={navigation}
                    SaleOrderID={SaleOrderID}
                />
            )}
            {activeTab === 1 && (
                <RefundHistory
                    navigation={navigation}
                />
            )}
        </SafeAreaView>
    )
}

export const renderPagerTitleIndicator = (titles) => {
    const itemWidth = {
        width: Dimensions.get('window').width / titles.length - 30
    };
    return (
        <PagerTitleIndicator
            initialPage={0}
            style={{ backgroundColor: COLORS.bg00A98F }}
            titles={titles}
            trackScroll={true}
            selectedBorderStyle={{
                backgroundColor: COLORS.bgFFF000,
                height: 2,
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
            }}
            renderTitle={(index, title, isSelected) => {
                return (
                    <View style={itemWidth}>
                        <MyText
                            style={{
                                color: isSelected
                                    ? COLORS.txtFFF000
                                    : COLORS.txtFFFFFF,
                                fontWeight: 'bold',
                                textAlign: 'center'
                            }}
                            text={title}
                        />
                    </View>
                );
            }}
        />
    );
};
const mapStateToProps = function (state) {
    return {
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(SearchCollection)

const styles = StyleSheet.create({})