import { StyleSheet, Text, View, TouchableOpacity } from 'react-native'
import React, { useState } from 'react'
import { constants } from '@constants'
import { COLORS } from '@styles'
import { Icon, MyText } from '@components'
import { translate } from '@translate'
import DropdownMenu from '../../CollectInstallmentPayments/component/DropdowmnBrightside';
import { helper } from '@common'

const ItemRefund = ({
    info,
    onPressDetail,
    onRefresh,
    onDeleteFefund
}) => {
    const {
        PRODUCTSHORTNAME,
        RQREFUNDTYPENAME,
        RECEIVEDBYPARTNER,
        APPROVALSTATUSNAME,
        AIRTIMECUSTOMERNAME,
        APPROVEDMONEY,
        DATEREQUESTREFUND,
        ISALLOWCASHOUT,
        OUTPUTRECEIPTID,
        PHONENUMBER,
        ISALLOWDELETEREFUND
    } = info

    const [isShow, setIsShow] = useState(false)
    return (
        <View style={styles.container}>
            <View
                style={styles.children}>
                <View style={{
                    flexDirection: "row",
                    width: constants.width - 20
                }}>
                    <TouchableOpacity
                        // disabled={AIRTIMETRANSACTIONTYPEID == 1513 ? false : true}
                        onPress={onPressDetail}
                        style={{
                            flexDirection: 'row',
                            justifyContent: 'center',
                            alignItems: 'center'
                        }}
                    >
                        <MyText
                            text={OUTPUTRECEIPTID}
                            style={{
                                color: COLORS.bg2C8BD7,
                                fontWeight: 'bold',
                                textAlign: 'center',
                            }}
                        />
                    </TouchableOpacity>
                </View>
                <TextField
                    name={"SHĐ/Tài khoản/IMEI: "}
                    value={PHONENUMBER}
                    key={"PHONENUMBER"}
                    extraStyle={{ color: COLORS.bg000000 }}
                />
                <TextField
                    name={'Tên sản phẩm: '}
                    value={PRODUCTSHORTNAME}
                    key={'PRODUCTSHORTNAME'}
                    extraStyle={{ color: COLORS.bg000000 }}
                />
                <TextField
                    name={'Loại y/c hoàn tiền: '}
                    value={RQREFUNDTYPENAME}
                    key={'PRODUCTSHORTNAME'}
                    extraStyle={{ color: COLORS.bg000000 }}
                />
                <TextField
                    name={'Đối tác: '}
                    value={RECEIVEDBYPARTNER}
                    key={'PRODUCTSHORTNAME'}
                    extraStyle={{ color: COLORS.bg000000 }}
                />
                <TextField
                    name={'Kết quả duyệt:'}
                    value={APPROVALSTATUSNAME}
                    key={'PRODUCTSHORTNAME'}
                    extraStyle={{ color: COLORS.bg000000 }}
                />
                <MoneyField
                    name={'Số tiền được chi:'}
                    value={`${helper.formatMoney(APPROVEDMONEY, false)} `}
                    key={'PRODUCTSHORTNAME'}
                    extraStyle={{ color: COLORS.bg000000 }}
                />
                <TextField
                    name={'Thời gian hủy: '}
                    value={DATEREQUESTREFUND}
                    key={'PRODUCTSHORTNAME'}
                    extraStyle={{ color: COLORS.bg000000 }}
                />
                <View style={{
                    flexDirection: 'row',
                }}>
                    {
                        ISALLOWCASHOUT == true && <View
                            style={{
                                marginTop: 10
                            }}
                        >
                            <TouchableOpacity onPress={onRefresh} style={{
                                flexDirection: 'row',
                            }}>
                                <MyText
                                    text={'Chi tiền mặt'}
                                    addSize={-1.5}
                                    style={{
                                        color: COLORS.bg2FB47C,
                                        fontWeight: 'bold',
                                    }}
                                />
                                <Icon
                                    iconSet={'Ionicons'}
                                    name={'refresh'}
                                    color={COLORS.bg2FB47C}
                                    style={{
                                        left: 5
                                    }}
                                />
                            </TouchableOpacity>
                        </View>
                    }
                    <View style={{ flex: 1 }} />
                    {
                        ISALLOWDELETEREFUND == true && <View
                            style={{
                                marginTop: 10,
                                flexDirection: "row",
                            }}
                        >
                            <TouchableOpacity onPress={onDeleteFefund} style={{
                                flexDirection: 'row',
                            }}>
                                <MyText
                                    text={'Hủy yêu cầu'}
                                    addSize={-1.5}
                                    style={{
                                        color: COLORS.bg00AAFF,
                                        fontWeight: 'bold',
                                    }}
                                />
                                <Icon
                                    iconSet={'MaterialIcons'}
                                    name={'auto-delete'}
                                    color={COLORS.bg00AAFF}
                                    style={{
                                        left: 5
                                    }}
                                />
                            </TouchableOpacity>
                        </View>
                    }
                </View>
            </View>
        </View>
    )
}

export default ItemRefund

const styles = StyleSheet.create({
    container: {
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 5,
        paddingVertical: 5
    },
    children: {
        width: constants.width,
        padding: 8,
        width: '97%',
        padding: 10,
        borderColor: COLORS.bd218DEB,
        backgroundColor: COLORS.bgFFFFFF,
        borderRadius: 10,
        shadowColor: COLORS.bg000000,
        shadowOffset: {
            width: 0,
            height: 2
        },
        shadowOpacity: 0.25,
        shadowRadius: 4,
        elevation: 5,
    }
})
const MoneyField = ({ name, value, color = COLORS.txt333333 }) => {
    return (
        <View style={{
            flexDirection: "row",
            justifyContent: "space-between",
            marginTop: 10
        }}>
            <MyText
                text={name}
                addSize={-1.5}
                style={{
                    color: COLORS.txt8E8E93,
                    fontWeight: 'bold',
                }}
            />
            <MyText
                text={value}
                addSize={1}
                style={{
                    color: COLORS.bgEA1D5D,
                    fontWeight: 'bold'
                }}
            />
        </View>
    );
}

const TextField = ({ name, value, extraStyle, isWarning }) => {
    return (
        <View style={{
            flexDirection: 'row',
            marginTop: 5,
            justifyContent: 'center',
            alignItems: 'center',
        }}>
            <MyText
                text={name}
                addSize={-1}
                style={{
                    color: COLORS.txt8E8E93,
                    flex: 1,
                    fontWeight: 'bold',
                }} />
            <MyText
                text={value}
                style={[{
                    color: isWarning ? COLORS.txtFF0000 : COLORS.txt333333,
                    flex: 1,
                    textAlign: 'right'
                }, extraStyle]}
            />
        </View>
    );
}
const IconField = ({
    title,
    name,
    color,
    textAlign,
    onPress,
    iconSet = 'Ionicons'
}) => {
    return (
        <TouchableOpacity
            activeOpacity={0.6}
            onPress={onPress}
        >
            <MyText
                text={title}
                addSize={-1}
                style={{
                    color: color,
                    textAlign: textAlign,
                    paddingVertical: 2
                }}>
                {" "}
                <Icon
                    iconSet={iconSet}
                    name={name}
                    color={color}
                    size={14}
                />
            </MyText>
        </TouchableOpacity>
    );
}

