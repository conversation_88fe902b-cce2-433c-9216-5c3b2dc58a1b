import React, { useState } from 'react';
import { View, TouchableOpacity } from 'react-native';
import { Icon, MyText } from "@components";
import { constants } from '@constants';
import { helper, dateHelper } from "@common";
import { translate } from '@translate';
import { COLORS } from "@styles";

const ItemCollection = ({
  item,
  onPressDetail,
  onPrint,
  onPayment,
  onAskStatus,
  onActiveCollection,
  onEdit,
  onDelete,
  navigation,
  ServiceCategoryID,
  AirtimeServiceGroupID,
  onRefund,
  onPay,
  regainTicket,
  onPreEnterOTP,
  printPromotion,
  onCancelTransaction
}) => {
  const {
    SERVICEVOUCHERID,
    SALEORDERID,
    <PERSON>RTIMETRANSACTIONTYPENAME,
    CUSTOMERALIAS,
    <PERSON>RTIMESTATUSNAME,
    CUSTOMERNAME,
    TOTALPAID,
    IsDeletedSO,
    IsQueryStatus,
    IsReActive,
    ISCOLLECTMONEY,
    ISPENDING,
    ISALLOWDELETE,
    DESC<PERSON>PTION,
    IS<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    AIRTIMETRANS<PERSON><PERSON>ON<PERSON>PEID,
    ISAL<PERSON><PERSON><PERSON>FUNDSERVICE,
    ISPRINTINSURANCE<PERSON><PERSON>,
    PH<PERSON><PERSON>UMBER,
    ISALLOWCASHOUT,
    OUTVOUCHERID,
    ISWAITINGTICKET,
    STAFFUSERNAME,
    CREATEDDATE,
    ISVIEWONLY,
    ISVERIFYOTP,
    PROMOTIONSALEORDERID,
    ISALLOWDELETETRANSACTION
  } = item;
  const [isShow, setIsShow] = useState(false);

  const TriangleCorner = () => {
    return (
      <View style={{
        position: 'absolute',
        top: 0,
        right: 0,
        width: 0,
        height: 0,
        borderLeftWidth: 50,
        borderTopWidth: 50,
        borderLeftColor: 'transparent',
        borderTopColor: '#5B99C2',
        borderTopRightRadius: 10,
        shadowColor: COLORS.bg000000,
        shadowOffset: {
          width: 0,
          height: 3,
        },
        shadowOpacity: 0.2,
        shadowRadius: 4.65,
        elevation: 6,
        justifyContent: 'center',
        alignItems: 'center',
      }}>
        <Icon
          iconSet={"Ionicons"}
          name={"lock-closed"}
          color={COLORS.bgFFFFFF}
          size={18}
          style={{
            position: 'absolute',
            top: -45,
            right: 5
          }}
        />
      </View>
    );
  }

  return (
    <View style={{
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 5,
      paddingVertical: 5
    }}>
      <View
        style={{
          width: constants.width,
          padding: 8,
          width: '97%',
          padding: 10,
          borderColor: COLORS.bd218DEB,
          backgroundColor: COLORS.bgFFFFFF,
          borderRadius: 10,
          shadowColor: COLORS.bg000000,
          shadowOffset: {
            width: 0,
            height: 2
          },
          shadowOpacity: 0.25,
          shadowRadius: 4,
          elevation: 5
        }}>
        {
          ISVIEWONLY === 1 &&
          <TriangleCorner />
        }
        <View style={{
          flexDirection: "row",
        }}>
          <TouchableOpacity
            disabled={AIRTIMETRANSACTIONTYPEID == 1513 ? false : true}
            onPress={onPressDetail}
            style={{
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center'
            }}
          >
            <MyText
              text={SERVICEVOUCHERID}
              style={{
                color: COLORS.bg2C8BD7,
                fontWeight: 'bold',
                textAlign: 'center',
              }}
            />
            <MyText
              text={" - "}
              addSize={-1.5}
              style={{
                color: COLORS.bgEA1D5D,
                fontWeight: 'bold'
              }}
            />
            <MyText
              text={CUSTOMERALIAS}
              addSize={-1.5}
              style={{
                color: COLORS.bg000000,
                fontSize: 15,
              }}

            />
            <Icon
              iconSet={'Ionicons'}
              name={"create-outline"}
              color={COLORS.bg2C8BD7}
              size={16}
              style={{
                marginLeft: 4,
                marginBottom: 4
              }}
            />
          </TouchableOpacity>
        </View>
        <TextField
          name={"Nhân viên tạo: "}
          value={STAFFUSERNAME}
          key={"STAFFUSERNAME"}
          extraStyle={{ color: COLORS.bg000000 }}
        />
        <TextField
          name={"Ngày tạo: "}
          value={dateHelper.formatStrDateFULL(CREATEDDATE)}
          key={"CREATEDDATE"}
          extraStyle={{ color: COLORS.bg000000 }}
        />
        <TextField
          name={"SHĐ/Tài khoản/IMEI: "}
          value={PHONENUMBER}
          key={"PHONENUMBER"}
          extraStyle={{ color: COLORS.bg000000 }}
        />
        {
          AIRTIMETRANSACTIONTYPEID != 1933 ?
            <TextField
              name={translate("collection.export_request")}
              value={SALEORDERID}
              key={"SALEORDERID"}
              extraStyle={{ color: COLORS.bg000000 }}
            />
            :
            <TextField
              name={"Mã phiếu chi: "}
              value={OUTVOUCHERID}
              key={"OUTVOUCHERID"}
              extraStyle={{ color: COLORS.bg000000 }}
            />
        }
        <TextField
          name={translate("collection.transaction_type")}
          value={AIRTIMETRANSACTIONTYPENAME}
          key={"AIRTIMETRANSACTIONTYPENAME"}
          isWarning={AIRTIMETRANSACTIONTYPENAME}
          extraStyle={{ color: COLORS.bg000000 }}
        />
        <TextField
          name={translate("collection.status")}
          value={AIRTIMESTATUSNAME}
          key={"AIRTIMESTATUSNAME"}
          extraStyle={{ fontWeight: 'bold', color: COLORS.txtFF0000 }}
        />
        <MyText
          text={`${translate("collection.note")} ${DESCRIPTION}`}
          addSize={-1.5}
          style={{
            color: COLORS.bg8E8E93,
            marginTop: 10,
            fontStyle: 'italic'
          }}
        />
        <TextField
          name={translate("collection.customer_name")}
          value={CUSTOMERNAME}
          key={"CUSTOMERNAME"}
          extraStyle={{ color: COLORS.bg000000 }}
        />
        <MoneyField
          name={translate("collection.amount_of_money")}
          value={`${helper.formatMoney(TOTALPAID, false)} `}
          color={COLORS.txtFF0000}
          key={"Payment"}
        />
        <ButtonAction
          navigation={navigation}
          onPayment={onPayment}
          onPrint={onPrint}
          onActionList={() => setIsShow(!isShow)}
          onAskStatus={onAskStatus}
          onEdit={onEdit}
          onActiveCollection={onActiveCollection}
          ishow={isShow}
          isDeletedSO={IsDeletedSO}
          IsQueryStatus={IsQueryStatus}
          IsReActive={IsReActive}
          ISCOLLECTMONEY={ISCOLLECTMONEY}
          ISPENDING={ISPENDING}
          ISSPRINTPAYBILL={ISSPRINTPAYBILL}
          item={item}
          ServiceCategoryID={ServiceCategoryID}
          AirtimeServiceGroupID={AirtimeServiceGroupID}
          ISALLOWREFUNDSERVICE={ISALLOWREFUNDSERVICE}
          ISPRINTINSURANCEFILE={ISPRINTINSURANCEFILE}
          onRefund={onRefund}
          ISALLOWCASHOUT={ISALLOWCASHOUT}
          onPay={onPay}
          ISWAITINGTICKET={ISWAITINGTICKET}
          regainTicket={regainTicket}
          ISVERIFYOTP={ISVERIFYOTP}
          onPreEnterOTP={onPreEnterOTP}
          PROMOTIONSALEORDERID={PROMOTIONSALEORDERID}
          printPromotion={printPromotion}
        />
        {
          ISALLOWDELETE == 1 ?
            <ButtonDelete
              isVisible={true}
              onDelete={onDelete}
              appName={"POS"}
              totalPaid={TOTALPAID}
            />
            :
            null
        }
        {
          ISALLOWDELETETRANSACTION == 1 ?
            <ButtonCancelTransaction
              isVisible={true}
              onCancelTransaction={onCancelTransaction}
              appName={"POS"}
              totalPaid={TOTALPAID}
            />
            :
            null
        }
      </View>
    </View>
  );
}

export default ItemCollection;

const MoneyField = ({ name, value, color = COLORS.txt333333 }) => {
  return (
    <View style={{
      flexDirection: "row",
      justifyContent: "space-between",
      marginTop: 10
    }}>
      <MyText
        text={name}
        addSize={-1.5}
        style={{
          color: COLORS.txt8E8E93,
        }}
      />
      <MyText
        text={value}
        addSize={-1.5}
        style={{
          color: color,
          fontWeight: 'bold'
        }}
      />
    </View>
  );
}

const TextField = ({ name, value, extraStyle, isWarning }) => {
  return (
    <MyText
      text={name}
      addSize={-1.5}
      style={{
        color: COLORS.txt8E8E93,
        marginTop: 10
      }}>
      <MyText
        text={value}
        style={[{
          color: isWarning ? COLORS.txtFF0000 : COLORS.txt333333,
        }, extraStyle]}
      />
    </MyText>
  );
}

const IconField = ({
  title,
  name,
  color,
  textAlign,
  onPress,
  iconSet
}) => {
  const isCheckIconSet = iconSet ? "MaterialIcons" : 'Ionicons'
  return (
    <TouchableOpacity
      activeOpacity={0.6}
      onPress={onPress}
    >
      <MyText
        text={title}
        addSize={-1}
        style={{
          color: color,
          textAlign: textAlign,
          paddingVertical: 2
        }}>
        {" "}
        <Icon
          iconSet={isCheckIconSet}
          name={name}
          color={color}
          size={14}
        />
      </MyText>
    </TouchableOpacity>
  );
}

const ButtonAction = ({
  onEdit,
  onPrint,
  onAskStatus,
  ISCOLLECTMONEY,
  ISPENDING,
  ISSPRINTPAYBILL,
  item,
  ServiceCategoryID,
  AirtimeServiceGroupID,
  ISALLOWREFUNDSERVICE,
  ISPRINTINSURANCEFILE,
  onRefund,
  ISALLOWCASHOUT,
  onPay,
  ISWAITINGTICKET,
  regainTicket,
  ISVERIFYOTP,
  onPreEnterOTP,
  PROMOTIONSALEORDERID,
  printPromotion
}) => {
  return (
    <View style={{
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginTop: 6,
      width: constants.width - 15,
      paddingRight: 15
    }}>
      {
        ISPENDING == 1 ?
          <IconField
            title={translate("collection.click_check_result")}
            name={"reload-outline"}
            color={COLORS.bgF49B0C}
            textAlign={"right"}
            onPress={onAskStatus}
          />
          :
          null
      }
      {
        ISCOLLECTMONEY == 1 ?
          <IconField
            title={translate("collection.collect_money")}
            name={"arrow-redo-outline"}
            color={COLORS.ic008000}
            textAlign={"left"}
            onPress={onEdit}
          />
          :
          null
      }
      {
        ISSPRINTPAYBILL == 1 ?
          <IconField
            title={translate("collection.print_receipt")}
            name={"print-outline"}
            color={COLORS.icC822B0}
            textAlign={"right"}
            onPress={onPrint}
          />
          :
          null
      }
      {
        ISALLOWREFUNDSERVICE == 1 ?
          <IconField
            title={"Tạo YC HỦY/CHUYỂN"}
            name={"sync-outline"}
            color={COLORS.bg2C8BD7}
            textAlign={"left"}
            onPress={onRefund}
          />
          :
          null
      }
      {
        ISALLOWCASHOUT == 1 ?
          <IconField
            title={"Chi tiền"}
            name={"receipt-sharp"}
            color={COLORS.txtFF4E00}
            textAlign={"left"}
            onPress={onPay}
          />
          :
          null
      }
      {
        ISWAITINGTICKET == 1 ?
          <IconField
            title={"Duyệt ticket"}
            name={"attach-outline"}
            color={COLORS.bg70AA55}
            textAlign={"left"}
            onPress={regainTicket}
          />
          :
          null
      }
      {
        ISVERIFYOTP == 1 ?
          <IconField
            title={"Xác nhận OTP"}
            name={"reload-outline"}
            color={COLORS.txtFF00BF}
            textAlign={"right"}
            onPress={onPreEnterOTP}
          />
          :
          null
      }
      {
        PROMOTIONSALEORDERID != null ?
          <IconField
            title={"In PMH"}
            name={"card-giftcard"}
            color={COLORS.icFF0000}
            textAlign={"right"}
            onPress={printPromotion}
            iconSet={true}
          />
          :
          null
      }
    </View>
  );
}

const ButtonDelete = ({ onDelete, isVisible, totalPaid }) => {
  return (
    <View style={{
      position: "absolute",
      top: 0,
      right: 0,
      alignItems: "flex-end"
    }}>
      {
        isVisible &&
        <TouchableOpacity style={{
          paddingVertical: 8,
          paddingHorizontal: 12,
          flexDirection: "row",
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: COLORS.btnDCE1E1,
          borderBottomStartRadius: 14,
        }}
          activeOpacity={0.6}
          onPress={onDelete}
        >
          <Icon
            iconSet={"Ionicons"}
            name={"trash"}
            color={COLORS.icFF0000}
            size={18}
            style={{ marginRight: 8 }}
          />
          <MyText
            text={totalPaid > 0 ? `Hủy YCX\nvà chi tiền` : `Hủy YCX`}
            addSize={-1}
            style={{
              color: COLORS.txtFF0000,
              textAlign: "center",
              lineHeight: 18,
            }}
          />
        </TouchableOpacity>
      }
    </View>
  );
};

const ButtonCancelTransaction = ({ onCancelTransaction, isVisible }) => {
  return (
    <View style={{
      position: "absolute",
      top: 0,
      right: 0,
      alignItems: "flex-end"
    }}>
      {
        isVisible &&
        <TouchableOpacity style={{
          paddingVertical: 8,
          paddingHorizontal: 12,
          flexDirection: "row",
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: '#FBF4DB',
          borderBottomStartRadius: 14,
        }}
          activeOpacity={0.6}
          onPress={onCancelTransaction}
        >
          <Icon
            iconSet={"MaterialCommunityIcons"}
            name={"delete-circle"}
            color={COLORS.icFF0000}
            size={18}
            style={{ marginRight: 8 }}
          />
          <MyText
            text={"Hủy GD"}
            addSize={-1}
            style={{
              color: COLORS.txtFF0000,
              textAlign: "center",
              lineHeight: 18,
            }}
          />
        </TouchableOpacity>
      }
    </View>
  );
};
