import {
    StyleSheet,
    Text,
    View,
    SafeAreaView,
    Animated,
    TouchableOpacity,
    FlatList,
    Alert
} from 'react-native';
import React, { useEffect, useState } from 'react';
import { COLORS } from '@styles';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { constants } from '@constants';
import { BaseLoading, Icon, MyText, PickerSearch, hideBlockUI, showBlockUI } from '@components';
import SearchInput from '../../CollectInstallmentPayments/component/SearchInput'
import ModalCalendar from '../../CollectInstallmentPayments/component/ModalCalendar';
import moment from 'moment';
import ItemRefund from '../component/ItemRefund';
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux';
import * as actionCollectInstallmentCreator from "../../CollectInstallmentPayments/action";
import { helper } from '@common';
import * as actionCollectionCreator from "../../CollectionTransfer/action";
import * as actionCollectionManagerCreator from "../action";

const MAX_HEIGHT = 117;
const MIN_HEIGHT = 40;
const DISTANCE = MAX_HEIGHT - MIN_HEIGHT;

const RefundHistory = ({
    dataSearchListHistory,
    stateSearchListHistory,
    actionCollectInstallment,
    actionCollection,
    route,
    dataCatalogListCollection,
    dataServiceGroupListCatalog,
    actionCollectionManager
}) => {

    const { SERVICEVOUCHERID } = route?.params ?? "";

    const [filter, setFilter] = useState({
        scrollY: new Animated.Value(0),
        isIncome: 0,
        isCompleteInfo: 0,
        isDelivery: 0
    });
    const [isShowCalendar, setIsShowCalendar] = useState(false);
    const [keyword, setkeyWork] = useState('');
    const [fromDate, setFromDate] = useState(new Date())
    const [toDate, setToDate] = useState(new Date())
    const [dataHistoryRefund, setDataHistoryRefund] = useState()
    const [searchByRefund, setSearchByRefund] = useState('');
    const [providerRefund, setProviderRefund] = useState('');
    const [searchByList, setSearchByList] = useState([]);
    const [updateSearchByList, setUpdateSearchByList] = useState('')
    const [providerList, setProviderList] = useState([]);
    const [updateProviderList, setUpdateProviderList] = useState('');
    const [approvalStatusList, setApprovalStatusList] = useState([]);
    const [updateApprovalStatusList, setupdateApprovalStatusList] = useState('');
    const [firtApprovalStatusLis, setFirtApprovalStatusLis] = useState('');
    const [updateCatalogID, setUpdateCatalogID] = useState('');
    const [updateServiceGroupID, setUpdateServiceGroupID] = useState('');
    const [cencelTranfer, setCancelTranfer] = useState([])

    const diffClamp = Animated.diffClamp(filter.scrollY, 0, DISTANCE);
    const translateY = diffClamp.interpolate({
        inputRange: [0, DISTANCE],
        outputRange: [0, -DISTANCE]
    });


    useEffect(() => {
        //nếu vị trí của data thay đổi backend phải tự chịu lỗi
        const data = {
            catalogID: updateCatalogID,
            serviceGroupID: updateServiceGroupID,
            isLoadSearchBy: true
        };
        actionCollection.getCatalogListCollection();
        actionCollectInstallment.getServiceListRefund(data).then((response) => {
            setCancelTranfer(response?.[0]?.ApprovalStatusList)
        }).catch((err) => console.log(err));
    }, [actionCollectInstallment])

    const getServiceListHistoryRefund = () => {
        const data = {
            catalogID: updateCatalogID,
            serviceGroupID: updateServiceGroupID,
            fromDate: new Date(fromDate),
            toDate: new Date(toDate),
            keyword: keyword,
            searchType: updateSearchByList,
            airtimetrsTypeIDList: updateProviderList,
            approvalStatus: firtApprovalStatusLis
        }
        actionCollectInstallment.getServiceListHistoryRefund(data)
        setDataHistoryRefund(dataSearchListHistory)
    }

    useEffect(() => {
        if (!!SERVICEVOUCHERID) {
            setkeyWork(SERVICEVOUCHERID)
        } else {
            console.log()
            setkeyWork("")
        }
        getServiceListHistoryRefund()
    }, [actionCollectInstallment, fromDate, toDate, searchByRefund, providerRefund, updateProviderList, updateSearchByList, updateApprovalStatusList, updateApprovalStatusList, firtApprovalStatusLis, SERVICEVOUCHERID, updateCatalogID, updateServiceGroupID])

    useEffect(() => {
        setDataHistoryRefund(dataSearchListHistory)
    }, [dataSearchListHistory])

    const onSubmit = (keyword) => {
        const data = {
            catalogID: updateCatalogID,
            serviceGroupID: updateServiceGroupID,
            keyword: keyword,
            fromDate: new Date(fromDate),
            toDate: new Date(toDate),
            searchType: updateSearchByList,
            airtimetrsTypeIDList: updateProviderList,
            approvalStatus: firtApprovalStatusLis
        }
        actionCollectInstallment.getServiceListHistoryRefund(data)
    }
    // 
    const actionSpendCash = (item) => {
        Alert.alert('Thông báo', `Bạn có chắc chắn muốn chi tiền cho giao dịch ${item.OUTPUTRECEIPTID} này?`, [
            {
                text: 'Cancel',
                onPress: () => console.log('Cancel Pressed'),
                style: 'cancel',
            },
            { text: 'OK', onPress: () => handleRefresh(item) },
        ]);
    }

    const actionDeleteRefund = (item) => {
        Alert.alert('Thông báo', `"Bạn có chắc chắn muốn hủy yêu cầu hoàn tiền ${item.OUTPUTRECEIPTID} này?`, [
            {
                text: 'Cancel',
                onPress: () => console.log('Cancel Pressed'),
                style: 'cancel',
            },
            { text: 'OK', onPress: () => handleDeleRefund(item) },
        ]);
    }

    const handleRefresh = (item) => {
        const { AIRTIMETRANSACTIONRFID, AIRTIMETRANSACTIONID } = item
        const data = {
            airtimeTransactionID: AIRTIMETRANSACTIONID,
            airtimeTransactionRFID: AIRTIMETRANSACTIONRFID
        }
        showBlockUI();
        actionCollectInstallment.getStatusHistory(data).then((response) => {
            hideBlockUI();
            Alert.alert('Thông báo', 'Chi tiền thành công', [
                { text: 'OK', onPress: () => getServiceListHistoryRefund() },
            ]);

        }).catch(error => {
            Alert.alert('Thông báo', error.msgError, [
                {
                    text: 'Cancel',
                    onPress: () => console.log('Cancel Pressed'),
                    style: 'cancel',
                },
                {
                    text: 'OK', onPress: () => {
                        hideBlockUI();
                        getServiceListHistoryRefund(item)
                    }
                },
            ]);
        });
    }

    const handleDeleRefund = (item) => {
        const { AIRTIMETRANSACTIONRFID, AIRTIMETRANSACTIONID } = item
        const data = {
            airtimeTransactionID: AIRTIMETRANSACTIONID,
            airtimeTransactionRFID: AIRTIMETRANSACTIONRFID
        }
        showBlockUI();
        actionCollectionManager.validateDeleteRefund(data).then((response) => {
            hideBlockUI();
            Alert.alert('Thông báo', response?.NoteRefund, [
                {
                    text: 'OK', onPress: () => {
                        setkeyWork("")
                        getServiceListHistoryRefund()
                    }
                },
            ]);

        }).catch(error => {
            Alert.alert('Thông báo', error, [
                {
                    text: 'OK', onPress: () => {
                        hideBlockUI();
                    }
                },
            ]);
        });
    }


    const handleNavigatorServiceGroupList = (item) => {
        showBlockUI();
        const { ServiceCatalogID } = item;
        actionCollection.getServiceGroupListCatalog(ServiceCatalogID).then((reponse) => {
            hideBlockUI();
        }).catch((err) => {
            Alert.alert("", err, [
                {
                    text: "OK",
                    onPress: () => {
                        hideBlockUI();
                    },
                },
            ]);
        });
    }

    return (
        <View style={styles.container}>
            <KeyboardAwareScrollView
                style={{
                    flex: 1,
                }}
                enableResetScrollToCoords={false}
                keyboardShouldPersistTaps="always"
                bounces={false}
                overScrollMode="always"
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                extraScrollHeight={60}>
                <SafeAreaView style={styles.safeareaview}>
                    <Animated.View
                        style={{
                            transform: [{ translateY: translateY }],
                            position: 'relative',
                            top: 0,
                            left: 0,
                            right: 0,
                            zIndex: 1,
                            backgroundColor: COLORS.bgF5F5F5,
                        }}>
                        <View style={styles.childrenAnimated}>
                            <TouchableOpacity style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                                backgroundColor: COLORS.bgFFFFFF,
                                borderRadius: 5,
                                width: constants.width - 20,
                                paddingHorizontal: 5,
                                borderWidth: 1,
                                borderColor: COLORS.bdDDDDDD,
                                height: 44,
                                alignSelf: 'center'
                            }}
                                onPress={() => setIsShowCalendar(true)}
                            >
                                <MyText
                                    style={{
                                        width: '87%',
                                        paddingHorizontal: 5
                                    }}
                                    text={`${moment(fromDate).format(
                                        'DD/MM/YYYY'
                                    )
                                        } - ${moment(toDate).format(
                                            'DD/MM/YYYY'
                                        )
                                        } `}
                                />
                                <Icon
                                    iconSet="Feather"
                                    name="calendar"
                                    style={{
                                        fontSize: 30,
                                        color: COLORS.ic2C8BD7
                                    }}
                                />
                            </TouchableOpacity>
                            <PickerSearch
                                label={'Label'}
                                value={'ID'}
                                valueSelected={updateApprovalStatusList}
                                data={cencelTranfer}
                                onChange={(item) => {
                                    setupdateApprovalStatusList(item.ID)
                                    setFirtApprovalStatusLis(item.ApprovalStatus)
                                }}
                                style={styles.pickerSearch}
                                defaultLabel={'Kết quả huỷ/chuyển'}
                            />
                            <View style={{
                                justifyContent: 'center',
                                alignItems: 'center',
                                marginTop: 5,
                                marginBottom: 5
                            }}>
                                <PickerSearch
                                    label={"Description"}
                                    value={"ServiceCatalogID"}
                                    valueSelected={updateCatalogID}
                                    data={helper.IsNonEmptyArray(dataCatalogListCollection) ? dataCatalogListCollection : []}
                                    onChange={(item) => {
                                        setUpdateCatalogID(item.ServiceCatalogID);
                                        handleNavigatorServiceGroupList(item);
                                        setUpdateServiceGroupID('')
                                    }}
                                    style={{
                                        flex: 1,
                                        flexDirection: "row",
                                        justifyContent: "center",
                                        alignItems: "center",
                                        height: 40,
                                        borderRadius: 4,
                                        borderWidth: 1,
                                        borderColor: COLORS.bdE4E4E4,
                                        width: constants.width - 20,
                                        backgroundColor: COLORS.btnFFFFFF,
                                        marginBottom: 5
                                    }}
                                    defaultLabel={"Nhóm dịch vụ"}
                                />
                                {
                                    !!updateCatalogID &&
                                    <PickerSearch
                                        label={"AirtimeServiceGroupName"}
                                        value={"AirtimeServiceGroupID"}
                                        valueSelected={updateServiceGroupID}
                                        data={helper.IsNonEmptyArray(dataServiceGroupListCatalog) ? dataServiceGroupListCatalog : []}
                                        onChange={(item) => {
                                            setUpdateServiceGroupID(item.AirtimeServiceGroupID)
                                        }}
                                        style={{
                                            flex: 1,
                                            flexDirection: "row",
                                            justifyContent: "center",
                                            alignItems: "center",
                                            height: 40,
                                            borderRadius: 4,
                                            borderWidth: 1,
                                            borderColor: COLORS.bdE4E4E4,
                                            width: constants.width - 20,
                                            backgroundColor: COLORS.btnFFFFFF,
                                        }}
                                        defaultLabel={"Chi tiết nhóm dịch vụ"}
                                    />
                                }
                            </View>
                        </View>
                    </Animated.View>
                    <SearchInput
                        onSubmit={() => onSubmit(keyword)}
                        inputText={keyword}
                        onChangeText={(text) => {
                            setkeyWork(text)
                        }}
                        onClearText={() => {
                            setkeyWork('');
                        }}
                        placeholder={'Từ khoá'}
                    />

                    <BaseLoading
                        isLoading={stateSearchListHistory.isFetching}
                        isError={stateSearchListHistory.isError}
                        isEmpty={stateSearchListHistory.isEmpty}
                        textLoadingError={stateSearchListHistory.description}
                        onPressTryAgains={() => {
                            getServiceListHistoryRefund()
                        }}
                        content={
                            <View style={{
                                width: constants.width,
                            }}>
                                <FlatList
                                    style={{ marginTop: 5 }}
                                    data={helper.IsNonEmptyArray(dataHistoryRefund) ? dataHistoryRefund : []}
                                    keyExtractor={(item, index) => `${index} `}
                                    renderItem={({ item, index }) => (
                                        <ItemRefund
                                            updateCatalogID={updateCatalogID}
                                            updateServiceGroupID={updateServiceGroupID}
                                            info={item}
                                            // onRefresh={() => handleRefresh(item)}
                                            onRefresh={() => actionSpendCash(item)}
                                            onDeleteFefund={() => actionDeleteRefund(item)}
                                        />
                                    )
                                    }
                                />
                            </View>
                        }
                    />
                </SafeAreaView>
            </KeyboardAwareScrollView>
            <ModalCalendar
                isVisible={isShowCalendar}
                hideModal={() => {
                    setIsShowCalendar(false);
                }}
                startDate={fromDate}
                endDate={toDate}
                setDate={(day) => {
                    setFromDate(day.startDate)
                    setToDate(day.endDate)
                }}
            />
        </View>
    );
};

const mapStateToProps = function (state) {
    return {
        dataSearchListHistory: state.collectInstallmentReducer.dataSearchListHistory,
        stateSearchListHistory: state.collectInstallmentReducer.stateSearchListHistory,
        dataCatalogListCollection: state.collectionReducer.dataCatalogListCollection,
        dataServiceGroupListCatalog: state.collectionReducer.dataServiceGroupListCatalog,
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionCollectInstallment: bindActionCreators(actionCollectInstallmentCreator, dispatch),
        actionCollection: bindActionCreators(actionCollectionCreator, dispatch),
        actionCollectionManager: bindActionCreators(actionCollectionManagerCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(RefundHistory);

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: COLORS.bgFFFFFF
    },
    safeareaview: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center'
    },
    childrenAnimated: {
        width: constants.width,
        marginTop: 5
    },
    showCalendarButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: COLORS.bgFFFFFF,
        borderRadius: 5,
        width: constants.width - 10,
        paddingHorizontal: 5,
        borderWidth: 1,
        borderColor: COLORS.bdDDDDDD,
        height: 50,
        alignSelf: 'center'
    },
    textCalendar: {
        width: '87%',
        paddingHorizontal: 5
    },
    horizontalPicker: {
        width: constants.width - 10,
        height: 50,
        marginTop: 13,
        alignSelf: 'center',
        flexDirection: 'row'
    },
    childrenPicker: {
        flex: 1,
        paddingRight: 3
    },
    childrenPickerRight: {
        flex: 1,
        paddingLeft: 3
    },
    twoPicker: {
        flex: 1,
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        height: 40,
        borderRadius: 4,
        borderWidth: 1,
        borderColor: COLORS.bdE4E4E4,
        width: constants.width - 20,
        backgroundColor: COLORS.btnFFFFFF,
        marginTop: 5,
        alignSelf: "center",
    },
    pickerSearch: {
        flex: 1,
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        height: 40,
        borderRadius: 4,
        borderWidth: 1,
        borderColor: COLORS.bdE4E4E4,
        width: constants.width - 20,
        backgroundColor: COLORS.btnFFFFFF,
        marginTop: 5,
        alignSelf: "center",
    },
    input: {
        width: constants.width - 10,
        borderRadius: 10,
        height: 50,
        marginTop: 10
    }
});
