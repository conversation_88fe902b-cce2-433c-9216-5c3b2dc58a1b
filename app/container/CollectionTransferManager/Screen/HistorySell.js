import React, { Component } from 'react';
import { connect } from 'react-redux';
import {
    View,
    TouchableOpacity,
    SafeAreaView,
    Animated,
    Alert,
    StyleSheet,
    FlatList
} from 'react-native';
import {
    MyText,
    Icon,
    BaseLoading,
    hideBlock<PERSON>,
    showBlock<PERSON>,
    PickerSearch
} from '@components';
import { COLORS } from '@styles';
import { constants } from '@constants';
import { translate } from '@translate';
import CheckFilter from '../component/CheckFilter';
import SearchInput from '../component/SearchInput';
import ModalFilter from '../component/ModalFilter';
import ItemCollection from '../component/ItemCollection';
import { bindActionCreators } from "redux";
import * as actionCollectionManagerCreator from "../action";
import * as actionSaleOrderCreator from "../../SaleOrderPayment/action";
import * as actionManagerSOCreator from "../../SaleOrderManager/action";
import * as actionCollectionCreator from "../../CollectionTransfer/action";
import * as actionBankAirtimeServiceCreator from "../../BankAirtimeService/action";
import * as actionInsuranceAirtimeServiceCreator from "../../InsuranceAirtimeService/action";
import moment from 'moment';
import { helper } from '@common';
import TransactionFilter from '../component/TransactionFilter';
import ModalCalendar from '../component/Modal/ModalCalendar';
const MAX_HEIGHT = 117;
const MIN_HEIGHT = 40;
const DISTANCE = MAX_HEIGHT - MIN_HEIGHT;
export class HistorySell extends Component {
    constructor(props) {
        super(props)
        this.state = {
            scrollY: new Animated.Value(0),
            isRegistered: 0,
            isVisibleModalFilter: false,
            fromDate: new Date(),
            toDate: new Date(),
            createdUser: '',
            countFilter: 0,
            isDelete: false,
            isCreate: true,
            IsActive: false,
            keyword: '',
            service: 0,
            isShowCalendar: false,
            catalogID: '',
            serviceGroupID: '',
            isFailedNotDeleted: false
        }
        this.isScrolling = false;
    }

    componentDidMount() {
        const { SaleOrderID } = this.props?.route?.params ?? {};
        this.props.actionCollection.getCatalogListCollection();
        this.focusListener = this.props.navigation.addListener('focus', () => {
            if (SaleOrderID != null) {
                this.setState({ keyword: SaleOrderID });
                this.getListDataCollectionManager({
                    keyword: SaleOrderID,
                    fromDate: this.state.fromDate,
                    toDate: this.state.toDate,
                    isCreate: this.state.isCreate,
                    isDelete: this.state.isDelete,
                    catalogID: this.state.catalogID,
                    serviceGroupID: this.state.serviceGroupID,
                    isFailedNotDeleted: this.state.isFailedNotDeleted,
                });
            } else {
                this.getListDataCollectionManager({
                    keyword: this.state.keyword,
                    fromDate: this.state.fromDate,
                    toDate: this.state.toDate,
                    isCreate: this.state.isCreate,
                    isDelete: this.state.isDelete,
                    catalogID: this.state.catalogID,
                    serviceGroupID: this.state.serviceGroupID,
                    isFailedNotDeleted: this.state.isFailedNotDeleted,
                });
            }
        });
    }

    componentWillUnmount() {
        if (this.focusListener) {
            this.focusListener();
        }
    }

    componentDidUpdate(preProps, preState) {
        // const { stateSearchCollection } = this.props;
        // if (preProps.stateSearchCollection.isFetching != stateSearchCollection.isFetching && stateSearchCollection.isFetching) {
        //     this.setState({ scrollY: new Animated.Value(0) });
        // }
    }

    getListDataCollectionManager = ({ keyword,
        fromDate,
        toDate,
        isCreate,
        isDelete,
        catalogID,
        serviceGroupID,
        isFailedNotDeleted

    }) => {
        const data = {
            keyword,
            fromDate,
            toDate,
            isCreate,
            isDelete,
            catalogID,
            serviceGroupID,
            isFailedNotDeleted

        }
        this.props.actionCollectionManager.getSearchCollectionManager(data)
    }

    handleScroll = ({ contentOffset }) => {
        const { y } = contentOffset;
        if (y <= 0) {
            this.setState({ scrollY: new Animated.Value(0) });
        }
    }

    onScrollBegin = () => {
        this.isScrolling = true;
    }

    onScrollEnd = () => {
        this.isScrolling = false;
    }

    onPressDetail = (item) => {
        const {
            actionCollectionManager
        } = this.props;
        hideBlockUI();
        const {
            AIRTIMETRANSACTIONID
        } = item;
        const {
            navigation
        } = this.props;
        showBlockUI();
        actionCollectionManager.getDataAttach(AIRTIMETRANSACTIONID).then((reponseDataAttach) => {
            hideBlockUI();
            navigation.navigate("SawDetail", { item, reponseDataAttach });
        }).catch(msgError => {
            Alert.alert("", msgError, [
                {
                    text: "OK",
                    onPress: hideBlockUI,
                }
            ])
        });


    }

    handleCheckDeleteSOCollection = (info) => {
        const { SERVICEVOUCHERID, SVSTATUSID, AIRTIMESTATUSID } = info ?? '';
        const {
            actionCollectionManager,
        } = this.props;
        showBlockUI();
        const data = {
            "serviceVoucherID": SERVICEVOUCHERID,
            "extraData": {
                "IsBlockManualAction": true,
                "svStatusID": SVSTATUSID,
                "airtimeStatusID": AIRTIMESTATUSID,
            }
        }
        actionCollectionManager.handleCheckDeleteServiceRequest(data).then((reponseDataAttach) => {
            this.handleDeleteSOCollection(info);
        }).catch(msgError => {
            Alert.alert("", msgError, [
                {
                    text: "OK",
                    onPress: () => {
                        hideBlockUI(),
                            this.setState({ keyword: SERVICEVOUCHERID })
                        this.getListDataCollectionManager({
                            keyword: SERVICEVOUCHERID,
                            fromDate: this.state.fromDate,
                            toDate: this.state.toDate,
                            isCreate: this.state.isCreate,
                            isDelete: this.state.isDelete,
                            catalogID: this.state.catalogID,
                            serviceGroupID: this.state.serviceGroupID
                        })
                    }
                }
            ])
        });
    }

    handleDeleteSOCollection = (info) => {
        const { SALEORDERID, ORDERTYPEID, TICKETID, AIRTIMETRANSACTIONID, SERVICEVOUCHERID } = info;
        const {
            actionManagerSO,
            navigation,
            actionSaleOrder,
        } = this.props;
        showBlockUI();
        this.setState({ scrollY: new Animated.Value(0) });
        actionManagerSO.getInfoSODelete(SALEORDERID).then(saleOrder => {
            hideBlockUI();
            actionSaleOrder.setDataSO({
                SaleOrderID: SALEORDERID,
                SaleOrderTypeID: ORDERTYPEID
            }).then(success => {
                const { TotalPaid, IsIncome, OrderTypeID } = saleOrder;
                const isCancel = IsIncome && (TotalPaid > 0);
                if (isCancel) {
                    navigation.navigate("CancelSO", {
                        TICKETID,
                        AIRTIMETRANSACTIONID,
                        SERVICEVOUCHERID
                    });
                    actionSaleOrder.getReportPrinterSocket(OrderTypeID);
                }
                else {
                    navigation.navigate("DeleteSO");
                }

            })
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError, [
                {
                    text: translate('common.btn_skip'),
                    onPress: hideBlockUI,
                },
                {
                    text: translate('common.btn_notify_try_again'),
                    onPress: () => this.handleDeleteSOCollection(info),
                },
            ]);
        })
    }

    handleActiveCollection = (item) => {

    }

    onPrint = (item) => {
        const { SALEORDERID, OUTPUTRECEIPTID } = item;
        const {
            navigation,
            actionSaleOrder,
            actionManagerSO,
        } = this.props;
        const parameterVariable = SALEORDERID || OUTPUTRECEIPTID;
        actionSaleOrder.setDataSO({
            SaleOrderID: parameterVariable,
            SaleOrderTypeID: 100
        }).then(success => {
            actionManagerSO.getContentTypeReport(parameterVariable);
            actionSaleOrder.getReportPrinterSocket(100);
            navigation.navigate("ReprintSaleOrder");
        })
    }

    onAskStatusCollection = (item) => {
        const {
            actionCollectionManager
        } = this.props;
        showBlockUI();
        const {
            AIRTIMESTATUSID,
            AIRTIMETRANSACTIONID,
            AIRTIMETRANSACTIONTYPEID,
            INPUTTIME,
            PROCESSUSER,
            PRODUCTID,
            SALEORDERID,
            STOREID,
            SERVICEVOUCHERID
        } = item
        const data = {
            airTimeStatusID: AIRTIMESTATUSID,
            airtimeTransactionID: AIRTIMETRANSACTIONID,
            airTimeTransactionTypeID: AIRTIMETRANSACTIONTYPEID,
            inputTime: INPUTTIME,
            processUser: PROCESSUSER,
            productID: PRODUCTID,
            saleOrderID: SALEORDERID,
            storeID: STOREID
        }
        actionCollectionManager.queryStatusServiceRequest(data).then((reponse) => {
            this.setState({
                keyword: SALEORDERID,
                fromDate: this.state.fromDate,
                toDate: this.state.toDate,
                isCreate: this.state.isCreate,
                isDelete: this.state.isDelete,
                catalogID: this.state.catalogID,
                serviceGroupID: this.state.serviceGroupID,
                isFailedNotDeleted: this.state.isFailedNotDeleted
            })
            hideBlockUI();
            if (reponse) {
                const isUpdateKeyWork = SALEORDERID || SERVICEVOUCHERID;
                Alert.alert("", reponse?.cus_AirtimeStatusCacheBO?.Description, [
                    {
                        text: "OK",
                        onPress: () => {
                            this.setState({ keyword: isUpdateKeyWork })
                            this.getListDataCollectionManager({
                                keyword: SALEORDERID || SERVICEVOUCHERID,
                                fromDate: this.state.fromDate,
                                toDate: this.state.toDate,
                                isCreate: this.state.isCreate,
                                isDelete: this.state.isDelete,
                                catalogID: this.state.catalogID,
                                serviceGroupID: this.state.serviceGroupID
                            })
                        }
                    }
                ])
            }

        }).catch(msgError => {
            Alert.alert("", msgError, [
                {
                    text: translate('common.btn_skip'),
                    onPress: hideBlockUI
                },
                {
                    text: translate('common.btn_notify_try_again'),
                    onPress: () => this.onAskStatusCollection(item),
                }
            ])
        });
    }

    handleSaleOrderPayment = async (SOInfo) => {
        const { SALEORDERID, catalogID, serviceGroupID, AIRTIMETRANSACTIONTYPENAME } = SOInfo;
        const item = {
            ServiceCategoryID: catalogID,
            AirtimeServiceGroupID: serviceGroupID,
            AirtimeServiceGroupName: AIRTIMETRANSACTIONTYPENAME
        }
        this.props.actionCollection.updateItemCatalog(item);
        try {
            showBlockUI();
            const { actionSaleOrder, navigation } = this.props;
            actionSaleOrder.setDataSO({
                SaleOrderID: SALEORDERID,
                SaleOrderTypeID: 100
            })
            const dataSaleOrder = await actionSaleOrder.getSaleOrderPayment(SALEORDERID);
            hideBlockUI();
            if (dataSaleOrder.cus_WarningMessage) {
                Alert.alert(
                    translate("common.notification"),
                    dataSaleOrder.cus_WarningMessage,
                    [
                        {
                            text: translate("common.customer_decline"),
                            onPress: () => this.handleSkipRandomDiscountPromotion(SOInfo)
                        },
                        {
                            text: translate("common.customer_accept"),
                            onPress: hideBlockUI,
                            style: "cancel"
                        }
                    ]
                );
            } else {
                navigation.navigate("SaleOrderPayment", { shouldOriginUpdate: true });
                actionSaleOrder.getReportPrinterSocket(100);
                actionSaleOrder.getDataQRTransaction(SALEORDERID);
                actionSaleOrder.getDataSCTransaction(SALEORDERID);
            }
        } catch ({ msgError }) {
            Alert.alert(translate('common.notification_uppercase'), msgError, [
                {
                    text: translate('common.btn_skip'),
                    onPress: hideBlockUI
                },
                {
                    text: translate('common.btn_notify_try_again'),
                    onPress: () => this.handleSaleOrderPayment(SOInfo),
                }
            ]);
        }
    }

    renderHeader = () => {
        return (
            <View
                style={{
                    height: 90,
                }}
            />
        );
    };
    handleNavigatorServiceGroupList = (item) => {
        // showBlockUI();
        const { ServiceCatalogID } = item;
        const isLoadSearchBy = true;
        this.props.actionCollection.getServiceGroupListCatalog(ServiceCatalogID, isLoadSearchBy).then((reponse) => {
            hideBlockUI();
        }).catch((err) => {
            Alert.alert("", err, [
                {
                    text: "OK",
                    onPress: () => {
                        hideBlockUI();
                    },
                },
            ]);
        });
    }

    onNavigationRefund = (item) => {
        const {
            navigation
        } = this.props;
        navigation.navigate("CancelService", { data: item })
    }

    handPaymentTransaction = (item) => {
        const { navigation, actionBankAirtimeService } = this.props;
        const {
            SERVICEVOUCHERID,
            CUSTOMERNAME,
            AMOUNT,
            FEE,
            SALEPRICEVAT
        } = item ?? '';
        const data = {
            ServiceVoucherID: SERVICEVOUCHERID,
            Withdrawer: CUSTOMERNAME,
            PriceInfo: {
                Amount: AMOUNT,
                SalePrice: SALEPRICEVAT
            }
        }
        actionBankAirtimeService.updateItemCollectionManagermant(data)
        navigation.navigate("CustomerExpense");
    }

    validateDataTicket = (item) => {
        const { actionBankAirtimeService } = this.props;
        showBlockUI();
        const { AIRTIMETRANSACTIONID, SERVICEVOUCHERID, AIRTIMETRANSACTIONTYPENAME } = item ?? ''
        const customData = {
            "cus_AirtimeTransactionSVMapBO": {
                "AirtimeTransactionID": AIRTIMETRANSACTIONID,
                "ServiceVoucherID": SERVICEVOUCHERID
            }
        }
        const data = {
            "serviceVoucherID": SERVICEVOUCHERID
        }
        actionBankAirtimeService.prepareDataCreateOrder(data).then((reponse) => {
            hideBlockUI();
            actionBankAirtimeService.customDataTicket(customData);
            this.props.navigation.navigate("CreateSaleOrder");
            this.props.actionBankAirtimeService.updateHeaderAirtime({ "AirTimeTransactionTypeName": AIRTIMETRANSACTIONTYPENAME })
        })
            .catch((msgError) => {
                Alert.alert("", msgError?.msgError, [
                    {
                        text: "OK",
                        onPress: () => {
                            hideBlockUI();
                        },
                    },
                ]);
            });
    }

    handlePreEnterNavigatorOTPAirtime = (item) => {
        showBlockUI();
        const isCalled = false;
        const { SALEORDERID, catalogID, serviceGroupID, AIRTIMETRANSACTIONTYPENAME, CUSTOMERPHONE } = item;
        const { navigation, actionCollection, actionSaleOrder, actionBankAirtimeService } = this.props;
        const itemCatalog = {
            ServiceCategoryID: catalogID,
            AirtimeServiceGroupID: serviceGroupID,
            AirtimeServiceGroupName: AIRTIMETRANSACTIONTYPENAME
        }
        actionSaleOrder.setDataSO({
            SaleOrderID: SALEORDERID,
            SaleOrderTypeID: 100
        }).then(success => {
            hideBlockUI();
            actionCollection.updateItemCatalog(itemCatalog);
            actionBankAirtimeService.updateCustomerPhone(CUSTOMERPHONE);
            navigation.navigate("OTPAirtimeService", {
                SaleOrderID: SALEORDERID,
                isCalled
            });
        }).catch((msgError) => {
            Alert.alert("", msgError, [
                {
                    text: "OK",
                    onPress: () => {
                        hideBlockUI();
                    },
                },
            ]);
        });
    }
    printPromotionCollection = (item) => {
        const { ISGIFTPROMOTION, SERVICEVOUCHERID } = item;
        const {
            navigation,
            actionSaleOrder,
            actionManagerSO,
        } = this.props;
        actionSaleOrder.setDataSO({
            SaleOrderID: SERVICEVOUCHERID,
            SaleOrderTypeID: 100
        }).then(success => {
            actionManagerSO.getContentTypeReport(SERVICEVOUCHERID);
            actionSaleOrder.getReportPrinterSocket(100);
            navigation.navigate("ReprintSaleOrder");
        })
    }

    createTransactionCancellationRequest = (item) => {
        const { SERVICEVOUCHERID, SVSTATUSID, AIRTIMESTATUSID } = item;
        Alert.alert("", "Bạn có chắc muốn tạo yêu cầu huỷ giao dịch", [
            {
                text: "BỎ QUA",
                onPress: () => {
                },
            },
            {
                text: "ĐỒNG Ý",
                onPress: () => {
                    this.handleCancelTransaction( SERVICEVOUCHERID, SVSTATUSID, AIRTIMESTATUSID);
                },
            },
        ]);

    }

    handleCancelTransaction = ( SERVICEVOUCHERID, SVSTATUSID, AIRTIMESTATUSID) => {
        showBlockUI();
        const { actionInsuranceAirtimeService } = this.props;
        const data = {
            serviceVoucherID: SERVICEVOUCHERID,
            "extraData": {
                "isCancelOTP": true,
                "svStatusID": SVSTATUSID,
                "airtimeStatusID:": AIRTIMESTATUSID
            }
        };
        actionInsuranceAirtimeService
            .getSendOTPProcessServicePrequest(data)
            .then((reponse) => {
                Alert.alert("", reponse?.object?.Message, [
                    {
                        text: "OK",
                        onPress: () => {
                            hideBlockUI();
                            this.setState({ keyword: SERVICEVOUCHERID })
                            this.getListDataCollectionManager({
                                keyword: SERVICEVOUCHERID,
                                fromDate: new Date(this.state.fromDate),
                                toDate: new Date(this.state.toDate),
                                isCreate: this.state.isCreate,
                                isDelete: this.state.isDelete,
                                catalogID: this.state.catalogID,
                                serviceGroupID: this.state.serviceGroupID,
                                isFailedNotDeleted: this.state.isFailedNotDeleted
                            })
                        },
                    },
                ]);
            })
            .catch((msgError) => {
                Alert.alert("", msgError, [
                    {
                        text: "BỎ QUA",
                        onPress: () => {
                            hideBlockUI();
                        },
                    },
                    {
                        text: "THỬ LẠI",
                        onPress: () => {
                            this.handleCancelTransaction(SERVICEVOUCHERID, SVSTATUSID, AIRTIMESTATUSID);
                        },
                    },
                ]);
            });
    };

    render() {
        const {
            scrollY,
            isVisibleModalFilter,
            isDelete,
            isCreate,
            IsActive,
            keyword,
            isShowCalendar,
            isFailedNotDeleted
        } = this.state;
        const diffClamp = Animated.diffClamp(scrollY, 0, DISTANCE);
        const translateY = diffClamp.interpolate({
            inputRange: [0, DISTANCE],
            outputRange: [0, -DISTANCE],
        });
        const {
            dataCollectionManager,
            stateCollectionManager: {
                isFetching,
                isError,
                isEmpty,
                description
            },
            dataCatalogListCollection,
            dataServiceGroupListCatalog
        } = this.props;

        return (
            <SafeAreaView
                style={{ flex: 1, backgroundColor: COLORS.bgF0F0F0 }}>
                <Animated.View style={{
                    transform: [{ translateY: translateY }],
                    backgroundColor: COLORS.bgF5F5F5,
                    position: 'absolute',
                    top: 0, left: 0, right: 0, zIndex: 2,
                }}>
                    <View style={{
                        width: constants.width
                    }}>
                        <CheckFilter
                            isDelete={isDelete}
                            isCreate={isCreate}
                            onChangeParam={(value1, value2) => {
                                if (!this.isScrolling) {
                                    this.setState({
                                        isCreate: value1, isDelete: value2,
                                        scrollY: new Animated.Value(0)
                                    }, () => this.getListDataCollectionManager({
                                        keyword: this.state.keyword,
                                        fromDate: new Date(this.state.fromDate),
                                        toDate: new Date(this.state.toDate),
                                        isCreate: this.state.isCreate,
                                        isDelete: this.state.isDelete,
                                        catalogID: this.state.catalogID,
                                        serviceGroupID: this.state.serviceGroupID,
                                        isFailedNotDeleted: this.state.isFailedNotDeleted
                                    }));
                                }
                            }}
                            disabled={isFetching}
                        />
                        <TransactionFilter
                            isFailedNotDeleted={isFailedNotDeleted}
                            onChangeParam={(value) => {
                                if (!this.isScrolling) {
                                    const currentDate = new Date();
                                    let fromDate, toDate;

                                    if (value) {
                                        fromDate = new Date(currentDate);
                                        toDate = new Date(currentDate);
                                        fromDate.setDate(fromDate.getDate() - 5);
                                    } else {
                                        fromDate = this.state.fromDate;
                                        toDate = this.state.toDate;
                                    }

                                    this.setState({
                                        isFailedNotDeleted: value,
                                        fromDate: fromDate,
                                        toDate: toDate,
                                        scrollY: new Animated.Value(0)
                                    }, () => this.getListDataCollectionManager({
                                        keyword: this.state.keyword,
                                        fromDate: new Date(this.state.fromDate),
                                        toDate: new Date(this.state.toDate),
                                        isCreate: this.state.isCreate,
                                        isDelete: this.state.isDelete,
                                        catalogID: this.state.catalogID,
                                        serviceGroupID: this.state.serviceGroupID,
                                        isFailedNotDeleted: this.state.isFailedNotDeleted
                                    }));
                                }
                            }}
                            disabled={isFetching}
                        />


                        <View style={{ justifyContent: 'center' }}>
                            <TouchableOpacity style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                                backgroundColor: COLORS.bgFFFFFF,
                                borderRadius: 5,
                                width: '95%',
                                paddingHorizontal: 5,
                                borderWidth: 1,
                                borderColor: COLORS.bdDDDDDD,
                                height: 44,
                                alignSelf: 'center',
                                marginTop: 12
                            }}
                                onPress={() => this.setState({ isShowCalendar: true })}
                            >
                                <MyText
                                    style={{
                                        width: '87%',
                                        paddingHorizontal: 5
                                    }}
                                    text={`${moment(this.state.fromDate).format(
                                        'DD/MM/YYYY'
                                    )
                                        } - ${moment(this.state.toDate).format(
                                            'DD/MM/YYYY'
                                        )
                                        } `}
                                />
                                <Icon
                                    iconSet="Feather"
                                    name="calendar"
                                    style={{
                                        fontSize: 30,
                                        color: COLORS.ic2C8BD7
                                    }}
                                />
                            </TouchableOpacity>
                            <View style={{
                                justifyContent: 'center',
                                alignItems: 'center',
                                marginTop: 5
                            }}>
                                <PickerSearch
                                    label={"Description"}
                                    value={"ServiceCatalogID"}
                                    valueSelected={this.state.catalogID}
                                    data={helper.IsNonEmptyArray(dataCatalogListCollection) ? dataCatalogListCollection : []}
                                    onChange={(item) => {
                                        this.setState({ catalogID: item.ServiceCatalogID });
                                        this.handleNavigatorServiceGroupList(item);
                                        this.setState({ serviceGroupID: '' })
                                        this.getListDataCollectionManager({
                                            keyword: this.state.keyword,
                                            fromDate: new Date(this.state.fromDate),
                                            toDate: new Date(this.state.toDate),
                                            isCreate: this.state.isCreate,
                                            isDelete: this.state.isDelete,
                                            catalogID: item?.ServiceCatalogID,
                                            serviceGroupID: this.state.serviceGroupID,
                                            isFailedNotDeleted: this.state.isFailedNotDeleted
                                        })
                                    }}
                                    style={{
                                        flex: 1,
                                        flexDirection: "row",
                                        justifyContent: "center",
                                        alignItems: "center",
                                        height: 40,
                                        borderRadius: 4,
                                        borderWidth: 1,
                                        borderColor: COLORS.bdE4E4E4,
                                        width: constants.width - 20,
                                        backgroundColor: COLORS.btnFFFFFF,
                                    }}
                                    defaultLabel={"Nhóm dịch vụ"}
                                />
                                {
                                    !!this.state.catalogID &&
                                    <PickerSearch
                                        label={"AirtimeServiceGroupName"}
                                        value={"AirtimeServiceGroupID"}
                                        valueSelected={this.state.serviceGroupID}
                                        data={helper.IsNonEmptyArray(dataServiceGroupListCatalog) ? dataServiceGroupListCatalog : []}
                                        onChange={(item) => {
                                            this.setState({ serviceGroupID: item.AirtimeServiceGroupID })
                                            this.getListDataCollectionManager({
                                                keyword: this.state.keyword,
                                                fromDate: new Date(this.state.fromDate),
                                                toDate: new Date(this.state.toDate),
                                                isCreate: this.state.isCreate,
                                                isDelete: this.state.isDelete,
                                                catalogID: this.state.catalogID,
                                                serviceGroupID: item?.AirtimeServiceGroupID,
                                                isFailedNotDeleted: this.state.isFailedNotDeleted
                                            })
                                        }}
                                        style={{
                                            flex: 1,
                                            flexDirection: "row",
                                            justifyContent: "center",
                                            alignItems: "center",
                                            height: 40,
                                            borderRadius: 4,
                                            borderWidth: 1,
                                            borderColor: COLORS.bdE4E4E4,
                                            width: constants.width - 20,
                                            backgroundColor: COLORS.btnFFFFFF,
                                            marginTop: 5
                                        }}
                                        defaultLabel={"Chi tiết nhóm dịch vụ"}
                                    />
                                }
                            </View>
                            <View style={{
                                justifyContent: 'center',
                                alignItems: 'center',
                                marginTop: 5
                            }}>
                                <SearchInput
                                    onSubmit={() => this.getListDataCollectionManager({
                                        keyword: this.state.keyword,
                                        fromDate: new Date(this.state.fromDate),
                                        toDate: new Date(this.state.toDate),
                                        isCreate: this.state.isCreate,
                                        isDelete: this.state.isDelete,
                                        catalogID: this.state.catalogID,
                                        serviceGroupID: this.state.serviceGroupID,
                                        isFailedNotDeleted: this.state.isFailedNotDeleted
                                    }

                                    )}
                                    inputText={keyword}
                                    onChangeText={(text) => {
                                        this.setState({ keyword: text })
                                    }}
                                    onClearText={() => {
                                        this.setState({ keyword: "" })
                                    }}
                                    style={{
                                        marginTop: 5
                                    }}
                                />
                            </View>
                        </View>
                    </View>
                </Animated.View>
                <BaseLoading
                    isLoading={isFetching}
                    isError={isError}
                    isEmpty={isEmpty}
                    textLoadingError={description}
                    onPressTryAgains={() => this.getListDataCollectionManager({
                        keyword: this.state.keyword,
                        fromDate: new Date(this.state.fromDate),
                        toDate: new Date(this.state.toDate),
                        isCreate: this.state.isCreate,
                        isDelete: this.state.isDelete,
                        catalogID: this.state.catalogID,
                        serviceGroupID: this.state.serviceGroupID,
                        isFailedNotDeleted: this.state.isFailedNotDeleted
                    })}
                    content={
                        <View style={{
                            marginTop: !!this.state.catalogID ? 40 : 35
                        }}>
                            <FlatList
                                style={{ marginTop: !!this.state.catalogID ? 125 : 85, marginBottom: 5 }}
                                data={dataCollectionManager}
                                keyExtractor={(item, index) => `${index}`}
                                renderItem={({ item, index }) => (
                                    <ItemCollection
                                        item={item}
                                        index={index}
                                        onPressDetail={() => this.onPressDetail(item)}
                                        onPrint={() => this.onPrint(item)}
                                        isDelete={isDelete}
                                        onEdit={() => this.handleSaleOrderPayment(item)}
                                        onAskStatus={() => this.onAskStatusCollection(item)}
                                        onActiveCollection={() => this.handleActiveCollection(item)}
                                        IsActive={IsActive}
                                        onDelete={() => this.handleCheckDeleteSOCollection(item)}
                                        onRefund={() => this.onNavigationRefund(item)}
                                        onPay={() => this.handPaymentTransaction(item)}
                                        regainTicket={() => this.validateDataTicket(item)}
                                        onPreEnterOTP={() => this.handlePreEnterNavigatorOTPAirtime(item)}
                                        printPromotion={() => this.printPromotionCollection(item)}
                                        onCancelTransaction={() => this.createTransactionCancellationRequest(item)}
                                    />
                                )
                                }
                                renderSectionHeader={({ section: { title } }) => (<DayTitle title={title} />)}
                                stickySectionHeadersEnabled={false}
                                alwaysBounceVertical={false}
                                bounces={false}
                                scrollEventThrottle={16}
                                onMomentumScrollBegin={this.onScrollBegin}
                                onMomentumScrollEnd={this.onScrollEnd}
                                ListHeaderComponent={this.renderHeader}
                            />
                        </View>
                    }
                />
                <ModalCalendar
                    isVisible={isShowCalendar}
                    hideModal={() => {
                        this.setState({ isShowCalendar: false })
                    }}
                    startDate={this.state.fromDate}
                    endDate={this.state.toDate}
                    isFailedNotDeleted={isFailedNotDeleted}
                    setDate={(day) => {
                        const formattedFromDate = moment(day.startDate).format('YYYY-MM-DD');
                        const formattedToDate = moment(day.endDate).format('YYYY-MM-DD');
                        this.setState({
                            fromDate: formattedFromDate,
                            toDate: formattedToDate,
                        }, () => {
                            this.getListDataCollectionManager({
                                keyword: this.state.keyword,
                                fromDate: new Date(day.startDate),
                                toDate: new Date(day.endDate),
                                isCreate: this.state.isCreate,
                                isDelete: this.state.isDelete,
                                catalogID: this.state.catalogID,
                                serviceGroupID: this.state.serviceGroupID,
                                isFailedNotDeleted: this.state.isFailedNotDeleted
                            });
                        });
                    }}
                />
            </SafeAreaView>
        )
    }
}

const mapStateToProps = function (state) {
    return {
        userInfo: state.userReducer,
        dataCollectionManager: state.collectionManagerReducer.dataCollectionManager,
        stateCollectionManager: state.collectionManagerReducer.stateCollectionManager,
        dataSaleOrder: state.saleOrderPaymentReducer.dataSaleOrder,
        dataSO: state.saleOrderPaymentReducer.dataSO,
        dataCatalogListCollection: state.collectionReducer.dataCatalogListCollection,
        dataServiceGroupListCatalog: state.collectionReducer.dataServiceGroupListCatalog,
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        actionCollectionManager: bindActionCreators(actionCollectionManagerCreator, dispatch),
        actionSaleOrder: bindActionCreators(actionSaleOrderCreator, dispatch),
        actionManagerSO: bindActionCreators(actionManagerSOCreator, dispatch),
        actionCollection: bindActionCreators(actionCollectionCreator, dispatch),
        actionBankAirtimeService: bindActionCreators(actionBankAirtimeServiceCreator, dispatch),
        actionInsuranceAirtimeService: bindActionCreators(actionInsuranceAirtimeServiceCreator, dispatch),
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(HistorySell)

const styles = StyleSheet.create({})

const Filter = ({ showFilter, countFilter }) => {
    return (
        <TouchableOpacity
            style={{
                marginRight: 10,
                width: constants.getSize(40),
                alignItems: 'center',
                justifyContent: 'center',
            }}
            onPress={showFilter}
            activeOpacity={0.7}>
            <MyText
                text={countFilter}
                style={{
                    position: 'absolute',
                    zIndex: 99999,
                    bottom: 3,
                    right: 3,
                    overflow: 'hidden',
                    backgroundColor: COLORS.bgFC8926,
                    borderRadius: 8,
                    paddingVertical: 1,
                    paddingHorizontal: 5,
                    fontSize: 12,
                    color: COLORS.txtFFFFFF,
                    fontWeight: 'bold',
                    alignItems: 'center',
                    justifyContent: 'center'
                }}
            />
            <Icon
                iconSet="FontAwesome"
                name="filter"
                style={{
                    color: COLORS.ic2C8BD7,
                    fontSize: 20,
                }}
            />
        </TouchableOpacity>
    )
}

const DayTitle = ({ title }) => {
    return (
        <View style={{
            backgroundColor: COLORS.bg199092,
            padding: 6,
            justifyContent: "center",
        }}>
            <MyText
                text={title}
                style={{
                    color: COLORS.txtFFFFFF,
                    fontWeight: 'bold'
                }}
            />
        </View>
    );
}
