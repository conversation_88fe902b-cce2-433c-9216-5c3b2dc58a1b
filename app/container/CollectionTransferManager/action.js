import { apiBase, EMPTY, ERROR, METHOD } from '@config';
import { API_CONST } from '@constants';
import { dateHelper, helper } from '@common';
const {
    API_GET_DATA_COLLECTION_MANAGER,
    API_CREATE_TRANSACTION_PARTNER,
    API_GET_DATA_ATTACH,
    API_QUERYTRANSACTION_PARTNER,
    API_QUERY_SERVICE_REQUEST,
    API_DELETE_REFUND,
    API_GET_PROCESS_SERVICE_REQUEST,
    API_GET_CASH_RETURN_DETAIL
} = API_CONST;

const START_GET_DATA_COLLECTION_MANAGER = 'START_API_GET_DATA_COLLECTION_MANAGER';
const STOP_GET_DATA_COLLECTION_MANAGER = 'STOP_GET_DATA_COLLECTION_MANAGER';
const START_GET_QUERY_STATUS = 'START_GET_QUERY_STATUS';
const STOP_GET_QUERY_STATUS = 'STOP_GET_QUERY_STATUS';
const CLEAR_DATA_QUERY_STAUS = 'CLEAR_DATA_QUERY_STAUS';

export const actionCollectionManager = {
    START_GET_DATA_COLLECTION_MANAGER,
    STOP_GET_DATA_COLLECTION_MANAGER,
    START_GET_QUERY_STATUS,
    STOP_GET_QUERY_STATUS,
    CLEAR_DATA_QUERY_STAUS
};

export const getSearchCollectionManager = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                Keyword: data.keyword,
                ProcessUser: data.isCreate ? getState().userReducer.userName : '',
                FromDate: dateHelper.formatDateYYYYMMDD(data.fromDate) + "T00:00:00",
                ToDate: dateHelper.formatDateYYYYMMDD(data.toDate) + "T23:59:59",
                IsDeleted: data.isDelete ? true : false,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                isFailedNotDeleted: data.isFailedNotDeleted ? true : false
            }
            dispatch(start_get_data_collection_manager())
            apiBase(API_GET_DATA_COLLECTION_MANAGER, METHOD.POST, body)
                .then((response) => {
                    if (
                        helper.hasProperty(response, 'object') &&
                        helper.isArray(response.object) &&
                        response.object.length > 0
                    ) {
                        console.log("getListDataCollectionManager success", response);
                        dispatch(stop_get_data_collection_manager(response.object));
                        resolve(response.object);
                    } else {
                        dispatch(stop_get_data_collection_manager([], false, 'Không tìm thấy đơn hàng', true));
                        reject("Lỗi lấy thông tin dữ liệu");
                    }
                })
                .catch((error) => {
                    console.log('getListDataCollectionManager err', error);
                    dispatch(stop_get_data_collection_manager([], !EMPTY, error.msgError, ERROR));
                    reject("Lỗi lấy thông tin dữ liệu");
                });
        })
    }

}

export const createTransactionPartner = function (saleOrderID, airtimeTransactionID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                saleOrderID: saleOrderID,
                airtimeTransactionID: airtimeTransactionID,
                serviceVoucherID: "",
            };
            apiBase(API_CREATE_TRANSACTION_PARTNER, METHOD.POST, body).then((response) => {
                console.log("createTransactionPartner success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                }
                else {
                    resolve("Lỗi lấy thông tin dữ liệu");
                }
            }).catch(error => {
                console.log("createTransactionPartner error", error);
                reject(error.msgError)
            })
        });
    }
}

export const getDataAttach = function (AirtimeTransactionID) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                AirtimeTransactionID: AirtimeTransactionID

            };
            apiBase(API_GET_DATA_ATTACH, METHOD.POST, body).then((response) => {
                console.log("getDataAttach success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                }
                else {
                    resolve("Lỗi lấy thông tin dữ liệu");
                }
            }).catch(error => {
                console.log("getDataAttach error", error);
                reject(error.msgError)
            })
        });
    }
}

export const queryTransactionPartner = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                airTimeStatusID: data.airTimeStatusID,
                airtimeTransactionID: data.airtimeTransactionID,
                airTimeTransactionTypeID: data.airTimeTransactionTypeID,
                inputTime: data.inputTime,
                processUser: data.processUser,
                productID: data.productID,
                saleOrderID: data.saleOrderID,
                storeID: data.storeID,
                isInterval: data.isInterval
            };
            apiBase(API_QUERYTRANSACTION_PARTNER, METHOD.POST, body).then((response) => {
                console.log("queryTransactionPartner success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                }
                else {
                    reject("Lỗi lấy thông tin dữ liệu");
                }
            }).catch(error => {
                console.log("queryTransactionPartner error", error);
                reject(error.msgError)
            })
        });
    }
}

export const queryStatusServiceRequest = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                airTimeStatusID: data.airTimeStatusID,
                airtimeTransactionID: data.airtimeTransactionID,
                airTimeTransactionTypeID: data.airTimeTransactionTypeID,
                inputTime: data.inputTime,
                processUser: data.processUser,
                productID: data.productID,
                saleOrderID: data.saleOrderID,
                storeID: data.storeID,
                isInterval: data.isInterval
            };
            apiBase(API_QUERY_SERVICE_REQUEST, METHOD.POST, body).then((response) => {
                console.log("queryTransactionPartner success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                }
                else {
                    reject("Lỗi lấy thông tin dữ liệu");
                }
            }).catch(error => {
                console.log("queryTransactionPartner error", error);
                reject(error.msgError)
            })
        });
    }
}

export const getQuerysTatusServiceRequest = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "serviceVoucherID": data.serviceVoucherID,
                "saleOrderID": data.saleOrderID
            };
            dispatch(start_get_query_status())
            apiBase(API_QUERY_SERVICE_REQUEST, METHOD.POST, body)
                .then((response) => {
                    console.log("getQuerysTatusServiceRequest success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        dispatch(stop_get_query_status(response.object, true, 'Không lấy được dữ liệu', false));
                        resolve(response.object);
                    }
                })
                .catch((error) => {
                    console.log("getQuerysTatusServiceRequest error", error);
                    dispatch(stop_get_query_status({}, !EMPTY, error.msgError, ERROR));
                    reject(error.msgError);
                });
        });
    };
};

export const validateDeleteRefund = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                airtimeTransactionID: data.airtimeTransactionID,
                airtimeTransactionRFID: data.airtimeTransactionRFID
            };
            apiBase(API_DELETE_REFUND, METHOD.POST, body).then((response) => {
                console.log("validateDeleteRefund success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                }
                else {
                    reject("Lỗi lấy thông tin dữ liệu");
                }
            }).catch(error => {
                console.log("validateDeleteRefund error", error);
                reject(error.msgError)
            })
        });
    }
}

export const handleCheckDeleteServiceRequest = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "serviceVoucherID": data.serviceVoucherID,
                "extraData": data.extraData
            };
            apiBase(API_GET_PROCESS_SERVICE_REQUEST, METHOD.POST, body).then((response) => {
                console.log("handleCheckDeleteServiceRequest success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                }
                else {
                    resolve("Lỗi lấy thông tin dữ liệu");
                }
            }).catch(error => {
                console.log("handleCheckDeleteServiceRequest error", error);
                reject(error.msgError)
            })
        });
    }
}

export const getRefundDetails = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "airtimeTransactionID": data.airtimeTransactionID
            };
            apiBase(API_GET_CASH_RETURN_DETAIL, METHOD.POST, body).then((response) => {
                console.log("getRefundDetails success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                }
                else {
                    reject("Lỗi lấy thông tin dữ liệu");
                }
            })
                .catch((error) => {
                    console.log("getRefundDetails error", error);
                    reject(error)
                });
        });
    };
};


const start_get_data_collection_manager = () => ({
    type: START_GET_DATA_COLLECTION_MANAGER
});
const stop_get_data_collection_manager = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_DATA_COLLECTION_MANAGER,
    data,
    isEmpty,
    description,
    isError
});

const start_get_query_status = () => ({
    type: START_GET_QUERY_STATUS
});
const stop_get_query_status = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_QUERY_STATUS,
    data,
    isEmpty,
    description,
    isError
});

export const clear_data_query_status = () => ({
    type: CLEAR_DATA_QUERY_STAUS
});