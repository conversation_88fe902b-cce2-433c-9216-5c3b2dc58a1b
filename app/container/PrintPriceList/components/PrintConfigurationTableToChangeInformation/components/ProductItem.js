import React from 'react';
import {
    View,
    StyleSheet,
    TouchableOpacity,
    TextInput,
} from 'react-native';
import { MyText, Icon, showPopup } from '@components';
import { COLORS } from '@styles';
import { translate } from '@translate';
import * as contantsBCH from '../../../constants';
const ProductItem = (props) => {
    const { product,
        changedProductList,
        setChangedProductList,
        removeProduct } = props;

    const onChooseProduct = (objProduct) => {
        if (!objProduct.isEnabled) return;
        let selectedProductList = changedProductList.map((item) => {
            return {
                ...item,
                isChecked: item.id == objProduct.id ? !item.isChecked : item.isChecked
            }
        })
        setChangedProductList(selectedProductList);
    }


    const onChangeNumber = (product, value) => {
        if (!product.isEnabled) return;
        let printQuantity = Number(value) < contantsBCH.MAX_NUMBER_PRINT_BY_ONE_PRODUCT
            ? Number(value)
            : contantsBCH.MAX_NUMBER_PRINT_BY_ONE_PRODUCT;
        let productList = changedProductList.map((item) => (
            {
                ...item,
                printQuantity: item.id === product.id ? printQuantity : item.printQuantity
            }))
        setChangedProductList(productList);
    }

    const onShowPopupRemove = (product) => {
        showPopup(translate('common.notification'),
            `Xác nhận xóa sản phẩm ${product.productID} - ${product.productName}?`, [
            {
                text: "Bỏ qua",
                style: "cancel",
                onPress: () => {
                }
            },
            {
                text: "Đồng ý",
                style: "default",
                onPress: () => { removeProduct(product) }
            },
        ]);
    }

    const decreaseQuantity = (product) => {
        if (!product.isEnabled) return;
        let printQuantity = Number(product.printQuantity - 1) < 0
            ? 0
            : Number(product.printQuantity - 1);
        let productList = changedProductList.map((item) => (
            {
                ...item,
                printQuantity: item.id === product.id ? printQuantity : item.printQuantity
            }))
        setChangedProductList(productList);
    };

    const increaseQuantity = (product) => {
        if (!product.isEnabled) return;
        let printQuantity = Number(product.printQuantity + 1) < contantsBCH.MAX_NUMBER_PRINT_BY_ONE_PRODUCT
            ? Number(product.printQuantity + 1)
            : contantsBCH.MAX_NUMBER_PRINT_BY_ONE_PRODUCT;
        let productList = changedProductList.map((item) => (
            {
                ...item,
                printQuantity: item.id === product.id ? printQuantity : item.printQuantity
            }))
        setChangedProductList(productList);
    };

    return (
        <TouchableOpacity
            activeOpacity={.8}
            onPress={() => {
                onChooseProduct(product);
            }}>
            <View style={[styles.container, product.isChecked ? styles.cardContainerActived : null]}>
                <View style={product.isEnabled ? { width: '90%' } : { width: '90%', opacity: 0.5 }}>
                    <View style={styles.infoProductContainer}>
                        <View style={styles.infoProduct}>
                            {
                                product.isChecked ?
                                    <Icon
                                        iconSet={"Ionicons"}
                                        name={"checkmark-circle"}
                                        color={COLORS.bg00A896}
                                        size={30}
                                        style={{ marginTop: 0 }}
                                    />
                                    :
                                    <Icon
                                        iconSet={"Ionicons"}
                                        name={"ellipse-outline"}
                                        color={COLORS.bg00A896}
                                        size={30}
                                        style={{ marginTop: 0 }}
                                    />
                            }
                            <MyText style={styles.productID}>
                                <MyText text={product.productID} style={styles.txtSize} />
                            </MyText>
                        </View>
                        <View style={{ flex: 0.5, flexDirection: 'row', justifyContent: 'flex-end' }}>
                            <View style={styles.numberPrint}>
                                <MyText style={styles.txtSize}>SL: </MyText>
                                <View style={styles.containerQuantity}>
                                    <TouchableOpacity style={styles.buttonQuantity} onPress={() => decreaseQuantity(product)}>
                                        <Icon
                                            iconSet={'Feather'}
                                            name='minus'
                                            color={COLORS.btnFFFFFF}
                                            size={20}
                                        />
                                    </TouchableOpacity>
                                    <TextInput
                                        style={styles.quantityText}
                                        value={product.printQuantity ? product.printQuantity.toString() : '0'}
                                        onChangeText={(value) => { onChangeNumber(product, value) }}
                                        keyboardType="numeric"
                                        returnKeyType={"done"}
                                        textAlign={'center'}
                                        editable={product.isEnabled ? true : false}
                                    />
                                    <TouchableOpacity style={styles.buttonQuantity} onPress={() => increaseQuantity(product)}>
                                        <Icon
                                            iconSet={'Feather'}
                                            name='plus'
                                            color={COLORS.btnFFFFFF}
                                            size={20}
                                        />
                                    </TouchableOpacity>
                                </View>
                            </View>
                            <View style={styles.numberPrinted}>
                                <MyText style={styles.txtSize}> Đã in: </MyText>
                                <MyText style={styles.txtSize}>{product.printedQuantity}</MyText>
                            </View>
                        </View>
                    </View>
                    <View style={[styles.mt_3]}>
                        <MyText>
                            <MyText style={{ fontSize: 16, fontWeight: 'bold', color: COLORS.txt288AD6 }} text={product.productName} />
                        </MyText>
                    </View>
                    {
                        product.seriesID &&
                        <View style={[styles.mt_3]}>
                            <MyText>
                                <MyText style={{ color: 'rgb(248, 152, 29)', fontSize: 12, fontWeight: 'bold', opacity: 1 }}
                                    text={"*Sản phẩm cùng dòng"} />
                            </MyText>
                        </View>
                    }
                    {
                        product.isNoPrice &&
                        <View style={[styles.mt_3]}>
                            <MyText>
                                <MyText style={{ color: 'red', fontSize: 12, fontWeight: 'bold', opacity: 1 }}
                                    text={"*Sản phẩm chưa được làm giá"} />
                            </MyText>
                        </View>
                    }


                </View>
                <View style={{ width: '10%', alignItems: 'flex-end' }}>
                    <TouchableOpacity
                        activeOpacity={0.7}
                        style={styles.circle}
                        onPress={() => onShowPopupRemove(product)}
                    >
                        <Icon
                            iconSet={'Feather'}
                            name='trash-2'
                            color={COLORS.btnFFFFFF}
                            size={20}
                        />
                    </TouchableOpacity>

                </View>

            </View >
        </TouchableOpacity>

    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        margin: 5,
        padding: 5,
        borderColor: "#DDDDDD",
        borderWidth: 2,
        flexDirection: "row",
        justifyContent: 'space-between',
        borderRadius: 10,
        backgroundColor: COLORS.txtFFFFFF,
    },
    cardContainerActived: {
        borderColor: "#97c2bd"
    },
    infoProductContainer: {
        flexDirection: "row",
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        flex: 1
    },
    infoProduct: {
        flex: 0.5,
        flexDirection: "row",
        justifyContent: 'flex-start',
        alignItems: 'center'
    },
    numberPrint:
    {
        flexDirection: "row",
        justifyContent: 'flex-start',
        alignItems: 'center'
    },
    numberPrinted:
    {
        flexDirection: "row",
        justifyContent: 'flex-start',
        alignItems: 'center',
        width: 55,
    },
    quantityText: {
        backgroundColor: 'white',
        fontWeight: 'bold',
        color: 'black',
        width: 22,
        height: 28,
        padding: 0,
        borderColor: 'rgb(0,143,215)',
        borderTopWidth: 2,
        borderBottomWidth: 2
    },
    productID: {
        maxWidth: 110
    },
    mt_3: {
        marginTop: 3
    },
    txtSize: {
        fontSize: 12
    },
    containerQuantity: {
        backgroundColor: 'rgb(0,143,215)',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderRadius: 5,
        paddingHorizontal: 1,
        paddingVertical: 1,
        borderRadius: 50,
        height: 28
    },
    circle: {
        width: 30,
        height: 30,
        borderRadius: 50, // Nửa của width hoặc height để tạo thành vòng tròn
        backgroundColor: 'rgb(250,93,104)',
        justifyContent: 'center',
        alignItems: 'center'
    },
});
export default ProductItem;
