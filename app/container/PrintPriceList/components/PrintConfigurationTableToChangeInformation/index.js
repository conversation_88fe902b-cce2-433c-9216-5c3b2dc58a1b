import React, { useEffect, useState } from 'react';
import {
    View,
    StyleSheet,
    Alert,
    SafeAreaView,
    Keyboard,
    TouchableOpacity,
    FlatList,
    ScrollView
} from 'react-native';
import { bindActionCreators } from 'redux';
import { translate } from '@translate';
import { constants } from '@constants';
import { COLORS } from '@styles';
import { dateHelper } from '@common';
import {
    PickerSearch,
    MyText,
    hideBlockUI,
    showBlockUI,
    Text
} from '@components';
import { connect } from 'react-redux';
import * as printPriceListAction from '../../action';
import ProductItem from './components/ProductItem';
import {
    Header,
    CheckBox,
    MultiPickerSearch,
    helper
} from '../../childrenComponents';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import * as contantsBCH from '../../constants';
import Info from './components/Info';
import Toast from 'react-native-toast-message';

const PrintConfigurationTableToChangeInformation = (props) => {
    const {
        navigation,
        route,
        actionPPL,
        companyID,
        userName,
        stateInventoryStatus,
        statePrintTemplate
    } = props;

    const { mainGroupID, lstSubGroupID, mainGroupName } = route.params;

    const [inventoryStatusListAll, setInventoryStatusListAll] = useState([]);
    const [inventoryStatusList, setInventoryStatusList] = useState([
        contantsBCH.EMPTY_STATUS
    ]);
    const [printTemplateListAll, setPrintTemplateListAll] = useState([]);
    const [printTemplateList, setPrintTemplateList] = useState([
        contantsBCH.EMPTY_PRINT_TEMPLATE
    ]);
    const [changedProductList, setChangedProductList] = useState([]);
    const [dateList, setDateList] = useState([]);
    const [selectedDate, setSelectedDate] = useState('');
    const [selectedInventoryStatus, setSelectedInventoryStatus] = useState({});
    const [selectedPrintTemplate, setSelectedPrintTemplate] = useState({});
    const [subGroupList, setSubGroupList] = useState([]);
    const [positionDisplayList, setPositionDisplayList] = useState([
        contantsBCH.EMPTY_POSITION_DISPLAY
    ]);
    const [selectedSubGroupIDLst, setSelectedSubGroupIDLst] = useState('');
    const [selectedPositionDisplayObj, setSelectedPositionDisplayObj] =
        useState(-1);
    const [filterOption, setFilterOption] = useState(1);
    const {
        LIST_PRINT_TEMPLATE_THERMAL_PRINTER,
        LIST_STORE_ACCEPT_PDF_FUNC_POS
    } = global.config;
    const isSeries = changedProductList.findIndex((item) => item.seriesID);
    const [isCheckedSeries, setIsCheckedSeries] = useState(false);
    useEffect(() => {
        getListSubGroupByMainGroup();
        getListDate();
    }, []);

    const getListDate = () => {
        const date = new Date();
        const { PR_EPRICE_NOTIFYTIME } = global.config;
        const lstDate = [];
        lstDate.push({
            date: dateHelper.formatDateYYYYMMDD(new Date()),
            name: `Ngày hôm nay: ${dateHelper.formatDateDDMMYYYY(new Date())}`
        });
        if (date.getHours() > PR_EPRICE_NOTIFYTIME - 1) {
            lstDate.push({
                date: dateHelper.formatDateYYYYMMDD(dateHelper.getNextDay()),
                name: `Ngày mai: ${dateHelper.formatDateDDMMYYYY(
                    dateHelper.getNextDay()
                )}`
            });
        }
        setDateList(lstDate);
        setSelectedDate(dateHelper.formatDateYYYYMMDD(new Date()));
    };

    const getListSubGroupByMainGroup = () => {
        let params = {
            mainGroupID: mainGroupID,
            lstSubGroupID: lstSubGroupID
        };
        showBlockUI();
        actionPPL
            .getListSubGroupByMainGroup(params)
            .then((res) => {
                if (res && res.length > 0) {
                    setSubGroupList(res);
                }
                hideBlockUI();
            })
            .catch((err) => {
                Alert.alert(translate('common.notification'), err.msgError, [
                    {
                        text: 'Ok',
                        onPress: hideBlockUI
                    }
                ]);
            });
    };
    const getDisplayPosition = (subGroupList) => {
        let params = {
            lstSubGroupID: subGroupList
        };
        actionPPL
            .getDisplayPosition(params)
            .then((res) => {
                if (res) {
                    if (res.length == 0) {
                        onAddChangeProductListIntoGrid([]);
                    }
                    setPositionDisplayList(res);
                    let objDisplay = res.find((item) => item.positionId == 2);
                    if (objDisplay) {
                        onGetProductByPositionDisplayAndSubGroup(
                            objDisplay,
                            subGroupList,
                            []
                        );
                    }
                }
            })
            .catch((err) => {
                Alert.alert(translate('common.notification'), err.msgError, [
                    {
                        text: 'Ok'
                    }
                ]);
            });
    };

    const getProductChange = (objSearch, noPriceProductList = []) => {
        showBlockUI();
        let params = {
            subGroupIdList: objSearch.subGroupIdList,
            positionId: objSearch.positionId
        };
        setIsCheckedSeries(false);
        actionPPL
            .getProductChange(params)
            .then((res) => {
                if (res) {
                    let listProduct = res.map((item) => ({
                        ...item,
                        isSelected: false,
                        isChecked:
                            companyID == contantsBCH.AN_KHANG_COMPANY
                                ? true
                                : false,
                        isEnabled: false,
                        isShow: !item.seriesID,
                        numberInventory: item.printQuantity,
                        mainGroupID: mainGroupID,
                        id: helper.guid()
                    }));
                    onAddChangeProductListIntoGrid(
                        listProduct,
                        noPriceProductList
                    );
                }
                hideBlockUI();
            })
            .catch((err) => {
                Alert.alert(translate('common.notification'), err.msgError, [
                    {
                        text: 'Ok',
                        onPress: hideBlockUI
                    }
                ]);
            });
    };

    const getListInventoryStatus = () => {
        showBlockUI();
        actionPPL
            .getListInventoryStatus()
            .then((res) => {
                if (res) {
                    setInventoryStatusListAll(res);
                }
                hideBlockUI();
            })
            .catch((err) => {
                Alert.alert(translate('common.notification'), err.msgError, [
                    {
                        text: 'Ok',
                        onPress: hideBlockUI
                    }
                ]);
            });
    };
    const getPrintTemplate = () => {
        showBlockUI();
        actionPPL
            .getPrintTemplate()
            .then((res) => {
                if (res) {
                    let tempData = res.map((item) => ({
                        ...item,
                        id: helper.guid(),
                        idAndName: `${item.epriceReportID} - ${item.epriceReportName}`
                    }));
                    setPrintTemplateListAll(tempData);
                }
                hideBlockUI();
            })
            .catch((err) => {
                Alert.alert(translate('common.notification'), err.msgError, [
                    {
                        text: 'Ok',
                        onPress: hideBlockUI
                    }
                ]);
            });
    };

    const getPriceList = (checkedProductList, typePrint) => {
        showBlockUI();
        const params = {
            listEPriceReportProducts: checkedProductList,
            inventoryStatus: selectedInventoryStatus,
            printDate: dateHelper.formatStrDateFULL(selectedDate),
            printType: selectedPrintTemplate.epriceReportID
        };
        actionPPL
            .getPriceList(params)
            .then((res) => {
                if (res) {
                    if (
                        res.noPriceProductList &&
                        res.noPriceProductList.length > 0
                    ) {
                        showWarningNoPriceProductList(res.noPriceProductList);
                    }
                    let tempLstSubGroupID = selectedSubGroupIDLst.split(',');
                    onGetProductByPositionDisplayAndSubGroup(
                        selectedPositionDisplayObj,
                        tempLstSubGroupID,
                        res.noPriceProductList
                    );
                    if (typePrint == 2) {
                        navigation.navigate('PDFViewer', {
                            selectedPrintTemplate: selectedPrintTemplate,
                            base64: res.base64,
                            listBase64: res.listBase64
                        });
                    } else {
                        navigation.navigate('PrinterSetup', {
                            base64: res.base64,
                            listBase64: res.listBase64,
                            objPrintTemplate: selectedPrintTemplate,
                            thermalPrinter:
                                LIST_PRINT_TEMPLATE_THERMAL_PRINTER.includes(
                                    selectedPrintTemplate.epriceReportID
                                )
                        });
                    }
                }
                hideBlockUI();
            })
            .catch((err) => {
                Alert.alert(translate('common.notification'), err.msgError, [
                    {
                        text: 'Ok',
                        onPress: hideBlockUI
                    }
                ]);
            });
    };

    const onShowPopupConfirm = (typePrint) => {
        if (!selectedDate) {
            Alert.alert(
                translate('common.notification'),
                'Vui lòng chọn ngày in',
                [
                    {
                        text: 'Ok'
                    }
                ]
            );
            return;
        }
        let checkedProductList = [];
        //kiểm tra có phải in cùng dòng k
        if (typePrint == 3) {
            checkedProductList = changedProductList.filter(
                (item) => item.isChecked && item.isShow && item.seriesID
            );
        } else if(typePrint == 1){
            checkedProductList = changedProductList.filter(
                (item) => item.isChecked && item.isShow && !item.seriesID
            );
        }else{
            checkedProductList = changedProductList.filter(
                (item) => item.isChecked && item.isShow
            );
        }
        if (checkedProductList.length === 0) {
            Alert.alert(
                translate('common.notification'),
                typePrint == 3
                    ? 'Vui lòng chọn sản phẩm in là sản phẩm cùng dòng'
                    : 'Vui lòng chọn sản phẩm in',
                [
                    {
                        text: 'Ok'
                    }
                ]
            );
            return;
        }
        /*if (checkedProductList.length > contantsBCH.NUMBER_OF_PRODUCT_IS_ONE_PRINTTING) {
            Alert.alert(translate('common.notification'), "Số lượng in không vượt quá 280 sản phẩm.", [
                {
                    text: "Ok",
                },
            ]);
            return;
        };*/
        let errProductList = checkedProductList.filter(
            (item) => item.printQuantity == 0
        );
        if (errProductList.length > 0) {
            Alert.alert(
                translate('common.notification'),
                `Vui lòng nhập số lượng in lớn hơn 0`,
                [
                    {
                        text: 'Ok'
                    }
                ]
            );
            return;
        }
        if (!selectedPrintTemplate?.epriceReportID) {
            Alert.alert(
                translate('common.notification'),
                'Vui lòng chọn mẫu in',
                [
                    {
                        text: 'Ok'
                    }
                ]
            );
            return;
        }
        if (
            !selectedInventoryStatus?.inventoryStatusId ||
            selectedInventoryStatus.inventoryStatusId == -1
        ) {
            Alert.alert(
                translate('common.notification'),
                'Vui lòng trạng thái SP lấy thông tin hiển thị lên BCH',
                [
                    {
                        text: 'Ok'
                    }
                ]
            );
            return;
        }
        getPriceList(checkedProductList, typePrint);
    };

    const onChangePrintTemplate = (item) => {
        let inventoryStatusIDList = item.inventoryStatusIDList
            ? item.inventoryStatusIDList.split('')
            : [];
        let tempInventoryStatusIDList =
            stateInventoryStatus.lstInventoryStatus.filter((data) =>
                inventoryStatusIDList.includes(
                    data.inventoryStatusId.toString()
                )
            );
        let productList = changedProductList.map((data) => {
            return {
                ...data,
                isEnabled: item.lstSubGroupID.includes(data.subGroupID),
                isChecked: item.lstSubGroupID.includes(data.subGroupID)
            };
        });

        if (tempInventoryStatusIDList.length === 0) {
            setInventoryStatusList([contantsBCH.EMPTY_STATUS]);
        } else {
            let obj = tempInventoryStatusIDList.find(
                (item) => item.inventoryStatusId == 1
            );
            setSelectedInventoryStatus(
                obj ? obj : tempInventoryStatusIDList[0]
            );
            setInventoryStatusList(tempInventoryStatusIDList);
        }
        setSelectedPrintTemplate(item);
        setChangedProductList(productList);
    };

    const onAddChangeProductListIntoGrid = (data, noPriceProductList) => {
        let listSubGroupByProduct = data.map((item) => item.subGroupID);
        let tempSelectPrintTemplateList =
            statePrintTemplate.lstPrintTemplate.filter((item) => {
                return item.lstSubGroupID.find((data) =>
                    listSubGroupByProduct.includes(data)
                );
            });
        if (tempSelectPrintTemplateList.length == 0) {
            setPrintTemplateList([contantsBCH.EMPTY_PRINT_TEMPLATE]);
            setInventoryStatusList([contantsBCH.EMPTY_STATUS]);
        } else {
            setPrintTemplateList(tempSelectPrintTemplateList);
        }
        //xử lý hiển thị sản phẩm chưa làm giá khi in.
        if (noPriceProductList && noPriceProductList.length > 0) {
            data = showWarningNoPriceProductList(data, noPriceProductList);
        }
        setChangedProductList(data);
        setFilterOption(true);
        setSelectedPrintTemplate({});
        setSelectedInventoryStatus({});
        Keyboard.dismiss();
    };

    const showWarningNoPriceProductList = (noPriceProductList) => {
        let productList = changedProductList.reduce((result, item) => {
            let objNoPriceProduct = noPriceProductList.find(
                (data) =>
                    data == item.productID && item.isEnabled && item.isChecked
            );
            if (objNoPriceProduct || item.isNoPrice) {
                let obj = {
                    ...item,
                    isChecked: false,
                    isEnabled: false,
                    isNoPrice: true
                };
                result.unshift(obj);
            } else {
                let obj = {
                    ...item,
                    isNoPrice: false
                };
                result.push(obj);
            }
            return result;
        }, []);
        setChangedProductList(productList);
        return productList;
    };

    const removeProduct = (product) => {
        showBlockUI();
        let productList = changedProductList.filter((item) => {
            return item.id != product.id;
        });
        let listSubGroupByProduct = productList.map((item) => item.subGroupID);
        let tempSelectPrintTemplateList = printTemplateList.filter((item) => {
            return (
                item.epriceReportID != -1 &&
                item.lstSubGroupID.find((data) =>
                    listSubGroupByProduct.includes(data)
                )
            );
        });

        let item = tempSelectPrintTemplateList.find((item) =>
            item.lstSubGroupID.includes(product.subGroupID)
        );
        if (
            !item &&
            selectedPrintTemplate.lstSubGroupID &&
            selectedPrintTemplate.lstSubGroupID.includes(product.subGroupID)
        ) {
            setSelectedPrintTemplate({});
            setSelectedInventoryStatus({});
            setInventoryStatusList([contantsBCH.EMPTY_STATUS]);
        }
        setPrintTemplateList(
            tempSelectPrintTemplateList.length > 0
                ? tempSelectPrintTemplateList
                : [contantsBCH.EMPTY_PRINT_TEMPLATE]
        );
        setChangedProductList(productList);
        hideBlockUI();
    };

    const onFilterChangeProductByOption = (value = true) => {
        let tempChangedProductList = [];
        tempChangedProductList = changedProductList.map((item) => {
            return {
                ...item,
                isShow: value
                    ? true
                    : item.printedQuantity < item.numberInventory
                    ? true
                    : false
            };
        });
        tempChangedProductList = changedProductList.map((item) => {
            return {
                ...item,
                isShow: item.seriesID == null
            };
        });
        setChangedProductList(tempChangedProductList);
        setIsCheckedSeries(false);
        setFilterOption(value);
    };

    const onFilterChangeProductBySeries = (value = true) => {
        let tempChangedProductList = [];
        tempChangedProductList = changedProductList.map((item) => {
            return {
                ...item,
                isShow: value ? item.seriesID != null : item.seriesID == null
            };
        });
        setSelectedPrintTemplate({})
        setChangedProductList(tempChangedProductList);
        setIsCheckedSeries(value);
    };

    const onGetPositionDisplayBySubgroup = (listSubgroupSelect) => {
        const listId = listSubgroupSelect.map((x) => x.subGroupID);
        setSelectedSubGroupIDLst(listId.join(','));
        getDisplayPosition(listId);
    };

    const onGetProductByPositionDisplayAndSubGroup = (
        objDisplayList,
        subGroupIdList,
        noPriceProductList = []
    ) => {
        setSelectedPositionDisplayObj(objDisplayList);
        let params = {
            subGroupIdList: subGroupIdList,
            positionId: objDisplayList.positionId
        };
        getProductChange(params, noPriceProductList);
    };

    return (
        <SafeAreaView style={styles.container}>
            <Header
                goBack={() => navigation.goBack()}
                title="In BCH sản phẩm thay đổi thông tin"
            />
            <KeyboardAwareScrollView
                enableResetScrollToCoords={true}
                keyboardShouldPersistTaps="handled"
                bounces={false}
                overScrollMode="always"
                contentContainerStyle={{ flex: 1 }}
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                extraScrollHeight={60}>
                <ScrollView>
                    <View style={styles.filterAreaContainer}>
                        <View style={styles.mt_10}>
                            <MyText
                                style={
                                    styles.title
                                }>{`${mainGroupID} - ${mainGroupName}`}</MyText>
                        </View>
                        <View style={styles.mt_10}>
                            <PickerSearch
                                label={'name'}
                                value={'date'}
                                data={dateList}
                                valueSelected={selectedDate}
                                onChange={(item) => {
                                    setSelectedDate(item.date);
                                }}
                                style={styles.comboBox}
                                defaultLabel={'Chọn ngày in'}
                            />
                        </View>
                        <View style={styles.mt_10}>
                            <MultiPickerSearch
                                value={'subGroupName'}
                                id={'subGroupID'}
                                data={subGroupList}
                                idSelect={selectedSubGroupIDLst}
                                onSelectItem={(listSelect) => {
                                    onGetPositionDisplayBySubgroup(listSelect);
                                }}
                                defaultValue={'Chọn nhóm hàng'}
                                style={styles.comboBox}
                                heightItem={50}
                                heightHeader={50}
                                heightContent="90%"
                            />
                        </View>

                        <View style={styles.mt_10}>
                            <PickerSearch
                                label={'positionName'}
                                value={'positionId'}
                                data={positionDisplayList}
                                valueSelected={
                                    selectedPositionDisplayObj.positionId
                                }
                                onChange={(item) => {
                                    if (item.positionId == -1) return;
                                    let tempLstSubGroupID =
                                        selectedSubGroupIDLst.split(',');
                                    onGetProductByPositionDisplayAndSubGroup(
                                        item,
                                        tempLstSubGroupID,
                                        []
                                    );
                                }}
                                style={styles.comboBox}
                                defaultLabel={'Chọn vị trí trưng bày'}
                            />
                        </View>
                        <View style={styles.filterProductContainer}>
                            <CheckBox
                                title="Sản phẩm chính"
                                size={30}
                                color="#197B96"
                                isCheck={filterOption ? true : false}
                                onCheck={() => {
                                    onFilterChangeProductByOption(true);
                                }}
                                disabled={
                                    changedProductList &&
                                    changedProductList.length == 0
                                        ? true
                                        : false
                                }
                            />
                            <CheckBox
                                title="Chưa in đủ"
                                size={30}
                                color="#197B96"
                                isCheck={!filterOption ? true : false}
                                disabled={
                                    changedProductList &&
                                    changedProductList.length == 0
                                        ? true
                                        : false
                                }
                                onCheck={() => {
                                    onFilterChangeProductByOption(false);
                                }}
                            />
                        </View>
                        {isSeries > -1 && (
                            <View style={styles.filterOnlyPrintSeries}>
                                <CheckBox
                                    title="Sản phẩm cùng dòng"
                                    size={30}
                                    color="rgb(248, 152, 29)"
                                    isCheck={isCheckedSeries}
                                    disabled={
                                        changedProductList &&
                                        changedProductList.length == 0
                                            ? true
                                            : false
                                    }
                                    onCheck={() => {
                                        onFilterChangeProductBySeries(
                                            !isCheckedSeries
                                        );
                                    }}
                                />
                            </View>
                        )}
                        <View style={styles.renderListContainer}>
                            <FlatList
                                nestedScrollEnabled={true}
                                style={styles.list}
                                data={changedProductList.filter(
                                    (item) => item.isShow
                                )}
                                initialNumToRender={30}
                                renderItem={({ item, index }) => (
                                    <ProductItem
                                        product={item}
                                        index={index}
                                        changedProductList={changedProductList}
                                        setChangedProductList={
                                            setChangedProductList
                                        }
                                        printTemplateListAll={
                                            statePrintTemplate.lstPrintTemplate
                                        }
                                        setPrintTemplateList={
                                            setPrintTemplateList
                                        }
                                        navigation={navigation}
                                        removeProduct={removeProduct}
                                    />
                                )}
                                keyExtractor={(item, index) => item.id}
                                removeClippedSubviews={false}
                                keyboardShouldPersistTaps={'always'}
                                showsVerticalScrollIndicator={false}
                            />
                        </View>
                        <View style={styles.mt_10}>
                            <Info
                                sum={
                                    changedProductList.filter(
                                        (item) => item.isShow
                                    ).length
                                }
                                selected={
                                    changedProductList.filter(
                                        (item) => item.isChecked && item.isShow
                                    ).length
                                }
                            />
                        </View>
                        <View style={styles.mt_10}>
                            <PickerSearch
                                label={'idAndName'}
                                value={'epriceReportID'}
                                data={printTemplateList}
                                valueSelected={
                                    selectedPrintTemplate.epriceReportID
                                }
                                onChange={(item) => {
                                    if (item.epriceReportID !== -1)
                                        onChangePrintTemplate(item);
                                }}
                                style={styles.comboBox}
                                defaultLabel={'Chọn mẫu in'}
                            />
                        </View>
                        <View style={styles.mt_10}>
                            <PickerSearch
                                label={'inventoryStatusName'}
                                value={'inventoryStatusId'}
                                data={inventoryStatusList}
                                valueSelected={
                                    selectedInventoryStatus.inventoryStatusId
                                }
                                onChange={(item) => {
                                    if (item.inventoryStatusId !== -1)
                                        setSelectedInventoryStatus(item);
                                }}
                                style={styles.comboBox}
                                defaultLabel={
                                    'Chọn trạng thái SP lấy thông tin hiển thị lên BCH'
                                }
                            />
                        </View>
                        {isCheckedSeries ? (
                            <TouchableOpacity
                                style={styles.btnProductSeries}
                                activeOpacity={0.7}
                                onPress={() => onShowPopupConfirm(3)}>
                                <MyText
                                    style={styles.btnTextVerify}
                                    addSize={2}
                                    text={'In BCH sản phẩm cùng dòng'}
                                />
                            </TouchableOpacity>
                        ) : (
                            <TouchableOpacity
                                style={styles.btnVerifyContainer}
                                activeOpacity={0.7}
                                onPress={() => onShowPopupConfirm(1)}>
                                <MyText
                                    style={styles.btnTextVerify}
                                    addSize={2}
                                    text={'In BCH sản phẩm'}
                                />
                            </TouchableOpacity>
                        )}
                        {(LIST_STORE_ACCEPT_PDF_FUNC_POS.split(',').includes(
                            userName
                        ) ||
                            LIST_STORE_ACCEPT_PDF_FUNC_POS == '-1') && (
                            <TouchableOpacity
                                style={styles.btnPDF}
                                activeOpacity={0.7}
                                onPress={() => onShowPopupConfirm(2)}>
                                <MyText
                                    style={styles.btnTextVerify}
                                    addSize={2}
                                    text={'Xem trước mẫu in'}
                                />
                            </TouchableOpacity>
                        )}
                    </View>
                </ScrollView>
            </KeyboardAwareScrollView>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: COLORS.bgFFFFFF,
        zIndex: 1
    },
    filterAreaContainer: {
        paddingHorizontal: 10,
        flex: 1
    },
    renderListContainer: {
        borderColor: COLORS.bg448E91,
        borderWidth: 2,
        marginTop: 10,
        borderRadius: 15,
        flex: 1,
        minHeight: 150,
        zIndex: -1,
        backgroundColor: COLORS.bgF0F0F0
    },
    filterProductContainer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        alignItems: 'center',
        width: '100%',
        marginVertical: 10
    },
    filterOnlyPrintSeries: {
        flexDirection: 'row',
        justifyContent: 'start',
        alignItems: 'center',
        width: '100%',
        marginVertical: 0
    },
    btnVerifyContainer: {
        backgroundColor: 'rgb(0,143,215)',
        paddingVertical: constants.defaultPaddingVertical,
        justifyContent: 'center',
        alignItems: 'center',
        marginVertical: constants.defaultMarginVertical,
        borderRadius: 10
    },
    btnProductSeries: {
        backgroundColor: 'rgb(248, 152, 29)',
        paddingVertical: constants.defaultPaddingVertical,
        justifyContent: 'center',
        alignItems: 'center',
        marginVertical: constants.defaultMarginVertical,
        borderRadius: 10
    },
    btnTextVerify: {
        color: COLORS.txtFFFFFF,
        fontWeight: 'bold'
    },
    comboBox: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        height: 40,
        borderRadius: 10,
        borderWidth: 1,
        borderColor: COLORS.bdE4E4E4,
        width: constants.width - 30,
        backgroundColor: COLORS.btnFFFFFF,
        marginHorizontal: 5
    },
    mt_10: {
        marginTop: 10
    },
    title: {
        fontSize: 20,
        color: COLORS.txtE69A51,
        fontWeight: 'bold'
    },
    list: {
        width: '100%',
        height: 290
    },
    btnPDF: {
        marginVertical: constants.defaultMarginVertical,
        borderRadius: 10,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: COLORS.bg6A976C,
        height: 40
    }
});

const mapStateToProps = function (state) {
    return {
        companyID: state.userReducer.companyID,
        userName: state.userReducer.userName,
        stateInventoryStatus: state.printPriceListReducer.stateInventoryStatus,
        statePrintTemplate: state.printPriceListReducer.statePrintTemplate
    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        actionPPL: bindActionCreators(printPriceListAction, dispatch)
    };
};

export default connect(
    mapStateToProps,
    mapDispatchToProps
)(PrintConfigurationTableToChangeInformation);
