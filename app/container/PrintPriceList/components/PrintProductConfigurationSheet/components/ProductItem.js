import React from 'react';
import { View, StyleSheet, TouchableOpacity, TextInput } from 'react-native';
import { MyText, Icon, showPopup } from '@components';
import { COLORS } from '@styles';
import { translate } from '@translate';
import * as contantsBCH from '../../../constants';
const ProductItem = (props) => {
    const {
        product,
        selectedProductList,
        setSelectedProductList,
        removeProduct
    } = props;

    const onChooseProduct = (objProduct) => {
        if (!objProduct.isEnabled) return;
        let productList = selectedProductList.map((item) => {
            return {
                ...item,
                isChecked:
                    item.id == objProduct.id ? !item.isChecked : item.isChecked
            };
        });
        setSelectedProductList(productList);
    };

    const onChangeNumber = (product, value) => {
        if (!product.isEnabled) return;
        let printQuantity =
            Number(value) < contantsBCH.MAX_NUMBER_PRINT_BY_ONE_PRODUCT
                ? Number(value)
                : contantsBCH.MAX_NUMBER_PRINT_BY_ONE_PRODUCT;
        let productList = selectedProductList.map((item) => ({
            ...item,
            printQuantity:
                item.id === product.id ? printQuantity : item.printQuantity
        }));
        setSelectedProductList(productList);
    };

    const onShowPopupRemove = (product) => {
        showPopup(
            translate('common.notification'),
            `Xác nhận xóa sản phẩm ${product.productID} - ${product.productName}?`,
            [
                {
                    text: 'Bỏ qua',
                    style: 'cancel',
                    onPress: () => {}
                },
                {
                    text: 'Đồng ý',
                    style: 'default',
                    onPress: () => {
                        removeProduct(product);
                    }
                }
            ]
        );
    };

    const decreaseQuantity = (product) => {
        if (!product.isEnabled) return;
        let printQuantity =
            Number(product.printQuantity - 1) < 0
                ? 0
                : Number(product.printQuantity - 1);
        let productList = selectedProductList.map((item) => ({
            ...item,
            printQuantity:
                item.id === product.id ? printQuantity : item.printQuantity
        }));
        setSelectedProductList(productList);
    };

    const increaseQuantity = (product) => {
        if (!product.isEnabled) return;
        let printQuantity =
            Number(product.printQuantity + 1) <
            contantsBCH.MAX_NUMBER_PRINT_BY_ONE_PRODUCT
                ? Number(product.printQuantity + 1)
                : contantsBCH.MAX_NUMBER_PRINT_BY_ONE_PRODUCT;
        let productList = selectedProductList.map((item) => ({
            ...item,
            printQuantity:
                item.id === product.id ? printQuantity : item.printQuantity
        }));
        setSelectedProductList(productList);
    };

    return (
        <TouchableOpacity
            activeOpacity={0.8}
            onPress={() => {
                onChooseProduct(product);
            }}>
            <View
                style={[
                    styles.container,
                    product.isChecked ? styles.cardContainerActived : null
                ]}>
                <View
                    style={
                        product.isEnabled
                            ? { width: '90%' }
                            : { width: '90%', opacity: 0.5 }
                    }>
                    <View style={styles.infoProductContainer}>
                        <View style={styles.infoProduct}>
                            <View style={styles.toolLeft}>
                                {product.isChecked ? (
                                    <Icon
                                        iconSet={'Ionicons'}
                                        name={'checkmark-circle'}
                                        color={COLORS.bg00A896}
                                        size={30}
                                        style={{ marginTop: 0 }}
                                    />
                                ) : (
                                    <Icon
                                        iconSet={'Ionicons'}
                                        name={'ellipse-outline'}
                                        color={COLORS.bg00A896}
                                        size={30}
                                        style={{ marginTop: 0 }}
                                    />
                                )}
                                <MyText
                                    style={styles.ml_3}
                                    text={product.productID}
                                />
                            </View>
                            <View style={styles.toolRight}>
                                <MyText>SL in: </MyText>
                                <View style={styles.containerQuantity}>
                                    <TouchableOpacity
                                        style={styles.buttonQuantity}
                                        onPress={() =>
                                            decreaseQuantity(product)
                                        }>
                                        <Icon
                                            iconSet={'Feather'}
                                            name="minus"
                                            color={COLORS.btnFFFFFF}
                                            size={20}
                                        />
                                    </TouchableOpacity>
                                    <TextInput
                                        style={styles.quantityText}
                                        value={product.printQuantity.toString()}
                                        onChangeText={(value) => {
                                            onChangeNumber(product, value);
                                        }}
                                        keyboardType="numeric"
                                        returnKeyType={'done'}
                                        textAlign={'center'}
                                        editable={
                                            product.isEnabled ? true : false
                                        }
                                    />
                                    <TouchableOpacity
                                        style={styles.buttonQuantity}
                                        onPress={() =>
                                            increaseQuantity(product)
                                        }>
                                        <Icon
                                            iconSet={'Feather'}
                                            name="plus"
                                            color={COLORS.btnFFFFFF}
                                            size={20}
                                        />
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </View>
                    <View style={styles.mt_3}>
                        <MyText
                            style={{
                                fontSize: 16,
                                fontWeight: 'bold',
                                color: COLORS.txt288AD6
                            }}
                            text={product.productName}
                        />
                    </View>
                    {product.seriesID && (
                        <View style={[styles.mt_3]}>
                            <MyText>
                                <MyText
                                    style={{
                                        color: 'rgb(248, 152, 29)',
                                        fontSize: 12,
                                        fontWeight: 'bold',
                                        opacity: 1
                                    }}
                                    text={'*Sản phẩm cùng dòng'}
                                />
                            </MyText>
                        </View>
                    )}
                    {product.isNoPrice && (
                        <View style={[styles.mt_3]}>
                            <MyText>
                                <MyText
                                    style={{
                                        color: 'red',
                                        fontSize: 12,
                                        fontWeight: 'bold',
                                        opacity: 1
                                    }}
                                    text={'*Sản phẩm chưa được làm giá'}
                                />
                            </MyText>
                        </View>
                    )}
                </View>
                <View>
                    <TouchableOpacity
                        activeOpacity={0.7}
                        style={styles.circle}
                        onPress={() => onShowPopupRemove(product)}>
                        <Icon
                            iconSet={'Feather'}
                            name="trash-2"
                            color={COLORS.btnFFFFFF}
                            size={20}
                        />
                    </TouchableOpacity>
                </View>
            </View>
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    containerQuantity: {
        backgroundColor: 'rgb(0,143,215)',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderRadius: 5,
        paddingHorizontal: 1,
        paddingVertical: 1,
        borderRadius: 50,
        height: 30,
        marginRight: 10
    },
    buttonQuantity: {
        padding: 3
    },
    quantityText: {
        backgroundColor: 'white',
        fontWeight: 'bold',
        color: 'black',
        width: 22,
        height: 30,
        padding: 0,
        borderColor: 'rgb(0,143,215)',
        borderTopWidth: 2,
        borderBottomWidth: 2
    },
    container: {
        flex: 1,
        margin: 5,
        padding: 6,
        flexDirection: 'row',
        justifyContent: 'space-between',
        borderRadius: 10,
        backgroundColor: COLORS.txtFFFFFF,
        borderWidth: 2,
        borderColor: '#DDDDDD'
    },
    cardContainerActived: {
        borderColor: '#97c2bd'
    },
    infoProductContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: 2
    },
    infoProduct: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '110%'
    },
    numberPrint: {
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'center'
    },
    input: {
        width: 45,
        height: 30,
        borderWidth: 1,
        padding: 0,
        borderColor: COLORS.bg448E91,
        borderRadius: 5,
        color: COLORS.txt000000,
        fontSize: 16
    },
    mt_3: {
        marginTop: 3
    },
    ml_3: {
        marginLeft: 1,
        fontSize: 15
    },
    circle: {
        width: 32,
        height: 32,
        borderRadius: 50, // Nửa của width hoặc height để tạo thành vòng tròn
        backgroundColor: 'rgb(250,93,104)',
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 2
    },
    toolRight: {
        flex: 0.5,
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'center'
    },
    toolLeft: {
        width: '100%',
        flex: 0.5,
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'flex-start'
    }
});
export default ProductItem;
