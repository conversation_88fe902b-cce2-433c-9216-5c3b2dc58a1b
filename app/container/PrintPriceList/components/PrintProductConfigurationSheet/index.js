import React, { useEffect, useState, useRef, forwardRef } from 'react';
import {
    View,
    StyleSheet,
    Alert,
    SafeAreaView,
    FlatList,
    Keyboard,
    TouchableOpacity,
    TouchableWithoutFeedback,
    ScrollView,
    BackHandler,
    TextInput
} from 'react-native';
import { bindActionCreators } from 'redux';
import { translate } from '@translate';
import { constants } from '@constants';
import * as contantsBCH from '../../constants';
import { COLORS } from '@styles';
import { dateHelper } from '@common';
import {
    BarcodeCamera,
    PickerSearch,
    MyText,
    hideBlockUI,
    showBlockUI,
    ScanBarcodeComponent,
    Icon
} from '@components';
import { connect } from 'react-redux';
import * as printPriceListAction from '../../action';
import SearchSuggest from './components/SearchSuggest';
import ProductItem from './components/ProductItem';
import { Header, PickerSearchCustom, helper } from '../../childrenComponents';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
const PrintProductConfigurationSheet = (props) => {
    const {
        userName,
        navigation,
        route,
        actionPPL,
        companyID,
        lstUnprintedProducts,
        stateInventoryStatus,
        statePrintTemplate
    } = props;
    const [valueSearch, setValueSearch] = useState('');
    const [isShowCamera, setIsShowCamera] = useState(false);
    const [inventoryStatusListAll, setInventoryStatusListAll] = useState([]);
    const [inventoryStatusList, setInventoryStatusList] = useState([
        contantsBCH.EMPTY_STATUS
    ]);
    const [printTemplateListAll, setPrintTemplateListAll] = useState([]);
    const [printTemplateList, setPrintTemplateList] = useState([
        contantsBCH.EMPTY_PRINT_TEMPLATE
    ]);
    const [selectedProductList, setSelectedProductList] = useState([]);
    const [searchedProduct, setSearchedProduct] = useState([]);
    const [dateList, setDateList] = useState([]);
    const [selectedDate, setSelectedDate] = useState('');
    const [selectedInventoryStatus, setSelectedInventoryStatus] = useState({});
    const [selectedPrintTemplate, setSelectedPrintTemplate] = useState({});
    const [showSuggest, setShowSuggest] = useState(false);
    const [showResultSuggest, setShowResultSuggest] = useState(false);
    const [keyboardStatus, setKeyboardStatus] = useState(true);
    const [skipProductPresentation, setSkipProductPresentation] = useState(0);
    const _keyboardDidShow = () => setKeyboardStatus(false);
    const _keyboardDidHide = () => setKeyboardStatus(true);
    const {
        LIST_PRINT_TEMPLATE_THERMAL_PRINTER,
        LIST_STORE_ACCEPT_PDF_FUNC_POS
    } = global.config;
    const cameraRef = useRef(null);
    const scanBCRef = useRef(null);
    const handleCameraRef = (ref) => {
        cameraRef.current = ref;
    };
    const refTem = useRef();
    refTem.current = selectedProductList;
    useEffect(() => {
        return () => {
            onGoBack();
        };
    }, []);
    useEffect(() => {
        Keyboard.addListener('keyboardDidShow', _keyboardDidShow);
        Keyboard.addListener('keyboardDidHide', _keyboardDidHide);

        // cleanup function
        return () => {
            Keyboard.removeListener('keyboardDidShow', _keyboardDidShow);
            Keyboard.removeListener('keyboardDidHide', _keyboardDidHide);
        };
    }, []);
    useEffect(() => {
        getListDate();
        if (lstUnprintedProducts.length > 0) {
            Alert.alert(
                'Thông báo',
                'Bạn có muốn giữ lại danh sách sản phẩm chưa hoàn tất in trước đó không?',
                [
                    {
                        text: 'Ok',
                        onPress: () => {
                            onChooseListProduct(lstUnprintedProducts);
                        }
                    },
                    {
                        text: 'Cancel'
                    }
                ]
            );
        }
    }, []);
    useEffect(() => {
        const backAction = () => {
            return true;
        };
        const backHandler = BackHandler.addEventListener(
            'hardwareBackPress',
            backAction
        );
        return () => backHandler.remove();
    }, []);
    const getListDate = () => {
        setDateList([
            {
                date: dateHelper.formatDateYYYYMMDD(new Date()),
                name: `Ngày hôm nay: ${dateHelper.formatDateDDMMYYYY(
                    new Date()
                )}`
            },
            {
                date: dateHelper.formatDateYYYYMMDD(dateHelper.getNextDay()),
                name: `Ngày mai: ${dateHelper.formatDateDDMMYYYY(
                    dateHelper.getNextDay()
                )}`
            }
        ]);
        setSelectedDate(dateHelper.formatDateYYYYMMDD(new Date()));
    };
    const getProductByKeyword = (valueSearch) => {
        let params = {
            keyword: valueSearch,
            skipProductPresentation: skipProductPresentation
        };
        showBlockUI();
        actionPPL
            .getProduct(params)
            .then((res) => {
                if (res) {
                    let listProduct = res.map((item) => ({
                        ...item,
                        isSelected: false,
                        isChecked:
                            companyID == contantsBCH.AN_KHANG_COMPANY
                                ? true
                                : false,
                        isEnabled: false
                    }));
                    if (listProduct && listProduct.length > 0) {
                        if (
                            companyID == contantsBCH.AN_KHANG_COMPANY &&
                            listProduct[0]?.isPackage
                        ) {
                            onChooseListProduct(listProduct);
                        } else if (listProduct[0]?.isBarcode) {
                            onChooseListProduct(listProduct);
                        } else if (listProduct[0]?.seriesID) {
                            onChooseListProduct(listProduct);
                        } else {
                            if (
                                listProduct.length == 1 &&
                                valueSearch.length == 13
                            ) {
                                onChooseListProduct(listProduct);
                            } else {
                                setShowResultSuggest(true);
                                setSearchedProduct(listProduct);
                                setShowSuggest(true);
                            }
                        }
                    } else if (listProduct.length == 0 && isShowCamera) {
                        cameraRef.current.resumePreview();
                        scanBCRef.current.status.isDetected = false;
                    }
                    hideBlockUI();

                    // if (listProduct && listProduct.length > 0 && listProduct[0].isPackage) {
                    //     onChooseListProduct(listProduct);
                    //     setShowSuggest(false);
                    // } else {
                    //     if (listProduct.length == 1 && valueSearch.length == 13) {
                    //         onChooseListProduct(listProduct);
                    //         return;
                    //     }
                    //     else if (listProduct.length == 0 && isShowCamera) {
                    //         cameraRef.current.resumePreview();
                    //         scanBCRef.current.status.isDetected = false;
                    //     }
                    //     setShowResultSuggest(true);
                    //     setSearchedProduct(listProduct);
                    //     setShowSuggest(true);
                    // }
                }
            })
            .catch((err) => {
                Alert.alert(translate('common.notification'), err.msgError, [
                    {
                        text: 'Ok',
                        onPress: () => {
                            setSearchedProduct([]);
                            setShowSuggest(false);
                            setShowResultSuggest(false);
                        }
                    }
                ]);
                hideBlockUI();
            });
    };
    const getProductByBarcode = (valueSearch) => {
        showBlockUI();
        let params = {
            keyword: valueSearch,
            skipProductPresentation: skipProductPresentation
        };
        actionPPL
            .getProduct(params)
            .then((res) => {
                if (res && res.length > 0) {
                    let productList = res.map((item) => {
                        return {
                            ...item,
                            isSelected: false,
                            isChecked:
                                companyID == contantsBCH.AN_KHANG_COMPANY
                                    ? true
                                    : false,
                            isEnabled: false
                        };
                    });

                    onChooseListProduct(productList);
                    setShowSuggest(false);
                } else {
                    Alert.alert(
                        translate('common.notification'),
                        'Không tìm thấy sản phẩm ',
                        [
                            {
                                text: 'Ok',
                                onPress: () => {
                                    setShowSuggest(false);
                                }
                            }
                        ]
                    );
                }
                hideBlockUI();
            })
            .catch((err) => {
                Alert.alert(translate('common.notification'), err.msgError, [
                    {
                        text: 'Ok',
                        onPress: () => {
                            setSearchedProduct([]);
                            setShowSuggest(false);
                            hideBlockUI();
                        }
                    }
                ]);
            });
    };

    const getPriceList = (checkedProductList, typePrint) => {
        showBlockUI();
        const params = {
            listEPriceReportProducts: checkedProductList,
            inventoryStatus: selectedInventoryStatus,
            printDate: dateHelper.formatStrDateFULL(selectedDate),
            printType: selectedPrintTemplate.epriceReportID
        };
        actionPPL
            .getPriceList(params)
            .then((res) => {
                if (res) {
                    if (
                        res.noPriceProductList &&
                        res.noPriceProductList.length > 0
                    ) {
                        showWarningNoPriceProductList(res.noPriceProductList);
                    }
                    if (
                        res.base64 ||
                        (res.listBase64 && res.listBase64.length != 0)
                    ) {
                        if (typePrint == 1) {
                            navigation.navigate('PrinterSetup', {
                                base64: res.base64,
                                listBase64: res.listBase64,
                                thermalPrinter:
                                    LIST_PRINT_TEMPLATE_THERMAL_PRINTER.includes(
                                        selectedPrintTemplate.epriceReportID
                                    ),
                                objPrintTemplate: selectedPrintTemplate
                            });
                        } else {
                            navigation.navigate('PDFViewer', {
                                selectedPrintTemplate: selectedPrintTemplate,
                                base64: res.base64,
                                listBase64: res.listBase64
                            });
                        }
                    }
                }
                hideBlockUI();
            })
            .catch((err) => {
                Alert.alert(translate('common.notification'), err.msgError, [
                    {
                        text: 'Ok',
                        onPress: hideBlockUI
                    }
                ]);
            });
    };

    const showWarningNoPriceProductList = (noPriceProductList) => {
        let productList = selectedProductList.reduce((result, item) => {
            let objNoPriceProduct = noPriceProductList.find(
                (data) =>
                    data == item.productID && item.isEnabled && item.isChecked
            );
            if (objNoPriceProduct || item.isNoPrice) {
                let obj = {
                    ...item,
                    isChecked: false,
                    isEnabled: false,
                    isNoPrice: true
                };
                result.unshift(obj);
            } else {
                let obj = {
                    ...item,
                    isNoPrice: false
                };
                result.push(obj);
            }
            return result;
        }, []);
        setSelectedProductList(productList);
    };

    const onChooseListProduct = (data) => {
        let chooseProductLst = data.map((item) => {
            return {
                ...item,
                id: helper.guid(),
                isEnabled:
                    selectedPrintTemplate?.lstSubGroupID &&
                    selectedPrintTemplate.lstSubGroupID.includes(
                        item.subGroupID
                    )
                        ? true
                        : item.isEnabled,
                isChecked:
                    selectedPrintTemplate?.lstSubGroupID &&
                    selectedPrintTemplate.lstSubGroupID.includes(
                        item.subGroupID
                    )
                        ? true
                        : false,
                isPrinted: false
            };
        });
        let productList = [...selectedProductList, ...chooseProductLst];
        let listSubGroupByProduct = productList.map((item) => item.subGroupID);
        let tempSelectPrintTemplateList =
            statePrintTemplate.lstPrintTemplate.filter((item) => {
                return item.lstSubGroupID.find((data) =>
                    listSubGroupByProduct.includes(data)
                );
            });
        setSelectedProductList(productList);
        setPrintTemplateList(
            tempSelectPrintTemplateList.length > 0
                ? tempSelectPrintTemplateList
                : [contantsBCH.EMPTY_PRINT_TEMPLATE]
        );
        Keyboard.dismiss();
        setShowSuggest(false);
        setValueSearch('');
        setSearchedProduct([]);
        if (isShowCamera) {
            cameraRef.current.resumePreview();
            setTimeout(() => {
                scanBCRef.current.status.isDetected = false;
            }, 700);
        }
    };

    const removeProduct = (product) => {
        let productList = selectedProductList.filter((item) => {
            return item.id != product.id;
        });
        let listSubGroupByProduct = productList.map((item) => item.subGroupID);
        let tempSelectPrintTemplateList = printTemplateList.filter((item) => {
            return (
                item.epriceReportID != -1 &&
                item.lstSubGroupID.find((data) =>
                    listSubGroupByProduct.includes(data)
                )
            );
        });
        let removedObjHasSelectedPrintTemplate =
            tempSelectPrintTemplateList.find((item) =>
                item.lstSubGroupID.includes(product.subGroupID)
            );
        if (
            !removedObjHasSelectedPrintTemplate &&
            selectedPrintTemplate?.lstSubGroupID &&
            selectedPrintTemplate.lstSubGroupID.includes(product.subGroupID)
        ) {
            setSelectedPrintTemplate({});
            setSelectedInventoryStatus({});
            setInventoryStatusList([contantsBCH.EMPTY_STATUS]);
        }
        setPrintTemplateList(
            tempSelectPrintTemplateList.length > 0
                ? tempSelectPrintTemplateList
                : [contantsBCH.EMPTY_PRINT_TEMPLATE]
        );
        setSelectedProductList(productList);
    };

    const onChangePrintTemplate = (item) => {
        showBlockUI();
        let inventoryStatusIDList = item.inventoryStatusIDList
            ? item.inventoryStatusIDList.split('')
            : [];
        let tempInventoryStatusIDList =
            stateInventoryStatus.lstInventoryStatus.filter((data) =>
                inventoryStatusIDList.includes(
                    data.inventoryStatusId.toString()
                )
            );
        let productList = selectedProductList.map((data) => {
            return {
                ...data,
                isEnabled: item.lstSubGroupID.includes(data.subGroupID),
                isChecked: item.lstSubGroupID.includes(data.subGroupID)
            };
        });
        if (tempInventoryStatusIDList.length === 0) {
            setInventoryStatusList([contantsBCH.EMPTY_STATUS]);
        } else {
            //ưu tiên trạng thái mới
            let obj = tempInventoryStatusIDList.find(
                (item) => item.inventoryStatusId == 1
            );
            setSelectedInventoryStatus(
                obj ? obj : tempInventoryStatusIDList[0]
            );
            setInventoryStatusList(tempInventoryStatusIDList);
        }
        setSelectedPrintTemplate(item);
        setSelectedProductList(productList);
        hideBlockUI();
    };

    const onShowPopupConfirm = (typePrint) => {
        onCloseSuggest();
        if (!selectedDate) {
            Alert.alert(
                translate('common.notification'),
                'Vui lòng chọn ngày in',
                [
                    {
                        text: 'Ok'
                    }
                ]
            );
            return;
        }
        let checkedProductList = selectedProductList.filter(
            (item) => item.isChecked
        );
        setSelectedProductList((current) => {
            current.forEach((item) => {
                if (item.isChecked) {
                    item.isPrinted = true;
                }
            });
            return current;
        });
        if (checkedProductList.length === 0) {
            Alert.alert(
                translate('common.notification'),
                'Vui lòng chọn sản phẩm in',
                [
                    {
                        text: 'Ok'
                    }
                ]
            );
            return;
        }
        /*if (checkedProductList.length > contantsBCH.NUMBER_OF_PRODUCT_IS_ONE_PRINTTING) {
            Alert.alert(translate('common.notification'), "Số lượng in không vượt quá 280 sản phẩm.", [
                {
                    text: "Ok",
                },
            ]);
            return;
        }*/
        let errProductList = selectedProductList.filter(
            (item) => item.isChecked && item.printQuantity == 0
        );
        if (errProductList.length > 0) {
            Alert.alert(
                translate('common.notification'),
                `Vui lòng nhập số lượng in lớn hơn 0`,
                [
                    {
                        text: 'Ok'
                    }
                ]
            );
            return;
        }

        if (!selectedPrintTemplate?.epriceReportID) {
            Alert.alert(
                translate('common.notification'),
                'Vui lòng chọn mẫu in',
                [
                    {
                        text: 'Ok'
                    }
                ]
            );
            return;
        }
        if (
            !selectedInventoryStatus?.inventoryStatusId ||
            selectedInventoryStatus.inventoryStatusId == -1
        ) {
            Alert.alert(
                translate('common.notification'),
                'Vui lòng trạng thái SP lấy thông tin hiển thị lên BCH',
                [
                    {
                        text: 'Ok'
                    }
                ]
            );
            return;
        }
        getPriceList(checkedProductList, typePrint);
    };

    const onCloseSuggest = () => {
        setShowSuggest(false);
        setShowResultSuggest(false);
        Keyboard.dismiss();
    };
    const resetData = () => {
        setInventoryStatusList([contantsBCH.EMPTY_STATUS]);
        setPrintTemplateList([contantsBCH.EMPTY_PRINT_TEMPLATE]);
        setSelectedProductList([]);
        setValueSearch('');
        setSearchedProduct([]);
        setSelectedInventoryStatus({});
        setSelectedPrintTemplate({});
    };
    const onGoBack = () => {
        const lstData = refTem.current.filter((item) => !item.isPrinted);

        actionPPL.SaveUnprinted(lstData);
        navigation.goBack();
    };
    return (
        <SafeAreaView style={styles.container}>
            <Header goBack={onGoBack} title="In BCH sản phẩm" />
            <KeyboardAwareScrollView
                enableResetScrollToCoords={true}
                keyboardShouldPersistTaps="handled"
                bounces={false}
                overScrollMode="always"
                contentContainerStyle={{ flex: 1 }}
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                extraScrollHeight={60}>
                <ScrollView>
                    <TouchableWithoutFeedback onPress={onCloseSuggest}>
                        <View style={styles.filterAreaContainer}>
                            <View>
                                {!isShowCamera ? (
                                    <>
                                        <View style={styles.mt_10}>
                                            <PickerSearch
                                                label={'name'}
                                                value={'date'}
                                                data={dateList}
                                                valueSelected={selectedDate}
                                                onChange={(item) => {
                                                    setSelectedDate(item.date);
                                                }}
                                                style={styles.comboBox}
                                                defaultLabel={'Chọn ngày in'}
                                                onPressView={onCloseSuggest}
                                            />
                                        </View>
                                        <View style={styles.mt_10}>
                                            <PickerSearch
                                                label={'name'}
                                                value={'type'}
                                                data={
                                                    contantsBCH.APPLY_PRINT_PRODUCT_BY_OPTION
                                                }
                                                valueSelected={
                                                    skipProductPresentation
                                                }
                                                onChange={(item) => {
                                                    resetData();
                                                    setSkipProductPresentation(
                                                        item.type
                                                    );
                                                }}
                                                style={styles.comboBox}
                                                defaultLabel={''}
                                                onPressView={onCloseSuggest}
                                            />
                                        </View>
                                    </>
                                ) : (
                                    <ScanBarcodeComponent
                                        isVisible={isShowCamera}
                                        closeCamera={() => {
                                            setIsShowCamera(false);
                                        }}
                                        resultScanBarcode={(barcode) => {
                                            Keyboard.dismiss();
                                            getProductByKeyword(barcode);
                                        }}
                                        onCameraRef={handleCameraRef}
                                        ref={scanBCRef}
                                    />
                                )}
                                <View style={styles.searchInput}>
                                    <SearchSuggest
                                        width={
                                            constants.width -
                                            constants.getSize(20)
                                        }
                                        height={40}
                                        placeholder={'Nhập tên/mã sản phẩm'}
                                        dataItems={searchedProduct}
                                        setIsShowCamera={setIsShowCamera}
                                        rightComponent={[
                                            !valueSearch
                                                ? null
                                                : {
                                                      source: {
                                                          uri: 'ic_close'
                                                      },
                                                      style: styles.icClose,
                                                      onpress: () => {
                                                          setValueSearch('');
                                                          setSearchedProduct(
                                                              []
                                                          );
                                                          setShowSuggest(false);
                                                      }
                                                  },
                                            null
                                        ]}
                                        onChangeText={(text) => {
                                            setValueSearch(text);
                                        }}
                                        showSuggest={showSuggest}
                                        setShowSuggest={setShowSuggest}
                                        showResultSuggest={showResultSuggest}
                                        setShowResultSuggest={
                                            setShowResultSuggest
                                        }
                                        getData={getProductByKeyword}
                                        onSelectedItem={onChooseListProduct}
                                        value={valueSearch}
                                        returnKeyType="search"
                                        onSubmitEditing={() =>
                                            getProductByKeyword(valueSearch)
                                        }
                                        onFocus={() => {
                                            if (valueSearch)
                                                setShowResultSuggest(true);
                                            setShowSuggest(true);
                                        }}
                                    />
                                </View>
                            </View>
                            <View style={styles.renderListContainer}>
                                <FlatList
                                    nestedScrollEnabled={true}
                                    style={styles.list}
                                    data={selectedProductList}
                                    renderItem={({ item, index }) => (
                                        <ProductItem
                                            product={item}
                                            index={index}
                                            selectedProductList={
                                                selectedProductList
                                            }
                                            setSelectedProductList={
                                                setSelectedProductList
                                            }
                                            printTemplateListAll={
                                                statePrintTemplate.lstPrintTemplate
                                            }
                                            setPrintTemplateList={
                                                setPrintTemplateList
                                            }
                                            navigation={navigation}
                                            removeProduct={removeProduct}
                                        />
                                    )}
                                    keyExtractor={(item, index) => item.id}
                                    removeClippedSubviews={false}
                                    keyboardShouldPersistTaps={'always'}
                                    showsVerticalScrollIndicator={false}
                                />
                            </View>

                            {keyboardStatus ? (
                                <View>
                                    <View style={styles.mt_10}>
                                        <PickerSearchCustom
                                            label={'idAndName'}
                                            value={'epriceReportID'}
                                            data={printTemplateList}
                                            valueSelected={
                                                selectedPrintTemplate.epriceReportID
                                            }
                                            onChange={(item) => {
                                                if (item.epriceReportID !== -1)
                                                    onChangePrintTemplate(item);
                                            }}
                                            style={styles.comboBox}
                                            defaultLabel={'Chọn mẫu in'}
                                            onPressView={onCloseSuggest}
                                        />
                                    </View>
                                    <View style={styles.mt_10}>
                                        <PickerSearchCustom
                                            label={'inventoryStatusName'}
                                            value={'inventoryStatusId'}
                                            data={inventoryStatusList}
                                            valueSelected={
                                                selectedInventoryStatus.inventoryStatusId
                                            }
                                            onChange={(item) => {
                                                if (
                                                    item.inventoryStatusId !==
                                                    -1
                                                )
                                                    setSelectedInventoryStatus(
                                                        item
                                                    );
                                            }}
                                            style={styles.comboBox}
                                            defaultLabel={
                                                'Chọn trạng thái SP lấy thông tin hiển thị lên BCH'
                                            }
                                            onPressView={onCloseSuggest}
                                        />
                                    </View>
                                    <TouchableOpacity
                                        style={styles.btnVerifyContainer}
                                        activeOpacity={0.7}
                                        onPress={() => onShowPopupConfirm(1)}>
                                        <MyText
                                            style={styles.btnTextVerify}
                                            addSize={2}
                                            text={'In BCH sản phẩm'}
                                        />
                                    </TouchableOpacity>
                                    {(LIST_STORE_ACCEPT_PDF_FUNC_POS.split(
                                        ','
                                    ).includes(userName) ||
                                        LIST_STORE_ACCEPT_PDF_FUNC_POS ==
                                            '-1') && (
                                        <TouchableOpacity
                                            style={styles.btnPDF}
                                            activeOpacity={0.7}
                                            onPress={() =>
                                                onShowPopupConfirm(2)
                                            }>
                                            <MyText
                                                style={styles.btnTextVerify}
                                                addSize={2}
                                                text={'Xem trước mẫu in'}
                                            />
                                        </TouchableOpacity>
                                    )}
                                </View>
                            ) : null}
                        </View>
                    </TouchableWithoutFeedback>
                </ScrollView>
                {/* {
                    isShowCamera &&
                    <BarcodeCamera
                        isVisible={isShowCamera}
                        closeCamera={() => {
                            setIsShowCamera(false);
                        }}
                        resultScanBarcode={(barcode) => {
                            Keyboard.dismiss();
                            setIsShowCamera(false);
                            getProductByBarcode(barcode);
                        }}
                    />
                } */}
            </KeyboardAwareScrollView>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: COLORS.bgFFFFFF,
        zIndex: 1
    },
    filterAreaContainer: {
        paddingHorizontal: 10,
        flex: 1
    },
    searchInput: {
        paddingTop: 10,
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
        flexDirection: 'row'
    },
    renderListContainer: {
        borderColor: COLORS.bg448E91,
        borderWidth: 2,
        marginTop: 15,
        borderRadius: 15,
        flex: 1,
        minHeight: 300,
        zIndex: -1,
        backgroundColor: COLORS.bgF0F0F0
    },
    icClose: {
        width: 12,
        height: 12,
        tintColor: COLORS.ic0000002,
        paddingRight: 15
    },
    btnVerifyContainer: {
        // backgroundColor: COLORS.bg6A976C,
        // justifyContent: 'center',
        // alignItems: 'center',
        // borderRadius: 10,
        // height: "100%",
        // paddingVertical: constants.defaultPaddingVertical,
        marginVertical: constants.defaultMarginVertical,
        borderRadius: 10,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgb(0,143,215)',
        height: 40
    },
    btnPDF: {
        marginVertical: constants.defaultMarginVertical,
        borderRadius: 10,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: COLORS.bg6A976C,
        height: 40
    },
    btnSearchContainer: {
        borderRadius: 10,
        borderTopLeftRadius: 0,
        borderBottomLeftRadius: 0,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#FEB602'
    },
    btnTextVerify: {
        color: COLORS.txtFFFFFF,
        fontWeight: 'bold'
    },
    btnTextSearch: {
        color: COLORS.txtFFFFFF,
        fontWeight: 'bold',
        height: 40,
        flex: 1
    },
    comboBox: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        height: 40,
        borderRadius: 10,
        borderWidth: 1,
        borderColor: COLORS.bdE4E4E4,
        width: constants.width - 30,
        backgroundColor: COLORS.btnFFFFFF,
        marginHorizontal: 5
    },
    mt_10: {
        marginTop: 10
    },
    list: {
        width: '100%',
        height: 220
    },
    input: {
        height: 40,
        borderColor: COLORS.bdE4E4E4,
        backgroundColor: COLORS.btnFFFFFF,
        borderRadius: 10,
        borderTopRightRadius: 0,
        borderBottomRightRadius: 0,
        fontWeight: 'bold',
        borderWidth: 1,
        borderColor: COLORS.bdE4E4E4,
        paddingLeft: 15
    },
    searchContainer: {
        paddingTop: 10,
        marginHorizontal: 5,
        flexDirection: 'row',
        flexWrap: 'wrap',
        alignItems: 'center',
        width: constants.width - 30
    }
});

const mapStateToProps = function (state) {
    return {
        userName: state.userReducer.userName,
        companyID: state.userReducer.companyID,
        lstUnprintedProducts: state.printPriceListReducer.lstUnprintedProducts,
        stateInventoryStatus: state.printPriceListReducer.stateInventoryStatus,
        statePrintTemplate: state.printPriceListReducer.statePrintTemplate
    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        actionPPL: bindActionCreators(printPriceListAction, dispatch)
    };
};

export default connect(
    mapStateToProps,
    mapDispatchToProps
)(PrintProductConfigurationSheet);
