import { consumerLoanAirtimeServiceState } from "./state";
import { actionConsumerLoanAirtimeService } from "./action";

const consumerLoanAirtimeServiceReducer = function (state = consumerLoanAirtimeServiceState, action) {
    switch (action.type) {
        case actionConsumerLoanAirtimeService.START_GET_SERVICE_LIST:
            return {
                ...state,
                dataServiceList: {},
                stateServiceList: {
                    isFetching: true,
                    isError: false,
                    description: '',
                    isEmpty: false
                }
            }
        case actionConsumerLoanAirtimeService.STOP_GET_SERVICE_LIST:
            return {
                ...state,
                dataServiceList: action.data,
                stateServiceList: {
                    isFetching: false,
                    isError: action.isError,
                    description: action.description,
                    isEmpty: action.isEmpty
                }
            }
        case actionConsumerLoanAirtimeService.UPDATE_CUSTOMER_AIRTIME_SERCICE:
            return {
                ...state,
                dataUpdateCustomer: action.data,
            };
        case actionConsumerLoanAirtimeService.CLEAR_DATA_CUSTOMER:
            return {
                ...state,
                dataUpdateCustomer: {},
            };
        case actionConsumerLoanAirtimeService.START_CREATE_OTP:
            return {
                ...state,
                dataCreateOTP: {},
            }
        case actionConsumerLoanAirtimeService.STOP_CREATE_OTP:
            return {
                ...state,
                dataCreateOTP: action.data,
            }
        case actionConsumerLoanAirtimeService.START_GET_DATA_CREATE_SERVICE_REQUEST:
            return {
                ...state,
                dataCreateServiceRequest: {},
            }
        case actionConsumerLoanAirtimeService.STOP_GET_DATA_CREATE_SERVICE_REQUEST:
            return {
                ...state,
                dataCreateServiceRequest: action.data,
            }
        case actionConsumerLoanAirtimeService.UPDATE_HEADER_AIRTIME:
            return {
                ...state,
                updateHeaderAirtime: action.data,
            }
        case actionConsumerLoanAirtimeService.START_GET_DATA_CONSUMER_LOAN_MANAGER:
            return {
                ...state,
                dataLoanConsumerManager: {},
                stateLoanConsumerManager: {
                    isFetching: true,
                    isError: false,
                    description: '',
                    isEmpty: false
                }
            }
        case actionConsumerLoanAirtimeService.STOP_GET_DATA_CONSUMER_LOAN_MANAGER:
            return {
                ...state,
                dataLoanConsumerManager: action.data,
                stateLoanConsumerManager: {
                    isFetching: false,
                    isError: action.isError,
                    description: action.description,
                    isEmpty: action.isEmpty
                }
            }
        case actionConsumerLoanAirtimeService.START_GET_SEND_OTP_PROCESS:
            return {
                ...state,
                dataSendOTPProcess: {},
            }

        case actionConsumerLoanAirtimeService.STOP_GET_SEND_OTP_PROCESS:
            return {
                ...state,
                dataSendOTPProcess: action.data,
            }
        default:
            return state;
    }
};

export { consumerLoanAirtimeServiceReducer };
