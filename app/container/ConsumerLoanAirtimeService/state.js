export const consumerLoanAirtimeServiceState = {
    dataSaleOrderCart: {},
    dataServiceList: {},
    stateServiceList: {
        isFetching: false,
        isError: false,
        isEmpty: false,
        description: '',
        updateHeaderAirtime: {},
    },
    dataUpdateCustomer: {},
    dataCreateOTP: {},
    dataCreateServiceRequest: {},
    updateHeaderAirtime: {},
    dataLoanConsumerManager: {},
    stateLoanConsumerManager: {
        isFetching: false,
        isError: false,
        isEmpty: false,
        description: ''
    },
    dataSendOTPProcess: {}
}