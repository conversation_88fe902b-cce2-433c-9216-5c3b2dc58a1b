import { API_CONST } from "@constants";
import { helper, dateHelper } from "@common";
import { apiBase, METHOD, ERROR, EMPTY, SUCCESS } from "@config";
import { translate } from "@translate";

const {
    API_GET_SERVICE_LIST,
    API_CREATE_OTP,
    API_VERIFY_OTP,
    API_GET_CREATE_SERVICE_REQUEST,
    API_GET_DATA_COLLECTION_MANAGER_NEW,
    API_PROCESS_SERVICE_REQUEST,
    API_GET_QUERY_STATUS,
    API_CREATE_AIRTIME_REFUND,
    API_GET_PROCESS_SERVICE_REQUEST
} = API_CONST;

const START_GET_SERVICE_LIST = 'START_GET_SERVICE_LIST';
const STOP_GET_SERVICE_LIST = 'STOP_GET_SERVICE_LIST';
const UPDATE_CUSTOMER_AIRTIME_SERCICE = 'UPDATE_CUSTOMER_AIRTIME_SERCICE';
const CLEAR_DATA_CUSTOMER = 'CLEAR_DATA_CUSTOMER';
const START_CREATE_OTP = 'START_CREATE_OTP';
const STOP_CREATE_OTP = 'STOP_CREATE_OTP';
const START_GET_DATA_CREATE_SERVICE_REQUEST = 'START_GET_DATA_CREATE_SERVICE_REQUEST';
const STOP_GET_DATA_CREATE_SERVICE_REQUEST = 'STOP_GET_DATA_CREATE_SERVICE_REQUEST';
const UPDATE_HEADER_AIRTIME = 'UPDATE_HEADER_AIRTIME';
const START_GET_DATA_CONSUMER_LOAN_MANAGER = 'START_GET_DATA_CONSUMER_LOAN_MANAGER';
const STOP_GET_DATA_CONSUMER_LOAN_MANAGER = 'STOP_GET_DATA_CONSUMER_LOAN_MANAGER';
const START_GET_SEND_OTP_PROCESS = "START_GET_SEND_OTP_PROCESS";
const STOP_GET_SEND_OTP_PROCESS = "STOP_GET_SEND_OTP_PROCESS";

export const actionConsumerLoanAirtimeService = {
    START_GET_SERVICE_LIST,
    STOP_GET_SERVICE_LIST,
    UPDATE_CUSTOMER_AIRTIME_SERCICE,
    CLEAR_DATA_CUSTOMER,
    START_CREATE_OTP,
    STOP_CREATE_OTP,
    START_GET_DATA_CREATE_SERVICE_REQUEST,
    STOP_GET_DATA_CREATE_SERVICE_REQUEST,
    UPDATE_HEADER_AIRTIME,
    START_GET_DATA_CONSUMER_LOAN_MANAGER,
    STOP_GET_DATA_CONSUMER_LOAN_MANAGER,
    START_GET_SEND_OTP_PROCESS,
    STOP_GET_SEND_OTP_PROCESS,
};

export const getServiceList = function (data) {
    return (dispatch, getState) => {
        const body = {
            loginStoreId: getState().userReducer.storeID,
            languageID: getState().userReducer.languageID,
            moduleID: getState().userReducer.moduleID,
            catalogID: data.catalogID,
            serviceGroupID: data.serviceGroupID,
        }
        dispatch(start_get_service_list())
        apiBase(API_GET_SERVICE_LIST, METHOD.POST, body)
            .then((response) => {
                console.log("getServiceList success", response);
                if (helper.IsNonEmptyArray(response.object)) {
                    dispatch(stop_get_service_list(response.object, false, '', false));
                } else {
                    dispatch(stop_get_service_list([], true, 'Không lấy được dữ liệu', false));
                }
            }).catch(error => {
                dispatch(stop_get_service_list([], false, error.msgError, true))
                console.log("getServiceList error", error);
            })
    }
};

export const createOTP = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                "type": data.type,
                "phoneNumber": data.phoneNumber,
                "typeContent": "CASHLOAN",
                "data": {
                    "OTP_LEN": data.lenOtp
                },
                "company": data.brandID,
                "onlySms": data.onlySms
            }
            dispatch(start_create_otp())
            apiBase(API_CREATE_OTP, METHOD.POST, body)
                .then((response) => {
                    console.log("createOTP success", response);
                    if (!helper.IsEmptyObject(response.object)) {
                        dispatch(stop_create_otp(response?.object, false, '', false));
                        resolve(response?.object);
                    } else {
                        dispatch(stop_create_otp({}, true, 'Không lấy được dữ liệu', false));
                        reject("Không lấy được dữ liệu");
                    }
                }).catch(error => {
                    dispatch(stop_create_otp({}, false, error.msgError, true))
                    console.log("createOTP error", error);
                    reject(error.msgError);
                })
        })
    }
};

export const verifyOTP = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                "otp": data.otp,
                "phoneNumber": data.phoneNumber,
            };
            apiBase(API_VERIFY_OTP, METHOD.POST, body)
                .then((response) => {
                    console.log("verifyOTP success", response);
                    const { object } = response;
                    if (
                        // helper.hasProperty(object, 'CUSTOMERID') &&
                        helper.hasProperty(object, 'ID')
                    ) {
                        resolve({
                            customerId: object.CUSTOMERID,
                            requestId: object.ID,
                        });
                    }
                    else {
                        reject(translate('shoppingCart.no_verified_information'));
                    }
                })
                .catch((error) => {
                    console.log("verifyOTP error", error);
                    const { msgError } = error;
                    if (helper.hasProperty(OTP_CODE, msgError)) {
                        reject(OTP_CODE[msgError]);
                    }
                    else {
                        reject(msgError);
                    }
                });
        });
    };
};

export const getDataCreateServiceRequest = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "catalogID": data.catalogID,
                "serviceGroupID": data.serviceGroupID,
                "airtimeTransactionTypeID": data.airtimeTransactionTypeID,
                "airTimeTransactionBO": data.airTimeTransactionBO,
                "ExtraData": data.ExtraData
            };
            dispatch(start_get_data_create_service_request());
            apiBase(API_GET_CREATE_SERVICE_REQUEST, METHOD.POST, body)
                .then((response) => {
                    console.log("getDataCreateServiceRequest success", response);
                    const { object } = response;
                    if (!helper.IsEmptyObject(object)) {
                        dispatch(stop_get_data_create_service_request(object));
                        resolve(response);
                    } else {
                        dispatch(stop_get_data_create_service_request({}));
                        resolve("Lỗi lấy thông tin dữ liệu");
                    }
                })
                .catch((error) => {
                    dispatch(stop_get_data_create_service_request(!SUCCESS, {}));
                    const { msgError } = error;
                    console.log("getDataCreateServiceRequest error", error);
                    reject(msgError);
                });
        });
    };
};

export const getSearchConsumerLoanManager = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                Keyword: data.keyword,
                ProcessUser: data.isCreate ? getState().userReducer.userName : '',
                FromDate: dateHelper.formatDateYYYYMMDD(data.fromDate) + "T00:00:00",
                ToDate: dateHelper.formatDateYYYYMMDD(data.toDate) + "T23:59:59",
                IsDeleted: data.isDelete ? true : false,
                catalogID: data.catalogID,
                serviceGroupID: data.serviceGroupID,
                isFailedNotDeleted: data.isFailedNotDeleted ? true : false
            }
            dispatch(start_get_data_comsumer_loan_manager())
            apiBase(API_GET_DATA_COLLECTION_MANAGER_NEW, METHOD.POST, body)
                .then((response) => {
                    if (
                        helper.hasProperty(response, 'object') &&
                        helper.isArray(response.object) &&
                        response.object.length > 0
                    ) {
                        console.log("getSearchConsumerLoanManager success", response);
                        dispatch(stop_get_data_comsumer_loan_manager(response.object));
                        resolve(response.object);
                    } else {
                        dispatch(stop_get_data_comsumer_loan_manager([], false, 'Không tìm thấy đơn hàng', true));
                        reject("Lỗi lấy thông tin dữ liệu");
                    }
                })
                .catch((error) => {
                    console.log('getSearchConsumerLoanManager err', error);
                    dispatch(stop_get_data_comsumer_loan_manager([], !EMPTY, error.msgError, ERROR));
                    reject("Lỗi lấy thông tin dữ liệu");
                });
        })
    }

}

export const processServiceRequest = function (data) {
    return (dispatch, getState) => {
        return new Promise((resolve, reject) => {
            const body = {
                "loginStoreId": getState().userReducer.storeID,
                "languageID": getState().userReducer.languageID,
                "moduleID": getState().userReducer.moduleID,
                "catalogID": data.catalogID,
                "serviceGroupID": data.serviceGroupID,
                "serviceVoucherID": data.serviceVoucherID
            };
            apiBase(API_PROCESS_SERVICE_REQUEST, METHOD.POST, body);
        })
    }

}

export const parnerQueryStatusServiceRequest = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                productID: data.productID,
                serviceVoucherID: data.serviceVoucherID
            };
            apiBase(API_GET_QUERY_STATUS, METHOD.POST, body).then((response) => {
                console.log("parnerQueryStatusServiceRequest success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                }
                else {
                    reject("Lỗi lấy thông tin dữ liệu");
                }
            }).catch(error => {
                console.log("parnerQueryStatusServiceRequest error", error);
                reject(error.msgError)
            })
        });
    }
}


export const createAirtimeRefundServiceRequest = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                productID: data.productID,
                airtimeTransactionTypeID: data.airtimeTransactionTypeID,
                airtimeTransactionID: data.airtimeTransactionID
            };
            apiBase(API_CREATE_AIRTIME_REFUND, METHOD.POST, body).then((response) => {
                console.log("createAirtimeRefundServiceRequest success", response);
                if (!helper.IsEmptyObject(response.object)) {
                    resolve(response.object);
                }
                else {
                    reject("Lỗi lấy thông tin dữ liệu");
                }
            }).catch(error => {
                console.log("createAirtimeRefundServiceRequest error", error);
                reject(error.msgError)
            })
        });
    }
}

export const getProcessServicePrequest = function (data) {
    return function (dispatch, getState) {
        return new Promise((resolve, reject) => {
            let body = {
                loginStoreId: getState().userReducer.storeID,
                languageID: getState().userReducer.languageID,
                moduleID: getState().userReducer.moduleID,
                "catalogID": data.catalogID,
                "serviceGroupID": data.serviceGroupID,
                "serviceVoucherID": data.serviceVoucherID,
                "isRequestOTP": data.isRequestOTP
            };
            dispatch(start_get_send_otp_processs());
            apiBase(API_GET_PROCESS_SERVICE_REQUEST, METHOD.POST, body)
                .then((response) => {
                    console.log("getSendOTPProcessServicePrequest success", response);
                    const { object } = response;
                    if (!helper.IsEmptyObject(object)) {
                        dispatch(stop_get_send_otp_processs(object));
                        resolve(response);
                    } else {
                        dispatch(stop_get_send_otp_processs({}));
                        reject({ msgError: "Không tìm thấy thông tin gửi lại OTP" });
                    }
                })
                .catch((error) => {
                    dispatch(stop_get_send_otp_processs({}));
                    const { msgError } = error;
                    console.log("getSendOTPProcessServicePrequest error", error);
                    reject(msgError);
                });
        });
    };
};

export const start_get_service_list = () => {
    return {
        type: START_GET_SERVICE_LIST,
    };
};

export const stop_get_service_list = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_SERVICE_LIST,
    data,
    isEmpty,
    description,
    isError
});

const update_customer_airtime_service = (
    data
) => ({
    type: UPDATE_CUSTOMER_AIRTIME_SERCICE,
    data
});

export const updateCustomerAirtimeService = (data) => {
    return function (dispatch, getState) {
        dispatch(update_customer_airtime_service(data));
    }
}

export const clear_data_customer = () => ({
    type: CLEAR_DATA_CUSTOMER
});

export const start_create_otp = () => {
    return {
        type: START_CREATE_OTP,
    };
};

export const stop_create_otp = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_CREATE_OTP,
    data,
    isEmpty,
    description,
    isError
});

const start_get_data_create_service_request = () => ({
    type: START_GET_DATA_CREATE_SERVICE_REQUEST
});

const stop_get_data_create_service_request = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_DATA_CREATE_SERVICE_REQUEST,
    data,
    isEmpty,
    description,
    isError
});

const update_header_airtime = (
    data
) => ({
    type: UPDATE_HEADER_AIRTIME,
    data
});

export const updateHeaderAirtime = (data) => {
    return function (dispatch, getState) {
        dispatch(update_header_airtime(data));
    }
}

const start_get_data_comsumer_loan_manager = () => ({
    type: START_GET_DATA_CONSUMER_LOAN_MANAGER
});
const stop_get_data_comsumer_loan_manager = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_DATA_CONSUMER_LOAN_MANAGER,
    data,
    isEmpty,
    description,
    isError
});

const start_get_send_otp_processs = () => ({
    type: START_GET_SEND_OTP_PROCESS
});
const stop_get_send_otp_processs = (
    data,
    isEmpty = false,
    description = '',
    isError = false
) => ({
    type: STOP_GET_SEND_OTP_PROCESS,
    data,
    isEmpty,
    description,
    isError
});

const OTP_CODE = {
    "Your OTP is exists": "Mã OTP đã tạo trước đó vẫn còn hiệu lực. Vui lòng chờ sau 1 phút để lấy lại mã mới.",
    "Your OTP is verified": "Mã OTP đã được xác thực trước đó. Vui lòng lấy lại mã mới để xác thực.",
    "Your OTP is expired": "Mã OTP đã hết hiệu lực. Vui lòng lấy lại mã mới để xác thực.",
    "Your OTP is not found": "Mã OTP không hợp lệ.",
    "Error occurred while sending params to sms crm": "Số điện thoại đã được gửi tin nhắn nhiều lần trong ngày."
}