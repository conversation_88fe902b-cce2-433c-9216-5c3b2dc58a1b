import {
  SafeAreaView,
  StyleSheet,
  View,
  FlatList,
  Animated,
} from "react-native";
import React, { useCallback, useRef, useState } from "react";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { COLORS } from "@styles";
import { TouchableOpacity } from "react-native";
import { BaseLoading, MyText } from "@components";
import { connect } from "react-redux";
import { bindActionCreators } from "redux";
import * as actionConsumerLoanAirtimeServiceCreator from "./action";
import { useFocusEffect } from "@react-navigation/native";
import FastImage from "react-native-fast-image";

const ConsumerLoanAirtimeService = ({
  actionConsumerLoanAirtimeService,
  dataServiceList,
  stateServiceList,
  itemCatalog,
  navigation,
}) => {
  const [updateItem, setUpdateItem] = useState({});
  const scaleValue = useRef(new Animated.Value(1)).current;
  const opacityValue = useRef(new Animated.Value(1)).current;
  const { ServiceCategoryID, AirtimeServiceGroupID } = itemCatalog ?? {};
  const { isFetching, isError, isEmpty, description } = stateServiceList ?? "";
  const [isVisibleModal, setIsVisibleModal] = useState(false);

  const animatePressIn = () => {
    Animated.parallel([
      Animated.spring(scaleValue, {
        toValue: 0.95,
        friction: 3,
        useNativeDriver: true,
      }),
      Animated.timing(opacityValue, {
        toValue: 0.8,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const animatePressOut = () => {
    Animated.parallel([
      Animated.spring(scaleValue, {
        toValue: 1,
        friction: 3,
        useNativeDriver: true,
      }),
      Animated.timing(opacityValue, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  };

  useFocusEffect(
    useCallback(() => {
      const data = {
        catalogID: ServiceCategoryID,
        serviceGroupID: AirtimeServiceGroupID,
      };
      actionConsumerLoanAirtimeService.getServiceList(data);
    }, [actionConsumerLoanAirtimeService])
  );

  const handleItemPress = useCallback(
    (item) => {
      const { AirTimeTransactionTypeID, IsStoreConfig } = item ?? {};
      const transactionTypes = {
        1972: true,
        1992: true
      };
      if (!transactionTypes[AirTimeTransactionTypeID]) {
        return;
      }
      actionConsumerLoanAirtimeService.updateHeaderAirtime(item);
      navigation.navigate(IsStoreConfig ? "MenuConsumerLoan" : "QrCodeCathayLifeOld");
    },
    [actionConsumerLoanAirtimeService, navigation]
  );

  const preGetServiceList = (ServiceCategoryID, AirtimeServiceGroupID) => {
    const data = {
      catalogID: ServiceCategoryID,
      serviceGroupID: AirtimeServiceGroupID,
    };
    actionConsumerLoanAirtimeService.getServiceList(data);
  };

  const renderItem = ({ item, index }) => {
    return (
      <View
        style={{
          padding: 5,
        }}
      >
        <View
          style={{
            flexDirection: "row",
            backgroundColor: COLORS.bgFFFFFF,
            borderRadius: 15,
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 5 },
            shadowOpacity: 0.15,
            shadowRadius: 10,
            elevation: 5,
            padding: 10,
            alignItems: "center",
          }}
        >
          <View
            style={{
              flexDirection: "row",
            }}
          >
            <TouchableOpacity
              onPressIn={animatePressIn}
              onPressOut={() => {
                animatePressOut();
                handleItemPress(item);
              }}
              style={{
                flexDirection: "row",
                alignItems: "center",
                flex: 1,
                height: 90,
                borderRadius: 10,
                backgroundColor: COLORS.bgFFFFFF,
                flexDirection: "row",
                shadowColor: COLORS.bg8E8E93,
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.3,
                shadowRadius: 6,
                elevation: 6,
              }}
            >
              <FastImage
                style={{ width: 90, height: 50, marginLeft: 5 }}
                source={{
                  uri: item.Logo,
                  priority: FastImage.priority.normal,
                }}
                resizeMode={FastImage.resizeMode.contain}
              />
              <MyText
                style={{
                  color: COLORS.txt000000,
                  fontSize: 12,
                  fontWeight: "bold",
                  textAlign: "center",
                  flex: 1,
                  paddingHorizontal: 5,
                  textAlign: "left",
                }}
                text={item.AirTimeTransactionTypeName}
              />
            </TouchableOpacity>
            {/* <View
              style={{
                width: "25%",
                height: 90,
                justifyContent: "center",
                alignItems: "center",
                backgroundColor: COLORS.txt0099E5,
                borderRadius: 10,
                marginLeft: 5,
              }}
            >
              <TouchableOpacity
                onPress={() => handleInfoPress(item)}
                style={{
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Icon
                  iconSet={"Ionicons"}
                  name={"information-circle-outline"}
                  color={COLORS.bgFFFFFF}
                  size={35}
                />
                <MyText
                  style={{
                    color: COLORS.bgFFFFFF,
                    width: 70,
                    textAlign: "center",
                  }}
                  text={"Biểu phí"}
                />
              </TouchableOpacity>
            </View> */}
          </View>
        </View>
        {isVisibleModal && (
          <InsuranceBenefits
            imageURL={updateItem?.PriceList?.[0]}
            isVisible={isVisibleModal}
            hideModal={() => {
              setIsVisibleModal(false);
            }}
          />
        )}
      </View>
    );
  };
  return (
    <View
      style={{
        flex: 1,
        backgroundColor: COLORS.bgFFFFFF,
      }}
    >
      <KeyboardAwareScrollView
        style={{
          flex: 1,
        }}
        enableResetScrollToCoords={false}
        keyboardShouldPersistTaps="always"
        bounces={false}
        overScrollMode="always"
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        extraScrollHeight={60}
      >
        <SafeAreaView
          style={{
            flex: 1,
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <BaseLoading
            isLoading={isFetching}
            isError={isError}
            isEmpty={isEmpty}
            textLoadingError={description}
            onPressTryAgains={() => {
              preGetServiceList(ServiceCategoryID, AirtimeServiceGroupID);
            }}
            content={
              <View>
                <FlatList
                  style={{
                    backgroundColor: COLORS.bgFFFFFF,
                    padding: 5,
                  }}
                  data={dataServiceList}
                  keyExtractor={(item, index) => `${index}`}
                  renderItem={(item) => renderItem(item)}
                />
              </View>
            }
          />
        </SafeAreaView>
      </KeyboardAwareScrollView>
    </View>
  );
};

const mapStateToProps = function (state) {
  return {
    dataServiceList: state.bankAirtimeServiceReducer.dataServiceList,
    stateServiceList: state.bankAirtimeServiceReducer.stateServiceList,
    itemCatalog: state.collectionReducer.itemCatalog,
  };
};

const mapDispatchToProps = function (dispatch) {
  return {
    actionConsumerLoanAirtimeService: bindActionCreators(
      actionConsumerLoanAirtimeServiceCreator,
      dispatch
    ),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(ConsumerLoanAirtimeService);

const styles = StyleSheet.create({});
