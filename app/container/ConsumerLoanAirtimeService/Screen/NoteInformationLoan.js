import { Alert, Keyboard, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useCallback, useState } from 'react';
import { MyText, TitleInput } from '@components';
import { constants } from '../../../constants';
import { COLORS } from '../../../styles';
import { helper } from '@common';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import { useFocusEffect } from '@react-navigation/native';
import * as actionConsumerLoanAirtimeServiceCreator from "../action";
import TitleInputEmail from '../component/TitleInputEmail'
import SingleDatePicker from '../component/datepicker/index';

const NoteInformationLoan = ({
    navigation,
    actionConsumerLoanAirtimeService,
    dataUpdateCustomer,
    updateHeaderAirtime
}) => {
    const [customerPhone, setCustomerPhone] = useState('');
    const [customerName, setCustomerName] = useState('');
    const [customerIdCard, setCustomerIdCard] = useState('');
    const [customerEmail, setCustomerEmail] = useState("");
    const [isError, setIsError] = useState(false);
    const [dateOfBirth, setDateOfBirth] = useState("");
    const regExpIDCard12 = new RegExp(/^\d{12}$/);
    const isValidateIDCard12 = regExpIDCard12.test(customerIdCard);
    const isValidateIDCard = isValidateIDCard12;
    const regExpPhoneNumber10 = new RegExp(/^\d{10}$/);
    const isValidateRechargerPhoneNumber = regExpPhoneNumber10.test(customerPhone);
    const isCathay = updateHeaderAirtime?.AirTimeTransactionTypeID === 1992;
    const airTimeTransactionTypeID = updateHeaderAirtime?.AirTimeTransactionTypeID;

    useFocusEffect(
        useCallback(() => {
            const { customerPhone, customerName, customerIdCard, dateOfBirth } = dataUpdateCustomer ?? '';
            setCustomerPhone(customerPhone);
            setCustomerName(customerName);
            setCustomerIdCard(customerIdCard);
            setDateOfBirth(dateOfBirth);
        }, [dataUpdateCustomer])
    );


    const handleValidateEmail = (email) => {
        if (!constants.regexEmail.test(email)) {
            setIsError(true);
        } else {
            setIsError(false);
        }
    }

    const valadateDataconnectStep = () => {
        if (!isValidateRechargerPhoneNumber) {
            Alert.alert("", "Vui lòng nhập số điện thoại đúng 10 số");
            return false;
        }
        if (customerName == "" || customerName == undefined) {
            Alert.alert("", "Vui lòng nhập tên khách hàng");
            return false;
        }
        if (!isValidateIDCard) {
            Alert.alert("", "Vui lòng nhập số CCCD/GCC đúng 12 số");
            return false;
        }
        if ((dateOfBirth == "" || dateOfBirth == undefined) && !isCathay) {
            Alert.alert("", "Vui lòng chọn ngày sinh khách hàng");
            return false;
        }
        if (customerEmail == "" && isCathay) {
            Alert.alert("", "Vui lòng nhập địa chỉ Email");
            return false;
        }
        else if (isError) {
            Alert.alert("", "Định dạng email không hợp lệ.");
            return false;
        }
        else {
            navigation.navigate('OTPConsumerAirtimeService');
        }
    }

    return (
        <View style={{
            flex: 1,
            backgroundColor: 'white'
        }}>
            <View style={{
                padding: 10
            }}>
                <TitleInput
                    title={"Số điện thoại khách hàng"}
                    styleInput={{
                        borderWidth: 1,
                        borderRadius: 4,
                        borderColor: COLORS.bdCCCCCC,
                        marginBottom: 5,
                        paddingHorizontal: 10,
                        backgroundColor: COLORS.bgFFFFFF,
                        paddingVertical: 8
                    }}
                    placeholder={"Nhập số điện thoại khách hàng"}
                    value={customerPhone}
                    onChangeText={(text) => {
                        const regExpPhone = new RegExp(/^[0]\d{0,9}$/);
                        const isValidate = regExpPhone.test(text) || (text == "");
                        if (isValidate) {
                            setCustomerPhone(text);
                            actionConsumerLoanAirtimeService.updateCustomerAirtimeService({
                                ...dataUpdateCustomer,
                                customerPhone: text
                            })
                        }
                    }}
                    keyboardType="numeric"
                    returnKeyType="done"
                    blurOnSubmit
                    width={constants.width - 20}
                    height={40}
                    clearText={() => {
                        setCustomerPhone('');
                        actionConsumerLoanAirtimeService.updateCustomerAirtimeService({
                            ...dataUpdateCustomer,
                            customerPhone: ''
                        })
                    }}
                    key="rechargerPhoneNumber"
                    isRequired={true}
                />
                <TitleInput
                    title={"Họ và tên khách hàng"}
                    styleInput={{
                        borderWidth: 1,
                        borderRadius: 4,
                        borderColor: COLORS.bdCCCCCC,
                        marginBottom: 10,
                        paddingHorizontal: 10,
                        backgroundColor: COLORS.bgFFFFFF,
                        paddingVertical: 8,
                    }}
                    placeholder={"Nhập họ và tên khách hàng"}
                    value={customerName}
                    onChangeText={(text) => {
                        if (helper.isValidateCharVN(text)) {
                            setCustomerName(text);
                            actionConsumerLoanAirtimeService.updateCustomerAirtimeService({
                                ...dataUpdateCustomer,
                                customerName: text
                            })
                        }
                    }}
                    keyboardType={"default"}
                    returnKeyType={"done"}
                    blurOnSubmit
                    width={constants.width - 20}
                    height={40}
                    clearText={() => {
                        setCustomerName('');
                        actionConsumerLoanAirtimeService.updateCustomerAirtimeService({
                            ...dataUpdateCustomer,
                            customerName: ''
                        })
                    }}
                    key="customerName"
                    isRequired={true}
                />
                <View style={{
                    marginVertical: -5,
                    height: 70,
                    marginBottom: 5
                }}>
                    <MyText style={{ fontWeight: '700', fontStyle: 'italic', fontSize: 13 }}
                        text={"Ngày sinh: "}
                        children={<MyText text={'*'} style={{ color: COLORS.txtFF0000, fontSize: 16 }} />}
                    />
                    <SingleDatePicker
                        date={dateOfBirth}
                        format={'YYYY-MM-DD'}
                        onDateChange={(dateStr) => {
                            setDateOfBirth(dateStr)
                            actionConsumerLoanAirtimeService.updateCustomerAirtimeService({
                                ...dataUpdateCustomer,
                                dateOfBirth: `${dateStr}T00:00:00`
                            })
                        }}
                        airTimeTransactionTypeID ={airTimeTransactionTypeID}
                    />
                </View>
                <TitleInput
                    title={"Số CCCD/GCC"}
                    styleInput={{
                        borderWidth: 1,
                        borderRadius: 4,
                        borderColor: COLORS.bdCCCCCC,
                        marginBottom: 10,
                        paddingHorizontal: 10,
                        backgroundColor: COLORS.bgFFFFFF,
                        paddingVertical: 8
                    }}
                    placeholder={"Nhập số CCCD/GCC"}
                    value={customerIdCard}
                    onChangeText={(text) => {
                        let validate = new RegExp(/^\d{0,12}$/);
                        if (validate.test(text) || text === "") {
                            setCustomerIdCard(text);
                            actionConsumerLoanAirtimeService.updateCustomerAirtimeService({
                                ...dataUpdateCustomer,
                                customerIdCard: text
                            })
                        }
                    }}
                    keyboardType="numeric"
                    returnKeyType="done"
                    onSubmitEditing={Keyboard.dismiss}
                    width={constants.width - 20}
                    height={40}
                    clearText={() => {
                        setCustomerIdCard('');
                        actionConsumerLoanAirtimeService.updateCustomerAirtimeService({
                            ...dataUpdateCustomer,
                            customerIdCard: ''
                        })
                    }}
                    key="customerIdCard"
                    isRequired={true}
                />
                {
                    isCathay && (
                        <>
                            <TitleInputEmail
                                title={"Địa chỉ Email"}
                                isRequired
                                styleInput={{
                                    borderWidth: 1,
                                    borderRadius: 4,
                                    borderColor: COLORS.bdCCCCCC,
                                    marginBottom: 5,
                                    paddingHorizontal: 10,
                                    paddingVertical: 8,
                                }}
                                placeholder={"Nhập địa chỉ Email"}
                                value={customerEmail}
                                onChangeText={(email) => {
                                    setCustomerEmail(email);
                                    actionConsumerLoanAirtimeService.updateCustomerAirtimeService({
                                        ...dataUpdateCustomer,
                                        customerEmail: email
                                    })
                                    handleValidateEmail(email);
                                }}
                                keyboardType={"default"}
                                returnKeyType={"done"}
                                blurOnSubmit={true}
                                onSubmitEditing={Keyboard.dismiss}
                                width={constants.width - 20}
                                height={40}
                                regex={constants.regexEmail}
                                errorMessage={"Định dạng email không hợp lệ."}
                                clearText={() => {
                                    Keyboard.dismiss();
                                    setCustomerEmail("");
                                    actionConsumerLoanAirtimeService.updateCustomerAirtimeService({
                                        ...dataUpdateCustomer,
                                        customerEmail: ""
                                    })
                                }}
                                maxLength={300}
                                key={"ContactEmail"}
                            />
                            <Note titleNote={"Địa chỉ Email dùng để nhận các thông tin quan trọng của CATHAY, nhân viên vui lòng nhập đúng!"} />
                        </>
                    )
                }

                <View style={{
                    width: '100%',
                    height: 50,
                    flexDirection: 'row',
                    paddingHorizontal: 10,
                    marginTop: 15,
                    justifyContent: 'center',
                    alignItems: 'center'
                }}>
                    <TouchableOpacity
                        onPress={() => valadateDataconnectStep()}
                        style={{
                            backgroundColor: COLORS.bg2FB47C,
                            height: 50,
                            borderRadius: 25,
                            alignItems: "center",
                            justifyContent: "center",
                            marginTop: 20,
                            width: '50%'
                        }}>
                        <MyText
                            text={"TIẾP TỤC"}
                            style={{
                                fontWeight: 'bold',
                                color: COLORS.bgFFFFFF
                            }}
                        />
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
};

const mapStateToProps = function (state) {
    return {
        itemCatalog: state.collectionReducer.itemCatalog,
        dataUpdateCustomer: state.consumerLoanAirtimeServiceReducer.dataUpdateCustomer,
        updateHeaderAirtime: state.bankAirtimeServiceReducer.updateHeaderAirtime,
    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        actionConsumerLoanAirtimeService: bindActionCreators(actionConsumerLoanAirtimeServiceCreator, dispatch),
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(NoteInformationLoan);

const styles = StyleSheet.create({});

const Note = ({ titleNote }) => {
    return (
        <View
            style={{
                flexDirection: "row",
                width: "95%",
                justifyContent: "center",
                alignItems: "center",
            }}
        >
            <MyText
                text={"Lưu ý: "}
                addSize={-1.5}
                style={{
                    color: COLORS.bgEA1D5D,
                    textAlign: "center",
                    fontStyle: "normal",
                    fontWeight: "bold",
                }}
            >
                <MyText
                    text={titleNote}
                    addSize={-1.5}
                    style={{
                        color: COLORS.bgEA1D5D,
                        textAlign: "center",
                        fontStyle: "italic",
                        width: constants.width - 10,
                    }}
                />
            </MyText>
        </View>
    );
};

