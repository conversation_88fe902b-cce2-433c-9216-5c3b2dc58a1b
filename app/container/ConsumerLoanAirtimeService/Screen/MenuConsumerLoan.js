import { FlatList, Image, Linking, StyleSheet, TouchableOpacity, View } from 'react-native'
import React, { useState } from 'react';
import { COLORS } from '@styles';
import { MyText } from '@components';
import { constants } from '../../../constants';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as actionConsumerLoanAirtimeServiceCreator from "../action";
import BonusMessage from '../component/BonusMessage';

const MenuConsumerLoan = ({
  navigation,
  actionConsumerLoanAirtimeService,
  updateHeaderAirtime
}) => {
  const { AirTimeTransactionTypeID, InfoReward } = updateHeaderAirtime;
  const safeInfoReward = Array.isArray(InfoReward) ? InfoReward : [];

  const LOAN_URLS = {
    1972: "https://cake.vn/loan-settlement",
    default: "https://docs.google.com/spreadsheets/d/1zNqYYIWiB5-eTIHSyqAt4lPDEJ-D_u6V/edit?gid=1712153470#gid=1712153470"
  };

  const data = [
    {
      id: 1,
      name: "Nhập liệu hồ sơ vay tiền mặt",
      screeen: "NoteInformationLoan",
      source: require('../../../../assets/loan_paper.png'),
    },
    {
      id: 2,
      name: "Danh sách hồ sơ vay tiền mặt",
      screeen: "PullConsumerLoanPackage",
      source: require('../../../../assets/list_paper_loan.png'),
    },
    {
      id: 3,
      name: "Tư vấn gói vay tiền mặt",
      screeen: () => {
        const url = LOAN_URLS[AirTimeTransactionTypeID] || LOAN_URLS.default;
        Linking.openURL(url);
      },
      source: require('../../../../assets/advise_loan.png'),
    }
  ];

  const renderItem = ({ item }) => {
    const handlePress = (item) => {
      if (typeof item.screeen === 'function') {
        item.screeen();
      } else {
        navigation.navigate(item.screeen);
      }
    };
    return (
      <TouchableOpacity
        onPress={() => {
          handlePress(item);
          actionConsumerLoanAirtimeService.clear_data_customer();
        }}
        style={{
          margin: 3,
          justifyContent: 'center',
          alignItems: 'center',
          padding: 10,
          width: constants.width / 2 - constants.getSize(10),
          height: 100,
          backgroundColor: COLORS.bgF2F2F2,
          borderRadius: 10,
          padding: 35,
          shadowColor: COLORS.bg000000,
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.25,
          shadowRadius: 4,
          elevation: 5,
          flexDirection: 'row'
        }}
        activeOpacity={1}
      >
        <Image
          style={{
            width: 60,
            height: 60,
          }}
          source={item.source}
        />
        <View style={{
          width: 110,
          height: 60,
          justifyContent: 'center'
        }}>
          <MyText
            text={item.name}
            style={{
              color: COLORS.bg7F7F7F,
              fontWeight: 'bold',
              marginLeft: 5,
              fontSize: 15,
            }}
          />
        </View>
      </TouchableOpacity>
    )
  }
  return (
    <View style={{
      marginTop: 5,
      justifyContent: 'center',
      alignItems: 'center'
    }}>
      <FlatList
        data={data}
        keyExtractor={(item, index) => index.toString()}
        renderItem={renderItem}
        numColumns={2}
        bounces={false}
        scrollEventThrottle={16}
        nestedScrollEnabled={true}
        columnWrapperStyle={{
          flex: 1
        }}
      />
      <BonusMessage message={safeInfoReward}/>
    </View>
  )
}

const mapStateToProps = function (state) {
  return {
    updateHeaderAirtime:
      state.insuranceAirtimeServiceReducer.updateHeaderAirtime,
  };
};

const mapDispatchToProps = function (dispatch) {
  return {
    actionConsumerLoanAirtimeService: bindActionCreators(actionConsumerLoanAirtimeServiceCreator, dispatch),
  };
};
export default connect(mapStateToProps, mapDispatchToProps)(MenuConsumerLoan);

const styles = StyleSheet.create({})