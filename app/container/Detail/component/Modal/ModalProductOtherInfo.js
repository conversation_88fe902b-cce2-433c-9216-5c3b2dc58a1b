import React, { useState } from 'react';
import {
    SafeAreaView,
    View,
    ActivityIndicator
} from 'react-native';
import KModal from "react-native-modal";
import { Bar } from 'react-native-progress';
import { WebView } from 'react-native-webview';
import { ModalHeader } from "@header";
import { constants, ENUM } from "@constants";
import { translate } from '@translate';
import { COLORS } from "@styles";
import { helper } from "@common";
import { useBrandCheck } from '../../..//AnKhangNew/hooks';

const ModalProductOtherInfo = ({
    isVisible,
    hideModal,
    productInfo,
    webInfo,
    brandID
}) => {
    const { productIDRef } = productInfo;
    const { link, title } = webInfo;
    const [isLoading, setIsLoading] = useState(true);
    const [progress, setProgress] = useState(0);
    console.log('ModalProductOtherInfo', {
        uri: `${HOST_BRAND[brandID]}sp-${link}-${productIDRef}`,
        headers: { "Referer": `${HOST_BRAND[brandID]}sp-${productIDRef}` }
    });
    onLoad = ({ nativeEvent }) => {
        setProgress(nativeEvent.progress);
    }

    onLoadEnd = () => {
        setIsLoading(false);
    }
    const isAva = useBrandCheck(ENUM.BRAND_ID.AVA);
    const uri = isAva
        ? `${HOST_BRAND[brandID]}spn-${productIDRef}`
        : `${HOST_BRAND[brandID]}sp-${link}-${productIDRef}`;

    const referer = isAva
        ? `${HOST_BRAND[brandID]}spn-${productIDRef}`
        : `${HOST_BRAND[brandID]}sp-${productIDRef}`;

    return (
        <KModal
            isVisible={isVisible}
            deviceWidth={constants.width}
            deviceHeight={constants.height}
            animationIn="slideInUp"
            animationOut="slideOutDown"
            useNativeDriver={true}
            hideModalContentWhileAnimating={true}
            style={{ margin: 0 }}
        >
            <View style={{
                flex: 1,
            }}>
                <ModalHeader
                    onClose={hideModal}
                    title={title}
                />
                <SafeAreaView style={{
                    flex: 1,
                    backgroundColor: COLORS.bgFFFFFF,
                }}>
                    {
                        isLoading &&
                        <Bar
                            progress={progress}
                            width={null}
                            borderRadius={0}
                            borderWidth={0}
                            color={COLORS.bg008000}
                            useNativeDriver={true}
                            height={5}
                        />
                    }
                    {
                        !helper.IsEmptyObject(productInfo) &&
                        <WebView
                            style={{ flex: 1 }}
                            source={{
                                uri: uri,
                                headers: { "Referer": referer }
                            }}
                            onLoadProgress={onLoad}
                            onLoadEnd={onLoadEnd}
                            mediaPlaybackRequiresUserAction={true}
                            allowsInlineMediaPlayback={true}
                            applicationNameForUserAgent={'mwgpos-app'}
                        />
                    }
                </SafeAreaView>
            </View>
        </KModal>
    );
}

export default ModalProductOtherInfo;

const HOST_BRAND = {
    "1": "https://www.thegioididong.com/",
    "2": "https://www.dienmayxanh.com/",
    "6": "https://www.bluetronics.com/",
    "8": "https://www.nhathuocankhang.com/",
    "11": "https://www.bluetronics.com/",
    "16": "https://www.thegioididong.com/",
    "14": "https://www.avasport.com/",
    "15": "https://www.avakids.com/",
    "17": "https://www.avasport.com/"
}