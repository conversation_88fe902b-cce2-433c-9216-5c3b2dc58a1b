import React, { useState, useEffect } from 'react';
import {
    SafeAreaView,
    StyleSheet,
    View,
    FlatList,
    TouchableOpacity,
    Image,
    Alert
} from 'react-native';
import KModal from "react-native-modal";
import { ModalHeader } from "@header";
import { BaseLoading, MyText, ImageURI, Icon } from "@components";
import { constants, API_CONST } from "@constants";
import RadioInterestRate from "../Radio/RadioInterestRate";
const { API_LOGO_PARTNER_INSTALLMENT } = API_CONST;
import { translate } from '@translate';
import { COLORS } from "@styles";

const ModalInstallment = ({
    isVisible,
    dataInstallment,
    dataInterestRate,
    onChangePartner,
    onChangeProgram,
    statePartner,
    stateProgram,
    onPartnerTryAgains,
    onProgramTryAgains,
    hideModal,
    dataUserCode
}) => {
    const [partnerID, setPartnerID] = useState(undefined);
    const [partnerLogo, setPartnerLogo] = useState("");
    const [interestRateLabel, setInterestRateLabel] = useState("");
    const [dataProgram, setDataProgram] = useState([]);
    const statusCode = dataUserCode?.userCodeStatus;

    const effectPartner = () => {
        onChangePartner(partnerID);
    }

    useEffect(
        effectPartner,
        [partnerID]
    )

    const renderItemInstallment = (item, index) => {
        const { partnerInstallmentID, logo, deactived, partnerInstallmentName, warning } = item;

        const checkPartnerStatus = () => {
            if (warning) {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    warning,
                    [
                        { text: translate('common.btn_skip'), style: "default" },
                        { text: "Ok", style: "default", onPress: onPressInstallmentItem }
                    ]
                );
            } else {
                onPressInstallmentItem();
            }
        };


        const onPressInstallmentItem = () => {
            if (deactived) {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    translate('detail.deactivated_installment_partner_notice', { partnerInstallmentName })
                )
            }
            else {
                if (partnerID != partnerInstallmentID) {
                    setPartnerID(partnerInstallmentID);
                    setPartnerLogo(logo);
                    setInterestRateLabel("");
                    setDataProgram([]);
                }
            }
        }
        return (
            <TouchableOpacity
                style={{
                    borderRadius: 2,
                    shadowOffset: {
                        width: 0,
                        height: 1,
                    },
                    shadowOpacity: 0.25,
                    shadowRadius: 3.84,
                    elevation: 2,
                    flex: 0.5,
                    justifyContent: "center",
                    alignItems: "center",
                    height: 50,
                    margin: 5,
                    backgroundColor: COLORS.btnFFFFFF,
                    borderWidth: (partnerID == partnerInstallmentID) ? 2 : 0,
                    borderColor: COLORS.bd2FB47C,
                }}
                onPress={checkPartnerStatus}
                activeOpacity={0.8}
                disabled={stateProgram.isFetching}
            >
                <View
                    style={{
                        width: 100,
                        justifyContent: "center",
                        alignItems: "center",
                    }}
                >
                    {/* <ImageURI
                        uri={API_LOGO_PARTNER_INSTALLMENT + logo}
                        style={{
                            width: 100,
                            height: 40,
                        }}
                        resizeMode={"contain"}
                    /> */}
                    {
                        !!logo && <Image
                            source={{ uri: logo }}
                            style={{
                                width: 100,
                                height: 40,
                            }}
                            resizeMode={"contain"}
                        />
                    }

                </View>
            </TouchableOpacity>
        );
    }

    const onSelectProgram = (programInfo) => () => {
        onChangeProgram(programInfo, partnerLogo);
    }

    const renderItemProgram = (item, index) => {
        const { saleProgramID, saleProgramName } = item;
        return (
            <TouchableOpacity
                style={{
                    justifyContent: "center",
                    padding: 10,
                    backgroundColor: COLORS.btnFFFFFF,
                    flexWrap: "wrap",
                    width: constants.width - 20,
                    borderTopWidth: index != 0 ? StyleSheet.hairlineWidth : 0
                }}
                onPress={onSelectProgram(item)}
            >
                <MyText style={{
                    width: "100%",
                    color: COLORS.txt333333
                }}
                    text={`${saleProgramID} - ${saleProgramName}`}
                />
            </TouchableOpacity>
        );
    }

    return (
        <KModal
            isVisible={isVisible}
            deviceWidth={constants.width}
            deviceHeight={constants.height}
            animationIn="slideInUp"
            animationOut="slideOutDown"
            useNativeDriver={true}
            hideModalContentWhileAnimating={true}
            style={{ margin: 0 }}
            onModalWillHide={() => {
                setPartnerID(undefined);
                setPartnerLogo("");
                setInterestRateLabel("");
                setDataProgram([]);
            }}
        >
            <View style={{
                flex: 1
            }}>
                <ModalHeader
                    onClose={hideModal}
                    title={translate('detail.instalment_program_uppercase')}
                />
                <SafeAreaView style={{
                    flex: 1,
                }}>
                    <View style={{
                        flex: 1,
                        padding: 10,
                        backgroundColor: COLORS.bgFFFFFF,
                    }}>
                        <BaseLoading
                            isLoading={statePartner.isFetching}
                            isEmpty={statePartner.isEmpty}
                            textLoadingError={statePartner.description}
                            isError={statePartner.isError}
                            onPressTryAgains={onPartnerTryAgains}
                            content={
                                <>
                                    <View style={{
                                        width: constants.width - 20,
                                    }}>
                                        <FlatList
                                            data={dataInstallment}
                                            renderItem={({ item, index }) => renderItemInstallment(item, index)}
                                            keyExtractor={(item, index) => index.toString()}
                                            numColumns={2}
                                            ListFooterComponent={
                                                <BaseLoading
                                                    isLoading={stateProgram.isFetching}
                                                    isEmpty={stateProgram.isEmpty}
                                                    textLoadingError={stateProgram.description}
                                                    isError={stateProgram.isError}
                                                    onPressTryAgains={onProgramTryAgains}
                                                    content={
                                                        <View style={{
                                                            width: constants.width - 20,
                                                        }}>
                                                            {
                                                                statusCode ?
                                                                    <View style={{
                                                                        marginTop: 10,
                                                                        flexDirection: 'row',
                                                                        alignItems: 'center'
                                                                    }}>
                                                                        <Icon
                                                                            iconSet={'MaterialCommunityIcons'}
                                                                            name={'information-outline'}
                                                                            color={COLORS.txt147EFB}
                                                                            size={20}
                                                                        />
                                                                        <View style={{
                                                                            marginLeft: 5
                                                                        }}>
                                                                            <MyText
                                                                                text={statusCode ? statusCode : "Không tìm thấy trạng thái từ đối tác trả góp"}
                                                                                style={{
                                                                                    color: COLORS.txt147EFB,
                                                                                    fontStyle: 'italic',
                                                                                    width: constants.width - 30,
                                                                                }}
                                                                            />
                                                                        </View>
                                                                    </View>
                                                                    :
                                                                    null
                                                            }
                                                            {
                                                                (partnerID != undefined) &&
                                                                <View style={{
                                                                    width: constants.width - 20,
                                                                    marginVertical: 10
                                                                }}>
                                                                    <RadioInterestRate
                                                                        data={dataInterestRate}
                                                                        interestRateLabelSelect={interestRateLabel}
                                                                        onSelectInterestRate={(itemInterestRate) => {
                                                                            const { interestRateLabel, salePrograms } = itemInterestRate;
                                                                            setInterestRateLabel(interestRateLabel);
                                                                            setDataProgram(salePrograms);
                                                                        }}
                                                                    />
                                                                </View>
                                                            }
                                                            {
                                                                !!interestRateLabel &&
                                                                <View style={{
                                                                    width: constants.width - 20,
                                                                    borderRadius: 4,
                                                                    shadowColor: COLORS.sd000000,
                                                                    shadowOffset: {
                                                                        width: 0,
                                                                        height: 1,
                                                                    },
                                                                    shadowOpacity: 0.25,
                                                                    shadowRadius: 3.84,
                                                                    elevation: 2,
                                                                    borderWidth: StyleSheet.hairlineWidth,
                                                                    borderColor: COLORS.bdE4E4E4,
                                                                    maxHeight: "90%",
                                                                }}>
                                                                    <FlatList
                                                                        data={dataProgram}
                                                                        renderItem={({ item, index }) => renderItemProgram(item, index)}
                                                                        keyExtractor={(item, index) => index.toString()}
                                                                    />
                                                                </View>
                                                            }
                                                        </View>
                                                    }
                                                />
                                            }
                                        />
                                    </View>

                                </>
                            }
                        />
                    </View>
                </SafeAreaView>
            </View>
        </KModal>
    );
}

export default ModalInstallment;