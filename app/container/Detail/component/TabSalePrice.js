import React from 'react';
import { StyleSheet, View, TouchableOpacity, Animated } from 'react-native';
import { Icon, MyText } from '@components';
import { constants } from '@constants';
import { COLORS } from '@styles';
import { helper } from '@common';

const TabSalePrice = ({
    listPrice,
    onChangeTab,
    status,
    onchangeSheet,
    styles
}) => {
    const onPressTab = (type, index) => {
        if (status != type) {
            onChangeTab(listPrice[index]);
        }
    };

    const onShowInfoPackage = () => {
        onchangeSheet();
    };

    return (
        <Animated.View
            style={[
                styles,
                {
                    width: constants.width - 8,
                    flexDirection: 'row',
                    flexWrap: 'wrap',
                    marginBottom: 5,
                    backgroundColor: COLORS.bgFFFFFF,
                    alignSelf: 'center',
                    justifyContent: 'center'
                }
            ]}>
            {listPrice.map((item, index) => {
                const { SalePricePolicyName, TotalAmount, packageStatus, PromotionPriceVAT } =
                    item;
                const price = TotalAmount - PromotionPriceVAT
                const isCheck = status === packageStatus;
                const shadow = isCheck
                    ? {
                        shadowColor: '#000',
                        shadowOffset: {
                            width: 0,
                            height: 3
                        },
                        shadowOpacity: 0.27,
                        shadowRadius: 1.65,
                        elevation: 6
                    }
                    : null;
                const priceName = SalePricePolicyName.length > 20 ? `${SalePricePolicyName.slice(0, 20)}... ` : SalePricePolicyName
                return (
                    <Animated.View
                        key={index}
                        style={{ width: (constants.width - 8) / 3 }}>
                        <Animated.View
                            // eslint-disable-next-line react-native/no-color-literals
                            style={{
                                backgroundColor: COLORS.btnFFFFFF,
                                borderWidth: StyleSheet.hairlineWidth + 1.1,
                                borderRadius: 15,
                                borderColor: isCheck
                                    ? '#EC2029'
                                    : COLORS.btnE4E4E4,
                                paddingVertical: 4,
                                marginBottom: 2,
                                marginHorizontal: 2,
                                ...shadow
                            }}>
                            <TouchableOpacity
                                style={{
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    marginHorizontal: 10
                                }}
                                activeOpacity={0.5}
                                onPress={() => {
                                    onPressTab(packageStatus, index);
                                }}>
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                    }}>
                                    <MyText
                                        numberOfLines={2}
                                        style={{
                                            fontWeight: isCheck
                                                ? 'bold'
                                                : 'normal',
                                            color: COLORS.txt000000
                                        }}
                                        addSize={-3.5}
                                        text={SalePricePolicyName}
                                    />
                                </View>

                                <MyText
                                    // eslint-disable-next-line react-native/no-color-literals
                                    style={{
                                        fontWeight: 'bold',
                                        color: isCheck ? '#EC2029' : '#5fc2f5'
                                    }}
                                    addSize={3}
                                    text={helper.convertNum(price)}
                                />
                            </TouchableOpacity>
                            {isCheck && (
                                <TouchableOpacity
                                    onPress={onShowInfoPackage}
                                    // eslint-disable-next-line react-native/no-color-literals
                                    style={{
                                        height: 23,
                                        width: 23,
                                        borderRadius: 23,
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        backgroundColor: 'white',
                                        position: 'absolute', // Here is the trick
                                        top: -7,
                                        right: -5,
                                        alignSelf: 'flex-end'
                                    }}>
                                    <Icon
                                        iconSet="MaterialIcons"
                                        name="info-outline"
                                        color={COLORS.ic288AD6}
                                        size={23}
                                    />
                                </TouchableOpacity>
                            )}
                        </Animated.View>
                    </Animated.View>
                );
            })}
        </Animated.View>
    );
};

export default TabSalePrice;
