/**
 * Sample React Native App
 *
 *
 * @format
 * @flow strict-local
 */

import React, { useState } from 'react';
import {
    ScrollView,
    View,
    Linking,
    Alert
} from 'react-native';
import { useSelector } from 'react-redux';
import { constants } from "@constants";
import { helper } from "@common";
import { showBlockUI, hideBlockUI } from "@components";
import ProductTitle from "./component/ProductTitle";
import ProductQuantity from "./component/ProductQuantity";
import CheckNewStock from "./component/CheckNewStock";
import CheckNewStockUnit from "./component/CheckNewStockUnit";
import CheckColorProduct from "./component/CheckColorProduct";
import ProductInstallment from "../component/ProductInstallment";
import ProductOtherInfo from "./component/ProductOtherInfo";
import { translate } from '@translate';
import MedicalOtherInfo from './component/MedicalOtherInfo';
import TabSalePrice from '../../component/TabSalePrice';
import ModalProductInformation from '../../../Detail/component/Modal/ModalProductInformation';
import ModalInstallmentConsultation from '../../component/Modal/ModalInstallmentConsultation';

const { PARTNER_ID } = constants

const NewStock = ({
    imageUrl,
    dataNewStock,
    quantity,
    onChangeQuantity,
    productInfo,
    onChangeProduct,
    isImei,
    saleProgramInfo,
    onShowInstallment,
    getFifoInfo,
    getFeatureInfo,
    getConfigInfo,
    isFavorite,
    onFavorite,
    removeInstallment,
    isCartEmpty,
    retailPriceVAT,
    actionDetail,
    isLoyalty,
    isFiFO,
    getWarrantyInfo,
    getUsageGuide,
    onRetryGetDiscount,
    onShowModalProductOtherInfo,
    disableQuantity,
    statusPrice,
    listPrice,
    onChangeTab,
    onchangeSheet,
    packagePriceSheetRef,
    dataRewardInstallment,
    handleInstallmentConsultantSheet,
}) => {
    const { productID, itemID, productColorID, purposesApplyonPos, salePriceVAT } = productInfo;
    const dataSize = dataNewStock.find(ele => ele.productColorID == productColorID);
    const isProductSize = (purposesApplyonPos && !helper.IsEmptyObject(dataSize));
    const { brandID, storeID, userName } = useSelector(state => state.userReducer)
    const isAnKhang = (`${brandID}` == '8');
    const [isVisibleModal, setIsVisibleModal] = useState(false);

    const commonProps = {
        isVisible: isVisibleModal,
        hideModal: () => setIsVisibleModal(false),
        storeID,
        userName,
        saleProgramInfo,
        productID
    };


    const getUrlInfo = (field, contentEmpty) => () => {
        showBlockUI();
        actionDetail.getConsultantInfo(productInfo).then(data => {
            if (helper.IsNonEmptyString(data[field])) {
                hideBlockUI();
                linkingUrl(data[field])
            }
            else {
                Alert.alert("", contentEmpty, [{
                    text: "OK",
                    onPress: hideBlockUI
                }])
            }
        }).catch(msgError => {
            Alert.alert("", msgError, [{
                text: "OK",
                onPress: hideBlockUI
            }])
        })
    }

    const handleModal = () => {
        if (isNewAdvice(saleProgramInfo.partnerID)) {
            if ((saleProgramInfo.partnerID) == PARTNER_ID.TPBanhEVO) {
                Linking.openURL('https://www.goevo.vn/emi-tools/mwg');
                return;
            }
            return handleInstallmentConsultantSheet();
        }
        setIsVisibleModal(true);
    }

    return (
        <View>
            <ScrollView
                contentContainerStyle={{
                    flexGrow: 1,
                }}>
                <View style={{
                    width: constants.width,
                    justifyContent: "center",
                    alignItems: "center",
                    marginTop: 10,
                    paddingBottom: 54
                }}>
                    <TabSalePrice
                        status={statusPrice}
                        listPrice={listPrice}
                        onChangeTab={onChangeTab}
                        onchangeSheet={onchangeSheet}
                    />
                    <ProductTitle
                        salePriceProduct={retailPriceVAT}
                        productInfo={productInfo}
                        isFavorite={isFavorite}
                        onFavorite={onFavorite}
                        actionDetail={actionDetail}
                        isLoyalty={isLoyalty}
                        onRetryGetDiscount={onRetryGetDiscount}
                        saleProgramInfo={saleProgramInfo}
                    />

                    <View style={{
                        width: constants.width,
                        padding: 8,
                        flexDirection: "row",
                    }}>
                        <ProductQuantity
                            imageUrl={imageUrl}
                            quantity={quantity}
                            onChangeQuantity={onChangeQuantity}
                            disabled={disableQuantity}
                        />
                        {
                            isProductSize
                                ? <CheckColorProduct
                                    dataNewStock={dataNewStock}
                                    productInfo={productInfo}
                                    onChangeProduct={onChangeProduct}
                                    disabled={isImei}
                                    getFifoInfo={getFifoInfo}
                                    dataSize={dataSize}
                                />
                                : (helper.IsNonEmptyString(itemID)
                                    ? <CheckNewStockUnit
                                        dataNewStock={dataNewStock}
                                        productIdSelect={productID}
                                        onChangeProduct={onChangeProduct}
                                        disabled={isImei}
                                        getFifoInfo={getFifoInfo}
                                        isFiFO={isFiFO}
                                        isAnKhang={isAnKhang}
                                    />
                                    : <CheckNewStock
                                        dataNewStock={dataNewStock}
                                        productIdSelect={productID}
                                        onChangeProduct={onChangeProduct}
                                        disabled={isImei}
                                        getFifoInfo={getFifoInfo}
                                        isFiFO={isFiFO}
                                    />)
                        }
                    </View>
                    {
                        !isAnKhang &&
                        <ProductInstallment
                            saleProgramInfo={saleProgramInfo}
                            onShowInstallment={onShowInstallment}
                            onDelete={removeInstallment}
                            isCartEmpty={isCartEmpty}
                            onPressProductInformation={handleModal}
                            dataRewardInstallment={dataRewardInstallment}
                            retailPriceVAT={retailPriceVAT}
                        />
                    }
                    {
                        isAnKhang
                            ? <MedicalOtherInfo onShowModalProductOtherInfo={onShowModalProductOtherInfo} onPressUsage={getUsageGuide} />
                            : <ProductOtherInfo
                                onPressFeature={getFeatureInfo}
                                onPressConfig={getConfigInfo}
                                onConsultancyInstallation={getUrlInfo(
                                    "productInstallationConsultancyUrl",
                                    translate('detail.no_installment_support_information')
                                )}
                                onConsultancyInstallment={getUrlInfo(
                                    "productInstallmentUrl",
                                    translate('detail.no_instalment_support_information')
                                )}
                                onSearchGuaranteePolicy={getWarrantyInfo}
                                onShowModalProductOtherInfo={onShowModalProductOtherInfo}
                            />
                    }
                    {
                        isVisibleModal && (
                            saleProgramInfo.partnerID == PARTNER_ID.SMART_POS ? (
                                <ModalProductInformation
                                    {...commonProps}
                                />
                            ) : (
                                <ModalInstallmentConsultation
                                    {...commonProps}
                                    salePriceVAT={salePriceVAT}
                                />
                            )
                        )
                    }


                </View>
            </ScrollView>
        </View>
    );
}

export default NewStock;

const linkingUrl = (url) => {
    Linking.canOpenURL(url).then(supported => {
        if (supported) {
            Linking.openURL(url);
        }
    });
}


const partners = new Set([
    String(PARTNER_ID.HOME_PAY_LATER),
    String(PARTNER_ID.KREDIVO),
    String(PARTNER_ID.CAKE),
    String(PARTNER_ID.TPBanhEVO),
]);

const isNewAdvice = (partnerInstallmentID) => {
    return partners.has(String(partnerInstallmentID));
};