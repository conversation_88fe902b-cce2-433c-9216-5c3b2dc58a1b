import React, { useState, useMemo, useEffect } from 'react';
import {
    View,
    TouchableOpacity,
    TouchableHighlight,
    ActivityIndicator,
    Alert
} from 'react-native';
import { useSelector } from 'react-redux';
import { constants } from '@constants';
import { Icon, MyText } from '@components';
import Tooltip from 'react-native-walkthrough-tooltip';
import { helper, dateHelper } from '@common';
import { translate } from '@translate';
import { COLORS } from '@styles';

const ProductTitle = ({
    salePriceProduct,
    productInfo,
    isFavorite,
    onFavorite,
    actionDetail,
    isLoyalty,
    onRetryGetDiscount,
    saleProgramInfo
}) => {
    const {
        productName,
        statusName,
        totalReward,
        productID,
        standardPoint,
        toDate,
        description,
        imei,
        inventoryStatusName,
        inventoryStatusID,
        productLotID
    } = productInfo;
    const onCheckProduct = () => {
        onFavorite(productInfo);
    };
    const [isLoadingPoint, setIsLoadingPoint] = useState(false);
    const [pointLoyalty, setPointLoyalty] = useState(0);
    const {
        dataDiscount: { discountPriceVAT, productID: discountProductID },
        stateDiscount: { isFetching, isError, description: discountDescription },
        packageServices
    } = useSelector((state) => state.detailReducer);

    const { standardPointById } = useSelector(
        (state) => state._pharmacyReducer
    );

    const id = `${productID}_${inventoryStatusID}`;
    const standardPointValue = standardPointById[id]?.value || standardPoint;
    const toDateValue = standardPointById[id]?.toDate || toDate;

    const finalProductSalePrice = useMemo(() => {
        const price = salePriceProduct - discountPriceVAT;
        const positivePrice = price > 0 ? price : 0;

        return isFetching && discountProductID !== productID
            ? salePriceProduct
            : positivePrice;
    }, [discountPriceVAT, salePriceProduct, isFetching]);

    useEffect(() => {
        if (isError) {
            Alert.alert(translate('common.notification'), discountDescription, [
                {
                    text: translate('common.btn_cancel')
                },
                {
                    text: translate('common.btn_notify_try_again'),
                    onPress: onRetryGetDiscount
                }
            ]);
        }
    }, [discountDescription, isError]);

    // useEffect(() => {
    //     setPointLoyalty(0);
    //     setIsLoadingPoint(true)
    //     let params = {
    //         OUTPUTPRODUCTDETAILS: [
    //             {
    //                 PRODUCTID: productInfo.productID,
    //                 OUTPUTTYPEID: productInfo.outputTypeID,
    //                 SUBGROUPID: productInfo.subGroupID,
    //                 //MAINGROUPID: productInfo.mainGroupID,
    //                 //BRANDID: productInfo.brandID,
    //                 //CUSTOMERLEVELID: 1,
    //                 //POINT: 0,
    //                 PRICE: productInfo.salePriceVAT,
    //                 QUANTITY: 1,
    //                 INVENTORYSTATUSID: productInfo.inventoryStatusID,
    //             }
    //         ]
    //     };
    //     actionDetail.getProductLoyaltyPoint(params).then(data => {
    //         const { POINTPRODUCTDETAILS } = data;
    //         if (helper.IsNonEmptyArray(POINTPRODUCTDETAILS)) {
    //             setPointLoyalty(POINTPRODUCTDETAILS[0].POINT)
    //         }
    //         setIsLoadingPoint(false)
    //     }).catch(msgError => {
    //         setPointLoyalty(0);
    //         setIsLoadingPoint(false)
    //     })
    // }, [productInfo])

    return (
        <View>
            <MyText
                style={{
                    color: COLORS.txt0099E5,
                    fontWeight: 'bold',
                    width: constants.width - 20,
                    textAlign: 'center'
                }}
                addSize={4}
                text={productName}>
                {helper.IsNonEmptyString(productLotID) && (
                    <MyText
                        style={{
                            color: COLORS.txt333333,
                            marginTop: 4,
                            fontWeight: 'bold'
                        }}
                        text={`\n(Barcode: ${productLotID})`}
                    />
                )}
            </MyText>
            {!!imei && (
                <View
                    style={{
                        width: constants.width - 20,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingTop: 6
                    }}>
                    <MyText
                        style={{
                            color: COLORS.txt333333,
                            fontWeight: 'normal'
                        }}
                        addSize={-1.5}
                        text={translate('detail.emei')}>
                        <MyText
                            style={{
                                color: COLORS.txtEA1D5D,
                                fontWeight: 'bold'
                            }}
                            text={imei}
                        />
                    </MyText>
                    <MyText
                        style={{
                            color: COLORS.txt410093,
                            fontWeight: 'bold'
                        }}
                        text={` (${inventoryStatusName})`}
                    />
                </View>
            )}

            <View
                style={{
                    width: constants.width - 20,
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    paddingVertical: 8
                }}>
                <TooltipWrapper
                    placement="bottom"
                    content={
                        <MyText
                            style={{ color: COLORS.txtFFFFFF }}
                            text={
                                description ||
                                translate('detail.no_description_information')
                            }
                        />
                    }
                    wrapper={
                        <View
                            style={{
                                flexDirection: 'row'
                            }}>
                            <MyText
                                style={{ color: COLORS.txtFBB034 }}
                                text={statusName}
                            />
                            <Icon
                                iconSet="Ionicons"
                                name="information-circle"
                                size={14}
                                color={COLORS.icFBB034}
                            />
                        </View>
                    }
                />
                {totalReward > 0 && (
                    <TooltipWrapper
                        placement="bottom"
                        content={
                            <MyText
                                style={{ color: COLORS.txtFFFFFF }}
                                text={translate('detail.total_reward')}
                            />
                        }
                        wrapper={
                            <View
                                style={{
                                    flexDirection: 'row'
                                }}>
                                <MyText
                                    style={{ color: COLORS.txt279B37 }}
                                    text={helper.convertNum(totalReward, false)}
                                />
                            </View>
                        }
                    />
                )}
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center'
                    }}
                // activeOpacity={0.8}
                // onPress={onCheckProduct}
                >
                    <MyText
                        style={{
                            color: COLORS.txt0000FF,
                            fontWeight: 'bold'
                        }}
                        text={productID}
                        onPress={onCheckProduct}
                    />
                    <Icon
                        iconSet="MaterialCommunityIcons"
                        name="star"
                        size={22}
                        color={isFavorite ? COLORS.icFFDD00 : COLORS.ic9F9FA3}
                    />
                </View>
            </View>

            <View
                style={{
                    width: constants.width - 20,
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between'
                }}>
                <View style={{ flexDirection: 'row' }}>
                    {
                        (helper.IsEmptyObject(packageServices) || helper.IsEmptyArray(packageServices[`${productID}_${saleProgramInfo.saleProgramID}`])) &&
                        <MyText
                            style={{ color: COLORS.txt333333 }}
                            text={translate('common.price')}>
                            <MyText
                                style={{ color: COLORS.txtFF0000 }}
                                text={helper.convertNum(finalProductSalePrice)}
                                addSize={4}
                            />
                        </MyText>
                    }
                    <DiscountInfo
                        isError={isError}
                        isFetching={isFetching}
                        discountPriceVAT={discountPriceVAT}
                        salePriceProduct={salePriceProduct}
                        onRetry={onRetryGetDiscount}
                    />
                </View>
                {standardPointValue > 0 && (
                    <TooltipWrapper
                        placement={pointLoyalty > 0 ? 'bottom' : 'left'}
                        content={
                            <MyText
                                style={{ color: COLORS.txtFFFFFF }}
                                text={translate('detail.standard_point')}
                            />
                        }
                        wrapper={
                            <View
                                style={{
                                    flexDirection: 'row',
                                    alignItems: 'center'
                                }}>
                                <MyText
                                    style={{ color: COLORS.txtBE0027 }}
                                    text={helper.convertNum(
                                        standardPointValue,
                                        false
                                    )}>
                                    <MyText
                                        style={{ color: COLORS.txtBE0027 }}
                                        text={`(${dateHelper.formatStrDateDDMM(
                                            toDateValue
                                        )})`}
                                        addSize={-2}
                                    />
                                </MyText>
                            </View>
                        }
                    />
                )}
                {isLoyalty && (pointLoyalty > 0 || isLoadingPoint) && (
                    <TooltipWrapper
                        placement="bottom"
                        content={
                            <MyText
                                style={{ color: COLORS.txtFFFFFF }}
                                text={translate('detail.close_member_point')}
                            />
                        }
                        wrapper={
                            <View
                                style={{
                                    flexDirection: 'row'
                                }}>
                                <Icon
                                    iconSet="MaterialCommunityIcons"
                                    name="gift"
                                    size={14}
                                    color={COLORS.icFF00BF}
                                    style={{
                                        marginTop: 2
                                    }}
                                />
                                {isLoadingPoint ? (
                                    <ActivityIndicator
                                        color={COLORS.txtFF00BF}
                                        style={{
                                            marginLeft: 4
                                        }}
                                    />
                                ) : (
                                    <MyText
                                        style={{
                                            color: COLORS.txtFF00BF,
                                            marginLeft: 2
                                        }}
                                        text={helper.convertNum(
                                            pointLoyalty,
                                            false
                                        )}
                                    />
                                )}
                            </View>
                        }
                    />
                )}
            </View>
        </View>
    );
};

export default ProductTitle;

export const TooltipWrapper = ({ content, placement, wrapper }) => {
    const [isVisible, setIsVisible] = useState(false);

    const onShow = () => {
        setIsVisible(true);
    };

    const onClose = () => {
        setIsVisible(false);
    };

    return (
        <Tooltip
            isVisible={isVisible}
            tooltipStyle={{
                shadowColor: COLORS.sd2FB47C,
                shadowOpacity: 0
            }}
            contentStyle={{
                flex: 1,
                backgroundColor: COLORS.bg2FB47C
            }}
            supportedOrientations={['portrait']}
            content={content}
            placement={placement}
            onClose={onClose}>
            <TouchableHighlight onPress={onShow} underlayColor="transparent">
                {wrapper}
            </TouchableHighlight>
        </Tooltip>
    );
};

const DiscountInfo = ({
    isError,
    isFetching,
    discountPriceVAT,
    salePriceProduct,
    onRetry
}) => {
    const shouldRenderPrice = discountPriceVAT !== 0 && !isFetching && !isError;
    return (
        <View
            style={{
                alignSelf: 'flex-end',
                marginLeft: 5
            }}>
            {isFetching && <ActivityIndicator color={COLORS.txtFF00BF} />}
            {shouldRenderPrice && (
                <MyText
                    style={{
                        color: COLORS.txtFF0000,
                        textDecorationLine: 'line-through',
                        opacity: 0.7
                    }}
                    text={helper.convertNum(salePriceProduct)}
                />
            )}
            {isError && (
                <TouchableOpacity onPress={onRetry}>
                    <Icon
                        iconSet="Ionicons"
                        name="reload-outline"
                        size={16}
                        color={COLORS.txtFF0000}
                    />
                </TouchableOpacity>
            )}
        </View>
    );
};
