/**
 * Sample React Native App
 *
 *
 * @format
 * @flow strict-local
 */

import React, { Component } from 'react';
import {
    View,
    Alert,
    BackHandler,
    StyleSheet,
    Platform,
    Animated,
    TouchableOpacity,
    KeyboardAvoidingView
} from 'react-native';
import {
    IndicatorViewPager,
    PagerTitleIndicator
} from 'react-native-best-viewpager';
import { FloatingAction } from "react-native-floating-action";
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import Toast from 'react-native-toast-message';
import { helper } from "@common";
import { constants, ENUM, CONFIG } from "@constants";
import {
    BaseLoading,
    showBlockUI,
    hideBlockUI,
    MyText,
    Button
} from "@components";
import Promotion from "./Promotion/index";
import SalePromtion from "./SalePromotion/index";
import TabInventoryStatus from "./ProductInfo/component/TabInventoryStatus";
import TabDeliveryType from "./DeliveryInfo/component/TabDeliveryType";
import TabReceiveType from "./DeliveryInfo/component/TabReceiveType";
import ModalInstallment from "./component/Modal/ModalInstallment";
import ModalFIFO from "./component/Modal/ModalFIFO";
import ModalFeature from "./component/Modal/ModalFeature";
import ModalConfig from "./component/Modal/ModalConfig";
import ModalLockInfo from "./component/Modal/ModalLockInfo";
import ModalProductOtherInfo from './component/Modal/ModalProductOtherInfo';
import NewStock from "./ProductInfo/NewStock/index";
import SecondStock from "./ProductInfo/SecondStock/index";
import ExhibitStock from "./ProductInfo/ExhibitStock/index";
import DeliveryAtStore from "./DeliveryInfo/DeliveryAtStore/index";
import DeliveryAtOtherStore from "./DeliveryInfo/DeliveryAtOtherStore/index";
import DeliveryAtHome from "./DeliveryInfo/DeliveryAtHome/index";
import SimStock from "./SimInfo/index";
import PromotionDelivery from "./PromotionDelivery/index";
import DeliveryDefault from './DeliveryInfo/DeliveryDefault/index';
import * as actionDetailCreator from "./action";
import * as actionLocationCreator from "../Location/action";
import * as actionCartCreator from "../ShoppingCart/action";
import * as actionPouchCreator from "../PouchRedux/action";
import * as actionNewPharmacyCreator from '../AnKhangNew/action';
import * as loyaltyActionCreator from '../Loyalty/action';
import { translate } from '@translate';
import ModalPreOrderLock from "./component/Modal/ModalPreOrderLock";
import { COLORS } from "@styles";
import ModalWarranty from './component/Modal/ModalWarranty';
import ButtonAddToCart from './DeliveryInfo/component/ButtonAddToCart';
import { TIMEOUT_WOW_POINTS } from '../Loyalty/constants';
import _ from 'lodash';
import TabSalePrice from './component/TabSalePrice';
import { InstallmentConsultantSheet, PackagePriceSheet } from './Sheets';
import { PRODUCT_STATUS_ID, PROMOTION_CONTENT, TYPE_OF_CARE_PACKAGE } from './constants';
import { Indicator } from '../AnKhangNew';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import CarouselCards from './component/ProductSuggest/CarouselCard';
import ModalFees from './component/Modal/ModalFees';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
const { AN_KHANG_PHARMACY, SALE, PRE_ORDER } = ENUM.SALE_SCENARIO_TYPE;
const { PARTNER_ID } = constants
const AnimatedPagerView = Animated.createAnimatedComponent(IndicatorViewPager);

class DetailScreen extends Component {
    constructor() {
        super();
        this.state = {
            setKeyPromotionSelected: new Set(),
            setGroupIDCondition: new Set(),
            setGroupIDPhoneValidate: new Set(),
            phoneValidate: "",
            isApplyPhone: false,

            inventoryStatusID: 1,
            quantity: 1,
            quantityInStock: 1,

            productInfo: {},
            productSecondInfo: {},
            productExhibitInfo: {},

            packagesTypeId: 0,
            retailPriceVAT: 0,

            isVisibleInstallment: false,
            saleProgramInfo: {
                logo: "",
                partnerID: undefined,
                saleProgramID: 0,
                saleProgramName: "",
                isSpecial: false
            },
            isHasSaleProgram: false,

            deliveryType: 1,
            receiveType: 1,

            outputStoreID: 0,
            storeRequests: [],

            expandPromotion: [],
            expandPromotionDelivery: [],

            isVisibleFIFO: false,
            fifoProduct: {},
            isVisibleFeature: false,
            isVisibleConfig: false,
            isVisibleWarranty: false,
            isVisibleLockInfo: false,
            defaultDelivery: {
                gender: null,
                contactPhone: "",
                contactName: "",
                contactAddress: "",
                provinceID: 0,
                districtID: 0,
                wardID: 0,
                deliveryType: 1,
                receiveType: 1
            },
            defaultReceiveInfo: {},
            isVisiblePreOrder: false,
            isVisibleProductOtherInfo: false,
            webInfo: {},
            defaultStatusPrice: '',
            defaultPackagePrice: {},
            topInset: 0,
            blockUI: false,
            listPackageService: [],
            loadingBlock: false,
            isViewPreorder: false,
            productSuggests: [],
            isFetchingSuggestProduct: false,
            defaultCustomerPhone: "",
            feePlusMoneyBO: {},
            isShowModalFees: false,
        }
        this.timeoutChangeProduct = null;
        this.timeoutChangeQuantity = null;
        this.timeoutChangeDelivery = null;
        this.timeoutChangeReceive = null;
        this.isGetSecondStock = false;
        this.isGetExhibitStock = false;
        this.expDataPromotion = {};
        this.expDataPromotionDelivery = {};
        this.defaultSaleProgram = {
            logo: "",
            partnerID: undefined,
            saleProgramID: 0,
            saleProgramName: "",
            isSpecial: false
        };
        this.FLOAT_ACTION = [
            {
                text: "Xem lock hàng pre-order",
                icon: { uri: "bt_lock_product" },
                name: "0",
                tintColor: 'transparent'
            },
            {
                text: translate('detail.view_lock_product'),
                icon: { uri: "bt_lock_product" },
                name: "1",
                tintColor: 'transparent'
            },
            {
                text: translate('detail.go_to_cart'),
                icon: { uri: "ic_cartshopping" },
                name: "2",
                tintColor: 'transparent'
            }
        ];
        this.refPageViewer = React.createRef(null);
        this.shouldCallPromotion = true;
        this.packagePriceSheetRef = React.createRef(null);
        this.animationRef = React.createRef(null);
        this.bodySummerFee = React.createRef(null)
        this.installmentConsultantSheetRef = React.createRef(null);
    }

    handleGetDiscountValue = (product = this.props.productSearch) => {
        const {
            userInfo: { storeID },
            saleScenarioTypeID,
            actionDetail
        } = this.props;
        const {
            imei,
            productID,
            inventoryStatusID,
            salePriceVAT,
            standardPriceAreaSalePrice
        } = product;
        const { quantity } = this.state;
        const isAnKhang = this.props.saleScenarioTypeID === AN_KHANG_PHARMACY;
        isAnKhang &&
            actionDetail.getDiscountPriceVAT({
                saleScenarioTypeID,
                imei,
                productID,
                inventoryStatusID,
                price: salePriceVAT,
                storeID,
                appliedQuantity: quantity,
                outputStoreID: storeID,
                deliveryTypeID: 1, // default 1 for An Khang
                storeRequests: [], // default 0 for An Khang
                saleProgramID: 0,
                cartRequest: {}, // default empty for An Khang
                pointLoyalty: 0,
                outputTypeID: 3,
                packagesTypeId: 0,
                standardPriceAreaSalePrice
            });
    };

    handleGetStandardPointById = (product = this.props.productSearch) => {
        const { saleScenarioTypeID, actionNewPharmacy, standardPointById } =
            this.props;
        const { productID, inventoryStatusID } = product;
        const isAnKhang = saleScenarioTypeID === AN_KHANG_PHARMACY;
        const id = `${productID}_${inventoryStatusID}`;

        isAnKhang &&
            standardPointById[id] === undefined &&
            actionNewPharmacy.getStandardPoint({
                productID,
                inventoryStatusID
            });
    };

    componentDidMount() {
        if (this.props.saleScenarioTypeID !== AN_KHANG_PHARMACY) {
            this.getInstallmentSearch();
        } else {
            this.onChangeProductSearch();
            this.handleGetDiscountValue();
            this.handleGetStandardPointById();
        }
        this.getDataProvince();
        this.getDataDistrict();
        //addEventListener "addEventListener"
        BackHandler.addEventListener('addEventListener', this.onBackButtonPressed);
    }

    componentDidUpdate(preProps, preState) {
        if (preProps.productSearch !== this.props.productSearch) {
            const { productSearch, userInfo: { storeID } } = this.props;
            if (!helper.IsEmptyObject(productSearch.productServicePackageBO)) {
                const deliveryInfo = {
                    gender: null,
                    contactPhone: "",
                    contactName: "",
                    contactAddress: "",
                    provinceID: 0,
                    districtID: 0,
                    wardID: 0,
                    deliveryType: 1,
                    receiveType: 1
                };
                const receiveInfo = {};
                const deliveryType = 1;
                const outputStoreID = storeID;
                this.setState({
                    defaultDelivery: deliveryInfo,
                    defaultReceiveInfo: receiveInfo,
                    deliveryType: deliveryType,
                    outputStoreID: outputStoreID,
                    receiveType: 1
                }, () => {
                    this.props.saleScenarioTypeID !== PRE_ORDER ? this.onChangeProductSearch() : this.handlePreOrder()
                });
            }
            else {
                this.props.saleScenarioTypeID !== PRE_ORDER ? this.onChangeProductSearch() : this.handlePreOrder()
            }
        }
        if (preProps.allKeyPromotion !== this.props.allKeyPromotion || preProps.promotionDelivery !== this.props.promotionDelivery) {
            const { allKeyPromotion, allGroupID, defaultKeyPromotion } = this.props;
            this.keepKeyPromotionSelected(allKeyPromotion, allGroupID, defaultKeyPromotion);
        }
        if (preProps.promotion !== this.props.promotion) {
            this.handlePromotionContent(this.state.setKeyPromotionSelected);
        }
        const { inventoryStatusID } = this.state;
        if (preState.inventoryStatusID !== inventoryStatusID) {
            const { NEW_STOCK, NEW_STOCK_DISCOUNT } = PRODUCT_STATUS_ID;
            this.setState({
                topInset:
                    inventoryStatusID === NEW_STOCK ||
                        inventoryStatusID === NEW_STOCK_DISCOUNT
                        ? 120
                        : 180
            });
        }
    }

    onBackButtonPressed = () => {
        return true;
    }

    componentWillUnmount() {
        // this.backHandler.remove();
        BackHandler.removeEventListener('hardwareBackPress', this.onBackButtonPressed);
    }

    render() {
        const {
            setKeyPromotionSelected,
            setGroupIDCondition,
            setGroupIDPhoneValidate,
            phoneValidate,
            isApplyPhone,

            inventoryStatusID,
            deliveryType,
            receiveType,
            isVisibleInstallment,
            saleProgramInfo,

            productInfo,
            productSecondInfo,
            productExhibitInfo,

            outputStoreID,
            expandPromotion,

            isVisibleFIFO,
            fifoProduct,
            isVisibleFeature,
            isVisibleConfig,
            isVisibleLockInfo,
            isVisibleWarranty,
            isHasSaleProgram,
            isVisiblePreOrder,
            quantity,
            quantityInStock,
            isVisibleProductOtherInfo,
            webInfo,
            isViewPreorder,
            isFetchingSuggestProduct,
            isShowModalFees
        } = this.state;
        const {
            searchInfo: { imei, isImeiSim, productID },
            promotion,
            salePromotion,
            statePromotion,
            stateProduct,
            stateSecond,
            userInfo: { storeID, brandID },
            partnerInstallment,
            statePartnerInstallment,
            programInstallment,
            stateProgramInstallment,
            dataFifo,
            stateFifo,
            dataWarranty,
            stateWarranty,
            dataConfig,
            stateConfig,
            dataFeature,
            stateFeature,
            dataLock,
            stateLock,
            cartRequest,
            actionDetail,
            dataPreOrderLock,
            statePreOrderLock,
            isDisableTabDelivery,
            saleScenarioTypeID,
            route: { params },
            dataShoppingCart,
            dataCheckPhoneNumberApplyPromotion,
            stateInventoryTab,
            suggestProducts,
            searchInfo,
            dataUserCode,
            productSearch: { productServicePackageBO }
        } = this.props;

        const isProductServicePackage = !helper.IsEmptyObject(productServicePackageBO)

        const isEmptyPromotion = (promotion.length == 0);
        const isEmptySalePromotion = (salePromotion.length == 0);
        const disabledPager = isViewPreorder || getValueDisabledPager(
            inventoryStatusID,
            productInfo,
            productSecondInfo,
            productExhibitInfo
        );
        const disabledTabInventory = stateSecond.isFetching;
        const productOrder = this.getProductInfoByStatus();
        const isReceiveType = (deliveryType == 1) && !imei && receiveType != 3;
        const isCartEmpty = helper.IsEmptyObject(cartRequest);
        const { IsAutoCreateEP } = cartRequest;
        const isTabDelivery = (!isImeiSim && !IsAutoCreateEP && !isProductServicePackage && saleProgramInfo.partnerID != PARTNER_ID.SAMSUNG);
        const isTabReceive = (isReceiveType && !IsAutoCreateEP && !productInfo.imei);
        const isTabStatus = !imei && (brandID != 16);
        const defaultDelivery = {
            deliveryStoreID: storeID,
            deliveryTypeID: 1,
            deliveryVehicles: 0,
            deliveryDistance: 0,
            shippingCost: 0,
            deliveryTime: '',
            deliveryProvinceID: 0,
            deliveryDistrictID: 0,
            deliveryWardID: 0,
            deliveryAddress: '',
            contactGender: true,
            contactPhone: '',
            contactName: '',
            customerNote: ''
        };
        const quantityMissing = quantity - quantityInStock;
        const defaultStoreRequests = [];
        const allowAddToCartEverywhere = this.props.saleScenarioTypeID === AN_KHANG_PHARMACY && !quantityMissing;
        // TODO: Android có vấn đề, chỉ chuyển title, k render nội dung, hard code cho IOS trước
        const initPage = params?.goToPromotion && Platform.OS === 'ios' ? 1 : 0;
        const disableTabDelivery = saleScenarioTypeID === PRE_ORDER;
        const disablePreorder = isViewPreorder && !this.shouldCallPromotion;
        const keyProductSuggest = `${productOrder.productID}_${inventoryStatusID}`

        return (
            <View style={{ flex: 1, backgroundColor: COLORS.bgFFFFFF }}>
                <BottomSheetModalProvider>
                    <ProductInfo
                        info={productOrder}
                    />
                    <AnimatedPagerView
                        ref={this.refPageViewer}
                        style={{ flex: 1, flexDirection: "column-reverse" }}
                        initialPage={initPage}
                        indicator={<PagerTitleIndicator
                            initialPage={1}
                            style={{ backgroundColor: COLORS.bg00A98F }}
                            titles={[translate('detail.products'), translate('detail.promotions'), translate('detail.attachments'), translate('detail.delivery_method')]}
                            trackScroll={true}
                            selectedBorderStyle={{
                                backgroundColor: COLORS.bgFFF000,
                                height: 2,
                                position: 'absolute',
                                bottom: 0,
                                left: 0,
                                right: 0
                            }}
                            renderTitle={(index, title, isSelected) => {
                                return (
                                    <MyText style={{
                                        color: isSelected
                                            ? COLORS.txtFFF000
                                            : (disabledPager ? COLORS.txtC0C0C0 : COLORS.txtFFFFFF)
                                        ,
                                        fontWeight: "bold"
                                    }}
                                        text={title}
                                        addSize={isSelected ? 2 : 0}
                                    />
                                )
                            }}
                            disabled={disabledPager}
                        />}
                        horizontalScroll={!disabledPager}
                        keyboardShouldPersistTaps={"always"}
                    >
                        <View style={{ flex: 1 }}
                            key={'ProductInfo'}
                        >
                            <BaseLoading
                                isLoading={stateProduct.isFetching}
                                isEmpty={stateProduct.isEmpty}
                                textLoadingError={stateProduct.description}
                                isError={stateProduct.isError}
                                onPressTryAgains={() => this.getDataSearch(false, true)}
                                content={
                                    <KeyboardAwareScrollView
                                        style={{
                                            flex: 1,
                                        }}
                                        enableResetScrollToCoords={false}
                                        keyboardShouldPersistTaps="always"
                                        bounces={false}
                                        overScrollMode="always"
                                        showsHorizontalScrollIndicator={false}
                                        showsVerticalScrollIndicator={false}
                                    >
                                        {
                                            isImeiSim ? (
                                                this.renderSimProduct()
                                            ) : (
                                                <View style={{ flex: 1 }}>
                                                    {isTabStatus && (
                                                        <TabInventoryStatus
                                                            status={inventoryStatusID}
                                                            productID={searchInfo.productIDERP}
                                                            productIDRef={searchInfo.productID}
                                                            onChangeTab={this.onChangeTabInventoryStatus}
                                                            disabled={disabledTabInventory}
                                                            getNewStockDiscount={actionDetail.getNewStockDiscount}
                                                        />
                                                    )}
                                                    {this.renderProductByStatus(inventoryStatusID, IsAutoCreateEP)}
                                                    {
                                                        suggestProducts.size > 0 && suggestProducts.has(keyProductSuggest) && !isFetchingSuggestProduct && (
                                                            <View
                                                                style={{
                                                                    overflow: 'hidden',
                                                                    paddingBottom: 5,
                                                                    borderRadius: 8,
                                                                    marginBottom: 45,
                                                                }}>
                                                                <CarouselCards productList={suggestProducts.get(keyProductSuggest)} />
                                                            </View>
                                                        )
                                                    }
                                                    {allowAddToCartEverywhere && (
                                                        <View style={{ marginBottom: 15 }}>
                                                            <ButtonAddToCart
                                                                isLoading={statePromotion.isFetching}
                                                                disabled={statePromotion.isFetching}
                                                                onAddToCart={() =>
                                                                    this.checkValidatePromotion(
                                                                        productOrder,
                                                                        defaultDelivery,
                                                                        defaultStoreRequests
                                                                    )
                                                                }
                                                            />
                                                        </View>
                                                    )}
                                                    <BlockUI
                                                        visible={this.state.blockUI}
                                                        onChangeVisible={(value) => {
                                                            this.setState({ blockUI: value });
                                                            this.packagePriceSheetRef.current?.dismiss();
                                                        }}
                                                    />
                                                    <Indicator
                                                        visible={this.state.loadingBlock}
                                                    />
                                                </View>
                                            )
                                        }
                                    </KeyboardAwareScrollView>

                                }
                            />
                            {helper.IsNonEmptyArray(
                                this.state.listPackageService
                            ) && (
                                    <PackagePriceSheet
                                        ref={this.packagePriceSheetRef}
                                        snapPoints={['99.99999%']}
                                        product={this.state.defaultPackagePrice}
                                        defaultTab={this.state.defaultStatusPrice}
                                        listPackageService={this.state.listPackageService}
                                        onGoNext={(data) => { }}
                                        getCurrentIndex={() => { }}
                                        disabled={false}
                                        topInset={this.state.topInset}
                                        onCloseSheet={() => {
                                            this.setState({ blockUI: false });
                                        }}
                                        listDetailServicePackages={this.state.defaultPackagePrice?.cus_GroupPricePolicyBO ?? []}
                                        onSelectPackage={this.onSelectPackage}
                                        handleGetPricePackage={this.handleGetPricePackage}
                                        storeID={this.props.userInfo.storeID}
                                        salePrice={this.state.listPackageService?.[0]?.TotalAmount}
                                    />

                                )}
                        </View>
                        {
                            !disablePreorder &&
                            <KeyboardAvoidingView
                                style={{
                                    flex: 1
                                }}
                                behavior={Platform.OS === 'ios' ? 'height' : null}
                            >
                                {
                                    !disabledPager &&
                                    <BaseLoading
                                        isLoading={statePromotion.isFetching}
                                        isEmpty={isEmptyPromotion}
                                        textLoadingError={
                                            statePromotion.isError
                                                ? statePromotion.description
                                                : translate('detail.no_promotion')
                                        }
                                        isError={statePromotion.isError}
                                        onPressTryAgains={this.getPromotion}
                                        content={
                                            <View style={{ flex: 1 }}>
                                                <Promotion
                                                    dataPromotion={promotion}
                                                    setKeyPromotionSelected={setKeyPromotionSelected}
                                                    updateKeyPromotionSelected={this.updateKeyPromotionSelected}
                                                    setGroupIDCondition={setGroupIDCondition}
                                                    updateGroupIDCondition={(newSetGroupIDCondition, newSetKeyPromotionSelected, newSetGroupIDPhoneValidate) => {
                                                        this.setState({
                                                            setGroupIDCondition: newSetGroupIDCondition,
                                                            setKeyPromotionSelected: newSetKeyPromotionSelected,
                                                            setGroupIDPhoneValidate: newSetGroupIDPhoneValidate
                                                        });
                                                    }}
                                                    phoneValidate={phoneValidate}
                                                    setGroupIDPhoneValidate={setGroupIDPhoneValidate}
                                                    updateGroupIDPhoneValidate={(phoneNumber, newSetGroupIDPhoneValidate, promotionId) => {
                                                        this.props.loyaltyAction.setWowPointsMessage({ promotionId });
                                                        this.checkWowPoints(phoneNumber);
                                                        this.setState({
                                                            phoneValidate: phoneNumber,
                                                            setGroupIDPhoneValidate: newSetGroupIDPhoneValidate,
                                                            defaultCustomerPhone: phoneNumber
                                                        });
                                                    }}
                                                    cancelPhoneValidate={(newSetGroupIDPhoneValidate, newSetKeyPromotionSelected) => {
                                                        this.setState({
                                                            setGroupIDPhoneValidate: newSetGroupIDPhoneValidate,
                                                            setKeyPromotionSelected: newSetKeyPromotionSelected,
                                                            defaultCustomerPhone: ""

                                                        });
                                                    }}
                                                    productInfo={{
                                                        "productID": productOrder.productID,
                                                        "storeID": outputStoreID,
                                                    }}
                                                    isFloating={true}
                                                    isApplyPhone={isApplyPhone}
                                                    productOrder={productOrder}
                                                    deliveryInfo={{
                                                        "storeID": storeID,
                                                        "outputStoreID": outputStoreID,
                                                        "deliveryTypeID": deliveryType
                                                    }}
                                                    actionDetail={actionDetail}
                                                    dataShoppingCart={dataShoppingCart}
                                                    dataCheckPhoneNumberApplyPromotion={dataCheckPhoneNumberApplyPromotion}
                                                />
                                                {allowAddToCartEverywhere && (
                                                    <View style={{ marginBottom: 15 }}>
                                                        <ButtonAddToCart
                                                            isLoading={statePromotion.isFetching}
                                                            disabled={statePromotion.isFetching}
                                                            onAddToCart={() =>
                                                                this.checkValidatePromotion(
                                                                    productOrder,
                                                                    defaultDelivery,
                                                                    defaultStoreRequests
                                                                )
                                                            }
                                                        />
                                                    </View>
                                                )}
                                            </View>
                                        }
                                    />
                                }
                            </KeyboardAvoidingView>
                        }

                        {
                            !disablePreorder && <View style={{ flex: 1 }}
                                key={'SalePromtion'}
                            >
                                {
                                    !disabledPager &&
                                    <BaseLoading
                                        isLoading={statePromotion.isFetching}
                                        isEmpty={isEmptySalePromotion}
                                        textLoadingError={
                                            statePromotion.isError
                                                ? statePromotion.description
                                                : translate('detail.no_attachments')
                                        }
                                        isError={statePromotion.isError}
                                        onPressTryAgains={this.getPromotion}
                                        content={
                                            <View style={{ flex: 1 }}>
                                                <SalePromtion
                                                    dataSalePromotion={salePromotion}
                                                    setKeyPromotionSelected={setKeyPromotionSelected}
                                                    updateKeyPromotionSelected={this.updateKeyPromotionSelected}
                                                    setGroupIDCondition={setGroupIDCondition}
                                                    updateGroupIDCondition={(newSetGroupIDCondition, newSetKeyPromotionSelected) => {
                                                        this.setState({
                                                            setGroupIDCondition: newSetGroupIDCondition,
                                                            setKeyPromotionSelected: newSetKeyPromotionSelected
                                                        }, this.removeExpandPromotion);
                                                    }}
                                                    productOrder={productOrder}
                                                    deliveryInfo={{
                                                        "storeID": storeID,
                                                        "outputStoreID": outputStoreID,
                                                        "deliveryTypeID": deliveryType
                                                    }}

                                                    expandPromotion={expandPromotion}
                                                    getExpandPromotion={this.getExpandPromotion}
                                                    phoneValidate={phoneValidate}
                                                    setGroupIDPhoneValidate={setGroupIDPhoneValidate}
                                                    updateGroupIDPhoneValidate={(phoneNumber, newSetGroupIDPhoneValidate) => {
                                                        this.setState({
                                                            phoneValidate: phoneNumber,
                                                            setGroupIDPhoneValidate: newSetGroupIDPhoneValidate,
                                                            defaultCustomerPhone: phoneNumber

                                                        });
                                                    }}
                                                    cancelPhoneValidate={(newSetGroupIDPhoneValidate, newSetKeyPromotionSelected) => {
                                                        this.setState({
                                                            setGroupIDPhoneValidate: newSetGroupIDPhoneValidate,
                                                            setKeyPromotionSelected: newSetKeyPromotionSelected,
                                                            defaultCustomerPhone: ""

                                                        }, this.removeExpandPromotion);
                                                    }}
                                                    isFloating={true}
                                                    actionDetail={actionDetail}
                                                />
                                                {allowAddToCartEverywhere && (
                                                    <View style={{ marginBottom: 15 }}>
                                                        <ButtonAddToCart
                                                            isLoading={statePromotion.isFetching}
                                                            disabled={statePromotion.isFetching}
                                                            onAddToCart={() =>
                                                                this.checkValidatePromotion(
                                                                    productOrder,
                                                                    defaultDelivery,
                                                                    defaultStoreRequests
                                                                )
                                                            }
                                                        />
                                                    </View>
                                                )}
                                            </View>
                                        }
                                    />
                                }
                            </View>
                        }

                        {
                            !disablePreorder && <View style={{ flex: 1 }}
                                key={'DeliveryInfo'}
                            >
                                {
                                    !disabledPager &&
                                    <>
                                        {
                                            isTabDelivery &&
                                            <TabDeliveryType
                                                disabled={disableTabDelivery}
                                                deliveryType={deliveryType}
                                                onChangeTab={this.onChangeTabDeliveryType}
                                                receiveType={receiveType}
                                            />
                                        }
                                        {
                                            isTabReceive &&
                                            <TabReceiveType
                                                disabled={disableTabDelivery || isDisableTabDelivery}
                                                receiveType={receiveType}
                                                onChangeTab={this.onChangeTabReceiveType}
                                            />
                                        }
                                        {this.renderDeliveryType(receiveType, productOrder, IsAutoCreateEP)}
                                    </>
                                }
                            </View>
                        }

                    </AnimatedPagerView>
                    <FloatingAction
                        actions={this.FLOAT_ACTION}
                        color={COLORS.btn597B66}
                        distanceToEdge={{
                            vertical: constants.heightBottomSafe + 10,
                            horizontal: 10
                        }}
                        overlayColor={COLORS.bg0000005}
                        dismissKeyboardOnPress={true}
                        iconWidth={18}
                        iconHeight={18}
                        buttonSize={42}
                        animated={false}
                        onPressItem={name => {
                            switch (name) {
                                case this.FLOAT_ACTION[0].name:
                                    this.moveToLockPreOrderCart(productOrder);
                                    break;
                                case this.FLOAT_ACTION[1].name:
                                    this.moveToLockCart(productOrder);
                                    break;
                                case this.FLOAT_ACTION[2].name:
                                    this.moveToShoppingCart();
                                    break;
                                default:
                                    break;
                            }
                        }}
                        key={"DetailAction"}
                    />
                    {
                        !isCartEmpty &&
                        <View
                            style={{
                                width: 10,
                                height: 10,
                                borderRadius: 5,
                                backgroundColor: COLORS.bg00AAFF,
                                position: "absolute",
                                bottom: 40,
                                right: 10
                            }}
                        />
                    }
                    {
                        isVisibleInstallment &&
                        <ModalInstallment
                            isVisible={isVisibleInstallment}
                            hideModal={() => {
                                this.setState({ isVisibleInstallment: false })
                                this.props.actionDetail.cleard_partner_installment_user_code()
                            }}
                            dataInstallment={partnerInstallment}
                            statePartner={statePartnerInstallment}
                            onPartnerTryAgains={this.getPartnerInstallment}
                            dataInterestRate={programInstallment}
                            stateProgram={stateProgramInstallment}
                            onProgramTryAgains={this.getProgramInstallment({
                                "storeID": outputStoreID,
                                "partnerInstallmentID": saleProgramInfo.partnerID,
                                "productID": productOrder.productID,
                                "salePriceVAT": productOrder.salePriceVAT,
                                "inventoryStatusID": productOrder.inventoryStatusID
                            })}
                            onChangePartner={(partnerID, logo) => {
                                if (partnerID > 0) {
                                    this.setState({
                                        saleProgramInfo: {
                                            ...saleProgramInfo,
                                            partnerID: partnerID,
                                        }
                                    }, this.getProgramInstallment({
                                        "storeID": outputStoreID,
                                        "partnerInstallmentID": partnerID,
                                        "productID": productOrder.productID,
                                        "salePriceVAT": productOrder.salePriceVAT,
                                        "inventoryStatusID": productOrder.inventoryStatusID
                                    }));
                                }
                            }}
                            onChangeProgram={(programInfo, logo) => {
                                const { NEW_STOCK } = PRODUCT_STATUS_ID;
                                const { inventoryStatusID } = this.state;
                                if (NEW_STOCK == inventoryStatusID) {
                                    this.onchangeProgramThreePrice({ programInfo, logo, productOrder })
                                }
                                else {
                                    this.setState({
                                        isVisibleInstallment: false,
                                        saleProgramInfo: {
                                            ...saleProgramInfo,
                                            partnerID: programInfo.partnerInstallmentID,
                                            saleProgramID: programInfo.saleProgramID,
                                            saleProgramName: programInfo.saleProgramName,
                                            logo: logo,
                                            isSpecial: programInfo.isSpecial
                                        }
                                    }, this.getPromotion);
                                    const isInstalment = programInfo.saleProgramID > 0;
                                    this.updateSalePriceProduct({
                                        "imei": productOrder.imei,
                                        "productID": productOrder.productID,
                                        "productIDRef": productOrder.productIDRef,
                                        "inventoryStatusID": productOrder.inventoryStatusID,
                                        "salePriceVAT": productOrder.salePriceVAT,
                                        "storeID": outputStoreID,
                                        "saleProgramID": programInfo.saleProgramID,
                                        "isInstalment": isInstalment,
                                        "isImeiSim": false
                                    });
                                }
                            }}
                            dataUserCode={dataUserCode}
                        />
                    }

                    <ModalFIFO
                        isVisible={isVisibleFIFO}
                        hideModal={() => {
                            this.setState({ isVisibleFIFO: false })
                        }}
                        dataFifo={dataFifo}
                        stateFifo={stateFifo}
                        fifoProduct={fifoProduct}
                        onFiFoTryAgains={() => this.getFiFoProduct(fifoProduct)}
                    />

                    <ModalFeature
                        isVisible={isVisibleFeature}
                        hideModal={() => {
                            this.setState({ isVisibleFeature: false })
                        }}
                        dataFeature={dataFeature}
                        stateFeature={stateFeature}
                        onFeatureTryAgains={() => this.getFeatureProduct(productOrder)}
                    />

                    <ModalWarranty
                        isVisible={isVisibleWarranty}
                        hideModal={() => {
                            this.setState({ isVisibleWarranty: false })
                        }}
                        dataWarranty={dataWarranty}
                        stateWarranty={stateWarranty}
                        onWarrantyTryAgains={() => this.getWarrantyProduct(productOrder)}
                    />

                    <ModalConfig
                        isVisible={isVisibleConfig}
                        hideModal={() => {
                            this.setState({ isVisibleConfig: false })
                        }}
                        dataConfig={dataConfig}
                        stateConfig={stateConfig}
                        onConfigTryAgains={() => this.getConfigProduct(productOrder)}
                    />

                    <ModalLockInfo
                        isVisible={isVisibleLockInfo}
                        hideModal={() => {
                            this.setState({ isVisibleLockInfo: false })
                        }}
                        dataLock={dataLock}
                        stateLock={stateLock}
                        onLockTryAgains={() => this.getLockProductInfo(productOrder)}
                    />

                    <ModalPreOrderLock
                        isVisible={isVisiblePreOrder}
                        hideModal={() => {
                            this.setState({ isVisiblePreOrder: false })
                        }}
                        productName={productInfo.productName}
                        dataPreOrderLock={dataPreOrderLock}
                        statePreOrderLock={statePreOrderLock}
                        onLockTryAgains={() => this.getPreOrderLockProductInfo(productOrder)}
                        actionDetail={actionDetail}
                    />
                    {
                        isVisibleProductOtherInfo &&
                        <ModalProductOtherInfo
                            isVisible={isVisibleProductOtherInfo}
                            hideModal={() => {
                                this.setState({ isVisibleProductOtherInfo: false });
                            }}
                            productInfo={productOrder}
                            webInfo={webInfo}
                            brandID={brandID}
                        />
                    }
                    {
                        isShowModalFees &&
                        <ModalFees
                            isShow={isShowModalFees}
                            hideModal={() => { this.setState({ isShowModalFees: false }) }}
                            feePlusMoneyBO={this.state.feePlusMoneyBO}
                            title={`Phụ phí tạm tính`}
                        />
                    }
                    <InstallmentConsultantSheet
                        bottomSheetRef={this.installmentConsultantSheetRef}
                        onChangeStatusSheet={() => { }}
                        productInfo={this.state.productInfo}
                        saleProgramInfo={this.state.saleProgramInfo}
                        quantity={this.state.quantity}
                    />
                </BottomSheetModalProvider>
            </View>
        );
    }

    onChangeTabPackagePrice = (packagePrice) => {
        const { productInfo } = this.state;
        const { packageStatus, TotalAmount } = packagePrice;
        this.setState(
            {
                defaultStatusPrice: packageStatus,
                defaultPackagePrice: packagePrice,
                productInfo: productInfo,
                retailPriceVAT: TotalAmount
            },
            this.getPromotion
        );
    };

    tabSalePriceStatus = () => {
        return (
            <View>
                <TabSalePrice
                    styles={{}}
                    status={this.state.defaultStatusPrice}
                    listPrice={this.state.listPackageService}
                    onChangeTab={(packagePrice) => {
                        this.onChangeTabPackagePrice(packagePrice);
                    }}
                    onchangeSheet={() => {
                        this.packagePriceSheetRef.current.snapToIndex(0);
                        this.setState({ blockUI: true });
                    }}
                    packagePriceSheetRef={this.packagePriceSheetRef}
                />

            </View>
        );
    };

    renderProductByStatus = (inventoryStatusID, isAutoCreateEP) => {
        const {
            quantity,
            productInfo,
            productSecondInfo,
            productExhibitInfo,
            saleProgramInfo,
            retailPriceVAT,
            outputStoreID,
            deliveryType
        } = this.state;
        const {
            searchInfo: { imageUrl, imei, isImeiSim, productID },
            dataProduct,
            secondStock,
            exhibitStock,
            stateSecond,
            stateExhibit,
            userInfo: { storeID, brandID, moduleID, languageID },
            dataConfig,
            dataFeature,
            dataFavorite,
            dataWarranty,
            cartRequest,
            actionPouch,
            actionDetail,
            stateInventoryTab,
            productSearch: { productServicePackageBO }
        } = this.props;
        const disableQuantity =
            this.props.saleScenarioTypeID === PRE_ORDER || !!imei || !helper.IsEmptyObject(productServicePackageBO);
        const { IsAutoCreateEP } = cartRequest;
        const isCartEmpty = helper.IsEmptyObject(cartRequest);
        const isFavorite = this.checkProductFavorite(dataFavorite, productInfo);
        const isLoyalty = false;
        const isFiFO = brandID != 8 && inventoryStatusID == 1;

        const handleChangeProductThreePrice = async (newProduct) => {
            const { defaultPackagePrice } = this.state
            const { saleScenarioTypeID } = this.props;
            try {
                showBlockUI()
                let bodyThreePrice = {
                    "loginStoreId": storeID,
                    "languageID": languageID,
                    "moduleID": moduleID,
                    "saleScenarioTypeID": saleScenarioTypeID,
                    "productProlicyBO": {
                        "productID": newProduct.productID,
                        "imei": newProduct.imei,
                        "inventoryStatusID": newProduct.inventoryStatusID
                    },
                    "outputStoreID": storeID,
                    "dateTime": null,
                    "saleProgramID": 0,
                    "deliveryTypeID": newProduct.deliveryTypeID ?? 1,
                    "customerInfoProlicyBO": {
                        "customerPhone": ""

                    },
                    "PricePolicyTypeID": defaultPackagePrice.PricePolicyTypeID,
                    "salePricePolicyID": defaultPackagePrice.SalePricePolicyID,
                };
                const dataPackageService = await this.props.actionDetail.getPackageService(bodyThreePrice);
                this.setState({ loadingBlock: false });
                const finalRetailPriceVAT = this.handlePackageService(dataPackageService, newProduct, saleProgramInfo)
                this.setState({
                    productInfo: newProduct,
                    quantityInStock: newProduct.quantity,
                    retailPriceVAT: finalRetailPriceVAT || newProduct.salePriceVAT,
                    saleProgramInfo: this.defaultSaleProgram,
                    outputStoreID: isAutoCreateEP ? outputStoreID : storeID,
                    quantity: 1,
                    deliveryType: IsAutoCreateEP ? deliveryType : 1,
                    receiveType: 1,
                    storeRequests: []
                }, () => { this.getSuggestProduct(newProduct.productID, newProduct.inventoryStatusID) });
                this.isGetSecondStock = false;
                this.isGetExhibitStock = false;
                if (this.timeoutChangeProduct) {
                    clearTimeout(this.timeoutChangeProduct);
                }
                this.timeoutChangeProduct = setTimeout(this.getPromotion, 400);
                this.handleGetDiscountValue(newProduct);
                this.handleGetStandardPointById(newProduct);

            } catch (error) {
                Alert.alert(
                    translate('common.notification'),
                    error.msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: 'cancel',
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: 'default',
                            onPress: () => handleChangeProductThreePrice(newProduct)
                        }
                    ]
                );
            } finally {
                hideBlockUI()
            }
        };
        switch (inventoryStatusID) {
            case 2:
                return (
                    <BaseLoading
                        isLoading={stateSecond.isFetching}
                        isEmpty={stateSecond.isEmpty}
                        textLoadingError={stateSecond.description}
                        isError={stateSecond.isError}
                        onPressTryAgains={this.getDataSecondStock}
                        content={
                            <SecondStock
                                dataSecondStock={secondStock}
                                productInfo={productSecondInfo}
                                defaultProduct={productInfo}
                                onChangeProduct={(newProduct) => {
                                    this.setState({
                                        productSecondInfo: newProduct,
                                        quantityInStock: newProduct.quantity,
                                        retailPriceVAT: newProduct.salePriceVAT,
                                        saleProgramInfo: this.defaultSaleProgram,
                                        outputStoreID: isAutoCreateEP ? outputStoreID : storeID,
                                        quantity: 1,
                                        deliveryType: IsAutoCreateEP ? deliveryType : 1,
                                        receiveType: 1,
                                        storeRequests: [],
                                    }, () => { this.getSuggestProduct(newProduct.productID, inventoryStatusID) });
                                    if (this.timeoutChangeProduct) {
                                        clearTimeout(this.timeoutChangeProduct);
                                    }
                                    this.timeoutChangeProduct = setTimeout(this.getPromotion, 400);
                                }}
                                saleProgramInfo={saleProgramInfo}
                                onShowInstallment={() => {
                                    this.setState({ isVisibleInstallment: true });
                                }}
                                removeInstallment={() => {
                                    this.setState({
                                        saleProgramInfo: {
                                            logo: "",
                                            partnerID: undefined,
                                            saleProgramID: 0,
                                            saleProgramName: "",
                                            isSpecial: false
                                        },
                                    }, this.getPromotion);
                                    this.updateSalePriceProduct({
                                        "imei": productSecondInfo.imei,
                                        "productID": productSecondInfo.productID,
                                        "productIDRef": productSecondInfo.productIDRef,
                                        "inventoryStatusID": productSecondInfo.inventoryStatusID,
                                        "salePriceVAT": productSecondInfo.salePriceVAT,
                                        "storeID": outputStoreID,
                                        "saleProgramID": 0,
                                        "isInstalment": false,
                                        "isImeiSim": false
                                    });
                                }}
                                isCartEmpty={isCartEmpty}
                                retailPriceVAT={retailPriceVAT}
                                quantity={quantity}
                                onChangeQuantity={(number) => {
                                    this.setState({
                                        quantity: number,
                                        outputStoreID: isAutoCreateEP ? outputStoreID : storeID,
                                        deliveryType: IsAutoCreateEP ? deliveryType : 1,
                                        receiveType: 1,
                                        storeRequests: []
                                    });
                                    if (number > 0) {
                                        if (this.timeoutChangeQuantity) {
                                            clearTimeout(this.timeoutChangeQuantity);
                                        }
                                        this.timeoutChangeQuantity = setTimeout(this.getPromotion, 400);
                                    }
                                }}
                            />
                        }
                    />
                );
            case 3:
                return (
                    <BaseLoading
                        isLoading={stateExhibit.isFetching}
                        isEmpty={stateExhibit.isEmpty}
                        textLoadingError={stateExhibit.description}
                        isError={stateExhibit.isError}
                        onPressTryAgains={this.getDataExhibitStock}
                        content={
                            <ExhibitStock
                                dataExhibitStock={exhibitStock}
                                productInfo={productExhibitInfo}
                                defaultProduct={productInfo}
                                onChangeProduct={(newProduct) => {
                                    const mainProduct = {
                                        "productID": newProduct.productID,
                                        "imei": newProduct.imei,
                                        "inventoryStatusID": newProduct.inventoryStatusID,
                                        "outputStoreID": outputStoreID,
                                        "outputTypeID": 3,
                                        "appliedQuantity": 1,
                                        "stockStoreID": null,
                                        "saleProgramID": 0,
                                        "deliveryTypeID": 1,
                                        "packagesTypeID": 0,
                                        "pointLoyalty": 0,
                                        "saleOrderDetailID": null
                                    };
                                    showBlockUI();
                                    this.props.actionDetail.checkImeiAllowSale({
                                        "mainProduct": mainProduct,
                                        "cartRequest": cartRequest
                                    }).then((success) => {
                                        this.setState({
                                            productExhibitInfo: newProduct,
                                            quantityInStock: newProduct.quantity,
                                            retailPriceVAT: newProduct.salePriceVAT,
                                            saleProgramInfo: this.defaultSaleProgram,
                                            outputStoreID: isAutoCreateEP ? outputStoreID : storeID,
                                            quantity: 1,
                                            deliveryType: IsAutoCreateEP ? deliveryType : 1,
                                            receiveType: 1,
                                            storeRequests: [],
                                        }, () => {
                                            hideBlockUI();
                                            this.getSuggestProduct(newProduct.productID, inventoryStatusID)
                                            this.getPromotion();
                                        });
                                        // if (this.timeoutChangeProduct) {
                                        //     clearTimeout(this.timeoutChangeProduct);
                                        // }
                                        // this.timeoutChangeProduct = setTimeout(this.getPromotion, 400);
                                    }).catch((msgError) => {
                                        Alert.alert('', msgError, [{
                                            text: 'OK',
                                            style: 'default',
                                            onPress: hideBlockUI
                                        }]);
                                    });
                                }}
                                saleProgramInfo={saleProgramInfo}
                                onShowInstallment={() => {
                                    this.setState({ isVisibleInstallment: true });
                                }}
                                removeInstallment={() => {
                                    this.setState({
                                        saleProgramInfo: {
                                            logo: "",
                                            partnerID: undefined,
                                            saleProgramID: 0,
                                            saleProgramName: "",
                                            isSpecial: false
                                        },
                                    }, this.getPromotion);
                                    this.updateSalePriceProduct({
                                        "imei": productExhibitInfo.imei,
                                        "productID": productExhibitInfo.productID,
                                        "productIDRef": productExhibitInfo.productIDRef,
                                        "inventoryStatusID": productExhibitInfo.inventoryStatusID,
                                        "salePriceVAT": productExhibitInfo.salePriceVAT,
                                        "storeID": outputStoreID,
                                        "saleProgramID": 0,
                                        "isInstalment": false,
                                        "isImeiSim": false
                                    });
                                }}
                                isCartEmpty={isCartEmpty}
                                retailPriceVAT={retailPriceVAT}
                                quantity={quantity}
                                onChangeQuantity={(number) => {
                                    this.setState({
                                        quantity: number,
                                        outputStoreID: isAutoCreateEP ? outputStoreID : storeID,
                                        deliveryType: IsAutoCreateEP ? deliveryType : 1,
                                        receiveType: 1,
                                        storeRequests: []
                                    });
                                    if (number > 0) {
                                        if (this.timeoutChangeQuantity) {
                                            clearTimeout(this.timeoutChangeQuantity);
                                        }
                                        this.timeoutChangeQuantity = setTimeout(this.getPromotion, 400);
                                    }
                                }}
                            />
                        }
                    />
                );
            default:
                return (
                    <BaseLoading
                        isLoading={stateInventoryTab.isFetching}
                        isEmpty={stateInventoryTab.isEmpty}
                        textLoadingError={stateInventoryTab.description}
                        isError={stateInventoryTab.isError}
                        onPressTryAgains={() => { this.getDataSearch(inventoryStatusID == 8) }}
                        content={
                            <NewStock
                                statusPrice={this.state.defaultStatusPrice}
                                listPrice={this.state.listPackageService}
                                onChangeTab={(packagePrice) => {
                                    this.onChangeTabPackagePrice(packagePrice);
                                }}
                                onchangeSheet={() => {
                                    this.packagePriceSheetRef.current?.present();
                                    // this.setState({ blockUI: true });
                                }}
                                imageUrl={imageUrl}
                                dataNewStock={dataProduct}
                                quantity={quantity}
                                onChangeQuantity={(number) => {
                                    this.setState({
                                        quantity: number,
                                        outputStoreID: isAutoCreateEP ? outputStoreID : storeID,
                                        deliveryType: IsAutoCreateEP ? deliveryType : 1,
                                        receiveType: 1,
                                        storeRequests: []
                                    });
                                    if (number > 0) {
                                        this.handleGetDiscountValue(productInfo);
                                        if (this.timeoutChangeQuantity) {
                                            clearTimeout(this.timeoutChangeQuantity);
                                        }
                                        this.timeoutChangeQuantity = setTimeout(this.getPromotion, 400);
                                    }
                                }}
                                onRetryGetDiscount={() => this.handleGetDiscountValue(productInfo)}
                                productInfo={productInfo}
                                onChangeProduct={(newProduct) => {
                                    const { NEW_STOCK } = PRODUCT_STATUS_ID;
                                    if (this.state.inventoryStatusID === NEW_STOCK) {
                                        this.setState({
                                            saleProgramInfo: this.defaultSaleProgram,
                                        }, () => handleChangeProductThreePrice(newProduct))
                                    } else {
                                        this.setState({
                                            productInfo: newProduct,
                                            quantityInStock: newProduct.quantity,
                                            retailPriceVAT: newProduct.salePriceVAT,
                                            saleProgramInfo: this.defaultSaleProgram,
                                            outputStoreID: isAutoCreateEP ? outputStoreID : storeID,
                                            quantity: 1,
                                            deliveryType: IsAutoCreateEP ? deliveryType : 1,
                                            receiveType: 1,
                                            storeRequests: [],
                                        }, () => { this.getSuggestProduct(newProduct.productID, inventoryStatusID) });
                                        this.isGetSecondStock = false;
                                        this.isGetExhibitStock = false;
                                        if (this.timeoutChangeProduct) {
                                            clearTimeout(this.timeoutChangeProduct);
                                        }
                                        this.timeoutChangeProduct = setTimeout(this.getPromotion, 400);
                                        this.handleGetDiscountValue(newProduct);
                                        this.handleGetStandardPointById(newProduct);
                                    }
                                }}
                                isImei={!!imei}
                                disableQuantity={disableQuantity}
                                saleProgramInfo={saleProgramInfo}
                                onShowInstallment={() => {
                                    this.setState({ isVisibleInstallment: true });
                                    actionDetail.cleard_partner_installment_user_code()
                                }}
                                getFifoInfo={(product) => {
                                    this.setState({
                                        isVisibleFIFO: true,
                                        fifoProduct: product
                                    });
                                    this.getFiFoProduct(product);
                                }}
                                getFeatureInfo={() => {
                                    this.setState({
                                        isVisibleFeature: true
                                    });
                                    if (dataFeature.length == 0) {
                                        this.getFeatureProduct(productInfo);
                                    }
                                }}
                                getWarrantyInfo={() => {
                                    this.setState({
                                        isVisibleWarranty: true
                                    });
                                    if (dataWarranty.length == 0) {
                                        this.getWarrantyProduct(productInfo);
                                    }
                                }}
                                getConfigInfo={() => {
                                    this.setState({
                                        isVisibleConfig: true
                                    });
                                    if (dataConfig.length == 0) {
                                        this.getConfigProduct(productInfo);
                                    }
                                }}
                                isFavorite={isFavorite}
                                onFavorite={(product) => {
                                    product.imageUrl = imageUrl;
                                    if (isFavorite) {
                                        actionPouch.removeProductFavorite(product);
                                    } else {
                                        actionPouch.addProductFavorite(product);
                                    }
                                }}
                                removeInstallment={() => {
                                    const { NEW_STOCK } = PRODUCT_STATUS_ID;
                                    const { inventoryStatusID } = this.state;
                                    if (NEW_STOCK == inventoryStatusID) {
                                        this.onchangeProgramThreePrice({ productOrder: productInfo }, isDelete = true)
                                    }
                                    else {
                                        this.setState({
                                            saleProgramInfo: {
                                                logo: "",
                                                partnerID: undefined,
                                                saleProgramID: 0,
                                                saleProgramName: "",
                                                isSpecial: false
                                            }
                                        }, this.getPromotion);
                                        this.updateSalePriceProduct({
                                            "imei": productInfo.imei,
                                            "productID": productInfo.productID,
                                            "productIDRef": productInfo.productIDRef,
                                            "inventoryStatusID": productInfo.inventoryStatusID,
                                            "salePriceVAT": productInfo.salePriceVAT,
                                            "storeID": outputStoreID,
                                            "saleProgramID": 0,
                                            "isInstalment": false,
                                            "isImeiSim": false
                                        });
                                    }
                                }}
                                isCartEmpty={isCartEmpty}
                                retailPriceVAT={retailPriceVAT}
                                actionDetail={actionDetail}
                                isLoyalty={isLoyalty}
                                isFiFO={isFiFO}
                                onShowModalProductOtherInfo={(webInfo) => {
                                    this.setState({
                                        isVisibleProductOtherInfo: true,
                                        webInfo: webInfo
                                    });
                                }}
                                dataRewardInstallment={this.props.dataRewardInstallment}
                                handleInstallmentConsultantSheet={this.handleInstallmentConsultantSheet}
                            />
                        }
                    />
                );
        }
    };

    renderSimProduct = () => {
        const {
            productInfo,
            packagesTypeId
        } = this.state;
        const {
            searchInfo: {
                imageUrl,
            },
            dataPackage,
            statePackage,
        } = this.props;
        return (<SimStock
            imageUrl={imageUrl}
            productInfo={productInfo}
            packagesId={packagesTypeId}
            dataPackage={dataPackage}
            statePackage={statePackage}
            getPackage={this.getDataPackage}
            updateSimInfo={(salePriceVAT, packagesId, totalreward) => {
                this.setState({
                    packagesTypeId: packagesId,
                    productInfo: {
                        ...productInfo,
                        "salePriceVAT": salePriceVAT,
                        "totalreward": totalreward
                    }
                });
            }}
        />);
    }

    renderDeliveryType = (receiveType, productOrder, isAutoCreateEP) => {
        const {
            productID,
            inventoryStatusID,
            imei,
            salePriceVAT
        } = productOrder;
        const {
            quantity,
            quantityInStock,
            outputStoreID,
            defaultDelivery,
            // PROMOTION_DELIVERY
            setKeyPromotionSelected,
            setGroupIDCondition,
            phoneValidate,
            setGroupIDPhoneValidate,
            deliveryType,
            isApplyPhone,
            isHasSaleProgram,
            defaultReceiveInfo,
            saleProgramInfo: {
                saleProgramID
            },
            expandPromotionDelivery
        } = this.state;
        const {
            userInfo,
            storeNearest,
            stateStoreNearest,
            storeShipping,
            stateStoreShipping,
            employee,
            stateEmployee,
            dataProvince,
            stateProvince,
            dataDistrict,
            stateDistrict,
            storeAtHome,
            stateStoreAtHome,
            actionDetail,
            statePromotion: {
                isFetching
            },
            storeOther,
            stateStoreOther,
            // PROMOTION_DELIVERY
            statePromotion,
            promotionDelivery,
            salePromotionDelivery,
            promotionLostSale
        } = this.props;
        const productInfo = {
            "productID": productID,
            "storeID": outputStoreID,
        };
        const deliveryInfo = {
            "storeID": userInfo.storeID,
            "outputStoreID": outputStoreID,
            "deliveryTypeID": deliveryType
        };
        if (isAutoCreateEP) {
            return (
                <DeliveryDefault
                    quantity={quantity}
                    quantityInStock={quantityInStock}
                    receiveInfo={defaultReceiveInfo}
                    addToShoppingCart={({ storeRequests, delivery }) => {
                        this.checkValidatePromotion(productOrder, delivery, storeRequests);
                    }}
                    // PROMOTION_DELIVERY
                    children={<PromotionDelivery
                        statePromotion={statePromotion}
                        promotion={promotionDelivery}
                        salePromotion={salePromotionDelivery}
                        onPromotionTryAgains={this.getPromotion}
                        updateKeyPromotionSelected={this.updateKeyPromotionSelected}
                        setKeyPromotionSelected={setKeyPromotionSelected}
                        setGroupIDCondition={setGroupIDCondition}
                        updateGroupIDCondition={(newSetGroupIDCondition, newSetKeyPromotionSelected, newSetGroupIDPhoneValidate) => {
                            this.setState({
                                setGroupIDCondition: newSetGroupIDCondition,
                                setKeyPromotionSelected: newSetKeyPromotionSelected,
                                setGroupIDPhoneValidate: newSetGroupIDPhoneValidate
                                    ? newSetGroupIDPhoneValidate
                                    : setGroupIDPhoneValidate
                            });
                        }}
                        phoneValidate={phoneValidate}
                        setGroupIDPhoneValidate={setGroupIDPhoneValidate}
                        updateGroupIDPhoneValidate={(phoneNumber, newSetGroupIDPhoneValidate) => {
                            this.setState({
                                phoneValidate: phoneNumber,
                                setGroupIDPhoneValidate: newSetGroupIDPhoneValidate,
                                defaultCustomerPhone: phoneNumber

                            });
                        }}
                        cancelPhoneValidate={(newSetGroupIDPhoneValidate, newSetKeyPromotionSelected) => {
                            this.setState({
                                setGroupIDPhoneValidate: newSetGroupIDPhoneValidate,
                                setKeyPromotionSelected: newSetKeyPromotionSelected,
                                defaultCustomerPhone: ""

                            });
                        }}
                        productInfo={productInfo}
                        isApplyPhone={isApplyPhone}
                        productOrder={productOrder}
                        deliveryInfo={deliveryInfo}
                        actionDetail={actionDetail}
                        expandPromotion={expandPromotionDelivery}
                        promotionLostSale={promotionLostSale}
                        packageServiceSelected={this.state.defaultPackagePrice}
                    />}
                />
            );
        }
        else {
            switch (receiveType) {
                case 2:
                    return (
                        <BaseLoading
                            isLoading={stateDistrict.isFetching}
                            isEmpty={stateDistrict.isEmpty}
                            textLoadingError={stateDistrict.description}
                            isError={stateDistrict.isError}
                            onPressTryAgains={this.getDataDistrict}
                            content={
                                <DeliveryAtOtherStore
                                    actionDetail={actionDetail}
                                    dataStore={storeOther}
                                    quantity={quantity}
                                    saleProgramID={saleProgramID}
                                    productOrder={productOrder}
                                    stateStoreOther={stateStoreOther}
                                    province={dataProvince}
                                    defaultDistrict={dataDistrict}
                                    defaultProvinceID={userInfo.provinceID}
                                    onUpdateOutputStore={(storeInfo) => {
                                        const { NEW_STOCK } = PRODUCT_STATUS_ID;
                                        const { inventoryStatusID } = this.state;
                                        if (NEW_STOCK == inventoryStatusID) {
                                            const stateUpdate = {
                                                outputStoreID: storeInfo.storeID,
                                                storeRequests: storeInfo.storeRequests
                                            }
                                            this.onUpdateOutputStoreThreePrice(storeInfo, stateUpdate)
                                        }
                                        else {
                                            this.setState(
                                                {
                                                    outputStoreID: storeInfo.storeID,
                                                    storeRequests: storeInfo.storeRequests
                                                },
                                                this.getPromotion
                                            );
                                        }
                                    }}
                                    getDataStore={(info) => {
                                        actionDetail.getStoreOther(info);
                                    }}
                                    addToShoppingCart={({ storeRequests, delivery }) => {
                                        this.checkValidatePromotion(productOrder, delivery, storeRequests);
                                    }}
                                    disabled={isFetching}
                                    // PROMOTION_DELIVERY
                                    children={<PromotionDelivery
                                        statePromotion={statePromotion}
                                        promotion={promotionDelivery}
                                        salePromotion={salePromotionDelivery}
                                        onPromotionTryAgains={this.getPromotion}
                                        updateKeyPromotionSelected={this.updateKeyPromotionSelected}
                                        setKeyPromotionSelected={setKeyPromotionSelected}
                                        setGroupIDCondition={setGroupIDCondition}
                                        updateGroupIDCondition={(newSetGroupIDCondition, newSetKeyPromotionSelected, newSetGroupIDPhoneValidate) => {
                                            this.setState({
                                                setGroupIDCondition: newSetGroupIDCondition,
                                                setKeyPromotionSelected: newSetKeyPromotionSelected,
                                                setGroupIDPhoneValidate: newSetGroupIDPhoneValidate
                                                    ? newSetGroupIDPhoneValidate
                                                    : setGroupIDPhoneValidate
                                            });
                                        }}
                                        phoneValidate={phoneValidate}
                                        setGroupIDPhoneValidate={setGroupIDPhoneValidate}
                                        updateGroupIDPhoneValidate={(phoneNumber, newSetGroupIDPhoneValidate) => {
                                            this.setState({
                                                phoneValidate: phoneNumber,
                                                setGroupIDPhoneValidate: newSetGroupIDPhoneValidate,
                                                defaultCustomerPhone: phoneNumber

                                            });
                                        }}
                                        cancelPhoneValidate={(newSetGroupIDPhoneValidate, newSetKeyPromotionSelected) => {
                                            this.setState({
                                                setGroupIDPhoneValidate: newSetGroupIDPhoneValidate,
                                                setKeyPromotionSelected: newSetKeyPromotionSelected,
                                                defaultCustomerPhone: ""

                                            });
                                        }}
                                        productInfo={productInfo}
                                        isApplyPhone={isApplyPhone}
                                        productOrder={productOrder}
                                        deliveryInfo={deliveryInfo}
                                        actionDetail={actionDetail}
                                        expandPromotion={expandPromotionDelivery}
                                        promotionLostSale={promotionLostSale}
                                        packageServiceSelected={this.state.defaultPackagePrice}
                                    />}
                                />
                            }
                        />
                    );
                case 3:
                    return (
                        <BaseLoading
                            isLoading={stateProvince.isFetching}
                            isEmpty={stateProvince.isEmpty}
                            textLoadingError={stateProvince.description}
                            isError={stateProvince.isError}
                            onPressTryAgains={this.getDataProvince}
                            content={
                                <DeliveryAtHome
                                    actionDetail={actionDetail}
                                    dataStore={storeAtHome}
                                    quantity={quantity}
                                    quantityInStock={quantityInStock}
                                    saleProgramID={saleProgramID}
                                    productOrder={productOrder}
                                    stateStoreAtHome={stateStoreAtHome}
                                    onUpdateOutputStore={(storeInfo) => {
                                        const { NEW_STOCK } = PRODUCT_STATUS_ID;
                                        const { inventoryStatusID } = this.state;
                                        if (NEW_STOCK == inventoryStatusID) {
                                            const stateUpdate = {
                                                outputStoreID: storeInfo.storeID,
                                                storeRequests: storeInfo.storeRequests,
                                                deliveryType: storeInfo.deliveryTypeID
                                            }
                                            this.onUpdateOutputStoreThreePrice(storeInfo, stateUpdate)
                                        }
                                        else {
                                            this.setState(
                                                {
                                                    outputStoreID: storeInfo.storeID,
                                                    storeRequests: storeInfo.storeRequests,
                                                    deliveryType: storeInfo.deliveryTypeID
                                                },
                                                this.getPromotion
                                            );
                                        }

                                    }}
                                    province={dataProvince}
                                    defaultDistrict={dataDistrict}
                                    defaultProvinceID={userInfo.provinceID}
                                    getDataStore={(info) => {
                                        actionDetail.getDataAtHome(info);
                                    }}
                                    addToShoppingCart={({ storeRequests, delivery, dataProfileSelected, isShipPartner }) => {
                                        this.checkValidatePromotion(productOrder, delivery, storeRequests, dataProfileSelected, isShipPartner);
                                    }}
                                    disabled={isFetching}
                                    defaultDelivery={defaultDelivery}
                                    defaultCustomerPhone={this.state.defaultCustomerPhone}
                                    getSummerFee={({ delivery, storeRequests }) => {
                                        this.bodySummerFee.current = {
                                            "delivery": delivery,
                                            "storeRequests": storeRequests,
                                            "productOrder": productOrder
                                        }
                                        this.handleGetSummerFee(this.bodySummerFee.current)
                                    }}
                                    feePlusMoneyBO={this.state.feePlusMoneyBO}
                                    onShowModalFees={() => { this.setState({ isShowModalFees: true }) }}
                                    // PROMOTION_DELIVERY
                                    children={<PromotionDelivery
                                        statePromotion={statePromotion}
                                        promotion={promotionDelivery}
                                        salePromotion={salePromotionDelivery}
                                        onPromotionTryAgains={this.getPromotion}
                                        updateKeyPromotionSelected={this.updateKeyPromotionSelected}
                                        setKeyPromotionSelected={setKeyPromotionSelected}
                                        setGroupIDCondition={setGroupIDCondition}
                                        updateGroupIDCondition={(newSetGroupIDCondition, newSetKeyPromotionSelected, newSetGroupIDPhoneValidate) => {
                                            this.setState({
                                                setGroupIDCondition: newSetGroupIDCondition,
                                                setKeyPromotionSelected: newSetKeyPromotionSelected,
                                                setGroupIDPhoneValidate: newSetGroupIDPhoneValidate
                                                    ? newSetGroupIDPhoneValidate
                                                    : setGroupIDPhoneValidate
                                            });
                                        }}
                                        phoneValidate={phoneValidate}
                                        setGroupIDPhoneValidate={setGroupIDPhoneValidate}
                                        updateGroupIDPhoneValidate={(phoneNumber, newSetGroupIDPhoneValidate) => {
                                            this.setState({
                                                phoneValidate: phoneNumber,
                                                setGroupIDPhoneValidate: newSetGroupIDPhoneValidate,
                                                defaultCustomerPhone: phoneNumber

                                            });
                                        }}
                                        cancelPhoneValidate={(newSetGroupIDPhoneValidate, newSetKeyPromotionSelected) => {
                                            this.setState({
                                                setGroupIDPhoneValidate: newSetGroupIDPhoneValidate,
                                                setKeyPromotionSelected: newSetKeyPromotionSelected,
                                                defaultCustomerPhone: ""
                                            });
                                        }}
                                        productInfo={productInfo}
                                        isApplyPhone={isApplyPhone}
                                        productOrder={productOrder}
                                        deliveryInfo={deliveryInfo}
                                        actionDetail={actionDetail}
                                        expandPromotion={expandPromotionDelivery}
                                        promotionLostSale={promotionLostSale}
                                        packageServiceSelected={this.state.defaultPackagePrice}
                                    />}
                                />
                            }
                        />
                    );
                default:
                    return (<DeliveryAtStore
                        actionDetail={actionDetail}
                        storeInfo={userInfo}
                        quantity={quantity}
                        quantityInStock={quantityInStock}
                        saleProgramID={saleProgramID}
                        productOrder={productOrder}
                        getDataStoreNearest={(info) => {
                            actionDetail.getStoreNearest(info);
                        }}
                        getDataStoreShipping={(info) => {
                            actionDetail.getStoreShipping(info);
                        }}
                        onUpdateStoreRequest={(storeRequests) => {
                            this.setState({
                                storeRequests: storeRequests,
                            }, this.getPromotion)
                        }}
                        getEmployee={this.getEmployee}
                        dataStoreNearest={storeNearest}
                        stateStoreNearest={stateStoreNearest}
                        dataStoreShipping={storeShipping}
                        stateStoreShipping={stateStoreShipping}
                        dataEmployee={employee}
                        stateEmployee={stateEmployee}
                        getPromotion={this.getPromotion}
                        addToShoppingCart={({ storeRequests, delivery }) => {
                            this.checkValidatePromotion(productOrder, delivery, storeRequests);
                        }}
                        disabled={isFetching}
                        // PROMOTION_DELIVERY
                        children={<PromotionDelivery
                            statePromotion={statePromotion}
                            promotion={promotionDelivery}
                            salePromotion={salePromotionDelivery}
                            onPromotionTryAgains={this.getPromotion}
                            updateKeyPromotionSelected={this.updateKeyPromotionSelected}
                            setKeyPromotionSelected={setKeyPromotionSelected}
                            setGroupIDCondition={setGroupIDCondition}
                            updateGroupIDCondition={(newSetGroupIDCondition, newSetKeyPromotionSelected, newSetGroupIDPhoneValidate) => {
                                this.setState({
                                    setGroupIDCondition: newSetGroupIDCondition,
                                    setKeyPromotionSelected: newSetKeyPromotionSelected,
                                    setGroupIDPhoneValidate: newSetGroupIDPhoneValidate
                                        ? newSetGroupIDPhoneValidate
                                        : setGroupIDPhoneValidate
                                });
                            }}
                            phoneValidate={phoneValidate}
                            setGroupIDPhoneValidate={setGroupIDPhoneValidate}
                            updateGroupIDPhoneValidate={(phoneNumber, newSetGroupIDPhoneValidate) => {
                                this.setState({
                                    phoneValidate: phoneNumber,
                                    setGroupIDPhoneValidate: newSetGroupIDPhoneValidate,
                                    defaultCustomerPhone: phoneNumber

                                });
                            }}
                            cancelPhoneValidate={(newSetGroupIDPhoneValidate, newSetKeyPromotionSelected) => {
                                this.setState({
                                    setGroupIDPhoneValidate: newSetGroupIDPhoneValidate,
                                    setKeyPromotionSelected: newSetKeyPromotionSelected,
                                    defaultCustomerPhone: ""

                                });
                            }}
                            productInfo={productInfo}
                            isApplyPhone={isApplyPhone}
                            productOrder={productOrder}
                            deliveryInfo={deliveryInfo}
                            actionDetail={actionDetail}
                            expandPromotion={expandPromotionDelivery}
                            promotionLostSale={promotionLostSale}
                            packageServiceSelected={this.state.defaultPackagePrice}
                        />}
                    />);
            }
        }
    }

    onChangeTabInventoryStatus = (status) => {
        const { deliveryType } = this.state;
        const {
            cartRequest: { IsAutoCreateEP }
        } = this.props;
        const { SECOND_STOCK, EXHIBIT_STOCK, NEW_STOCK_DISCOUNT } =
            PRODUCT_STATUS_ID;
        if (this.timeoutChangeProduct) {
            clearTimeout(this.timeoutChangeProduct);
        }
        switch (status) {
            case SECOND_STOCK:
                this.setState({
                    inventoryStatusID: status,
                    productExhibitInfo: {},
                    saleProgramInfo: this.defaultSaleProgram,
                    quantity: 1,
                    deliveryType: IsAutoCreateEP ? deliveryType : 1,
                    receiveType: 1,
                });
                if (!this.isGetSecondStock) {
                    this.isGetSecondStock = true;
                    this.getDataSecondStock();
                }
                break;
            case EXHIBIT_STOCK:
                this.setState({
                    inventoryStatusID: status,
                    productSecondInfo: {},
                    saleProgramInfo: this.defaultSaleProgram,
                    quantity: 1,
                    deliveryType: IsAutoCreateEP ? deliveryType : 1,
                    receiveType: 1,
                });
                if (!this.isGetExhibitStock) {
                    this.isGetExhibitStock = true;
                    this.getDataExhibitStock();
                }
                break;
            case NEW_STOCK_DISCOUNT: {
                const {
                    productInfo: { quantity, salePriceVAT }
                } = this.state;
                this.setState({
                    inventoryStatusID: status,
                    productSecondInfo: {},
                    productExhibitInfo: {},
                    saleProgramInfo: this.defaultSaleProgram,
                    quantityInStock: quantity,
                    retailPriceVAT: salePriceVAT,
                    quantity: 1,
                    deliveryType: IsAutoCreateEP ? deliveryType : 1,
                    receiveType: 1,
                }, () => {
                    this.getDataSearch(true);
                });
                break;
            }
            default: {
                const {
                    productInfo: { quantity, salePriceVAT }
                } = this.state;
                this.setState({
                    inventoryStatusID: status,
                    productSecondInfo: {},
                    productExhibitInfo: {},
                    saleProgramInfo: this.defaultSaleProgram,
                    quantityInStock: quantity,
                    retailPriceVAT: salePriceVAT,
                    quantity: 1,
                    deliveryType: IsAutoCreateEP ? deliveryType : 1,
                    receiveType: 1,
                });
                if (this.props.saleScenarioTypeID !== AN_KHANG_PHARMACY) {
                    this.getDataSearch();
                } else {
                    this.timeoutChangeProduct = setTimeout(this.getPromotion, 200);
                }
                break;
            }
        }
    };

    onSelectPackage = (indexGroup, indexItem) => {
        this.setState(prevState => ({
            defaultPackagePrice: {
                ...prevState.defaultPackagePrice,
                cus_GroupPricePolicyBO: prevState.defaultPackagePrice?.cus_GroupPricePolicyBO?.map((group, groupIdx) =>
                    groupIdx === indexGroup
                        ? {
                            ...group,
                            GroupPricePolicyDetailBOList: group.GroupPricePolicyDetailBOList.map((item, itemIdx) => ({
                                ...item,
                                IsSelected: itemIdx === indexItem ? !item.IsSelected : false // Toggle selection, deselect others
                            }))
                        }
                        : group
                )
            }
        }));
    };

    handleGetPricePackage = async () => {

        const selectedPackage = this.state.defaultPackagePrice?.cus_GroupPricePolicyBO?.flatMap(group =>
            group?.GroupPricePolicyDetailBOList?.filter(({ IsSelected }) => IsSelected)
        ) ?? [];

        const packageDetailServices = selectedPackage.flatMap(item => item.PolicyDetailBOList ?? []);

        try {
            showBlockUI()
            this.packagePriceSheetRef.current?.dismiss()
            const {
                productSearch,
                userInfo: { storeID, moduleID, languageID },
                cartRequest,
                actionDetail,
                saleScenarioTypeID,
                packageServices
            } = this.props;
            const {
                imei,
                productID,
                inventoryStatusID,
                salePrice,
                vat,
                vatPercent,
                salePriceVAT,
            } = this.state.productInfo;
            const {
                quantity,
                saleProgramInfo: { saleProgramID, partnerID, isSpecial },
                deliveryType,
                outputStoreID,
                productInfo,
                listPackageService
            } = this.state;

            const body = {
                "loginStoreId": storeID,
                "languageID": languageID,
                "moduleID": moduleID,
                "saleScenarioTypeID": saleScenarioTypeID,
                "productProlicyBO": {
                    "productID": productInfo.productID,
                    "imei": productInfo.imei,
                    "inventoryStatusID": productInfo.inventoryStatusID
                },
                "outputStoreID": storeID,
                "dateTime": null,
                "saleProgramID": saleProgramID || 0,
                "deliveryTypeID": productInfo.deliveryTypeID ?? 1,
                "customerInfoProlicyBO": {
                    "customerPhone": ""
                },
                "salePricePolicyDetailBOs": packageDetailServices
            }
            const packagePrice = await actionDetailCreator.getPricePolicyProgram(body);
            const id = `${productID}_${saleProgramID}`;
            const services = listPackageService;

            if (helper.IsNonEmptyArray(services)) {
                const lastIndex = services.length - 1;
                const newPackageServices = services.map((val, index) => ({
                    ...val,
                    ...(index === lastIndex
                        ? {
                            ...packagePrice,
                            IsSelected: true,
                            cus_GroupPricePolicyBO: this.state.defaultPackagePrice.cus_GroupPricePolicyBO,
                            TotalAmountNOVAT: packagePrice.TotalAmountNOVAT + salePrice,
                            TotalAmount: packagePrice.TotalAmount + salePriceVAT,
                        }
                        : { IsSelected: false }
                    ),
                }));




                const defaultPrice = newPackageServices.find(
                    (item) => item.IsSelected
                );
                this.setState({
                    listPackageService: newPackageServices,
                    defaultStatusPrice: defaultPrice.packageStatus,
                    defaultPackagePrice: defaultPrice,
                    retailPriceVAT: defaultPrice.TotalAmount

                }, () => {
                    if (this.state.saleProgramInfo.saleProgramID > 0) {
                        this.onchangeProgramThreePrice({ programInfo: this.state.saleProgramInfo, productOrder: this.state.productInfo }, isDelete = false)
                    }
                    actionDetail.getPromotion({
                        "imei": imei,
                        "productID": productID,
                        "inventoryStatusID": inventoryStatusID,
                        "price": defaultPrice.TotalAmountNOVAT || salePrice,
                        "VAT": vat,
                        "VATPercent": vatPercent,
                        "storeID": storeID,
                        "appliedQuantity": quantity,
                        "outputStoreID": outputStoreID,
                        "deliveryTypeID": deliveryType,
                        "storeRequests": [],
                        "saleProgramID": saleProgramID,
                        "cartRequest": cartRequest,
                        "partnerID": partnerID,
                        "isSpecial": isSpecial,
                        "isPromotionEngine": true
                    });

                });

            }
        } catch (error) {
            console.log("🚀 ~ DetailScreen ~ handleGetPricePackage= ~ error:", error)
            Alert.alert(translate('common.notification_uppercase'), error ?? "Đã có lỗi xảy ra. Vui lòng thử lại!",
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI

                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: this.handleGetPricePackage
                    }
                ]
            )
        }
        finally {
            hideBlockUI()
        }


    }



    onChangeTabDeliveryType = (type) => {
        const {
            userInfo: { storeID }
        } = this.props;
        if (this.timeoutChangeDelivery) {
            clearTimeout(this.timeoutChangeDelivery);
        }
        if (type == 1) {
            this.onChangeDeliveryTypeThreePrice()
        } else {
            this.setState({
                deliveryType: 2,
                receiveType: 3,
                outputStoreID: storeID,
                storeRequests: []
            });
        }
    };

    onChangeTabReceiveType = (type) => {
        const {
            userInfo: {
                storeID
            },
        } = this.props;
        if (this.timeoutChangeReceive) {
            clearTimeout(this.timeoutChangeReceive);
        }
        if (type == 1) {
            this.setState({
                deliveryType: 1,
                receiveType: 1,
                outputStoreID: storeID,
                storeRequests: []
            })
            this.timeoutChangeReceive = setTimeout(this.getPromotion, 200);
        }
        else {
            this.setState({
                deliveryType: 1,
                receiveType: 2,
                outputStoreID: storeID,
                storeRequests: []
            })
        }
    }

    getDataProvince = () => {
        const {
            actionLocation,
        } = this.props;
        actionLocation.getDataProvince();
    }

    getDataDistrict = () => {
        const {
            actionLocation,
            userInfo: {
                provinceID
            }
        } = this.props;
        actionLocation.getDataDistrict(provinceID);
    }

    getProductInfoByStatus = () => {
        const { SECOND_STOCK, EXHIBIT_STOCK } = PRODUCT_STATUS_ID;
        const {
            inventoryStatusID,
            productInfo,
            productSecondInfo,
            productExhibitInfo
        } = this.state;
        switch (inventoryStatusID) {
            case SECOND_STOCK:
                return productSecondInfo;
            case EXHIBIT_STOCK:
                return productExhibitInfo;
            default:
                return productInfo;
        }
    };

    handlePreOrder = async (checkCallPromotionPre) => {
        const {
            userInfo: {
                storeID,
                languageID,
                moduleID
            },
            productSearch,
            saleScenarioTypeID
        } = this.props;
        const {
            saleProgramInfo: {
                saleProgramID
            }
        } = this.state;
        if (!helper.IsEmptyObject(productSearch)) {
            try {
                const body = {
                    "loginStoreId": storeID,
                    languageID,
                    moduleID,
                    "saleScenarioTypeID": saleScenarioTypeID,
                    "productID": productSearch.productID,
                    "inventoryStatusID": this.state.inventoryStatusID,
                    "saleQuantity:": "1",
                    "isInstallment": saleProgramID > 0
                }
                showBlockUI()
                const shouldCallPromotion = await this.props.actionDetail.checkProductPre(body)
                this.shouldCallPromotion = shouldCallPromotion
                if (!checkCallPromotionPre) {
                    this.onChangeProductSearch()
                }
            } catch (msgError) {
                this.shouldCallPromotion = false;
                this.setState({ isViewPreorder: true });
                Alert.alert(translate('common.notification_uppercase'), msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: "cancel",
                            onPress: () => {
                                hideBlockUI()
                                this.onChangeProductSearch()
                                this.FLOAT_ACTION = this.FLOAT_ACTION.filter((item) => item.name == 0)
                            }
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: "default",
                            onPress: this.handlePreOrder
                        }
                    ]
                )
            }
            finally {
                hideBlockUI()
            }

        }
    }

    getSuggestProduct = (productID, inventoryStatusID) => {
        const {
            searchInfo,
            userInfo: {
                storeID,
                moduleID,
                languageID
            },
            actionDetail,
            saleScenarioTypeID,
            suggestProducts
        } = this.props;
        const keyProductSuggest = `${productID}_${inventoryStatusID}`
        if (!suggestProducts.has(keyProductSuggest)) {
            this.setState({ isFetchingSuggestProduct: true })
            const body = {
                "loginStoreId": storeID,
                "languageID": languageID,
                "moduleID": moduleID,
                "saleScenarioTypeID": saleScenarioTypeID,
                "productID": productID,
                "inventoryStatusID": inventoryStatusID
            }
            actionDetail.getSuggestProduct(body).then((listProduct) => {
                const infoProductSuggest = {
                    ProductID: keyProductSuggest,
                    Products: listProduct
                }
                actionDetail.set_suggest_products(infoProductSuggest)
                this.setState({ isFetchingSuggestProduct: false })
            }).catch(msgError => {
                this.setState({ isFetchingSuggestProduct: false })
            });
        }

    }

    onChangeProductSearch = async () => {
        const { defaultDelivery } = this.state;
        const {
            productSearch,
            userInfo: { storeID },
            cartRequest,
            actionDetail,
            searchInfo: { isImeiSim },
            saleScenarioTypeID,
            packageServices
        } = this.props;
        if (!helper.IsEmptyObject(productSearch)) {
            const {
                imei,
                productID,
                inventoryStatusID,
                salePrice,
                vat,
                vatPercent,
                quantity: quantityInStock,
                clientQuantity,
                salePriceVAT,
            } = productSearch;
            const {
                quantity,
                saleProgramInfo: { saleProgramID, partnerID, isSpecial },
                deliveryType,
                outputStoreID,
                storeRequests
            } = this.state;
            const initQuantity =
                saleScenarioTypeID === AN_KHANG_PHARMACY
                    ? clientQuantity
                    : quantity;

            // handle three price
            const { inventoryStatusID: newInventoryStatusID } = this.state;
            let finalRetailPriceVAT = 0;
            let finalRetailPrice = 0;
            const { NEW_STOCK, NEW_STOCK_DISCOUNT } = PRODUCT_STATUS_ID;
            if (newInventoryStatusID === NEW_STOCK) {
                const id = `${productID}_${saleProgramID}`
                if (helper.IsNonEmptyArray(packageServices[id])) {
                    const newPackageService = packageServices[id].map(
                        (item) => {
                            const newSalePrice = item.originTotalAmountNOVAT + salePrice;
                            const newSalePriceVAT =
                                item.originTotalAmount + salePriceVAT;
                            return {
                                ...item,
                                TotalAmountNOVAT: newSalePrice,
                                TotalAmount: newSalePriceVAT
                            };
                        }
                    );
                    this.props.actionDetail.set_package_services({
                        id: id,
                        package: newPackageService
                    });
                    const defaultPrice = newPackageService.find(
                        (item) => item.IsSelected
                    );
                    // productSearch.salePriceVAT = defaultPrice.TotalAmount;
                    // productSearch.salePrice = defaultPrice.TotalAmountNOVAT;
                    finalRetailPriceVAT = defaultPrice.TotalAmount;
                    finalRetailPrice = defaultPrice.TotalAmountNOVAT
                    this.setState({
                        listPackageService: newPackageService,
                        defaultStatusPrice: defaultPrice.packageStatus,
                        defaultPackagePrice: defaultPrice,
                        topInset:
                            newInventoryStatusID === NEW_STOCK ||
                                newInventoryStatusID === NEW_STOCK_DISCOUNT
                                ? 120
                                : 180
                    });
                } else {
                    this.setState({
                        listPackageService: [],
                        defaultStatusPrice: '',
                        defaultPackagePrice: {},
                        topInset: 0
                    });
                }
            }
            //

            this.setState(
                {
                    productInfo: productSearch,
                    quantityInStock: quantityInStock,
                    retailPriceVAT: finalRetailPriceVAT || salePriceVAT,
                    deliveryType: defaultDelivery.deliveryType,
                    receiveType: defaultDelivery.receiveType,
                    quantity: initQuantity
                },
                this.getPartnerInstallment
            );
            if (this.shouldCallPromotion) {
                actionDetail.getPromotion({
                    "imei": imei,
                    "productID": productID,
                    "inventoryStatusID": inventoryStatusID,
                    "price": finalRetailPrice || salePrice,
                    "VAT": vat,
                    "VATPercent": vatPercent,
                    "storeID": storeID,
                    "appliedQuantity": quantity,
                    "outputStoreID": outputStoreID,
                    "deliveryTypeID": deliveryType,
                    "storeRequests": [],
                    "saleProgramID": saleProgramID,
                    "cartRequest": cartRequest,
                    "partnerID": partnerID,
                    "isSpecial": isSpecial,
                    "isPromotionEngine": true
                });
            }
            if (isImeiSim) {
                actionDetail.getSimPackage({
                    "productID": productID,
                    "salePrice": salePrice,
                    "vat": vat,
                    "vatPercent": vatPercent,
                    "imei": imei
                });
            }
        } else {
            this.setState({ productInfo: {} });
        }
    };

    getInstallmentSearch = () => {
        const {
            cartRequest,
            userInfo: {
                storeID
            },
        } = this.props;
        const {
            saleProgramInfo,
            deliveryInfo,
            phoneValidate,
            isHasSaleProgram,
            receiveInfo,
            deliveryType,
            outputStoreID,
        } = getSaleProgramInfo(cartRequest, storeID);
        const isApplyPhone = !!phoneValidate;
        this.defaultSaleProgram = saleProgramInfo;
        this.setState({
            saleProgramInfo: saleProgramInfo,
            defaultDelivery: deliveryInfo,
            phoneValidate: phoneValidate,
            isApplyPhone: isApplyPhone,
            isHasSaleProgram: isHasSaleProgram,
            defaultReceiveInfo: receiveInfo,
            deliveryType: deliveryType,
            outputStoreID: outputStoreID
        }, () => {
            this.getDataSearch(false, true)
        });
    }

    getDataSearch = async (isOnSale = false, isFirstCall = false) => {
        const {
            searchInfo: {
                imei,
                inventoryStatusID,
                isImeiSim,
                productID,
                productIDERP
            },
            actionDetail
        } = this.props;
        const {
            saleProgramInfo: { saleProgramID },
            outputStoreID,
            defaultDelivery
        } = this.state;
        let statusID = isOnSale ? 8 : inventoryStatusID || 1;
        const isInstalment = saleProgramID > 0;
        actionDetail.getInfoProductSearch({
            "imei": imei,
            "productID": productIDERP,
            "productIDRef": productID,
            "inventoryStatusID": statusID,
            "storeID": outputStoreID,
            "saleProgramID": saleProgramID,
            "isInstalment": isInstalment,
            "isImeiSim": isImeiSim,
            "deliveryType": defaultDelivery.deliveryType,
            "isCallThreePrice": true
        }, isFirstCall);
        if (!isImeiSim) {
            this.getSuggestProduct(productIDERP, statusID)
        }
    }

    getDataSecondStock = () => {
        const {
            productInfo: {
                productID,
                productIDERP
            }
        } = this.state;
        const {
            userInfo: {
                storeID
            },
            actionDetail
        } = this.props;
        const { saleProgramInfo: {
            saleProgramID
        } } = this.state;
        const isInstalment = (saleProgramID > 0);
        actionDetail.getInfoProductSecond({
            "imei": "",
            "productID": productID,
            "productIDRef": productIDERP,
            "storeID": storeID,
            "saleProgramID": saleProgramID,
            "isInstalment": isInstalment,
        });
    }

    getDataExhibitStock = () => {
        const {
            productInfo: {
                productID,
                productIDERP
            }
        } = this.state;
        const {
            userInfo: {
                storeID
            },
            actionDetail
        } = this.props;
        const { saleProgramInfo: {
            saleProgramID
        } } = this.state;
        const isInstalment = (saleProgramID > 0);
        actionDetail.getInfoProductExhibit({
            "imei": "",
            "productID": productID,
            "productIDRef": productIDERP,
            "storeID": storeID,
            "saleProgramID": saleProgramID,
            "isInstalment": isInstalment,
        });
    }

    getPromotion = async () => {
        const { promotion, actionDetail, saleScenarioTypeID } = this.props;
        const { isViewPreorder } = this.state;
        if (saleScenarioTypeID === PRE_ORDER) {
            const checkCallPromotionPre = true;
            const disablePreorder = isViewPreorder && !this.shouldCallPromotion;
            if (!disablePreorder) {
                await this.handlePreOrder(checkCallPromotionPre);
            }
            if (!this.shouldCallPromotion && helper.IsNonEmptyArray(promotion)) {
                actionDetail.stop_get_promotion([], [], [], [], [], false, "", false)
            }
        }
        if (this.shouldCallPromotion) {
            const productOrder = this.getProductInfoByStatus();
            let newPrice = productOrder.salePrice
            if (!helper.IsEmptyObject(this.state.defaultPackagePrice)) {
                newPrice = this.state.defaultPackagePrice.TotalAmountNOVAT;
            }
            const {
                userInfo: { storeID },
                cartRequest,
                actionDetail
            } = this.props;
            const {
                quantity,
                outputStoreID,
                deliveryType,
                storeRequests,
                saleProgramInfo: { saleProgramID, partnerID, isSpecial }
            } = this.state;
            actionDetail.getPromotion({
                "imei": productOrder.imei,
                "productID": productOrder.productID,
                "inventoryStatusID": productOrder.inventoryStatusID,
                "price": newPrice,
                "VAT": productOrder.vat,
                "VATPercent": productOrder.vatPercent,
                "storeID": storeID,
                "appliedQuantity": quantity,
                "outputStoreID": outputStoreID,
                "deliveryTypeID": deliveryType,
                "storeRequests": storeRequests,
                "saleProgramID": saleProgramID,
                "cartRequest": cartRequest,
                "partnerID": partnerID,
                "isSpecial": isSpecial,
                "isPromotionEngine": true
            });
        }
    };

    getExpandPromotion = (
        product,
        keyPromotion,
        promotionGroup,
        subIndex
    ) => {
        const {
            deliveryType,
            saleProgramInfo: {
                saleProgramID,
                partnerID,
                isSpecial
            },
            outputStoreID,
            storeRequests,
            quantity
        } = this.state;
        const {
            userInfo: {
                storeID
            },
            cartRequest,
            actionDetail,
            saleScenarioTypeID
        } = this.props;
        showBlockUI();
        actionDetail.getExpandSalePromotion({
            "productID": product.productID,
            "inventoryStatusID": product.inventoryStatusID,
            "price": product.salePrice,
            "VAT": product.vat,
            "VATPercent": product.vatPercent,
            "promotionGroupID": promotionGroup.promotionGroupID,
            "isApplyTotalPromotion": promotionGroup.isApplyTotalPromotion,
            "storeID": storeID,
            "appliedQuantity": product.quantity,
            "outputStoreID": outputStoreID,
            "deliveryTypeID": deliveryType,
            "storeRequests": storeRequests,
            "saleProgramID": saleProgramID,
            "cartRequest": cartRequest,
            "partnerID": partnerID,
            "isSpecial": isSpecial,
            "promotionListId": product.promotionListId,
            "isPromotionEngine": true
        }).then(({ expPromotion, expPromotionDelivery, isApplyTotalPromotion }) => {
            promotionGroup.isApplyTotalPromotion = isApplyTotalPromotion;
            hideBlockUI();
            if (helper.IsNonEmptyArray(expPromotion)) {
                this.expDataPromotion[keyPromotion] = {
                    "data": expPromotion,
                    "title": product.productName,
                    "subIndex": subIndex,
                    "productID": product.productID
                };
                const newExpPromotion = Object.entries(this.expDataPromotion);
                this.setState({ expandPromotion: newExpPromotion });
            }
            if (helper.IsNonEmptyArray(expPromotionDelivery)) {
                this.expDataPromotionDelivery[keyPromotion] = {
                    "data": expPromotionDelivery,
                    "title": product.productName,
                    "subIndex": subIndex,
                    "productID": product.productID
                };
                const newExpPromotionDelivery = Object.entries(this.expDataPromotionDelivery);
                this.setState({ expandPromotionDelivery: newExpPromotionDelivery });
            }
        }).catch(msgError => {
            Alert.alert(translate('common.notification_uppercase'), msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: "cancel",
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: "default",
                        onPress: () => this.getExpandPromotion(
                            product,
                            keyPromotion,
                            promotionGroup,
                            subIndex
                        )
                    }
                ]
            )
        });
    }

    removeExpandPromotion = () => {
        const {
            setKeyPromotionSelected,
            setGroupIDCondition,
            setGroupIDPhoneValidate
        } = this.state;
        Object.keys(this.expDataPromotion).forEach(keyPromotion => {
            if (!setKeyPromotionSelected.has(keyPromotion)) {
                const { data } = this.expDataPromotion[keyPromotion];
                data.forEach((groupPromotion) => {
                    const { promotionGroupID, promotionProducts } = groupPromotion;
                    promotionProducts.forEach((product, index) => {
                        const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
                        const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
                        setKeyPromotionSelected.delete(key);
                        setGroupIDCondition.delete(promotionGroupID);
                        setGroupIDPhoneValidate.delete(promotionGroupID);
                    });
                });
                delete this.expDataPromotion[keyPromotion];
            }
        });
        const newExpPromotion = Object.entries(this.expDataPromotion);
        Object.keys(this.expDataPromotionDelivery).forEach(keyPromotion => {
            if (!setKeyPromotionSelected.has(keyPromotion)) {
                const { data } = this.expDataPromotionDelivery[keyPromotion];
                data.forEach((groupPromotion) => {
                    const { promotionGroupID, promotionProducts } = groupPromotion;
                    promotionProducts.forEach((product, index) => {
                        const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
                        const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
                        setKeyPromotionSelected.delete(key);
                        setGroupIDCondition.delete(promotionGroupID);
                        setGroupIDPhoneValidate.delete(promotionGroupID);
                    });
                });
                delete this.expDataPromotionDelivery[keyPromotion];
            }
        });
        const newExpPromotionDelivery = Object.entries(this.expDataPromotionDelivery);
        this.setState({
            expandPromotion: newExpPromotion,
            expandPromotionDelivery: newExpPromotionDelivery,
            setKeyPromotionSelected: setKeyPromotionSelected,
            setGroupIDCondition: setGroupIDCondition,
            setGroupIDPhoneValidate: setGroupIDPhoneValidate
        }, () => {
            !helper.IsEmptyObject(this.bodySummerFee.current) && this.handleGetSummerFee(this.bodySummerFee.current)
        });
    }

    updateKeyPromotionSelected = (setKeyPromotionSelected, excludePromotionIDs, saleProductGroupID, keyProduct, infoPromo) => {
        const {
            promotion,
            salePromotion,
            promotionDelivery,
            salePromotionDelivery,
            promotionLostSale,
        } = this.props;
        ////// xử lý cho nhập nội dung khuyến mãi
        this.handlePromotionContent(setKeyPromotionSelected)
        ///////

        if (helper.IsEmptyArray(excludePromotionIDs)) {
            this.setState({ setKeyPromotionSelected: setKeyPromotionSelected }, this.removeExpandPromotion);
        }
        else {
            let expandGift = [];
            let expandGiftDelivery = [];
            if (!helper.IsEmptyObject(this.expDataPromotion[keyProduct])) {
                expandGift = this.expDataPromotion[keyProduct].data;
            }
            if (!helper.IsEmptyObject(this.expDataPromotionDelivery[keyProduct])) {
                expandGiftDelivery = this.expDataPromotionDelivery[keyProduct].data;
            }
            const allPromotion = [...promotion, ...promotionDelivery, ...expandGift, ...expandGiftDelivery, ...promotionLostSale];
            const alllSalePromotion = [...salePromotion, ...salePromotionDelivery];
            const newSetKeyPromotionSelected = helper.excludeKeyPromotionSelected(
                allPromotion,
                alllSalePromotion,
                setKeyPromotionSelected,
                excludePromotionIDs,
                saleProductGroupID
            );
            this.setState({ setKeyPromotionSelected: newSetKeyPromotionSelected }, this.removeExpandPromotion);
        }
    }

    handlePromotionContent = (keyPromotion) => {
        const {
            promotion,
            mapPromotionContentInput,
            PROMOTIONIMEITVSAMSUNG,
            actionDetail
        } = this.props;
        if (helper.IsNonEmptyArray(promotion)) {
            promotion.forEach((groupPromotion) => {
                const {
                    promotionGroupID,
                    promotionProducts,
                    promotionID,
                } = groupPromotion;
                const isPromotionContent = `,${PROMOTIONIMEITVSAMSUNG},`.includes(`,${promotionID.toString()},`);
                if (isPromotionContent) {
                    const indexPromo = promotionProducts.findIndex((item) => {
                        const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = item;
                        const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
                        return keyPromotion.has(key)
                    })
                    if (indexPromo !== -1) {
                        if (!mapPromotionContentInput.has(promotionGroupID)) {
                            actionDetail.set_map_content_promotion_input({ id: promotionGroupID, isFill: false })
                        }
                    }
                    else {
                        if (mapPromotionContentInput.has(promotionGroupID)) {
                            actionDetail.delete_map_content_promotion_input({ key: promotionGroupID })
                        }
                    }
                }
            });
        }

    }

    updatePromotionKeys = () => {
        const { promotionDelivery } = this.props;
        const { defaultPackagePrice } = this.state;
        let { setKeyPromotionSelected } = this.state;

        if (!defaultPackagePrice?.cus_PricePolicyPromionBOList?.length || !promotionDelivery?.length) return;

        // Tạo tập hợp GroupID hợp lệ và PromotionID đã chọn
        const validGroupIDs = new Set(defaultPackagePrice.cus_PricePolicyPromionBOList.map(p => p.PromotionListGroupID));
        const selectedPromotionIDs = new Set(defaultPackagePrice.cus_PricePolicyPromionBOList.map(p => p.PromotionID));

        // Tạo tập hợp key mặc định
        const defaultSelectedKeys = new Set(
            defaultPackagePrice.cus_PricePolicyPromionBOList.map(p => `${p.PromotionListGroupID}null00`)
        );

        // Xóa các key không hợp lệ và khuyến mãi bị loại trừ
        promotionDelivery.forEach(promoGroup => {
            const isValidGroup = validGroupIDs.has(promoGroup.promotionGroupID);
            const excludedPromotionIDs = new Set(promoGroup.excludePromotionIDs || []);

            promoGroup?.promotionProducts?.forEach(promo => {
                const promoKey = `${promoGroup.promotionGroupID}${promo.productID}${promo.inventoryStatusID}${promo.promotionListGroupIDForSaleProduct}`;

                // Kiểm tra nếu khuyến mãi bị loại trừ
                const isExcluded = [...excludedPromotionIDs].some(id => selectedPromotionIDs.has(id));

                // Xoá nếu:
                // 1. Khuyến mãi bị loại trừ
                // 2. Nhóm không hợp lệ mà key vẫn còn trong set
                // 3. Key không thuộc danh sách mặc định
                if (isExcluded || !isValidGroup || (!defaultSelectedKeys.has(promoKey) && setKeyPromotionSelected.has(promoKey))) {
                    setKeyPromotionSelected.delete(promoKey);
                }
            });
        });

        // Đảm bảo các key mặc định có trong set
        defaultSelectedKeys.forEach(key => setKeyPromotionSelected.add(key));
    };





    keepKeyPromotionSelected = (allKeyPromotion, allGroupID, defaultKeyPromotion) => {
        const {
            setKeyPromotionSelected,
            setGroupIDCondition,
            setGroupIDPhoneValidate,
            defaultPackagePrice
        } = this.state;
        const isEmptyKeySelected = (setKeyPromotionSelected.size == 0);
        const isEmptyAllKey = (allKeyPromotion.size == 0);
        this.expDataPromotion = {};
        this.expDataPromotionDelivery = {};
        if (isEmptyAllKey) {
            this.setState({
                setKeyPromotionSelected: new Set(),
                setGroupIDCondition: new Set(),
                setGroupIDPhoneValidate: new Set(),
                expandPromotion: [],
                expandPromotionDelivery: []
            });
        }
        else if (isEmptyKeySelected) {
            // ĐA GIÁ
            this.updatePromotionKeys();
            //
            // this.handlePromotionContent(defaultKeyPromotion)
            this.setState({
                setKeyPromotionSelected: defaultKeyPromotion,
                setGroupIDCondition: new Set(),
                setGroupIDPhoneValidate: new Set(),
                expandPromotion: [],
                expandPromotionDelivery: []
            },);
        }
        else {
            // ĐA GIÁ
            this.updatePromotionKeys();
            //
            for (let key of setKeyPromotionSelected) {
                if (!allKeyPromotion.has(key)) {
                    setKeyPromotionSelected.delete(key);
                }
            }
            for (let key of setGroupIDCondition) {
                if (!allGroupID.has(key)) {
                    setGroupIDCondition.delete(key);
                }
            }
            for (let key of setGroupIDPhoneValidate) {
                if (!allGroupID.has(key)) {
                    setGroupIDPhoneValidate.delete(key);
                }
            }
            // this.handlePromotionContent(setKeyPromotionSelected)
            this.setState({
                setKeyPromotionSelected: setKeyPromotionSelected,
                setGroupIDCondition: setGroupIDCondition,
                setGroupIDPhoneValidate: setGroupIDPhoneValidate,
                expandPromotion: [],
                expandPromotionDelivery: []
            });
        }
    }

    getPartnerInstallment = () => {
        const { outputStoreID } = this.state;
        const { actionDetail } = this.props;
        actionDetail.getPartnerInstallment(outputStoreID);
    }

    getProgramInstallment = (data) => () => {
        const { actionDetail, userInfo: { storeID } } = this.props;
        const { partnerInstallmentID } = data ?? {};
        actionDetail.getProgramInstallment(data);
        if (helper.isMatchedInstallmentStore(storeID)) {
            actionDetail.getRewardInstallment(partnerInstallmentID);
        }
        actionDetail.getPartnerInstallmentUserCode(partnerInstallmentID)
    }

    getEmployee = (storeID) => {
        const { actionDetail } = this.props;
        actionDetail.getEmployeeAtStore(storeID)
    }

    getFiFoProduct = (fifoProduct) => {
        const { actionDetail } = this.props;
        const { productID } = fifoProduct;
        actionDetail.getFifoInfo(productID);
    }

    getConfigProduct = (productInfo) => {
        const { productIDRef } = productInfo;
        const { actionDetail } = this.props;
        actionDetail.getConfigInfo(productIDRef);
    }

    getFeatureProduct = (productInfo) => {
        const { productIDRef } = productInfo;
        const { actionDetail } = this.props;
        actionDetail.getFeatureInfo(productIDRef);
    }

    getWarrantyProduct = (productInfo) => {
        const { productIDRef } = productInfo;
        const { actionDetail } = this.props;
        actionDetail.getWarrantyPolicyInfo(productIDRef);
    }

    getListPromotionSelected = () => {
        const {
            setKeyPromotionSelected,
            expandPromotion,
            expandPromotionDelivery,
            phoneValidate,
            setGroupIDPhoneValidate
        } = this.state;
        const {
            promotion,
            salePromotion,
            // PROMOTION_DELIVERY
            promotionDelivery,
            salePromotionDelivery,
            promotionLostSale,
            mapPromotionContentInput
        } = this.props;
        let expPromotion = [];
        expandPromotion.forEach(ele => {
            const { data } = ele[1];
            expPromotion = [...expPromotion, ...data];
        });
        let expPromotionDelivery = [];
        expandPromotionDelivery.forEach(ele => {
            const { data } = ele[1];
            expPromotionDelivery = [...expPromotionDelivery, ...data];
        });
        const allPromotion = [...promotion, ...expPromotion, ...promotionDelivery, ...expPromotionDelivery, ...promotionLostSale];
        const allSalePromotion = [...salePromotion, ...salePromotionDelivery];
        let listPromotion = [];
        let isValidate = true;
        let msgValidate = "";
        let isWarning = false;
        let isRequiedContentPromo = true
        allPromotion.forEach(groupPromotion => {
            const {
                promotionProducts,
                promotionGroupID,
                isRequired,
                isCheckCustomer,
                promotionGroupName,
                promotionID,
                isRandomDiscount
            } = groupPromotion;
            const isNonEmpty = helper.isArray(promotionProducts);
            if (isNonEmpty) {
                const isValidatePhone = setGroupIDPhoneValidate.has(promotionGroupID);
                const isCheckWarning = (!isCheckCustomer || isValidatePhone);
                const isCheckRequire = isRequired && isCheckWarning;
                groupPromotion.applyToCustomerPhone = isCheckCustomer || isRandomDiscount ? phoneValidate : "";
                let productSelected = promotionProducts.filter((product, index) => {
                    const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
                    const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
                    return setKeyPromotionSelected.has(key);
                });
                if (productSelected.length > 0) {
                    if (mapPromotionContentInput.has(promotionGroupID) && !mapPromotionContentInput.get(promotionGroupID)) {
                        msgValidate = ` Vui lòng nhập Imei tivi cũ của khách hàng (3-20 ký tự). \n\t${promotionGroupName}`
                        isRequiedContentPromo = false;
                        return
                    }
                    else {
                        listPromotion.push({
                            ...groupPromotion,
                            promotionProducts: productSelected
                        });
                    }
                }
                else {
                    const allExcludeID = helper.getExcludePromotionID(
                        allPromotion,
                        allSalePromotion,
                        setKeyPromotionSelected
                    );
                    const isExclude = allExcludeID.has(promotionID);
                    if (!isExclude) {
                        if (isCheckRequire) {
                            msgValidate += (
                                isValidate
                                    ? `\t${promotionGroupName}`
                                    : `\n\t${promotionGroupName}`
                            );
                            isValidate = false;
                        }
                        if (isCheckWarning) {
                            isWarning = true;
                        }
                    }
                }
            }
        })
        allSalePromotion.forEach(subPromotion => {
            const { promotionGroups } = subPromotion;
            promotionGroups.forEach(groupPromotion => {
                const { promotionProducts, promotionGroupID } = groupPromotion;
                let productSelected = promotionProducts.filter((product, index) => {
                    const { productID, inventoryStatusID, promotionListGroupIDForSaleProduct } = product;
                    const key = `${promotionGroupID}${productID}${inventoryStatusID}${promotionListGroupIDForSaleProduct}`;
                    return setKeyPromotionSelected.has(key);
                });
                if (productSelected.length > 0) {
                    listPromotion.push({
                        ...groupPromotion,
                        promotionProducts: productSelected
                    })
                }
            })
        })
        return { listPromotion, isValidate, msgValidate, isWarning, isRequiedContentPromo };
    }

    checkValidatePromotion = (productOrder, delivery, storeRequests, dataProfileSelected, isShipPartner = false) => {
        const {
            quantity,
            outputStoreID,
            deliveryType,
            saleProgramInfo: { saleProgramID },
            packagesTypeId,
            phoneValidate,
            quantityInStock,
            defaultPackagePrice,
            setKeyPromotionSelected,
        } = this.state;
        const {
            cartRequest,
            searchInfo: { isImeiSim },
            statePromotion,
            userInfo: { storeID },
            promotionDelivery,
            PRODUCTDELIVERYPROMOTION
        } = this.props;
        const { listPromotion, isValidate, msgValidate, isWarning, isRequiedContentPromo } =
            this.getListPromotionSelected();
        const isRequirePackage = isImeiSim && packagesTypeId == 0;
        const {
            expirationDate,
            isSetExpirationDate,
            isRequiredBatchNo,
            batchNo,
            isBatchManagement,
            manufactureBatchNO,
            messageWarning,
            isRequiredQRCode,
            clientQuantity,
            totalreward,
            productServicePackageBO
        } = productOrder;
        const extraPropsForAnKhang = {
            expirationDate,
            isSetExpirationDate,
            isrequiredBatchNO: isRequiredBatchNo,
            batchNO: batchNo,
            isBatchManagement,
            manufactureBatchNO
        };
        let extraPropsThreePrice = {}
        let extraServicePackage = {}
        const { NEW_STOCK } = PRODUCT_STATUS_ID;

        if (!helper.IsEmptyObject(defaultPackagePrice) && this.state.inventoryStatusID == NEW_STOCK) {
            extraPropsThreePrice.extensionProperty = {
                "PricePolicyApplyBO": {
                    "PricePolicyProgramID": defaultPackagePrice.pricePolicyProgramID,
                    "SalePricePolicyID": defaultPackagePrice.SalePricePolicyID,
                    "SalePricePolicyName": defaultPackagePrice.SalePricePolicyName,
                    "ProductID": productOrder.productID,
                    "lstSaleOrderPricePolicyApplyDetailBO": defaultPackagePrice.DetailPolicies,
                    "PricePolicyTypeID": defaultPackagePrice.PricePolicyTypeID
                }
            }
        }
        if (!helper.IsEmptyObject(productServicePackageBO)) {
            extraServicePackage.extensionProperty = { ProductServicePackageBO: productServicePackageBO }
        }
        const isAnKhang = this.props.saleScenarioTypeID === AN_KHANG_PHARMACY;
        // const notifyForAnKhang = productOrder.quantity <= 0 && isAnKhang && !isOtherStore
        const indexRandom = listPromotion.findIndex(pro => pro.isRandomDiscount)
        if (indexRandom !== -1) {
            if (helper.IsNonEmptyArray(listPromotion[indexRandom].promotionProducts)) {
                let newPromotion = listPromotion[indexRandom].promotionProducts.map(promotionPro => {
                    return {
                        ...promotionPro,
                        extensionProperty: this.props.dataCheckPhoneNumberApplyPromotion
                    };
                });
                listPromotion[indexRandom].promotionProducts = newPromotion
            }
        }
        const msgServicePackagePrice = helper.checkUnselectedPromotionDelivery(PRODUCTDELIVERYPROMOTION, promotionDelivery, setKeyPromotionSelected, defaultPackagePrice, deliveryType, isShipPartner)
        // Thêm giỏ hàng SP được xuất tại kho khác thì bỏ qua `messageWarning`
        const isValidStore = delivery.deliveryStoreID !== storeID;
        let shouldSkipMessage = false;
        if (!isValidStore) {
            shouldSkipMessage = clientQuantity < quantityInStock;
        }
        if (
            messageWarning &&
            isBatchManagement &&
            !isRequiredQRCode &&
            shouldSkipMessage
        ) {
            Alert.alert("", messageWarning);
        }
        else if (!isRequiedContentPromo) {
            Alert.alert("", msgValidate);
        }
        else if (isRequirePackage) {
            Alert.alert("", translate('detail.please_select_sim_package'));
        }
        else if (statePromotion.isError) {
            Alert.alert("", translate('detail.warning_get_promotion'));
        }
        else if (!isValidate) {
            Alert.alert(translate('detail.please_select_promotion'), msgValidate);
        }
        // else if (notifyForAnKhang) {
        //     Alert.alert("", 'Không đủ số lượng bán!');
        // }
        else if (defaultPackagePrice?.Ranking > 2 && !!msgServicePackagePrice) {
            Alert.alert("", msgServicePackagePrice);

        }
        else if (isWarning) {
            Alert.alert('', translate('detail.dismiss_promotion'), [
                {
                    text: translate('common.btn_skip'),
                    style: 'cancel'
                },
                {
                    text: translate('common.btn_continue'),
                    style: 'default',
                    onPress: () => {
                        this.checkMissPromotionDelivery({
                            mainProduct: {
                                "productID": productOrder.productID,
                                "imei": productOrder.imei,
                                "inventoryStatusID": productOrder.inventoryStatusID,
                                "pointLoyalty": productOrder.pointLoyalty,
                                "outputTypeID": 3,
                                "appliedQuantity": quantity,
                                "outputStoreID": delivery.deliveryStoreID,
                                "deliveryTypeID": delivery.deliveryTypeID,
                                "saleProgramID": saleProgramID,
                                "packagesTypeId": packagesTypeId,
                                ...extraPropsForAnKhang,
                                ...extraPropsThreePrice,
                                ...extraServicePackage
                            },
                            storeRequests: storeRequests,
                            delivery: delivery,
                            promotionGroups: listPromotion,
                            cartRequest: cartRequest,
                            profile: dataProfileSelected ?? {}
                        });
                    }
                }
            ]);
        } else {
            this.checkMissPromotionDelivery({
                mainProduct: {
                    "productID": productOrder.productID,
                    "imei": productOrder.imei,
                    "inventoryStatusID": productOrder.inventoryStatusID,
                    "pointLoyalty": productOrder.pointLoyalty,
                    "outputTypeID": 3,
                    "appliedQuantity": quantity,
                    "outputStoreID": delivery.deliveryStoreID,
                    "deliveryTypeID": delivery.deliveryTypeID,
                    "saleProgramID": saleProgramID,
                    "packagesTypeId": packagesTypeId,
                    "totalreward": totalreward,
                    ...extraPropsForAnKhang,
                    ...extraPropsThreePrice,
                    ...extraServicePackage
                },
                storeRequests: storeRequests,
                delivery: delivery,
                promotionGroups: listPromotion,
                cartRequest: cartRequest,
                profile: dataProfileSelected ?? {}
            }
            );
        }
    };

    checkMissPromotionDelivery = (data) => {
        const { setKeyPromotionSelected, deliveryType } = this.state;
        const { promotion, promotionDelivery } = this.props;
        const { mainProduct: { deliveryTypeID, outputStoreID }, storeRequests, delivery: { isStoreDelivery } } = data;
        const emptyPromotion = !helper.IsNonEmptyArray(promotionDelivery);
        const differentType = (deliveryType != deliveryTypeID)
        if (differentType && emptyPromotion && !isStoreDelivery) {
            helper.LoggerInfo({
                "{+ApplySelectedPromotion+} MissPromotion": {
                    deliveryType,
                    setKeyPromotionSelected,
                    promotion,
                    promotionDelivery,
                    data
                }
            });
            Alert.alert("",
                'Hệ thống không lấy được khuyến mãi hình thức giao cho sản phẩm. Vui lòng kiểm tra lại.',
                [
                    {
                        text: "OK",
                        onPress: () => {
                            this.setState({
                                outputStoreID: outputStoreID,
                                deliveryType: deliveryTypeID,
                                storeRequests: storeRequests
                            }, this.getPromotion)
                        }
                    }
                ]
            )
        }
        else {
            this.addToShoppingCart(data)
        }
    }

    addToShoppingCart = async (data) => {
        const {
            productSearch,
            navigation,
            actionPouch,
            actionCart,
            actionDetail,
            customerConfirmPolicy,
            userInfo: { storeID, userName, languageID, moduleID }
        } = this.props;
        const { saleProgramInfo: { partnerID } } = this.state
        try {
            showBlockUI();
            if (partnerID == PARTNER_ID.SAMSUNG && !!data.mainProduct?.imei) {
                const body = {
                    loginStoreId: storeID,
                    languageID: languageID,
                    moduleID: moduleID,
                    "IMEI": data.mainProduct?.imei,
                    "ProductID": data.mainProduct.productID,
                    "InventoryStatusID": data.mainProduct.inventoryStatusID,
                    "StoreID": storeID,
                    "CheckFormatIMEI": true,
                    "PartnerInstallmentID": partnerID,
                    "UserName": userName
                }
                imeiIsValid = await actionDetailCreator.validateIMEIWithPartner(body)
            }
            if (checkServiceOrder({ storeID, userName, productSearch })) {
                return (
                    navigation.navigate("AppleCarePlus", { dataCart: data, typePackage: productSearch.serviceAddInfoTemplateId }),
                    hideBlockUI()
                )
            }
            const dataCart = await actionCart.addToShoppingCart(data)
            if (!!data.delivery.customerPhone) {
                actionDetail.set_phone_number_create_at_home(data.delivery.customerPhone)
            }
            hideBlockUI();
            actionDetail.reset_package_services()
            actionDetail.reset_map_content_promotion_input()
            // Push để mount ShoppingCart
            navigation.push('ShoppingCart');
            actionPouch.setDataCartApply(dataCart);
        } catch (error) {
            const msgError = helper.IsNonEmptyString(error) ? error : error.msgError
            Alert.alert(
                translate('common.notification_uppercase'),
                msgError ?? "Đã có lỗi xảy ra. Vui lòng thử lại!",
                [
                    {
                        text: translate('common.btn_skip'),
                        style: 'cancel',
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: 'default',
                        onPress: () => this.addToShoppingCart(data)
                    }
                ]
            );
        }



    };

    checkProductFavorite = (dataFavorite, productInfo) => {
        if (helper.isArray(dataFavorite)) {
            const indexProduct = dataFavorite.findIndex(ele => ele.productID == productInfo.productID);
            return indexProduct > -1;
        }
        else {
            return false;
        }
    }

    moveToShoppingCart = () => {
        const {
            cartRequest,
            dataCartApply,
            navigation,
            actionCart,
        } = this.props;
        const isCartEmpty = helper.IsEmptyObject(cartRequest);
        const isCartApplyEmpty = helper.IsEmptyObject(dataCartApply);
        if (isCartEmpty && isCartApplyEmpty) {
            Alert.alert("", translate('detail.no_product_in_cart'));
        }
        else {
            actionCart.setDataShoppingCart(dataCartApply);
            navigation.navigate("ShoppingCart");
        }
    }

    getLockProductInfo = (productOrder) => {
        const {
            actionDetail,
            userInfo: {
                storeID
            },
        } = this.props;
        actionDetail.getLockProductInfo({
            "storeID": storeID,
            "imei": productOrder.imei,
            "productID": productOrder.productID,
            "productIDRef": productOrder.productIDRef,
            "inventoryStatusID": productOrder.inventoryStatusID,
        });
    }

    moveToLockCart = (productOrder) => {
        this.getLockProductInfo(productOrder);
        this.setState({ isVisibleLockInfo: true });
    }

    getDataPackage = (data) => {
        const {
            actionDetail
        } = this.props;
        actionDetail.getSimPackage({ data })
    }

    updateSalePriceProduct = (infoProduct) => {
        const { actionDetail } = this.props;
        actionDetail.getSalePriceProduct(infoProduct).then((product) => {
            const { salePriceVAT, costPrice, productServicePackageBO } = product
            /// 3 giá cần chỗ này
            hideBlockUI()
            const { retailPriceVAT, listPackageService } = this.state;
            if (
                salePriceVAT != retailPriceVAT &&
                helper.IsNonEmptyArray(listPackageService)
            ) {
                const newPackageService = listPackageService.map((item) => {
                    return { ...item, TotalAmount: salePriceVAT + item.originTotalAmount };
                });
                this.setState({ listPackageService: newPackageService });
            }
            const newProductInfo = { ...this.state.productInfo, costPrice: costPrice, productServicePackageBO: productServicePackageBO }
            this.setState({ retailPriceVAT: salePriceVAT, productInfo: newProductInfo });
        });
    };

    handlePackageService = (dataPackageService, productInfo, saleProgramInfo) => {
        const newProductOrder = { ...productInfo }
        let newPackageService = [...dataPackageService]
        if (helper.IsNonEmptyArray(newPackageService)) {
            newPackageService.forEach(
                (item) => {
                    item.TotalAmountNOVAT = item.originTotalAmountNOVAT + newProductOrder.salePrice;
                    item.TotalAmount = item.originTotalAmount + newProductOrder.salePriceVAT;
                }
            );
        }
        const newSaleProgramID = saleProgramInfo.saleProgramID ?? 0
        this.props.actionDetail.set_package_services({
            id: `${newProductOrder.productID}_${newSaleProgramID}`,
            package: newPackageService
        });
        const { NEW_STOCK, NEW_STOCK_DISCOUNT } = PRODUCT_STATUS_ID;
        const { inventoryStatusID } = this.state;
        let finalRetailPriceVAT = 0;
        if (helper.IsNonEmptyArray(newPackageService)) {
            const defaultPrice = newPackageService.find(
                (item) => item.IsSelected
            );
            finalRetailPriceVAT = defaultPrice.TotalAmount;
            // newProductOrder.salePriceVAT = defaultPrice.TotalAmount;
            // newProductOrder.salePrice = defaultPrice.TotalAmountNOVAT;
            this.setState({
                listPackageService: newPackageService,
                defaultStatusPrice: defaultPrice.packageStatus,
                defaultPackagePrice: defaultPrice,
                topInset:
                    inventoryStatusID === NEW_STOCK ||
                        inventoryStatusID === NEW_STOCK_DISCOUNT
                        ? 120
                        : 180
            });
        } else {
            this.setState({
                listPackageService: [],
                defaultStatusPrice: '',
                defaultPackagePrice: {},
                topInset: 0
            });
        }
        return finalRetailPriceVAT
    }
    onChangeDeliveryTypeThreePrice = async () => {
        const { productInfo, saleProgramInfo, defaultPackagePrice } = this.state
        const { userInfo: { storeID, moduleID, languageID }, saleScenarioTypeID } = this.props
        const newProductOrder = { ...productInfo }
        try {
            showBlockUI()
            let bodyThreePrice = {
                "loginStoreId": storeID,
                "languageID": languageID,
                "moduleID": moduleID,
                "saleScenarioTypeID": saleScenarioTypeID,
                "productProlicyBO": {
                    "productID": newProductOrder.productID,
                    "imei": newProductOrder.imei,
                    "inventoryStatusID": newProductOrder.inventoryStatusID
                },
                "outputStoreID": storeID,
                "dateTime": null,
                "saleProgramID": saleProgramInfo.saleProgramID ?? 0,
                "deliveryTypeID": 1,
                "customerInfoProlicyBO": {
                    "customerPhone": ""

                },
                "PricePolicyTypeID": defaultPackagePrice.PricePolicyTypeID,
                "salePricePolicyID": defaultPackagePrice.SalePricePolicyID,
            };
            const dataPackageService =
                await this.props.actionDetail.getPackageService(bodyThreePrice);
            const finalRetailPriceVAT = this.handlePackageService(dataPackageService, productInfo, saleProgramInfo)
            this.setState(
                {
                    deliveryType: 1,
                    receiveType: 1,
                    outputStoreID: storeID,
                    storeRequests: [],
                    productInfo: newProductOrder,
                    retailPriceVAT: finalRetailPriceVAT || newProductOrder.salePriceVAT,
                },
            );
            this.timeoutChangeDelivery = setTimeout(this.getPromotion, 200);

        } catch (error) {
            console.log("🚀 ~ file: index.js:3521 ~ DetailScreen ~ onChangeDeliveryTypeThreePrice= ~ error:", error)
            Alert.alert(
                translate('common.notification'),
                error.msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: 'cancel',
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: 'default',
                        onPress: () =>
                            this.onChangeDeliveryTypeThreePrice()
                    }
                ]
            );
        } finally {
            hideBlockUI()
        }
    }
    onUpdateOutputStoreThreePrice = async (storeInfo, stateUpdate) => {
        setTimeout(async () => {
            const { productInfo, saleProgramInfo, defaultPackagePrice } = this.state
            const { userInfo: { storeID, moduleID, languageID }, saleScenarioTypeID } = this.props
            const newProductOrder = { ...productInfo }
            try {
                showBlockUI()
                let bodyThreePrice = {
                    "loginStoreId": storeID,
                    "languageID": languageID,
                    "moduleID": moduleID,
                    "saleScenarioTypeID": saleScenarioTypeID,
                    "productProlicyBO": {
                        "productID": newProductOrder.productID,
                        "imei": newProductOrder.imei,
                        "inventoryStatusID": newProductOrder.inventoryStatusID
                    },
                    "outputStoreID": storeID,
                    "dateTime": null,
                    "saleProgramID": saleProgramInfo.saleProgramID ?? 0,
                    "deliveryTypeID": storeInfo.deliveryTypeID ?? 1,
                    "customerInfoProlicyBO": {
                        "customerPhone": ""

                    },
                    "PricePolicyTypeID": defaultPackagePrice.PricePolicyTypeID,
                    "salePricePolicyID": defaultPackagePrice.SalePricePolicyID,
                };
                const dataPackageService =
                    await this.props.actionDetail.getPackageService(bodyThreePrice);
                const finalRetailPriceVAT = this.handlePackageService(dataPackageService, productInfo, saleProgramInfo)
                this.setState(
                    {
                        ...stateUpdate,
                        productInfo: newProductOrder,
                        retailPriceVAT: finalRetailPriceVAT || newProductOrder.salePriceVAT,
                    },
                    this.getPromotion
                );
            } catch (error) {
                console.log("🚀 ~ file: index.js:3521 ~ DetailScreen ~ onUpdateOutputStoreThreePrice= ~ error:", error)
                Alert.alert(
                    translate('common.notification'),
                    error.msgError,
                    [
                        {
                            text: translate('common.btn_skip'),
                            style: 'cancel',
                            onPress: hideBlockUI
                        },
                        {
                            text: translate('common.btn_notify_try_again'),
                            style: 'default',
                            onPress: () =>
                                this.onUpdateOutputStoreThreePrice(storeInfo, stateUpdate)
                        }
                    ]
                );
            } finally {
                hideBlockUI()
            }
        }, 50);
    }
    onchangeProgramThreePrice = async (dataProgram, isDelete) => {
        const { defaultPackagePrice } = this.state
        const { userInfo: { storeID, brandID, moduleID, languageID }, saleScenarioTypeID } = this.props
        const { productOrder, programInfo = {} } = dataProgram
        const newProductOrder = { ...productOrder }
        this.setState({ isVisibleInstallment: false })
        try {
            showBlockUI();
            let bodyThreePrice = {
                "loginStoreId": storeID,
                "languageID": languageID,
                "moduleID": moduleID,
                "saleScenarioTypeID": saleScenarioTypeID,
                "productProlicyBO": {
                    "productID": newProductOrder.productID,
                    "imei": newProductOrder.imei,
                    "inventoryStatusID": newProductOrder.inventoryStatusID
                },
                "outputStoreID": storeID,
                "dateTime": null,
                "saleProgramID": programInfo.saleProgramID ?? 0,
                "deliveryTypeID": newProductOrder.deliveryTypeID ?? 1,
                "customerInfoProlicyBO": {
                    "customerPhone": ""

                },
                "PricePolicyTypeID": defaultPackagePrice.PricePolicyTypeID,
                "salePricePolicyID": defaultPackagePrice.SalePricePolicyID,
            };
            const dataPackageService =
                await this.props.actionDetail.getPackageService(bodyThreePrice);
            const finalRetailPriceVAT = this.handlePackageService(dataPackageService, productOrder, programInfo)
            this.setState({
                productInfo: newProductOrder,
                retailPriceVAT: finalRetailPriceVAT || newProductOrder.salePriceVAT,
            }, () => this.handleSaleProgram(dataProgram, isDelete)
            );
        } catch (error) {
            console.log("🚀 ~ file: index.js:3521 ~ DetailScreen ~ onchangeProgramThreePrice= ~ error:", error)
            Alert.alert(
                translate('common.notification'),
                error.msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: 'cancel',
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: 'default',
                        onPress: () =>
                            this.onchangeProgramThreePrice(dataProgram, isDelete)
                    }
                ]
            );
        }
        // finally {
        //     hideBlockUI()
        // }
    }
    handleSaleProgram = ({ programInfo = {}, productOrder, logo = "" }, isDelete) => {
        const { saleProgramInfo, outputStoreID } = this.state
        const newSaleProgramInfo = isDelete ? {
            ...saleProgramInfo,
            logo: '',
            partnerID: undefined,
            saleProgramID: 0,
            saleProgramName: '',
            isSpecial: false
        } : {
            ...saleProgramInfo,
            partnerID: programInfo.partnerInstallmentID,
            saleProgramID: programInfo.saleProgramID,
            saleProgramName: programInfo.saleProgramName,
            logo: logo || saleProgramInfo.logo,
            isSpecial: programInfo.isSpecial
        }
        this.setState({
            saleProgramInfo: newSaleProgramInfo
        }, this.getPromotion);
        const isInstalment = programInfo.saleProgramID > 0;
        this.updateSalePriceProduct({
            imei: productOrder.imei,
            productID: productOrder.productID,
            productIDRef: productOrder.productIDRef,
            inventoryStatusID: productOrder.inventoryStatusID,
            salePriceVAT: productOrder.salePriceVAT,
            storeID: outputStoreID,
            saleProgramID: isDelete ? 0 : programInfo.saleProgramID,
            isInstalment: isDelete ? false : isInstalment,
            isImeiSim: false
        });

    }

    getPreOrderLockProductInfo = (productOrder) => {
        const {
            actionDetail,
            userInfo: {
                storeID
            },
        } = this.props;
        actionDetail.getPreOrderLockProductInfo({
            "storeID": storeID,
            "productID": productOrder.productID
        });
    }

    moveToLockPreOrderCart = (productOrder) => {
        this.getPreOrderLockProductInfo(productOrder);
        this.setState({ isVisiblePreOrder: true });
    }

    checkWowPoints = (phoneNumber) => {
        // this.props.loyaltyAction.checkWowPoints(phoneNumber, true)
        //     .then((wowPointsMessage) => {
        //         if (wowPointsMessage) {
        //             setTimeout(() => {
        //                 this.props.loyaltyAction.setWowPointsMessage({
        //                     phoneNumber: '',
        //                     message: ''
        //                 });
        //             }, TIMEOUT_WOW_POINTS);
        //         }
        //     })
        //     .catch((error) => {
        //         !CONFIG.isPRODUCTION && Toast.show({
        //             type: 'error',
        //             text1: error ?? 'Lỗi: Hỏi Trần Nghĩa - 165059'
        //         });
        //         console.log("checkWowPoints ", error);
        //     })
    }
    handleGetSummerFee = (data) => {
        const { productOrder, delivery, storeRequests } = data
        const { actionCart, cartRequest } = this.props;
        const { listPromotion } = this.getListPromotionSelected();
        const {
            totalreward
        } = productOrder;
        const {
            quantity,
            saleProgramInfo: { saleProgramID },
            packagesTypeId,
        } = this.state;
        const body = {
            mainProduct: {
                "productID": productOrder.productID,
                "imei": productOrder.imei,
                "inventoryStatusID": productOrder.inventoryStatusID,
                "pointLoyalty": productOrder.pointLoyalty,
                "outputTypeID": 3,
                "appliedQuantity": quantity,
                "outputStoreID": delivery.deliveryStoreID,
                "deliveryTypeID": delivery.deliveryTypeID,
                "saleProgramID": saleProgramID,
                "packagesTypeId": packagesTypeId,
                "totalreward": totalreward,
            },
            storeRequests: storeRequests,
            delivery: {
                ...delivery, deliveryAddress: " Hồ Chí Minh",
                contactGender: 1,
                contactPhone: "0979146817",
                contactName: "Công Ty Cổ Phần Sữa V",
            },
            promotionGroups: listPromotion,
            cartRequest: null,
        }
        actionCart
            .getSummerFee(body)
            .then(({ FeeAndDepositBO }) => {
                let feeBO = {}
                if (!helper.IsEmptyObject(FeeAndDepositBO) && !helper.IsEmptyObject(FeeAndDepositBO?.cus_FeeBOList?.[0]) && (!helper.IsEmptyObject(FeeAndDepositBO?.cus_FeeBOList?.[0]?.cus_ForwarderFeeBO))) {
                    feeBO = { ...FeeAndDepositBO.cus_FeeBOList[0].cus_ForwarderFeeBO, "Rounding": FeeAndDepositBO.cus_FeeBOList[0].Rounding ?? 0 }
                }
                this.setState({ feePlusMoneyBO: feeBO })
            })
    }
    handleInstallmentConsultantSheet = () => {
        this.installmentConsultantSheetRef.current?.present()
    }
}

const mapStateToProps = function (state) {
    return {
        searchInfo: state.saleReducer.productSearch,
        userInfo: state.userReducer,
        dataProduct: state.detailReducer.dataProduct,
        productSearch: state.detailReducer.productSearch,
        secondStock: state.detailReducer.secondStock,
        exhibitStock: state.detailReducer.exhibitStock,
        promotion: state.detailReducer.promotion,
        salePromotion: state.detailReducer.salePromotion,
        allKeyPromotion: state.detailReducer.allKeyPromotion,
        allGroupID: state.detailReducer.allGroupID,
        defaultKeyPromotion: state.detailReducer.defaultKeyPromotion,
        employee: state.detailReducer.employee,
        dataProvince: state.locationReducer.dataProvince,
        dataDistrict: state.locationReducer.dataDistrict,
        storeNearest: state.detailReducer.storeNearest,
        storeShipping: state.detailReducer.storeShipping,
        storeOther: state.detailReducer.storeOther,
        storeAtHome: state.detailReducer.storeAtHome,
        partnerInstallment: state.detailReducer.partnerInstallment,
        programInstallment: state.detailReducer.programInstallment,
        dataFifo: state.detailReducer.dataFifo,
        dataConfig: state.detailReducer.dataConfig,
        dataFeature: state.detailReducer.dataFeature,
        dataLock: state.detailReducer.dataLock,
        dataWarranty: state.detailReducer.dataWarranty,
        dataPackage: state.detailReducer.dataPackage,
        statePromotion: state.detailReducer.statePromotion,
        stateProduct: state.detailReducer.stateProduct,
        stateInventoryTab: state.detailReducer.stateInventoryTab,
        stateSecond: state.detailReducer.stateSecond,
        stateExhibit: state.detailReducer.stateExhibit,
        stateStoreNearest: state.detailReducer.stateStoreNearest,
        stateStoreShipping: state.detailReducer.stateStoreShipping,
        stateEmployee: state.detailReducer.stateEmployee,
        stateProvince: state.locationReducer.stateProvince,
        stateDistrict: state.locationReducer.stateDistrict,
        stateStoreOther: state.detailReducer.stateStoreOther,
        stateStoreAtHome: state.detailReducer.stateStoreAtHome,
        statePartnerInstallment: state.detailReducer.statePartnerInstallment,
        stateProgramInstallment: state.detailReducer.stateProgramInstallment,
        packageServices: state.detailReducer.packageServices,
        stateFifo: state.detailReducer.stateFifo,
        stateConfig: state.detailReducer.stateConfig,
        stateWarranty: state.detailReducer.stateWarranty,
        stateFeature: state.detailReducer.stateFeature,
        stateLock: state.detailReducer.stateLock,
        statePackage: state.detailReducer.statePackage,
        cartRequest: state.shoppingCartReducer.dataShoppingCart,
        dataFavorite: state.pouchFavorite.dataFavorite,
        dataCartApply: state.pouchCartApply.dataCartApply,
        promotionDelivery: state.detailReducer.promotionDelivery,
        promotionLostSale: state.detailReducer.promotionLostSale,
        salePromotionDelivery: state.detailReducer.salePromotionDelivery,
        dataPreOrderLock: state.detailReducer.dataPreOrderLock,
        statePreOrderLock: state.detailReducer.statePreOrderLock,
        isDisableTabDelivery: state.detailReducer.isDisableTabDelivery,
        saleScenarioTypeID: state.specialSaleProgramReducer.saleScenarioTypeID,
        standardPointById: state._pharmacyReducer.standardPointById,
        dataShoppingCart: state.shoppingCartReducer.dataShoppingCart,
        dataCheckPhoneNumberApplyPromotion: state.detailReducer.dataCheckPhoneNumberApplyPromotion,
        suggestProducts: state.detailReducer.suggestProducts,
        mapPromotionContentInput: state.detailReducer.mapPromotionContentInput,
        PROMOTIONIMEITVSAMSUNG: state.appSettingReducer.PROMOTIONIMEITVSAMSUNG,
        dataRewardInstallment: state.detailReducer.dataRewardInstallment,
        dataUserCode: state.detailReducer.dataUserCode,
        customerConfirmPolicy: state.shoppingCartReducer.customerConfirmPolicy,
        PRODUCTDELIVERYPROMOTION: state.appSettingReducer.PRODUCTDELIVERYPROMOTION,
    };
};

const mapDispatchToProps = function (dispatch) {
    return {
        actionDetail: bindActionCreators(actionDetailCreator, dispatch),
        actionLocation: bindActionCreators(actionLocationCreator, dispatch),
        actionCart: bindActionCreators(actionCartCreator, dispatch),
        actionPouch: bindActionCreators(actionPouchCreator, dispatch),
        actionNewPharmacy: bindActionCreators(actionNewPharmacyCreator, dispatch),
        loyaltyAction: bindActionCreators(loyaltyActionCreator, dispatch)
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(DetailScreen);

const getSaleProgramInfo = (cartRequest, storeID) => {
    const {
        SaleOrderDetails,
        ApplyPromotionToCustomerPhone,
        IsAutoCreateEP,
        CustomerInfo
    } = cartRequest;
    const isNonEmpty = helper.IsNonEmptyArray(SaleOrderDetails);
    let isHasSaleProgram = false;
    let saleProgramInfo = {
        logo: "",
        partnerID: undefined,
        saleProgramID: 0,
        saleProgramName: "",
        isSpecial: false
    };
    let deliveryInfo = {
        gender: null,
        contactPhone: "",
        contactName: "",
        contactAddress: "",
        provinceID: 0,
        districtID: 0,
        wardID: 0,
        deliveryType: 1,
        receiveType: 1
    };
    let receiveInfo = {};
    let deliveryType = 1;
    let outputStoreID = storeID;
    const phoneValidate = ApplyPromotionToCustomerPhone || "";
    if (isNonEmpty) {
        const dataDetails = [...SaleOrderDetails].reverse();
        const isAtHome = (dataDetails[0].DeliveryInfoRequest.DeliveryTypeID != 1);
        const infoSaleProgram = SaleOrderDetails.find(saleorder => !!saleorder.SaleProgramInfo);
        const infoDelivery = dataDetails.find(saleorder => {
            const { DeliveryInfoRequest: { DeliveryTypeID } } = saleorder;
            return (DeliveryTypeID != 1);
        });
        if (infoSaleProgram) {
            const {
                SaleProgramInfo: {
                    Logo,
                    PartnerInstallmentID,
                    SaleProgramID,
                    SaleProgramName,
                    IsSpecial
                },
                DeliveryInfoRequest,
            } = infoSaleProgram;
            saleProgramInfo = {
                logo: Logo,
                partnerID: PartnerInstallmentID,
                saleProgramID: SaleProgramID,
                saleProgramName: SaleProgramName,
                isSpecial: IsSpecial
            };
            isHasSaleProgram = true;
            if (IsAutoCreateEP) {
                receiveInfo = {
                    "deliveryInfo": DeliveryInfoRequest,
                    "storeRequests": []
                };
                deliveryType = DeliveryInfoRequest.DeliveryTypeID;
                outputStoreID = DeliveryInfoRequest.DeliveryStoreID;
            }
        }
        if (infoDelivery) {
            const { DeliveryInfoRequest: {
                ContactGender,
                ContactName,
                ContactPhone,
                DeliveryAddress,
                DeliveryProvinceID,
                DeliveryDistrictID,
                DeliveryWardID,
                DeliveryTypeID,
                DeliveryDistrictName,
                DeliveryWardName,
                DeliveryProvinceName
            } } = infoDelivery;
            deliveryInfo = {
                gender: ContactGender,
                contactPhone: ContactPhone,
                contactName: ContactName,
                contactAddress: DeliveryAddress,
                provinceID: DeliveryProvinceID,
                districtID: DeliveryDistrictID,
                wardID: DeliveryWardID,
                deliveryType: isAtHome ? DeliveryTypeID : 1,
                receiveType: isAtHome ? 3 : 1,
                deliveryDistrictName: DeliveryDistrictName,
                deliveryWardName: DeliveryWardName,
                deliveryProvinceName: DeliveryProvinceName,
                customerPhone: CustomerInfo.CustomerPhone
            }
        }
    }

    console.log("🚀 ~ getSaleProgramInfo ~ deliveryInfo:", deliveryInfo)

    return {
        saleProgramInfo,
        deliveryInfo,
        phoneValidate,
        isHasSaleProgram,
        receiveInfo,
        deliveryType,
        outputStoreID
    };
}

const getValueDisabledPager = (
    inventoryStatusID,
    productInfo,
    productSecondInfo,
    productExhibitInfo
) => {
    const { SECOND_STOCK, EXHIBIT_STOCK } = PRODUCT_STATUS_ID
    switch (inventoryStatusID) {
        case SECOND_STOCK:
            return helper.IsEmptyObject(productSecondInfo);
        case EXHIBIT_STOCK:
            return helper.IsEmptyObject(productExhibitInfo);
        default:
            return helper.IsEmptyObject(productInfo);
    }
};

const checkServiceOrder = ({ storeID, userName, productSearch: { serviceAddInfoTemplateId } }) =>
    serviceAddInfoTemplateId == TYPE_OF_CARE_PACKAGE.WATER_FILTER || serviceAddInfoTemplateId == TYPE_OF_CARE_PACKAGE.APPLE_CARE;


const ProductInfo = ({ info }) => {
    const { productName } = info;
    return (
        <View style={{
            width: constants.width,
            justifyContent: "center",
            alignItems: "center",
            paddingHorizontal: 10,
            paddingVertical: 4,
            backgroundColor: COLORS.bgF0F0F0
        }}>
            <View style={{
                width: constants.width - 20,
                height: 32,
                borderWidth: StyleSheet.hairlineWidth,
                borderColor: COLORS.bd333333,
                borderRadius: 4,
                backgroundColor: COLORS.bgFFFFFF,
                justifyContent: "center",
                alignItems: "center",
                paddingHorizontal: 8
            }}>
                <MyText style={{
                    color: COLORS.txt147EFB,
                    fontWeight: "bold"
                }}
                    numberOfLines={1}
                    text={productName || ""}
                />
            </View>
        </View>
    );
}

export const BlockUI = ({ visible, onChangeVisible }) => (
    <View
        // eslint-disable-next-line react-native/no-color-literals
        style={{
            flex: 1,
            backgroundColor: '#00000033',
            zIndex: visible ? 999999 : -999999,
            position: 'absolute',
            left: 0,
            top: 0,
            width: visible ? '100%' : 0,
            height: visible ? constants.height : 0
        }}>
        {!visible ? null : (
            <TouchableOpacity
                onPress={() => { onChangeVisible(!visible) }}
                style={{
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center'
                }}>

            </TouchableOpacity>
        )}
    </View>
);





