import React, { useState } from 'react';
import { View } from 'react-native';
import { constants } from "@constants";
import { MyText } from "@components";
import { helper, dateHelper } from "@common";
import { translate } from '@translate';
import { COLORS } from "@styles";
import { TooltipWrapper } from '../../ProductInfo/NewStock/component/ProductTitle';

const SimTitle = ({
    productInfo,
    stateStandardPoint
}) => {
    const {
        productName,
        salePriceVAT,
        imei,
        totalreward
    } = productInfo;
    const isVisible = (salePriceVAT > 0);
    return (
        <>
            <MyText style={{
                color: COLORS.txt0099E5,
                fontWeight: "bold",
                width: constants.width - 20,
                textAlign: "center"
            }}
                addSize={4}
                text={productName}
            />
            <View style={{
                justifyContent: "space-between",
                alignItems: 'center',
                flexDirection: 'row',
                padding: 5,
                width: constants.width - 5,
            }}>
                <View style={{ flexDirection: "row" }}>
                    <MyText style={{
                        color: COLORS.txt333333,
                        fontSize: 12.5,
                        fontWeight: "normal",
                        textAlign: "center",
                    }}
                        addSize={-1.5}
                        text={translate('detail.emei')}
                    />
                    <MyText style={{
                        color: COLORS.txtEA1D5D,
                        fontWeight: "bold",
                        textAlign: 'center',
                    }}
                        text={imei}
                    />
                </View>

                {totalreward > 0 && (
                    <TooltipWrapper
                        placement="bottom"
                        content={
                            <MyText
                                style={{ color: COLORS.txtFFFFFF }}
                                text={translate('detail.total_reward')}
                            />
                        }
                        wrapper={
                            <View
                                style={{
                                    flexDirection: 'row'
                                }}>
                                <MyText
                                    style={{ color: COLORS.txt279B37 }}
                                    text={helper.convertNum(totalreward, false)}
                                />
                            </View>
                        }
                    />
                )}

                {stateStandardPoint.standardPoint > 0 && (
                    <TooltipWrapper
                        placement={'bottom'}
                        content={
                            <MyText
                                style={{ color: COLORS.txtFFFFFF }}
                                text={translate('detail.standard_point')}
                            />
                        }
                        wrapper={
                            <View
                                style={{
                                    flexDirection: 'row',
                                    alignItems: 'center'
                                }}>
                                <MyText
                                    style={{ color: COLORS.txtBE0027 }}
                                    text={helper.convertNum(
                                        stateStandardPoint.standardPoint,
                                        false
                                    )}>
                                    <MyText
                                        style={{ color: COLORS.txtBE0027 }}
                                        text={`(${dateHelper.formatStrDateDDMM(
                                            stateStandardPoint.toDate || ""
                                        )})`}
                                        addSize={-2}
                                    />
                                </MyText>
                            </View>
                        }
                    />
                )}

            </View>
            <View style={{
                width: constants.width - 20,
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
                marginTop: 8
            }}>
                <View style={{
                    width: 130,
                }}>
                    {
                        isVisible &&
                        <MyText style={{
                            color: COLORS.txt333333,
                            width: 130,
                        }}
                            text={translate('common.price')}
                        >
                            <MyText style={{ color: COLORS.txtFF0000, }}
                                text={helper.convertNum(salePriceVAT)}
                                addSize={4}
                            />
                        </MyText>
                    }
                </View>
                <MyText style={{
                    color: COLORS.txt0000FF,
                    width: constants.width - 160,
                }}
                    text={translate('detail.select_package')}
                    addSize={2}
                />
            </View>
        </>
    );
}

export default SimTitle;