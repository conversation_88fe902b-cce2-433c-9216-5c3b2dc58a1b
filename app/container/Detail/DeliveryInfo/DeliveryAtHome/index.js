/**
 * Sample React Native App
 *
 *
 * @format
 * @flow strict-local
 */

import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  TouchableOpacity,
  Keyboard,
  Alert,
  StyleSheet
} from 'react-native';
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { helper, storageHelper, dateHelper } from "@common";
import {
  BaseLoading,
  FieldInput,
  PickerLocation,
  MyText,
  showBlockUI,
  hideBlockUI,
  BouncyCheckboxCustom,
  Icon,
} from "@components";
import { v4 as uuidv4 } from 'uuid';
import { constants, STORAGE_CONST } from "@constants";
import StoreInfo from "./component/StoreInfo";
import ButtonAddToCart from "../component/ButtonAddToCart";
import RadioGender from "../../component/Radio/RadioGender";
import {
  getDistrict,
  getWard,
  getAllLocation
} from "../../../Location/action";
import ModalDatePicker from "../component/Modal/ModalDatePicker";
import { getCustomerByPhone, getCustomerByPhoneNew, getCustomerProfile, getGender, insertProfileReceive, set_map_customer_confirm_policy, updateProfileReceive } from "../../../ShoppingCart/action";
import { translate } from '@translate';
import { COLORS } from "@styles";
import ModalSelectStore from './component/ModalSelectStore';
import SelectedStoreInfo from './component/SelectedStoreInfo';
import DropDown from '../../../ShoppingCart/component/DropDown';
import { Text } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { TYPE_PROFILE } from '../../../../constants/constants';
import CustomerReceivePicker from './component/CustomerReceivePicker';
import AddressReceivePicker from './component/AddressReceicePicker';
import CustomerProfileInfo from '../../component/CustomerProfileInfo';
import ModalAddressReceive from './component/ModalAddressReceive';
import ModalCustomerReceive from './component/ModalCustomerReceive';


const TYPE_MODAL = {
  EDIT: 2,
  INSERT: 1
};

const DeliveryAtHome = ({
  dataStore,
  stateStoreAtHome,
  province,
  defaultDistrict,
  getDataStore,
  quantity,
  quantityInStock,
  productOrder,
  saleProgramID,
  onUpdateOutputStore,
  addToShoppingCart,
  disabled,
  defaultDelivery,
  defaultProvinceID,
  children,
  actionDetail,
  defaultCustomerPhone,
  getSummerFee = () => { },
  feePlusMoneyBO,
  onShowModalFees
}) => {
  const dispatch = useDispatch();
  const [provinceID, setProvinceID] = useState(defaultProvinceID);
  const [district, setDistrict] = useState([]);
  const [districtID, setDistrictID] = useState(0);
  const [ward, setWard] = useState([]);
  const [wardID, setWardID] = useState(0);
  const [indexPager, setIndexPager] = useState(0);
  const [isShowIndicator, setIsShowIndicator] = useState(false);

  let dataProfileSelected = useRef({});


  const [stateProfileReceivePicker, setStateProfileReceivePicker] = useState(
    {
      [TYPE_PROFILE.CUSTOMER_RECEIVE]:
      {
        isShow: false,
        itemSelected: {},
        title: "",
        labelButton: "",
        data: [],
      },
      [TYPE_PROFILE.ADDRESS_RECEIVE]:
      {
        isShow: false,
        itemSelected: {},
        title: "",
        labelButton: "",
        data: [],
      }
    }
  );

  const [stateModalProfileReceive, setStateModalProfileReceive] = useState({
    [TYPE_PROFILE.CUSTOMER_RECEIVE]:
    {
      isShow: false,
      typeModal: 0,
      itemSelected: {},
      title: ''
    },
    [TYPE_PROFILE.ADDRESS_RECEIVE]:
    {
      isShow: false,
      typeModal: 0,
      itemSelected: {},
      title: ''
    }
  });


  const [stateCustomerInfo, setStateCustomerInfo] = useState({
    profileID: null,
    customerPhone: "",
    customerName: "",
    gender: null,
  });

  const [gender, setGender] = useState(null);
  const [contactPhone, setContactPhone] = useState("");
  const [contactName, setContactName] = useState("");
  const [contactAddress, setContactAddress] = useState("");
  const [contactNote, setContactNote] = useState("");
  const [storeInfo, setStoreInfo] = useState({});
  const [deliveryTime, setDeliveryTime] = useState("");
  const [vehicleTypeID, setVehicleTypeID] = useState(0);
  const [customerInfoSelected, setCustomerInfoSelected] = useState(null);
  const [extraAddress, setExtraAddress] = useState("");
  const [isFocus, setIsFocus] = useState(false);


  const [minDate, setMinDate] = useState("");
  const [maxDate, setMaxDate] = useState("");
  const [dataDate, setDataDate] = useState({});
  const [currentDate, setCurrentDate] = useState("");
  const [currentHour, setCurrentHour] = useState("");
  const [isCallCRM, setIsCallCRM] = useState(false);

  let isAutoOpen = useRef(true);

  const [isVisible, setIsVisible] = useState(false);
  const { CRMGOIYKHOGIAOBACKUP } = global.config;

  const [isLock, setIsLock] = useState(false);
  const [isDeliveryStore, setIsDeliveryStore] = useState(false);
  const [deliveryStoreInfo, setDeliveryStoreInfo] = useState({});
  const [deliveryTypeInfo, setDeliveryTypeInfo] = useState({});
  const [deliveryTypes, setDeliveryTypes] = useState([]);

  const [disabledProfile, setDisableProfile] = useState(false);
  const [blockUI, setBlockUI] = useState(false);
  const [isSameCustomer, setIsSameCustomer] = useState(false);
  const [disabledPhone, setDisablePhone] = useState(false);

  const { storeID, languageID, moduleID } = useSelector((state) => state.userReducer);

  const { dataShoppingCart } = useSelector((_state) => _state.shoppingCartReducer);
  const { staffInfo } = useSelector((_state) => _state.staffPromotionReducer);
  const {
    ApplyPromotionToCustomerPhone
  } = dataShoppingCart;

  const getStoreShippingInfo = (eleStore) => {
    setIsDeliveryStore(false);
    const isNotNull = helper.IsNonEmptyArray(eleStore.suggestTimes);
    let isEmpty = true;
    // if (isNotNull) {
    //   const isHasData = eleStore.suggestTimes.find(ele => !ele.isWarning);
    //   isEmpty = !isHasData;
    // }
    if (isEmpty) {
      showBlockUI();
      actionDetail.getStoreInfoShipping({
        "imei": productOrder.imei,
        "productID": productOrder.productID,
        "inventoryStatusID": productOrder.inventoryStatusID,
        "salePriceVAT": productOrder.salePriceVAT,
        "stockQuantity": quantityInStock,
        "quantity": quantity,
        "saleProgramID": saleProgramID,
        "provinceID": provinceID,
        "districtID": districtID,
        "wardID": wardID,
        "deliveryAddress": contactAddress,
        "storeID": eleStore.storeID,
        "distance": eleStore.distance
      }).then(({ storeInfo, storeDelivery }) => {
        hideBlockUI();
        setDeliveryStoreInfo(storeDelivery);
        setIsDeliveryStore(false);
        setStoreInfo(storeInfo);
        setVehicleTypeID(storeInfo.vehicleTypeID);
      }).catch(msgError => {
        hideBlockUI();
        setDeliveryStoreInfo({});
        setIsDeliveryStore(false);
        setStoreInfo(eleStore);
        setVehicleTypeID(eleStore.vehicleTypeID);
      });
    }
    else {
      setDeliveryStoreInfo({});
      setIsDeliveryStore(false);
      setStoreInfo(eleStore);
      setVehicleTypeID(eleStore.vehicleTypeID);
    }
  };

  const getInfoDelivery = (deliveryInfo) => {
    const { coordinates } = stateProfileReceivePicker[TYPE_PROFILE.ADDRESS_RECEIVE].itemSelected ?? { coordinates: null }
    const deliveryBody = !helper.IsEmptyObject(deliveryInfo) ? { ...deliveryInfo } : {
      "provinceID": provinceID,
      "districtID": districtID,
      "wardID": wardID,
      "deliveryAddress": contactAddress,
    };
    setIsCallCRM(false);
    actionDetail.getDataAtHome({
      "imei": productOrder.imei,
      "productID": productOrder.productID,
      "inventoryStatusID": productOrder.inventoryStatusID,
      "salePriceVAT": productOrder.salePriceVAT,
      "stockQuantity": quantityInStock,
      "quantity": quantity,
      "saleProgramID": saleProgramID,
      "coordinates": coordinates,
      ...deliveryBody
    }).then(({ isBackupCRM, storeDelivery }) => {
      setDeliveryStoreInfo(storeDelivery);
      if (isBackupCRM) {
        setIsCallCRM(true);
        setIsVisible(true);
      }
    });
  };

  const onchangeDeliveryType = (ele) => {
    setDeliveryTypeInfo(ele);
  };

  const effectChangeProvince = () => {
    if (provinceID > 0) {
      if (provinceID != defaultProvinceID) {
        getDataDistrict(provinceID);
      }
      else {
        setDistrict(defaultDistrict);
        setIndexPager(1);
      }
    }
  };

  const effectChangeDistrict = () => {
    if (districtID > 0) {
      getDataWard(provinceID, districtID);
    }
  };

  const effectChangeWard = () => {
    if (wardID > 0) {
      if (CRMGOIYKHOGIAOBACKUP == "1") {
        if (isAutoOpen.current) {
          setIsVisible(true);
        } else {
          isAutoOpen.current = true;
        }
      } else {
        getInfoDelivery();
      }
    }
  };

  const effectChangeDataStore = () => {
    if (wardID > 0 && dataStore.length > 0) {
      setStoreInfo(dataStore[0]);
      setVehicleTypeID(dataStore[0].vehicleTypeID);
    }
  };

  const effectChangeStore = () => {
    if (!helper.IsEmptyObject(storeInfo)) {
      getSuggestTimes(storeInfo);
      setIsLock(false);
      if (storeInfo.deliveryTypeID == deliveryTypeInfo.deliveryTypeID) {
        effectChangeDeliveryType();
      }
    }
  };

  const effectChangeDeliveryType = () => {
    if (deliveryTypeInfo.deliveryTypeID) {
      storeInfo.deliveryTypeID = deliveryTypeInfo.deliveryTypeID;
      storeInfo.deliveryTypeName = deliveryTypeInfo.deliveryTypeName;
      onUpdateOutputStore(storeInfo);
    }
  };

  useEffect(
    effectChangeProvince,
    [provinceID]
  );

  useEffect(
    effectChangeDistrict,
    [districtID]
  );

  useEffect(
    effectChangeWard,
    [wardID, saleProgramID]
  );

  useEffect(
    effectChangeDataStore,
    [dataStore]
  );

  useEffect(
    effectChangeStore,
    [storeInfo]
  );

  useEffect(() => {
    if (helper.IsEmptyObject(deliveryStoreInfo)) return;
    getSuggestTimes(isDeliveryStore ? deliveryStoreInfo : storeInfo);
  }, [isDeliveryStore]);

  useEffect(
    effectChangeDeliveryType,
    [deliveryTypeInfo.deliveryTypeID]
  );

  useEffect(() => {
    if (helper.IsNonEmptyString(currentHour)) {
      const {
        storeRequests,
        storeID,
        deliveryTypeID,
        distance,
        shippingCost,
      } = storeInfo;
      console.log("🚀 ~ useEffect ~ storeInfo:", storeInfo);
      // const shippingCost = getShippingCost(storeRequests);
      const delivery = {
        "deliveryStoreID": storeID,
        "deliveryTypeID": isDeliveryStore ? 2 : deliveryTypeInfo.deliveryTypeID,
        "deliveryVehicles": vehicleTypeID,
        "deliveryDistance": distance,
        "shippingCost": shippingCost,
        "deliveryTime": deliveryTime,
        "deliveryProvinceID": provinceID,
        "deliveryDistrictID": districtID,
        "deliveryWardID": wardID,
        "deliveryAddress": contactAddress,
        "contactGender": gender,
        "contactPhone": contactPhone,
        "contactName": contactName,
        "customerNote": contactNote,
        "isStoreDelivery": isDeliveryStore
      };
      getSummerFee({ delivery, storeRequests });
    }
  }, [deliveryTypeInfo]);


  const handleCustomerInfo = (customerInfo) => {
    const {
      Gender,
      ContactPhone,
      ContactName,
      ContactAddress,
      CustomerAddress
    } = customerInfo;
    setContactPhone(ContactPhone);
    setGender(Gender);
    setContactName(ContactName);
    setContactAddress(CustomerAddress || ContactAddress);
    setCustomerInfoSelected(customerInfo.id);
  };

  const getDefaultInfo = (provinceID, districtID, wardID) => {
    if (wardID > 0) {
      showBlockUI();
      setExtraAddress(` ${defaultDelivery.deliveryWardName} - ${defaultDelivery.deliveryDistrictName} - ${defaultDelivery.deliveryProvinceName}`);
      getAllLocation(provinceID, districtID).then(({ dataDistrict, dataWard }) => {
        hideBlockUI();
        setProvinceID(provinceID);
        setDistrictID(districtID);
        setWardID(wardID);
        setDistrict(dataDistrict);
        setWard(dataWard);
      }).catch(msgError => {
        hideBlockUI();
        console.log("defaultDelivery", defaultDelivery);
      });
    }
  };

  const getDataDistrict = (provinceID) => {
    setIsShowIndicator(true);
    getDistrict(provinceID).then(data => {
      setDistrict(data);
      setIndexPager(1);
      setIsShowIndicator(false);
    }).catch(msgError => {
      Alert.alert(translate('common.notification_uppercase'), msgError,
        [
          {
            text: translate('common.btn_skip'),
            style: "cancel",
            onPress: () => setIsShowIndicator(false)
          },
          {
            text: translate('common.btn_notify_try_again'),
            style: "default",
            onPress: () => getDataDistrict(provinceID)
          }
        ]
      );
    });
  };

  const getDataWard = (provinceID, districtID) => {
    setIsShowIndicator(true);
    getWard(provinceID, districtID).then(data => {
      setWard(data);
      setIndexPager(2);
      setIsShowIndicator(false);
    }).catch(msgError => {
      Alert.alert(translate('common.notification_uppercase'), msgError,
        [
          {
            text: translate('common.btn_skip'),
            style: "cancel",
            onPress: () => setIsShowIndicator(false)
          },
          {
            text: translate('common.btn_notify_try_again'),
            style: "default",
            onPress: () => getDataWard(provinceID, districtID)
          }
        ]
      );
    });
  };

  const getSuggestTimes = (info) => {
    const { suggestTimes } = info;
    const newSuggestTimes = isDeliveryStore ? deliveryStoreInfo.suggestTimes : suggestTimes;
    if (helper.IsNonEmptyArray(newSuggestTimes)) {
      const {
        beginDate,
        endDate,
        data,
        defaultDate
      } = helper.getDateTimeSuggest(newSuggestTimes);
      const { deliveryValue } = beginDate;
      const min = deliveryValue.split("T")[0];
      const max = endDate.deliveryValue.split("T")[0];
      setMinDate(min);
      setMaxDate(max);
      setDataDate(data);
      if (defaultDate) {
        const current = defaultDate.deliveryValue.split("T")[0];
        const hour = defaultDate ? defaultDate.deliveryText : "";
        const deliveryTime = defaultDate ? defaultDate.deliveryValue : "";
        setCurrentDate(current);
        setCurrentHour(hour);
        setDeliveryTime(deliveryTime);
        setDeliveryTypeInfo(defaultDate);
        setDeliveryTypes(defaultDate.deliveryTypelst);
      } else {
        setCurrentDate(min);
        setCurrentHour("");
        setDeliveryTime("");
      }
    }
    else {
      Alert.alert("", translate('detail.expired_time'),
        [
          {
            text: "OK",
            style: "default",
            onPress: () => {
              setStoreInfo({});
              setVehicleTypeID(0);
            }
          }
        ]
      );
    }
  };


  const renderContactInfo = () => {
    return (
      <>
        <View style={{
          width: constants.width - 20,
          flexDirection: "row",
          paddingVertical: 4,
          justifyContent: "space-between",
          // backgroundColor: COLORS.bgFF0000
        }}>
          <RadioGender
            gender={gender}
            onSwitchGender={(value) => {
              setGender(value);
            }}
          />
          <TouchableOpacity style={{
            justifyContent: "center",
            alignItems: "center",
          }}
            onPress={getOldCustomerInfo}
          >
            <MyText style={{
              color: COLORS.txtFFA500,
              textDecorationLine: 'underline',
              fontWeight: "bold",
            }}
              text={translate('detail.old_customer')}
            />
          </TouchableOpacity>
        </View>

        <FieldInput
          styleInput={{
            borderWidth: 1,
            borderRadius: 4,
            borderColor: COLORS.bdCCCCCC,
            marginVertical: 5,
            paddingHorizontal: 10,
            backgroundColor: COLORS.bgFFFFFF,
            paddingVertical: 8,
          }}
          placeholder={translate('detail.text_input_phone_number_contact')}
          value={contactPhone}
          onChangeText={(text) => {
            const regExpPhone = new RegExp(/^[0]\d{0,9}$/);
            const isValidate = regExpPhone.test(text) || (text == "");
            if (isValidate) {
              setContactPhone(text);
            }
          }}
          keyboardType={"numeric"}
          returnKeyType={"done"}
          blurOnSubmit={true}
          onBlur={() => getContactInfo(contactPhone)}
          width={constants.width - 20}
          height={40}
          clearText={() => {
            setContactPhone("");
          }}
        />

        <FieldInput
          styleInput={{
            borderWidth: 1,
            borderRadius: 4,
            borderColor: COLORS.bdCCCCCC,
            marginVertical: 5,
            paddingHorizontal: 10,
            backgroundColor: COLORS.bgFFFFFF,
            paddingVertical: 8
          }}
          placeholder={translate('detail.text_input_name_contact')}
          value={contactName}
          onChangeText={(text) => {
            if (helper.isValidateCharVN(text)) {
              setContactName(text);
            }
          }}
          returnKeyType={"default"}
          width={constants.width - 20}
          height={40}
          clearText={() => {
            setContactName("");
          }}
          maxLength={50}
        />

        <FieldInput
          styleInput={{
            borderWidth: 1,
            borderRadius: 4,
            borderColor: COLORS.bdCCCCCC,
            marginVertical: 5,
            paddingHorizontal: 10,
            backgroundColor: COLORS.bgFFFFFF,
            justifyContent: 'center',
            paddingVertical: 8
          }}
          textAlignVertical={'center'}
          underlineColorAndroid={'transparent'}
          placeholder={translate('detail.text_input_address_contact')}
          value={contactAddress}
          onChangeText={(text) => {
            if (helper.isValidateCharVN(text)) {
              setContactAddress(text);
            }
          }}
          returnKeyType={"default"}
          blurOnSubmit={true}
          onSubmitEditing={() => { Keyboard.dismiss(); }}
          width={constants.width - 20}
          multiline={true}
          height={40}
          clearText={() => {
            setContactAddress("");
          }}
          maxLength={300}
        />
      </>
    );
  };

  // const getContactInfo = (phoneNumber) => {
  //   const regExpPhone = new RegExp(/^[0]\d{8,9}$/);
  //   const isValidate = regExpPhone.test(phoneNumber);
  //   if (isValidate) {
  //     getCustomerByPhoneNew(phoneNumber).then(info => {
  //       const NewCustomerInfo = info.map((item) => {
  //         return (
  //           {
  //             ...item,
  //             id: uuidv4()
  //           }
  //         )
  //       })
  //       setContactPhone(phoneNumber);
  //       setGender(NewCustomerInfo[0].Gender);
  //       setContactName(NewCustomerInfo[0].CustomerName);
  //       setContactAddress(NewCustomerInfo[0].CustomerAddress || contactAddress);
  //       setDataCustomerInfo(NewCustomerInfo);
  //       setCustomerInfoSelected(NewCustomerInfo[0].id)
  //     })
  //   }
  // }

  const getOldCustomerInfo = () => {
    if (contactPhone) {
      getContactInfo(contactPhone);
    }
    else {
      storageHelper.getItem(STORAGE_CONST.CUSTOMER_INFO).then(result => {
        if (helper.IsNonEmptyString(result)) {
          const dataTopInfo = JSON.parse(result);
          const customerInfo = dataTopInfo.find(ele => helper.IsEmptyString(ele.taxID));
          if (customerInfo) {
            const {
              gender,
              customerName,
              customerAddress,
              customerPhone,
            } = customerInfo;
            setGender(gender);
            setContactPhone(customerPhone);
            setContactName(customerName);
            setContactAddress(customerAddress || contactAddress);
          }
        }
      }).catch(
        error => {
          console.log("getOldCustomerInfo error", error);
        }
      );
    }
  };

  const onAddToCart = () => {
    Keyboard.dismiss();
    const isValidate = checkValidateInfo({
      contactAddress,
      contactName,
      contactPhone,
      deliveryTime,
      gender
    });
    if (isValidate) {
      const {
        storeRequests,
        storeID,
        deliveryTypeID,
        distance,
        shippingCost,
        isShipPartner
      } = storeInfo;
      // const shippingCost = getShippingCost(storeRequests);
      const delivery = {
        "deliveryStoreID": storeID,
        "deliveryTypeID": isDeliveryStore ? 2 : deliveryTypeInfo.deliveryTypeID,
        "deliveryVehicles": vehicleTypeID,
        "deliveryDistance": distance,
        "shippingCost": shippingCost,
        "deliveryTime": deliveryTime,
        "deliveryProvinceID": provinceID,
        "deliveryDistrictID": districtID,
        "deliveryWardID": wardID,
        "deliveryAddress": contactAddress,
        "contactGender": gender,
        "contactPhone": contactPhone,
        "contactName": contactName,
        "customerNote": contactNote,
        "isStoreDelivery": isDeliveryStore,
        "customerPhone": stateCustomerInfo.customerPhone,
      };

      if (!helper.IsEmptyObject(stateProfileReceivePicker[TYPE_PROFILE.CUSTOMER_RECEIVE].itemSelected)) {
        dataProfileSelected.current = {
          ...dataProfileSelected.current,
          [TYPE_PROFILE.CUSTOMER_RECEIVE]: [stateProfileReceivePicker[TYPE_PROFILE.CUSTOMER_RECEIVE].itemSelected]
        };
      }
      if (!helper.IsEmptyObject(stateProfileReceivePicker[TYPE_PROFILE.ADDRESS_RECEIVE].itemSelected)) {
        dataProfileSelected.current = {
          ...dataProfileSelected.current,
          [TYPE_PROFILE.ADDRESS_RECEIVE]: [stateProfileReceivePicker[TYPE_PROFILE.ADDRESS_RECEIVE].itemSelected]
        };
      }
      if (isSameCustomer) {
        delete dataProfileSelected.current?.[TYPE_PROFILE.CUSTOMER_RECEIVE];
      }
      addToShoppingCart({
        storeRequests: storeRequests,
        delivery: delivery,
        dataProfileSelected: dataProfileSelected.current,
        isShipPartner
      });
    }
  };

  const onSelectOutputStore = (store) => {
    const { isSetupProduct } = productOrder;
    const suggestTime = dateHelper.initResponseSuggestTime();
    const storeData = {
      "storeID": store.storeID,
      "storeName": store.storeName,
      "storeAddress": store.storeAddress,
      "deliveryTypeID": isSetupProduct ? 281 : 2,
      "deliveryTypeName": isSetupProduct ? translate('offlineCart.dedicated_deliver') : translate('offlineCart.store_deliver'),
      "quantity": quantity,
      "suggestTimes": suggestTime,
      "vehicleTypeID": isSetupProduct ? 2 : 1,
      "vehicleTypeName": isSetupProduct ? translate("detail.truck") : translate("detail.motorbike"),
      "distance": 0,
      "shippingCost": 0,
      "deposit": 0,
      "depositTime": "",
      "deliveryTime": "",
      "storeRequests": null,
      "stockQuantity": 0,
      "getStockType": 0,
      "messageError": "",
      "transportTypeID": 0
    };
    setStoreInfo(storeData);
    setVehicleTypeID(storeData.vehicleTypeID);
  };

  const getContactInfo = (phoneNumber) => {
    const {
      customerPhone,
    } = stateCustomerInfo;
    const phoneNumberInput = phoneNumber || customerPhone
    setStateCustomerInfo({
      ...stateCustomerInfo,
      customerName: "",
      gender: null,
      profileID: 0
    });

    const regExpPhone = new RegExp(/^[0]\d{8,9}$/);
    const isValidate = regExpPhone.test(phoneNumberInput);
    if (isValidate) {
      getCustomerProfile({ phoneNumber: phoneNumberInput, typeProfile: TYPE_PROFILE.CUSTOMER }).then(customerProfile => {
        if (customerProfile == null) return;
        const customerInfo = { ...customerProfile[0] };
        setStateCustomerInfo({
          customerName: customerInfo.customerName || "",
          customerPhone: phoneNumberInput,
          gender: customerInfo.gender,
          profileID: customerInfo.profileId
        });
        if (isSameCustomer) {
          setContactPhone(phoneNumberInput);
          setContactName(customerInfo.customerName || "");
          setGender(customerInfo.gender);
        }
        // dispatch(set_map_customer_confirm_policy({
        //   type: TYPE_PROFILE.CUSTOMER,
        //   infoCustomerCRM: customerProfile
        // }));
        // setContactPhone(contactPhone);
        // setGender(getGender(customerInfo.gender));
        // setContactName(customerInfo.customerName);
      }).catch(err => {
        console.log("🚀 ~ getCustomerProfile ~ err:", err);
      }).finally(() => {
        setDisableProfile(false);
      });
    }
  };
  const getProfileAddressReceive = () => {
    Keyboard.dismiss();
    const {
      customerPhone,
      profileID
    } = stateCustomerInfo;
    if (!!profileID) {
      showBlockUI();
      getCustomerProfile({ phoneNumber: customerPhone, typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE }).then(address => {
        hideBlockUI();
        if (address == null) return handleStateModalProfileReceive({
          title: "Thêm địa chỉ giao hàng",
          typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE,
          typeModal: TYPE_MODAL.INSERT,
          itemSelected: {},
          isShow: true
        });
        handleStateProfileReceivePicker({ isShow: true, typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE, data: address, title: "Địa chỉ giao hàng" });
      }).catch(msgError => {
        Alert.alert(translate('common.notification_uppercase'), msgError,
          [
            {
              text: translate('common.btn_skip'),
              style: "cancel",
              onPress: hideBlockUI
            },
            {
              text: translate('common.btn_notify_try_again'),
              style: "default",
              onPress: getProfileAddressReceive
            }
          ]
        );
      });
    }
    else {
      handleStateModalProfileReceive({
        title: "Thêm địa chỉ giao hàng",
        typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE,
        typeModal: TYPE_MODAL.INSERT,
        isShow: true
      });

    }

  };
  const getProfileCustomerReceive = () => {
    Keyboard.dismiss();
    const {
      customerPhone,
      profileID
    } = stateCustomerInfo;
    if (!!profileID) {
      showBlockUI();
      getCustomerProfile({ phoneNumber: customerPhone, typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE }).then(customerProfile => {
        hideBlockUI();
        if (customerProfile == null) return handleStateModalProfileReceive({
          title: "Thêm người nhận",
          typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE,
          typeModal: TYPE_MODAL.INSERT,
          itemSelected: {},
          isShow: true
        });
        handleStateProfileReceivePicker({ isShow: true, typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE, data: customerProfile, title: "Người Nhận" });
      }).catch(msgError => {

        Alert.alert(translate('common.notification_uppercase'), msgError,
          [
            {
              text: translate('common.btn_skip'),
              style: "cancel",
              onPress: hideBlockUI
            },
            {
              text: translate('common.btn_notify_try_again'),
              style: "default",
              onPress: getProfileCustomerReceive
            }
          ]
        );
      });
    }
    else {
      handleStateModalProfileReceive({
        title: "Thêm người nhận",
        typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE,
        typeModal: TYPE_MODAL.INSERT,
        isShow: true
      });
    }

  };

  const handleAPICustomerReceive = (newCustomer) => {
    const { typeModal } = stateModalProfileReceive[TYPE_PROFILE.CUSTOMER_RECEIVE];
    if (typeModal === TYPE_MODAL.INSERT) return handleInsertReceiveCustomer(newCustomer);
    handleUpdateReceiveCustomer(newCustomer);
  };
  const handleInsertReceiveCustomer = (newCustomer) => {
    if (!!(stateCustomerInfo.profileID)) {
      const {
        profileID
      } = stateCustomerInfo;
      setBlockUI(true);
      const body = {
        "loginStoreId": storeID,
        "languageID": languageID,
        "moduleID": moduleID,
        "profile": {
          "6": [
            {
              "receiverId": null,
              "receiverGender": newCustomer.gender,
              "receiverName": newCustomer.contactName,
              "receiverPhone": newCustomer.contactPhone,
              "profileId": profileID,
              "type": TYPE_PROFILE.CUSTOMER_RECEIVE,
              "versionCode": null,
              "isModify": null,
              "isSigned": null,
              "signatureId": null
            }
          ]
        },
        typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE
      };
      insertProfileReceive(body).then(customerReceive => {
        setBlockUI(false);
        handleStateProfileReceivePicker({
          typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE,
          isShow: false,
          itemSelected: customerReceive

        });
        handleStateModalProfileReceive({
          typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE,
          isShow: false,
          itemSelected: customerReceive
        });
      }).catch((msgError) => {
        Alert.alert(translate('common.notification_uppercase'), msgError,
          [
            {
              text: translate('common.btn_skip'),
              style: "cancel",
              onPress: () => setBlockUI(false)

            },
            {
              text: translate('common.btn_notify_try_again'),
              style: "default",
              onPress: () => handleInsertReceiveCustomer(newCustomer)
            }
          ]
        );

      });
    }
    else {
      const defaultCustomerReceive = {
        "receiverId": null,
        "receiverGender": newCustomer.gender,
        "receiverName": newCustomer.contactName,
        "receiverPhone": newCustomer.contactPhone,
        "profileId": null,
        "type": TYPE_PROFILE.CUSTOMER_RECEIVE,
        "versionCode": null,
        "isModify": null,
        "isSigned": null,
        "signatureId": null
      };
      handleStateModalProfileReceive({
        typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE,
        itemSelected: defaultCustomerReceive,
        isShow: false

      });
      handleStateProfileReceivePicker({
        typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE,
        itemSelected: defaultCustomerReceive,
        isShow: false
      });
    }


  };
  const handleUpdateReceiveCustomer = (newCustomer) => {
    const { itemSelected } = stateModalProfileReceive[TYPE_PROFILE.CUSTOMER_RECEIVE];
    const { receiverId, profileId, versionCode, isSigned, signatureId, isModify } = itemSelected;
    setBlockUI(true);
    const body = {
      "loginStoreId": storeID,
      "languageID": languageID,
      "moduleID": moduleID,
      "profile": {
        "6": [
          {
            "receiverId": receiverId,
            "receiverGender": newCustomer.gender,
            "receiverName": newCustomer.contactName,
            "receiverPhone": newCustomer.contactPhone,
            "profileId": profileId,
            "type": TYPE_PROFILE.CUSTOMER_RECEIVE,
            "versionCode": versionCode,
            "isModify": isModify,
            "isSigned": isSigned,
            "signatureId": signatureId
          }
        ]
      },
      typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE
    };
    updateProfileReceive(body).then(customerReceive => {
      setBlockUI(false);
      handleStateProfileReceivePicker({
        typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE,
        isShow: false,
        itemSelected: customerReceive
      });
      handleStateModalProfileReceive({
        typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE,
        isShow: false,
        itemSelected: customerReceive

      });
    }).catch((msgError) => {
      Alert.alert(translate('common.notification_uppercase'), msgError,
        [
          {
            text: translate('common.btn_skip'),
            style: "cancel",
            onPress: () => setBlockUI(false)

          },
          {
            text: translate('common.btn_notify_try_again'),
            style: "default",
            onPress: () => handleUpdateReceiveCustomer(newCustomer)
          }
        ]
      );

    });
  };
  const handleAPIAddressReceive = (newAddress) => {
    const { typeModal } = stateModalProfileReceive[TYPE_PROFILE.ADDRESS_RECEIVE];
    if (typeModal === TYPE_MODAL.INSERT) return handleInsertAddressCustomer(newAddress);
    handleUpdateAddressCustomer(newAddress);
  };
  const handleInsertAddressCustomer = (newAddress) => {
    const {
      address,
      wardID,
      districtID,
      provinceID,
      provinceName,
      districtName,
      wardName
    }
      = newAddress;
    const { profileID } = stateCustomerInfo;
    if (!!(stateCustomerInfo.profileID)) {
      if (address.length <= 3) {
        return Alert.alert("", "Vui lòng nhập số nhà, tên đường nhiều hơn 3 kí tự!");
      }
      setBlockUI(true);
      const body = {
        "loginStoreId": storeID,
        "languageID": languageID,
        "moduleID": moduleID,
        "profile": {
          [TYPE_PROFILE.ADDRESS_RECEIVE]: [
            {
              "deliveryId": null,
              "address": address,
              "wardId": wardID,
              "provinceId": provinceID,
              "countryId": 2,
              "districtId": districtID,
              "profileId": profileID,
              "type": TYPE_PROFILE.ADDRESS_RECEIVE,
              "versionCode": null,
              "isModify": null,
              "isSigned": null,
              "signatureId": null,
              "provinceName": provinceName,
              "districtName": districtName,
              "wardName": wardName,
            }
          ]
        },
        typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE
      };
      insertProfileReceive(body).then(customerReceive => {
        const { wardId, provinceId, districtId } = customerReceive;
        setBlockUI(false);
        handleStateProfileReceivePicker({
          typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE,
          isShow: false,
          itemSelected: customerReceive
        });
        handleStateModalProfileReceive({
          typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE,
          isShow: false,
          itemSelected: customerReceive
        });

        setProvinceID(provinceId);
        setDistrictID(districtId);
        setWardID(
          wardId
        );
        setContactAddress(address);
      }).catch((msgError) => {
        Alert.alert(translate('common.notification_uppercase'), msgError,
          [
            {
              text: translate('common.btn_skip'),
              style: "cancel",
              onPress: () => setBlockUI(false)

            },
            {
              text: translate('common.btn_notify_try_again'),
              style: "default",
              onPress: () => handleInsertAddressCustomer(newAddress)
            }
          ]
        );

      });

    }
    else {
      const defaultAddress = {
        address: address,
        countryId: null,
        deliveryId: null,
        districtId: districtID,
        isModify: null,
        isSigned: null,
        profileId: null,
        provinceId: provinceID,
        signatureId: null,
        type: TYPE_PROFILE.ADDRESS_RECEIVE,
        versionCode: null,
        wardId: wardID,
        wardName,
        districtName,
        provinceName
      };
      handleStateModalProfileReceive({
        typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE,
        itemSelected: defaultAddress,
        isShow: false
      });
      handleStateProfileReceivePicker({
        typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE,
        itemSelected: defaultAddress,
        isShow: false
      });

      setProvinceID(provinceID);
      setDistrictID(districtID);
      setWardID(
        wardID
      );
      setContactAddress(address);
    }

  };
  const handleUpdateAddressCustomer = (newAddress) => {
    const { itemSelected } = stateModalProfileReceive[TYPE_PROFILE.ADDRESS_RECEIVE];
    const { address,
      districtID,
      provinceID,
      wardID,
      provinceName,
      districtName,
      wardName
    } = newAddress;
    const { deliveryId, profileId, versionCode, isSigned, signatureId, isModify } = itemSelected;
    setBlockUI(true);
    const body = {
      "loginStoreId": storeID,
      "languageID": languageID,
      "moduleID": moduleID,
      "profile": {
        "2": [
          {
            "deliveryId": deliveryId,
            "address": address,
            "wardId": wardID,
            "provinceId": provinceID,
            "countryId": "2",
            "districtId": districtID,
            "profileId": profileId,
            "type": 2,
            "versionCode": versionCode,
            "isModify": isModify,
            "isSigned": isSigned,
            "signatureId": signatureId,
            provinceName,
            districtName,
            wardName
          }
        ]
      },
      typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE
    };
    updateProfileReceive(body).then(customerReceive => {
      setBlockUI(false);
      handleStateModalProfileReceive({
        typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE,
        isShow: false,
        itemSelected: customerReceive,
      });
      handleStateProfileReceivePicker({
        typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE,
        itemSelected: customerReceive,
        isShow: false
      });

    }).catch((msgError) => {
      Alert.alert(translate('common.notification_uppercase'), msgError,
        [
          {
            text: translate('common.btn_skip'),
            style: "cancel",
            onPress: () => setBlockUI(false)

          },
          {
            text: translate('common.btn_notify_try_again'),
            style: "default",
            onPress: () => handleUpdateAddressCustomer(newAddress)
          }
        ]
      );

    });
  };

  const handleHideModal = (typeProfile) => {
    setStateModalProfileReceive({
      ...stateModalProfileReceive, [typeProfile]: {
        ...stateModalProfileReceive[typeProfile],
        isShow: false,
        typeModal: 0,
        title: '',
      }
    });
  };

  const handleStateModalProfileReceive = (newState) => {
    const { typeProfile } = newState;
    setStateModalProfileReceive({
      ...stateModalProfileReceive, [typeProfile]: {
        ...stateModalProfileReceive[typeProfile],
        ...newState
      }
    });
  };

  const handleStateProfileReceivePicker = (newState) => {
    const { typeProfile } = newState;
    setStateProfileReceivePicker({
      ...stateProfileReceivePicker, [typeProfile]: {
        ...stateProfileReceivePicker[typeProfile],
        ...newState
      }
    });
  };

  const handleClearDataDelivery = () => {
    const {
      customerPhone,
    } = stateCustomerInfo;
    setDisableProfile(true);
    setIsSameCustomer(false);
    handleStateModalProfileReceive({
      typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE,
      isShow: false,
      typeModal: 0,
      itemSelected: {},
      title: ''
    });
    handleStateModalProfileReceive({
      typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE,
      isShow: false,
      typeModal: 0,
      itemSelected: {},
      title: ''
    });
    handleStateProfileReceivePicker({
      typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE,
      isShow: false,
      itemSelected: {},
      title: "",
      labelButton: "",
      data: [],
    });
    handleStateProfileReceivePicker({
      typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE,
      isShow: false,
      itemSelected: {},
      title: "",
      labelButton: "",
      data: [],
    });

    setProvinceID(0);
    setDistrictID(0);
    setWardID(
      0
    );
    setContactAddress("");
    setStateCustomerInfo({
      ...stateCustomerInfo,
      customerName: "",
      gender: null,
      profileID: 0
    });
    setExtraAddress("");
    setContactName("");
    setContactPhone("");
    if (helper.IsNonEmptyString(customerPhone)) return setContactPhone("");
  };

  useEffect(() => {
    if (helper.IsEmptyObject(stateProfileReceivePicker[TYPE_PROFILE.ADDRESS_RECEIVE].itemSelected)) return;
    const { provinceId, districtId, wardId, address, districtName, wardName, provinceName } = stateProfileReceivePicker[TYPE_PROFILE.ADDRESS_RECEIVE].itemSelected;
    setProvinceID(provinceId);
    setDistrictID(districtId);
    setWardID(wardId);
    setContactAddress(address);
    setExtraAddress(` ${wardName} - ${districtName} - ${provinceName}`);
    // if (wardId == wardID) {
    //   getInfoDelivery({ wardID: wardId, districtID: districtId, provinceID: provinceId, deliveryAddress: address });
    // }

  }, [stateProfileReceivePicker[TYPE_PROFILE.ADDRESS_RECEIVE].itemSelected]);

  useEffect(() => {
    if (helper.IsEmptyObject(stateProfileReceivePicker[TYPE_PROFILE.CUSTOMER_RECEIVE].itemSelected)) return;
    const { receiverGender, receiverName, receiverPhone } = stateProfileReceivePicker[TYPE_PROFILE.CUSTOMER_RECEIVE].itemSelected;
    setContactName(receiverName);
    setContactPhone(receiverPhone);
    setGender(receiverGender);
  }, [stateProfileReceivePicker[TYPE_PROFILE.CUSTOMER_RECEIVE].itemSelected]);

  useEffect(() => {
    const {
      customerPhone,
      customerName,
      gender
    } = stateCustomerInfo;
    if (!isSameCustomer) return setContactPhone("");
    setContactPhone(customerPhone);
    setContactName(customerName);
    setGender(gender);
  }, [isSameCustomer]);

  useEffect(() => {
    const {
      customerPhone,
    } = stateCustomerInfo;
    if (!helper.IsNonEmptyString(customerPhone)) return setDisableProfile(true);
    const isValidatePhone = helper.isValidatePhone(customerPhone);
    if (isValidatePhone) {
      // if (isFocus) return
      getContactInfo();
    }
    else {
      handleClearDataDelivery();
    }
  }, [stateCustomerInfo.customerPhone]);

  const effectSetDefaultInfo = () => {
    if (helper.IsNonEmptyArray(dataShoppingCart.SaleOrderDetails)) {
      const lastSaleOrderDetail = dataShoppingCart.SaleOrderDetails[0];
      const { Profile } = lastSaleOrderDetail;
      Object.entries(Profile).forEach(([key, value]) => {
        if (helper.IsEmptyObject(value?.[0])) return;
        dataProfileSelected.current = {
          ...dataProfileSelected.current,
          [key]: [value[0]]
        };
      });
    }

    setStateCustomerInfo({
      ...stateCustomerInfo,
      customerName: defaultDelivery.contactName || staffInfo.CustomerName,
      customerPhone: defaultCustomerPhone || defaultDelivery.customerPhone || staffInfo.CustomerPhone,
      gender: defaultDelivery.gender || staffInfo.Gender
    });
    if (!!defaultCustomerPhone || !!ApplyPromotionToCustomerPhone || !!staffInfo.CustomerPhone) {
      setDisablePhone(true);
    }
    else {
      setDisablePhone(false);
    }
    if (CRMGOIYKHOGIAOBACKUP == "1" && defaultDelivery.wardID > 0) {
      isAutoOpen.current = false;
    }
    getDefaultInfo(defaultDelivery.provinceID, defaultDelivery.districtID, defaultDelivery.wardID);
    if (!defaultCustomerPhone) {
      setGender(defaultDelivery.gender);
      setContactPhone(defaultDelivery.contactPhone);
      setContactName(defaultDelivery.contactName);
      setContactAddress(defaultDelivery.contactAddress);
    }
  };

  useEffect(
    effectSetDefaultInfo,
    [defaultDelivery, defaultCustomerPhone]
  );
  // useEffect(() => {
  //   console.log("🚀 ~ useEffect ~ defaultCustomerPhone:", !!defaultCustomerPhone)

  //   if (!!(defaultCustomerPhone)) {
  //     setStateCustomerInfo({
  //       ...stateCustomerInfo,
  //       customerPhone: defaultCustomerPhone,
  //     })
  //     setDisablePhone(true)

  //   }
  //   else {
  //     setStateCustomerInfo({
  //       ...stateCustomerInfo,
  //       // customerPhone: "",
  //     })
  //     setDisablePhone(false)
  //   }
  // }, [defaultCustomerPhone])




  return (
    <View style={{
      flex: 1,
    }}>
      <KeyboardAwareScrollView
        style={{
          flex: 1
        }}
        enableResetScrollToCoords={false}
        keyboardShouldPersistTaps="always"
        bounces={false}
        overScrollMode="always"
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        extraScrollHeight={60}
      >
        <View style={{
          width: constants.width,
          paddingVertical: 10,
          alignItems: "center"
        }}>
          {/* <PickerLocation
            dataProvince={{
              data: province,
              id: "provinceID",
              value: "provinceName"
            }}
            dataDistrict={{
              data: district,
              id: "districtID",
              value: "districtName"
            }}
            dataWard={{
              data: ward,
              id: "wardID",
              value: "wardName"
            }}
            wardID={wardID}
            districtID={districtID}
            provinceID={provinceID}
            onSelectProvince={(item) => {
              setProvinceID(item.provinceID);
              setDistrictID(0);
              setWardID(0);
            }}
            onSelectDistrict={(item) => {
              setDistrictID(item.districtID);
              setWardID(0);
            }}
            onSelectWard={(item) => {
              if (item.wardID != wardID) {
                setStoreInfo({});
                setVehicleTypeID(0);
                setWardID(item.wardID);
              }
            }}
            indexPager={indexPager}
            onShowPicker={(index) => {
              setIndexPager(index);
            }}
            updatePager={(index) => {
              setIndexPager(index);
            }}
            isShowIndicator={isShowIndicator}
          /> */}
          {/* {renderContactInfo()} */}
          <CustomerProfileInfo
            isSameCustomer={isSameCustomer}
            stateCustomerInfo={stateCustomerInfo}
            setStateCustomerInfo={setStateCustomerInfo}
            contactAddress={contactAddress + extraAddress}
            contactName={contactName}
            contactPhone={contactPhone}
            disabled={disabledProfile}
            getProfileInfo={getContactInfo}
            getProfileAddressReceive={getProfileAddressReceive}
            getProfileCustomerReceive={getProfileCustomerReceive}
            onPressSameCustomer={() => { setIsSameCustomer(!isSameCustomer); }}
            disabledPhone={disabledPhone}
            setIsFocus={setIsFocus}
            onClear={handleClearDataDelivery}
          />
          {
            wardID > 0 &&
            <BaseLoading
              isLoading={stateStoreAtHome.isFetching}
              isEmpty={!isCallCRM && stateStoreAtHome.isEmpty}
              textLoadingError={stateStoreAtHome.description}
              isError={!isCallCRM && stateStoreAtHome.isError}
              onPressTryAgains={getInfoDelivery}
              content={
                <View style={{
                  width: constants.width,
                  alignItems: "center"
                }}>
                  {
                    (CRMGOIYKHOGIAOBACKUP == "1" || isCallCRM)
                      ? <View style={{
                        width: constants.width - 20,
                        marginBottom: 8
                      }}>
                        {helper.IsEmptyObject(storeInfo) ?
                          <MyText
                            text={'Chọn kho xuất:'}
                            style={{
                              color: COLORS.txt0099E5,
                              fontWeight: 'bold',
                              textDecorationLine: "underline"
                            }}
                            onPress={() => setIsVisible(true)}
                          />
                          :
                          <SelectedStoreInfo
                            storeInfo={storeInfo}
                            onChangeVehicle={setVehicleTypeID}
                            vehicleTypeID={vehicleTypeID}
                            showModal={() => setIsVisible(true)}
                          />
                        }
                      </View>
                      : <StoreInfo
                        dataStore={dataStore}
                        storeInfo={storeInfo}
                        onChangeStore={(newStoreInfo) => {
                          if (!isLock) {
                            setIsLock(true);
                            getStoreShippingInfo(newStoreInfo);
                          }
                        }}
                        onChangeVehicle={setVehicleTypeID}
                        vehicleTypeID={vehicleTypeID}
                        isLock={isLock}
                        isDeliveryStore={isDeliveryStore}
                        deliveryTypeInfo={deliveryTypeInfo}
                        deliveryTypes={deliveryTypes}
                        onchangeDeliveryType={onchangeDeliveryType}
                      />
                  }


                  {/* {
                    feePlusMoneyAmount > 0 &&
                    <View style={{
                      width: constants.width - 20,
                      paddingBottom: 8,
                      flexDirection: "row",
                      justifyContent: "flex-end"
                    }}>
                      <MyText text={"Phí giao dự kiến: "} />
                      <MyText style={{ color: COLORS.txtF50537, fontWeight: "bold" }}
                        text={helper.convertNum(feePlusMoneyAmount)}
                      />
                    </View>
                  } */}

                  {
                    !helper.IsEmptyObject(storeInfo) &&
                    <View style={{
                      width: constants.width,
                      alignItems: "center"
                    }}>
                      {
                        !helper.IsEmptyObject(deliveryStoreInfo) &&
                        <View style={{ width: constants.width - 20, paddingBottom: 10 }}>
                          <BouncyCheckboxCustom
                            isChecked={isDeliveryStore}
                            onToggle={setIsDeliveryStore}
                            label="Hình thức giao: Siêu thị đi giao"
                            iconColor={COLORS.bgF49B0C}
                          />
                        </View>
                      }

                      <ModalDatePicker
                        key={"ModalDatePicker"}
                        minDate={minDate}
                        maxDate={maxDate}
                        dataDate={dataDate}
                        currentDate={currentDate}
                        currentHour={currentHour}
                        onChangeDay={(dateString) => {
                          setCurrentDate(dateString);
                          setCurrentHour("");
                          setDeliveryTime("");
                        }}
                        onChangeHour={(ele) => {
                          const {
                            deliveryValue,
                            deliveryText,
                            deliveryTypelst
                          } = ele;
                          setCurrentHour(deliveryText);
                          setDeliveryTime(deliveryValue);
                          setDeliveryTypeInfo(ele);
                          setDeliveryTypes(deliveryTypelst);
                        }}
                        actionDetail={actionDetail}
                        storeInfo={{
                          "imei": productOrder.imei,
                          "productID": productOrder.productID,
                          "inventoryStatusID": productOrder.inventoryStatusID,
                          "salePriceVAT": productOrder.salePriceVAT,
                          "stockQuantity": quantityInStock,
                          "quantity": quantity,
                          "saleProgramID": saleProgramID,
                          "provinceID": provinceID,
                          "districtID": districtID,
                          "wardID": wardID,
                          "deliveryAddress": contactAddress,
                          "storeID": storeInfo.storeID,
                          "distance": storeInfo.distance,
                          "deliveryTypeID": deliveryTypeInfo.deliveryTypeID,
                          "deliveryVehicles": storeInfo.vehicleTypeID,
                          "isCreatedPO": storeInfo.isCreatedPO
                        }}
                        updateData={setDataDate}
                        suggestTimeDay={storeInfo.suggestTimes}
                      />
                      {
                        !helper.IsEmptyObject(feePlusMoneyBO) && feePlusMoneyBO?.FeeMoney > 0 &&
                        <TouchableOpacity
                          onPress={onShowModalFees}
                          style={{
                            width: constants.width - 20,
                            paddingTop: 8,
                          }}>
                          <View style={{
                            flexDirection: "row",
                            justifyContent: "flex-end",
                            alignItems: "center",

                          }}>
                            <MyText style={{
                              textDecorationLine: 'underline',
                              textDecorationColor: COLORS.bg147EFB
                            }} text={"Phụ phí tạm tính: "} >
                              <MyText style={{
                                color: COLORS.txtF50537, fontWeight: "bold",
                              }}
                                text={helper.convertNum(feePlusMoneyBO.FeeMoney)}
                              />
                            </MyText>

                          </View>

                        </TouchableOpacity>
                      }
                      <FieldInput
                        styleInput={{
                          borderWidth: 1,
                          borderRadius: 4,
                          borderColor: COLORS.bdCCCCCC,
                          marginVertical: 10,
                          paddingHorizontal: 10,
                          backgroundColor: COLORS.bgFFFFFF,
                          justifyContent: 'center',
                          paddingVertical: 8
                        }}
                        textAlignVertical={'center'}
                        underlineColorAndroid={'transparent'}
                        placeholder={translate('detail.text_input_note')}
                        value={contactNote}
                        onChangeText={(text) => {
                          if (helper.isValidateCharVN(text)) {
                            setContactNote(text);
                          }
                        }}
                        returnKeyType={"default"}
                        blurOnSubmit={true}
                        onSubmitEditing={() => { Keyboard.dismiss(); }}
                        width={constants.width - 20}
                        multiline={true}
                        height={40}
                        clearText={() => {
                          setContactNote("");
                        }}
                        maxLength={500}
                      />
                      {
                        // PROMOTION_DELIVERY
                        helper.IsNonEmptyString(deliveryTime) &&
                        children
                      }
                      {<ButtonAddToCart
                        onAddToCart={onAddToCart}
                        disabled={disabled}
                      />}
                    </View>
                  }
                </View>
              }
            />
          }
        </View>
        {
          isVisible &&
          <ModalSelectStore
            isVisible={isVisible}
            hideModal={() => setIsVisible(false)}
            onSelectedStore={(store) => {
              onSelectOutputStore(store);
            }}
          />
        }
        {
          stateProfileReceivePicker[TYPE_PROFILE.ADDRESS_RECEIVE].isShow &&
          <AddressReceivePicker
            isShow={stateProfileReceivePicker[TYPE_PROFILE.ADDRESS_RECEIVE].isShow}
            hideModal={() => {
              handleStateProfileReceivePicker({ isShow: false, typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE });
            }}
            onSelect={(info) => {
              handleStateProfileReceivePicker({ isShow: false, itemSelected: info, typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE });
            }}
            valueSelect={stateProfileReceivePicker[TYPE_PROFILE.ADDRESS_RECEIVE].itemSelected}
            data={stateProfileReceivePicker[TYPE_PROFILE.ADDRESS_RECEIVE].data}
            title={stateProfileReceivePicker[TYPE_PROFILE.ADDRESS_RECEIVE].title}
            blockUI={blockUI}
            onInsertAddressReceive={() => {
              handleStateModalProfileReceive({ isShow: true, title: "Thêm địa chỉ giao hàng", typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE, typeModal: TYPE_MODAL.INSERT, itemSelected: {} });
            }}
            onUpdateAddressReceive={(item) => {
              handleStateModalProfileReceive({ isShow: true, title: "Chỉnh sửa địa chỉ nhận", typeProfile: TYPE_PROFILE.ADDRESS_RECEIVE, typeModal: TYPE_MODAL.EDIT, itemSelected: item });
            }}
            //props modal profile
            stateModalProfileReceive={stateModalProfileReceive}
            hideModalAddressReceive={(type) => {
              handleStateProfileReceivePicker({ typeProfile: type, isShow: false });
              handleStateModalProfileReceive({ typeProfile: type, isShow: false });
            }}
            handleAPIAddressReceive={handleAPIAddressReceive}
          />
        }
        {
          stateProfileReceivePicker[TYPE_PROFILE.CUSTOMER_RECEIVE].isShow &&
          <CustomerReceivePicker
            isShow={stateProfileReceivePicker[TYPE_PROFILE.CUSTOMER_RECEIVE].isShow}
            hideModal={() => {
              handleStateProfileReceivePicker({ isShow: false, typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE });
            }}
            onSelect={(info) => {
              handleStateProfileReceivePicker({ isShow: false, typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE, itemSelected: info });
            }}
            valueSelect={stateProfileReceivePicker[TYPE_PROFILE.CUSTOMER_RECEIVE].itemSelected}
            data={stateProfileReceivePicker[TYPE_PROFILE.CUSTOMER_RECEIVE].data}
            title={stateProfileReceivePicker[TYPE_PROFILE.CUSTOMER_RECEIVE].title}
            blockUI={blockUI}
            onInsertCustomerReceive={() => {
              handleStateModalProfileReceive({ isShow: true, title: "Thêm người nhận", typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE, typeModal: TYPE_MODAL.INSERT, itemSelected: {} });
            }}
            onUpdateCustomerReceive={(item) => {
              handleStateModalProfileReceive({ isShow: true, title: "Chỉnh sửa người nhận", typeProfile: TYPE_PROFILE.CUSTOMER_RECEIVE, typeModal: TYPE_MODAL.EDIT, itemSelected: item });
            }}
            // props modal profile
            stateModalProfileReceive={stateModalProfileReceive}
            handleAPICustomerReceive={handleAPICustomerReceive}
            hideModalCustomerReceive={(type) => {
              handleStateProfileReceivePicker({ typeProfile: type, isShow: false });
              handleStateModalProfileReceive({ typeProfile: type, isShow: false });
            }}
          />
        }

        {stateModalProfileReceive[TYPE_PROFILE.ADDRESS_RECEIVE].isShow && (
          <ModalAddressReceive
            isShow={stateModalProfileReceive[TYPE_PROFILE.ADDRESS_RECEIVE].isShow}
            hideModal={() =>
              handleHideModal(TYPE_PROFILE.ADDRESS_RECEIVE)
            }
            title={stateModalProfileReceive[TYPE_PROFILE.ADDRESS_RECEIVE].title}
            blockUI={blockUI}
            onPress={handleAPIAddressReceive}
            itemSelected={
              stateModalProfileReceive[TYPE_PROFILE.ADDRESS_RECEIVE].itemSelected
            }
          />
        )}

        {stateModalProfileReceive[TYPE_PROFILE.CUSTOMER_RECEIVE].isShow && (
          <ModalCustomerReceive
            isShow={stateModalProfileReceive[TYPE_PROFILE.CUSTOMER_RECEIVE].isShow}
            hideModal={() =>
              handleHideModal(TYPE_PROFILE.CUSTOMER_RECEIVE)
            }
            title={stateModalProfileReceive[TYPE_PROFILE.CUSTOMER_RECEIVE].title}
            blockUI={blockUI}
            onPress={handleAPICustomerReceive}
            itemSelected={
              stateModalProfileReceive[TYPE_PROFILE.CUSTOMER_RECEIVE].itemSelected
            }
          />
        )}



      </KeyboardAwareScrollView>
    </View>
  );
};

export default DeliveryAtHome;

const getShippingCost = (storeRequests) => {
  let sum = 0;
  if (helper.IsNonEmptyArray(storeRequests)) {
    storeRequests.forEach(ele => {
      const { shippingCost } = ele;
      sum += shippingCost;
    });
  }
  return sum;
};

export const checkValidateInfo = ({ contactPhone, contactName, contactAddress, deliveryTime, gender }) => {
  // const regExpPhone = new RegExp(/^[0]\d{8,9}$/);
  const isValidatePhone = helper.isValidatePhone(contactPhone);
  if (!helper.IsNonEmptyString(contactPhone)) {
    Alert.alert("", "Vui lòng nhập số điện thoại người nhận.");
    return false;
  }
  if (!isValidatePhone) {
    Alert.alert("", translate('detail.please_enter_10_digits_phone_number'));
    return false;
  }
  if (!helper.isValidatePhonePrefix(contactPhone)) {
    Alert.alert("", "Vui lòng nhập số điện thoại người mua.");
    return false;
  }
  if (!helper.IsNonEmptyString(contactName)) {
    Alert.alert("", "Vui lòng nhập tên người mua.");
    return false;
  }
  if (!helper.IsNonEmptyString(contactAddress)) {
    Alert.alert("", translate('detail.please_enter_delivery_address'));
    return false;
  }
  if (!helper.IsNonEmptyString(deliveryTime)) {
    Alert.alert("", translate('detail.please_select_delivery_time'));
    return false;
  }
  if (gender == null) {
    Alert.alert("", translate('shoppingCart.validation_gender'));
    return false;
  }

  return true;
};
