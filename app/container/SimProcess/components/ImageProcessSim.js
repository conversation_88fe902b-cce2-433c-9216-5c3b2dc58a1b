import React from 'react';
import {
    View,
    TouchableOpacity,
} from 'react-native';
import {
    MyText,
    Icon,
    ImageCDN
} from "@components";
import { constants } from '@constants';
import { translate } from '@translate';
import { COLORS } from "@styles";

const ImageProcessSim = function ({ props, onCamera, deleteImage, uploadImage, urlImageRemote, urlImageLocal, title, isRequired, type, name, isSign }) {
    return (
        <View style={{
            marginVertical: 0,
            paddingHorizontal: 10,
        }}
        >
            <MyText
                text={name}
                style={{ fontSize: 15, marginVertical: 5 }}
            />
            {
                (!urlImageRemote && !urlImageLocal)
                    ?
                    <TouchableOpacity style={{
                        flex: 1,
                        height: 150,
                        width: 150,
                        justifyContent: "center",
                        alignItems: "center",
                        alignSelf: "center",
                        backgroundColor: COLORS.btnF5F5F5,
                        marginHorizontal: 2
                    }}
                        onPress={onCamera}
                    >
                        <View style={{ justifyContent: "center", alignItems: "center" }}>
                            <Icon
                                iconSet={"Ionicons"}
                                name={isSign ? "pencil-outline" : "ios-camera"}
                                color={COLORS.icFFB23F}
                                size={60}
                            />
                            {!!title &&
                                <MyText
                                    text={title}
                                    style={{
                                        container: {
                                            width: constants.getSize(70),
                                            height: constants.getSize(30),
                                            marginVertical: constants.getSize(10)
                                        },
                                        text: {
                                            color: COLORS.txtFFFFFF,
                                            fontSize: 12
                                        }
                                    }}
                                    children={isRequired ? <MyText text={"*"} style={{ color: COLORS.txtFF0000, fontSize: 16 }} /> : null}
                                />
                            }
                        </View>
                    </TouchableOpacity>
                    :
                    <View style={{
                        flex: 1,
                        justifyContent: "center",
                        marginHorizontal: 2,
                        flexDirection: "row",
                    }}>
                        <View style={{
                            width: (type == 2 ? "100%" : constants.width - constants.getSize(60)),
                            height: 150,
                        }}>
                            <ImageCDN
                                style={{
                                    width: "auto",
                                    height: 150,
                                }}
                                uri={urlImageRemote ? urlImageRemote : urlImageLocal}
                                resizeMode={"contain"}
                            />
                        </View>
                        <TouchableOpacity style={{
                            padding: 5,
                            justifyContent: "center",
                            alignItems: "center",
                            position: "absolute",
                            top: 0,
                            right: 0,
                            backgroundColor: COLORS.btnF2F2F2,
                        }}
                            onPress={deleteImage}
                        >
                            <MyText
                                addSize={-2}
                                text={translate('instalmentManager.btn_delete')}
                                style={{
                                    color: COLORS.txtD0021B
                                }}
                            />
                        </TouchableOpacity>
                    </View>
            }
        </View>

    );
}

export default ImageProcessSim;