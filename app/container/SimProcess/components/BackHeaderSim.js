import React from 'react';
import { connect, useDispatch } from 'react-redux';
import { COLORS } from '@styles';
import { BaseHeader, HeaderCenter, HeaderLeft, HeaderRight } from '@header';
import { setNumGoBack } from '../../ActiveSimManager/action';

const BackHeaderSim = ({
    onGoBack,
    netInfo,
    userInfo,
    isOffline,
    dataSIMProcessInfo,
    count
}) => {
    const title = `YCXL SIM ${dataSIMProcessInfo?.SIMProcessRequestID || ''}`;
    const dispatch = useDispatch()
    return (
        <BaseHeader backgroundColor={isOffline ? '#666666' : COLORS.bg2FB47C}>
            <HeaderLeft
                onPress={() => {
                    dispatch(setNumGoBack(count + 1))
                    onGoBack()
                }}
                iconInfo={{
                    iconSet: 'Ionicons',
                    name: 'chevron-back',
                    color: COLORS.icFFFFFF,
                    size: 30
                }}
            />
            <HeaderCenter title={title} info={userInfo} />
            <HeaderRight info={netInfo} />
        </BaseHeader>
    );
};

const mapStateToProps = (state) => ({
    netInfo: state.networkReducer,
    userInfo: state.userReducer,
    isOffline: state.appSwitchReducer.isOffline,
    dataSIMProcessInfo:
        state.ActiveSimManagementReducer.lstSIM?.dataSIMProcessInfo,
    count:
        state.ActiveSimManagementReducer.numGoBack
});

export default connect(mapStateToProps)(BackHeaderSim);
