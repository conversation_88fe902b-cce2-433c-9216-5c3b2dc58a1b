import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import { CameraDOT, MyText } from '../../../components'

const Camera = ({
    title,
    uriImage,
    onTakePicture,
    onDeletePicture
}) => {
    return (
        <View style={{
            marginVertical: 10,
            paddingHorizontal: 10,
        }}
        >
            <MyText
                text={title}
                style={{ fontSize: 15, marginVertical: 5 }}
            />
            <CameraDOT
                uriImage={uriImage}
                onTakePicture={onTakePicture}
                onDelete={onDeletePicture}
            />
        </View>
    )
}

export default Camera

const styles = StyleSheet.create({})