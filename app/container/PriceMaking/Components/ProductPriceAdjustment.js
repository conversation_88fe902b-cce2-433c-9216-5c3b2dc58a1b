import { Alert, Animated, TouchableOpacity, View } from 'react-native';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { COLORS } from '../../../styles';
import { Icon, MyText, NumberInput } from '../../../components';
import { dateHelper, debounce, helper } from '../../../common';
import ModalCalendar from '../../PaymentTransactions/component/ModalCalendar';
import moment from 'moment';
import { constants } from '../../../constants';

const ProductPriceAdjustment = ({ productInfo, setDataAdjust, setIsFocus }) => {
    const {
        ProductID,
        cus_MaxDay,
        cus_DownMax,
        cus_UpMax,
        ProductName,
        StandardPriceVAT,
        MaxSalePrice,
        MinSalePrice
    } = productInfo ?? {};
    const translateY = useRef(new Animated.Value(50)).current;
    const opacity = useRef(new Animated.Value(0)).current;

    const [priceAtStore, setPriceAtStore] = useState(0);
    const [numberPercent, setNumberPercent] = useState(0);
    const [isShowCalendar, setIsShowCalendar] = useState(false);
    const [update, setUpdate] = useState(0);
    const [fromDate, setFromDate] = useState(
        new Date(Date.now() + 1 * 24 * 60 * 60 * 1000)
    );
    const [toDate, setToDate] = useState(
        new Date(Date.now() + 1 * 24 * 60 * 60 * 1000)
    );
    const [maxDate, setMaxDate] = useState('');
    const [minValue, setMinValue] = useState(0);
    const [maxValue, setMaxValue] = useState(0);
    const [minDate, setMinDate] = useState('');

    const placeholder = ` [${helper.convertNum(
        MinSalePrice
    )} - ${helper.convertNum(MaxSalePrice)}] `;

    const debounceHandlePercent = useCallback(
        debounce((data) => handleGetPercent(data), 1000),
        []
    );

    const handleGetPercent = ({ price, max, min }) => {
        const numberPercent = calcPercentage(price, StandardPriceVAT);
        setNumberPercent(numberPercent);
        setPriceAtStore(price);
        setUpdate((prev) => prev + 1);
        setIsFocus(false);
    };

    const handleChangePrice = (data) => {
        debounceHandlePercent(data);
    };

    useEffect(() => {
        setDataAdjust({
            fromDate,
            toDate,
            priceAtStore,
            maxValue: MaxSalePrice,
            minValue: MinSalePrice
        });
    }, [fromDate, toDate, priceAtStore]);

    useEffect(() => {
        Animated.parallel([
            Animated.timing(translateY, {
                toValue: 0,
                duration: 1000,
                delay: 1000 / 3,
                useNativeDriver: true
            }),
            Animated.timing(opacity, {
                toValue: 1,
                duration: 1000,
                delay: 1000 / 3,
                useNativeDriver: true
            })
        ]).start();
    }, []);

    useEffect(() => {
        const maxValue = Number(
            (StandardPriceVAT + StandardPriceVAT * (cus_UpMax / 100)).toFixed(
                0
            ) ?? 0
        );
        const minValue = Number(
            (StandardPriceVAT - StandardPriceVAT * (cus_DownMax / 100)).toFixed(
                0
            ) ?? 0
        );
        const minDate = dateHelper.formatDateYYYYMMDD(
            new Date(new Date().setDate(new Date().getDate() + 1))
        );

        setMaxValue(maxValue), setMinValue(minValue), setMinDate(minDate);
    }, [productInfo]);

    return (
        <Animated.View
            style={{
                opacity,
                transform: [{ translateY }],
                paddingVertical: 10,
                backgroundColor: COLORS.bgFFFFFF,
                shadowColor: '#000',
                shadowOffset: {
                    width: 0,
                    height: 4
                },
                shadowOpacity: 0.3,
                shadowRadius: 4.65,
                elevation: 8,
                marginVertical: 10
            }}>
            <Animated.View
                style={{
                    paddingHorizontal: 10
                }}>
                <MyText
                    style={{
                        width: '100%',
                        paddingVertical: 10,
                        flexDirection: 'row',
                        paddingHorizontal: 10,
                        justifyContent: 'space-between',
                        fontWeight: 'bold'
                    }}
                    text={ProductID}>
                    {`  -  `}
                    <MyText
                        style={{
                            fontWeight: 'bold'
                        }}
                        text={ProductName}
                    />
                </MyText>

                <TextField
                    title={'Giá tại siêu thị'}
                    value={helper.formatMoney(StandardPriceVAT)}
                />

                <View
                    style={{
                        paddingVertical: 10,
                        flexWrap: 'wrap'
                    }}>
                    <MyText
                        style={{
                            paddingVertical: 10,
                            flexDirection: 'row',
                            paddingHorizontal: 10,
                            fontWeight: 'bold',
                            color: COLORS.bg8E8E93
                        }}
                        text={'Giá bạn điều chỉnh '}
                    />
                    <View
                        style={{
                            flexDirection: 'row',
                            paddingLeft: 10,
                            justifyContent: 'space-between'
                        }}>
                        <View
                            style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'flex-end',
                                width: 120
                            }}>
                            <Icon
                                iconSet={'Entypo'}
                                name={
                                    Math.sign(numberPercent) != -1
                                        ? 'arrow-bold-up'
                                        : 'arrow-bold-down'
                                }
                                color={
                                    Math.sign(numberPercent) != -1
                                        ? COLORS.bg64B74F
                                        : COLORS.bgEA1D5D
                                }
                                size={22}
                            />
                            {priceAtStore != 0 ? (
                                <MyText
                                    style={{
                                        paddingVertical: 10,
                                        flex: 1,
                                        flexDirection: 'row',
                                        paddingHorizontal: 10,
                                        justifyContent: 'space-between',
                                        fontWeight: 'bold',
                                        color:
                                            Math.sign(numberPercent) != -1
                                                ? COLORS.bg64B74F
                                                : COLORS.bgEA1D5D
                                    }}
                                    text={`${numberPercent}%`}
                                />
                            ) : (
                                <MyText
                                    style={{
                                        paddingVertical: 10,
                                        flex: 1,
                                        flexDirection: 'row',
                                        paddingHorizontal: 10,
                                        justifyContent: 'space-between',
                                        fontWeight: 'bold',
                                        color:
                                            Math.sign(numberPercent) != -1
                                                ? COLORS.bg64B74F
                                                : COLORS.bgEA1D5D
                                    }}
                                    text={``}
                                />
                            )}
                        </View>
                        <NumberInput
                            style={{
                                borderWidth: 1,
                                borderRadius: 4,
                                borderColor: COLORS.bdCCCCCC,
                                backgroundColor: COLORS.bgFFFFFF,
                                color: COLORS.txt333333,
                                height: 40,
                                justifyContent: 'flex-end',
                                textAlign: 'right',
                                width: constants.width / 2 + 30,
                                marginLeft: 2,
                                fontSize: 14,
                                paddingRight: 10
                            }}
                            placeholder={placeholder}
                            value={priceAtStore}
                            onChangeText={(value) => {
                                handleChangePrice({
                                    price: value,
                                    max: MaxSalePrice,
                                    min: MinSalePrice
                                });
                            }}
                        />
                    </View>
                </View>
                <View
                    style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        paddingTop: 10
                    }}>
                    <MyText
                        style={{
                            paddingVertical: 10,
                            flexDirection: 'row',
                            paddingHorizontal: 10,
                            justifyContent: 'space-between',
                            fontWeight: 'bold',
                            color: COLORS.bg8E8E93,
                            flex: 0.35
                        }}
                        text={'Thời gian áp dụng '}
                    />
                    <View style={{ paddingBottom: 10, flex: 0.65 }}>
                        <TouchableOpacity
                            style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                                backgroundColor: COLORS.bgFFFFFF,
                                borderRadius: 5,
                                paddingHorizontal: 5,
                                borderWidth: 1,
                                borderColor: COLORS.bdDDDDDD,
                                height: 44,
                                alignSelf: 'center'
                            }}
                            onPress={() => setIsShowCalendar(true)}>
                            <MyText
                                style={{
                                    width: '87%',
                                    paddingHorizontal: 5
                                }}
                                text={`${moment(fromDate).format(
                                    'DD/MM/YYYY'
                                )} - ${moment(toDate).format('DD/MM/YYYY')} `}
                            />
                            <Icon
                                iconSet="Feather"
                                name="calendar"
                                style={{
                                    fontSize: 30,
                                    color: COLORS.ic2C8BD7
                                }}
                            />
                        </TouchableOpacity>
                    </View>
                </View>
            </Animated.View>
            <ModalCalendar
                isVisible={isShowCalendar}
                hideModal={() => {
                    setIsShowCalendar(false);
                }}
                startDate={fromDate}
                endDate={toDate}
                setDate={(day) => {
                    const diffInMs =
                        new Date(day.endDate) - new Date(day.startDate);
                    const diffInDays = diffInMs / (1000 * 60 * 60 * 24) + 1;
                    if (diffInDays > cus_MaxDay) {
                        Alert.alert(
                            'Thông báo',
                            `Vui lòng chọn số ngày không vượt quá số ngày khai báo áp dụng (${cus_MaxDay} ngày)!`,
                            [{ text: 'OK', onPress: () => { } }],
                            { cancelable: false }
                        );
                    } else {
                        setFromDate(day.startDate);
                        setToDate(day.endDate);
                    }
                }}
                minDate={minDate}
                maxDate={maxDate}
                hideExtraDays
                disableMonthChange
                firstDay={1}
                hideDayNames={false}
                showWeekNumbers={false}
                enableSwipeMonths={false}
            />
        </Animated.View>
    );
};

export default ProductPriceAdjustment;

const TextField = ({
    valueColor = COLORS.bg000000,
    title,
    value,
    fontWeight
}) => {
    return (
        <View
            style={{
                width: '100%',
                paddingVertical: 5,
                flexDirection: 'row',
                paddingHorizontal: 10,
                justifyContent: 'space-between'
            }}>
            <MyText
                text={`${title}  `}
                style={{
                    fontWeight: 'bold',
                    color: COLORS.bg8E8E93
                }}
            />
            <MyText
                text={value}
                style={{
                    color: valueColor,
                    textAlign: 'right',
                    flex: 1,
                    fontWeight: fontWeight
                }}
            />
        </View>
    );
};

export const calcPercentage = (priceInput, priceProduct, fixed = 2) => {
    const percent = ((priceInput - priceProduct) / priceProduct) * 100;
    if (!isNaN(percent)) {
        return Number(percent.toFixed(fixed));
    } else {
        return 0;
    }
};
