import { <PERSON><PERSON>, FlatList, Keyboard, StyleSheet, View } from 'react-native';
import React, {
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react';
import SafeAreaView from 'react-native-safe-area-view';
import { COLORS } from '@styles';
import {
    SearchInput,
    Button,
    hideBlockUI,
    showBlockUI,
    BaseLoading
} from '@components';
import { translate } from '@translate';
import ContractItem from './components/ContractItem';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import ReasonCancel from './sheets/ReasonCancel';
import OTPSheet from './sheets/OTPSheet';
import TicketSheet from './sheets/TicketSheet';
import {
    checkTicket,
    getContracts,
    getTicket,
    processCancel,
    processConfirm
} from './action';
import { useSelector } from 'react-redux';
const CancelInstallmentContract = () => {
    // useState
    const [keyword, setKeyword] = useState('');
    const [stateContracts, setStateContracts] = useState({
        isFetching: false,
        isError: false,
        description: '',
        data: []
    });
    const [contractSelected, setContractSelected] = useState({});
    const [dataConfirmContract, setDataConfirmContract] = useState({});
    const [dataReason, setDataReason] = useState([]);
    const [expireTime, setExpireTime] = useState(0);
    const [stateTicket, setStateTicket] = useState({
        ticketInfo: {},
        ticketStatus: {}
    });

    //useRef
    const bottomSheetReasonCancelRef = useRef(null);
    const bottomSheetOTPRef = useRef(null);
    const bottomSheetTicketSheetRef = useRef(null);
    const originReasons = useRef([]);

    //userSelector
    const { storeID, languageID, moduleID } = useSelector(
        (state) => state.userReducer
    );

    const baseBody = useMemo(
        () => ({
            loginStoreId: storeID,
            languageID,
            moduleID
        }),
        [storeID, languageID, moduleID]
    );

    useEffect(() => {
        if (dataReason.length === 0) return;
        bottomSheetReasonCancelRef.current?.present();
    }, [dataReason]);

    const onchangeKeyword = useCallback((text) => {
        setKeyword(text);
    }, []);

    const handleSearchContract = useCallback(() => {
        Keyboard.dismiss();
        setStateContracts({
            isFetching: true,
            isError: false,
            description: '',
            data: []
        });
        const body = {
            ...baseBody,
            keyword,
            searchBy: 0
        };
        getContracts(body)
            .then((result) => {
                const { ListData, ListReasonCancel } = result;
                setStateContracts({
                    isFetching: false,
                    isError: false,
                    description: '',
                    data: ListData
                });
                originReasons.current = ListReasonCancel;
            })
            .catch((msgError) => {
                setStateContracts({
                    isFetching: false,
                    isError: true,
                    description: msgError,
                    data: []
                });
            });
    }, [baseBody, keyword]);

    const onCancelContract = useCallback(
        (item) => {
            const reasons = originReasons.current?.map((item) => ({
                ...item,
                isSelected: false
            }));
            setDataReason(reasons);
            setContractSelected(item);
        },
        [stateContracts.data]
    );

    const handleProcessSendOTP = useCallback(
        (type) => {
            const cancelContractID =
                type == 'RESEND_OTP'
                    ? dataConfirmContract.CancelContractID
                    : contractSelected.CANCELCONTRACTID;
            showBlockUI();
            const body = {
                ...baseBody,
                partnerInstallmentID: contractSelected.PARTNERINSTALLMENTID,
                contractNo: contractSelected.CONTRACTID,
                cancelType: '1',
                reasonCode:
                    dataReason.find((item) => item.isSelected)?.REASONCODE ||
                    null,
                cancelContractID: cancelContractID
            };
            processCancel(body)
                .then((result) => {
                    hideBlockUI();
                    setDataConfirmContract(result);
                    bottomSheetTicketSheetRef.current?.dismiss();
                    bottomSheetOTPRef.current?.present();
                })
                .catch((msgError) => {
                    bottomSheetTicketSheetRef.current?.dismiss();
                    bottomSheetOTPRef.current?.dismiss();
                    Alert.alert(
                        translate('common.notification_uppercase'),
                        msgError,
                        [
                            {
                                text: translate('common.btn_skip'),
                                style: 'cancel',
                                onPress: () => {
                                    hideBlockUI();
                                    type != 'RESEND_OTP' &&
                                        handleSearchContract(keyword);
                                }
                            }
                        ]
                    );
                });
        },
        [dataReason, contractSelected, keyword]
    );

    const onselectReason = useCallback((id) => {
        setDataReason((prev) =>
            prev.map((item) => ({
                ...item,
                isSelected: item.REASONCODE === id
            }))
        );
    }, []);

    const handleConfirmCancel = useCallback(
        (otpCode) => {
            showBlockUI();
            const body = {
                ...baseBody,
                partnerInstallmentID: contractSelected.PARTNERINSTALLMENTID,
                contractNo: contractSelected.CONTRACTID,
                cancelType: '1',
                reasonCode:
                    dataReason.find((item) => item.isSelected)?.REASONCODE ||
                    null,
                otp: otpCode,
                cancelContractID: dataConfirmContract.CancelContractID || ''
            };
            setExpireTime(0);
            processConfirm(body)
                .then((result) => {
                    const { Message, NextStep } = result;
                    if (NextStep == 'Done') {
                        Alert.alert(
                            translate('common.notification_uppercase'),
                            Message || '',
                            [
                                {
                                    text: 'OK',
                                    style: 'cancel',
                                    onPress: () => {
                                        hideBlockUI();
                                        bottomSheetOTPRef.current?.dismiss();
                                        handleSearchContract(keyword);
                                    }
                                }
                            ]
                        );
                    } else {
                        Alert.alert(
                            translate('common.notification_uppercase'),
                            Message || '',
                            [
                                {
                                    text: 'OK',
                                    style: 'cancel',
                                    onPress: () => {
                                        hideBlockUI();
                                    }
                                }
                            ]
                        );
                    }
                })
                .catch((msgConfirm) => {
                    Alert.alert(
                        translate('common.notification_uppercase'),
                        msgConfirm,
                        [
                            {
                                text: translate('saleExpress.retry'),
                                style: 'cancel',
                                onPress: () => {
                                    handleConfirmCancel(otpCode);
                                }
                            },
                            {
                                text: translate('common.btn_skip'),
                                style: 'cancel',
                                onPress: hideBlockUI
                            }
                        ]
                    );
                });
        },
        [baseBody, contractSelected, dataReason, dataConfirmContract, keyword]
    );

    const handleGetInfoTicket = useCallback(() => {
        showBlockUI();
        const body = {
            ...baseBody,
            actionType: 0,
            dataService: {
                ContractID: contractSelected.CONTRACTID,
                SVTicketType: 'INSTALLMENT',
                tickettype: 1
            }
        };
        getTicket(body)
            .then((result) => {
                hideBlockUI();
                setStateTicket({ ...stateTicket, ticketInfo: result });
                bottomSheetReasonCancelRef.current?.dismiss();
                bottomSheetTicketSheetRef.current?.present();
            })
            .catch((msgConfirm) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgConfirm,
                    [
                        {
                            text: translate('saleExpress.retry'),
                            style: 'cancel',
                            onPress: handleGetInfoTicket
                        },
                        {
                            text: translate('common.btn_skip'),
                            style: 'cancel',
                            onPress: hideBlockUI
                        }
                    ]
                );
            });
    }, [contractSelected]);

    const handleCheckTicket = useCallback(() => {
        showBlockUI();
        const body = {
            ...baseBody,
            actionType: 1,
            dataService: {
                ContractID: contractSelected.CONTRACTID,
                SVTicketType: 'INSTALLMENT',
                ticketid: stateTicket.ticketInfo?.data?.TICKETID || ''
            }
        };
        checkTicket(body)
            .then((result) => {
                hideBlockUI();
                setStateTicket({ ...stateTicket, ticketStatus: result });
            })
            .catch((msgConfirm) => {
                Alert.alert(
                    translate('common.notification_uppercase'),
                    msgConfirm,
                    [
                        {
                            text: translate('saleExpress.retry'),
                            style: 'cancel',
                            onPress: handleCheckTicket
                        },
                        {
                            text: translate('common.btn_skip'),
                            style: 'cancel',
                            onPress: hideBlockUI
                        }
                    ]
                );
            });
    }, [contractSelected, stateTicket]);

    return (
        <SafeAreaView style={styles.wrapper}>
            <BottomSheetModalProvider>
                <View style={styles.container_wrapper}>
                    <View style={styles.searchInput_wrapper}>
                        <View style={styles.input_search}>
                            <SearchInput
                                width={'100%'}
                                height={40}
                                placeholder={'Nhập mã hợp đồng/ mã YCX'}
                                onChangeText={(text) => onchangeKeyword(text)}
                                value={keyword}
                                returnKeyType="search"
                                rightComponent={[
                                    !!keyword
                                        ? {
                                              source: { uri: 'ic_close' },
                                              style: {
                                                  width: 12,
                                                  height: 12,
                                                  tintColor: COLORS.ic0000002,
                                                  marginRight: 20
                                              },
                                              onpress: () => {
                                                  setKeyword('');
                                              }
                                          }
                                        : null
                                ]}
                            />
                        </View>
                        <View style={styles.button_search}>
                            <Button
                                text={'TÌM'}
                                styleContainer={{
                                    backgroundColor: COLORS.bg288AD6,
                                    height: 40,
                                    borderRadius: 13
                                }}
                                styleText={{
                                    color: COLORS.txtFFFFFF,
                                    fontSize: 14
                                }}
                                onPress={handleSearchContract}
                            />
                        </View>
                    </View>
                    <View style={{ flex: 1, alignContent: 'center' }}>
                        <BaseLoading
                            isLoading={stateContracts.isFetching}
                            textLoadingError={stateContracts.description}
                            isError={stateContracts.isError}
                            onPressTryAgains={handleSearchContract}
                            content={
                                <FlatList
                                    data={stateContracts.data}
                                    renderItem={({ item, index }) => (
                                        <ContractItem
                                            onCancelContract={() =>
                                                onCancelContract(item)
                                            }
                                            index={index}
                                            item={item}
                                        />
                                    )}
                                    keyExtractor={(item, index) =>
                                        index.toString()
                                    }
                                    showsVerticalScrollIndicator={false}
                                    showsHorizontalScrollIndicator={false}
                                    removeClippedSubviews
                                    keyboardShouldPersistTaps="always"
                                />
                            }
                        />
                    </View>

                    <ReasonCancel
                        bottomSheetModalRef={bottomSheetReasonCancelRef}
                        reasonList={dataReason}
                        handleOnPress={handleGetInfoTicket}
                        onselectReason={onselectReason}
                        onChangeStatusSheet={(position) => {
                            if (position == -1) {
                                //
                            }
                        }}
                    />
                    <OTPSheet
                        contractSelected={contractSelected}
                        retrySendOTP={() => handleProcessSendOTP('RESEND_OTP')}
                        onConfirm={handleConfirmCancel}
                        bottomSheetOTPRef={bottomSheetOTPRef}
                        expireTime={expireTime}
                        setExpireTime={setExpireTime}
                        onChangeStatusSheet={(position) => {
                            if (position == -1) {
                                //
                            }
                        }}
                    />
                    <TicketSheet
                        bottomSheetTicketSheetRef={bottomSheetTicketSheetRef}
                        stateTicket={stateTicket}
                        contractID={contractSelected.CONTRACTID}
                        handleCheckTicket={handleCheckTicket}
                        handleGetInfoTicket={handleGetInfoTicket}
                        handleOnPress={handleProcessSendOTP}
                        onChangeStatusSheet={(position) => {
                            if (position == -1) {
                                setStateTicket({
                                    ...stateTicket,
                                    ticketInfo: {},
                                    ticketStatus: {}
                                });
                            }
                        }}
                    />
                </View>
            </BottomSheetModalProvider>
        </SafeAreaView>
    );
};

export { CancelInstallmentContract };

const styles = StyleSheet.create({
    wrapper: {
        flex: 1,
        backgroundColor: COLORS.bgFFFFFF
    },
    container_wrapper: {
        flex: 1,
        padding: 10
    },
    searchInput_wrapper: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
        paddingBottom: 10
    },
    button_search: {
        flex: 1.5,
        marginLeft: 10
    },
    input_search: {
        flex: 6.5
    }
});
