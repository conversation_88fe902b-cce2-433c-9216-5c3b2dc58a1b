import * as React from 'react';
import { StyleSheet, View, TouchableOpacity, Pressable } from 'react-native';
import { Icon, MyText, BottomSheet } from '@components';
import { COLORS } from '@styles';
import { constants } from '@constants';

export default ReasonCancel = ({
    bottomSheetModalRef,
    handleOnPress,
    reasonList,
    onselectReason,
    onChangeStatusSheet
}) => {
    const renderInner = () => {
        return (
            <View
                style={{
                    backgroundColor: COLORS.bgFFFFFF,
                    flex: 1
                }}>
                <View style={{ margin: 10 }}>
                    {reasonList.map(
                        (
                            { isSelected = false, REASONCODE, REASONNAME },
                            index
                        ) => (
                            <View style={{
                                paddingVertical: 5,
                                borderBottomWidth: StyleSheet.hairlineWidth,
                                borderColor: 'lightslategrey',
                            }} key={index}>
                                <TouchableOpacity
                                    activeOpacity={0.7}
                                    onPress={() => onselectReason(REASONCODE)}

                                    style={{
                                        flexDirection: 'row',
                                        justifyContent: "space-between",
                                        alignItems: "center",
                                        marginHorizontal: 10,
                                        paddingVertical: 10,

                                    }}>

                                    <MyText
                                        style={{ fontWeight: "500" }}
                                        text={REASONNAME}
                                    />

                                    <Icon
                                        iconSet={'Ionicons'}
                                        name={isSelected ?
                                            'radio-button-on-sharp' :
                                            "radio-button-off-sharp"
                                        }
                                        size={20}
                                        color={COLORS.bgF49B0C}
                                    />

                                </TouchableOpacity>
                            </View>

                        )
                    )}
                </View>
            </View>
        );
    };

    const handleComponent = () => {
        return (
            <View style={styles.handle}>
                <View style={{ flex: 1 }} />
                <View
                    style={{
                        flex: 6,
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}>
                    <MyText
                        addSize={1}
                        style={{
                            fontWeight: 'bold',
                            color: COLORS.txt000000
                        }}
                        text="Lý do huỷ"
                    />
                </View>
                <View style={{ flex: 1 }}></View>
            </View>
        );
    };

    const footerComponent = () => {
        return (
            <View
                style={{
                    backgroundColor: COLORS.bgFFFFFF,
                    justifyContent: 'center',
                    alignItems: 'center',
                    flexDirection: 'row',
                    height: 100,
                    paddingBottom: 50,
                    padding: 10
                }}>
                <Pressable style={styles.button} onPress={handleOnPress}>
                    <MyText addSize={2} style={styles.text}>
                        {'TIẾP TỤC'}
                    </MyText>
                </Pressable>
            </View>
        );
    };

    return (
        <BottomSheet
            bs={bottomSheetModalRef}
            snapPoints={['60%']}
            handleComponent={handleComponent}
            footerComponent={footerComponent}
            onChangeStatusSheet={onChangeStatusSheet}>
            {renderInner()}
        </BottomSheet>
    );
};

const styles = StyleSheet.create({
    button: {
        backgroundColor: COLORS.bgF49B0C,
        width: constants.width / 2,
        padding: 10,
        borderRadius: 10,
        alignItems: 'center'
    },
    text: {
        color: COLORS.txtFFFFFF,
        fontWeight: '600'
    },

    handle: {
        backgroundColor: COLORS.bgFFFFFF,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
        height: 50,
        elevation: 2,
        borderTopStartRadius: 22,
        borderTopEndRadius: 22,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: COLORS.bgC4C4C4
    }
});
