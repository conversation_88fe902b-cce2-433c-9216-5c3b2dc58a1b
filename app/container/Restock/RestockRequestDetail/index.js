import React, { useEffect, useRef, useState } from 'react';
import Safe<PERSON>reaView from 'react-native-safe-area-view';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { CaptureCamera, Icon, MyText, hideBlockUI, showBlockUI } from '@components';
import { API_CONST, constants, ENUM } from '@constants';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { dateHelper, helper } from '@common';
import { getImageCDN } from '../../ActiveSimManager/action';
import { Alert, Modal, TouchableOpacity, View } from 'react-native';
import * as actionRestockCreator from '../action';
import { COLORS } from '@styles';
import { translate } from '@translate';
import { CustomerInfo, ListRequestedProduct, RestockDetailBackHeader } from './components';
import { ButtonGroup } from '../CreateRestockRequest/components';
import ImageViewer from 'react-native-image-zoom-viewer';
import { launchImageLibrary } from 'react-native-image-picker';

const { API_GET_IMAGE_CDN, API_GET_IMAGE_CDN_NEW } = API_CONST;
const { FILE_PATH: { RESTOCK_REQUEST_DETAIL } } = ENUM;

function RestockRequestDetail({ navigation, restockAction, route }) {


    const currentIndex = useRef(0);
    const [requestDetail, setRequestDetail] = useState({});
    const [customerName, setCustomerName] = useState('');
    const [customerPhone, setCustomerPhone] = useState('');
    const [isVisibleCamera, setIsVisibleCamera] = useState(false);
    const [imageUrls, setImageUrls] = useState([{}]);
    const [isViewImage, setIsViewImage] = useState(false);
    const [isEditable, setIsEditable] = useState(false);

    const isSelectedProduct = helper.IsNonEmptyArray(requestDetail.RequestNewProductDetails) && requestDetail.RequestNewProductDetails.findIndex(item => item.isChecked) > -1

    useEffect(() => {
        let requestInfo = route.params.requestInfo;
        requestInfo.RequestNewProductDetails = requestInfo.RequestNewProductDetails.map(item => ({ ...item, isChecked: item.IsInvoice == 1 }));
        setRequestDetail(requestInfo);
        const isExpired = !helper.IsEmptyObject(requestInfo) && (new Date().getTime() - dateHelper.convert_string_to_date(requestInfo.CreatedDate).getTime() > 48 * 60 * 60 * 1000);
        const editable = requestInfo.IsCompleted != 1 && !isExpired;
        setIsEditable(editable);
        const { CustomerInfo, RSImageInfo } = requestInfo;
        if (!helper.IsEmptyObject(CustomerInfo)) {
            const { CustomerName, CustomerPhone } = CustomerInfo
            if (helper.IsNonEmptyString(CustomerName)) {
                setCustomerName(CustomerName);
            }
            if (helper.IsNonEmptyString(CustomerPhone)) {
                setCustomerPhone(CustomerPhone);
            }
        }
        if (helper.IsNonEmptyArray(RSImageInfo)) {
            setImageUrls(RSImageInfo.map(item => ({
                imageURL: item.cus_UrlFile
            })).filter(item => helper.IsNonEmptyString(item.imageURL)))
        } else {
            setImageUrls(editable ? [{}] : [])
        }
    }, [])

    const openCamera = (index) => {
        setIsVisibleCamera(true);
        currentIndex.current = index
    }

    const onCheckProduct = (index) => {
        let newRequestDetail = { ...requestDetail };
        newRequestDetail.RequestNewProductDetails[index].isChecked = !newRequestDetail.RequestNewProductDetails[index].isChecked;
        newRequestDetail.RequestNewProductDetails[index].IsInvoice = 1 - newRequestDetail.RequestNewProductDetails[index].IsInvoice;
        setRequestDetail(newRequestDetail)
    }

    const onChangeQuantity = (index, number) => {
        let newRequestDetail = { ...requestDetail };
        newRequestDetail.RequestNewProductDetails[index].Quantity = number;
        setRequestDetail(newRequestDetail)
    }

    const viewImage = () => {
        setIsViewImage(true);
    }

    const takePicture = (photo) => {
        setIsVisibleCamera(false)
        showBlockUI();
        if (helper.hasProperty(photo, 'uri')) {
            helper.resizeImage(photo).then(({ path, uri, size, name }) => {
                const body = helper.createFormData({ uri: uri, type: 'image/jpg', name: name, path: RESTOCK_REQUEST_DETAIL });
                getImageCDN(body)
                    .then((response) => {
                        const remoteURI = API_GET_IMAGE_CDN_NEW + response[0];
                        let newImageUrls = [...imageUrls];
                        let itemImg = {
                            realProductStatusAttachId: 0,
                            imageURL: remoteURI,
                            isDeleted: 0,
                            fileName: response[0]
                        }
                        newImageUrls[currentIndex.current] = itemImg;
                        setImageUrls(newImageUrls)
                        onGetRealProductStatusAttachmentBOs(newImageUrls)
                        hideBlockUI();
                    }).catch((error) => {
                        hideBlockUI();
                        console.log('uploadPicture', error);
                    })

            }).catch((error) => {
                hideBlockUI();
                console.log("resizeImage", error);
            });
        } else { hideBlockUI(); }
    }

    const selectPicture = () => {
        launchImageLibrary(
            {
                mediaType: 'photo',
                noData: true
            },
            (response) => {
                setIsVisibleCamera(false)
                showBlockUI();
                if (helper.hasProperty(response, 'uri')) {
                    helper.resizeImage(response)
                        .then(({ path, uri, size, name }) => {
                            const body = helper.createFormData({ uri: uri, type: 'image/jpg', name: name, path: RESTOCK_REQUEST_DETAIL });
                            getImageCDN(body)
                                .then((response) => {
                                    const remoteURI = API_GET_IMAGE_CDN_NEW + response[0];
                                    let newImageUrls = [...imageUrls];
                                    let itemImg = {
                                        realProductStatusAttachId: 0,
                                        imageURL: remoteURI,
                                        isDeleted: 0,
                                        fileName: response[0]
                                    }
                                    newImageUrls[currentIndex.current] = itemImg;
                                    setImageUrls(newImageUrls)
                                    onGetRealProductStatusAttachmentBOs(newImageUrls)
                                    hideBlockUI();
                                }).catch((error) => {
                                    hideBlockUI();
                                    console.log('uploadPicture', error);
                                })
                        })
                        .catch((error) => {
                            hideBlockUI();
                            console.log('resizeImage', error);
                        });
                } else { hideBlockUI(); }
            }
        );
    };

    const deleteImage = (index) => {
        let newImageUrls = [...imageUrls];
        if (newImageUrls[index].realProductStatusAttachId !== 0) {
            const indexImage = currentAttachments.findIndex(ele => ele.realProductStatusAttachId == newImageUrls[index].realProductStatusAttachId);
            if (indexImage !== -1) {
                currentAttachments[indexImage].isDeleted = 1;
            }
        }
        newImageUrls[index] = {}
        setImageUrls(newImageUrls)
    }

    const updateRequest = () => {
        showBlockUI();
        const imageInfo = imageUrls.filter(item => helper.IsNonEmptyString(item.imageURL)).map(item => ({
            "REQUESTID": null,
            "FILEPATH": item.imageURL
        }))
        const customerInfo = {
            "RequestID": null,
            "CustomerName": customerName,
            "CustomerPhone": customerPhone,
            "Version": 123
        }
        const requestInfo = {
            ...requestDetail,
            RSImageInfo: imageInfo,
            CustomerInfo: customerInfo
        }
        restockAction.updateRestockRequestInfo(requestInfo).then(() => {
            Alert.alert(
                "",
                "Đã cập nhật yêu cầu thành công!",
                [
                    {
                        text: "OK",
                        style: 'cancel',
                        onPress: () => {
                            hideBlockUI();
                            navigation.goBack();
                        }
                    },
                ]
            );
        }).catch(msgError => {
            Alert.alert(
                translate('common.notification_uppercase'),
                msgError,
                [
                    {
                        text: translate('common.btn_skip'),
                        style: 'cancel',
                        onPress: hideBlockUI
                    },
                    {
                        text: translate('common.btn_notify_try_again'),
                        style: 'default',
                        onPress: updateRequest
                    }
                ]
            );
        })
    }

    const getCustomerInfo = () => {
        if (helper.IsNonEmptyString(customerPhone) && !helper.isValidatePhone(customerPhone)) {
            Alert.alert("", "Vui lòng nhập số điện thoại hợp lệ!");
            return false;
        } else {
            showBlockUI();
            restockAction.getCustomerInfo(customerPhone).then(({ CustomerName }) => {
                hideBlockUI();
                setCustomerName(CustomerName)
            }).catch(error => {
                hideBlockUI();
            })
        }

    }

    const isButtonDisabled = helper.IsEmptyString(customerName) ||
        helper.IsEmptyString(customerPhone) ||
        imageUrls.findIndex(item => helper.IsNonEmptyString(item.imageURL)) == -1 ||
        !isSelectedProduct

    return (
        <View style={{ flex: 1 }}>
            <RestockDetailBackHeader
                onGoBack={navigation.goBack}
                title={requestDetail.RequestID}
            />
            <SafeAreaView style={{ flex: 1 }}>
                <KeyboardAwareScrollView
                    style={{
                        flex: 1,
                        backgroundColor: COLORS.bgF0F0F0
                    }}
                    contentContainerStyle={{
                        alignItems: 'center'
                    }}
                    enableResetScrollToCoords={true}
                    keyboardShouldPersistTaps="always"
                    bounces={false}
                    overScrollMode="always"
                    showsHorizontalScrollIndicator={false}
                    showsVerticalScrollIndicator={false}
                    extraScrollHeight={0}
                    nestedScrollEnabled={true}
                >
                    <ListRequestedProduct
                        listProduct={requestDetail.RequestNewProductDetails}
                        onCheck={onCheckProduct}
                        onChangeQuantity={onChangeQuantity}
                        isEditable={isEditable}
                    />
                    <CustomerInfo
                        customerName={customerName}
                        onChangeCustomerName={setCustomerName}
                        customerPhone={customerPhone}
                        onChangeCustomerPhone={setCustomerPhone}
                        onSubmitPhoneNumber={getCustomerInfo}
                        imageUrls={imageUrls}
                        setImageUrls={setImageUrls}
                        openCamera={openCamera}
                        deleteImage={deleteImage}
                        isRequiredCustomerInfo={isSelectedProduct}
                        isEditable={isEditable}
                        viewImage={viewImage}
                    />
                    {isEditable && <ButtonGroup
                        cancel={navigation.goBack}
                        confirm={updateRequest}
                        isCreateButtonDisabled={isButtonDisabled}
                    />}
                    <CaptureCamera
                        isVisibleCamera={isVisibleCamera}
                        takePicture={takePicture}
                        closeCamera={() => { setIsVisibleCamera(false) }}
                        selectPicture={selectPicture}
                    />
                    <Modal visible={isViewImage} transparent>
                        <ImageViewer
                            renderHeader={() => (
                                <TouchableOpacity
                                    style={{
                                        position: 'absolute',
                                        right: 5,
                                        top: constants.heightTopSafe + 5,
                                        zIndex: 100
                                    }}
                                    onPress={() => setIsViewImage(false)}>
                                    <Icon
                                        iconSet="Ionicons"
                                        name="close"
                                        color={COLORS.bgFFFFFF}
                                        size={40}
                                    />
                                </TouchableOpacity>
                            )}
                            imageUrls={imageUrls.filter(item => helper.IsNonEmptyString(item.imageURL)).map(item => ({
                                url: item.imageURL
                            }))}
                            enableSwipeDown
                            onCancel={() => setIsViewImage(false)}
                        />
                    </Modal>
                </KeyboardAwareScrollView>
            </SafeAreaView>
        </View>
    )
}

const mapStateToProps = function (state) {
    return {
        listRequestProduct: state.restockReducer.listRequestProduct
    }
}

const mapDispatchToProps = function (dispatch) {
    return {
        restockAction: bindActionCreators(actionRestockCreator, dispatch)
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(RestockRequestDetail)